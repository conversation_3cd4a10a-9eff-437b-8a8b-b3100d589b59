﻿# CMakeList.txt: DtDrvApp 的 CMake 项目，在此处包括源代码并定义
# 项目特定的逻辑。
#
cmake_minimum_required (VERSION 3.8)

project ("DtDrvApp")

set(CMAKE_CXX_COMPILER "c++")
set(CMAKE_CXX_STANDARD 11)
set(CMAKE_CXX_STANDARD_REQUIRED True)
set(CMAKE_CXX_FLAGS -Wall)

#string(REPLACE ";" " " CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS}")

option(USE_FREETDS "Build with FreeTDS support" ON)
option(USE_MYSQL "Build with MySQL support" OFF)

# 确保只选择一种数据库
if(USE_FREETDS AND USE_MYSQL)
    message(FATAL_ERROR "Cannot enable both FreeTDS and MySQL at the same time!")
endif()

include_directories(src/ 
                    src/CommSource/
                    src/CommSource/ColumnResult/
                    src/Search_DIY/
                    src/Search_DIY/DIY_Sock/
                    src/Search_DIY/DataSearch_Model/
                    src/Search_DIY/DataSearch_Area/
                    src/Search_DIY/DataSearch_Area/SearchArea_Concrete/
                    src/Search_DIY/DataSearch_Log/
                    src/Search_DIY/DataSearch_Log/SearchLog_Concrete/
                    src/Search_DIY/DataSearch_Cell/
                    src/Search_DIY/DataSearch_Cell/SearchCell_Concrete/
                    src/DataItem/
                    src/Search_Passed/
                    src/Search_Passed/Authentication/
                    src/Search_Passed/ConfigManagement/
                    src/Search_Passed/ConfigManagement/ConfigMng_Sock/
                    src/Search_Passed/ConfigManagement/ConfigMng_Concrete/
                    src/Search_Passed/DataSearch_Community/
                    src/Search_Passed/DataSearch_Community/CommunityMng_Concrete/
                    src/Search_Passed/DataSearch_Community/CommunityMng_Sock/
                    src/Search_Passed/DataSearch_Deal/
                    src/Search_Passed/DataSearch_Deal/Search_Concrete/
                    src/Search_Passed/DataSearch_Deal/Search_Sock/
                    src/Search_Passed/DataSearch_Statistic/
                    src/Search_Passed/DataSearch_Statistic/Statistic_Concrete/
                    src/Search_Passed/DataSearch_Statistic/Statistic_Sock/
                    src/Search_Passed/DBManagement/
                    src/Search_Passed/DBManagement/DBMng_Sock/
                    src/Search_Passed/TestDataManagement/
                    src/Search_Passed/TestDataManagement/TestDataMng_Sock/
                    src/Search_Passed/TestDataManagement/TestDataMng_Concrete/
                    src/Search_Passed/UserManagement/
                    src/Search_Passed/UserManagement/UserMng_Concrete/
                    src/Search_Passed/UserManagement/UserMng_Sock/
                    utils/ 
                    stdclass/
                    PPM/
                    EncodeUtils/)
# 添加 FreeTDS 相关头文件目录
if(USE_FREETDS)
    include_directories(utils/freetds/)
    include_directories(stdclass/freetds/)
endif(USE_FREETDS)
# 添加 MySql 相关头文件目录
if(USE_MYSQL)
    include_directories(utils/mysql/)
    include_directories(stdclass/mysql/)
endif(USE_MYSQL)

# Add sources
file(GLOB BASE_SOURCES
    "${PROJECT_SOURCE_DIR}/DtDrvApp.cpp"
    "${PROJECT_SOURCE_DIR}/src/*.cpp"
    "${PROJECT_SOURCE_DIR}/src/CommSource/*.cpp"
    "${PROJECT_SOURCE_DIR}/src/CommSource/ColumnResult/*.cpp"
    "${PROJECT_SOURCE_DIR}/src/Search_DIY/*.cpp"
    "${PROJECT_SOURCE_DIR}/src/Search_DIY/DIY_Sock/*.cpp"
    "${PROJECT_SOURCE_DIR}/src/Search_DIY/DataSearch_Model/*.cpp"
    "${PROJECT_SOURCE_DIR}/src/Search_DIY/DataSearch_Area/*.cpp"
    "${PROJECT_SOURCE_DIR}/src/Search_DIY/DataSearch_Area/SearchArea_Concrete/*.cpp"
    "${PROJECT_SOURCE_DIR}/src/Search_DIY/DataSearch_Log/*.cpp"
    "${PROJECT_SOURCE_DIR}/src/Search_DIY/DataSearch_Log/SearchLog_Concrete/*.cpp"
    "${PROJECT_SOURCE_DIR}/src/Search_DIY/DataSearch_Cell/*.cpp"
    "${PROJECT_SOURCE_DIR}/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete/*.cpp"
    "${PROJECT_SOURCE_DIR}/src/DataItem/*.cpp"
    "${PROJECT_SOURCE_DIR}/src/Search_Passed/*.cpp"
    "${PROJECT_SOURCE_DIR}/src/Search_Passed/Authentication/*.cpp"
    "${PROJECT_SOURCE_DIR}/src/Search_Passed/ConfigManagement/*.cpp"
    "${PROJECT_SOURCE_DIR}/src/Search_Passed/ConfigManagement/ConfigMng_Sock/*.cpp"
    "${PROJECT_SOURCE_DIR}/src/Search_Passed/ConfigManagement/ConfigMng_Concrete/*.cpp"
    "${PROJECT_SOURCE_DIR}/src/Search_Passed/DataSearch_Community/*.cpp"
    "${PROJECT_SOURCE_DIR}/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete/*.cpp"
    "${PROJECT_SOURCE_DIR}/src/Search_Passed/DataSearch_Community/CommunityMng_Sock/*.cpp"
    "${PROJECT_SOURCE_DIR}/src/Search_Passed/DataSearch_Deal/*.cpp"
    "${PROJECT_SOURCE_DIR}/src/Search_Passed/DataSearch_Deal/Search_Concrete/*.cpp"
    "${PROJECT_SOURCE_DIR}/src/Search_Passed/DataSearch_Deal/Search_Sock/*.cpp"
    "${PROJECT_SOURCE_DIR}/src/Search_Passed/DataSearch_Statistic/*.cpp"
    "${PROJECT_SOURCE_DIR}/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete/*.cpp"
    "${PROJECT_SOURCE_DIR}/src/Search_Passed/DataSearch_Statistic/Statistic_Sock/*.cpp"
    "${PROJECT_SOURCE_DIR}/src/Search_Passed/DBManagement/*.cpp"
    "${PROJECT_SOURCE_DIR}/src/Search_Passed/DBManagement/DBMng_Sock/*.cpp"
    "${PROJECT_SOURCE_DIR}/src/Search_Passed/TestDataManagement/*.cpp"
    "${PROJECT_SOURCE_DIR}/src/Search_Passed/TestDataManagement/TestDataMng_Sock/*.cpp"
    "${PROJECT_SOURCE_DIR}/src/Search_Passed/TestDataManagement/TestDataMng_Concrete/*.cpp"
    "${PROJECT_SOURCE_DIR}/src/Search_Passed/UserManagement/*.cpp"
    "${PROJECT_SOURCE_DIR}/src/Search_Passed/UserManagement/UserMng_Concrete/*.cpp"
    "${PROJECT_SOURCE_DIR}/src/Search_Passed/UserManagement/UserMng_Sock/*.cpp"
    "${PROJECT_SOURCE_DIR}/utils/*.cpp"
    "${PROJECT_SOURCE_DIR}/stdclass/*.cpp"
    "${PROJECT_SOURCE_DIR}/PPM/*.cpp"
)

# 添加 FreeTDS 相关文件
if(USE_FREETDS)
    file(GLOB FREETDS_SOURCES
        "${PROJECT_SOURCE_DIR}/utils/freetds/*.cpp"
        "${PROJECT_SOURCE_DIR}/stdclass/freetds/*.cpp"
    )
    list(APPEND BASE_SOURCES ${FREETDS_SOURCES})
endif(USE_FREETDS)

# 添加 MySQL 相关文件
if(USE_MYSQL)
    file(GLOB MYSQL_SOURCES
    "${PROJECT_SOURCE_DIR}/utils/mysql/*.cpp"
    "${PROJECT_SOURCE_DIR}/stdclass/mysql/*.cpp"
    )
    list(APPEND BASE_SOURCES ${MYSQL_SOURCES})
endif(USE_MYSQL)

#aux_source_directory(./ SOURCE_DIR)

# 添加链接库目录
link_directories("lib/" "/usr/lib64/")

# 添加 mysql 库文件目录
if(USE_MYSQL)
    link_directories("/usr/lib64/mysql/")
endif(USE_MYSQL)

# 将源代码添加到此项目的可执行文件。
add_executable (DtDrvApp ${BASE_SOURCES})

# sybdb freetds 编译需要连接的库
# pthread 线程方法需要连接的库
# rt 共享内存编译时需要连接的库
# z 压缩解压zlib库
# EncodeUtils 加密解密库
target_link_libraries(DtDrvApp pthread rt crypto EncodeUtils zip z dl)

if(USE_FREETDS)
    target_link_libraries(DtDrvApp sybdb)  
endif(USE_FREETDS)

if(USE_MYSQL)
    target_link_libraries(DtDrvApp mysqlclient ssl resolv m)
endif(USE_MYSQL)

# 编译完成后执行拷贝配置文件和动态库的命令
set(CONFIG_PATH "${PROJECT_SOURCE_DIR}/build/ini/")
if(NOT EXISTS ${CONFIG_PATH})
    EXECUTE_PROCESS(COMMAND mkdir ${CONFIG_PATH})
endif()

EXECUTE_PROCESS(COMMAND cp -af ${PROJECT_SOURCE_DIR}/ini/DtDrvApp.ini ${CONFIG_PATH})
EXECUTE_PROCESS(COMMAND cp -af ${PROJECT_SOURCE_DIR}/ini/restart.sh ${PROJECT_SOURCE_DIR}/build/)
EXECUTE_PROCESS(COMMAND cp -af ${PROJECT_SOURCE_DIR}/lib/libEncodeUtils.so ${PROJECT_SOURCE_DIR}/build/)
EXECUTE_PROCESS(COMMAND cp -af ${PROJECT_SOURCE_DIR}/config/ ${PROJECT_SOURCE_DIR}/build/)

# 定义宏
# 控制freetds 7.0 协议在连接Win数据库时返回的类型不同，导致解析错误的问题
# 如果连接的是linux数据库需要关闭此 宏定义
# add_definitions(-DUNIX -DFREETDS_7_0)

# 是否使用 MYSQL API 访问 OceanBase 数据库
if(USE_MYSQL)
    add_definitions(-DUSE_MYSQL)
endif(USE_MYSQL)

