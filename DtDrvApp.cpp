// DtDrvApp.cpp: 定义应用程序的入口点。

#include "DtDrvApp.h"
#include "linux_api.h"
#include "StdMonitor.h"
#include "StdCommond.h"

#define WATCHDOG_SPAN 180000

int main()
{
	// 获取项目名称
	char cProName[PATH_MAX] = { 0 };

	if (Clinux_api::get_executable_name(cProName) != 0)
	{
		OUTPUT_ERR("[error]: get executable name filed!...\n");
		return 0;
	}

	printf("*****************************************************\n");
	printf("***** WELCOME TO LOGIN DtDrvApp Decode PROGRAM ******\n");
	printf("*****************************************************\n");

	pid_t pid = getpid(); // 获取当前进程的PID
	//保证一台服务器电脑在多用户的情况下只能有一个用户运行一次
	if (Clinux_api::IsProcessRunning(cProName, pid))
	{
		OUTPUT_ERR("program already exists!program will exit in 2 seconds!");
		printf("program already exists!program will exit in 2 seconds!\n");
		CStdCommond::Sleep(2000);
		return false;
	}

	m_pDtDrvApp_ = CDtDrvAppMain::Instence();

	m_pDtDrvApp_->StartThread();

	while(true)
	{
			CStdCommond::Sleep(1000);
	}

	printf("DtDrvApp Stop...\n");
	OUTPUT_ERR("DtDrvApp Stop...\n");

	return 0;
}




