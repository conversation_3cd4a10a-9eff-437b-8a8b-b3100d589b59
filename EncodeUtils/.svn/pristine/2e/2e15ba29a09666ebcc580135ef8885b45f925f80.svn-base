#ifndef __CRYPTODEF_H__
#define __CRYPTODEF_H__

#include <string>

static char DEFAULT_ENCRYPT_KEY[] = {"<PERSON>    "};

enum PaddingMode
{
	None = 1,     // 不补齐，待加密数据非8字节整数倍时不进行补齐
		
	PKCS7 = 2,    // 即待加密数据按8字节分组，最后一组不足8字数据按补位长度分别右补1-7个字节的相应数据
	//（如补1个字节为0x01，补两个字节为0x02 0x02依次类推到七个字节补七个0x07）。
	// 特别注意当待加密数据为8字节整数倍时，需要最后补8字节的0x00
	
	Zeros = 3,    // 零补齐，即待加密数据按8字节分组，最后一组不足8字节数据右补0x00至8字节。
	
	ANSIX923 = 4, // ANSIX923 填充字符串由一个字节序列组成，此字节序列的最后一个字节填充字节序列的长度，
	// 其余字节均填充数字零。
	
	ISO10126 = 5 // ISO10126 填充字符串由一个字节序列组成，此字节序列的最后一个字节填充字节序列的长度，
	// 其余字节填充随机数据。
};

enum CipherMode
{
	GENERAL = 0,
	ECB,
	CBC,
	CFB,
	OFB,
	TRIPLE_ECB,
	TRIPLE_CBC
};

#endif