# CMakeList.txt: DtDrvServer 的 CMake 项目，在此处包括源代码并定义
# 项目特定的逻辑。
#
cmake_minimum_required (VERSION 3.8)

project ("EncodeUtils")

set(CMAKE_CXX_COMPILER "c++")
set(CMAKE_CXX_STANDARD 11)
set(CMAKE_CXX_STANDARD_REQUIRED True)
set(CMAKE_CXX_FLAGS -Wall)

# Add sources
file(GLOB SOURCES "${PROJECT_SOURCE_DIR}/*.cpp")

include_directories(./ ../stdclass/)

# 生成动态库。
add_library(EncodeUtils_shared SHARED ${SOURCES})
set_target_properties(EncodeUtils_shared PROPERTIES OUTPUT_NAME "EncodeUtils")
set_target_properties(EncodeUtils_shared PROPERTIES CLEAN_DIRECT_OUTPUT 1)
# 生成静态库
add_library(EncodeUtils_static STATIC ${SOURCES})
set_target_properties(EncodeUtils_static PROPERTIES OUTPUT_NAME "EncodeUtils")
set_target_properties(EncodeUtils_static PROPERTIES CLEAN_DIRECT_OUTPUT 1)

# 编译完成后可以执行 make install 安装库到上层目录的 lib 下
set(CMAKE_INSTALL_PREFIX "${PROJECT_SOURCE_DIR}/../")
set(CMAKE_LIB_TARGET_DIR "${CMAKE_INSTALL_PREFIX}/lib/")

if(NOT EXISTS ${CMAKE_LIB_TARGET_DIR})
    EXECUTE_PROCESS(COMMAND mkdir ${CMAKE_LIB_TARGET_DIR})
    if(EXISTS ${CMAKE_LIB_TARGET_DIR})
        install(FILES build/libEncodeUtils.a build/libEncodeUtils.so DESTINATION lib/)
    endif()
else()
    install(FILES build/libEncodeUtils.a build/libEncodeUtils.so DESTINATION lib/)
endif()

# TODO: 如有需要，请添加测试并安装目标。
