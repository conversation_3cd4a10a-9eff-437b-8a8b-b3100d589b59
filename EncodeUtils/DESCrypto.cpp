// DESCrypto.cpp: implementation of the CDESCrypto class.
//
//////////////////////////////////////////////////////////////////////

#include "DESCrypto.h"
#include "Base64.h"

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CDESCrypto::CDESCrypto(void)
{
	DES_cblock IV = { 0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37, 0x38 };	
	memcpy(m_bIV_CBC, IV, sizeof(m_bIV_CBC));
}

CDESCrypto::~CDESCrypto(void)
{

}

CDESCrypto* CDESCrypto::Instance(void)
{
	static CDESCrypto theInstance;
	
	return &theInstance;
}

std::string CDESCrypto::DES_Encrypt(const std::string cleartext, const std::string key, CipherMode mode, PaddingMode padMode)
{
	switch (mode) 
	{
	case GENERAL:
	case ECB:
		{
			return DES_Encrypt_ECB(cleartext, key, mode, padMode);
		}
		
	case CBC:
		{
			return DES_Encrypt_CBC(cleartext, key, mode, padMode);
		}
		
	case CFB:
		{
			return DES_Encrypt_CFB(cleartext, key, mode, padMode);
		}
		
	case TRIPLE_ECB:
		{
			return DES_Encrypt_TRIPLE_ECB(cleartext, key, mode, padMode);
		}
		
	case TRIPLE_CBC:
		{
			return DES_Encrypt_TRIPLE_CBC(cleartext, key, mode, padMode);
		}
	default:
		return "";
	}
}

std::string CDESCrypto::DES_Encrypt_ECB(const std::string cleartext, const std::string key, CipherMode mode, PaddingMode padMode)
{
	DES_cblock keyEncrypt;
	memset(keyEncrypt, 0, 8);
	
	if (key.length() <= 8) 
		memcpy(keyEncrypt, key.c_str(), key.length());
	else 
		memcpy(keyEncrypt, key.c_str(), 8);
	
	DES_key_schedule keySchedule;
	DES_set_key_unchecked(&keyEncrypt, &keySchedule);	
	
	const_DES_cblock inputText;
	DES_cblock outputText;
	std::vector<unsigned char> vecCiphertext;
	unsigned char tmp[8];
	
	for (size_t i = 0; i < cleartext.length() / 8; i ++) {
		memcpy(inputText, cleartext.c_str() + i * 8, 8);
		DES_ecb_encrypt(&inputText, &outputText, &keySchedule, DES_ENCRYPT);
		memcpy(tmp, outputText, 8);
		
		for (int j = 0; j < 8; j++)
			vecCiphertext.push_back(tmp[j]);
	}
	
	if (cleartext.length() % 8 != 0) {
		int tmp1 = cleartext.length() / 8 * 8;
		int tmp2 = cleartext.length() - tmp1;
		memset(inputText, 0, 8);
		memcpy(inputText, cleartext.c_str() + tmp1, tmp2);
		
		DES_ecb_encrypt(&inputText, &outputText, &keySchedule, DES_ENCRYPT);
		memcpy(tmp, outputText, 8);
		
		for (int j = 0; j < 8; j++)
			vecCiphertext.push_back(tmp[j]);
	}
	
	m_strResult = "";
	for (size_t i = 0; i < vecCiphertext.size(); i++)
	{
		m_strResult += vecCiphertext[i];
	}

	return m_strResult;
}

std::string CDESCrypto::DES_Encrypt_CBC(const std::string cleartext, const std::string key, CipherMode mode, PaddingMode padMode)
{
	//we make a little room in inbuffer array for zero-padding when decrypting,
	//The output is always an integral multiple of eight bytes and i use inbuffer for both encrypt and 
	//decrypt processes by reverting argument in function call .
	DES_cblock keyEncrypt, ivec;
	memset(keyEncrypt, 0, 8);
	
	if (key.length() <= 8) 
		memcpy(keyEncrypt, key.c_str(), key.length());
	else 
		memcpy(keyEncrypt, key.c_str(), 8);
	
	memcpy(ivec, m_bIV_CBC, sizeof(m_bIV_CBC));

	int nInSize = cleartext.length();
	
	// PKCS7 padding
	BYTE pad = (8 - (nInSize % 8));
	int new_data_len = (nInSize % 8) ? (nInSize / 8 + 1) * 8 : nInSize;
	if (padMode == PKCS7)
	{		
		new_data_len = nInSize + pad;
	}
	
	unsigned char* inbuffer = new unsigned char[new_data_len];
	unsigned char* outbuffer = new unsigned char[new_data_len + 16];
	memset(inbuffer, 0, new_data_len);
	memset(outbuffer, 0, new_data_len + 16);

	memcpy(inbuffer, cleartext.c_str(), nInSize);	
	if (padMode == PKCS7)
	{
		memset(inbuffer + nInSize, pad, pad);	
	}
	
	DES_key_schedule schedule;
	DES_set_odd_parity(&keyEncrypt);
	DES_set_key_checked(&keyEncrypt, &schedule);

	//encrypt
	DES_ncbc_encrypt((const unsigned char*)inbuffer, outbuffer, new_data_len, &schedule, &ivec, DES_ENCRYPT);

	//display cipher encoded in base64
	m_strResult = Base64_Encode(outbuffer, new_data_len);
	//re-initialize iv since it's changed from last encrypt process.
	
#if 0
	memcpy(ivec, m_bIV_CBC, sizeof(m_bIV_CBC));
	memset(inbuffer, 0 , sizeof(inbuffer));
	//decrypt : notice last parameter
	DES_ncbc_encrypt((const unsigned char*)outbuffer, (unsigned char*)inbuffer, new_data_len, &schedule, &ivec, DES_DECRYPT);
#endif	

	delete inbuffer;
	delete outbuffer;
	
	return m_strResult;
}

std::string CDESCrypto::DES_Encrypt_CFB(const std::string cleartext, const std::string key, CipherMode mode, PaddingMode padMode)
{
	DES_cblock keyEncrypt, ivec;
	memset(keyEncrypt, 0, 8);
	
	if (key.length() <= 8) 
		memcpy(keyEncrypt, key.c_str(), key.length());
	else 
		memcpy(keyEncrypt, key.c_str(), 8);
	
	DES_key_schedule keySchedule;
	DES_set_key_unchecked(&keyEncrypt, &keySchedule);	
	
	memcpy(ivec, m_bIV_CBC, sizeof(m_bIV_CBC));
	
	unsigned char* outputText = new unsigned char[cleartext.length()];
	memset(outputText, 0, cleartext.length());
	
	const unsigned char* tmp = (const unsigned char*)cleartext.c_str();
	
	DES_cfb_encrypt(tmp, outputText, 8, cleartext.length(), &keySchedule, &ivec, DES_ENCRYPT);
	
	m_strResult = (char*)outputText;
	
	delete [] outputText;

	return m_strResult;
}

std::string CDESCrypto::DES_Encrypt_TRIPLE_ECB(const std::string cleartext, const std::string key, CipherMode mode, PaddingMode padMode)
{
	DES_cblock ke1, ke2, ke3;
	memset(ke1, 0, 8);
	memset(ke2, 0, 8);
	memset(ke2, 0, 8);
	
	if (key.length() >= 24) {
		memcpy(ke1, key.c_str(), 8);
		memcpy(ke2, key.c_str() + 8, 8);
		memcpy(ke3, key.c_str() + 16, 8);
	} else if (key.length() >= 16) {
		memcpy(ke1, key.c_str(), 8);
		memcpy(ke2, key.c_str() + 8, 8);
		memcpy(ke3, key.c_str() + 16, key.length() - 16);
	} else if (key.length() >= 8) {
		memcpy(ke1, key.c_str(), 8);
		memcpy(ke2, key.c_str() + 8, key.length() - 8);
		memcpy(ke3, key.c_str(), 8);
	} else {
		memcpy(ke1, key.c_str(), key.length());
		memcpy(ke2, key.c_str(), key.length());
		memcpy(ke3, key.c_str(), key.length());
	}
	
	DES_key_schedule ks1, ks2, ks3;
	DES_set_key_unchecked(&ke1, &ks1);
	DES_set_key_unchecked(&ke2, &ks2);
	DES_set_key_unchecked(&ke3, &ks3);
	
	const_DES_cblock inputText;
	DES_cblock outputText;
	std::vector<unsigned char> vecCiphertext;
	unsigned char tmp[8];
	
	for (size_t i = 0; i < cleartext.length() / 8; i ++) {
		memcpy(inputText, cleartext.c_str() + i * 8, 8);
		DES_ecb3_encrypt(&inputText, &outputText, &ks1, &ks2, &ks3, DES_ENCRYPT);
		memcpy(tmp, outputText, 8);
		
		for (int j = 0; j < 8; j++)
			vecCiphertext.push_back(tmp[j]);
	}
	
	if (cleartext.length() % 8 != 0) {
		int tmp1 = cleartext.length() / 8 * 8;
		int tmp2 = cleartext.length() - tmp1;
		memset(inputText, 0, 8);
		memcpy(inputText, cleartext.c_str() + tmp1, tmp2);
		
		DES_ecb3_encrypt(&inputText, &outputText, &ks1, &ks2, &ks3, DES_ENCRYPT);
		memcpy(tmp, outputText, 8);
		
		for (int j = 0; j < 8; j++)
			vecCiphertext.push_back(tmp[j]);
	}
	
	m_strResult = "";
	for (size_t i = 0; i < vecCiphertext.size(); i++)
	{
		m_strResult += vecCiphertext[i];
	}
	
	return m_strResult;
}

std::string CDESCrypto::DES_Encrypt_TRIPLE_CBC(const std::string cleartext, const std::string key, CipherMode mode, PaddingMode padMode)
{
	DES_cblock ke1, ke2, ke3, ivec;
	memset(ke1, 0, 8);
	memset(ke2, 0, 8);
	memset(ke2, 0, 8);
	
	if (key.length() >= 24) {
		memcpy(ke1, key.c_str(), 8);
		memcpy(ke2, key.c_str() + 8, 8);
		memcpy(ke3, key.c_str() + 16, 8);
	} else if (key.length() >= 16) {
		memcpy(ke1, key.c_str(), 8);
		memcpy(ke2, key.c_str() + 8, 8);
		memcpy(ke3, key.c_str() + 16, key.length() - 16);
	} else if (key.length() >= 8) {
		memcpy(ke1, key.c_str(), 8);
		memcpy(ke2, key.c_str() + 8, key.length() - 8);
		memcpy(ke3, key.c_str(), 8);
	} else {
		memcpy(ke1, key.c_str(), key.length());
		memcpy(ke2, key.c_str(), key.length());
		memcpy(ke3, key.c_str(), key.length());
	}
	
	DES_key_schedule ks1, ks2, ks3;
	DES_set_key_unchecked(&ke1, &ks1);
	DES_set_key_unchecked(&ke2, &ks2);
	DES_set_key_unchecked(&ke3, &ks3);
	
	memcpy(ivec, m_bIV_CBC, sizeof(m_bIV_CBC));
	
	int iLength = cleartext.length() % 8 ? (cleartext.length() / 8 + 1) * 8 : cleartext.length();
	unsigned char* tmp = new unsigned char[iLength + 16];
	memset(tmp, 0, iLength);
	
	DES_ede3_cbc_encrypt((const unsigned char*)cleartext.c_str(), tmp, cleartext.length()+1, &ks1, &ks2, &ks3, &ivec, DES_ENCRYPT);
	
	m_strResult = (char*)tmp;
	
	delete [] tmp;

	return m_strResult;
}

std::string CDESCrypto::DES_Decrypt(const std::string ciphertext, const std::string key, CipherMode mode, PaddingMode padMode)
{
	m_strResult = "";

	switch (mode) 
	{
	case GENERAL:
	case ECB:
		{
			return DES_Decrypt_ECB(ciphertext, key, mode, padMode);
		}
		
	case CBC:
		{
			return DES_Decrypt_CBC(ciphertext, key, mode, padMode);
		}
		
	case CFB:
		{
			return DES_Decrypt_CFB(ciphertext, key, mode, padMode);
		}
		
	case TRIPLE_ECB:
		{
			return DES_Decrypt_TRIPLE_ECB(ciphertext, key, mode, padMode);
		}
		
	case TRIPLE_CBC:
		{
			return DES_Decrypt_TRIPLE_CBC(ciphertext, key, mode, padMode);
		}
		
	}
	
	return m_strResult;
}

std::string CDESCrypto::DES_Decrypt_ECB(const std::string ciphertext, const std::string key, CipherMode mode, PaddingMode padMode)
{
	DES_cblock keyEncrypt;
	memset(keyEncrypt, 0, 8);
	
	if (key.length() <= 8) 
		memcpy(keyEncrypt, key.c_str(), key.length());
	else 
		memcpy(keyEncrypt, key.c_str(), 8);
	
	DES_key_schedule keySchedule;
	DES_set_key_unchecked(&keyEncrypt, &keySchedule);	
	
	const_DES_cblock inputText;
	DES_cblock outputText;
	std::vector<unsigned char> vecCleartext;
	unsigned char tmp[8];
	
	for (size_t i = 0; i < ciphertext.length() / 8; i ++) {
		memcpy(inputText, ciphertext.c_str() + i * 8, 8);
		DES_ecb_encrypt(&inputText, &outputText, &keySchedule, DES_DECRYPT);
		memcpy(tmp, outputText, 8);
		
		for (int j = 0; j < 8; j++)
			vecCleartext.push_back(tmp[j]);
	}
	
	if (ciphertext.length() % 8 != 0) {
		int tmp1 = ciphertext.length() / 8 * 8;
		int tmp2 = ciphertext.length() - tmp1;
		memset(inputText, 0, 8);
		memcpy(inputText, ciphertext.c_str() + tmp1, tmp2);
		
		DES_ecb_encrypt(&inputText, &outputText, &keySchedule, DES_DECRYPT);
		memcpy(tmp, outputText, 8);
		
		for (int j = 0; j < 8; j++)
			vecCleartext.push_back(tmp[j]);
	}
	
	m_strResult = "";
	for (size_t i = 0; i < vecCleartext.size(); i++)
	{
		m_strResult += vecCleartext[i];
	}

	return m_strResult;
}

std::string CDESCrypto::DES_Decrypt_CBC(const std::string ciphertext, const std::string key, CipherMode mode, PaddingMode padMode)
{
	m_strResult = "";

	DES_cblock keyEncrypt, ivec;
	memset(keyEncrypt, 0, 8);
	
	if (key.length() <= 8) 
		memcpy(keyEncrypt, key.c_str(), key.length());
	else 
		memcpy(keyEncrypt, key.c_str(), 8);
	
	DES_key_schedule keySchedule;
	DES_set_key_unchecked(&keyEncrypt, &keySchedule);	
	
	memcpy(ivec, m_bIV_CBC, sizeof(m_bIV_CBC));

	if (!CheckIsBase64Encode(ciphertext))
	{
		return m_strResult;
	}
	
	std::string strTemp = Base64_Decode(ciphertext);
	int iLength = strTemp.length() % 8 ? (strTemp.length() / 8 + 1) * 8 : strTemp.length();
	unsigned char* tmp = new unsigned char[iLength*2];
	memset(tmp, 0, iLength);
	
	DES_ncbc_encrypt((const unsigned char*)strTemp.c_str(), tmp, iLength, &keySchedule, &ivec, DES_DECRYPT);
	
	// 去掉PKCS7 padding
	if (padMode == PKCS7)
	{
		m_strResult = "";
		BYTE pad = tmp[iLength - 1];

		if (pad > 0 && pad <= 8)
		{
			// 判断是否是加密的串，否则返回空串
			for (int i = iLength - 1; i >= (iLength - pad); i--)
			{
				if (tmp[i] != pad)
				{
					return m_strResult;
				}
			}

			iLength -= pad;
		}
		else
		{
			return m_strResult;
		}
	}

	if (iLength <= 0)
	{
		return m_strResult;
	}

	tmp[iLength] = 0;
	m_strResult = (char*)tmp;
	
	delete [] tmp;

	return m_strResult;
}

std::string CDESCrypto::DES_Decrypt_CFB(const std::string ciphertext, const std::string key, CipherMode mode, PaddingMode padMode)
{
	DES_cblock keyEncrypt, ivec;
	memset(keyEncrypt, 0, 8);
	
	if (key.length() <= 8) 
		memcpy(keyEncrypt, key.c_str(), key.length());
	else 
		memcpy(keyEncrypt, key.c_str(), 8);
	
	DES_key_schedule keySchedule;
	DES_set_key_unchecked(&keyEncrypt, &keySchedule);	
	
	memcpy(ivec, m_bIV_CBC, sizeof(m_bIV_CBC));
	
	unsigned char* outputText = new unsigned char[ciphertext.length()];
	memset(outputText, 0, ciphertext.length());
	
	const unsigned char* tmp = (const unsigned char*)ciphertext.c_str();
	
	DES_cfb_encrypt(tmp, outputText, 8, 32/*ciphertext.length() - 16*/, &keySchedule, &ivec, DES_DECRYPT);
	
	m_strResult = (char*)outputText;
	
	delete [] outputText;

	return m_strResult;
}

std::string CDESCrypto::DES_Decrypt_TRIPLE_ECB(const std::string ciphertext, const std::string key, CipherMode mode, PaddingMode padMode)
{	
	DES_cblock ke1, ke2, ke3;
	memset(ke1, 0, 8);
	memset(ke2, 0, 8);
	memset(ke2, 0, 8);
	
	if (key.length() >= 24) {
		memcpy(ke1, key.c_str(), 8);
		memcpy(ke2, key.c_str() + 8, 8);
		memcpy(ke3, key.c_str() + 16, 8);
	} else if (key.length() >= 16) {
		memcpy(ke1, key.c_str(), 8);
		memcpy(ke2, key.c_str() + 8, 8);
		memcpy(ke3, key.c_str() + 16, key.length() - 16);
	} else if (key.length() >= 8) {
		memcpy(ke1, key.c_str(), 8);
		memcpy(ke2, key.c_str() + 8, key.length() - 8);
		memcpy(ke3, key.c_str(), 8);
	} else {
		memcpy(ke1, key.c_str(), key.length());
		memcpy(ke2, key.c_str(), key.length());
		memcpy(ke3, key.c_str(), key.length());
	}
	
	DES_key_schedule ks1, ks2, ks3;
	DES_set_key_unchecked(&ke1, &ks1);
	DES_set_key_unchecked(&ke2, &ks2);
	DES_set_key_unchecked(&ke3, &ks3);
	
	const_DES_cblock inputText;
	DES_cblock outputText;
	std::vector<unsigned char> vecCleartext;
	unsigned char tmp[8];
	
	for (size_t i = 0; i < ciphertext.length() / 8; i ++) {
		memcpy(inputText, ciphertext.c_str() + i * 8, 8);
		DES_ecb3_encrypt(&inputText, &outputText, &ks1, &ks2, &ks3, DES_DECRYPT);
		memcpy(tmp, outputText, 8);
		
		for (int j = 0; j < 8; j++)
			vecCleartext.push_back(tmp[j]);
	}
	
	if (ciphertext.length() % 8 != 0) {
		int tmp1 = ciphertext.length() / 8 * 8;
		int tmp2 = ciphertext.length() - tmp1;
		memset(inputText, 0, 8);
		memcpy(inputText, ciphertext.c_str() + tmp1, tmp2);
		
		DES_ecb3_encrypt(&inputText, &outputText, &ks1, &ks2, &ks3, DES_DECRYPT);
		memcpy(tmp, outputText, 8);
		
		for (int j = 0; j < 8; j++)
			vecCleartext.push_back(tmp[j]);
	}
	
	m_strResult = "";
	for (size_t i = 0; i < vecCleartext.size(); i++)
	{
		m_strResult += vecCleartext[i];
	}

	return m_strResult;
}

std::string CDESCrypto::DES_Decrypt_TRIPLE_CBC(const std::string ciphertext, const std::string key, CipherMode mode, PaddingMode padMode)
{
	DES_cblock ke1, ke2, ke3, ivec;
	memset(ke1, 0, 8);
	memset(ke2, 0, 8);
	memset(ke2, 0, 8);
	
	if (key.length() >= 24) {
		memcpy(ke1, key.c_str(), 8);
		memcpy(ke2, key.c_str() + 8, 8);
		memcpy(ke3, key.c_str() + 16, 8);
	} else if (key.length() >= 16) {
		memcpy(ke1, key.c_str(), 8);
		memcpy(ke2, key.c_str() + 8, 8);
		memcpy(ke3, key.c_str() + 16, key.length() - 16);
	} else if (key.length() >= 8) {
		memcpy(ke1, key.c_str(), 8);
		memcpy(ke2, key.c_str() + 8, key.length() - 8);
		memcpy(ke3, key.c_str(), 8);
	} else {
		memcpy(ke1, key.c_str(), key.length());
		memcpy(ke2, key.c_str(), key.length());
		memcpy(ke3, key.c_str(), key.length());
	}
	
	DES_key_schedule ks1, ks2, ks3;
	DES_set_key_unchecked(&ke1, &ks1);
	DES_set_key_unchecked(&ke2, &ks2);
	DES_set_key_unchecked(&ke3, &ks3);
	
	memcpy(ivec, m_bIV_CBC, sizeof(m_bIV_CBC));
	
	int iLength = ciphertext.length() % 8 ? (ciphertext.length() / 8 + 1) * 8 : ciphertext.length();
	unsigned char* tmp = new unsigned char[iLength];
	memset(tmp, 0, iLength);
	
	DES_ede3_cbc_encrypt((const unsigned char*)ciphertext.c_str(), tmp, ciphertext.length()+1, &ks1, &ks2, &ks3, &ivec, DES_DECRYPT);
	
	m_strResult = (char*)tmp;
	
	delete [] tmp;
	
	return m_strResult;
}
