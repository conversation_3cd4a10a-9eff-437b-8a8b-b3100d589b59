// DESCrypto.h: interface for the CDESCrypto class.
//
//////////////////////////////////////////////////////////////////////

#ifndef __DESCRYPTO_H__
#define __DESCRYPTO_H__

#include "CryptoDef.h"
#include "openssl/des.h"
#include "StdHeader.h"

class CDESCrypto  
{
public:
	CDESCrypto(void);
	virtual ~CDESCrypto(void);

	static CDESCrypto* Instance(void);

	/**
	 * 功能描述：加密字符串
	 * @Param cleartext: 需要加密的字符串
	 * @Param key: 密钥
	 * @Param mode: DES加密模式
	 * @Param padMode: DES补齐模式

	 * @Return: 加密后的字符串
	*/
	std::string DES_Encrypt(const std::string cleartext, const std::string key = DEFAULT_ENCRYPT_KEY, CipherMode mode = CBC, PaddingMode padMode = PKCS7);

	/**
	 * 功能描述：解密字符串
	 * @Param ciphertext: 需要解密的字符串
	 * @Param key: 密钥
	 * @Param mode: DES加密模式
	 * @Param padMode: DES补齐模式

	 * @Return: 解密后的字符串，如果解密失败则返回空字符串
	*/
	std::string DES_Decrypt(const std::string ciphertext, const std::string key = DEFAULT_ENCRYPT_KEY, CipherMode mode = CBC, PaddingMode padMode = PKCS7);

protected:
	
private:
	std::string DES_Encrypt_ECB(const std::string cleartext, const std::string key, CipherMode mode, PaddingMode padMode);
	std::string DES_Encrypt_CBC(const std::string cleartext, const std::string key, CipherMode mode, PaddingMode padMode);
	std::string DES_Encrypt_CFB(const std::string cleartext, const std::string key, CipherMode mode, PaddingMode padMode);
	std::string DES_Encrypt_TRIPLE_ECB(const std::string cleartext, const std::string key, CipherMode mode, PaddingMode padMode);
	std::string DES_Encrypt_TRIPLE_CBC(const std::string cleartext, const std::string key, CipherMode mode, PaddingMode padMode);

	std::string DES_Decrypt_ECB(const std::string ciphertext, const std::string key, CipherMode mode, PaddingMode padMode);
	std::string DES_Decrypt_CBC(const std::string ciphertext, const std::string key, CipherMode mode, PaddingMode padMode);
	std::string DES_Decrypt_CFB(const std::string ciphertext, const std::string key, CipherMode mode, PaddingMode padMode);
	std::string DES_Decrypt_TRIPLE_ECB(const std::string ciphertext, const std::string key, CipherMode mode, PaddingMode padMode);
	std::string DES_Decrypt_TRIPLE_CBC(const std::string ciphertext, const std::string key, CipherMode mode, PaddingMode padMode);

	DES_cblock m_bIV_CBC;
	std::string m_strResult;
};

#endif 
