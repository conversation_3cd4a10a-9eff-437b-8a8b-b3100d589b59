// Select_Reactor.cpp,v 4.44 2002/02/26 23:41:49 dhinton Exp

#include "PPM_Select_Reactor.h"

//PPM_RCSID(ace, Select_Reactor, "Select_Reactor.cpp,v 4.44 2002/02/26 23:41:49 dhinton Exp")


//#if defined (PPM_HAS_EXPLICIT_TEMPLATE_INSTANTIATION)
//# if defined (PPM_MT_SAFE) && (PPM_MT_SAFE != 0)
//template class PPM_Select_Reactor_Token_T<PPM_Token>;
//template class PPM_Select_Reactor_T< PPM_Select_Reactor_Token_T<PPM_Token> >;
//template class PPM_Lock_Adapter< PPM_Select_Reactor_Token_T<PPM_Token> >;
//template class PPM_Guard< PPM_Select_Reactor_Token_T<PPM_Token> >;
//# else
//template class PPM_Select_Reactor_Token_T<PPM_Noop_Token>;
//template class PPM_Select_Reactor_T< PPM_Select_Reactor_Token_T<PPM_Noop_Token> >;
//template class PPM_Lock_Adapter< PPM_Select_Reactor_Token_T<PPM_Noop_Token> >;
//# endif /* PPM_MT_SAFE && PPM_MT_SAFE != 0 */
//#elif defined (PPM_HAS_TEMPLATE_INSTANTIATION_PRAGMA)
//# if defined (PPM_MT_SAFE) && (PPM_MT_SAFE != 0)
//#   pragma instantiate PPM_Select_Reactor_Token_T<PPM_Token>
//#   pragma instantiate PPM_Select_Reactor_T< PPM_Select_Reactor_Token_T<PPM_Token> >
//#   pragma instantiate PPM_Lock_Adapter< PPM_Select_Reactor_Token_T<PPM_Token> >
//#   pragma instantiate PPM_Guard< PPM_Select_Reactor_Token_T<PPM_Token> >
//# else
//#   pragma instantiate PPM_Select_Reactor_Token_T<PPM_Noop_Token>
//#   pragma instantiate PPM_Select_Reactor_T< PPM_Select_Reactor_Token_T<PPM_Noop_Token> >
//#   pragma instantiate PPM_Lock_Adapter< PPM_Select_Reactor_Token_T<PPM_Noop_Token> >
//# endif /* PPM_MT_SAFE && PPM_MT_SAFE != 0 */
//#endif /* PPM_HAS_EXPLICIT_TEMPLATE_INSTANTIATION */
