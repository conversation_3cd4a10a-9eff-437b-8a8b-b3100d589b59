/* -*- C++ -*- */
// Flag_Manip.i,v 1.2 2001/03/04 00:55:26 brunsch Exp

// Return flags currently associated with handle.

PPM_INLINE int
PPM_Flag_Manip::get_flags (PPM_HANDLE handle)
{
  PPM_TRACE ("PPM_Flag_Manip::get_flags");

#if defined (PPM_LACKS_FCNTL)
  // PPM_OS::fcntl is not supported, e.g., on VxWorks.  It
  // would be better to store <PERSON><PERSON>'s notion of the flags
  // associated with the handle, but this works for now.
  PPM_UNUSED_ARG (handle);
  return 0;
#else
  return PPM_OS::fcntl (handle, F_GETFL, 0);
#endif /* PPM_LACKS_FCNTL */
}
