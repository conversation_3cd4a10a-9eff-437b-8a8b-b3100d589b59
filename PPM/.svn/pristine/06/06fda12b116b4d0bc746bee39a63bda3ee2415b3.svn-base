/* -*- C++ -*- */
// Select_Reactor_T.i,v 4.10 2002/06/06 17:32:33 schmidt Exp

#include "./PPM_Reactor.h"

/* PPM_INLINE */ int
PPM_Select_Reactor_T::resume_handler (PPM_Event_Handler *h)
{
  PPM_TRACE ("PPM_Select_Reactor_T::resume_handler");
  return this->resume_handler (h->get_handle ());
}

/* PPM_INLINE */ int
PPM_Select_Reactor_T::resume_handler (const PPM_Handle_Set &handles)
{
  PPM_TRACE ("PPM_Select_Reactor_T::resume_handler");
  PPM_Handle_Set_Iterator handle_iter (handles);
  PPM_HANDLE h;

  PPM_MT (PPM_GUARD_RETURN (PPM_SELECT_REACTOR_TOKEN, PPM_mon, this->token_, -1));

  while ((h = handle_iter ()) != PPM_INVALID_HANDLE)
    if (this->resume_i (h) == -1)
      return -1;

  return 0;
}

/* PPM_INLINE */ int
PPM_Select_Reactor_T::suspend_handler (PPM_Event_Handler *h)
{
  PPM_TRACE ("PPM_Select_Reactor_T::suspend_handler");
  return this->suspend_handler (h->get_handle ());
}

/* PPM_INLINE */ int
PPM_Select_Reactor_T::suspend_handler (const PPM_Handle_Set &handles)
{
  PPM_TRACE ("PPM_Select_Reactor_T::suspend_handler");
  PPM_Handle_Set_Iterator handle_iter (handles);
  PPM_HANDLE h;

  PPM_MT (PPM_GUARD_RETURN (PPM_SELECT_REACTOR_TOKEN, PPM_mon, this->token_, -1));

  while ((h = handle_iter ()) != PPM_INVALID_HANDLE)
    if (this->suspend_i (h) == -1)
      return -1;

  return 0;
}

/* PPM_INLINE */ int
PPM_Select_Reactor_T::register_handler (int signum,
                                                                  PPM_Event_Handler *new_sh,
                                                                  PPM_Sig_Action *new_disp,
                                                                  PPM_Event_Handler **old_sh,
                                                                  PPM_Sig_Action *old_disp)
{
  PPM_TRACE ("PPM_Select_Reactor_T::register_handler");
  return this->signal_handler_->register_handler (signum,
                                                  new_sh, new_disp,
                                                  old_sh, old_disp);
}

#if defined (WIN32)

/* PPM_INLINE */ int
PPM_Select_Reactor_T::register_handler (PPM_Event_Handler *event_handler,
                                                                  PPM_HANDLE event_handle)
{
  // Don't have an implementation for this yet...
  PPM_UNUSED_ARG (event_handler);
  PPM_UNUSED_ARG (event_handle);
  PPM_NOTSUP_RETURN (-1);
}

#endif /* WIN32 */

/* PPM_INLINE */ int
PPM_Select_Reactor_T::register_handler (PPM_HANDLE event_handle,
                                                                  PPM_HANDLE io_handle,
                                                                  PPM_Event_Handler *event_handler,
                                                                  PPM_Reactor_Mask mask)
{
  // Don't have an implementation for this yet...
  PPM_UNUSED_ARG (event_handle);
  PPM_UNUSED_ARG (io_handle);
  PPM_UNUSED_ARG (event_handler);
  PPM_UNUSED_ARG (mask);
  PPM_NOTSUP_RETURN (-1);
}

/* PPM_INLINE */ int
PPM_Select_Reactor_T::handler (int signum, PPM_Event_Handler **handler)
{
  PPM_TRACE ("PPM_Select_Reactor_T::handler");
  return this->handler_i (signum, handler);
}

/* PPM_INLINE */ int
PPM_Select_Reactor_T::remove_handler (int signum,
                                                                PPM_Sig_Action *new_disp,
                                                                PPM_Sig_Action *old_disp,
                                                                int sigkey)
{
  PPM_TRACE ("PPM_Select_Reactor_T::remove_handler");
  return this->signal_handler_->remove_handler (signum, new_disp, old_disp, sigkey);
}

/* PPM_INLINE */ int
PPM_Select_Reactor_T::uses_event_associations (void)
{
  // Since the Select_Reactor does not do any event associations, this
  // function always return 0.
  return 0;
}

// = The remaining methods in this file must be called with locks
// held.

// Performs operations on the "ready" bits.

/* PPM_INLINE */ int
PPM_Select_Reactor_T::ready_ops (PPM_Event_Handler *handler,
                                                           PPM_Reactor_Mask mask,
                                                           int ops)
{
  PPM_TRACE ("PPM_Select_Reactor_T::ready_ops");
  return this->ready_ops (handler->get_handle (), mask, ops);
}

// Performs operations on the "dispatch" masks.

/* PPM_INLINE */ int
PPM_Select_Reactor_T::mask_ops (PPM_Event_Handler *handler,
                                                          PPM_Reactor_Mask mask,
                                                          int ops)
{
  PPM_TRACE ("PPM_Select_Reactor_T::mask_ops");
  return this->mask_ops (handler->get_handle (), mask, ops);
}

/* PPM_INLINE */ int
PPM_Select_Reactor_T::schedule_wakeup (PPM_Event_Handler *eh,
                                                                 PPM_Reactor_Mask mask)
{
  PPM_TRACE ("PPM_Select_Reactor_T::schedule_wakeup");
  return this->mask_ops (eh->get_handle (), mask, PPM_Reactor::ADD_MASK);
}

/* PPM_INLINE */ int
PPM_Select_Reactor_T::cancel_wakeup (PPM_Event_Handler *eh,
                                                               PPM_Reactor_Mask mask)
{
  PPM_TRACE ("PPM_Select_Reactor_T::cancel_wakeup");
  return this->mask_ops (eh->get_handle (), mask, PPM_Reactor::CLR_MASK);
}

/* PPM_INLINE */ int
PPM_Select_Reactor_T::schedule_wakeup (PPM_HANDLE handle,
                                                                 PPM_Reactor_Mask mask)
{
  PPM_TRACE ("PPM_Select_Reactor_T::schedule_wakeup");
  return this->mask_ops (handle, mask, PPM_Reactor::ADD_MASK);
}

/* PPM_INLINE */ int
PPM_Select_Reactor_T::cancel_wakeup (PPM_HANDLE handle,
                                                               PPM_Reactor_Mask mask)
{
  PPM_TRACE ("PPM_Select_Reactor_T::cancel_wakeup");
  return this->mask_ops (handle, mask, PPM_Reactor::CLR_MASK);
}

///* PPM_INLINE */ PPM_Lock &
//PPM_Select_Reactor_T::lock (void)
//{
//  PPM_TRACE ("PPM_Select_Reactor_T::lock");
//  return this->lock_adapter_;
//}

/* PPM_INLINE */ void
PPM_Select_Reactor_T::wakeup_all_threads (void)
{
  // Send a notification, but don't block if there's no one to receive
  // it.
//  this->notify (0, PPM_Event_Handler::NULL_MASK, (PPM_Time_Value *) &PPM_Time_Value::zero);
}

/* PPM_INLINE */ int
PPM_Select_Reactor_T::alertable_handle_events (PPM_Time_Value *max_wait_time)
{
  return this->handle_events (max_wait_time);
}

/* PPM_INLINE */ int
PPM_Select_Reactor_T::alertable_handle_events (PPM_Time_Value &max_wait_time)
{
  return this->handle_events (max_wait_time);
}

/* PPM_INLINE */ int
PPM_Select_Reactor_T::deactivated (void)
{
  return this->deactivated_;
}

/* PPM_INLINE */ void
PPM_Select_Reactor_T::deactivate (int do_stop)
{
  this->deactivated_ = do_stop;
  this->wakeup_all_threads ();
}

/* PPM_INLINE */ size_t
PPM_Select_Reactor_T::size (void) const
{
  return this->handler_rep_.size ();
}
