// Flag_Manip.cpp,v 1.1 2000/10/20 21:23:02 doccvs Exp

#include "./PPM_Flag_Manip.h"

#if defined (PPM_LACKS_INLINE_FUNCTIONS)
#include "./PPM_Flag_Manip.inl"
#endif /* PPM_LACKS_INLINE_FUNCTIONS */

// Flags are file status flags to turn on.

int
PPM_Flag_Manip::set_flags (PPM_HANDLE handle, int flags)
{
  PPM_TRACE ("PPM_Flag_Manip::set_flags");
#if defined (WIN32) || defined (VXWORKS) || defined (PPM_LACKS_FCNTL)
  switch (flags)
    {
    case PPM_NONBLOCK:
      // nonblocking argument (1)
      // blocking:            (0)
      {
        u_long nonblock = 1;
        return PPM_OS::ioctl (handle, FIONBIO, &nonblock);
      }
    default:
      PPM_NOTSUP_RETURN (-1);
    }
#else
  int val = PPM_OS::fcntl (handle, F_GETFL, 0);

  if (val == -1)
    return -1;

  // Turn on flags.
  PPM_SET_BITS (val, flags);

  if (PPM_OS::fcntl (handle, F_SETFL, val) == -1)
    return -1;
  else
    return 0;
#endif /* WIN32 || PPM_LACKS_FCNTL */
}

// Flags are the file status flags to turn off.

int
PPM_Flag_Manip::clr_flags (PPM_HANDLE handle, int flags)
{
  PPM_TRACE ("PPM_Flag_Manip::clr_flags");

#if defined (WIN32) || defined (VXWORKS) || defined (PPM_LACKS_FCNTL)
  switch (flags)
    {
    case PPM_NONBLOCK:
      // nonblocking argument (1)
      // blocking:            (0)
      {
        u_long nonblock = 0;
        return PPM_OS::ioctl (handle, FIONBIO, &nonblock);
      }
    default:
      PPM_NOTSUP_RETURN (-1);
    }
#else
  int val = PPM_OS::fcntl (handle, F_GETFL, 0);

  if (val == -1)
    return -1;

  // Turn flags off.
  PPM_CLR_BITS (val, flags);

  if (PPM_OS::fcntl (handle, F_SETFL, val) == -1)
    return -1;
  else
    return 0;
#endif /* WIN32 || PPM_LACKS_FCNTL */
}
