#ifndef _PPM_CONN_ACTOR_H_
#define _PPM_CONN_ACTOR_H_

#include "./PPM_Tcp_Client.h"
#include "./PPM_SOCK_Buffer.h"
#include "./PPM_Public_Var.h"
#include "./PPM_Reactor.h"

/**
客户端对象创建的时候需要应用自己new一个对象,所以,删除需要外面删除
在删除之前需要调用DeleteNotify通知底层要删除了,以免底层还在使用此
对象从而产生错误,另外底层产生错误时或者是对端socket关闭时而没有重
连标示,会有两个函数通知应用
InvalidNotify:通知socket已经不可用,再调用SendData函数无效
TcpClientDelNotify:通知对象的ptcpclient已经被删除,上层可以根据情况
                   删除对象了.
*/
class PPM_Client_Deal_Base : public CDataActor{
public:
	PPM_Client_Deal_Base(){ 
		ptcpclient = NULL;
		ptcpclient_old = NULL;
	};
	//在程序退出的时候需要通知ptcpclient已经不能用了
	virtual ~PPM_Client_Deal_Base(){
		this->DeleteNotify();
		this->CloseClient();
		if(NULL != ptcpclient){
			ptcpclient->SetDataActor(NULL);
		}else{
			if(NULL != ptcpclient_old){
				ptcpclient_old->SetDataActor(NULL);
			}
		}
	}
	///当socket不可用的时候会通知应用此客户端不可用了,如果需要的话
	///作相应的处理
	virtual void InvalidNotify(){
		ptcpclient_old = ptcpclient;
		ptcpclient = NULL;
	};
	virtual BOOL DealData(const BYTE* const pData, const int nCount)
	{
		return TRUE;
	}
	///通知PPM_Tcp_Client 对象已经删除
	virtual void TcpClientDelNotify(){
		InvalidNotify();
		ptcpclient_old = NULL;
		ptcpclient = NULL;
	};
	int SendData(const BYTE * const pBuf, const int nSize){
		if(NULL != ptcpclient){
			return ptcpclient->SendData(pBuf,nSize);
		}
		else{
			return -1;
		}
	};

	int RecvData(const BYTE * const pBuf, const int nSize){
		if(NULL != ptcpclient){
			return ptcpclient->RecvData(pBuf,nSize);
		}
		else{
			return -1;
		}
	};
	///主动关闭连接，将释放连接的处理
	void CloseClient(){
		if(NULL != ptcpclient){
			ptcpclient->CloseClient();
		}
	}
	///外面对象删除前，需要先调用此函数通知一下
	void DeleteNotify(){
		if(NULL != ptcpclient){
			ptcpclient->ActorDeleteNotify();
		}else if(NULL != ptcpclient_old){
			ptcpclient_old->ActorDeleteNotify();
		}
//		InvalidNotify();
	}
	//virtual int RecvDeal(BYTE* pBuf,int iMaxLen,PPM_SOCK_Stream* pSockStream){
	//	return 0;
	//}
	PPM_Tcp_Client* GetTcpClient(){
		return ptcpclient;
	};
	void SetTcpClient(PPM_Tcp_Client* pclient){
		ptcpclient = pclient;
	}
    
    virtual int RecvDeal(BYTE* pBuf,int iMaxLen,PPM_SOCK_Stream* pSockStream){
		return 0;
	}

private:
    PPM_Tcp_Client* ptcpclient;
    PPM_Tcp_Client* ptcpclient_old; //应用不要使用此变量
};
struct stru_client{
	PPM_Tcp_Client* pconn_type;
	PPM_SOCK_Buffer* psockbuffer;
	PPM_Client_Deal_Base* pClientDeal; //这个是具体的客户端处理类
	BYTE bIsClientValid;
};


class PPM_Tcp_Client_Actor{
public:
	~PPM_Tcp_Client_Actor(){
		//退出的时候需要删除buffer
		ReleaseAll();
		m_vecpConn.clear();
		m_vecpConn_unused.clear();
		PPM_OS::sleep(10);
	}
	int GetVecSize(){
		return m_vecpConn.size();
	};
	int AddClient(stru_client* clientinfo){
		m_Lock_.Lock();
		m_vecpConn.push_back(clientinfo);
		m_Lock_.Unlock();
		return 1;
	};
	//通知客户端tcpip连接已经不可用
	void ClearClientIDClientflag(stru_client* pclientinfo){
// 		m_Lock_.Lock();
// 		pclientinfo->bIsClientValid = 0;
		pclientinfo->pClientDeal->InvalidNotify();
// 		m_Lock_.Unlock();
	};
	void ResetSingleClient(stru_client* const pClient,PPM_Client_Deal_Base* newclientDeal){
		m_Lock_.Lock();
		if(NULL != pClient->pconn_type ){
			//用于通知上层client已经删除
			pClient->pClientDeal->TcpClientDelNotify();
			delete pClient->pconn_type;
			pClient->pClientDeal->SetTcpClient(NULL);
			pClient->pconn_type = NULL;
			pClient->bIsClientValid = 0;
		}
		pClient->pconn_type = newclientDeal->GetTcpClient();
		pClient->pClientDeal = newclientDeal;
		pClient->bIsClientValid = 1;
		m_Lock_.Unlock();

	};
	//只是提供一个接口给PPM_Client调用删除PPM_Tcp_Client的操作
	void DeleteClient(PPM_Tcp_Client* ptcpclient){
		delete ptcpclient;
	}
	// 释放客户端
	void ReleaseClient(stru_client* const pClient){
		stru_client* pstruClt = pClient;
		
        //用于通知上层client已经删除
		pClient->pClientDeal->TcpClientDelNotify();

		if (pstruClt != NULL)
		{
			std::vector<stru_client*>::iterator it;

			pstruClt->pconn_type->StopThread(3);
			pstruClt->pconn_type->UnRegiste();

			m_Lock_.Lock();
			it = std::find(m_vecpConn.begin(), m_vecpConn.end(), pstruClt);
			if(it != m_vecpConn.end()){
				m_vecpConn_unused.push_back(pstruClt);
				//释放原先的所有节点
				ReleaseAllOld();
				m_vecpConn.erase(it);
			}
			else{
				PPM_SOCKLOG((LM_ERROR,"need to release stru_client but not in vec_clientinfo\n"));
//				m_vecpConn_unused.push_back(pstruClt);

			}
			m_Lock_.Unlock();

// 			if(pstruClt->pconn_type != NULL){
// 				delete pstruClt->pconn_type;
// 				pstruClt->pconn_type = NULL;
// 			}
// 			if(pstruClt->psockbuffer != NULL){
// 				delete pstruClt->psockbuffer;
// 				pstruClt->psockbuffer = NULL;
// 			}
// 
// 			delete pstruClt;
// 
// 			pstruClt = NULL;
		}
	};
	void ReleaseAll(void){
		ReleaseAllOld();

		CStdAutoLock tm_autolock(&m_Lock_);

		stru_client* pstruClt = NULL;
		
		for (unsigned int i = 0; i < m_vecpConn.size(); i++)
		{
			pstruClt = m_vecpConn.at(i);
			if(pstruClt != NULL){
//				ReleaseClient(pstruClt);
				/*if (pstruClt->pconn_type != NULL)
				{
					delete pstruClt->pconn_type;
					pstruClt->pconn_type = NULL;
				}
				if (pstruClt->psockbuffer != NULL)
				{
					delete pstruClt->psockbuffer;
					pstruClt->psockbuffer = NULL;
				}*/
				delete pstruClt;
				pstruClt = NULL;
			}
		}
		m_vecpConn.clear();
	};
	void ReleaseAllOld(void){
		PPM_OS::sleep(100);
		CStdAutoLock tm_autolock(&m_Lock_);

		stru_client* pstruClt = NULL;
		
		for (unsigned int i = 0; i < m_vecpConn_unused.size(); i++)
		{
			pstruClt = m_vecpConn_unused.at(i);
			if(pstruClt != NULL){
// 				ReleaseClient(pstruClt);
				/*if (pstruClt->pconn_type != NULL)
				{
					delete pstruClt->pconn_type;
					pstruClt->pconn_type = NULL;
				}
				if (pstruClt->psockbuffer != NULL)
				{
					delete pstruClt->psockbuffer;
					pstruClt->psockbuffer = NULL;
				}*/
				delete pstruClt;
				pstruClt = NULL;
			}
		}
		m_vecpConn_unused.clear();
	};    //如果有，返回的是对应的id，如果没有则返回-1,输入错误返回-2
	stru_client* IsSocketBufferExists(const char* ipaddr,u_short portno)
	{
		CStdAutoLock tm_autolock(&m_Lock_);

		if(PPM_OS::strlen(ipaddr) > 20){
			return NULL;
		}
		for(unsigned int i = 0; i < m_vecpConn.size() ;i ++){
			if(NULL == m_vecpConn[i]->psockbuffer){
				continue;
			}
			stru_client* tm_pinfo = m_vecpConn[i];
			if( (portno == tm_pinfo->psockbuffer->m_portNo) && 
				(0 == PPM_OS::memcmp(ipaddr,tm_pinfo->psockbuffer->m_pipaddress,PPM_OS::strlen(ipaddr))) )
			{
				return m_vecpConn[i];
			}
		}
		return NULL;
	}
	virtual void ClientDelete(PPM_Tcp_Client* ptcpclient){
		PPM_SOCKLOG((LM_INFO,"delete tcpclient %d\n",ptcpclient));
	};

private:
	//用来保存已经建立的连接
	std::vector<stru_client*> m_vecpConn;

 	std::vector<stru_client*> m_vecpConn_unused;
	CStdLock m_Lock_;

};

class PPM_Server_Deal_Base : public CDataActor{
public:
	PPM_Server_Deal_Base(){ m_ptcpclient_ = NULL;}
	virtual ~PPM_Server_Deal_Base(){
	};
	void SetTcpClient(PPM_Tcp_Client* ptcpclient){
		m_ptcpclient_ = ptcpclient;
	}
	PPM_HANDLE GetHandle(){
		return m_ptcpclient_->get_handle();
	}
	virtual int SendData(const BYTE * const pBuf, const int nSize){
		if(NULL != m_ptcpclient_){
			return m_ptcpclient_->SendData(pBuf,nSize);
		}
		else{
			return -1;
		}
	};
 
	virtual int RecvDeal(BYTE* pBuf,int iMaxLen,PPM_SOCK_Stream* pSockStream){
		return 0;
	}
	virtual void BeginDeal(){};

	PPM_Tcp_Client* m_ptcpclient_;
private:
};


struct stru_server{
	PPM_Tcp_Client* ptcpclient;
	PPM_SOCK_Buffer* psockbuffer;
	PPM_Server_Deal_Base* pClientDeal; //这个是具体的客户端处理类
	PPM_Reactor* pReactor;
	BYTE bNeedDelReactor; //是否需要删除pReactor
	BYTE bIsClientValid;
};


class PPM_Tcp_Server_Actor{
public:
	PPM_Tcp_Server_Actor(){
		vec_clientinfo_.clear();
		vec_clientinfo_unused_.clear();
		m_bIsDeleteAll_ = 0;
	}
	virtual ~PPM_Tcp_Server_Actor(){
		//在释放Actor的时候，因为由它管理的tcpclient都使用到了它，所以需要将下属的所有
		//tcpclient处理干净才能退出
		ReleaseAll();
	}
	int GetVecSize(){
		return vec_clientinfo_.size();
	};
	///增加一个连接
	int AddClient(stru_server* pclientinfo){
		m_lock_.Lock();
		vec_clientinfo_.push_back(pclientinfo);
		m_lock_.Unlock();
		return 1;
	};
	///此函数为内部调用，外面不要管理此函数
	void ClearClientflag(stru_server* pclientinfo){
		if(1 == m_bIsDeleteAll_){
			return ;
		}
//	  CStdAutoLock tm_autolock(&m_lock_);
	  pclientinfo->bIsClientValid = 0;
	  ClientSockInvalid(pclientinfo->pClientDeal);
	};
	/// 释放客户端,在释放客户端的时候先通知应用客户端无效，然后删除PPM_Tcp_Client
	/// pClientDeal是在PPM_Tcp_Client的析购中删除的
	void ReleaseClient(stru_server* const pClient){
		if(1 == m_bIsDeleteAll_){
			return;
		}
		stru_server* pstruClt = pClient;
		
        //用于通知上层client已经无效
		if(pClient->bIsClientValid == 1){
			ClientSockInvalid(pstruClt->pClientDeal);
		}

		if (pstruClt != NULL)
		{
			std::vector<stru_server*>::iterator it;

			//确保线程停止,注册去掉，socket关闭即可
			pstruClt->ptcpclient->StopThread(3);
			pstruClt->ptcpclient->UnRegiste();


			m_lock_.Lock();
			//放入旧的当中
			it = std::find(vec_clientinfo_.begin(), vec_clientinfo_.end(), pstruClt);
			if(it != vec_clientinfo_.end()){
				//释放原先的所有节点
				ReleaseAllOld();
				vec_clientinfo_unused_.push_back(pstruClt);
				vec_clientinfo_.erase(it);
			}else{
				PPM_SOCKLOG((LM_ERROR,"need to release stru_server but not in vec_clientinfo_\n"));
			}
			m_lock_.Unlock();

// 			if(pstruClt->ptcpclient != NULL){
// 				delete pstruClt->ptcpclient;
// 				pstruClt->ptcpclient = NULL;
// 			}
// // 			if(pstruClt->pClientDeal != NULL){
// // 				delete pstruClt->pClientDeal;
// // 				pstruClt->pClientDeal = NULL;
// // 			}
// 			if(pstruClt->psockbuffer != NULL){
// 				delete pstruClt->psockbuffer;
// 				pstruClt->psockbuffer = NULL;
// 			}
// 			if(1 == pstruClt->bNeedDelReactor){
// 				if(NULL != pstruClt->pReactor){
// 					pstruClt->pReactor->end_event_loop();
// 					delete pstruClt->pReactor;
// 					pstruClt->pReactor = NULL;
// 				}
// 			}
// 
// 			delete pstruClt;
// 
// 			pstruClt = NULL;
 		}
	};
	//此函数为内部调用，外面不要管理此函数
	void SetClientflag(stru_server* pclientinfo){
		if(1 == m_bIsDeleteAll_){
			return ;
		}

	  CStdAutoLock tm_autolock(&m_lock_);
	  pclientinfo->bIsClientValid = 1;
	  NewClientShow(pclientinfo->pClientDeal);
	};
	//如果有，返回的是对应的id，如果没有则返回-1,输入错误返回-2
	stru_server* IsSocketBufferExists(const char* ipaddr,u_short portno)
	{
		CStdAutoLock tm_autolock(&m_lock_);

		if(PPM_OS::strlen(ipaddr) > 46){
			return NULL;
		}
		for(unsigned int i = 0; i < vec_clientinfo_.size() ;i ++){
			if(NULL == vec_clientinfo_[i]->psockbuffer){
				continue;
			}
			stru_server* tm_pinfo = vec_clientinfo_[i];
			if( (portno == tm_pinfo->psockbuffer->m_portNo) && 
				(0 == PPM_OS::memcmp(ipaddr,tm_pinfo->psockbuffer->m_pipaddress,PPM_OS::strlen(ipaddr))) )
			{
				return vec_clientinfo_[i];
			}
		}
		return NULL;
	}

	/// 通知客户端有新的连接进来
	virtual void NewClientShow(PPM_Server_Deal_Base* pServer_Deal){};
	virtual void MoreClientShow(void* pServer_Deal){};

	/// 通知客户端此对象将要删除
	virtual void ClientDelete(PPM_Server_Deal_Base* pServer_Deal){};

	/// 通知客户端此对象中的socket已经无效，不能发送数据，但可能会有收到的数据没有处理完
	virtual void ClientSockInvalid(PPM_Server_Deal_Base* pServer_Deal){};

	//在退出前释放
	void ReleaseAll(){
		ReleaseAllOld();

		m_bIsDeleteAll_ = 1;
		CStdAutoLock tm_autolock(&m_lock_);
		for(unsigned int i = 0; i < vec_clientinfo_.size(); i++){
			if(NULL != vec_clientinfo_[i]->ptcpclient){
//				vec_clientinfo_[i]->ptcpclient->CloseClient();
				delete vec_clientinfo_[i]->ptcpclient;
				vec_clientinfo_[i]->ptcpclient = NULL;
			}
			if(NULL != vec_clientinfo_[i]->psockbuffer){
				delete vec_clientinfo_[i]->psockbuffer;
				vec_clientinfo_[i]->psockbuffer = NULL;
			}
			//pClientDeal会在ptcpclient的析购中删除
// 			if(NULL != vec_clientinfo_[i]->pClientDeal){
// 				delete vec_clientinfo_[i]->pClientDeal;
// 				vec_clientinfo_[i]->pClientDeal = NULL;
// 			}
			if(1 == vec_clientinfo_[i]->bNeedDelReactor){
				if(NULL != vec_clientinfo_[i]->pReactor){
					vec_clientinfo_[i]->pReactor->end_event_loop();
					delete vec_clientinfo_[i]->pReactor;
					vec_clientinfo_[i]->pReactor = NULL;
				}
			}
			delete vec_clientinfo_[i];
			vec_clientinfo_[i] = NULL;
		}
		vec_clientinfo_.clear();

	};
	//释放所有旧的
	void ReleaseAllOld(){
		PPM_OS::sleep(100);
		CStdAutoLock tm_autolock(&m_lock_);
		for(unsigned int i = 0; i < vec_clientinfo_unused_.size(); i++){
			if(NULL != vec_clientinfo_unused_[i]->ptcpclient){
//				vec_clientinfo_[i]->ptcpclient->CloseClient();
				delete vec_clientinfo_unused_[i]->ptcpclient;
				vec_clientinfo_unused_[i]->ptcpclient = NULL;
			}
			if(NULL != vec_clientinfo_unused_[i]->psockbuffer){
				delete vec_clientinfo_unused_[i]->psockbuffer;
				vec_clientinfo_unused_[i]->psockbuffer = NULL;
			}
			//pClientDeal会在ptcpclient的析购中删除
// 			if(NULL != vec_clientinfo_[i]->pClientDeal){
// 				delete vec_clientinfo_[i]->pClientDeal;
// 				vec_clientinfo_[i]->pClientDeal = NULL;
// 			}
			if(1 == vec_clientinfo_unused_[i]->bNeedDelReactor){
				if(NULL != vec_clientinfo_unused_[i]->pReactor){
					vec_clientinfo_unused_[i]->pReactor->end_event_loop();
					delete vec_clientinfo_unused_[i]->pReactor;
					vec_clientinfo_unused_[i]->pReactor = NULL;
				}
			}
			delete vec_clientinfo_unused_[i];
			vec_clientinfo_unused_[i] = NULL;
		}
		vec_clientinfo_unused_.clear();

	};
    void SetClientIDTcpClient(stru_server* pclientinfo,PPM_Tcp_Client* ptcpclient)
	{
		pclientinfo->ptcpclient = ptcpclient;
		pclientinfo->bIsClientValid = 1;
	}
	CStdLock m_lock_;
private:

	//用来保存已经建立的连接
	std::vector<stru_server*> vec_clientinfo_;

	//用来保存已经建立的连接
	std::vector<stru_server*> vec_clientinfo_unused_;

	///已经全部删除
	BOOL m_bIsDeleteAll_;

};

class PPM_Tcp_Server_Actor_Null : public PPM_Tcp_Server_Actor
{
public:
	virtual void NewClientShow(PPM_Server_Deal_Base* pServer_Deal){};
	virtual void ClientDelete(PPM_Server_Deal_Base* pServer_Deal){};
	virtual void ClientSockInvalid(PPM_Server_Deal_Base* pServer_Deal){};

};



#endif
