
#ifndef _PPM_TIME_VALUE_H
#define _PPM_TIME_VALUE_H

#include "StdHeader.h"
#include "PPM_MACRO.h"
#include "PPM_typedef.h"

// Define some helpful constants.
// Not type-safe, and signed.  For backward compatibility.
#define PPM_ONE_SECOND_IN_MSECS 1000L
#define PPM_ONE_SECOND_IN_USECS 1000000L
#define PPM_ONE_SECOND_IN_NSECS 1000000000L

// -------------------------------------------------------------------
// These forward declarations are only used to circumvent a bug in
// MSVC 6.0 compiler.  They shouldn't cause any problem for other
// compilers and they can be removed once MS release a SP that contains
// the fix.
class PPM_Time_Value;
PPM_Time_Value operator + (const PPM_Time_Value &tv1,
                                         const PPM_Time_Value &tv2);
PPM_Time_Value operator - (const PPM_Time_Value &tv1,
                                         const PPM_Time_Value &tv2);
int operator < (const PPM_Time_Value &tv1,
                                       const PPM_Time_Value &tv2);
int operator > (const PPM_Time_Value &tv1,
                                       const PPM_Time_Value &tv2);
int operator <= (const PPM_Time_Value &tv1,
                                    const PPM_Time_Value &tv2);
int operator >= (const PPM_Time_Value &tv1,
                                    const PPM_Time_Value &tv2);
int operator == (const PPM_Time_Value &tv1,
                                    const PPM_Time_Value &tv2);
int operator != (const PPM_Time_Value &tv1,
                                    const PPM_Time_Value &tv2);



// -------------------------------------------------------------------


// -------------------------------------------------------------------

/**
包装的时间类，实际上是在结构timeval上进行的包装，windows的时间是从Jan. 1, 1970开始
计算，前面long表示秒数，后面long 1000表示1毫秒，时间可以表示到1970 + 68 = 2038年7月1日
 */
class PPM_Time_Value
{
public:

  /// Constant "0".
  static const PPM_Time_Value zero;

  /**
   * Constant for maximum time representable.  Note that this time is
   * not intended for use with <select> or other calls that may have
   * *their own* implementation-specific maximum time representations.
   * Its primary use is in time computations such as those used by the
   * dynamic subpriority strategies in the PPM_Dynamic_Message_Queue
   * class.
   */
  static const PPM_Time_Value max_time;

  // = Initialization methods.

  /// Default Constructor.
  PPM_Time_Value (void);

  /// Constructor.
  PPM_Time_Value (long sec, long usec = 0);

  // = Methods for converting to/from various time formats.

  /// Construct the PPM_Time_Value from a timeval.
  PPM_Time_Value (const struct timeval &t);

  /// Construct the PPM_Time_Value object from a timespec_t.
  PPM_Time_Value (const timespec_t &t);

  /// Initializes the PPM_Time_Value from two longs.
  void set (long sec, long usec);

  /// Initializes the PPM_Time_Value from a double, which is assumed to be
  /// in second format, with any remainder treated as microseconds.
  void set (double d);

  /// Initializes the PPM_Time_Value from a timeval.
  void set (const timeval &t);

  /// Initializes the PPM_Time_Value object from a timespec_t.
  void set (const timespec_t &t);

  /// Converts from PPM_Time_Value format into milli-seconds format.
  /**  毫秒数
   * @return Sum of second field (in milliseconds) and microsecond field
   *         (in milliseconds).
   *
   * @note The semantics of this method differs from the sec() and
   *       usec() methods.  There is no analogous "millisecond"
   *       component in an PPM_Time_Value.
   */
  __int64 msec (void) const;

  /// Converts from milli-seconds format into PPM_Time_Value format.
  /**
   * @note The semantics of this method differs from the sec() and
   *       usec() methods.  There is no analogous "millisecond"
   *       component in an PPM_Time_Value.
   */
  void msec (__int64);

  /// Returns the value of the object as a timespec_t.
  operator timespec_t () const;

  /// Returns the value of the object as a timeval.
  operator timeval () const;

  /// Returns a pointer to the object as a timeval.
  operator const timeval *() const;

  // = The following are accessor/mutator methods.

  /// Get seconds.
  /**
   * @return The second field/component of this PPM_Time_Value.
   *
   * @note The semantics of this method differs from the msec()
   *       method.
   */
  long sec (void) const;

  /// Set seconds.
  void sec (long sec);

  /// Get microseconds.
  /**
   * @return The microsecond field/component of this PPM_Time_Value.
   *
   * @note The semantics of this method differs from the msec()
   *       method.
   */
  long usec (void) const;

  /// Set microseconds.
  void usec (long usec);
  
  std::string timetostring() const;
  std::string timetostring2() const;

  int GetYear();
  int GetMonth();
  int GetDay();
  int GetHour();
  int GetMinute();
  int GetSecond();
  int GetMilliSecond();
  int GetTotalSeconds();
  __int64 DateDiff(const PPM_Time_Value&tBegin);
  int DayDiff(const PPM_Time_Value&tBegin);
  static std::string SecondToString(const int nSecond);
  BOOL SetTime(WORD wyear,BYTE bmonth,BYTE bday,BYTE bhour,BYTE bminute,BYTE bsecond,WORD wms);
  BOOL SetTime(char* strTime,int len);
  BOOL SetTime_NoDate(char* strTime,int len);
  BOOL SetTime_NoDateMS(char* strTime,int len);
  // = The following arithmetic methods operate on PPM_Time_Value's.

  /// Add @a tv to this.
  PPM_Time_Value &operator += (const PPM_Time_Value &tv);

  /// Subtract @a tv to this.
  PPM_Time_Value &operator -= (const PPM_Time_Value &tv);

  /// Multiply the time value by the @a d factor, which must be >= 0.
  PPM_Time_Value &operator *= (double d);

  /// Increment microseconds as postfix.
  /**
   * @note The only reason this is here is to allow the use of PPM_Atomic_Op
   * with PPM_Time_Value.
   */
  PPM_Time_Value operator++ (int);

  /// Increment microseconds as prefix.
  /**
   * @note The only reason this is here is to allow the use of PPM_Atomic_Op
   * with PPM_Time_Value.
   */
  PPM_Time_Value &operator++ (void);

  /// Decrement microseconds as postfix.
  /**
   * @note The only reason this is here is to allow the use of PPM_Atomic_Op
   * with PPM_Time_Value.
   */
  PPM_Time_Value operator-- (int);

  /// Decrement microseconds as prefix.
  /**
   * @note The only reason this is here is to allow the use of PPM_Atomic_Op
   * with PPM_Time_Value.
   */
  PPM_Time_Value &operator-- (void);

  /// Adds two PPM_Time_Value objects together, returns the sum.
  friend  PPM_Time_Value operator + (const PPM_Time_Value &tv1,
                                                  const PPM_Time_Value &tv2);

  /// Subtracts two PPM_Time_Value objects, returns the difference.
  friend  PPM_Time_Value operator - (const PPM_Time_Value &tv1,
                                                  const PPM_Time_Value &tv2);

  /// True if @a tv1 < @a tv2.
  friend  int operator < (const PPM_Time_Value &tv1,
                                       const PPM_Time_Value &tv2);

  /// True if @a tv1 > @a tv2.
  friend  int operator > (const PPM_Time_Value &tv1,
                                       const PPM_Time_Value &tv2);

  /// True if @a tv1 <= @a tv2.
  friend  int operator <= (const PPM_Time_Value &tv1,
                                        const PPM_Time_Value &tv2);

  /// True if @a tv1 >= @a tv2.
  friend  int operator >= (const PPM_Time_Value &tv1,
                                        const PPM_Time_Value &tv2);

  /// True if @a tv1 == @a tv2.
  friend  int operator == (const PPM_Time_Value &tv1,
                                        const PPM_Time_Value &tv2);

  /// True if @a tv1 != @a tv2.
  friend  int operator != (const PPM_Time_Value &tv1,
                                        const PPM_Time_Value &tv2);

  //@{
  /// Multiplies the time value by @a d
  friend  PPM_Time_Value operator * (double d,
                                                  const PPM_Time_Value &tv);

  friend  PPM_Time_Value operator * (const PPM_Time_Value &tv,
                                                  double d);
  //@}

  /// Dump is a no-op.
  /**
   * The dump() method is a no-op.  It's here for backwards compatibility
   * only, but does not dump anything. Invoking logging methods here
   * violates layering restrictions in ACE because this class is part
   * of the OS layer and @c PPM_Log_Msg is at a higher level.
   */
  void dump (void) const;
  void ConvertToNetSeq(){
	tv_.tv_sec = MAKEINT_NETSEQ1(tv_.tv_sec);
	tv_.tv_usec = MAKEINT_NETSEQ1(tv_.tv_usec);
  };
  void RevertNetSeq(){
	tv_.tv_sec = REVERTINT_NETSEQ1(tv_.tv_sec);
	tv_.tv_usec = REVERTINT_NETSEQ1(tv_.tv_usec);
  };

private:
  /// Put the timevalue into a canonical form.
  void normalize (void);

  /// Store the values as a timeval.
  timeval tv_;
};

#if !defined (PPM_LACKS_INLINE_FUNCTIONS)
# include "PPM_Time_Value.inl"
#endif

#endif /* PPM_TIME_VALUE_H */

