#ifndef __PPM_SELFMONITOR_H__
#define __PPM_SELFMONITOR_H__

#include "./StdHeader.h"
#include "./PPM_OS.h"
#include "./StdLock.h"
#include "./StdFileMapping.h"
#include "./PPM_Log_Msg.h"
#include "./PPM_Thread_Manager.h"


#define VERSION_LEN 10 
#define MONITOR_HEAD_LEN 22 //4 + 10 + 8
/**
自身监控模块,主要是监控
调用方式如下:
	要监测的线程对象需要从类PPM_ThreadDeal_Base来继承，需要实现的方法如下：
	ResetThread方法:即复位线程方法,对象默认为可复位属性
	main方法:是线程运行的主函数,注意,不是死循环
 例如CMapDeal基层自PPM_ThreadDeal_Base

  PPM_SelfMonitor tm_SelfMonitor("programname");

  CMapDeal* tm_mapdeal = new CMapDeal;
  tm_mapdeal->SetThreadName("mapdeal");//设置线程名称

  tm_SelfMonitor.Register(tm_mapdeal);
  //tm_SelfMonitor.main();
  tm_SelfMonitor.StartThread();
*/
class PPM_SelfMonitor: public PPM_ThreadDeal_Base
{
public:
	PPM_SelfMonitor(const char* sharemem_name,char* pchversion,char* pchprogramname,int memlen = MONITOR_HEAD_LEN,int checkspan = 30, int maxTimes = 10);
	virtual ~PPM_SelfMonitor(){
		StopThread();
		m_list_Thread_.clear();
	}
	///注册要监控的线程到监控模块
	void Register(PPM_ThreadDeal_Base* pMonitor);

	///移除所注册的模块
	void Remove(PPM_ThreadDeal_Base* pMonitor);
 
	///主函数，死循环
	BOOL main();

	///设置监控状态
	void SetMonitorFlag(BYTE flag){
		m_MonitorFlag_ = flag;
		m_FileMapping_.WriteFileMap(&flag,1);
	};

	void SetMonitorVersion(char* pchVersion);
	void SetMonitorTime();

	///检测各个线程运行状况
	BOOL CheckStatus();

	///获取监控状态
	BYTE GetMonitorFlag(){
		return m_MonitorFlag_;
	};
	BOOL ResetThread() {
        return TRUE;
    }
    void SetCheckSpan(int span) { 
        checkSpan = span;
    }
	//统一 前4个字节表示为进程监控
	void ReadMapContent(void* const pMapData, const int nLen){
	    m_FileMapping_.ReadFileMap( pMapData, nLen,MONITOR_HEAD_LEN );
	}
	//统一 前4个字节表示为进程监控
	void WriteMapContent(void* const pMapData, const int nLen){
	    m_FileMapping_.WriteFileMap( pMapData, nLen,MONITOR_HEAD_LEN );
	}

private:
	///线程正常运行标志
	BYTE m_MonitorFlag_;

	///所注册的监控对象
	std::list<PPM_ThreadDeal_Base*> m_list_Thread_;

	///贡献内存成员
	CStdFileMapping m_FileMapping_;

	//记录异常的累计次数
	int times;

    //检测时间间隔，单位为秒。要求checkSpan>被监控对象的允许异常自恢复最长时间
    int checkSpan;
	
	//允许的最大异常次数。
	int m_nMaxErrorTimes;

	CStdLock m_lock_;

	char m_pchVersion_[10];
	char m_pchProgramName_[30];
};



#endif
