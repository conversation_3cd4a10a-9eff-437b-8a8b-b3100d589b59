#ifndef _PPM_TCP_SERVER_H_
#define _PPM_TCP_SERVER_H_

#include "./PPM_SOCK_Acceptor.h"
#include "./PPM_Event_Handler.h"
#include "./PPM_Log_Msg.h"
#include "./PPM_CONN_Actor.h"
#include "./PPM_Thread_Manager.h"
#include "./PPM_Reactor.h"

#define MAX_SERVER_CONN 500 
struct stru_bufferconfig{
	BYTE b_IsExclusive;
	//异常时的数据处理原则，是否将未处理的数据处理完，
	//1表示收处理,在程序退出时处理完所收的数据,或者是重启后处理;
	//2表示发处理，在程序重启后处理完未发的数据
	//3表示收发都处理,0表示都不处理
	BYTE b_DealDataFlag;
	int i_RecvNode;
	int i_SendNode;
	BYTE b_RecvNodeType;
	BYTE b_SendNodeType;
};

//PPM_THR_FUNC_RETURN ListenThread(void * argus);

template <class T_SVC_HANDLER>
class PPM_Tcp_Server : public PPM_Event_Handler
{
public:
	//构造函数，创建一个新的socket,注意，
	//这里的server_actor主要是为了要上面的业务处理
/**
此函数用来创建服务端
输入：
		PPM_INET_Addr addr  : 服务端地址,里面包括端口号
		PPM_Tcp_Server_Actor* server_actor :给服务端提供可以知道什么时候来新连接和什么时候
		                      客户端的连接实效的回调
		int type: 是tcp还是udp
		BYTE bNoLoss: 是否处理完接受到的数据1表示收处理,2表示发处理,3表示收发都处理,0表示都不处理
		BYTE bIsExclusive :是否对于同一客户端ip和侦听端口只能连一次
		const BYTE eRNodeType: 收缓冲类型
		const BYTE eSNodeType: 发缓冲类型
		const int nRecvNode: 收缓冲节点数
		const int nSendNode :发缓冲节点数
注:缓冲类型:
   CNodeMng::BuffMode 内存模式,对于收数据缓冲而言会注册到反应器,可以收数据,
   CNodeMng::FileMode 文件模式,直接通过文件进行缓存,不通过内存,一般不用
   CNodeMng::BothMode 两种模式,如果收缓冲定义为此模式,则会注册到反应器,收数据,先
                                 内存缓冲,再文件缓冲
   CNodeMng::NullMode 无缓冲,对于收缓冲定义这种情况的话,是不能够触发收数据的,适合
                               那种只发,不收的那种情况,socket为阻塞方式,不注册到反应器
	缓冲节点数:是针对有内存模式的情况,1个节点是8k

注意:
    在服务端会新new一个T_SVC_HANDLER对象,其指针会在NewClientShow中告诉你,当对象的socket
因为某种原因socket不能使用时,会通过ClientDelete通知应用的.但这个对象的删除
是在PPM_Tcp_Client的析购函数中删除的,是在处理完收到的数据后才真正删除对象的
    在重用buffer的情况下，只new一个新的PPM_Tcp_Client对象，在重用时先通知应用此socket不能
使用，但在PPM_Tcp_Client的析购函数中不会删除，因为它又给新的重用了

modified by yht:
   server可能产生如下几个线程:
   1 reactor的侦听端口线程
   2 每个连接有一个侦听数据线程
   3 可能有收数据线呈，如果收结点类型不为NullMode的话
   4 可能有发数据线呈，如果发节点类型不为NullMode的话
*/
	PPM_Tcp_Server( PPM_INET_Addr addr , 
			PPM_Tcp_Server_Actor* server_actor = NULL,
			int type = SOCK_STREAM,
			BYTE bNoLoss = 1,//默认是不能丢失所收到的数据
			BYTE bIsExclusive = 1,//默认是做IP和端口唯一性的限制
			const BYTE eRNodeType = CNodeMng::NullMode,//收缓存类型
			const BYTE eSNodeType = CNodeMng::NullMode,//发缓存类型
			const int nRecvNode = STD_NODE_COUNT, //节点大小
			const int nSendNode = STD_NODE_COUNT,
			const long lListenThreadPriority = PPM_DEFAULT_THREAD_PRIORITY, //reactor的侦听端口线程
			const long lDecDataThreadPriority = PPM_DEFAULT_THREAD_PRIORITY,//每个连接有一个侦听数据线程
			const long lRecDataThreadPriority = PPM_DEFAULT_THREAD_PRIORITY,//可能有收数据线呈，如果收结点类型不为NullMode的话
			const long lSendDataThreadPriority = PPM_DEFAULT_THREAD_PRIORITY,//可能有发数据线呈，如果发节点类型不为NullMode的话
			const BYTE bSendHeadFlag = 1, //发送是否加AA55标示
			const BYTE bRecvHeadFlag = 1,  //接收是否加AA55标示
			const BYTE bIsNeedNewReactor = 1  //是否需要新的反应器,默认为需要
	){

		m_bIsNeedNewReactor_ = bIsNeedNewReactor;
		m_lListenThreadPriority_ = lListenThreadPriority;
		m_lDecDataThreadPriority_ = lDecDataThreadPriority;
		m_lRecDataThreadPriority_ = lRecDataThreadPriority;
		m_lSendDataThreadPriority_ = lSendDataThreadPriority;

		m_bSendHeadFlag_ = bSendHeadFlag;
		m_bRecvHeadFlag_ = bRecvHeadFlag;
		m_bexit_ = 0;
		m_ServerStatus = 0;
#ifdef WIN32
		//首先初始化通讯的环境
		WSADATA wsaData;
		WORD wVersionRequested = MAKEWORD_NETSEQ(2, 2);
		int nResult = WSAStartup(wVersionRequested, &wsaData);
		if(nResult != 0){
			PPM_SOCKLOG((LM_ERROR,"Server WSAStartup Failed! %m\n"));
			return;
		} 
		//如果初始化的不是我们想要的版本,退出
		if (LOBYTE(wsaData.wVersion) != 2 || HIBYTE(wsaData.wVersion) != 2)
		{
		   WSACleanup();
		   PPM_SOCKLOG((LM_ERROR,"Server WSADATA version is%d.%d,is error! \n",
			   HIBYTE(wsaData.wVersion),
			   LOBYTE(wsaData.wVersion)));
		   return ;
		}
#endif
		ready_delete_ = 0;
		//所有的server对应一个反应器,全局的
		reactor(PPM_Reactor::instance());
		
		PPM_OS::memset(&stru_bufconf_,0,sizeof(stru_bufconf_));
		stru_bufconf_.b_IsExclusive = bIsExclusive;
		stru_bufconf_.b_RecvNodeType = eRNodeType;
		stru_bufconf_.b_SendNodeType = eSNodeType;
		stru_bufconf_.i_RecvNode = nRecvNode;
		stru_bufconf_.i_SendNode = nSendNode;
		stru_bufconf_.b_DealDataFlag = bNoLoss;

    if(NULL == server_actor){
			needdel_ = 1;
			server_actor_ = new PPM_Tcp_Server_Actor_Null;
		}
		else{
			needdel_ = 0;
			server_actor_ = server_actor;
		}
		server_port_ = addr.get_port_number();
		
		//保存创建的服务端对象的协议类型，用于在接受套接字时使用，用于区分IPv4和IPv6
		add_family = addr.get_type();
		add_size = addr.get_size();

//	if(addr.get_port_number() == 0){
//		if(sock_acceptor_.open(PPM_Addr::sap_any) < 0){
//			PPM_SOCKLOG((LM_ERROR,"open acceptor error %m\n"));
//		};
//	}else{
		if(sock_acceptor_.open(addr) < 0){
			PPM_SOCKLOG((LM_ERROR,"open acceptor error %m\n"));
			//端口侦听失败
			m_ServerStatus = -1;
		};
//	}
//for test
//#ifdef _DEBUG
//	sockaddr_in tm_paddrin;
//	int tm_len1 = sizeof(sockaddr_in);
//	tm_len1 = ::getsockname((SOCKET)sock_acceptor_.get_handle(),(sockaddr *)&tm_paddrin,&tm_len1);
//	PPM_INET_Addr tm_inetaddr;
//	tm_inetaddr.set(&tm_paddrin,sizeof(sockaddr_in));
//		PPM_SOCKLOG((LM_INFO," Start listen port:%d\n",tm_inetaddr.get_port_number()));
//#endif
//for test end

		Listen();

		PPM_SOCKLOG((LM_INFO," Start listen port:%d\n",addr.get_port_number()));
	};
	//析构
    ~PPM_Tcp_Server(){
		m_bexit_ = 1;
		//这里析购的时候会调用到handleclose操作
		this->reactor()->remove_handler(this,HI_PRIORITY | ACCEPT_MASK);
		//我们默认等5秒钟,handleclose操作完成
		//所有通过这个server产生的连接都删除
		server_actor_->ReleaseAll();
		
		if(1 == needdel_){
			delete server_actor_;
		}
		int i = 0;
		while(0 == ready_delete_){
			PPM_OS::sleep(10);
			if(i++ > 500){
				PPM_SOCKLOG((LM_ERROR,"Wait for Server handleclose 5 seconds!\n"));
				break;
			}
		}
#ifdef WIN32
		//清除通讯的环境
		if(0 > WSACleanup()){
			PPM_SOCKLOG((LM_ERROR,"Server Error ocurr when WSACleanup! %m \n"));
		}
#endif
	};
  /// 被动接收数据.
  virtual int handle_input (PPM_HANDLE fd = PPM_INVALID_HANDLE);
  virtual int handle_close(PPM_HANDLE handle, PPM_Reactor_Mask close_mask){
	  PPM_SOCKLOG((LM_ERROR,"Stop listen Port %d! %m \n",server_port_));
	  sock_acceptor_.close();
	  if(0 == m_bexit_)
	  {
		  PPM_INET_Addr tm_addr(server_port_);
			if(sock_acceptor_.open(tm_addr) < 0){
				PPM_SOCKLOG((LM_ERROR,"open acceptor error %m\n"));
			};

			Listen();
			PPM_SOCKLOG((LM_ERROR," ReStart listen port:%d\n",tm_addr.get_port_number())); 
	  }
	  else
	  {
		  ready_delete_ = 1;
	  }
	  return 0;
  };

  virtual PPM_HANDLE get_handle (void) const{
		return sock_acceptor_.get_handle();
  };
  //开始侦听，运行检测循环
  int Listen();

public:
	PPM_THR_FUNC_RETURN ListenThread(void * argus,long priority)
	{
		PPM_Tcp_Server* pSvr = (PPM_Tcp_Server*)argus;
		
		return pSvr->CommMain(priority);
	};

	int GetServerStatus()
	{
		return m_ServerStatus;
	}

	int GetAddrfamily(void)
	{
		return add_family;
	};

	int GetAddrsize(void)
	{
		return add_size;
	};

private:
	PPM_THR_FUNC_RETURN CommMain(long priority){
		  reactor(PPM_Reactor::instance());
		  
		  
		  this->reactor()->register_handler(this,HI_PRIORITY|ACCEPT_MASK);

//		  this->reactor()->run_event_loop();
		  this->reactor()->SetThreadName("main listen reactor thread");
		  this->reactor()->StartThread(priority);
		  
		  return 0;
	};
   
	//用来侦听的socket
//	PPM_SOCK_Stream sock_;
	//socket接收器
	PPM_SOCK_Acceptor  sock_acceptor_;
	//用于传给PPM_Tcp_Client的回调对象
	T_SVC_HANDLER* svc_handler;
    //用于通知应用有新的连接到达
	PPM_Tcp_Server_Actor* server_actor_;

	PPM_hthread_t server_reactor_handle_;
	u_short server_port_;
	int needdel_;
    //侦听的buffer类型
	stru_bufferconfig stru_bufconf_;

	long m_lListenThreadPriority_; //reactor的侦听端口线程
	long m_lDecDataThreadPriority_;//每个连接有一个侦听数据线程
	long m_lRecDataThreadPriority_;//可能有收数据线呈，如果收结点类型不为NullMode的话
	long m_lSendDataThreadPriority_;//可能有发数据线呈，如果发节点类型不为NullMode的话

	BYTE m_bexit_; //表示退出，不需要重新侦听了。
	BYTE ready_delete_; //这个标志标示是否可以删除了

	BYTE m_bSendHeadFlag_ ;
	BYTE m_bRecvHeadFlag_ ;
	BYTE m_bIsNeedNewReactor_;

	int m_ServerStatus; //标志服务端的初状态，负数为异常

	int add_family;       
	int add_size;
};

//在新的连接到来时使用
template<class T_SVC_HANDLER>
inline int PPM_Tcp_Server<T_SVC_HANDLER>::handle_input(PPM_HANDLE fd)
{

	PPM_INET_Addr remote_address;
	PPM_SOCK_Stream stream;

	remote_address.set_type(this->GetAddrfamily());
	remote_address.set_size(this->GetAddrsize());

	int result = this->sock_acceptor_.accept (stream, // stream
									   &remote_address, // remote address
									   0, // timeout
									   1, // restart
									   1);  // reset new handler
	if(-1 == result){
		PPM_SOCKLOG((LM_ERROR,"accept error!\n"));
		return 0; 
	}
	char p_strip[50];
	u_short remote_portno = remote_address.get_port_number();
	PPM_OS::snprintf(p_strip,sizeof(p_strip),"%s",remote_address.get_host_addr());
	PPM_SOCKLOG ((LM_INFO, "Remote connection from: %s port no: %d handle %d\n",
		  p_strip,
		  remote_portno,
		  stream.get_handle()));
	remote_address.dump ();

	//判断是否已经有socket_buffer
	stru_server* preused_stru = server_actor_->IsSocketBufferExists(remote_address.get_host_addr(),server_port_);
	BOOL isTrue = FALSE;
	if(NULL == preused_stru)
	{
	  //没有相关buffer
	  isTrue = TRUE;
	}
	else if( 1 != preused_stru->psockbuffer->GetExclusiveFlag() ){
	  //BUFFER唯一
	  isTrue = TRUE;
	}
	else{
	  isTrue = FALSE;
	}

	T_SVC_HANDLER *handler = NULL;
	PPM_Tcp_Client *tcpclient = NULL;
	PPM_SOCK_Buffer* pSockBuf = NULL;
	if(TRUE == isTrue){
		//首先看是否有可用的连接
		if(NULL == server_actor_){
			return 0;
		}
		if(server_actor_->GetVecSize() > MAX_SERVER_CONN){
			PPM_SOCKLOG((LM_ERROR,"PPM_Server::maybe have create too many connections."));
			return 0;
		}

		//没有对应的buffer
		//创建新的socketbuffer
		//创建一个新的socket处理回调对象，创建一个新的PPM_Tcp_Client，并将回调赋给PPM_Tcp_Client
		pSockBuf = new PPM_SOCK_Buffer(p_strip,server_port_,
			                           stru_bufconf_.i_RecvNode,stru_bufconf_.b_RecvNodeType,
			                           stru_bufconf_.i_SendNode,stru_bufconf_.b_SendNodeType,
									   stru_bufconf_.b_IsExclusive,stru_bufconf_.b_DealDataFlag);

		handler = new T_SVC_HANDLER;

		//对于新建的TCPCLIENT,都需要有自己的reactor
		PPM_Reactor* tm_Reactor = NULL;
		if(1 == m_bIsNeedNewReactor_){
			tm_Reactor = new PPM_Reactor;
		}
		tcpclient = new PPM_Tcp_Client(stream,tm_Reactor,pSockBuf,handler,
			m_lDecDataThreadPriority_,m_lRecDataThreadPriority_,m_lSendDataThreadPriority_,
			m_bSendHeadFlag_,m_bRecvHeadFlag_
			);

		if(NULL != tcpclient){
			handler->SetTcpClient(tcpclient);
			stru_server* tm_struServer = new stru_server;
			tm_struServer->pClientDeal = handler;
			tm_struServer->pReactor = tm_Reactor;
			tm_struServer->bNeedDelReactor = 1;
			tm_struServer->bIsClientValid = 1;
			tm_struServer->ptcpclient = tcpclient;
			tm_struServer->psockbuffer = pSockBuf;
			server_actor_->AddClient(tm_struServer);

			//将server_actor_赋给tcpclient，用于client关闭时的通知
			tcpclient->SetServerActor(server_actor_);
			//将tm_clientid赋给tcpclient，用于client关闭时的通知
			tcpclient->SetServerStru(tm_struServer);
			//通知应用有新的连接到达
			server_actor_->NewClientShow(handler);
			server_actor_->MoreClientShow((void*)tm_struServer);
			tcpclient->Registe();
		}
		else{
			if(NULL != tm_Reactor)
				delete tm_Reactor;
			PPM_SOCKLOG((LM_ERROR,"create tcpclient error %m\n"));
			return -1;
		}	  
	}
	else{
		PPM_Tcp_Client* pold_client = preused_stru->ptcpclient;
		pSockBuf = preused_stru->psockbuffer;
		handler = (T_SVC_HANDLER *)preused_stru->pClientDeal;
		//处理掉原先的对象
	    pold_client->ServerReset(stream) ;

		//对于新建的TCPCLIENT,都需要有自己的reactor
		PPM_Reactor* tm_reReactor = NULL;
		if(1 == m_bIsNeedNewReactor_){
			tm_reReactor = new PPM_Reactor;
			preused_stru->pReactor = tm_reReactor;
		}else{
			preused_stru->pReactor = NULL;
		}
		//新建一个client对象，但共用原先的buffer

		//对于新建的TCPCLIENT,都需要有自己的reactor
		tcpclient = new PPM_Tcp_Client(stream,preused_stru->pReactor,pSockBuf,handler,
			m_lDecDataThreadPriority_,m_lRecDataThreadPriority_,m_lSendDataThreadPriority_,
			m_bSendHeadFlag_,m_bRecvHeadFlag_);
		if(NULL != tcpclient){
			handler->SetTcpClient(tcpclient);
			//将server_actor_赋给tcpclient，用于client关闭时的通知
			tcpclient->SetServerActor(server_actor_);

			//将tm_clientid赋给tcpclient，用于client关闭时的通知
			tcpclient->SetServerStru(preused_stru);
			server_actor_->SetClientIDTcpClient(preused_stru,tcpclient);

			//通知应用有新的连接到达,虽然说这个地方是从用的原先的对象指针,但在实效之前
			//已经通知应用层这个对象不能使用了
			server_actor_->NewClientShow(preused_stru->pClientDeal);
			server_actor_->MoreClientShow((void*)preused_stru);
			tcpclient->Registe();
		}
		else{
		  PPM_SOCKLOG((LM_ERROR,"create tcpclient2 error %m\n"));
		  return -1;
		}	  

	}

  return 0;
}

template<class T_SVC_HANDLER>
inline int PPM_Tcp_Server<T_SVC_HANDLER>::Listen()
{
//	  reactor(PPM_Reactor::instance());
	  //如果反应器已经运行了就不再运行第二遍
	  if(TRUE == reactor()->reactor_event_loop_done()){
		  this->reactor()->register_handler(this,HI_PRIORITY|ACCEPT_MASK);
		  return 0;
	  }

	  ListenThread(this,m_lListenThreadPriority_);

//	  PPM_Thread_Manager* const thrmng = PPM_Thread_Manager::instance();
//	  //服务的侦听线程
// 	  thrmng->spawn((PPM_THR_FUNC)ListenThread, &server_reactor_handle_, this,
//		  THR_NEW_LWP | THR_JOINABLE | THR_DETACHED,  0, m_lListenThreadPriority_);

      return 0;
}
#endif
