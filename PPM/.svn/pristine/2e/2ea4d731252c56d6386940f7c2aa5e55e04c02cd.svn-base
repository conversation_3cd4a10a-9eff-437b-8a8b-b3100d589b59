// Magic number declaration and definition for ctime and ctime_r ()
static const int ctime_buf_size = 26;

#define PPM_SOCKCALL_RETURN(OP,TYPE,FAILVALUE) PPM_OSCALL_RETURN(OP,TYPE,FAILVALUE)

PPM_INLINE int
PPM_Countdown_Time::start (void)
{
  if (this->max_wait_time_ != 0)
    {
      this->start_time_ = PPM_OS::gettimeofday ();
      this->stopped_ = 0;
    }
  return 0;
}

PPM_INLINE int
PPM_Countdown_Time::stopped (void) const
{
  return stopped_;
}

PPM_INLINE int
PPM_Countdown_Time::stop (void)
{
  if (this->max_wait_time_ != 0 && this->stopped_ == 0)
    {
      PPM_Time_Value elapsed_time =
        PPM_OS::gettimeofday () - this->start_time_;

      if (*this->max_wait_time_ > elapsed_time)
        *this->max_wait_time_ -= elapsed_time;
      else
        {
          // Used all of timeout.
          *this->max_wait_time_ = PPM_Time_Value::zero;
          // errno = ETIME_;
        }
      this->stopped_ = 1;
    }
  return 0;
}

PPM_INLINE int
PPM_Countdown_Time::update (void)
{
  return this->stop () == 0 && this->start ();
}


PPM_INLINE int
PPM_OS::sleep (u_int mseconds)
{
//  PPM_OSCALL_RETURN (::sleep (mseconds), int, -1);
	timeval tv_copy;
	tv_copy.tv_sec = mseconds / 1000;
	tv_copy.tv_usec = (mseconds % 1000) * 1000;
  PPM_OSCALL_RETURN (::select (0, 0, 0, 0, &tv_copy), int, -1);
}

PPM_INLINE int
PPM_OS::sleep (const PPM_Time_Value &tv)
{
  // Copy the timeval, because this platform doesn't declare the timeval
  // as a pointer to const.
  timeval tv_copy = tv;
  PPM_OSCALL_RETURN (::select (0, 0, 0, 0, &tv_copy), int, -1);
 
  return 0;

}


PPM_INLINE int
PPM_OS::ioctl (PPM_HANDLE handle,
               int cmd,
               void *val)
{
  PPM_OSCALL_RETURN (ioctl (handle, cmd, val), int, -1);

}

PPM_INLINE int
PPM_OS::getsockname (PPM_HANDLE handle,
                     struct sockaddr *addr,
                     int *addrlen)
{
  PPM_SOCKCALL_RETURN (::getsockname ((PPM_SOCKET) handle,
                                      addr,
                                      (PPM_SOCKET_LEN *) addrlen),
                       int, -1);
}

PPM_INLINE int
PPM_OS::getsockopt (PPM_HANDLE handle,
                    int level,
                    int optname,
                    char *optval,
                    int *optlen)
{
  PPM_SOCKCALL_RETURN (::getsockopt ((PPM_SOCKET) handle,
                                     level,
                                     optname,
                                     optval,
                                     (PPM_SOCKET_LEN *) optlen),
                       int,
                       -1);
}

PPM_INLINE int
PPM_OS::setsockopt (PPM_HANDLE handle,
                    int level,
                    int optname,
                    const char *optval,
                    int optlen)
{

  // To work around an inconsistency with Microsofts implementation of
  // sockets, we will check for SO_REUSEADDR, and ignore it. Winsock
  // always behaves as if SO_REUSEADDR=1. Some implementations have the
  // same behaviour as Winsock, but use a new name for it. SO_REUSEPORT.
  // If you want the normal behaviour for SO_REUSEADDR=0, then NT 4 sp4 and later
  // supports SO_EXCLUSIVEADDRUSE. This also requires using an updated Platform SDK
  // so it was decided to ignore the option for now. (Especially since ACE always
  // sets SO_REUSEADDR=1, which we can mimic by doing nothing.)
//   if (level == SOL_SOCKET) {
//     if (optname == SO_REUSEADDR) {
//       return 0; // Not supported by Winsock
//     }
//     if (optname == SO_REUSEPORT) {
//       optname = SO_REUSEADDR;
//     }
//   }

  PPM_SOCKCALL_RETURN (::setsockopt ((PPM_SOCKET) handle,
                                     level,
                                     optname,
                                     (const char *) optval,
                                     optlen),
                       int,
                       -1);
}

PPM_INLINE int
PPM_OS::getpeername (PPM_HANDLE handle, struct sockaddr *addr,
                     int *addrlen)
{
  PPM_SOCKCALL_RETURN (::getpeername ((PPM_SOCKET) handle,
                                      addr,
                                      (PPM_SOCKET_LEN *) addrlen),
                       int, -1);
}

PPM_INLINE int
PPM_OS::closesocket (PPM_HANDLE handle)
{
  PPM_OSCALL_RETURN (::close (handle), int, -1);
}

PPM_INLINE PPM_HANDLE
PPM_OS::socket (int domain,
                int type,
                int proto)
{
  PPM_SOCKCALL_RETURN (::socket (domain,
                                 type,
                                 proto),
                       PPM_HANDLE,
                       PPM_INVALID_HANDLE);
}

PPM_INLINE PPM_HANDLE
PPM_OS::accept (PPM_HANDLE handle,
                struct sockaddr *addr,
                int *addrlen)
{                      
  PPM_HANDLE PPM_result = ::accept ((PPM_SOCKET) handle,
                                    addr,
                                    (PPM_SOCKET_LEN *) addrlen) ;
  if (PPM_result == PPM_INVALID_HANDLE && errno == EAGAIN)
    errno = EWOULDBLOCK;
  return PPM_result;
}
PPM_INLINE int
PPM_OS::bind (PPM_HANDLE handle, struct sockaddr *addr, int addrlen)
{

  PPM_SOCKCALL_RETURN (::bind ((PPM_SOCKET) handle,
                               addr,
                               (PPM_SOCKET_LEN) addrlen), int, -1);
}

PPM_INLINE int
PPM_OS::connect (PPM_HANDLE handle,
                 struct sockaddr *addr,
                 int addrlen)
{
  PPM_SOCKCALL_RETURN (::connect ((PPM_SOCKET) handle,
                                  addr,
                                  (PPM_SOCKET_LEN) addrlen), int, -1);
}
PPM_INLINE int
PPM_OS::listen (PPM_HANDLE handle, int backlog)
{
  PPM_SOCKCALL_RETURN (::listen ((PPM_SOCKET) handle, backlog), int, -1);
}

PPM_INLINE int
PPM_OS::recv (PPM_HANDLE handle, char *buf, size_t len, int flags)
{

  // On UNIX, a non-blocking socket with no data to receive, this
  // system call will return WSAEWOULDBLOCK or EAGAIN, depending on the
  // platform.  UNIX 98 allows either errno, and they may be the same
  // numeric value.  So to make life easier for upper ACE layers as
  // well as application programmers, always change EAGAIN to
  // WSAEWOULDBLOCK.  Rather than hack the PPM_OSCALL_RETURN macro, it's
  // handled explicitly here.  If the PPM_OSCALL macro ever changes,
  // this function needs to be reviewed.  On Win32, the regular macros
  // can be used, as this is not an issue.

  int PPM_result_;
  PPM_result_ = ::recv ((PPM_SOCKET) handle, buf, len, flags);
  if (PPM_result_ == -1 && errno == EAGAIN)
    errno = EWOULDBLOCK;
  return PPM_result_;
  
//  PPM_SOCKCALL_RETURN (::recv ((PPM_SOCKET) handle, buf,
//                               PPM_static_cast (int, len), flags), int, -1);
}

PPM_INLINE int
PPM_OS::recvfrom (PPM_HANDLE handle,
                  char *buf,
                  size_t len,
                  int flags,
                  struct sockaddr *addr,
                  int *addrlen)
{
  PPM_SOCKCALL_RETURN (::recvfrom ((PPM_SOCKET) handle, buf, len, flags,
                                   addr, (PPM_SOCKET_LEN *) addrlen),
                       int, -1);

}

PPM_INLINE int
PPM_OS::send (PPM_HANDLE handle, const char *buf, size_t len, int flags)
{
  PPM_OS_TRACE ("PPM_OS::send");

  // On UNIX, a non-blocking socket with no data to receive, this
  // system call will return WSAEWOULDBLOCK or EAGAIN, depending on the
  // platform.  UNIX 98 allows either errno, and they may be the same
  // numeric value.  So to make life easier for upper ACE layers as
  // well as application programmers, always change EAGAIN to
  // WSAEWOULDBLOCK.  Rather than hack the PPM_OSCALL_RETURN macro, it's
  // handled explicitly here.  If the PPM_OSCALL macro ever changes,
  // this function needs to be reviewed.  On Win32, the regular macros
  // can be used, as this is not an issue.

  int PPM_result_;
  PPM_result_ = ::send ((PPM_SOCKET) handle, buf, len, flags);
  if (PPM_result_ == -1 && errno == EAGAIN)
    errno = EWOULDBLOCK;
  return PPM_result_;
}
PPM_INLINE int
PPM_OS::sendto (PPM_HANDLE handle,
                const char *buf,
                size_t len,
                int flags,
                const struct sockaddr *addr,
                int addrlen)
{
  PPM_OS_TRACE ("PPM_OS::sendto");

  PPM_SOCKCALL_RETURN (::sendto ((PPM_SOCKET) handle, buf, len, flags,
                                 PPM_const_cast (struct sockaddr *, addr), addrlen),
                       int, -1);

}

// It would be really cool to add another version of select that would
// function like the one we're defending against below!
PPM_INLINE int
PPM_OS::select (int width,
                fd_set *rfds, fd_set *wfds, fd_set *efds,
                const PPM_Time_Value *timeout)
{
  PPM_OS_TRACE ("PPM_OS::select");
	if((0 == rfds) && (0 == wfds) && (0 == efds))	 
	{
		PPM_OS::sleep(*timeout);
		return -1;
	}

  // We must defend against non-conformity!
  timeval copy;
  timeval *timep;

  if (timeout != 0)
    {
      copy = *timeout;
      timep = &copy;
    }
  else
    timep = 0;

  PPM_SOCKCALL_RETURN (::select (width,
                                 (PPM_FD_SET_TYPE *) rfds,
                                 (PPM_FD_SET_TYPE *) wfds,
                                 (PPM_FD_SET_TYPE *) efds,
                                 timep),
                       int, -1);
}

PPM_INLINE int
PPM_OS::select (int width,
                fd_set *rfds, fd_set *wfds, fd_set *efds,
                const PPM_Time_Value &timeout)
{
  PPM_OS_TRACE ("PPM_OS::select");
	if((0 == rfds) && (0 == wfds) && (0 == efds))	 
	{
		PPM_OS::sleep(timeout);
		return -1;
	}

# define ___PPM_TIMEOUT &copy
  timeval copy = timeout;

  PPM_SOCKCALL_RETURN (::select (width,
                                 (PPM_FD_SET_TYPE *) rfds,
                                 (PPM_FD_SET_TYPE *) wfds,
                                 (PPM_FD_SET_TYPE *) efds,
                                 ___PPM_TIMEOUT),
                       int, -1);
#undef ___PPM_TIMEOUT
}

PPM_INLINE int
PPM_OS::shutdown (PPM_HANDLE handle, int how)
{
  PPM_OS_TRACE ("PPM_OS::shutdown");
  PPM_SOCKCALL_RETURN (::shutdown ((PPM_SOCKET) handle, how), int, -1);
}

PPM_INLINE int
PPM_OS::atoi (const char *s)
{
  PPM_OSCALL_RETURN (::atoi (s), int, -1);
}
PPM_INLINE DWORDLONG
PPM_OS::atoi64_16 (const char *s)
{
	DWORDLONG tm_returnvalue = 0;
	int istrlen = PPM_OS::strlen(s);
	if(istrlen <= 8){
		::sscanf(s, "%llX", &tm_returnvalue);
	}else if(istrlen <= 16){
		char tm_hchar[10];
		char tm_lchar[10];
		PPM_OS::memcpy(tm_hchar,s,istrlen - 8);
		tm_hchar[istrlen - 8] = 0;
		PPM_OS::memcpy(tm_lchar,&s[istrlen - 8],8);
		tm_lchar[8] = 0;
		int tm_high = 0;
		::sscanf(tm_hchar, "%X", &tm_high);
		::sscanf(tm_lchar, "%llX", &tm_returnvalue);
		tm_returnvalue += tm_high * ((DWORDLONG)0xffffffff + 1);
	}
//	if(istrlen > 16){
//		return 0;
//	}else if(istrlen > 8){
//
//	}else{
//	}

	return tm_returnvalue;
}

PPM_INLINE unsigned long
PPM_OS::inet_addr (const char *name)
{
  PPM_OS_TRACE ("PPM_OS::inet_addr");
  return ::inet_addr (name);
}

PPM_INLINE std::string
PPM_OS::getlocalhostip ()
{
	PPM_OS_TRACE ("PPM_OS::inet_addr");
	char tm_str1[200];
	PPM_OS::hostname(tm_str1,sizeof(tm_str1));
	hostent * tm_hnt;
	tm_hnt = ::gethostbyname(tm_str1);
	if(NULL == tm_hnt){
		return "0";
	}
	sprintf(tm_str1 ,"%s",PPM_OS::inet_ntoa(*((in_addr*)tm_hnt->h_addr_list[0])));
	std::string outstr(tm_str1);
	return outstr;
}

PPM_INLINE int
PPM_OS::getlocalhost ()
{
  PPM_OS_TRACE ("PPM_OS::inet_addr");
  return ::inet_addr(PPM_OS::getlocalhostip().c_str());
}


PPM_INLINE int
PPM_OS::hostname (char name[], size_t maxnamelen)
{
  PPM_OS_TRACE ("PPM_OS::hostname");

//  BOOL PPM_result_;

  PPM_SOCKCALL_RETURN (gethostname (name, maxnamelen), int, SOCKET_ERROR);
                                        
}
PPM_INLINE char *
PPM_OS::inet_ntoa (const struct in_addr addr)
{
  PPM_OS_TRACE ("PPM_OS::inet_ntoa");
  PPM_OSCALL_RETURN (::inet_ntoa (addr),
                     char *,
                     0);
}
PPM_INLINE char *
PPM_OS::inet_ntoa (const int iaddr)
{
  PPM_OS_TRACE ("PPM_OS::inet_ntoa");
	//配置中心ip端口
	in_addr tm_inaddr;
	tm_inaddr.s_addr = iaddr;
    return PPM_OS::inet_ntoa(tm_inaddr);
}


PPM_INLINE char *
PPM_OS::inet_ntop (int family, const void *addrptr, char *strptr, size_t len)
{
	const u_char *p = (const u_char*) addrptr;
	if (family == AF_INET6)
	{
		char temp[46];
		snprintf(temp, sizeof(temp), "%02x%02x:%02x%02x:%02x%02x:%02x%02x:%02x%02x:%02x%02x:%02x%02x:%02x%02x", 
			p[0],p[1],p[2],p[3],p[4],p[5],p[6],p[7],p[8],p[9],p[10],p[11],p[12],p[13],p[14],p[15]);
		
		if (strlen(temp) >= len)
		{
			errno = ENOSPC;
			return (NULL);
		}

		strcpy(strptr, temp);
		return strptr;
	}

	errno = EAFNOSUPPORT;
	return (NULL);
}

PPM_INLINE int
PPM_OS::close (PPM_HANDLE handle)
{
  PPM_OS_TRACE ("PPM_OS::close");
//  BOOL PPM_result_;

  PPM_OSCALL_RETURN (::close (handle), int, -1);
	
}

PPM_INLINE int
PPM_OS::get_flags (PPM_HANDLE handle)
{
  // PPM_OS::fcntl is not supported, e.g., on VxWorks.  It
  // would be better to store ACE's notion of the flags
  // associated with the handle, but this works for now.
  return PPM_OS::fcntl (handle, F_GETFL, 0);
  return 0;
}
PPM_INLINE struct hostent *
PPM_OS::gethostbyname_r(const char *name,
                         struct hostent *result,
                         PPM_HOSTENT_DATA buffer,
                         int *h_errnop)
{
  PPM_SOCKCALL_RETURN (::gethostbyname (name),
                       struct hostent *,
                       0);              
                    
}
PPM_INLINE struct hostent *
PPM_OS::gethostbyaddr_r (const char *addr,
                         int length,
                         int type,
                         struct hostent *result,
                         PPM_HOSTENT_DATA buffer,
                         int *h_errnop)
{
  PPM_SOCKCALL_RETURN (::gethostbyaddr (addr, length, type),
                       struct hostent *, 0);

                     
}
PPM_INLINE struct servent *
PPM_OS::getservbyname_r (const char *svc,
                         const char *proto,
                         struct servent *result,
                         PPM_SERVENT_DATA buf)
{
  PPM_SOCKCALL_RETURN (::getservbyname (svc, proto),
                       struct servent *, 0);
                   
}


PPM_INLINE int
PPM_OS::event_destroy (PPM_event_t *event)
{
//  BOOL PPM_result_;
  int r1 = PPM_OS::mutex_destroy (&event->lock_);
  int r2 = PPM_OS::cond_destroy (&event->condition_);
  return r1 != 0 || r2 != 0 ? -1 : 0;

}

PPM_INLINE PPM_Time_Value
PPM_OS::gettimeofday (void)
{
  int result = 0;
  timeval tv;

	PPM_OSCALL (::gettimeofday (&tv, 0), int, -1, result);
  
  if (result == -1)
    return -1;
  else
    return PPM_Time_Value (tv);

}

PPM_INLINE int
PPM_OS::set_errno_to_last_error (void)
{
	return errno;
}

PPM_INLINE int
PPM_OS::event_init (PPM_event_t *event,
                    int manual_reset,
                    int initial_state,
                    int type,
                    const char *name,
                    void *arg,
                    LPSECURITY_ATTRIBUTES sa)
{
  PPM_UNUSED_ARG (sa);
  event->manual_reset_ = manual_reset;
  event->is_signaled_ = initial_state;
  event->waiting_threads_ = 0;

  int result = PPM_OS::cond_init (&event->condition_,
                                  PPM_static_cast (short, type),
                                  name,
                                  arg);
  if (result == 0)
    result = PPM_OS::mutex_init (&event->lock_,
                                 type,
                                 name,
                                 (PPM_mutexattr_t *) arg);
  return result;

}

PPM_INLINE int
PPM_OS::event_wait (PPM_event_t *event)
{
  int result = 0;
  int error = 0;

  // grab the lock first
  if (PPM_OS::mutex_lock (&event->lock_) == 0)
    {
      if (event->is_signaled_ == 1)
        // Event is currently signaled.
        {
          if (event->manual_reset_ == 0)
            // AUTO: reset state
            event->is_signaled_ = 0;
        }
      else
        // event is currently not signaled
        {
          event->waiting_threads_++;

          if (PPM_OS::cond_wait (&event->condition_,
                                 &event->lock_) != 0)
            {
              result = -1;
              error = errno;
              // Something went wrong...
            }

          event->waiting_threads_--;
        }

      // Now we can let go of the lock.
      PPM_OS::mutex_unlock (&event->lock_);

      if (result == -1)
        // Reset errno in case mutex_unlock() also fails...
        errno = error;
    }
  else
    result = -1;
  return result;

}

PPM_INLINE int
PPM_OS::event_timedwait (PPM_event_t *event,
                         PPM_Time_Value *timeout,
                         int use_absolute_time)
{
  int result = 0;
  int error = 0;

  // grab the lock first
  if (PPM_OS::mutex_lock (&event->lock_) == 0)
    {
      if (event->is_signaled_ == 1)
        // event is currently signaled
        {
          if (event->manual_reset_ == 0)
            // AUTO: reset state
            event->is_signaled_ = 0;
        }
      else
        // event is currently not signaled
        {
          event->waiting_threads_++;

          // cond_timewait() expects absolute time, check
          // <use_absolute_time> flag.
          if (use_absolute_time == 0 && timeout != 0)
            *timeout += PPM_OS::gettimeofday ();

          if (PPM_OS::cond_timedwait (&event->condition_,
                                      &event->lock_,
                                      timeout) != 0)
            {
              result = -1;
              error = errno;
            }

          event->waiting_threads_--;
        }

      // Now we can let go of the lock.
      PPM_OS::mutex_unlock (&event->lock_);

      if (result == -1)
        // Reset errno in case mutex_unlock() also fails...
        errno = error;
    }
  else
    result = -1;
  return result;

}

PPM_INLINE int
PPM_OS::event_signal (PPM_event_t *event)
{
//  BOOL PPM_result_;
  int result = 0;
  int error = 0;

  // grab the lock first
  if (PPM_OS::mutex_lock (&event->lock_) == 0)
    {
      // Manual-reset event.
      if (event->manual_reset_ == 1)
        {
          // signal event
          event->is_signaled_ = 1;
          // wakeup all
          if (PPM_OS::cond_broadcast (&event->condition_) != 0)
            {
              result = -1;
              error = errno;
            }
        }
      // Auto-reset event
      else
        {
          if (event->waiting_threads_ == 0)
            // No waiters: signal event.
            event->is_signaled_ = 1;

          // Waiters: wakeup one waiter.
          else if (PPM_OS::cond_signal (&event->condition_) != 0)
            {
              result = -1;
              error = errno;
            }
        }

      // Now we can let go of the lock.
      PPM_OS::mutex_unlock (&event->lock_);

      if (result == -1)
        // Reset errno in case mutex_unlock() also fails...
        errno = error;
    }
  else
    result = -1;
  return result;
	
}

PPM_INLINE int
PPM_OS::event_pulse (PPM_event_t *event)
{
//  BOOL PPM_result_;
  int result = 0;
  int error = 0;

  // grab the lock first
  if (PPM_OS::mutex_lock (&event->lock_) == 0)
    {
      // Manual-reset event.
      if (event->manual_reset_ == 1)
        {
          // Wakeup all waiters.
          if (PPM_OS::cond_broadcast (&event->condition_) != 0)
            {
              result = -1;
              error = errno;
            }
        }
      // Auto-reset event: wakeup one waiter.
      else if (PPM_OS::cond_signal (&event->condition_) != 0)
        {
          result = -1;
          error = errno;
        }

      // Reset event.
      event->is_signaled_ = 0;

      // Now we can let go of the lock.
      PPM_OS::mutex_unlock (&event->lock_);

      if (result == -1)
        // Reset errno in case mutex_unlock() also fails...
        errno = error;
    }
  else
    result = -1;
  return result;

}

PPM_INLINE int
PPM_OS::event_reset (PPM_event_t *event)
{
//  BOOL PPM_result_;
  int result = 0;

  // Grab the lock first.
  if (PPM_OS::mutex_lock (&event->lock_) == 0)
    {
      // Reset event.
      event->is_signaled_ = 0;

      // Now we can let go of the lock.
      PPM_OS::mutex_unlock (&event->lock_);
    }
  else
    result = -1;
  return result;

}


PPM_INLINE int
PPM_OS::sigaddset (sigset_t *s, int signum)
{
  PPM_OSCALL_RETURN (::sigaddset (s, signum), int, -1);

}

PPM_INLINE int
PPM_OS::sigdelset (sigset_t *s, int signum)
{
  PPM_OSCALL_RETURN (::sigdelset (s, signum), int, -1);

}

PPM_INLINE int
PPM_OS::sigemptyset (sigset_t *s)
{
  PPM_OSCALL_RETURN (::sigemptyset (s), int, -1);

}

PPM_INLINE int
PPM_OS::sigfillset (sigset_t *s)
{
  PPM_OSCALL_RETURN (::sigfillset (s), int, -1);

}

PPM_INLINE int
PPM_OS::sigismember (sigset_t *s, int signum)
{
  PPM_OSCALL_RETURN (::sigismember (s, signum), int, -1);

}

PPM_INLINE int
PPM_OS::sigsuspend (const sigset_t *sigset)
{
  sigset_t s;

  if (sigset == 0)
    {
      sigset = &s;
      PPM_OS::sigemptyset (&s);
    }
  PPM_OSCALL_RETURN (::sigsuspend (sigset), int, -1);
}

PPM_INLINE int
PPM_OS::sigprocmask (int how, const sigset_t *nsp, sigset_t *osp)
{
#ifdef WIN32
  PPM_UNUSED_ARG (how);
  PPM_UNUSED_ARG (nsp);
  PPM_UNUSED_ARG (osp);
  return (-1);
#else
  PPM_OSCALL_RETURN (::sigprocmask (how, nsp, osp), int, -1);
#endif
}

PPM_INLINE int
PPM_OS::pthread_sigmask (int how, const sigset_t *nsp, sigset_t *osp)
{
#ifdef WIN32
	return -1;
#elif defined AIX
 PPM_OSCALL_RETURN (PPM_ADAPT_RETVAL (::pthread_sigmask (how, nsp, osp),
                                       PPM_result_),
                     int,
                     -1);
#else
	return -1;
#endif
}


PPM_INLINE int
PPM_OS::sigaction (int signum,
                   const struct sigaction *nsa,
                   struct sigaction *osa)
{
  PPM_OS_TRACE ("PPM_OS::sigaction");
  if (signum == 0)
    return 0;
#ifdef WIN32
  struct sigaction sa;

  if (osa == 0)
    osa = &sa;

  if (nsa == 0)
    {
      osa->sa_handler = ::signal (signum, SIG_IGN);
      ::signal (signum, osa->sa_handler);
    }
  else
    osa->sa_handler = ::signal (signum, nsa->sa_handler);
  return osa->sa_handler == SIG_ERR ? -1 : 0;
#else
  PPM_OSCALL_RETURN (::sigaction (signum, nsa, osa), int, -1);
#endif
}

//thread


PPM_INLINE int
PPM_OS::thr_setprio (PPM_hthread_t thr_id, int prio)
{
  PPM_OS_TRACE ("PPM_OS::thr_setprio");
#ifdef WIN32
  PPM_WIN32CALL_RETURN (PPM_ADAPT_RETVAL (::SetThreadPriority (thr_id, prio),
                                          PPM_result_),
                        int, -1);
#else
  struct sched_param param;
  int policy = 0;
  int result;

  PPM_OSCALL (PPM_ADAPT_RETVAL (::pthread_getschedparam (thr_id, &policy, &param),
                                result), // not sure if use of result here is cool, cjc
              int, -1, result);
  if (result == -1)
    return result; // error in pthread_getschedparam
  param.sched_priority = prio;
  PPM_OSCALL_RETURN (PPM_ADAPT_RETVAL (::pthread_setschedparam (thr_id, policy, &param),
                                       result),
                     int, -1);
#endif
}

PPM_INLINE int
PPM_OS::thr_continue (PPM_hthread_t target_thread)
{
#ifdef WIN32
  PPM_OS_TRACE ("PPM_OS::thr_continue");
  DWORD result = ::ResumeThread (target_thread);
  if (result == PPM_SYSCALL_FAILED)
    return (-1);
  else
    return 0;
#else
  return -1;
#endif
}


PPM_INLINE int
PPM_OS::thr_suspend (PPM_hthread_t target_thread)
{
#ifdef WIN32
  PPM_OS_TRACE ("PPM_OS::thr_suspend");
  if (::SuspendThread (target_thread) != PPM_SYSCALL_FAILED)
    return 0;
  else
    return (-1);
#else
  return -1;
#endif
}

PPM_INLINE int
PPM_OS::thr_getprio (PPM_hthread_t thr_id, int &prio)
{
#ifdef WIN32
  PPM_OS_TRACE ("PPM_OS::thr_getprio");
  prio = ::GetThreadPriority (thr_id);
  if (prio == THREAD_PRIORITY_ERROR_RETURN)
    return (-1);
  else
    return 0;
#else
 
  struct sched_param param;
  int result;
  int policy = 0;

  PPM_OSCALL (PPM_ADAPT_RETVAL (::pthread_getschedparam (thr_id, &policy, &param),
                                result), int,
              -1, result);
  prio = param.sched_priority;
  return result;
#endif
}

PPM_INLINE void
PPM_OS::thr_self (PPM_hthread_t &self)
{
  PPM_OS_TRACE ("PPM_OS::thr_self");
#ifdef WIN32
  self = ::GetCurrentThread ();
  int i = ::GetLastError();
#else
  self = pthread_self ();
#endif
}

PPM_INLINE PPM_thread_t
PPM_OS::thr_self (void)
{
#ifdef WIN32
  return ::GetCurrentThreadId ();
#else
  PPM_OSCALL_RETURN (pthread_self (), int, -1);
#endif
}
// 
// PPM_INLINE int
// PPM_OS::thr_create (PPM_THR_FUNC func,
//                     void *args,
//                     long flags,
//                     PPM_Thread_ID *thr_id,
//                     long priority,
//                     void *stack,
//                     size_t stacksize);
// {
//   PPM_OS_TRACE ("PPM_OS::thr_create");
//   PPM_thread_t thread_id;
//   PPM_hthread_t thread_handle;
// 
//   int result = PPM_OS::thr_create (func, args, flags,
//                                    &thread_id, &thread_handle,
//                                    priority, stack, stacksize);
//   if (result == -1)
//     return -1;
//   else if (thr_id != 0)
//     {
//       thr_id->id (thread_id);
//       thr_id->handle (thread_handle);
//       return result;
//     }
// }
PPM_INLINE u_long
PPM_OS::log2 (u_long num)
{
  u_long log = 0;

  for (;
       num > 0;
       log++)
    num >>= 1;

  return log;
}

PPM_INLINE pid_t
PPM_OS::getpid (void)
{
#ifdef WIN32
  return ::GetCurrentProcessId ();
#else
  PPM_OSCALL_RETURN (::getpid (), int, -1);
#endif
}
PPM_INLINE char *
PPM_OS::ctime_r (const time_t *t, char *buf, int buflen)
{
  if (buflen < ctime_buf_size)
    {
      errno = ERANGE;
      return 0;
    }

  char *result;
  PPM_OSCALL (::ctime (t), char *, 0, result);
  if (result != 0)
    PPM_OS::strsncpy (buf, result, buflen);
  return buf;

}


PPM_INLINE int
PPM_OS::fflush (FILE *fp)
{
  PPM_OS_TRACE ("PPM_OS::fflush");

  PPM_OSCALL_RETURN (::fflush (fp), int, -1);
}

PPM_INLINE int
PPM_OS::last_error (void)
{
  // PPM_OS_TRACE ("PPM_OS::last_error");
#ifdef WIN32
  int lerror = ::GetLastError ();
  int lerrno = errno;
  return lerrno == 0 ? lerror : lerrno;
#else
  return errno;
#endif
}

PPM_INLINE DWORDLONG
PPM_OS::atoi64(const char* strin)
{
	if(PPM_OS::strlen(strin) > 18){
		char tm_str[50];
		PPM_OS::snprintf(tm_str,49,"%s",strin);
		tm_str[18] = 0;
#ifdef WIN32
	return ::_atoi64(tm_str);
#else
	return ::atoll(tm_str);
#endif
	}
#ifdef WIN32
	return ::_atoi64(strin);
#else
	return ::atoll(strin);
#endif
}

PPM_INLINE std::string
PPM_OS::i64toa(DWORDLONG keyin,int type)
{
	char tm_str[50];

	PPM_OS::snprintf(tm_str,50,"%lld",keyin);
	std::string strout(tm_str);
	return strout;
}

//add for unix

PPM_INLINE int
PPM_OS::fcntl (PPM_HANDLE handle, int cmd, long arg)
{
  PPM_OS_TRACE ("PPM_OS::fcntl");

  PPM_OSCALL_RETURN (::fcntl (handle, cmd, arg), int, -1);

}

PPM_INLINE int
PPM_OS::thr_getconcurrency (void)
{
  PPM_OS_TRACE ("PPM_OS::thr_getconcurrency");
  PPM_NOTSUP_RETURN (0);
}


PPM_INLINE int
PPM_OS::thr_setconcurrency (int hint)
{
  PPM_OS_TRACE ("PPM_OS::thr_setconcurrency");
  PPM_UNUSED_ARG (hint);
  PPM_NOTSUP_RETURN (0);
}


PPM_INLINE int
PPM_OS::mutex_destroy (PPM_mutex_t *m)
{
  PPM_OS_TRACE ("PPM_OS::mutex_destroy");
#ifdef WIN32
//  switch (m->type_)
//    {
//    case USYNC_PROCESS:
//      PPM_WIN32CALL_RETURN (PPM_ADAPT_RETVAL (::CloseHandle (m->proc_mutex_),
//                                              PPM_result_),
//                            int, -1);
//    case USYNC_THREAD:
//      return PPM_OS::thread_mutex_destroy (&m->thr_mutex_);
//   default:
//      errno = EINVAL;
      return -1;
//    }
#else
  PPM_OSCALL_RETURN (PPM_ADAPT_RETVAL (::pthread_mutex_destroy (m),
                                       PPM_result_), int, -1);
#endif

}

// NOTE: The PPM_OS::cond_* functions for Unix platforms are defined
// here because the PPM_OS::sema_* functions below need them.
// However, PPM_WIN32 and VXWORKS define the PPM_OS::cond_* functions
// using the PPM_OS::sema_* functions.  So, they are defined in OS.cpp.

PPM_INLINE int
PPM_OS::cond_destroy (PPM_cond_t *cv)
{
  PPM_OS_TRACE ("PPM_OS::cond_destroy");
#ifdef WIN32
	return -1;
#else
  PPM_OSCALL_RETURN (PPM_ADAPT_RETVAL (::pthread_cond_destroy (cv), PPM_result_), int, -1);
#endif

}



PPM_INLINE int
PPM_OS::cond_init (PPM_cond_t *cv,
                   PPM_condattr_t &attributes,
                   const char *name,
                   void *arg)
{
  // PPM_OS_TRACE ("PPM_OS::cond_init");
#ifdef WIN32
	return -1;
#else
   int result = -1;

  if (
      ::pthread_cond_init (cv, &attributes) == 0
      )
     result = 0;
  else
     result = -1;       // PPM_ADAPT_RETVAL used it for intermediate status

  return result;
#endif
}

PPM_INLINE int
PPM_OS::cond_init (PPM_cond_t *cv, short type, const char *name, void *arg)
{
  PPM_condattr_t attributes;
  if (PPM_OS::condattr_init (attributes, type) == 0
      && PPM_OS::cond_init (cv, attributes, name, arg) == 0)
    {
      (void) PPM_OS::condattr_destroy (attributes);
      return 0;
    }
  return -1;
}


PPM_INLINE int
PPM_OS::condattr_init (PPM_condattr_t &attributes,
                       int type)
{
#ifdef WIN32
	return -1;
#else
   int result = -1;

  if (
      ::pthread_condattr_init (&attributes) == 0
      && ::pthread_condattr_setpshared (&attributes, type) == 0
      )
     result = 0;
  else
     result = -1;       // PPM_ADAPT_RETVAL used it for intermediate status

  return result;
  
#endif
}

PPM_INLINE int
PPM_OS::condattr_destroy (PPM_condattr_t &attributes)
{
#ifdef WIN32
	return 0;
#else
  ::pthread_condattr_destroy (&attributes);
  	return 0;
#endif
}


PPM_INLINE int
PPM_OS::mutex_init (PPM_mutex_t *m,
                    int type,
                    const char *name,
                    PPM_mutexattr_t *attributes,
                    LPSECURITY_ATTRIBUTES sa)
{
  // PPM_OS_TRACE ("PPM_OS::mutex_init");
#ifdef WIN32
	return 0;
#else
  PPM_UNUSED_ARG (name);
  PPM_UNUSED_ARG (attributes);
  PPM_UNUSED_ARG (sa);

  pthread_mutexattr_t l_attributes;
  if (attributes == 0)
    attributes = &l_attributes;
  int result = 0;
  int attr_init = 0;  // have we initialized the local attributes.

  // Only do these initializations if the <attributes> parameter
  // wasn't originally set.
  if (attributes == &l_attributes)
    {
      if (::pthread_mutexattr_init (attributes) == 0)
        {
          ::pthread_mutexattr_settype(attributes,PTHREAD_MUTEX_RECURSIVE);
          
          result = 0;
          attr_init = 1; // we have initialized these attributes
        }
      else
        result = -1;        // PPM_ADAPT_RETVAL used it for intermediate status
    }

  if (result == 0)
    {

        if (::pthread_mutex_init (m, attributes) == 0)
        result = 0;
      else
        result = -1;        // PPM_ADAPT_RETVAL used it for intermediate status
    }

  // Only do the deletions if the <attributes> parameter wasn't
  // originally set.
  if (attributes == &l_attributes && attr_init)
    ::pthread_mutexattr_destroy (&l_attributes);

  return result;
                     
#endif
}


PPM_INLINE int
PPM_OS::mutex_lock (PPM_mutex_t *m)
{
  // PPM_OS_TRACE ("PPM_OS::mutex_lock");
#ifdef WIN32
	return -1;
#else
  PPM_OSCALL_RETURN (PPM_ADAPT_RETVAL (pthread_mutex_lock (m), PPM_result_),
                     int, -1);
#endif                   
}

PPM_INLINE int
PPM_OS::mutex_unlock (PPM_mutex_t *m)
{
#ifdef WIN32
	return -1;
#else
  PPM_OSCALL_RETURN (PPM_ADAPT_RETVAL (pthread_mutex_unlock (m), PPM_result_),
                     int, -1);
#endif
}


PPM_INLINE int
PPM_OS::cond_signal (PPM_cond_t *cv)
{
#ifdef WIN32
	return -1;
#else
  PPM_OSCALL_RETURN (PPM_ADAPT_RETVAL (::pthread_cond_signal (cv),PPM_result_),
                     int, -1);
#endif
}

PPM_INLINE int
PPM_OS::cond_timedwait (PPM_cond_t *cv,
                        PPM_mutex_t *external_mutex,
                        PPM_Time_Value *timeout)
{
#ifdef WIN32
	return -1;
#else
  int result;
  timespec_t ts;

  if (timeout != 0)
    ts = *timeout; // Calls PPM_Time_Value::operator timespec_t().

      PPM_OSCALL (::pthread_cond_timedwait (cv, external_mutex,
                                            (PPM_TIMESPEC_PTR) &ts),
                  int, -1, result);
		  return result;
#endif
}


PPM_INLINE int
PPM_OS::cond_wait (PPM_cond_t *cv,
                   PPM_mutex_t *external_mutex)
{
#ifdef WIN32
	return -1;
#else
  PPM_OSCALL_RETURN (PPM_ADAPT_RETVAL (::pthread_cond_wait (cv, external_mutex), PPM_result_),
                     int, -1);
#endif
}


PPM_INLINE int
PPM_OS::cond_broadcast (PPM_cond_t *cv)
{
#ifdef WIN32
	return -1;
#else
  PPM_OSCALL_RETURN (PPM_ADAPT_RETVAL (::pthread_cond_broadcast (cv),
                                       PPM_result_),
                     int, -1);
#endif /* PPM_HAS_THREADS */
}


PPM_INLINE int
PPM_OS::getrlimit (int resource, struct rlimit *rl)
{
  PPM_OS_TRACE ("PPM_OS::getrlimit");

#if defined (WIN32)
  PPM_UNUSED_ARG (resource);
  PPM_UNUSED_ARG (rl);

  PPM_NOTSUP_RETURN (-1);
#else
# if defined (PPM_HAS_RLIMIT_RESOURCE_ENUM)
  PPM_OSCALL_RETURN (::getrlimit ((PPM_HAS_RLIMIT_RESOURCE_ENUM) resource, rl), int, -1);
# else
  PPM_OSCALL_RETURN (::getrlimit (resource, rl), int, -1);
# endif /* PPM_HAS_RLIMIT_RESOURCE_ENUM */
#endif /* PPM_LACKS_RLIMIT */
}

PPM_INLINE int
PPM_OS::setrlimit (int resource, PPM_SETRLIMIT_TYPE *rl)
{
  PPM_OS_TRACE ("PPM_OS::setrlimit");

#if defined (WIN32)
  PPM_UNUSED_ARG (resource);
  PPM_UNUSED_ARG (rl);

  PPM_NOTSUP_RETURN (-1);
#else
# if defined (PPM_HAS_RLIMIT_RESOURCE_ENUM)
 PPM_OSCALL_RETURN (::setrlimit ((PPM_HAS_RLIMIT_RESOURCE_ENUM) resource, rl), int, -1);
# else
  PPM_OSCALL_RETURN (::setrlimit (resource, rl), int, -1);
# endif /* PPM_HAS_RLIMIT_RESOURCE_ENUM */
#endif /* PPM_LACKS_RLIMIT */
}


PPM_INLINE int
PPM_OS::thr_sigsetmask (int how,
                        const sigset_t *nsm,
                        sigset_t *osm)
{
#ifdef WIN32
	return -1;
#elif defined AIX
  PPM_OSCALL_RETURN (sigthreadmask (how, nsm, osm), int, -1);
#else
  PPM_OSCALL_RETURN (::sigprocmask (how, nsm, osm), int, -1);
#endif
}


PPM_INLINE int
PPM_OS::fstat (PPM_HANDLE handle, PPM_stat *stp)
{
#ifdef WIN32
	return -1;
//    PPM_OSCALL_RETURN (::_fstat (handle, stp), int, -1);
#else
    PPM_OSCALL_RETURN (::fstat (handle, stp), int, -1);
#endif /* PPM_PSOS_LACKS_PHILE */
}


PPM_INLINE int
PPM_OS::set_errno_to_wsa_last_error (void)
{
# if defined (WIN32)
  return errno = ::WSAGetLastError ();
#else
  return errno;
# endif /* defined(WIN32) */
}

