// PPM_Tcp_Client.cpp: implementation of the PPM_Tcp_Client class.
//
//////////////////////////////////////////////////////////////////////

#include "./PPM_Tcp_Client.h"
#include "./PPM_Thread_Manager.h"
#include "./PPM_Tcp_Server.h"
#include "./PPM_Reactor.h"
#include "./PPM_Client.h"
#include "./PPM_CONN_Actor.h"

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

PPM_SOCK_Connector PPM_Tcp_Client::m_sConner;
/**
客户端调用的初始化函数
输入:
    server_ip:需要连接的服务端ip地址
	server_port:需要连接的服务端端口号
	pSockBuffer:数据缓冲对象指针
	preactor:反应器对象指针,全局的
	bReconnect:是否重连标志,0不重连,1重连,默认不重连
	pActor:业务处理类对象的指针,默认为空,会自动创建一个
*/
 PPM_Tcp_Client::PPM_Tcp_Client(const char* const server_ip, 
							    const u_short server_port,
							    PPM_SOCK_Buffer* pSockBuffer,
							    PPM_Reactor* preactor,
							    BYTE bReconnect,
							    CDataActor* pActor,
								const long lDecDataThreadPriority,//侦听数据线程,如果只是发送不接收数据,这个常数无效
								const long lRecDataThreadPriority,//可能有收数据线呈，如果收结点类型不为NullMode的话
								const long lSendDataThreadPriority,//可能有发数据线呈，如果发节点类型不为NullMode的话							   
								const BYTE bSendHeadFlag, //发送是否加AA55标示
								const BYTE bRecvHeadFlag, //接收是否加AA55标示
								BYTE isNeedDelReactor, //是否需要删除reactor
								const char* local_ip
							   )
:m_skStream(),m_Addr(server_port, server_ip), bBindLocal(FALSE),
m_bRunFlag_Send(true), m_bRunFlag_Recv(true),m_precvBuf(NULL), m_psendBuf(NULL), m_pActor(pActor)
{
	m_lDecDataThreadPriority_ = lDecDataThreadPriority;
	m_lRecDataThreadPriority_ = lRecDataThreadPriority;
	m_lSendDataThreadPriority_ = lSendDataThreadPriority;
	
	if(local_ip != NULL)
	{
		m_LocalAddr = PPM_INET_Addr(0, local_ip);
		bBindLocal = TRUE;
	}

    m_bReactorDelFlag = 0;
	m_IsHandleClosed = 1;
	m_bRegisted = 0;
	m_bSendHeadFlag_ = bSendHeadFlag;
	m_bRecvHeadFlag_ = bRecvHeadFlag;

	PPM_OS::memset(&m_stru_clientctl,0,sizeof(m_stru_clientctl));
	m_stru_clientctl.bReconnect = bReconnect;

	m_RecvThr_Handle = 0;
	m_SendThr_Handle = 0;
	m_bIsRecvThr_Stop = 1;
	m_bIsSendThr_Stop = 1;
	m_ptcp_server_actor_ = NULL;
	m_type = 0;
//	m_bRegisted = 0;
	m_pstru_client = NULL;

	if(NULL == pActor){
		m_pActor = new CDataActorNull;
		m_stru_clientctl.bActorNeedDel = 1;
	}
	m_pActor->SetOverFlag(FALSE);

	m_pSockBuffer = pSockBuffer;

	if(NULL != preactor ){
		this->reactor(preactor);
	    m_bReactorDelFlag = isNeedDelReactor;
	}else{
		this->reactor(PPM_Reactor::instance());
		m_bReactorDelFlag = 0;
	}
    BeginInstance();
	//如果不需要重连接的话
    if(false == Connect()){
        if(1 != m_stru_clientctl.bReconnect){
            m_stru_clientctl.bIsInitSuc = 0;
            return;
        }
    }
	m_stru_clientctl.bIsInitSuc = 1;

	Initialize();
}
/**
服务端调用的初始化函数
输入:
    sockstream:已经连接好的PPM_SOCK_Stream对象
	preactor:反应器对象指针,全局的
	pSockBuffer:数据缓冲对象指针
	pActor:业务处理类对象的指针,不能为空
*/
PPM_Tcp_Client::PPM_Tcp_Client( PPM_SOCK_Stream sockstream,
								PPM_Reactor* preactor,
								PPM_SOCK_Buffer* pSockBuffer,								
								CDataActor* pActor,
								const long lDecDataThreadPriority,//侦听数据线程,如果只是发送不接收数据,这个常数无效
								const long lRecDataThreadPriority,//可能有收数据线呈，如果收结点类型不为NullMode的话
								const long lSendDataThreadPriority,//可能有发数据线呈，如果发节点类型不为NullMode的话		
								const BYTE bSendHeadFlag, //发送是否加AA55标示
								const BYTE bRecvHeadFlag  //接收是否加AA55标示
								)
:m_bRunFlag_Send(true), m_bRunFlag_Recv(true),m_precvBuf(NULL), m_psendBuf(NULL), m_pActor(pActor)
{
	m_lDecDataThreadPriority_ = lDecDataThreadPriority;
	m_lRecDataThreadPriority_ = lRecDataThreadPriority;
	m_lSendDataThreadPriority_ = lSendDataThreadPriority;

	m_bRegisted = 1;
    m_bReactorDelFlag = 0;
	m_IsHandleClosed = 1;

	m_RecvThr_Handle = 0;
	m_SendThr_Handle = 0;
	m_bIsRecvThr_Stop = 1;
	m_bIsSendThr_Stop = 1;
	m_pSockBuffer = pSockBuffer;
	m_ptcp_server_actor_ = NULL;
	m_stru_serverctl.i_clientid = -1;
	m_stru_serverctl.bClosetype = 0;
	m_stru_serverctl.bIsDestory = 0;

	//add by cai 
    m_Addr = PPM_INET_Addr(0, pSockBuffer->m_pipaddress);

	m_bSendHeadFlag_ = bSendHeadFlag;
	m_bRecvHeadFlag_ = bRecvHeadFlag;
	if(NULL != m_pActor){
		m_pActor->SetOverFlag(FALSE);
	}
	
	if(NULL != preactor ){
		this->reactor(preactor);
	    m_bReactorDelFlag = 1;
	}else{
		this->reactor(PPM_Reactor::instance());
		m_bReactorDelFlag = 0;
	}


	m_skStream.set_handle(sockstream.get_handle());
	m_type = 1;

	Initialize();
}

PPM_Tcp_Client::~PPM_Tcp_Client(void)
{
// 	if(1 == m_bRegisted){
// 		this->reactor()->remove_handler(this,READ_MASK);
// 	}
	UnRegiste();
	StopThread(3);

//	DealAbnormSock();
	m_skStream.close();

	int i = 0;
	//下面的处理相当于是协调侦听线程和业务线程的关系，等待侦听线程相关的结束
	while( 0 == m_IsHandleClosed){
		PPM_OS::sleep(20);
		PPM_SOCKLOG((LM_INFO,"wait for handleclose %d!\n",i));
		if((i++) > 400){
			break;
		}
	}
	//如果需要关闭REACTOR且不是重用的情况，则需要关闭
	if( (1 == m_bReactorDelFlag) && (2 != GetDestoryFlag() )){
		this->reactor()->end_event_loop();
		//停止就可以了,删除反映器的事情留给后面做
// 		//等待5秒钟关闭
 		int i = 0;
 		while(FALSE == this->reactor()->isEventLoop_Over()){
 			if(i++ > 500){
 				break;
 			}
 			PPM_OS::sleep(10);
 		}
// 		delete this->reactor();
	}
	if (m_precvBuf != NULL)
	{
		delete []m_precvBuf;
		m_precvBuf = NULL;
	}

	//区分客户端和服务端两种情况
    if(( 1 == m_stru_clientctl.bActorNeedDel) && ( 0 == m_type) ){
		//客户端
            if(NULL != m_pActor){
		delete m_pActor;
            }
	}
	else{
		//服务端，如果需要删除资源
		if ( (NULL != m_pActor) && (2 != GetDestoryFlag()) && (1 == m_type) )
		{
			m_ptcp_server_actor_->ClientDelete((PPM_Server_Deal_Base *)m_pActor);
			delete m_pActor;
		}
	}

	m_psendBuf = NULL;

 	m_pActor = NULL;
}

PPM_THR_FUNC_VOID_RETURN SendThread(void * argus)
{
	PPM_Tcp_Client* pClt = (PPM_Tcp_Client*)argus;
	
	return pClt->SendMain();
};

///接受线程函数入口
PPM_THR_FUNC_VOID_RETURN RecvThread(void * argus)
{
	PPM_Tcp_Client* pClt = (PPM_Tcp_Client*)argus;
	
	return pClt->RecvMain();
};
/**
初始化函数,在这里启动相应的线程
*/
void PPM_Tcp_Client::Initialize(void)
{
	m_nRecvCount = 0;
	PPM_Thread_Manager* const pThread = PPM_Thread_Manager::instance();
    if(m_pSockBuffer!=NULL && m_pSockBuffer->m_SendMode != CNodeMng::NullMode){
		//启动发送数据线程,发送缓冲区中的数据，针对底层socket而言
 		pThread->spawn((PPM_THR_VOID_FUNC)SendThread, &m_SendThr_Handle, this,
			THR_NEW_LWP | THR_JOINABLE | THR_DETACHED,  0, m_lSendDataThreadPriority_);
	}else{
		BeginInstance();
	}

	if(m_pSockBuffer!=NULL && m_pSockBuffer->m_RecvMode != CNodeMng::NullMode){
		//启动接收数据线程，针对业务层而言，接收缓冲区中的数据，具体socket
		//收数据是通过反应器实现的
 		pThread->spawn((PPM_THR_VOID_FUNC)RecvThread, &m_RecvThr_Handle, this,
			THR_NEW_LWP | THR_JOINABLE | THR_DETACHED,  0, m_lRecDataThreadPriority_);
	}
}

/**
例程运行初始化函数,初始化收小缓冲和测试包
*/
bool PPM_Tcp_Client::BeginInstance(void)
{
    if (m_precvBuf == NULL)
    {
        m_precvBuf = new BYTE[NODE_BUF_SIZE];
        memset(m_Test, 0, sizeof(m_Test));
        m_Test[0] = TESTPACKHEAD & 0XFF;
        m_Test[1] = (TESTPACKHEAD & 0XFF00) >> 8; 
    }
    
    return 1;
}
/**
用于客户端的连接函数,如果需要注册,就在这里注册
*/
bool PPM_Tcp_Client::Connect(void)
{
	//如果已经注册，先关闭注册的对象
	//可能这里不需要,因为发送错误的时候已经removehandle了
	UnRegiste();
// 	if(1 == m_bRegisted){
// 		this->reactor()->remove_handler(this,READ_MASK);
// 		m_bRegisted = 0;
// 	}
// 	m_skStream.close();
//	PPM_Time_Value tm_timeout(4,0);
	PPM_SOCKLOG((LM_INFO,"connecting to %s,port:%d ......\n",m_Addr.get_host_addr(),m_Addr.get_port_number()));
	
	
	if (PPM_Tcp_Client::m_sConner.connect(m_skStream, m_Addr, 0, m_LocalAddr) == -1)
	{
		// 连接失败
		m_skStream.close();
//		PPM_OS::sleep(2000);
		
//		PPM_SOCKLOG((LM_ERROR,"connecting to %s,port:%d failed%m!\n",m_Addr.get_host_addr(),m_Addr.get_port_number()));
		m_IsHandleClosed = 1;
		return false;
	}
	PPM_SOCKLOG((LM_INFO,"connecting to %s,port:%d succeed!\n",m_Addr.get_host_addr(),m_Addr.get_port_number()));

	if( (CNodeMng::NullMode != m_pSockBuffer->m_RecvMode) || 
        (1 == m_stru_clientctl.bReconnect) ) 
    {
		
// 		PPM_hthread_t loopthr_handle;
// 		//如果反应器没有启动,启动反应器
// 		if(FALSE == this->reactor()->reactor_event_loop_done()){
// 			//新起一个线程
// 		    PPM_Thread_Manager* const thrmng = PPM_Thread_Manager::instance();
// 			//服务的侦听线程
// 			thrmng->spawn((PPM_THR_FUNC)ReactorThread, &loopthr_handle, this);
// 
// 		}else{
// 			this->reactor()->register_handler(this,READ_MASK);
// 		}
		if(1 == m_stru_clientctl.bIsInitSuc){
			m_bRegisted = 1;
			Registe();
			//表明已经注册上去，handleclose还未来
			m_IsHandleClosed = 0;
		}else{
			m_IsHandleClosed = 1;
		}
	}else{
		//表示不会有handleclose
		m_IsHandleClosed = 1;
	}

	return true;
}

/**
结束例程,也就是停止线程
*/
void PPM_Tcp_Client::EndInstance(void)
{
	ResetRunFlag();
}

/**
发数据线程主处理函数,是死循环
*/
PPM_THR_FUNC_VOID_RETURN PPM_Tcp_Client::SendMain(void)
{
	BeginInstance();

	m_bIsSendThr_Stop = 0;

	int res = 0;
	// 收发先在一个线程中，处理不过来的话再分开

	m_bRunFlag_Send = true;
	
	while(m_bRunFlag_Send)
	{
		res = OnSend();
		if (res < 0)
		{   
			//有数据发送但发送失败
			PPM_OS::sleep(10);
			if(0 == this->m_type){
				//客户端重新连接,注意，重连的时候收发数据线呈不能停止，应用不知道这一细节
				if(1 == m_stru_clientctl.bReconnect){
					//一直等到连接上为止
					int tm_icount = 30;
					while((false == Connect()) && (true == m_bRunFlag_Send)){
							if(tm_icount > 29){
								PPM_SOCKLOG((LM_ERROR,"connecting to %s,port:%d failed%m!\n",m_Addr.get_host_addr(),m_Addr.get_port_number()));
								tm_icount = 0;
							}
							PPM_OS::sleep(2000);
							tm_icount++;
					}
					//将刚才没有发出去的数据发出去
					if(1 == m_bSendHeadFlag_){
						res = m_skStream.send((char*)m_psendBuf, m_nSendCount);
					}else{
						res = m_skStream.send((char*)(m_psendBuf + 4),(m_nSendCount - 4));
					}
					if(res < 0){
						PPM_SOCKLOG((LM_ERROR,"resend error %m\n"));
					}

				}else{
					break;
				}
			}
			else{
				//服务端需要关闭socket，并从反应器中删掉
				//出现小于0的情况反映器自动会关掉对应的handle
				break;
			}
		}
		else if( (res == 0) && (1 == m_bSendHeadFlag_) )
		{
			//没有数据发送,如果是服务端，检测socket是否正常
			//发送测试包
			res = m_skStream.send((char*)m_Test,sizeof(m_Test));
			//发送测试包失败
			if(res < 0){
				if(0 == m_type){
					if(1 == m_stru_clientctl.bReconnect){
						//一直等到连接上为止
						int tm_icount = 30;
						while((false == Connect()) && (true == m_bRunFlag_Send)){
							if(tm_icount > 29){
								PPM_SOCKLOG((LM_ERROR,"connecting to %s,port:%d failed%m!\n",m_Addr.get_host_addr(),m_Addr.get_port_number()));
								tm_icount = 0;
							}
							PPM_OS::sleep(2000);
							tm_icount++;
						}
					}
				}else{
					//表明此socket有问题
					break;
				}
			}
			PPM_OS::sleep(100);
		}else if(0 == res){
			PPM_OS::sleep(100);
		}
	}
	m_bIsSendThr_Stop = 1;

	return 0;
}

/**
反应器线程主处理函数,是死循环
*/
PPM_THR_FUNC_RETURN
PPM_Tcp_Client::ReactorMain(long priority){	
	
	if(PPM_INVALID_HANDLE != this->get_handle()){
		this->reactor()->register_handler(this,READ_MASK);
	}
  m_IsHandleClosed = 0;
  this->reactor()->StartThread(priority);
//  this->reactor()->run_event_loop();
  m_bRegisted = 1;

  return 0;
};
void  
PPM_Tcp_Client::UnRegiste(){
	if(1 == m_bRegisted){
		this->reactor()->remove_handler(this,READ_MASK);
		m_bRegisted = 0;
	}
	this->m_skStream.close();
};

void 
PPM_Tcp_Client::Registe()
{
	//如果反应器没有启动,启动反应器
	if(FALSE == this->reactor()->reactor_event_loop_done()){
		ReactorThread(this,m_lDecDataThreadPriority_);
//		PPM_hthread_t loopthr_handle;
//		//新起一个线程
//		PPM_Thread_Manager* const thrmng = PPM_Thread_Manager::instance();
//		//服务的侦听线程
//		thrmng->spawn((PPM_THR_FUNC)ReactorThread, &loopthr_handle, this,THR_NEW_LWP | THR_JOINABLE | THR_DETACHED,
//		  0,m_lDecDataThreadPriority_);
		while(this->reactor()->reactor_event_loop_done() == FALSE){
			PPM_OS::sleep(10);
		}
	}else{
		if(PPM_INVALID_HANDLE != this->get_handle()){
 			this->reactor()->register_handler(this,READ_MASK);
		}
		m_IsHandleClosed = 0;
 		m_bRegisted = 1;
	}
//	if((0 == m_bRecvHeadFlag_) && (1==m_type)){
	if(1==m_type){
		m_pstru_server->pClientDeal->BeginDeal();
	}
};

/**
发数据函数,在发线程中调用
输出:
   0 :提示上层continue
   负:提示上层错误,如果客户端就重连接,如果需要的话,服务端就关闭socket
   正:ok
*/
int PPM_Tcp_Client::OnSend()
{
	int res = 0;
	// 获取要发送的数据
	if( m_pSockBuffer->GetSendData(&m_psendBuf, m_nSendCount) > 0)
	{
		if(1 == m_bSendHeadFlag_){
			res = m_skStream.send((char*)m_psendBuf, m_nSendCount);
		}else{
			res = m_skStream.send((char*)(m_psendBuf + 4),(m_nSendCount - 4));
		}
		if(res < 0){
			return -1;
		}
		return res;
	}

	return 0;
}
/**
发数据函数,在发线程中调用
输出:
   0 :提示上层continue
   负:提示上层错误,如果客户端就重连接,如果需要的话,服务端就关闭socket
   正:ok
*/
int PPM_Tcp_Client::OnRecv()
{
	int nRt = 0;


	nRt = m_skStream.select_r();
	if (nRt < 0)
	{
		if(PPM_OS::last_error() == EWOULDBLOCK){
			PPM_SOCKLOG((LM_ERROR,"in OnRecv select error!%m\n "));
			return 0;
		}
		PPM_SOCKLOG((LM_ERROR,"select error !%m\n"));
		return nRt;
	}else if(0 == nRt){
		return 0;
	}
	nRt = 0;
	int tm_RecvCount = 0;
	if(1 == m_bRecvHeadFlag_){
		//有数据包头的处理
		tm_RecvCount = OnRecv_HaveHead();
	}else{
		tm_RecvCount = OnRecv_NoHead();
	}
	if(tm_RecvCount <= 0){
		return tm_RecvCount;
	}
	//重试次数
	//上面已经收到了纯业务的数据,已经放到m_precvBuf中
	if(m_pSockBuffer==NULL || m_pSockBuffer->m_RecvMode == CNodeMng::NullMode){
		//直接处理
        if(m_type == 1)
        {
		    m_pstru_server->pClientDeal->DealData(m_precvBuf,tm_RecvCount);
        }
        else
        {
		    m_pstru_client->pClientDeal->DealData(m_precvBuf,tm_RecvCount);
        }
		return 0;
	}
	else{
		//需要写入接受缓冲
		//向接收缓冲写数据,直到写进去为止
		nRt = m_pSockBuffer->PutRecvData(m_precvBuf, tm_RecvCount);
		while( (0 == nRt) && ( true == m_bRunFlag_Recv) ){
			PPM_OS::sleep(10);
			nRt = m_pSockBuffer->PutRecvData(m_precvBuf, tm_RecvCount);
		}
		return nRt;
	}
}
int 
PPM_Tcp_Client::OnRecv_HaveHead()
{
	int nLen = 0;
	int retrytimes = 0;
	int nRt = 0;
	while(nLen < PKG_HEADER_LEN)
	{
		nRt = m_skStream.recv((char*)(m_precvBuf + nLen), PKG_HEADER_LEN - nLen);
		if (nRt >= 0)
		{
			nLen += nRt;
		}
		else if(-2 == nRt){
			return -1;
		}
		else if(PPM_OS::last_error() == EWOULDBLOCK){
			PPM_OS::sleep(2);
			if(retrytimes++ > 5000)
			{
				PPM_SOCKLOG((LM_ERROR,"maybe pack headlen is error %d - %d!%m\n ",nLen,PKG_HEADER_LEN));
				return -1;
			};
			continue;
		}
		else
		{
			PPM_SOCKLOG((LM_ERROR,"RECV ERROR %d - %d %m\n",nLen,PKG_HEADER_LEN));
			return nRt;
		}
	}
	// 测试包
	if ( (m_precvBuf[0] ==(BYTE)(TESTPACKHEAD & 0XFF) ) && 
		 (m_precvBuf[1] ==(BYTE)((TESTPACKHEAD & 0XFF00) >> 8 )) )
	{
		return 0;
	}

	int nCount = MAKEWORD_NETSEQ(m_precvBuf[2],m_precvBuf[3]);

	if ((nCount > (NODE_BUF_SIZE - PKG_HEADER_LEN)) || 
		(m_precvBuf[0] !=(BYTE)(DATAPACKHEAD & 0XFF)) || 
		(m_precvBuf[1] !=(BYTE)((DATAPACKHEAD & 0XFF00) >> 8)) )
	{
		//到这里说明包头错误，需要定位到包头，包头有可能是测试包，有可能是数据包,最多检测8k
		int tm_count = 0;
		retrytimes = 0;
		BOOL tm_biscorrect = FALSE;
		while(tm_count < 65536){
			PPM_SOCKLOG((LM_ERROR,"pack header error %d - %d %m\n",m_precvBuf[0],m_precvBuf[1]));
			PPM_OS::memmove(m_precvBuf,m_precvBuf+1,(PKG_HEADER_LEN - 1));

			nRt = m_skStream.recv((char*)(m_precvBuf + PKG_HEADER_LEN - 1), 1);
			if(nRt > 0){
				tm_count += nRt;
			}
			else if(-2 == nRt){
				return -1;
			}
			else if(PPM_OS::last_error() == EWOULDBLOCK){
				if(retrytimes++ > 2000)
				{
					PPM_SOCKLOG((LM_ERROR,"try to position head!\n ",nLen,PKG_HEADER_LEN));
					return 0;
				};
				continue;
			}else{
				PPM_SOCKLOG((LM_ERROR,"RECV ERROR %l %m\n"));
				return nRt;
			}

			tm_count += nRt;

			if(0 == nRt){
				PPM_SOCKLOG((LM_ERROR,"RECV len 0 %m\n"));
				continue;
			}
			if(TRUE == CheckPackHead(m_precvBuf)){
				PPM_SOCKLOG((LM_ERROR,"pack header correct!\n"));
//				nCount = m_precvBuf[3] * 256 + m_precvBuf[2];
				nCount = MAKEWORD_NETSEQ(m_precvBuf[2],m_precvBuf[3]);
				if(0 == nCount){
					return 0;//测试包
				}
				tm_biscorrect = TRUE;
				break;
			}
		}
		if(FALSE == tm_biscorrect){
			return 0;
		}
	}
	nRt = 0;
	nLen = 0;
	// 只收纯数据

	retrytimes = 0;
	while(nLen < nCount)
	{
		//注意这里的非阻塞方式的，所以一个onreceive事件可能收不过来
		nRt = m_skStream.recv((char*)(m_precvBuf + nLen), nCount - nLen);
		if (nRt >= 0)
		{
			nLen += nRt;
		}
		else if(-2 == nRt){
			return -1;
		}
		else if(PPM_OS::last_error() == EWOULDBLOCK){
			
			if(retrytimes++ > 5000)
			{
				PPM_SOCKLOG((LM_ERROR,"maybe pack headlen is error %d - %d!%m\n ",nLen,nCount));
				return -1;
			};
			PPM_OS::sleep(2);
			continue;
		}
		else
		{
			PPM_SOCKLOG((LM_ERROR,"RECV ERROR %d - %d %m\n",nLen,nCount));
			return nRt;
		}
	}
	
	m_nRecvCount += nCount;
	return nCount;
}
int 
PPM_Tcp_Client::OnRecv_NoHead()
{
	//调用具体的业务数据处理函数，先将数据接收到缓冲中
    if (m_type == 1)
    {
	    return m_pstru_server->pClientDeal->RecvDeal(m_precvBuf,NODE_BUF_SIZE,&m_skStream);
    }
    else
    {
        while (m_pstru_client == NULL)
        {
            PPM_OS::sleep(100);
        }
	    return m_pstru_client->pClientDeal->RecvDeal(m_precvBuf,NODE_BUF_SIZE, &m_skStream);
    }
}
/**
检查包头的正确性，在包头有错误时使用
*/
BOOL PPM_Tcp_Client::CheckPackHead(const BYTE* const pBuf)
{
	if(NULL == pBuf){
		return FALSE;
	}
	//首先看是否为测试包
	// 测试包
	int nCount = MAKEWORD_NETSEQ(m_precvBuf[2],m_precvBuf[3]) ;
	if ( (m_precvBuf[0] ==(BYTE)(TESTPACKHEAD & 0XFF) ) && 
		 (m_precvBuf[1] ==(BYTE)((TESTPACKHEAD & 0XFF00) >> 8 )) &&
		 (nCount == 0)
		 )
	{
		return TRUE;
	}

	if ((nCount < (NODE_BUF_SIZE - PKG_HEADER_LEN)) && 
		(m_precvBuf[0] ==(BYTE)(DATAPACKHEAD & 0XFF)) && 
		(m_precvBuf[1] ==(BYTE)((DATAPACKHEAD & 0XFF00) >> 8)) )
	{
		return TRUE;
	}
	return FALSE;
}
/**
从接收缓冲区中取数据, 不支持同时多个读,这个函数在内部调用
输入:
   pBuf :需要写入收数据的缓冲的指针的指针
   nSize:需要返回的读取的数据的大小
输出:
   读取到的数据的大小
*/
int PPM_Tcp_Client::RecvData(BYTE ** const pBuf, int& nSize)
{
	if(m_pSockBuffer->ReadData(pBuf, nSize) <= 0)
	{
		return 0;
	}
	return nSize;
}
/**
收数据线程主处理函数,是死循环
*/
PPM_THR_FUNC_VOID_RETURN PPM_Tcp_Client::RecvMain(void)
{
	if (NULL == m_pActor)
	{
		return 0;
	}

	PPM_Time_Value wtime(0, 10000);
	int nSize = 0;
	BYTE* pData = NULL;
    //表明线程开赛
	m_bIsRecvThr_Stop = 0;

	m_bRunFlag_Recv = true;

	while(m_bRunFlag_Recv)
	{
		nSize = 0;
		pData = NULL;

		if (RecvData(&pData, nSize) > 0)
		{
			if( (pData != NULL) && (NULL != m_pActor) )
			{
				m_pActor->DealData(pData, nSize);
			}

		}
		else if(1 == m_type){
			//服务端
			//表示没有数据可读，且发线程关闭，且需要删除资源，且是服务端说明
			//这个时候如果读不到数据就需要推出了,下面的处理是sleep100毫秒再读一次
			//读不到就推出线程
			if( (1 == m_bIsSendThr_Stop) && 
				(1 == GetDestoryFlag()) ){
				PPM_OS::sleep(100);
				nSize = 0;
				pData = NULL;

				if (RecvData(&pData, nSize) > 0)
				{
					if( (pData != NULL) && (m_pActor != NULL) )
					{
						m_pActor->DealData(pData, nSize);
					}
				}
				else{
					break;
				}
			}
			else{
				PPM_OS::sleep(100);
			}

		}else{
			//客户端,没有可收的数据
			if(1 == m_stru_clientctl.bIsDestory){
				break;
			}
			PPM_OS::sleep(100);
		}
	}
    //表明线程结束
	m_bIsRecvThr_Stop = 1;
	//如果是客户端需要删除资源
    if(0 == m_type){
		if( (1 == m_stru_clientctl.bIsDestory) ){
			//需要关闭
			StopThread(2);
			m_ptcp_client_actor_->ReleaseClient(m_pstru_client);
		}
	}
	//如果是服务端且需要删除资源
	else if(1 == GetDestoryFlag()){
		//那就需要删除发数据线程,然后清除资源
		StopThread(2);
		m_ptcp_server_actor_->ReleaseClient(m_pstru_server);
	}
	
	return 0;
}
void PPM_Tcp_Client::SetCloseType(BYTE closetype)
{
	m_stru_clientctl.bClosetype = closetype;
	m_stru_serverctl.bClosetype = closetype;
}
void PPM_Tcp_Client::SetClientActor(PPM_Tcp_Client_Actor* ptcpclientactor)
{
	m_ptcp_client_actor_ = ptcpclientactor;
}
void PPM_Tcp_Client::SetServerActor(PPM_Tcp_Server_Actor* ptcpserveractor)
{
	m_ptcp_server_actor_ = ptcpserveractor;
}
void PPM_Tcp_Client::SetClientStru(stru_client* pstru_client)
{
	m_pstru_client = pstru_client;
}
void PPM_Tcp_Client::SetServerStru(stru_server* pstru_server)
{
	m_pstru_server = pstru_server;
}

/**
在有数据从socket过来的时候,反应器会触发到此函数
*/
int
PPM_Tcp_Client::handle_input (PPM_HANDLE handle)
{
	int res = OnRecv();
	if (res < 0)
	{
		PPM_SOCKLOG((LM_ERROR,"recv return %d! %m\n",res));
		return -1;
	}
    return 0;
}

/**
主动关闭client
*/
int
PPM_Tcp_Client::CloseClient()
{
	//设置关闭类型为主动关闭
	SetCloseType(1);

	//先停掉发送线程
	StopThread(2);

	//删除注册对象
	if(1 == m_bRegisted){
		UnRegiste();
//		this->reactor()->remove_handler(this,READ_MASK);
	}else{
// 		m_IsHandleClosed = 1;
// 		m_ptcp_client_actor_->ReleaseClient(m_pstru_client);
		handle_close(this->get_handle(),READ_MASK);
	}

	//后续由handleclose处理
	return 0;
}

/**
在socket异常或socket关闭的时候会触发此函数
*/
int
PPM_Tcp_Client::handle_close (PPM_HANDLE handle,
                               PPM_Reactor_Mask)
{
	//服务端如果已经handleclosed
	//客户端的有几种情况,如果没有注册,m_IsHandleClosed直接等于1,如果注册就判断是否有handleclose
	if((1 == m_IsHandleClosed) && (1 == m_bRegisted)){
		return 0;
	}
	//如果是客户端
	if(0 == m_type){
		return HandleClose_client(handle);
	}
	else{
		if(NULL != m_pActor)
		{
			m_pActor->ClientClose();
		}
		if( 1 == m_stru_serverctl.bClosetype)//主动关闭
		{
			//服务端主动关闭处理
			return HandleClose_server_active(handle);
		}else{
			//服务端被动退出处理
			return HandleClose_server_passive(handle);
		}
	}
}

/**
客户端handle_close时的操作
*/
int
PPM_Tcp_Client::HandleClose_client(PPM_HANDLE handle)
{
	PPM_SOCKLOG ((LM_INFO, "CClient::handle_close handle = %d,type:%d\n", handle,m_stru_clientctl.bClosetype));
	//主动发起关闭,被动产生关闭两种情况
	//主动发起关闭,需要释放全部资源
	bool bneeddestory = false;
	if(1 == m_stru_clientctl.bClosetype){
		bneeddestory = true;
	}else{
		//数据发送错误,如果需要重新连接,不要释放资源,关闭原先的连接即可
		if(1 == m_stru_clientctl.bReconnect){
			m_skStream.close();
		}
		else{
			//需要释放全部资源
			bneeddestory = true;
		}
	}

	if(true == bneeddestory){
		//释放全部资源
		//停止发送数据线程
		StopThread(2);
		//判断是否需要处理完接收的数据,或者是根本就没有接受缓冲
		BYTE dealflag = m_pSockBuffer->GetDealDataFlag();
		if( ((dealflag & 0x01) <= 0) || 
			(m_pSockBuffer->m_RecvMode == CNodeMng::NullMode) ){
		  //不需要处理完
		  //关闭线程
		  StopThread(1);
		  //删除安全缓冲
		  //删除通讯对象
		  //提示服务端已经删除

		  if(NULL != m_ptcp_client_actor_){
			  m_IsHandleClosed = 1;
			  m_ptcp_client_actor_->ReleaseClient(m_pstru_client);
			  m_pActor = NULL;
		  }
		  else{
			  PPM_SOCKLOG((LM_ERROR,"NO m_ptcp_client_actor_!\n"));
			  m_IsHandleClosed = 1;
			  return -1;
		  }

		}
		else{
		  //安全缓冲和通讯对象都不删除,等待发送数据线程完成后再由线呈结束调用其删除
		  //提示在收数据线呈关闭时删除资源
		  m_stru_clientctl.bIsDestory = 1;
		  //提示外面此客户端连接已无效
		  m_ptcp_client_actor_->ClearClientIDClientflag(m_pstru_client);
		  m_IsHandleClosed = 1;
		  return 0;
		}
	}
	m_IsHandleClosed = 1;
	return 0;

}

/**
服务端handle_close时的操作,被动关闭
*/
int
PPM_Tcp_Client::HandleClose_server_passive(PPM_HANDLE handle)
{
	PPM_SOCKLOG ((LM_INFO, "CClient::handle_close handle = %d,type:passive\n", handle));
	if(2 == GetDestoryFlag()){
	  //这个时候线程已经停掉了,留着外面serverreset删除自己
	  PPM_SOCKLOG((LM_INFO,"tcpclient reused,delete old! \n"));
	  m_IsHandleClosed = TRUE;
//	  delete this;
	  return 0;
	}
	//首先关闭数据发送线程
	StopThread(2);
	//关闭socket
	m_skStream.close ();

	//反应器中移除对象
	//自动移除


	//判断是否需要处理完接收的数据
	BYTE dealflag = m_pSockBuffer->GetDealDataFlag();
	if( ((dealflag & 0x01) <= 0) || 
		(m_pSockBuffer->m_RecvMode == CNodeMng::NullMode) ){
	  //不需要处理完
	  //关闭线程
	  StopThread(1);
	  //删除安全缓冲
	  //删除通讯对象
	  //提示服务端已经删除
	  if(NULL != m_ptcp_server_actor_){
		  m_IsHandleClosed = 1;
		  //这里需要释放
		  m_ptcp_server_actor_->ReleaseClient(m_pstru_server);
	  }
	  else{
		  PPM_SOCKLOG((LM_ERROR,"NO m_ptcp_server_actor_!\n"));
		  m_IsHandleClosed = 1;
		  return -1;
	  }

	}
	else{
	  //安全缓冲和通讯对象都不删除,等待发送数据线程完成后再由线呈结束调用其删除
	  //提示在收数据线呈关闭时删除资源
	  SetServerDestoryFlag(1);
	  //提示外面此客户端连接已无效
	  m_ptcp_server_actor_->ClearClientflag(m_pstru_server);
	}
	m_IsHandleClosed = 1;
    return 0;
}
/**
服务端handle_close时的操作,住户动关闭
*/
int
PPM_Tcp_Client::HandleClose_server_active(PPM_HANDLE handle)
{
	//这个函数执行完后线程需停止,线程需关闭，不需要释放自己
	PPM_SOCKLOG ((LM_INFO, "CClient::handle_close handle = %d,type:active\n", handle));

	//关闭数据发送和接受线程
	StopThread(3);

	//关闭socket
	m_skStream.close ();

	//提示m_ptcp_server_actor_中释放相应的client
	if(NULL != m_ptcp_server_actor_){
		m_IsHandleClosed = 1;
		m_ptcp_server_actor_->ReleaseClient(m_pstru_server);
	}
	else{
		PPM_SOCKLOG((LM_ERROR,"NO m_ptcp_server_actor_!\n"));
		m_IsHandleClosed = 1;
		return -1;
	}
	m_IsHandleClosed = 1;
    return 0;
}
/**
移除注册对象
*/
int
PPM_Tcp_Client::DealAbnormSock()
{
	UnRegiste();
// 	if(1 == this->m_bRegisted){
// 		this->reactor()->remove_handler(this,READ_MASK);
// 	};
	return 1;
}

/**
关闭线程flag :1表示收线程 2表示发线程 3表示收发线程都Stop
注意,这里是强行关闭
*/
void
PPM_Tcp_Client::StopThread(BYTE flag,int iwaitSeconds)
{
	//判断线程是否启动，如果线程没有启动，无线程可杀
	BYTE tm_flag = flag;
	if(m_pSockBuffer->m_SendMode == CNodeMng::NullMode){
		tm_flag = (tm_flag & 0xfd);
	}
	if(m_pSockBuffer->m_RecvMode == CNodeMng::NullMode){
		//对于收数据的情况,在没有线程的情况下也需要通知写数据死循环退出
		if((flag & 0x01) > 0 ){
			if(NULL != m_pActor){
				m_pActor->SetOverFlag(TRUE);
			}
		}
		tm_flag = (tm_flag & 0xfe);
	}
	//停掉发线程
	int i = 0;
	BOOL isTrue = FALSE;
	if((tm_flag & 0x02) > 0)
	{
		m_bRunFlag_Send = false;
		if(-1 == iwaitSeconds)
		{
			//一直等待线呈结束
			while(1 != m_bIsSendThr_Stop){
				PPM_OS::sleep(10);
			}
		}
		else
		{
			for(i = 0; i < (iwaitSeconds * 100) ;i++){
				if(1 == m_bIsSendThr_Stop){
					isTrue = TRUE;
					break;
				}
				else{
					PPM_OS::sleep(10);
				}
			}
			if(FALSE == isTrue){
				if((0 == m_bIsSendThr_Stop)){
					PPM_Thread_Manager::instance()->terminate_thr(m_SendThr_Handle);
					PPM_SOCKLOG((LM_ERROR,"强行退出发线程!\n"));
					m_bIsSendThr_Stop = 1;
				}
			}
		}

	}

	isTrue = FALSE;

	if((tm_flag & 0x01) > 0)
	{
		m_bRunFlag_Recv = false;
		//控制dealdata不要处理数据了
		if(NULL != m_pActor){
			m_pActor->SetOverFlag(TRUE);
		}

		if(-1 == iwaitSeconds)
		{
			//一直等待线呈结束
			while(1 != m_bIsRecvThr_Stop){
				PPM_OS::sleep(10);
			}
		}
		else
		{
			for(i = 0; i < (iwaitSeconds * 100) ;i++){
				if(1 == (m_bIsRecvThr_Stop)){
					isTrue = TRUE;
					break;
				}
				else{
					PPM_OS::sleep(10);
				}
			}
			if(FALSE == isTrue){
				if(0 == m_bIsRecvThr_Stop){
					PPM_Thread_Manager::instance()->terminate_thr(m_RecvThr_Handle);
					PPM_SOCKLOG((LM_ERROR,"强行退出收线程!\n"));
				}
			}
		}
	}

	return;
}

/**
这个是在buffer重用的时候调用的函数
*/
int
PPM_Tcp_Client::ServerReset(PPM_SOCK_Stream newsocket)
{
	BYTE tm_bRegisteFlag = m_bRegisted;

	//线程停止时不要删除资源,表示是重用的,只删除自己的
	if(0 == m_type){
		m_stru_clientctl.bIsDestory = 0;
	}else{
		SetServerDestoryFlag(2);
	}
	m_ptcp_server_actor_->ClearClientflag(m_pstru_server);

	//停止收发线程
	StopThread(3);

	//关闭原先socket
	m_skStream.close();

	//反应器移除原先注册对象
  	DealAbnormSock();

	
	if(1 == m_bReactorDelFlag){
		PPM_OS::sleep(500);
		delete m_pstru_server->pReactor;	
		m_pstru_server->pReactor = NULL;
		m_bRegisted = 0;
	}

	if(1 == m_type) //如果是服务端
	{
		//等待HANDLECLOSE结束
		int i = 0;
		while(FALSE == m_IsHandleClosed){
			PPM_OS::sleep(100);
			
			if((i++) > 50){
				break;
			}
		}
	}
	else{
		//客户端
		//然后由handleclose处理掉自己
		if(1 != tm_bRegisteFlag){
			handle_close(this->get_handle(),READ_MASK);
		}
		else{
			//等待HANDLECLOSE结束
			int i = 0;
			while(FALSE == m_IsHandleClosed){
				PPM_OS::sleep(100);
				
				if((i++) > 50){
					break;
				}
			}
		}	

	}

	delete this ;
	return 0;
}



