// Addr.cpp
// Addr.cpp,v 4.14 2002/04/11 14:49:11 dhinton Exp

#include "PPM_Addr.h"

//const PPM_Addr PPM_sap_any (-1, -1);
const PPM_Addr PPM_Addr::sap_any (-1, -1);


#if defined (PPM_LACKS_INLINE_FUNCTIONS)
#include "PPM_Addr.inl"
#endif 

void
PPM_Addr::dump (void) const
{
  PPM_TRACE ("PPM_Addr::dump");
}

// Initializes instance variables.

void
PPM_Addr::base_set (int type, int size)
{
  this->addr_type_ = type;
  this->addr_size_ = size;
}

// Initializes instance variables.  Note that 0 is an unspecified
// protocol family type...

PPM_Addr::PPM_Addr (int type, int size)
{
  this->base_set (type, size);
}

PPM_Addr::~PPM_Addr (void)
{
}
