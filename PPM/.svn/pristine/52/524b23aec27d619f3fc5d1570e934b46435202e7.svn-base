/* -*- C++ -*- */
// Log_Record.i,v 4.12 2001/08/12 05:21:42 shuston Exp

// Log_Record.i

PPM_INLINE
PPM_Log_Record::~PPM_Log_Record (void)
{
}

// PPM_INLINE void
// PPM_Log_Record::encode (void)
// {
//   PPM_TRACE ("PPM_Log_Record::encode");
//   this->length_ = htonl (this->length_);
//   this->type_ = htonl (this->type_);
//   // Make sure we don't enclose the sec() and usec() fields until
//   // they've been normalized.
//   this->secs_ = htonl (this->secs_);
//   this->usecs_ = htonl (this->usecs_);
//   this->pid_ = htonl (this->pid_);
// }
// 
// PPM_INLINE void
// PPM_Log_Record::decode (void)
// {
//   PPM_TRACE ("PPM_Log_Record::decode");
//   PPM_Time_Value tv (ntohl (this->secs_),
//                      ntohl (this->usecs_));
// 
//   this->secs_ = tv.sec ();
//   this->usecs_ = tv.usec ();
//   this->type_ = ntohl (this->type_);
//   this->pid_ = ntohl (this->pid_);
//   this->length_ = ntohl (this->length_);
// }
// 
PPM_INLINE long
PPM_Log_Record::type (void) const
{
  return (long) this->type_;
}

PPM_INLINE void
PPM_Log_Record::type (long t)
{
  this->type_ = (PPM_UINT32) t;
}

PPM_INLINE long
PPM_Log_Record::length (void) const
{
  return (long) this->length_;
}

PPM_INLINE void
PPM_Log_Record::length (long l)
{
  this->length_ = PPM_static_cast (PPM_UINT32, l);
}

PPM_INLINE PPM_Time_Value 
PPM_Log_Record::time_stamp (void) const
{
  return PPM_Time_Value ((long) this->secs_, (long) this->usecs_);
}

PPM_INLINE void
PPM_Log_Record::time_stamp (const PPM_Time_Value &ts)
{
  this->secs_ = (PPM_UINT32) ts.sec ();
  this->usecs_ = (PPM_UINT32) ts.usec ();
}

PPM_INLINE const char *
PPM_Log_Record::msg_data (void) const
{
  PPM_TRACE ("PPM_Log_Record::msg_data");
  return this->msg_data_;
}

PPM_INLINE size_t
PPM_Log_Record::msg_data_len (void) const
{
  PPM_TRACE ("PPM_Log_Record::msg_data_len");
  return PPM_OS::strlen (this->msg_data_) + 1;
}
