#include <ctype.h>
#include "./PPM_OS_String.h"

#if defined (PPM_LACKS_INLINE_FUNCTIONS)
# include "./PPM_OS_String.inl"
#endif

char *
PPM_OS_String::strsncpy (char *dst, const char *src, size_t maxlen)
{
  register char *rdst = dst;
  register const char *rsrc = src;
  register size_t rmaxlen = maxlen;

  if (rmaxlen > 0)
    {
      if (rdst!=rsrc)
        {
          *rdst = '\0';
          if (rsrc != 0)
            strncat (rdst, rsrc, --rmaxlen);
        }
      else
        {
          rdst += (rmaxlen - 1);
          *rdst = '\0';
        }
    }
  return dst;
}

char *
PPM_OS_String::strdup (const char *s)
{
  return ::strdup (s);
}
