#ifndef PPM_OS_ERRNO_H
#define PPM_OS_ERRNO_H


#if !defined (PPM_LACKS_PRAGMA_ONCE)
# pragma once
#endif /* PPM_LACKS_PRAGMA_ONCE */

#include "./PPM_MACRO.h"
#include "../stdclass/StdHeader.h"

/**
 * @class PPM_Errno_Guard
 *
这个类的作用主要是为了获取系统错误的信息，单独包装是由于有时候需要更改错误的指
就有了单线程和多线程的区别了
 */
class PPM_Errno_Guard
{
public:
  ///  Stash the value of <error> into <error_> and initialize the
  ///  <errno_ptr_> to the address of <errno_ref>.
  PPM_Errno_Guard (PPM_ERRNO_TYPE &errno_ref,
                   int error);

  ///  Stash the value of <errno> into <error_> and initialize the
  ///  <errno_ptr_> to the address of <errno_ref>.
  PPM_Errno_Guard (PPM_ERRNO_TYPE &errno_ref);

  /// Reset the value of <errno> to <error>.
  ~PPM_Errno_Guard (void);

  /// Assign <error> to <error_>.
  int operator= (int error);

  /// Compare <error> with <error_> for equality.
  int operator== (int error);

  /// Compare <error> with <error_> for inequality.
  int operator!= (int error);

private:
#if defined (PPM_MT_SAFE)
  PPM_ERRNO_TYPE *errno_ptr_;
#endif /* PPM_MT_SAFE */
  int error_;
};

#if !defined (PPM_LACKS_INLINE_FUNCTIONS)
# include "./PPM_OS_Error.inl"
#endif /* PPM_HAS_INLINED_OSCALLS */

#endif /* PPM_OS_ERRNO_H */
