/* -*- C++ -*- */
// SOCK.i,v 4.4 1998/09/25 17:24:22 irfan Exp

// SOCK.i

PPM_INLINE
PPM_SOCK::~PPM_SOCK (void)
{
  // PPM_TRACE ("PPM_SOCK::~PPM_SOCK");
}

PPM_INLINE int 
PPM_SOCK::set_option (int level, 
		      int option, 
		      void *optval, 
		      int optlen) const
{
  PPM_TRACE ("PPM_SOCK::set_option");
  return PPM_OS::setsockopt (this->get_handle (), level, 
			     option, (char *) optval, optlen);
}

// Provides access to the PPM_OS::getsockopt system call.

PPM_INLINE int 
PPM_SOCK::get_option (int level, 
		      int option, 
		      void *optval, 
		      int *optlen) const
{
  PPM_TRACE ("PPM_SOCK::get_option");
  return PPM_OS::getsockopt (this->get_handle (), level, 
			     option, (char *) optval, optlen);
}
