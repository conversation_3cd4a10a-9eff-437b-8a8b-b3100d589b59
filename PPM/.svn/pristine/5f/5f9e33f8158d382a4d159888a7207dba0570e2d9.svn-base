#include "./PPM_SelfMonitor.h"
#include "./PPM_Thread_Manager.h"


PPM_SelfMonitor::PPM_SelfMonitor(const char* sharemem_name,char* pchversion,char* pchprogramname,int memlen,int checkspan, int maxTimes)
: m_FileMapping_(sharemem_name,memlen)
{
	if(PPM_OS::strlen(pchversion) > sizeof(m_pchVersion_)){
		PPM_OS::strcpy(m_pchVersion_,"Ver Err!");
	}else{
		PPM_OS::strcpy(m_pchVersion_,pchversion);		
	}
	if(PPM_OS::strlen(pchprogramname) > sizeof(m_pchProgramName_)){
		PPM_DEBUG((LM_ERROR,"program name is too long!\n"));
	}else{
		PPM_OS::strcpy(m_pchProgramName_,pchprogramname);		
	}
	
	SetMonitorFlag(1); //设置为正常状态 
	SetMonitorVersion(m_pchVersion_);
	SetMonitorTime();
	times = 0;
    checkSpan = checkspan;
	m_nMaxErrorTimes = maxTimes;

}



/**
将要监控的线程注册到监控模块，外部调用
输入：
pMonitor：需要监控的模块指针，注意这个模块包含一个线程
*/
void
PPM_SelfMonitor::Register(PPM_ThreadDeal_Base* pMonitor)
{
	CStdAutoLock tm_autolock(&m_lock_);
	std::list<PPM_ThreadDeal_Base*>::iterator itr;
	itr = m_list_Thread_.begin();
	while(itr != m_list_Thread_.end()){
		if( (*itr) == pMonitor ){
			return;//已经存在
		}
		itr++;
	}
	m_list_Thread_.push_back(pMonitor);
	pMonitor->SetThreadFlag(TRUE);
	PPM_DEBUG((LM_ERROR,"PPM_SelfMonitor::Register:%s\n", pMonitor->m_name_));
}

/**
将要监控的线程从模块中消除注册,外内部调用
输入：
pMonitor：需要取消注册的模块指针，注意这个模块包含一个线程
*/
void
PPM_SelfMonitor::Remove(PPM_ThreadDeal_Base* pMonitor)
{
	PPM_DEBUG((LM_ERROR,"PPM_SelfMonitor::Remove:%s\n", pMonitor->m_name_));
	CStdAutoLock tm_autolock(&m_lock_);
	
	std::list<PPM_ThreadDeal_Base*>::iterator itr;
	itr = m_list_Thread_.begin();
	while(itr != m_list_Thread_.end()){
		if( (*itr) == pMonitor ){
			m_list_Thread_.erase(itr);
			return;
		}
		itr++;
	}
}

/**
这是一个死循环，用来定期检测各个注册的线程的运行情况
*/
BOOL
PPM_SelfMonitor::main()
{
//	PPM_OS::sleep(1000*90);//一分钟检测一次
	for(int i = 0;i < checkSpan; i++){
		PPM_OS::sleep(1000);
		if(GetRunningFlag() == 0){
			return FALSE;
		}
	} 
	
	if(TRUE == CheckStatus()){
		SetMonitorFlag(1); //如果一切正常，设置为正常状态
		PPM_DEBUG((LM_ERROR,"SetMonitorFlag:%d\n",1));
		times = 0;
	}else{
		 //不正常状态
		times++;
		if(times >= m_nMaxErrorTimes){
			PPM_DEBUG((LM_ERROR,"Should Set SetMonitorFlag(0)!\n"));
            SetMonitorFlag(0);
			PPM_DEBUG((LM_ERROR,"SetMonitorFlag:%d\n",0));

            //表明设置为不正常状态已经很长时间了，还在运行，重启机器
            //::ExitWindowsEx(EWX_REBOOT | EWX_FORCE,0);
		}
	}
	return TRUE;
};

/**
检测各线程的运行情况，对于可复位的有问题的线程进行复位
输出：
TRUE :都正常，或者是有问题的线程都可复位
FALSE:有不可复位的线程有问题
*/
BOOL
PPM_SelfMonitor::CheckStatus()
{
	std::list<PPM_ThreadDeal_Base*>::iterator itr;
	itr = m_list_Thread_.begin();
	while(itr != m_list_Thread_.end()){
		//线程是否有问题
		if( 0 >= (*itr)->GetThreadFlag() ){
			//说明线程有问题
			if(0 >= (*itr)->IsResetable()){
				//不可复位
				PPM_DEBUG((LM_ERROR,"Thread %s is unnormal,unresetable!\n",(*itr)->m_name_));
				return FALSE;
			}else{
				//可复位
				PPM_DEBUG((LM_ERROR,"Thread %s is reseted!\n",(*itr)->m_name_));
				(*itr)->ResetThread();
				(*itr)->SetThreadFlag(0);//置位无效
			}			
		}else{
			//线程没有问题
 			PPM_DEBUG((LM_NOTICE,"Thread %s is Normal!\n",(*itr)->m_name_));
			(*itr)->SetThreadFlag(0);//置位无效
		}
		itr++;
	}
	return TRUE;
}

/************************************************************************
设置版本号
************************************************************************/
void 
PPM_SelfMonitor::SetMonitorVersion(char* pchVersion)
{
	m_FileMapping_.WriteFileMap(pchVersion,VERSION_LEN,4);
}
/************************************************************************
设置程序创建时间
************************************************************************/
void 
PPM_SelfMonitor::SetMonitorTime()
{
	PPM_Time_Value tm_time = PPM_OS::GetSelfCreateTime(m_pchProgramName_);
	m_FileMapping_.WriteFileMap(&tm_time,8,4 + VERSION_LEN);
}


