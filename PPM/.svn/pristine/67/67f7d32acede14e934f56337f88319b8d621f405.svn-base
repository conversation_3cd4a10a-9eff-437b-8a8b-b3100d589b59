// -*- C++ -*-

//=============================================================================
/**
 *  @file   Flag_Manip.h
 *
 *  Flag_Manip.h,v 1.5 2002/04/10 17:44:15 ossama Exp
 *
 *  This class includes the functions used for the Flag Manipulation.
 *
 *  <AUTHOR> <<EMAIL>>
 */
//=============================================================================

#ifndef PPM_FLAG_MANIP_H
#define PPM_FLAG_MANIP_H

#if !defined (PPM_LACKS_PRAGMA_ONCE)
# pragma once
#endif /* PPM_LACKS_PRAGMA_ONCE */

#include "./PPM_OS.h"

class  PPM_Flag_Manip
{
 public:

  // = Set/get/clear various flags related to I/O HANDLE.
  /// Set flags associated with <handle>.
  static int set_flags (PPM_HANDLE handle,
                        int flags);

  /// Clear flags associated with <handle>.
  static int clr_flags (PPM_HANDLE handle,
                        int flags);

  /// Return the current setting of flags associated with <handle>.
  static int get_flags (PPM_HANDLE handle);


};

#if !defined (PPM_LACKS_INLINE_FUNCTIONS)
#include "./PPM_Flag_Manip.inl"
#endif /* PPM_LACKS_INLINE_FUNCTIONS */

#endif  /* PPM_FLAG_MANIP_H */
