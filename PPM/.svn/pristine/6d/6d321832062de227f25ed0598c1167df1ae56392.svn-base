// Signal.cpp,v 4.48 2002/08/28 02:05:17 schmidt Exp

#include "./PPM_Signal.h"
#include "./PPM_OS_Error.h"

#if defined (PPM_LACKS_INLINE_FUNCTIONS)
#include "./PPM_Signal.inl"
#endif /* __PPM_INLINE__ */

// Static definitions.
sigset_t* PPM_Sig_Guard::default_mask_ = NULL;

extern "C" void
PPM_sig_handler_dispatch (int signum, siginfo_t *info, ucontext_t *context)
{
  PPM_TRACE ("PPM_sig_handler_dispatch");
  PPM_Sig_Handler::dispatch (signum, info, context);
}

//#define PPM_signal_handler_dispatcher PPM_SignalHandler(PPM_sig_handler_dispatch)

#define PPM_signal_handler_dispatcher PPM_SignalHandler(PPM_Sig_Handler::dispatch)

// Array of Event_Handlers that will handle the signals.
PPM_Event_Handler *PPM_Sig_Handler::signal_handlers_[PPM_NSIG];

// Remembers if a signal has occurred.
sig_atomic_t PPM_Sig_Handler::sig_pending_ = 0;

void
PPM_Sig_Action::dump (void) const
{
  PPM_TRACE ("PPM_Sig_Action::dump");
}

void
PPM_Sig_Set::dump (void) const
{
  PPM_TRACE ("PPM_Sig_Set::dump");
}

PPM_Sig_Action::PPM_Sig_Action (void)
{
  // PPM_TRACE ("PPM_Sig_Action::PPM_Sig_Action");
  this->sa_.sa_flags = 0;

  // Since Service_Config::signal_handler_ is static and has an
  // PPM_Sig_Action instance, Win32 will get errno set unless this is
  // commented out.
  this->sa_.sa_handler = 0;
}

PPM_Sig_Action::PPM_Sig_Action (PPM_SignalHandler sig_handler,
                                sigset_t *sig_mask,
                                int sig_flags)
{
  // PPM_TRACE ("PPM_Sig_Action::PPM_Sig_Action");
  this->sa_.sa_flags = sig_flags;

  if (sig_mask == 0)
    PPM_OS::sigemptyset (&this->sa_.sa_mask);
  else
    this->sa_.sa_mask = *sig_mask; // Structure assignment...

  this->sa_.sa_handler = PPM_SignalHandlerV (sig_handler);
}

PPM_Sig_Action::PPM_Sig_Action (PPM_SignalHandler sig_handler,
                                const PPM_Sig_Set &sig_mask,
                                int sig_flags)
{
  // PPM_TRACE ("PPM_Sig_Action::PPM_Sig_Action");
  this->sa_.sa_flags = sig_flags;

  // Structure assignment...
  this->sa_.sa_mask = sig_mask.sigset ();

  this->sa_.sa_handler = PPM_SignalHandlerV (sig_handler);
}

PPM_Sig_Action::PPM_Sig_Action (PPM_SignalHandler sig_handler,
                                int signum,
                                sigset_t *sig_mask,
                                int sig_flags)
{
  // PPM_TRACE ("PPM_Sig_Action::PPM_Sig_Action");
  this->sa_.sa_flags = sig_flags;

  if (sig_mask == 0)
    PPM_OS::sigemptyset (&this->sa_.sa_mask);
  else
    this->sa_.sa_mask = *sig_mask; // Structure assignment...

  this->sa_.sa_handler =  PPM_SignalHandlerV (sig_handler);
  PPM_OS::sigaction (signum, &this->sa_, 0);
}

PPM_Sig_Action::PPM_Sig_Action (PPM_SignalHandler sig_handler,
                                int signum,
                                const PPM_Sig_Set &sig_mask,
                                int sig_flags)
{
  // PPM_TRACE ("PPM_Sig_Action::PPM_Sig_Action");
  this->sa_.sa_flags = sig_flags;

  // Structure assignment...
  this->sa_.sa_mask = sig_mask.sigset ();

  this->sa_.sa_handler = PPM_SignalHandlerV (sig_handler);
  PPM_OS::sigaction (signum, &this->sa_, 0);
}

PPM_Sig_Action::PPM_Sig_Action (const PPM_Sig_Set &signals,
                                PPM_SignalHandler sig_handler,
                                const PPM_Sig_Set &sig_mask,
                                int sig_flags)
{
  // PPM_TRACE ("PPM_Sig_Action::PPM_Sig_Action");
  this->sa_.sa_flags = sig_flags;

  // Structure assignment...
  this->sa_.sa_mask = sig_mask.sigset ();

  this->sa_.sa_handler =  PPM_SignalHandlerV (sig_handler);

  for (int s = 1; s < PPM_NSIG; s++)
    if (signals.is_member (s))
      PPM_OS::sigaction (s, &this->sa_, 0);
}

PPM_Sig_Action::PPM_Sig_Action (const PPM_Sig_Set &signals,
                                PPM_SignalHandler sig_handler,
                                sigset_t *sig_mask,
                                int sig_flags)
{
  // PPM_TRACE ("PPM_Sig_Action::PPM_Sig_Action");
  this->sa_.sa_flags = sig_flags;

  if (sig_mask == 0)
    PPM_OS::sigemptyset (&this->sa_.sa_mask);
  else
    this->sa_.sa_mask = *sig_mask; // Structure assignment...

  this->sa_.sa_handler =  PPM_SignalHandlerV (sig_handler);

#if (PPM_NSIG > 0)  &&  !defined (CHORUS)
  for (int s = 1; s < PPM_NSIG; s++)
    if (signals.is_member (s))
      PPM_OS::sigaction (s, &this->sa_, 0);
#else  /* PPM_NSIG <= 0  ||  CHORUS */
  PPM_UNUSED_ARG (signals);
#endif /* PPM_NSIG <= 0  ||  CHORUS */
}


void
PPM_Sig_Guard::dump (void) const
{
  PPM_TRACE ("PPM_Sig_Guard::dump");
}

void
PPM_Sig_Handler::dump (void) const
{
  PPM_TRACE ("PPM_Sig_Handler::dump");
}

int
PPM_Sig_Handler::sig_pending (void)
{
  return PPM_Sig_Handler::sig_pending_ != 0;
}

void
PPM_Sig_Handler::sig_pending (int pending)
{
  PPM_TRACE ("PPM_Sig_Handler::sig_pending");

  PPM_Sig_Handler::sig_pending_ = pending;
}

PPM_Event_Handler *
PPM_Sig_Handler::handler (int signum)
{
  if (PPM_Sig_Handler::in_range (signum))
    return PPM_Sig_Handler::signal_handlers_[signum];
  else
    return 0;
}

PPM_Event_Handler *
PPM_Sig_Handler::handler_i (int signum,
                            PPM_Event_Handler *new_sh)
{
  PPM_TRACE ("PPM_Sig_Handler::handler_i");

  if (PPM_Sig_Handler::in_range (signum))
    {
      PPM_Event_Handler *sh = PPM_Sig_Handler::signal_handlers_[signum];

      PPM_Sig_Handler::signal_handlers_[signum] = new_sh;
      return sh;
    }
  else
    return 0;
}

PPM_Event_Handler *
PPM_Sig_Handler::handler (int signum,
                          PPM_Event_Handler *new_sh)
{
  return PPM_Sig_Handler::handler_i (signum, new_sh);
}

// Register an PPM_Event_Handler along with the corresponding SIGNUM.
// This method does NOT acquire any locks, so it can be called from a
// signal handler.

int
PPM_Sig_Handler::register_handler_i (int signum,
                                     PPM_Event_Handler *new_sh,
                                     PPM_Sig_Action *new_disp,
                                     PPM_Event_Handler **old_sh,
                                     PPM_Sig_Action *old_disp)
{
  PPM_TRACE ("PPM_Sig_Handler::register_handler_i");

  if (PPM_Sig_Handler::in_range (signum))
    {
      PPM_Sig_Action sa; // Define a "null" action.
      PPM_Event_Handler *sh = PPM_Sig_Handler::handler_i (signum,
                                                          new_sh);

      // Return a pointer to the old <PPM_Sig_Handler> if the user
      // asks for this.
      if (old_sh != 0)
        *old_sh = sh;

      // Make sure that <new_disp> points to a valid location if the
      // user doesn't care...
      if (new_disp == 0)
        new_disp = &sa;

      new_disp->handler (PPM_signal_handler_dispatcher);
      return new_disp->register_action (signum, old_disp);
    }
  else
    return -1;
}

// Register an PPM_Event_Handler along with the corresponding SIGNUM.
// This method acquires a lock, so it can't be called from a signal
// handler, e.g., <dispatch>.

int
PPM_Sig_Handler::register_handler (int signum,
                                   PPM_Event_Handler *new_sh,
                                   PPM_Sig_Action *new_disp,
                                   PPM_Event_Handler **old_sh,
                                   PPM_Sig_Action *old_disp)
{

  return PPM_Sig_Handler::register_handler_i (signum,
                                              new_sh,
                                              new_disp,
                                              old_sh,
                                              old_disp);
}

// Remove an PPM_Event_Handler.

int
PPM_Sig_Handler::remove_handler (int signum,
                                 PPM_Sig_Action *new_disp,
                                 PPM_Sig_Action *old_disp,
                                 int)
{

  if (PPM_Sig_Handler::in_range (signum))
    {
      PPM_Sig_Action sa (SIG_DFL, (sigset_t *) 0); // Define the default disposition.

      if (new_disp == 0)
        new_disp = &sa;

      PPM_Sig_Handler::signal_handlers_[signum] = 0;

      // Register either the new disposition or restore the default.
      return new_disp->register_action (signum, old_disp);
    }
  else
    return -1;
}

// Master dispatcher function that gets called by a signal handler and
// dispatches one handler...

void
PPM_Sig_Handler::dispatch (int signum,
                           siginfo_t *siginfo,
                           ucontext_t *ucontext)
{
  PPM_TRACE ("PPM_Sig_Handler::dispatch");

  // Save/restore errno.
  PPM_Errno_Guard error (errno);

  // We can't use the <sig_pending> call here because that acquires
  // the lock, which is non-portable...
  PPM_Sig_Handler::sig_pending_ = 1;

//   // Darn well better be in range since the OS dispatched this...
//   PPM_ASSERT (PPM_Sig_Handler::in_range (signum));

  PPM_Event_Handler *eh = PPM_Sig_Handler::signal_handlers_[signum];

  if (eh != 0)
    {
      if (eh->handle_signal (signum, siginfo, ucontext) == -1)
        {
          // Define the default disposition.
          PPM_Sig_Action sa ((PPM_SignalHandler) SIG_DFL, (sigset_t *) 0);

          PPM_Sig_Handler::signal_handlers_[signum] = 0;

          // Remove the current disposition by registering the default
          // disposition.
          sa.register_action (signum);

          // Allow the event handler to close down if necessary.
          eh->handle_close (PPM_INVALID_HANDLE,
                            PPM_Event_Handler::SIGNAL_MASK);
        }
      else
        // Win32 is weird in the sense that it resets the signal
        // disposition to SIG_DFL after a signal handler is
        // dispatched.  Therefore, to workaround this "feature" we
        // must re-register the <PPM_Event_Handler> with <signum>
        // explicitly.
        PPM_Sig_Handler::register_handler_i (signum,
                                             eh);
   }
 }

// class PPM_Sig_Handlers_Set
// {
// public:
//   static PPM_SIG_HANDLERS_SET *instance (int signum);
// 
// private:
//   static PPM_SIG_HANDLERS_SET *sig_handlers_[PPM_NSIG];
// };
// 
// /* static */
// PPM_SIG_HANDLERS_SET *PPM_Sig_Handlers_Set::sig_handlers_[PPM_NSIG];
// 
// /* static */
// PPM_SIG_HANDLERS_SET *
// PPM_Sig_Handlers_Set::instance (int signum)
// {
//   if (signum <= 0 || signum >= PPM_NSIG)
//     return 0; // This will cause problems...
//   else if (PPM_Sig_Handlers_Set::sig_handlers_[signum] == 0)
//     PPM_NEW_RETURN (PPM_Sig_Handlers_Set::sig_handlers_[signum],
//                     PPM_SIG_HANDLERS_SET,
//                     0);
//   return PPM_Sig_Handlers_Set::sig_handlers_[signum];
// }
