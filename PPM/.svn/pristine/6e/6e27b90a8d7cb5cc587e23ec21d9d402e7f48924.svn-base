/* -*- C++ -*- */
// Reactor.i,v 4.29 2002/08/13 15:17:14 schmidt Exp

 #include "./PPM_Reactor_Impl.h"
// #include "./PPM_Handle_Set.h"

PPM_INLINE PPM_Reactor_Impl *
PPM_Reactor::implementation (void) const
{
  return this->implementation_;
}

PPM_INLINE void
PPM_Reactor::implementation (PPM_Reactor_Impl *impl)
{
  this->implementation_ = impl;
}

// Run the event loop until the <PPM_Reactor::handle_events> method
// returns -1 or the <end_event_loop> method is invoked.
//启动侦听变化，只有handle_events返回-1或end_event_loop调用的时候才结束
PPM_INLINE int
PPM_Reactor::run_event_loop (void)
{
  PPM_TRACE ("PPM_Reactor::run_event_loop");
//   PPM_Reactor *r = PPM_Reactor::instance ();
// 
//   if (r == 0)
//     return -1;
//   return r->run_reactor_event_loop ();

  return run_reactor_event_loop ();
}

PPM_INLINE int
PPM_Reactor::end_event_loop (void)
{
  PPM_TRACE ("PPM_Reactor::end_event_loop");
//  PPM_Reactor::instance ()->end_reactor_event_loop ();
	end_reactor_event_loop ();
  return 0;
}

PPM_INLINE int
PPM_Reactor::close (void)
{
  return this->implementation ()->close ();
}


PPM_INLINE int
PPM_Reactor::handle_events (PPM_Time_Value *max_wait_time)
{
  return this->implementation ()->handle_events (max_wait_time);
}

PPM_INLINE int
PPM_Reactor::handle_events (PPM_Time_Value &max_wait_time)
{
  return this->implementation ()->handle_events (max_wait_time);
}

PPM_INLINE int
PPM_Reactor::register_handler (PPM_Event_Handler *event_handler,
                               PPM_Reactor_Mask mask)
{
  // Remember the old reactor.
  PPM_Reactor *old_reactor = event_handler->reactor ();

  // Assign *this* <Reactor> to the <Event_Handler>.
  event_handler->reactor (this);

  int result = this->implementation ()->register_handler (event_handler,
                                                          mask);
  if (result == -1)
    // Reset the old reactor in case of failures.
    event_handler->reactor (old_reactor);

  return result;
}

PPM_INLINE int
PPM_Reactor::remove_handler (PPM_Event_Handler *event_handler,
                             PPM_Reactor_Mask mask)
{
  return this->implementation ()->remove_handler (event_handler,
                                                  mask);
}

PPM_INLINE int
PPM_Reactor::suspend_handler (PPM_Event_Handler *event_handler)
{
  return this->implementation ()->suspend_handler (event_handler);
}

PPM_INLINE int
PPM_Reactor::suspend_handlers (void)
{
  return this->implementation ()->suspend_handlers ();
}

PPM_INLINE int
PPM_Reactor::resume_handler (PPM_Event_Handler *event_handler)
{
  return this->implementation ()->resume_handler (event_handler);
}

PPM_INLINE int
PPM_Reactor::resume_handlers (void)
{
  return this->implementation ()->resume_handlers ();
}



PPM_INLINE int
PPM_Reactor::mask_ops (PPM_Event_Handler *event_handler,
                       PPM_Reactor_Mask mask,
                       int ops)
{
  return this->implementation ()->mask_ops (event_handler,
                                            mask,
                                            ops);
}

PPM_INLINE int
PPM_Reactor::ready_ops (PPM_Event_Handler *event_handler,
                        PPM_Reactor_Mask mask,
                        int ops)
{
  return this->implementation ()->ready_ops (event_handler,
                                             mask,
                                             ops);
}

PPM_INLINE size_t
PPM_Reactor::size (void) const
{
  return this->implementation ()->size ();
}



