#ifndef PPM_THREAD_H
#define PPM_THREAD_H

#if !defined (PPM_LACKS_PRAGMA_ONCE)
# pragma once
#endif /* PPM_LACKS_PRAGMA_ONCE */

#include "./PPM_OS.h"

/**
 * @class PPM_Thread
 *
 * @brief Provides a wrapper for threads.
 *
 * This class provides a common interface that is mapped onto
 * POSIX Pthreads, Solaris threads, Win32 threads, VxWorks
 * threads, or pSoS threads.  Note, however, that it is
 * generally a better idea to use the <PPM_Thread_Manager>
 * programming API rather than the <PPM_Thread> API since the
 * thread manager is more powerful.
 */
class PPM_Thread
{
public:
  /**
   * Creates a new thread having <flags> attributes and running <func>
   * with <args> (if <thread_adapter> is non-0 then <func> and <args>
   * are ignored and are obtained from <thread_adapter>).  <thr_id>
   * and <t_handle> are set to the thread's ID and handle (?),
   * respectively.  The thread runs at <priority> priority (see
   * below).
   *
   * The <flags> are a bitwise-OR of the following:
   * = BEGIN<INDENT>
   * THR_CANCEL_DISABLE, THR_CANCEL_ENABLE, THR_CANCEL_DEFERRED,
   * THR_CANCEL_ASYNCHRONOUS, THR_BOUND, THR_NEW_LWP, THR_DETACHED,
   * THR_SUSPENDED, THR_DAEMON, THR_JOINABLE, THR_SCHED_FIFO,
   * THR_SCHED_RR, THR_SCHED_DEFAULT, THR_EXPLICIT_SCHED,
   * THR_SCOPE_SYSTEM, THR_SCOPE_PROCESS
   * = END<INDENT>
   *
   * By default, or if <priority> is set to
   * PPM_DEFAULT_THREAD_PRIORITY, an "appropriate" priority value for
   * the given scheduling policy (specified in <flags}>, e.g.,
   * <THR_SCHED_DEFAULT>) is used.  This value is calculated
   * dynamically, and is the median value between the minimum and
   * maximum priority values for the given policy.  If an explicit
   * value is given, it is used.  Note that actual priority values are
   * EXTREMEMLY implementation-dependent, and are probably best
   * avoided.
   *
   * Note that <thread_adapter> is always deleted when <spawn>
   * is called, so it must be allocated with global operator new.
   */
  static int spawn (PPM_THR_VOID_FUNC func,
                    void *arg = 0,
                    long flags = THR_NEW_LWP | THR_JOINABLE,
                    PPM_thread_t *t_id = 0,
                    PPM_hthread_t *t_handle = 0,
                    long priority = PPM_DEFAULT_THREAD_PRIORITY,
                    void *stack = 0,
                    size_t stack_size = 0);


  /// Continue the execution of a previously suspended thread.
  static int resume (PPM_hthread_t);

  /// Suspend the execution of a particular thread.
  static int suspend (PPM_hthread_t);

  /// Get the priority of a particular thread.
  static int getprio (PPM_hthread_t, int &prio);

  /// Set the priority of a particular thread.
  static int setprio (PPM_hthread_t, int prio);

//   /// Send a signal to the thread.
//   static int kill (PPM_thread_t, int signum);

//   /// Yield the thread to another.
//   static void yield (void);

  /**
   * Return the unique kernel handle of the thread.  Note that on
   * Win32 this is actually a pseudohandle, which cannot be shared
   * with other processes or waited on by threads.  To locate the real
   * handle, please use the <PPM_Thread_Manager::thr_self> method.
   */
  static void self (PPM_hthread_t &t_handle);

  /// Return the unique ID of the thread.
  static PPM_thread_t self (void);

private:
  /// Ensure that we don't get instantiated.
  PPM_Thread (void);
};

#if !defined (PPM_LACKS_INLINE_FUNCTIONS)
#include "./PPM_Thread.inl"
#endif /* __PPM_INLINE__ */

#endif /* PPM_THREAD_H */
