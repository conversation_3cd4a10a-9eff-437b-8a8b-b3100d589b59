// -*- C++ -*-

//=============================================================================
/**
 *  @file    Addr.h
 *
 *  Addr.h,v 4.19 2002/04/10 18:18:16 ossama Exp
 *
 *  <AUTHOR> <<EMAIL>>
 */
//=============================================================================

#ifndef PPM_ADDR_H
#define PPM_ADDR_H

#include "PPM_MACRO.h"
/**
 * @class PPM_Addr
 *
 * @brief Defines the base class for the "address family independent"
 * address format.
 */
class PPM_Addr
{
public:
  // = Initialization and termination methods.
  /// Initializes instance variables.
  PPM_Addr (int type = -1,
            int size = -1);

  /// Destructor.
  virtual ~PPM_Addr (void);

  // = Get/set the size of the address.

  /// Return the size of the address.
  int get_size (void) const;

  /// Sets the size of the address.
  void set_size (int size);

  // = Get/set the type of the address.

  /// Get the type of the address.
  int get_type (void) const;

  /// Set the type of the address.
  void set_type (int type);

  /// Return a pointer to the address.
  virtual void *get_addr (void) const;

  /// Set a pointer to the address.
  virtual void set_addr (void *, int len);

  // = Equality/inequality tests
  /// Check for address equality.
  int operator == (const PPM_Addr &sap) const;

  /// Check for address inequality.
  int operator != (const PPM_Addr &sap) const;

  /// Initializes instance variables.
  void base_set (int type, int size);

  /// Wild-card address.
  static const PPM_Addr sap_any;

  /// Returns a hash value.  This should be overwritten by a subclass
  /// that can produce a better hash value.
  virtual unsigned long hash (void) const;

  /// Dump the state of an object.
  void dump (void) const;


protected:
  /// e.g., AF_UNIX, AF_INET, AF_SPIPE, etc.
  int addr_type_;

  /// Number of bytes in the address.
  int addr_size_;
};


#if !defined (PPM_LACKS_INLINE_FUNCTIONS)
# include "PPM_Addr.inl"
#endif


#endif /* PPM_ADDR_H */
