// Log_Record.cpp,v 4.64 2002/10/05 00:25:52 shuston Exp

#include "./PPM_Log_Record.h"
#include "./PPM_Log_Msg.h"

#if defined (PPM_LACKS_INLINE_FUNCTIONS)
# include "./PPM_Log_Record.inl"
#endif


const char *PPM_Log_Record::priority_names_[] =
{
  PPM_LIB_TEXT ("<none>"),
  PPM_LIB_TEXT ("LM_SHUTDOWN"),
  PPM_LIB_TEXT ("LM_TRACE"),
  PPM_LIB_TEXT ("LM_DEBUG"),
  PPM_LIB_TEXT ("LM_INFO"),
  PPM_LIB_TEXT ("LM_NOTICE"),
  PPM_LIB_TEXT ("LM_WARNING"),
  PPM_LIB_TEXT ("LM_STARTUP"),
  PPM_LIB_TEXT ("LM_ERROR"),
  PPM_LIB_TEXT ("LM_CRITICAL"),
  PPM_LIB_TEXT ("LM_ALERT"),
  PPM_LIB_TEXT ("LM_EMERGENCY"),
  PPM_LIB_TEXT ("LM_UNK(04000)"),
  PPM_LIB_TEXT ("LM_UNK(010000)"),
  PPM_LIB_TEXT ("LM_UNK(020000)"),
  PPM_LIB_TEXT ("LM_UNK(040000)"),
  PPM_LIB_TEXT ("LM_UNK(0100000)"),
  PPM_LIB_TEXT ("LM_UNK(0200000)"),
  PPM_LIB_TEXT ("LM_UNK(0400000)"),
  PPM_LIB_TEXT ("LM_UNK(01000000)"),
  PPM_LIB_TEXT ("LM_UNK(02000000)"),
  PPM_LIB_TEXT ("LM_UNK(04000000)"),
  PPM_LIB_TEXT ("LM_UNK(010000000)"),
  PPM_LIB_TEXT ("LM_UNK(020000000)"),
  PPM_LIB_TEXT ("LM_UNK(040000000)"),
  PPM_LIB_TEXT ("LM_UNK(0100000000)"),
  PPM_LIB_TEXT ("LM_UNK(0200000000)"),
  PPM_LIB_TEXT ("LM_UNK(0400000000)"),
  PPM_LIB_TEXT ("LM_UNK(01000000000)"),
  PPM_LIB_TEXT ("LM_UNK(02000000000)"),
  PPM_LIB_TEXT ("LM_UNK(04000000000)"),
  PPM_LIB_TEXT ("LM_UNK(010000000000)"),
  PPM_LIB_TEXT ("LM_UNK(020000000000)")
};

const char *
PPM_Log_Record::priority_name (PPM_Log_Priority p)
{
  return PPM_Log_Record::priority_names_[PPM_OS::log2 (p)];
}

void
PPM_Log_Record::priority_name (PPM_Log_Priority p,
                               const char *name)
{
  // Name must be a statically allocated string
  PPM_Log_Record::priority_names_[PPM_OS::log2 (p)] = name;
}

u_long
PPM_Log_Record::priority (void) const
{
  PPM_TRACE ("PPM_Log_Record::priority");

  // Get the priority of the <Log_Record> <type_>.  This is computed
  // as the base 2 logarithm of <type_> (which must be a power of 2,
  // as defined by the enums in <PPM_Log_Priority>).
  return PPM_OS::log2 ((u_long) this->type_);
}

void
PPM_Log_Record::priority (u_long p)
{
  PPM_TRACE ("PPM_Log_Record::priority");

  // Set the priority of the <Log_Record> <type_> (which must be a
  // power of 2, as defined by the enums in <PPM_Log_Priority>).
  this->type_ = (PPM_UINT32) p;
}

void
PPM_Log_Record::dump (void) const
{
}

void
PPM_Log_Record::msg_data (const char *data)
{
  // PPM_TRACE ("PPM_Log_Record::msg_data");
  PPM_OS::strsncpy (this->msg_data_, data,
                    (sizeof this->msg_data_ / sizeof (char)));
  this->round_up ();
}

void
PPM_Log_Record::round_up (void)
{
  // PPM_TRACE ("PPM_Log_Record::round_up");
  // Determine the length of the payload.
  size_t len = (sizeof (*this) - sizeof (this->msg_data_))
    + (sizeof (char) * ((PPM_OS::strlen (this->msg_data_) + 1)));

  // Round up to the alignment.
  len = ((len + PPM_Log_Record::ALIGN_WORDB - 1)
         & ~(PPM_Log_Record::ALIGN_WORDB - 1));
  this->length_ = PPM_static_cast (PPM_UINT32, len);
}

PPM_Log_Record::PPM_Log_Record (PPM_Log_Priority lp,
                                const PPM_Time_Value &ts,
								long p)
  : length_ (0),
    type_ (PPM_UINT32 (lp)),
    secs_ ((PPM_UINT32) ts.sec ()),
    usecs_ ((PPM_UINT32) ts.usec ()),
    pid_ (PPM_UINT32 (p))
{
  // PPM_TRACE ("PPM_Log_Record::PPM_Log_Record");
}

PPM_Log_Record::PPM_Log_Record (void)
  : length_ (0),
    type_ (0),
    secs_ (0),
    usecs_ (0),
    pid_ (0)
{
  // PPM_TRACE ("PPM_Log_Record::PPM_Log_Record");
}

int
PPM_Log_Record::format_msg (const char host_name[],
                            char *msg)
{
  /* 0123456789012345678901234     */
  /* Oct 18 14:25:36.000 1989<nul> */
//   char timestamp[26]; // Only used by VERBOSE and VERBOSE_LITE.
// 
//     {
//       time_t now = this->secs_;
//       char ctp[26]; // 26 is a magic number...
// 
//       if (PPM_OS::ctime_r (&now, ctp, sizeof ctp) == 0)
//         return -1;
// 
//       /* 01234567890123456789012345 */
//       /* Wed Oct 18 14:25:36 1989n0 */
// 
//       ctp[19] = '\0'; // NUL-terminate after the time.
//       ctp[24] = '\0'; // NUL-terminate after the date.
// 
//       PPM_OS::sprintf (timestamp,
//                        PPM_LIB_TEXT ("%s %s.%03ld"),
//                         ctp + 20,
// 						ctp + 4,
//                        ((long) this->usecs_) / 1000
//                       );
//     }

    PPM_OS::sprintf (msg,
                     PPM_LIB_TEXT ("%s"),
//                       timestamp,
//                     PPM_Log_Record::priority_name (PPM_Log_Priority (this->type_)),
                     this->msg_data_);
  return 0;
}

int
PPM_Log_Record::print (const char host_name[], FILE *fp ,CStdLog * pStdLog)
{
  char verbose_msg [MAXVERBOSELOGMSGLEN];
  int result = this->format_msg (host_name, verbose_msg);

  if (result == 0)
  {
      if (fp != 0)
      {
          int verbose_msg_len = PPM_static_cast (int, PPM_OS::strlen (verbose_msg));
		      // sprintf(verbose_msg,"%s",verbose_msg) ;
          if(NULL != pStdLog){
            pStdLog->OutPut(verbose_msg);
          }
          else{
            int fwrite_result = PPM_OS::fprintf (fp, PPM_LIB_TEXT ("%s"), verbose_msg);
            // We should have written everything
            if (fwrite_result != verbose_msg_len)
            result = -1;
            else
            PPM_OS::fflush (fp);
          }
      }
  }

  return result;
}


