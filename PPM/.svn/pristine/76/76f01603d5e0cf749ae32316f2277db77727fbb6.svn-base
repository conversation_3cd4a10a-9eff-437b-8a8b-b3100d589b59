/* -*- C++ -*- */

//=============================================================================
/**
 *  @file    Select_Reactor.h
 *
 *  Select_Reactor.h,v 4.43 2002/02/25 17:25:39 dhinton Exp
 *
 *  <AUTHOR> <<EMAIL>>
 */
//=============================================================================

#ifndef PPM_SELECT_REACTOR_H
#define PPM_SELECT_REACTOR_H
//#include "ace/pre.h"
#include "../stdclass/StdHeader.h"
#include "./PPM_Select_Reactor_T.h"

#if !defined (PPM_LACKS_PRAGMA_ONCE)
# pragma once
#endif /* PPM_LACKS_PRAGMA_ONCE */

//typedef PPM_Select_Reactor_Token_T<PPM_SELECT_TOKEN> PPM_Select_Reactor_Token;

typedef PPM_Select_Reactor_T PPM_Select_Reactor;

/**
 * @class PPM_Guard< PPM_Select_Reactor_Token_T<PPM_Noop_Token> >
 *
 * @brief Template specialization of <PPM_Guard> for the
 * <PPM_Null_Mutex>.
 *
 * This specialization is useful since it helps to speedup
 * performance of the "Null_Mutex" considerably.
 */
//class  PPM_Guard< PPM_Select_Reactor_Token_T<PPM_Noop_Token> >
//{
//public:
//  // = Initialization and termination methods.
//  PPM_Guard (PPM_Select_Reactor_Token_T<PPM_Noop_Token> &) {}
//  PPM_Guard (PPM_Select_Reactor_Token_T<PPM_Noop_Token> &, int) {}
//  ~PPM_Guard (void) {}
//
//  int acquire (void) { return 0; }
//  int tryacquire (void) { return 0; }
//  int release (void) { return 0; }
//  int locked (void) { return 1; }
//  int remove (void) { return 0; }
//  void dump (void) const {}
//
//private:
//  // = Prevent assignment and initialization.
//  PPM_UNIMPLEMENTED_FUNC (void operator= (const PPM_Guard< PPM_Select_Reactor_Token_T<PPM_Noop_Token> > &))
//  PPM_UNIMPLEMENTED_FUNC (PPM_Guard (const PPM_Guard< PPM_Select_Reactor_Token_T<PPM_Noop_Token> > &))
//};

//#include "ace/post.h"
#endif /* PPM_SELECT_REACTOR_H */
