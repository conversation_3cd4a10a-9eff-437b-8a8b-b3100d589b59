
#ifndef PPM_EVENT_HANDLER_H
#define PPM_EVENT_HANDLER_H

#if !defined (PPM_LACKS_PRAGMA_ONCE)
# pragma once
#endif /* PPM_LACKS_PRAGMA_ONCE */

#include "./PPM_OS.h"

// Forward declaration.
class PPM_Reactor;

typedef u_long PPM_Reactor_Mask;

/**
 * @class PPM_Event_Handler
 * 此类是一个事件基类框架，基于反应器的事件响应机制
 * @brief Provides an abstract interface for handling various types of
 * I/O, timer, and signal events.
 *
 * Subclasses read/write input/output on an I/O descriptor,
 * handle an exception raised on an I/O descriptor, handle a
 * timer's expiration, or handle a signal.
 */
class PPM_Event_Handler
{
public:
  enum
  {
    LO_PRIORITY = 0,
    HI_PRIORITY = 10,
    NULL_MASK = 0,

    READ_MASK = (1 << 0),
    WRITE_MASK = (1 << 1),
    EXCEPT_MASK = (1 << 2),

    ACCEPT_MASK = (1 << 3),
    CONNECT_MASK = (1 << 4),
    TIMER_MASK = (1 << 5),
    QOS_MASK = (1 << 6),
    GROUP_QOS_MASK = (1 << 7),
    SIGNAL_MASK = (1 << 8),
    ALL_EVENTS_MASK = READ_MASK |
                      WRITE_MASK |
                      EXCEPT_MASK |
                      ACCEPT_MASK |
                      CONNECT_MASK |
                      TIMER_MASK |
                      QOS_MASK |
                      GROUP_QOS_MASK |
                      SIGNAL_MASK,
    RWE_MASK = READ_MASK |
               WRITE_MASK |
               EXCEPT_MASK,
    DONT_CALL = (1 << 9)
  };

  /// Destructor is virtual to enable proper cleanup.
  virtual ~PPM_Event_Handler (void);

  /// Get the I/O handle.
  virtual PPM_HANDLE get_handle (void) const;

  /// Set the I/O handle.
  virtual void set_handle (PPM_HANDLE);

  // = Get/set priority
  /// Get the priority of the Event_Handler.
  virtual int priority (void) const;

  /// Set the priority of the Event_Handler.
  virtual void priority (int priority);

  /// Called when input events occur (e.g., connection or data).
  virtual int handle_input (PPM_HANDLE fd = PPM_INVALID_HANDLE);

  /// Called when output events are possible (e.g., when flow control
  /// abates or non-blocking connection completes).
  virtual int handle_output (PPM_HANDLE fd = PPM_INVALID_HANDLE);

  /// Called when an exceptional events occur (e.g., SIGURG).
  virtual int handle_exception (PPM_HANDLE fd = PPM_INVALID_HANDLE);

  /// Called when a <handle_*()> method returns -1 or when the
  /// <remove_handler> method is called on an <PPM_Reactor>.  The
  /// <close_mask> indicates which event has triggered the
  /// <handle_close> method callback on a particular <handle>.
  virtual int handle_close (PPM_HANDLE handle,
                            PPM_Reactor_Mask close_mask);
  /// Called when object is signaled by OS (either via UNIX signals or
  /// when a Win32 object becomes signaled).
  virtual int handle_signal (int signum, siginfo_t * = 0, ucontext_t * = 0);

  enum
    {
      /// The handler is not resumed at all. Could lead to deadlock..
      PPM_EVENT_HANDLER_NOT_RESUMED = -1,
      /// The reactor takes responsibility of resuming the handler and
      /// is the default
      PPM_REACTOR_RESUMES_HANDLER = 0,
      /// The application takes responsibility of resuming the handler
      PPM_APPLICATION_RESUMES_HANDLER
    };
  /* Called to figure out whether the handler needs to resumed by the
   * reactor or the application can take care of it. The default
   * value of 0 would be returned which would allow the reactor to
   * take care of resumption of the handler. The application can
   * return a value more than zero and decide to resume the handler
   * themseleves.
   */
  // @@ NOTE: This method is only useful for the PPM_TP_Reactor. Sad
  // that we have to have this method in a class that is supposed to
  // be used across different componets in ACE.
  virtual int resume_handler (void);

  // = Accessors to set/get the various event demultiplexors.
  /// Set the event demultiplexors.
  virtual void reactor (PPM_Reactor *reactor);

  /// Get the event demultiplexors.
  virtual PPM_Reactor *reactor (void) const;

protected:
  /// Force PPM_Event_Handler to be an abstract base class.
  PPM_Event_Handler (PPM_Reactor * = 0,
                     int priority = PPM_Event_Handler::LO_PRIORITY);

private:

  /// Priority of this Event_Handler.
  int priority_;

  /// Pointer to the various event demultiplexors.
  PPM_Reactor *reactor_;
};

#endif /* PPM_EVENT_HANDLER_H */
