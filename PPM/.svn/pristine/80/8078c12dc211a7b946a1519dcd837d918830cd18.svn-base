// SOCK_Acceptor.cpp
// SOCK_Acceptor.cpp,v 4.30 2002/03/08 23:18:09 spark Exp

#include "./PPM_SOCK_Acceptor.h"
#include "./PPM_OS_Error.h"
#include "./PPM_Public_Var.h"

// Do nothing routine for constructor.
PPM_SOCK_Acceptor::PPM_SOCK_Acceptor (void)
{
}
PPM_SOCK_Acceptor::~PPM_SOCK_Acceptor (void)
{
}

// Performs the timed accept operation.

int
PPM_SOCK_Acceptor::shared_accept_start (PPM_Time_Value *timeout,
                                        int restart,
                                        int &in_blocking_mode) const
{
  PPM_HANDLE handle = this->get_handle ();

  // Handle the case where we're doing a timed <accept>.
  if (timeout != 0)
    {
      if (PPM_OS::handle_timed_accept (handle,
                                    timeout,
                                    restart) == -1)
        return -1;
      else
        {
          in_blocking_mode = PPM_BIT_DISABLED (PPM_OS::get_flags (handle),
                                               PPM_NONBLOCK);
          // Set the handle into non-blocking mode if it's not already
          // in it.
          if (in_blocking_mode
              && PPM_OS::set_flags (handle,
                                 PPM_NONBLOCK) == -1)
            return -1;
        }
    }

  return 0;
}

int
PPM_SOCK_Acceptor::shared_accept_finish (PPM_SOCK_Stream new_stream,
                                         int in_blocking_mode,
                                         int reset_new_handle) const
{
  PPM_HANDLE new_handle = new_stream.get_handle ();

  // Check to see if we were originally in blocking mode, and if so,
  // set the <new_stream>'s handle and <this> handle to be in blocking
  // mode.
  if (in_blocking_mode)
    {
      // Save/restore errno.
      PPM_Errno_Guard error (errno);

      // Only disable PPM_NONBLOCK if we weren't in non-blocking mode
      // originally.
      PPM_OS::clr_flags (this->get_handle (),
                      PPM_NONBLOCK);
      PPM_OS::clr_flags (new_handle,
                      PPM_NONBLOCK);
    }
#ifdef WIN32
  if (reset_new_handle)
    // Reset the event association inherited by the new handle.
    ::WSAEventSelect ((SOCKET) new_handle, 0, 0);
#endif

  return new_handle == PPM_INVALID_HANDLE ? -1 : 0;
}

// General purpose routine for accepting new connections.

int
PPM_SOCK_Acceptor::accept (PPM_SOCK_Stream &new_stream,
                           PPM_Addr *remote_addr,
                           PPM_Time_Value *timeout,
                           int restart,
                           int reset_new_handle) const
{
  int in_blocking_mode = 0;
  if (this->shared_accept_start (timeout,
                                 restart,
                                 in_blocking_mode) == -1)
    return -1;
  else
    {
      // On Win32 the third parameter to <accept> must be a NULL
      // pointer if we want to ignore the client's address.
      int *len_ptr = 0;
      sockaddr *addr = 0;
      int len = 0;

      if (remote_addr != 0)
        {
          len = remote_addr->get_size ();
          len_ptr = &len;
          addr = (sockaddr *) remote_addr->get_addr ();
        }

      do
        new_stream.set_handle (PPM_OS::accept (this->get_handle (),
                                               addr,
                                               len_ptr));
      while (new_stream.get_handle () == PPM_INVALID_HANDLE
             && restart != 0
             && errno == EINTR
             && timeout == 0);

      // Reset the size of the addr, so the proper UNIX/IPv4/IPv6 family
      // is known.
      if (new_stream.get_handle () != PPM_INVALID_HANDLE
          && remote_addr != 0)
        {
          remote_addr->set_size (len);
          remote_addr->set_type (addr->sa_family);
        }
    }

  return this->shared_accept_finish (new_stream,
                                     in_blocking_mode,
                                     reset_new_handle);
}


void
PPM_SOCK_Acceptor::dump (void) const
{
}

int
PPM_SOCK_Acceptor::shared_open (const PPM_Addr &local_sap,
                                int protocol_family,
                                int backlog)
{
  int error = 0;

  if (protocol_family == PF_INET)
  {
    sockaddr_in local_inet_addr;
    PPM_OS::memset (PPM_reinterpret_cast (void *,
                                          &local_inet_addr),
                    0,
                    sizeof local_inet_addr);

    if (local_sap == PPM_Addr::sap_any)
    {
      local_inet_addr.sin_port = 0;
    }
    else
      local_inet_addr = *PPM_reinterpret_cast (sockaddr_in *,
                                                local_sap.get_addr ());
    if (local_inet_addr.sin_port == 0)
    {
      if (PPM_OS::bind_port (this->get_handle ()) == -1)
        error = 1;
    }
    else if (PPM_OS::bind (this->get_handle (),
                            PPM_reinterpret_cast (sockaddr *,
                                                  &local_inet_addr),
                            sizeof local_inet_addr) == -1)
              PPM_SOCKLOG((LM_ERROR,"Bind port %d error\n",local_inet_addr.sin_port));
  }
  else if (PPM_OS::bind (this->get_handle (),
                         (sockaddr *) local_sap.get_addr (),
                         local_sap.get_size ()) == -1)
    error = 1;

  if (error != 0
      || PPM_OS::listen (this->get_handle (),
                         backlog) == -1)
    {
      error = 1;
      this->close ();
    }
  return error ? -1 : 0;
}

// General purpose routine for performing server PPM_SOCK creation.

int
PPM_SOCK_Acceptor::open (const PPM_Addr &local_sap,
                         int reuse_addr,
                         int protocol_family,
                         int backlog,
                         int protocol)
{
  if (local_sap != PPM_Addr::sap_any)
    protocol_family = local_sap.get_type ();
  else if (protocol_family == PF_UNSPEC)
  {
    protocol_family = PF_INET;
  }

  if (PPM_SOCK::open (SOCK_STREAM,
                      protocol_family,
                      protocol,
                      reuse_addr) == -1)
    return -1;
  else
    return this->shared_open (local_sap,
                              protocol_family,
                              backlog);
}

// General purpose routine for performing server PPM_SOCK creation.

PPM_SOCK_Acceptor::PPM_SOCK_Acceptor (const PPM_Addr &local_sap,
                                      int reuse_addr,
                                      int protocol_family,
                                      int backlog,
                                      int protocol)
{
  if (this->open (local_sap,
                  reuse_addr,
                  protocol_family,
                  backlog,
                  protocol) == -1)
  {
    PPM_TRACE ((LM_ERROR,
                PPM_LIB_TEXT ("%p\n"),
                PPM_LIB_TEXT ("PPM_SOCK_Acceptor")))
  }
}

int
PPM_SOCK_Acceptor::close (void)
{
  return PPM_SOCK::close ();
}
