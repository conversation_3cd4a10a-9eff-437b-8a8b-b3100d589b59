// IPC_SAP.cpp,v 4.31 2000/12/05 08:09:54 irfan Exp

#include "./PPM_IPC_SAP.h"
#include "./PPM_OS.h"

#if defined (PPM_LACKS_INLINE_FUNCTIONS)
#include "PPM_IPC_SAP.inl"
#endif /* PPM_LACKS_INLINE_FUNCTIONS */

void
PPM_IPC_SAP::dump (void) const
{
  PPM_TRACE ("PPM_IPC_SAP::dump");

}

// This is the do-nothing constructor.  It does not perform a
// PPM_OS::socket system call.

PPM_IPC_SAP::PPM_IPC_SAP (void)
  : handle_ (PPM_INVALID_HANDLE)
{
  // PPM_TRACE ("PPM_IPC_SAP::PPM_IPC_SAP");
}

int
PPM_IPC_SAP::enable (int value) const
{
  PPM_TRACE ("PPM_IPC_SAP::enable");
#ifdef WIN32
 switch (value)
   {
    case PPM_NONBLOCK:
      {
        // nonblocking argument (1)
        // blocking:            (0)
        u_long nonblock = 1;
        return PPM_OS::ioctl (this->handle_,
                              FIONBIO,
                              &nonblock);
      }
    default:
 		return -1;
//    PPM_NOTSUP_RETURN (-1);
   }
  /* NOTREACHED */
  return 0;
#else
  switch (value)
    {
    case PPM_NONBLOCK:
      if (PPM_Flag_Manip::set_flags (this->handle_,
                                     PPM_NONBLOCK) == PPM_INVALID_HANDLE)
        return -1;
      break;
    default:
      return -1;
    }
  return 0;
#endif
}

int
PPM_IPC_SAP::disable (int value) const
{
  PPM_TRACE ("PPM_IPC_SAP::disable");
#ifdef WIN32
  switch (value)
    {
    case PPM_NONBLOCK:
      // nonblocking argument (1)
      // blocking:            (0)
      {
        u_long nonblock = 0;
        return PPM_OS::ioctl (this->handle_,
                              FIONBIO,
                              &nonblock);
      }
    default:
		return -1;
 //      PPM_NOTSUP_RETURN (-1);
    }
  /* NOTREACHED */
  return 0;
#else
  switch (value)
    {
    case PPM_NONBLOCK:
      if (PPM_Flag_Manip::clr_flags (this->handle_,
                                     PPM_NONBLOCK) == -1)
        return -1;
      break;
    default:
      return -1;
    }
  return 0;
#endif
}
