#ifndef _PPM_SOCK_BUFFER_H_
#define _PPM_SOCK_BUFFER_H_

#include "StdBuffer.h"
/*
此类包装了socket接收和发送使用的buffer，包括内存和文件
*/
class PPM_SOCK_Buffer
{
public:
	PPM_SOCK_Buffer(char* strip,u_short portno,int ReadSize,BYTE RNodeType,int WriteSize, BYTE WNodeType, 
		           BYTE exclusive_flag,BYTE DealDataFlag);
	~PPM_SOCK_Buffer();

	//初始化缓冲ReadSize:读缓冲大小；WriteSize：写缓冲大小；
	//exclusive_flag：进程中对于ip，端口是否唯一；
	//RNodeType:收缓冲类型，是否需要文件缓冲
	//WNodeType:发缓冲类型,是否需要文件缓冲
	//DealDataFlag:标示当socket关闭时，是否需要处理完接收的和发送的数据
	BOOL Init(int ReadSize,BYTE RNodeType,int WriteSize, BYTE WNodeType, BYTE exclusive_flag,BYTE DealDataFlag);

	//从收缓冲区中读取指定长度的数据，返回读取的长度,应用层使用
  int ReadData(BYTE ** const pBuf, int& nSize);

	//向发缓冲区中写入指定长度的数据,返回写入的长度，应用层使用
	int WriteData(const BYTE * const pBuf, int nSize);

	//从发缓冲区中取数据,用于socket发送,返回取到的数据长度
	int GetSendData(BYTE** pBuf, int& nCount);

	//向收缓冲区中写数据，给应用程读数据使用,返回写入数据的长度
	int PutRecvData(const BYTE* const pBuf, const int nCount);

	//获取数据处理标示,是否需要将收发缓冲中的数据处理完1表示收处理,2表示发处理,3表示收发都处理,0表示都不处理
	BYTE GetDealDataFlag(){
		return m_DealDataFlag;
	};
	BYTE GetExclusiveFlag(){
		return m_exclusive_flag;
	};
	    //ip地址,即对端ip
	char m_pipaddress[46]; //此处由原来的20改为46，是为了兼容IPv6的地址长度 [fhx]
	//端口号,服务端的端口号
	u_short m_portNo;

	BYTE m_RecvMode;
	BYTE m_SendMode;

	int GetBusySize(void)
	{
		return ((NULL == m_precvProxy)?0:m_precvProxy->GetBusySize());
	};
private:
	//缓冲唯一性标识,是否缓冲区是唯一的
	BYTE m_exclusive_flag;
	//是否需要将收发缓冲中的数据处理完1表示收处理，2表示发处理3表示收发都处理
	BYTE m_DealDataFlag;
	// 接收缓存代理
	CStdBuffer* m_precvProxy;
	// 发送缓存代理
	CStdBuffer* m_psendProxy;
};

#endif