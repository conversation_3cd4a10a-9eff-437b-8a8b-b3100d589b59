// Default dtor.
PPM_INLINE
PPM_INET_Addr::~PPM_INET_Addr (void)
{
}

PPM_INLINE void
PPM_INET_Addr::reset (void)
{
  PPM_OS::memset(&this->inet_addr_, 0, sizeof(this->inet_addr_));
  PPM_OS::memset(&this->inet6_addr_, 0, sizeof(this->inet6_addr_));

  if (this->get_type() == AF_INET) {
    this->inet_addr_.in4_.sin_family = AF_INET;
  }

}

PPM_INLINE int
PPM_INET_Addr::determine_type (void) const
{
  return AF_INET;
}

PPM_INLINE void *
PPM_INET_Addr::ip_addr_pointer (void) const
{
  return (void*)&this->inet_addr_.in4_.sin_addr;
}

PPM_INLINE int
PPM_INET_Addr::ip_addr_size (void) const
{
  // These _UNICOS changes were picked up from pre-IPv6 code in
  // get_host_name_i... the IPv6 section above may need something
  // similar, so keep an eye out for it.
  return PPM_static_cast (int, sizeof this->inet_addr_.in4_.sin_addr.s_addr);
}

// Return the port number, converting it into host byte order...

PPM_INLINE u_short
PPM_INET_Addr::get_port_number (void) const
{
	if (this->get_type() == AF_INET6)
	{
		return ntohs (this->inet6_addr_.in6_.sin6_port);
	}
	else
	{
		return ntohs (this->inet_addr_.in4_.sin_port);
	}
}

// Return the address.

PPM_INLINE void *
PPM_INET_Addr::get_addr (void) const
{
	if (this->get_type() == AF_INET6)
	{
		return (void*)&this->inet6_addr_;
	}
	else
	{
		return (void*)&this->inet_addr_;
	}
}

PPM_INLINE int
PPM_INET_Addr::get_addr_size (void) const
{
  return sizeof this->inet_addr_.in4_;
}


PPM_INLINE u_long
PPM_INET_Addr::hash (void) const
{
  return this->get_ip_address () + this->get_port_number ();
}

PPM_INLINE int
PPM_INET_Addr::operator < (const PPM_INET_Addr &rhs) const
{
  return this->get_ip_address () < rhs.get_ip_address ()
    || (this->get_ip_address () == rhs.get_ip_address ()
        && this->get_port_number () < rhs.get_port_number ());
}


