// Select_Reactor_T.cpp,v 4.61 2002/11/18 17:33:40 bala Exp

#ifndef PPM_SELECT_REACTOR_T_C
#define PPM_SELECT_REACTOR_T_C

#include "./PPM_Select_Reactor_T.h"
#include "./StdHeader.h"

//#include "ace/ACE.h"
#include "./PPM_Log_Msg.h"
#include "./PPM_Thread.h"

// @@ The latest version of SunCC can't grok the code if we put inline
// function here.  Therefore, we temporarily disable the code here.
// We shall turn this back on once we know the problem gets fixed.
#if 1 // !defined (__PPM_INLINE__)
#include "./PPM_Select_Reactor_T.inl"
#endif /* __PPM_INLINE__ */

//PPM_RCSID(ace, Select_Reactor_T, "Select_Reactor_T.cpp,v 4.61 2002/11/18 17:33:40 bala Exp")
//
//  PPM_ALLOC_HOOK_DEFINE(PPM_Select_Reactor_T)

#if defined (WIN32)
#define PPM_SELECT_REACTOR_HANDLE(H) (this->event_handlers_[(H)].handle_)
#define PPM_SELECT_REACTOR_EVENT_HANDLER(THIS,H) ((THIS)->event_handlers_[(H)].event_handler_)
#else
#define PPM_SELECT_REACTOR_HANDLE(H) (H)
#define PPM_SELECT_REACTOR_EVENT_HANDLER(THIS,H) ((THIS)->event_handlers_[(H)])
#endif /* WIN32 */

int
PPM_Select_Reactor_T::any_ready
  (PPM_Select_Reactor_Handle_Set &wait_set)
{
  PPM_TRACE ("PPM_Select_Reactor_T::any_ready");

  if (this->mask_signals_)
    {
#if !defined (WIN32)
      // Make this call signal safe.
      PPM_Sig_Guard sb;
#endif /* WIN32 */

      return this->any_ready_i (wait_set);
    }
  return this->any_ready_i (wait_set);
}

int
PPM_Select_Reactor_T::any_ready_i
  (PPM_Select_Reactor_Handle_Set &wait_set)
{
  int number_ready = this->ready_set_.rd_mask_.num_set ()
    + this->ready_set_.wr_mask_.num_set ()
    + this->ready_set_.ex_mask_.num_set ();

  if (number_ready > 0 && &wait_set != &(this->ready_set_))
    {
      wait_set.rd_mask_ = this->ready_set_.rd_mask_;
      wait_set.wr_mask_ = this->ready_set_.wr_mask_;
      wait_set.ex_mask_ = this->ready_set_.ex_mask_;

      this->ready_set_.rd_mask_.reset ();
      this->ready_set_.wr_mask_.reset ();
      this->ready_set_.ex_mask_.reset ();
    }

  return number_ready;
}

 int
PPM_Select_Reactor_T::handler_i (int signum,
                                                           PPM_Event_Handler **eh)
{
  PPM_TRACE ("PPM_Select_Reactor_T::handler_i");
  PPM_Event_Handler *handler = this->signal_handler_->handler (signum);

  if (handler == 0)
    return -1;
  else if (eh != 0)
    *eh = handler;
  return 0;
}

 int
PPM_Select_Reactor_T::initialized (void)
{
  PPM_TRACE ("PPM_Select_Reactor_T::initialized");
  PPM_MT (PPM_GUARD_RETURN (PPM_SELECT_REACTOR_TOKEN, PPM_mon, this->token_, 0));
  return this->initialized_;
}

 int
PPM_Select_Reactor_T::owner (PPM_thread_t tid,
                                                       PPM_thread_t *o_id)
{
  PPM_TRACE ("PPM_Select_Reactor_T::owner");
  PPM_MT (PPM_GUARD_RETURN (PPM_SELECT_REACTOR_TOKEN, PPM_mon, this->token_, -1));

  if (o_id)
    *o_id = this->owner_;

  this->owner_ = tid;

  return 0;
}

 int
PPM_Select_Reactor_T::owner (PPM_thread_t *t_id)
{
  PPM_TRACE ("PPM_Select_Reactor_T::owner");
  PPM_MT (PPM_GUARD_RETURN (PPM_SELECT_REACTOR_TOKEN, PPM_mon, this->token_, -1));
  *t_id = this->owner_;
  return 0;
}

 int
PPM_Select_Reactor_T::restart (void)
{
  PPM_MT (PPM_GUARD_RETURN (PPM_SELECT_REACTOR_TOKEN, PPM_mon, this->token_, -1));
  return this->restart_;
}

 int
PPM_Select_Reactor_T::restart (int r)
{
  PPM_MT (PPM_GUARD_RETURN (PPM_SELECT_REACTOR_TOKEN, PPM_mon, this->token_, -1));
  int current_value = this->restart_;
  this->restart_ = r;
  return current_value;
}

 void
PPM_Select_Reactor_T::requeue_position (int rp)
{
  PPM_TRACE ("PPM_Select_Reactor_T::requeue_position");
  PPM_MT (PPM_GUARD (PPM_SELECT_REACTOR_TOKEN, PPM_mon, this->token_));
#if defined (WIN32)
  PPM_UNUSED_ARG (rp);
  // Must always requeue ourselves "next" on Win32.
  this->requeue_position_ = 0;
#else
  this->requeue_position_ = rp;
#endif /* WIN32 */
}

 int
PPM_Select_Reactor_T::requeue_position (void)
{
  PPM_TRACE ("PPM_Select_Reactor_T::requeue_position");
  PPM_MT (PPM_GUARD_RETURN (PPM_SELECT_REACTOR_TOKEN, PPM_mon, this->token_, -1));
  return this->requeue_position_;
}

// void
//PPM_Select_Reactor_T::max_notify_iterations (int iterations)
//{
//  PPM_TRACE ("PPM_Select_Reactor_T::max_notify_iterations");
//  PPM_MT (PPM_GUARD (PPM_SELECT_REACTOR_TOKEN, PPM_mon, this->token_));
//
//  this->notify_handler_->max_notify_iterations (iterations);
//}
//
// int
//PPM_Select_Reactor_T::max_notify_iterations (void)
//{
//  PPM_TRACE ("PPM_Select_Reactor_T::max_notify_iterations");
//  PPM_MT (PPM_GUARD_RETURN (PPM_SELECT_REACTOR_TOKEN, PPM_mon, this->token_, -1));
//  return this->notify_handler_->max_notify_iterations ();
//}

// Enqueue ourselves into the list of waiting threads.
 void
PPM_Select_Reactor_T::renew (void)
{
  PPM_TRACE ("PPM_Select_Reactor_T::renew");
#if defined (PPM_MT_SAFE) && (PPM_MT_SAFE != 0)
  if (this->supress_notify_renew () == 0)
    this->token_.renew (this->requeue_position_);
#endif /* defined (PPM_MT_SAFE) && (PPM_MT_SAFE != 0) */
}

//template <class PPM_SELECT_REACTOR_MUTEX> void
//PPM_Select_Reactor_Token_T<PPM_SELECT_REACTOR_MUTEX>::dump (void) const
//{
////  PPM_TRACE ("PPM_Select_Reactor_Token_T::dump");
////
////  PPM_DEBUG ((LM_DEBUG, PPM_BEGIN_DUMP, this));
////  PPM_DEBUG ((LM_DEBUG, PPM_LIB_TEXT ("\n")));
////  PPM_DEBUG ((LM_DEBUG, PPM_END_DUMP));
//}
//
//template <class PPM_SELECT_REACTOR_MUTEX>
//PPM_Select_Reactor_Token_T<PPM_SELECT_REACTOR_MUTEX>::PPM_Select_Reactor_Token_T
//  (PPM_Select_Reactor_Impl &r,
//   int s_queue)
//    : select_reactor_ (&r)
//{
//  PPM_TRACE ("PPM_Select_Reactor_Token_T::PPM_Select_Reactor_Token");
//
//  this->queueing_strategy (s_queue);
//}
//
//template <class PPM_SELECT_REACTOR_MUTEX>
//PPM_Select_Reactor_Token_T<PPM_SELECT_REACTOR_MUTEX>::PPM_Select_Reactor_Token_T (int s_queue)
//  : select_reactor_ (0)
//{
//  PPM_TRACE ("PPM_Select_Reactor_Token_T::PPM_Select_Reactor_Token");
//
//  this->queueing_strategy (s_queue);
//}
//
//template <class PPM_SELECT_REACTOR_MUTEX>
//PPM_Select_Reactor_Token_T<PPM_SELECT_REACTOR_MUTEX>::~PPM_Select_Reactor_Token_T (void)
//{
//  PPM_TRACE ("PPM_Select_Reactor_Token_T::~PPM_Select_Reactor_Token_T");
//}
//
//template <class PPM_SELECT_REACTOR_MUTEX> PPM_Select_Reactor_Impl &
//PPM_Select_Reactor_Token_T<PPM_SELECT_REACTOR_MUTEX>::select_reactor (void)
//{
//  return *this->select_reactor_;
//}
//
//template <class PPM_SELECT_REACTOR_MUTEX> void
//PPM_Select_Reactor_Token_T<PPM_SELECT_REACTOR_MUTEX>::select_reactor
//  (PPM_Select_Reactor_Impl &select_reactor)
//{
//  this->select_reactor_ = &select_reactor;
//}
//
//// Used to wakeup the Select_Reactor.
//
//template <class PPM_SELECT_REACTOR_MUTEX> void
//PPM_Select_Reactor_Token_T<PPM_SELECT_REACTOR_MUTEX>::sleep_hook (void)
//{
//  PPM_TRACE ("PPM_Select_Reactor_Token_T::sleep_hook");
//  if (this->select_reactor_->notify () == -1)
//    PPM_ERROR ((LM_ERROR,
//                PPM_LIB_TEXT ("%p\n"),
//                PPM_LIB_TEXT ("sleep_hook failed")));
//}

// int
//PPM_Select_Reactor_T::notify (PPM_Event_Handler *eh,
//                                                        PPM_Reactor_Mask mask,
//                                                        PPM_Time_Value *timeout)
//{
//  PPM_TRACE ("PPM_Select_Reactor_T::notify");
//
//  int n = 0;
//
//  // Pass over both the Event_Handler *and* the mask to allow the
//  // caller to dictate which Event_Handler method the receiver
//  // invokes.  Note that this call can timeout.
//
//  n = this->notify_handler_->notify (eh, mask, timeout);
//  return n == -1 ? -1 : 0;
//}

 int
PPM_Select_Reactor_T::resume_handler (PPM_HANDLE handle)
{
  PPM_TRACE ("PPM_Select_Reactor_T::resume_handler");
  PPM_MT (PPM_GUARD_RETURN (PPM_SELECT_REACTOR_TOKEN, PPM_mon, this->token_, -1));
  return this->resume_i (handle);
}

 int
PPM_Select_Reactor_T::suspend_handler (PPM_HANDLE handle)
{
  PPM_TRACE ("PPM_Select_Reactor_T::suspend_handler");
  PPM_MT (PPM_GUARD_RETURN (PPM_SELECT_REACTOR_TOKEN, PPM_mon, this->token_, -1));
  return this->suspend_i (handle);
}

 int
PPM_Select_Reactor_T::suspend_handlers (void)
{
  PPM_TRACE ("PPM_Select_Reactor_T::suspend_handlers");
  PPM_MT (PPM_GUARD_RETURN (PPM_SELECT_REACTOR_TOKEN, PPM_mon, this->token_, -1));

  PPM_Event_Handler *eh = 0;

  for (PPM_Select_Reactor_Handler_Repository_Iterator iter (&this->handler_rep_);
       iter.next (eh) != 0;
       iter.advance ())
    this->suspend_i (eh->get_handle ());

  return 0;
}

 int
PPM_Select_Reactor_T::resume_handlers (void)
{
  PPM_TRACE ("PPM_Select_Reactor_T::resume_handlers");
  PPM_MT (PPM_GUARD_RETURN (PPM_SELECT_REACTOR_TOKEN, PPM_mon, this->token_, -1));

  PPM_Event_Handler *eh = 0;

  for (PPM_Select_Reactor_Handler_Repository_Iterator iter (&this->handler_rep_);
       iter.next (eh) != 0;
       iter.advance ())
    this->resume_i (eh->get_handle ());

  return 0;
}

 int
PPM_Select_Reactor_T::register_handler
  (PPM_Event_Handler *handler,
   PPM_Reactor_Mask mask)
{
  PPM_TRACE ("PPM_Select_Reactor_T::register_handler");
  PPM_MT (PPM_GUARD_RETURN (PPM_SELECT_REACTOR_TOKEN, PPM_mon, this->token_, -1));
  return this->register_handler_i (handler->get_handle (), handler, mask);
}

 int
PPM_Select_Reactor_T::register_handler
  (PPM_HANDLE handle,
   PPM_Event_Handler *handler,
   PPM_Reactor_Mask mask)
{
  PPM_TRACE ("PPM_Select_Reactor_T::register_handler");
  PPM_MT (PPM_GUARD_RETURN (PPM_SELECT_REACTOR_TOKEN, PPM_mon, this->token_, -1));
  return this->register_handler_i (handle, handler, mask);
}

 int
PPM_Select_Reactor_T::register_handler
  (const PPM_Handle_Set &handles,
   PPM_Event_Handler *handler,
   PPM_Reactor_Mask mask)
{
  PPM_TRACE ("PPM_Select_Reactor_T::register_handler");
  PPM_MT (PPM_GUARD_RETURN (PPM_SELECT_REACTOR_TOKEN, PPM_mon, this->token_, -1));
  return this->register_handler_i (handles, handler, mask);
}

 int
PPM_Select_Reactor_T::handler
  (PPM_HANDLE handle,
   PPM_Reactor_Mask mask,
   PPM_Event_Handler **handler)
{
  PPM_TRACE ("PPM_Select_Reactor_T::handler");
  PPM_MT (PPM_GUARD_RETURN (PPM_SELECT_REACTOR_TOKEN, PPM_mon, this->token_, -1));
  return this->handler_i (handle, mask, handler);
}

 int
PPM_Select_Reactor_T::remove_handler
  (const PPM_Handle_Set &handles,
   PPM_Reactor_Mask mask)
{
  PPM_TRACE ("PPM_Select_Reactor_T::remove_handler");
  PPM_MT (PPM_GUARD_RETURN (PPM_SELECT_REACTOR_TOKEN, PPM_mon, this->token_, -1));
  return this->remove_handler_i (handles, mask);
}

 int
PPM_Select_Reactor_T::remove_handler
  (PPM_Event_Handler *handler,
   PPM_Reactor_Mask mask)
{
  PPM_TRACE ("PPM_Select_Reactor_T::remove_handler");
  PPM_MT (PPM_GUARD_RETURN (PPM_SELECT_REACTOR_TOKEN, PPM_mon, this->token_, -1));
  return this->remove_handler_i (handler->get_handle (), mask);
}

 int
PPM_Select_Reactor_T::remove_handler
  (PPM_HANDLE handle,
   PPM_Reactor_Mask mask)
{
  PPM_TRACE ("PPM_Select_Reactor_T::remove_handler");
  PPM_MT (PPM_GUARD_RETURN (PPM_SELECT_REACTOR_TOKEN, PPM_mon, this->token_, -1));
  return this->remove_handler_i (handle, mask);
}

// Performs operations on the "ready" bits.

 int
PPM_Select_Reactor_T::ready_ops
  (PPM_HANDLE handle,
   PPM_Reactor_Mask mask,
   int ops)
{
  PPM_TRACE ("PPM_Select_Reactor_T::ready_ops");
  PPM_MT (PPM_GUARD_RETURN (PPM_SELECT_REACTOR_TOKEN, PPM_mon, this->token_, -1));
  return this->bit_ops (handle,
                        mask,
                        this->ready_set_,
                        ops);
}

 int
PPM_Select_Reactor_T::open
  (size_t size,
   int restart,
   PPM_Sig_Handler *sh)
{
  PPM_TRACE ("PPM_Select_Reactor_T::open");
  PPM_MT (PPM_GUARD_RETURN (PPM_SELECT_REACTOR_TOKEN, PPM_mon, this->token_, -1));

  // Can't initialize ourselves more than once.
  if (this->initialized_ > 0)
    return -1;

  this->owner_ = PPM_Thread::self ();
  this->restart_ = restart;
  this->signal_handler_ = sh;
//  this->timer_queue_ = tq;
//  this->notify_handler_ = notify;

  int result = 0;

  // Allows the signal handler to be overridden.
  if (this->signal_handler_ == 0)
    {
      PPM_NEW_RETURN (this->signal_handler_,
                      PPM_Sig_Handler,
                      -1);

      if (this->signal_handler_ == 0)
        result = -1;
      else
        this->delete_signal_handler_ = 1;
    }

//  // Allows the timer queue to be overridden.
//  if (result != -1 && this->timer_queue_ == 0)
//    {
//      PPM_NEW_RETURN (this->timer_queue_,
//                      PPM_Timer_Heap,
//                      -1);
//
//      if (this->timer_queue_ == 0)
//        result = -1;
//      else
//        this->delete_timer_queue_ = 1;
//    }

//  // Allows the Notify_Handler to be overridden.
//  if (result != -1 && this->notify_handler_ == 0)
//    {
//      PPM_NEW_RETURN (this->notify_handler_,
//                      PPM_Select_Reactor_Notify,
//                      -1);
//
//      if (this->notify_handler_ == 0)
//        result = -1;
//      else
//        this->delete_notify_handler_ = 1;
//    }
//
  if (result != -1 && this->handler_rep_.open (size) == -1)
    result = -1;
//  else if (this->notify_handler_->open (this,
//                                        0,
//                                        disable_notify_pipe) == -1)
//    result = -1;

  if (result != -1)
    // We're all set to go.
    this->initialized_ = 1;
  else
    // This will close down all the allocated resources properly.
    this->close ();

  return result;
}

 int
PPM_Select_Reactor_T::set_sig_handler(PPM_Sig_Handler *signal_handler)
{
  if (this->signal_handler_ != 0 && this->delete_signal_handler_ != 0)
    delete this->signal_handler_;
  this->signal_handler_ = signal_handler;
  this->delete_signal_handler_ = 0;
  return 0;
}
//
// PPM_Timer_Queue *
//PPM_Select_Reactor_T::timer_queue (void) const
//{
//  return this->timer_queue_;
//}
//
// int
//PPM_Select_Reactor_T::timer_queue
//  (PPM_Timer_Queue *tq)
//{
//  if (this->timer_queue_ != 0 && this->delete_timer_queue_ != 0)
//    delete this->timer_queue_;
//  this->timer_queue_ = tq;
//  this->delete_timer_queue_ = 0;
//  return 0;
//}
//
// int
//PPM_Select_Reactor_T::set_timer_queue
//  (PPM_Timer_Queue *tq)
//{
//  return this->timer_queue (tq);
//}


PPM_Select_Reactor_T::PPM_Select_Reactor_T
  (PPM_Sig_Handler *sh,
   int mask_signals,
   int s_queue)
    : deactivated_ (0),
      mask_signals_ (mask_signals)
{
  PPM_TRACE ("PPM_Select_Reactor_T::PPM_Select_Reactor_T");

  // First try to open the Reactor with the hard-coded default.
  if (this->open (PPM_Select_Reactor_T::DEFAULT_SIZE,
                  0,
                  sh) == -1)
    {
      // The hard-coded default Reactor size failed, so attempt to
      // determine the size at run-time by checking the process file
      // descriptor limit on platforms that support this feature.

      // There is no need to deallocate resources from previous open()
      // call since the open() method deallocates any resources prior
      // to exiting if an error was encountered.

      // Set the default reactor size to be the current limit on the
      // number of file descriptors available to the process.  This
      // size is not necessarily the maximum limit.
      if (this->open (PPM_OS::max_handles (),
                     0,
                     sh) == -1)
        PPM_ERROR ((LM_ERROR,
                    PPM_LIB_TEXT ("%p\n"),
                    PPM_LIB_TEXT ("PPM_Select_Reactor_T::open ")
                    PPM_LIB_TEXT ("failed inside ")
                    PPM_LIB_TEXT ("PPM_Select_Reactor_T::CTOR")));
    }
}

// Initialize PPM_Select_Reactor_T.


PPM_Select_Reactor_T::PPM_Select_Reactor_T
  (size_t size,
   int rs,
   PPM_Sig_Handler *sh,
   int mask_signals,
   int s_queue)
//    : token_ (*this, s_queue),
//      lock_adapter_ (token_),
	: deactivated_ (0),
      mask_signals_ (mask_signals)
{
  PPM_TRACE ("PPM_Select_Reactor_T::PPM_Select_Reactor_T");

  if (this->open (size,
                  rs,
                  sh) == -1)
    PPM_ERROR ((LM_ERROR,
                PPM_LIB_TEXT ("%p\n"),
                PPM_LIB_TEXT ("PPM_Select_Reactor_T::open ")
                PPM_LIB_TEXT ("failed inside PPM_Select_Reactor_T::CTOR")));
}

// Close down the PPM_Select_Reactor_T instance, detaching any
// remaining Event_Handers.  This had better be called from the main
// event loop thread...

 int
PPM_Select_Reactor_T::close (void)
{
  PPM_TRACE ("PPM_Select_Reactor_T::close");
  PPM_MT (PPM_GUARD_RETURN (PPM_SELECT_REACTOR_TOKEN, PPM_mon, this->token_, -1));

  if (this->delete_signal_handler_)
    {
      delete this->signal_handler_;
      this->signal_handler_ = 0;
      this->delete_signal_handler_ = 0;
    }

  this->handler_rep_.close ();

//  if (this->delete_timer_queue_)
//    {
//      delete this->timer_queue_;
//      this->timer_queue_ = 0;
//      this->delete_timer_queue_ = 0;
//    }
//
//  if (this->notify_handler_ != 0)
//    this->notify_handler_->close ();
//
//  if (this->delete_notify_handler_)
//    {
//      delete this->notify_handler_;
//      this->notify_handler_ = 0;
//      this->delete_notify_handler_ = 0;
//    }

  this->initialized_ = 0;

  return 0;
}

 int
PPM_Select_Reactor_T::current_info
  (PPM_HANDLE, size_t &)
{
  return -1;
}


PPM_Select_Reactor_T::~PPM_Select_Reactor_T (void)
{
  PPM_TRACE ("PPM_Select_Reactor_T::~PPM_Select_Reactor_T");
  this->close ();
}

 int
PPM_Select_Reactor_T::remove_handler_i
  (const PPM_Handle_Set &handles,
   PPM_Reactor_Mask mask)
{
  PPM_TRACE ("PPM_Select_Reactor_T::remove_handler_i");
  PPM_HANDLE h;

  PPM_Handle_Set_Iterator handle_iter (handles);

  while ((h = handle_iter ()) != PPM_INVALID_HANDLE)
    if (this->remove_handler_i (h, mask) == -1)
      return -1;

  return 0;
}

 int
PPM_Select_Reactor_T::register_handler_i
  (const PPM_Handle_Set &handles,
   PPM_Event_Handler *handler,
   PPM_Reactor_Mask mask)
{
  PPM_TRACE ("PPM_Select_Reactor_T::register_handler_i");
  PPM_HANDLE h;

  PPM_Handle_Set_Iterator handle_iter (handles);
  while ((h = handle_iter ()) != PPM_INVALID_HANDLE)
    if (this->register_handler_i (h, handler, mask) == -1)
      return -1;

  return 0;
}

 int
PPM_Select_Reactor_T::register_handler
  (const PPM_Sig_Set &sigset,
   PPM_Event_Handler *new_sh,
   PPM_Sig_Action *new_disp)
{
  PPM_TRACE ("PPM_Select_Reactor_T::register_handler");

  int result = 0;

#if (PPM_NSIG > 0)  &&  !defined (CHORUS)
  for (int s = 1; s < PPM_NSIG; s++)
    if (sigset.is_member (s)
        && this->signal_handler_->register_handler (s,
                                                    new_sh,
                                                    new_disp) == -1)
      result = -1;
#else  /* PPM_NSIG <= 0  ||  CHORUS */
  PPM_UNUSED_ARG (sigset);
  PPM_UNUSED_ARG (new_sh);
  PPM_UNUSED_ARG (new_disp);
#endif /* PPM_NSIG <= 0  ||  CHORUS */
  return result;
}

 int
PPM_Select_Reactor_T::remove_handler
  (const PPM_Sig_Set &sigset)
{
  PPM_TRACE ("PPM_Select_Reactor_T::remove_handler");
  int result = 0;

#if (PPM_NSIG > 0)  &&  !defined (CHORUS)
  for (int s = 1; s < PPM_NSIG; s++)
    if (sigset.is_member (s)
        && this->signal_handler_->remove_handler (s) == -1)
      result = -1;
#else  /* PPM_NSIG <= 0  ||  CHORUS */
  PPM_UNUSED_ARG (sigset);
#endif /* PPM_NSIG <= 0  ||  CHORUS */

  return result;
}

// int
//PPM_Select_Reactor_T::cancel_timer (PPM_Event_Handler *handler,
//                                                              int dont_call_handle_close)
//{
//  PPM_TRACE ("PPM_Select_Reactor_T::cancel_timer");
//  PPM_MT (PPM_GUARD_RETURN (PPM_SELECT_REACTOR_TOKEN, PPM_mon, this->token_, -1));
//
//  if (this->timer_queue_ != 0)
//    return this->timer_queue_->cancel (handler, dont_call_handle_close);
//  else
//    return 0;
//}
//
// int
//PPM_Select_Reactor_T::cancel_timer (long timer_id,
//                                                              const void **arg,
//                                                              int dont_call_handle_close)
//{
//  PPM_TRACE ("PPM_Select_Reactor_T::cancel_timer");
//  PPM_MT (PPM_GUARD_RETURN (PPM_SELECT_REACTOR_TOKEN, PPM_mon, this->token_, -1));
//
//  if (this->timer_queue_ != 0)
//    return this->timer_queue_->cancel (timer_id,
//                                       arg,
//                                       dont_call_handle_close);
//  else
//    return 0;
//}
//
// long
//PPM_Select_Reactor_T::schedule_timer
//  (PPM_Event_Handler *handler,
//   const void *arg,
//   const PPM_Time_Value &delay_time,
//   const PPM_Time_Value &interval)
//{
//  PPM_TRACE ("PPM_Select_Reactor_T::schedule_timer");
//  PPM_MT (PPM_GUARD_RETURN (PPM_SELECT_REACTOR_TOKEN, PPM_mon, this->token_, -1));
//
//  return this->timer_queue_->schedule
//    (handler,
//     arg,
//     timer_queue_->gettimeofday () + delay_time,
//     interval);
//}
//
// int
//PPM_Select_Reactor_T::reset_timer_interval
//  (long timer_id,
//   const PPM_Time_Value &interval)
//{
//  PPM_TRACE ("PPM_Select_Reactor_T::reset_timer_interval");
//  PPM_MT (PPM_GUARD_RETURN (PPM_SELECT_REACTOR_TOKEN, PPM_mon, this->token_, -1));
//
//  return this->timer_queue_->reset_interval (timer_id, interval);
//}

// Main event loop driver that blocks for <max_wait_time> before
// returning (will return earlier if I/O or signal events occur).

 int
PPM_Select_Reactor_T::handle_events
  (PPM_Time_Value &max_wait_time)
{
  PPM_TRACE ("PPM_Select_Reactor_T::handle_events");

  return this->handle_events (&max_wait_time);
}

 int
PPM_Select_Reactor_T::handle_error (void)
{
  PPM_DEBUG((LM_ERROR,"handle error :%m \n"));
  PPM_TRACE ("PPM_Select_Reactor_T::handle_error");
  if (errno == EINTR)
    return this->restart_;
#if defined (__MVS__) || defined (WIN32) || defined (VXWORKS)
  // On MVS Open Edition and Win32, there can be a number of failure
  // codes on a bad socket, so check_handles on anything other than
  // EINTR.  VxWorks doesn't even bother to always set errno on error
  // in select (specifically, it doesn't return EBADF for bad FDs).
  else
    return this->check_handles ();
#else
#  if defined (PPM_PSOS)
  else if (errno == EBADS)
    return this->check_handles ();
#  else
  else if (errno == EBADF)
    return this->check_handles ();
#  endif /* PPM_PSOS */
  else
    return -1;
#endif  /* __MVS__ || WIN32 */
}

 void
PPM_Select_Reactor_T::notify_handle
  (PPM_HANDLE handle,
   PPM_Reactor_Mask mask,
   PPM_Handle_Set &ready_mask,
   PPM_Event_Handler *event_handler,
   PPM_EH_PTMF ptmf)
{
  PPM_TRACE ("PPM_Select_Reactor_T::notify_handle");
  // Check for removed handlers.
  if (event_handler == 0)
    return;

  int status = (event_handler->*ptmf) (handle);

  if (status < 0)
    this->remove_handler_i (handle, mask);
  else if (status > 0)
    ready_mask.set_bit (handle);
}

// Perform GET, CLR, SET, and ADD operations on the select()
// Handle_Sets.
//
// GET = 1, Retrieve current value
// SET = 2, Set value of bits to new mask (changes the entire mask)
// ADD = 3, Bitwise "or" the value into the mask (only changes
//          enabled bits)
// CLR = 4  Bitwise "and" the negation of the value out of the mask
//          (only changes enabled bits)
//
// Returns the original mask.

 int
PPM_Select_Reactor_T::mask_ops
  (PPM_HANDLE handle,
   PPM_Reactor_Mask mask,
   int ops)
{
  PPM_TRACE ("PPM_Select_Reactor_T::mask_ops");
  PPM_MT (PPM_GUARD_RETURN (PPM_SELECT_REACTOR_TOKEN, PPM_mon, this->token_, -1));
  return this->bit_ops (handle, mask,
                        this->wait_set_,
                        ops);
}

// Must be called with locks held.

 int
PPM_Select_Reactor_T::handler_i
  (PPM_HANDLE handle,
   PPM_Reactor_Mask mask,
   PPM_Event_Handler **handler)
{
  PPM_TRACE ("PPM_Select_Reactor_T::handler_i");
  PPM_Event_Handler *h = this->handler_rep_.find (handle);

  if (h == 0)
    return -1;
  else
    {
      if ((PPM_BIT_ENABLED (mask, PPM_Event_Handler::READ_MASK)
           || PPM_BIT_ENABLED (mask, PPM_Event_Handler::ACCEPT_MASK))
          && this->wait_set_.rd_mask_.is_set (handle) == 0)
        return -1;
      if (PPM_BIT_ENABLED (mask, PPM_Event_Handler::WRITE_MASK)
          && this->wait_set_.wr_mask_.is_set (handle) == 0)
        return -1;
      if (PPM_BIT_ENABLED (mask, PPM_Event_Handler::EXCEPT_MASK)
          && this->wait_set_.ex_mask_.is_set (handle) == 0)
        return -1;
    }

  if (handler != 0)
    *handler = h;
  return 0;
}

// Must be called with locks held

 int
PPM_Select_Reactor_T::resume_i (PPM_HANDLE handle)
{
  PPM_TRACE ("PPM_Select_Reactor_T::resume_i");
  if (this->handler_rep_.find (handle) == 0)
    return -1;

  if (this->suspend_set_.rd_mask_.is_set (handle))
    {
      this->wait_set_.rd_mask_.set_bit (handle);
      this->suspend_set_.rd_mask_.clr_bit (handle);
    }
  if (this->suspend_set_.wr_mask_.is_set (handle))
    {
      this->wait_set_.wr_mask_.set_bit (handle);
      this->suspend_set_.wr_mask_.clr_bit (handle);
    }
  if (this->suspend_set_.ex_mask_.is_set (handle))
    {
      this->wait_set_.ex_mask_.set_bit (handle);
      this->suspend_set_.ex_mask_.clr_bit (handle);
    }
  return 0;
}

// Must be called with locks held

 int
PPM_Select_Reactor_T::suspend_i (PPM_HANDLE handle)
{
  PPM_TRACE ("PPM_Select_Reactor_T::suspend_i");
  if (this->handler_rep_.find (handle) == 0)
    return -1;

  if (this->wait_set_.rd_mask_.is_set (handle))
    {
      this->suspend_set_.rd_mask_.set_bit (handle);
      this->wait_set_.rd_mask_.clr_bit (handle);
    }
  if (this->wait_set_.wr_mask_.is_set (handle))
    {
      this->suspend_set_.wr_mask_.set_bit (handle);
      this->wait_set_.wr_mask_.clr_bit (handle);
    }
  if (this->wait_set_.ex_mask_.is_set (handle))
    {
      this->suspend_set_.ex_mask_.set_bit (handle);
      this->wait_set_.ex_mask_.clr_bit (handle);
    }
  return 0;
}

// Must be called with locks held

 int
PPM_Select_Reactor_T::is_suspended_i (PPM_HANDLE handle)
{
  PPM_TRACE ("PPM_Select_Reactor_T::is_suspended_i");
  if (this->handler_rep_.find (handle) == 0)
    return 0;

  return this->suspend_set_.rd_mask_.is_set (handle) ||
         this->suspend_set_.wr_mask_.is_set (handle) ||
         this->suspend_set_.ex_mask_.is_set (handle)    ;

}

// Must be called with locks held

 int
PPM_Select_Reactor_T::register_handler_i
  (PPM_HANDLE handle,
   PPM_Event_Handler *event_handler,
   PPM_Reactor_Mask mask)
{
  PPM_TRACE ("PPM_Select_Reactor_T::register_handler_i");

  // Insert the <handle, event_handle> tuple into the Handler
  // Repository.
  return this->handler_rep_.bind (handle, event_handler, mask);
}

 int
PPM_Select_Reactor_T::remove_handler_i
  (PPM_HANDLE handle,
   PPM_Reactor_Mask mask)
{
  PPM_TRACE ("PPM_Select_Reactor_T::remove_handler_i");

  // Unbind this handle.
  return this->handler_rep_.unbind (handle, mask);
}

 int
PPM_Select_Reactor_T::work_pending
  (const PPM_Time_Value &max_wait_time)
{
  PPM_TRACE ("PPM_Select_Reactor_T::work_pending");

  PPM_Time_Value mwt (max_wait_time);
  PPM_MT (PPM_Countdown_Time countdown (&mwt));

  PPM_MT (PPM_GUARD_RETURN (PPM_SELECT_REACTOR_TOKEN,
                            PPM_mon,
                            this->token_,
                            -1));

  if (this->deactivated_)
    return 0;

  // Update the countdown to reflect time waiting for the mutex.
  PPM_MT (countdown.update ());

  PPM_Time_Value timer_buf (0);
  PPM_Time_Value timer_out (5);
  PPM_Time_Value *this_timeout = &timer_out;
//    this->timer_queue_->calculate_timeout (&mwt, &timer_buf);

  u_long width = (u_long) this->handler_rep_.max_handlep1 ();

  PPM_Select_Reactor_Handle_Set fd_set;
  fd_set.rd_mask_ = this->wait_set_.rd_mask_;
  fd_set.wr_mask_ = this->wait_set_.wr_mask_;
  fd_set.ex_mask_ = this->wait_set_.ex_mask_;

  return PPM_OS::select (int (width),
                         fd_set.rd_mask_,
                         fd_set.wr_mask_,
                         fd_set.ex_mask_,
                         this_timeout);
}

// Must be called with lock held.

 int
PPM_Select_Reactor_T::wait_for_multiple_events
  (PPM_Select_Reactor_Handle_Set &dispatch_set,
   PPM_Time_Value *max_wait_time)
{
  PPM_TRACE ("PPM_Select_Reactor_T::wait_for_multiple_events");
  u_long width = 0;
  PPM_Time_Value timer_buf (0);
  PPM_Time_Value timer_out (0,5000);
  PPM_Time_Value *this_timeout;

  int number_of_active_handles = this->any_ready (dispatch_set);

  // If there are any bits enabled in the <ready_set_> then we'll
  // handle those first, otherwise we'll block in <select>.

  if (number_of_active_handles == 0)
    {
      do
        {
          this_timeout = &timer_out;
//            this->timer_queue_->calculate_timeout (max_wait_time,
//                                                   &timer_buf);
          width = (u_long) this->handler_rep_.max_handlep1 ();

          dispatch_set.rd_mask_ = this->wait_set_.rd_mask_;
          dispatch_set.wr_mask_ = this->wait_set_.wr_mask_;
          dispatch_set.ex_mask_ = this->wait_set_.ex_mask_;
          number_of_active_handles = PPM_OS::select (int (width),
                                                     dispatch_set.rd_mask_,
                                                     dispatch_set.wr_mask_,
                                                     dispatch_set.ex_mask_,
                                                     this_timeout);
		  if(number_of_active_handles == -1){
			  int errorno = PPM_OS::last_error();
			  if(0 == errorno){
				number_of_active_handles = 0;
			  }else{
				  PPM_DEBUG((LM_ERROR,"ERROR:%m \n"));
			  }
		  }
		  if(0 == number_of_active_handles){
			  PPM_OS::sleep(10);
		  }
//		  fd_set f1,f2,f3;
//		  f1.fd_count = 0;
//		  f2.fd_count = 0;
//		  f3.fd_count = 0;
// 	    const timeval *timep = (this_timeout == 0 ? (const timeval *)0 : *this_timeout);
//		int res = ::select (width,
//										 (PPM_FD_SET_TYPE *) &f1,
//										 (PPM_FD_SET_TYPE *) &f2,
//										 (PPM_FD_SET_TYPE *) &f3,
//										 timep);
//		int errorno = PPM_OS::last_error();
//		PPM_DEBUG((LM_ERROR,"ERROR:%m \n"));
        }
      while (number_of_active_handles == -1 && this->handle_error () > 0);

      if (number_of_active_handles > 0)
        {
#if !defined (WIN32)
          // Resynchronize the fd_sets so their "max" is set properly.
          dispatch_set.rd_mask_.sync (this->handler_rep_.max_handlep1 ());
          dispatch_set.wr_mask_.sync (this->handler_rep_.max_handlep1 ());
          dispatch_set.ex_mask_.sync (this->handler_rep_.max_handlep1 ());
#endif /* WIN32 */
        }
      else if (number_of_active_handles == -1)
        {
          // Normally, select() will reset the bits in dispatch_set
          // so that only those filed descriptors that are ready will
          // have bits set.  However, when an error occurs, the bit
          // set remains as it was when the select call was first made.
          // Thus, we now have a dispatch_set that has every file
          // descriptor that was originally waited for, which is not
          // correct.  We must clear all the bit sets because we
          // have no idea if any of the file descriptors is ready.
          //
          // NOTE: We dont have a test case to reproduce this
          // problem. But pleae dont ignore this and remove it off.
          dispatch_set.rd_mask_.reset ();
          dispatch_set.wr_mask_.reset ();
          dispatch_set.ex_mask_.reset ();
        }
    }

  // Return the number of events to dispatch.
  return number_of_active_handles;
}
//
// int
//PPM_Select_Reactor_T::dispatch_timer_handlers
//  (int &number_of_handlers_dispatched)
//{
//  number_of_handlers_dispatched += this->timer_queue_->expire ();
//  if (this->state_changed_)
//    return -1;
//  else
//    return 0;
//}

// int
//PPM_Select_Reactor_T::dispatch_notification_handlers
//  (PPM_Select_Reactor_Handle_Set &dispatch_set,
//   int &number_of_active_handles,
//   int &number_of_handlers_dispatched)
//{
//  // Check to see if the PPM_HANDLE associated with the
//  // Select_Reactor's notify hook is enabled.  If so, it means that
//  // one or more other threads are trying to update the
//  // PPM_Select_Reactor_T's internal tables or the notify pipe is
//  // enabled.  We'll handle all these threads and notifications, and
//  // then break out to continue the event loop.
//
////  int n = this->notify_handler_->dispatch_notifications (number_of_active_handles,
////                                                         dispatch_set.rd_mask_);
//	 n = 0;
//  if (n == -1)
//    return -1;
//  else
//    number_of_handlers_dispatched += n;
//
//  return this->state_changed_ ? -1 : 0;
//}

 int
PPM_Select_Reactor_T::dispatch_io_set
  (int number_of_active_handles,
   int &number_of_handlers_dispatched,
   int mask,
   PPM_Handle_Set &dispatch_mask,
   PPM_Handle_Set &ready_mask,
   PPM_EH_PTMF callback)
{
  PPM_HANDLE handle;

  PPM_Handle_Set_Iterator handle_iter (dispatch_mask);

  while ((handle = handle_iter ()) != PPM_INVALID_HANDLE
         && number_of_handlers_dispatched < number_of_active_handles
         && this->state_changed_ == 0)
    {
      // PPM_DEBUG ((LM_DEBUG,  PPM_LIB_TEXT ("PPM_Select_Reactor_T::dispatching\n")));
      number_of_handlers_dispatched++;
      this->notify_handle (handle,
                           mask,
                           ready_mask,
                           this->handler_rep_.find (handle),
                           callback);
    }

  if (number_of_handlers_dispatched > 0 && this->state_changed_)
    return -1;

  return 0;
}

 int
PPM_Select_Reactor_T::dispatch_io_handlers
  (PPM_Select_Reactor_Handle_Set &dispatch_set,
   int &number_of_active_handles,
   int &number_of_handlers_dispatched)
{
  // Handle output events (this code needs to come first to handle the
  // obscure case of piggy-backed data coming along with the final
  // handshake message of a nonblocking connection).

  // PPM_DEBUG ((LM_DEBUG,  PPM_LIB_TEXT ("PPM_Select_Reactor_T::dispatch - WRITE\n")));
  if (this->dispatch_io_set (number_of_active_handles,
                             number_of_handlers_dispatched,
                             PPM_Event_Handler::WRITE_MASK,
                             dispatch_set.wr_mask_,
                             this->ready_set_.wr_mask_,
                             &PPM_Event_Handler::handle_output) == -1)
    {
      number_of_active_handles -= number_of_handlers_dispatched;
      return -1;
    }

  // PPM_DEBUG ((LM_DEBUG,  PPM_LIB_TEXT ("PPM_Select_Reactor_T::dispatch - EXCEPT\n")));
  if (this->dispatch_io_set (number_of_active_handles,
                             number_of_handlers_dispatched,
                             PPM_Event_Handler::EXCEPT_MASK,
                             dispatch_set.ex_mask_,
                             this->ready_set_.ex_mask_,
                             &PPM_Event_Handler::handle_exception) == -1)
    {
      number_of_active_handles -= number_of_handlers_dispatched;
      return -1;
    }

  // PPM_DEBUG ((LM_DEBUG,  PPM_LIB_TEXT ("PPM_Select_Reactor_T::dispatch - READ\n")));
  if (this->dispatch_io_set (number_of_active_handles,
                             number_of_handlers_dispatched,
                             PPM_Event_Handler::READ_MASK,
                             dispatch_set.rd_mask_,
                             this->ready_set_.rd_mask_,
                             &PPM_Event_Handler::handle_input) == -1)
    {
      number_of_active_handles -= number_of_handlers_dispatched;
      return -1;
    }

  number_of_active_handles -= number_of_handlers_dispatched;
  return 0;
}

 int
PPM_Select_Reactor_T::dispatch
  (int active_handle_count,
   PPM_Select_Reactor_Handle_Set &dispatch_set)
{
  PPM_TRACE ("PPM_Select_Reactor_T::dispatch");

  int io_handlers_dispatched = 0;
  int other_handlers_dispatched = 0;
  int signal_occurred = 0;
  // The following do/while loop keeps dispatching as long as there
  // are still active handles.  Note that the only way we should ever
  // iterate more than once through this loop is if signals occur
  // while we're dispatching other handlers.

  do
    {
      // Note that we keep track of changes to our state.  If any of
      // the dispatch_*() methods below return -1 it means that the
      // <wait_set_> state has changed as the result of an
      // <PPM_Event_Handler> being dispatched.  This means that we
      // need to bail out and rerun the select() loop since our
      // existing notion of handles in <dispatch_set> may no longer be
      // correct.
      //
      // In the beginning, our state starts out unchanged.  After
      // every iteration (i.e., due to signals), our state starts out
      // unchanged again.

      this->state_changed_ = 0;

      // Perform the Template Method for dispatching all the handlers.

      // First check for interrupts.
      if (active_handle_count == -1)
        {
          // Bail out -- we got here since <select> was interrupted.
          if (PPM_Sig_Handler::sig_pending () != 0)
            {
              PPM_Sig_Handler::sig_pending (0);

              // If any HANDLES in the <ready_set_> are activated as a
              // result of signals they should be dispatched since
              // they may be time critical...
              active_handle_count = this->any_ready (dispatch_set);

              // Record the fact that the Reactor has dispatched a
              // handle_signal() method.  We need this to return the
              // appropriate count below.
              signal_occurred = 1;
            }
          else
            return -1;
        }

      // Handle timers early since they may have higher latency
      // constraints than I/O handlers.  Ideally, the order of
      // dispatching should be a strategy...
//      else if (this->dispatch_timer_handlers (other_handlers_dispatched) == -1)
//        // State has changed or timer queue has failed, exit loop.
//        break;

      // Check to see if there are no more I/O handles left to
      // dispatch AFTER we've handled the timers...
      else if (active_handle_count == 0)
        return io_handlers_dispatched
          + other_handlers_dispatched
          + signal_occurred;

      // Next dispatch the notification handlers (if there are any to
      // dispatch).  These are required to handle multi-threads that
      // are trying to update the <Reactor>.

//      else if (this->dispatch_notification_handlers
//               (dispatch_set,
//                active_handle_count,
//                other_handlers_dispatched) == -1)
//        // State has changed or a serious failure has occured, so exit
//        // loop.
//        break;

      // Finally, dispatch the I/O handlers.
      else if (this->dispatch_io_handlers
               (dispatch_set,
                active_handle_count,
                io_handlers_dispatched) == -1)
        // State has changed, so exit loop.
        break;
    }
  while (active_handle_count > 0);

  return io_handlers_dispatched + other_handlers_dispatched + signal_occurred;
}

// int
//PPM_Select_Reactor_T::release_token (void)
//{
//#if defined (WIN32)
//  this->token_.release ();
//  return (int) EXCEPTION_CONTINUE_SEARCH;
//#else
//  return 0;
//#endif /* WIN32 */
//}

 int
PPM_Select_Reactor_T::handle_events
  (PPM_Time_Value *max_wait_time)
{
  PPM_TRACE ("PPM_Select_Reactor_T::handle_events");

#if defined (PPM_MT_SAFE) && (PPM_MT_SAFE != 0)

  // Stash the current time -- the destructor of this object will
  // automatically compute how much time elapsed since this method was
  // called.
  PPM_Countdown_Time countdown (max_wait_time);

  PPM_GUARD_RETURN (PPM_SELECT_REACTOR_TOKEN, PPM_mon, this->token_, -1);

  if (PPM_OS::thr_equal (PPM_Thread::self (),
                         this->owner_) == 0 || this->deactivated_)
    return -1;

  // Update the countdown to reflect time waiting for the mutex.
  countdown.update ();
#else
  if (this->deactivated_)
    return -1;
#endif /* PPM_MT_SAFE */

  return this->handle_events_i (max_wait_time);
}

 int
PPM_Select_Reactor_T::handle_events_i(PPM_Time_Value *max_wait_time)
{
  int result = -1;

  PPM_SEH_TRY
  {
    PPM_Select_Reactor_Handle_Set dispatch_set;

    int number_of_active_handles =
      this->wait_for_multiple_events (dispatch_set,
                                      max_wait_time);

    result = this->dispatch (number_of_active_handles,
                              dispatch_set);
  }
//  PPM_SEH_EXCEPT (this->release_token ())
//    {
//      // As it stands now, we catch and then rethrow all Win32
//      // structured exceptions so that we can make sure to release the
//      // <token_> lock correctly.
//    }

  this->state_changed_ = 1;

  return result;
}

 int
PPM_Select_Reactor_T::check_handles (void)
{
  PPM_TRACE ("PPM_Select_Reactor_T::check_handles");

#if defined (WIN32) || defined (__MVS__) || defined (PPM_PSOS) || defined (VXWORKS)
  PPM_Time_Value time_poll = PPM_Time_Value::zero;
  PPM_Handle_Set rd_mask;
#endif /* WIN32 || MVS || PPM_PSOS || VXWORKS */

  PPM_Event_Handler *eh = 0;
  int result = 0;

  for (PPM_Select_Reactor_Handler_Repository_Iterator iter (&this->handler_rep_);
       iter.next (eh) != 0;
       iter.advance ())
    {
      PPM_HANDLE handle = eh->get_handle ();

      // Skip back to the beginning of the loop if the HANDLE is
      // invalid.
      if (handle == PPM_INVALID_HANDLE)
        continue;

#if defined (WIN32) || defined (__MVS__) || defined (PPM_PSOS) || defined (VXWORKS)
      // Win32 needs to do the check this way because fstat won't work on
      // a socket handle.  MVS Open Edition needs to do it this way because,
      // even though the docs say to check a handle with either select or
      // fstat, the fstat method always says the handle is ok.
      // pSOS needs to do it this way because file handles and socket handles
      // are maintained by separate pieces of the system.  VxWorks needs the select
      // variant since fstat always returns an error on socket FDs.
      rd_mask.set_bit (handle);

      int select_width;
#  if defined (PPM_WIN64)
      // This arg is ignored on Windows and causes pointer truncation
      // warnings on 64-bit compiles.
      select_width = 0;
#  else
      select_width = int (handle) + 1;
#  endif /* PPM_WIN64 */

      if (PPM_OS::select (select_width,
                          rd_mask, 0, 0,
                          &time_poll) < 0)
        {
          result = 1;
          this->remove_handler_i (handle,
                                  PPM_Event_Handler::ALL_EVENTS_MASK);
        }
      rd_mask.clr_bit (handle);
#else /* !WIN32 && !MVS && !PPM_PSOS */
      struct stat temp;

      if (PPM_OS::fstat (handle, &temp) == -1)
        {
          result = 1;
          this->remove_handler_i (handle,
                                  PPM_Event_Handler::ALL_EVENTS_MASK);
        }
#endif /* WIN32 || MVS || PPM_PSOS */
    }

  return result;
}

 void
PPM_Select_Reactor_T::dump (void) const
{
  PPM_TRACE ("PPM_Select_Reactor_T::dump");

//  PPM_DEBUG ((LM_DEBUG, PPM_BEGIN_DUMP, this));

//  this->timer_queue_->dump ();
  this->handler_rep_.dump ();
  this->signal_handler_->dump ();
  PPM_DEBUG ((LM_DEBUG,
              PPM_LIB_TEXT ("delete_signal_handler_ = %d\n"),
              this->delete_signal_handler_));

  PPM_HANDLE h;

  for (PPM_Handle_Set_Iterator handle_iter_wr (this->wait_set_.wr_mask_);
       (h = handle_iter_wr ()) != PPM_INVALID_HANDLE;
       ++handle_iter_wr)
    PPM_DEBUG ((LM_DEBUG, PPM_LIB_TEXT ("write_handle = %d\n"), h));

  for (PPM_Handle_Set_Iterator handle_iter_rd (this->wait_set_.rd_mask_);
       (h = handle_iter_rd ()) != PPM_INVALID_HANDLE;
       ++handle_iter_rd)
    PPM_DEBUG ((LM_DEBUG, PPM_LIB_TEXT ("read_handle = %d\n"), h));

  for (PPM_Handle_Set_Iterator handle_iter_ex (this->wait_set_.ex_mask_);
       (h = handle_iter_ex ()) != PPM_INVALID_HANDLE;
       ++handle_iter_ex)
    PPM_DEBUG ((LM_DEBUG, PPM_LIB_TEXT ("except_handle = %d\n"), h));

  for (PPM_Handle_Set_Iterator handle_iter_wr_ready (this->ready_set_.wr_mask_);
       (h = handle_iter_wr_ready ()) != PPM_INVALID_HANDLE;
       ++handle_iter_wr_ready)
    PPM_DEBUG ((LM_DEBUG, PPM_LIB_TEXT ("write_handle_ready = %d\n"), h));

  for (PPM_Handle_Set_Iterator handle_iter_rd_ready (this->ready_set_.rd_mask_);
       (h = handle_iter_rd_ready ()) != PPM_INVALID_HANDLE;
       ++handle_iter_rd_ready)
    PPM_DEBUG ((LM_DEBUG, PPM_LIB_TEXT ("read_handle_ready = %d\n"), h));

  for (PPM_Handle_Set_Iterator handle_iter_ex_ready (this->ready_set_.ex_mask_);
       (h = handle_iter_ex_ready ()) != PPM_INVALID_HANDLE;
       ++handle_iter_ex_ready)
    PPM_DEBUG ((LM_DEBUG, PPM_LIB_TEXT ("except_handle_ready = %d\n"), h));

  PPM_DEBUG ((LM_DEBUG, PPM_LIB_TEXT ("restart_ = %d\n"), this->restart_));
  PPM_DEBUG ((LM_DEBUG, PPM_LIB_TEXT ("\nrequeue_position_ = %d\n"), this->requeue_position_));
  PPM_DEBUG ((LM_DEBUG, PPM_LIB_TEXT ("\ninitialized_ = %d\n"), this->initialized_));
  PPM_DEBUG ((LM_DEBUG, PPM_LIB_TEXT ("\nowner_ = %d\n"), this->owner_));

//#if defined (PPM_MT_SAFE) && (PPM_MT_SAFE != 0)
//  this->notify_handler_->dump ();
//  this->token_.dump ();
//#endif /* PPM_MT_SAFE */

//  PPM_DEBUG ((LM_DEBUG, PPM_END_DUMP));
}
#endif /* PPM_SELECT_REACTOR_T_C */
