#pragma once

#include "DataActor.h"

class ServerDealBase : public CDataActor{
public:
	ServerDealBase(){ m_ptcpclient_ = NULL;}
	virtual ~ServerDealBase(){
	};
	void SetTcpClient(PPM_Tcp_Client* ptcpclient){
		m_ptcpclient_ = ptcpclient;
	}
	PPM_HANDLE GetHandle(){
		return m_ptcpclient_->get_handle();
	}
	virtual int SendData(const BYTE * const pBuf, const int nSize){
		if(NULL != m_ptcpclient_){
			return m_ptcpclient_->SendData(pBuf,nSize);
		}
		else{
			return -1;
		}
	};
 
	virtual int RecvDeal(BYTE* pBuf,int iMaxLen,PPM_SOCK_Stream* pSockStream){
		return 0;
	}
	virtual void BeginDeal(){};

	PPM_Tcp_Client* m_ptcpclient_;
private:
};