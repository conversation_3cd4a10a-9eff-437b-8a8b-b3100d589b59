
PPM_INLINE int
PPM_Thread::spawn (PPM_THR_VOID_FUNC func,
		   void *arg,
		   long flags,
		   PPM_thread_t *t_id,
		   PPM_hthread_t *t_handle,
		   long priority,
		   void *thr_stack,
		   size_t thr_stack_size)
{
  PPM_TRACE ("PPM_Thread::spawn");

  return PPM_OS::thr_create (func,
			     arg,
			     flags,
			     t_id,
			     t_handle,
			     priority,
			     thr_stack,
			     thr_stack_size);
}


PPM_INLINE PPM_thread_t
PPM_Thread::self (void)
{
//  PPM_TRACE ("PPM_Thread::self");
  return PPM_OS::thr_self ();
}

// PPM_INLINE void
// PPM_Thread::exit (PPM_THR_FUNC_RETURN status)
// {
//   PPM_TRACE ("PPM_Thread::exit");
//   PPM_OS::thr_exit (status);
// }

// PPM_INLINE void
// PPM_Thread::yield (void)
// {
//   PPM_TRACE ("PPM_Thread::yield");
//   PPM_OS::thr_yield ();
// }

PPM_INLINE int
PPM_Thread::resume (PPM_hthread_t t_id)
{
  PPM_TRACE ("PPM_Thread::resume");
  return PPM_OS::thr_continue (t_id);
}

PPM_INLINE int
PPM_Thread::suspend (PPM_hthread_t t_id)
{
  PPM_TRACE ("PPM_Thread::suspend");
  return PPM_OS::thr_suspend (t_id);
}

// PPM_INLINE int
// PPM_Thread::kill (PPM_thread_t t_id, int signum)
// {
//   PPM_TRACE ("PPM_Thread::kill");
//   return PPM_OS::thr_kill (t_id, signum);
// }

// PPM_INLINE int
// PPM_Thread::join (PPM_hthread_t wait_for,
// 		  PPM_THR_FUNC_RETURN *status)
// {
//   PPM_TRACE ("PPM_Thread::join");
//   return PPM_OS::thr_join (wait_for, status);
// }

PPM_INLINE void
PPM_Thread::self (PPM_hthread_t &t_id)
{
//  PPM_TRACE ("PPM_Thread::self");
  PPM_OS::thr_self (t_id);
}

PPM_INLINE int
PPM_Thread::getprio (PPM_hthread_t t_id, int &prio)
{
  PPM_TRACE ("PPM_Thread::getprio");
  return PPM_OS::thr_getprio (t_id, prio);
}

PPM_INLINE int
PPM_Thread::setprio (PPM_hthread_t t_id, int prio)
{
  PPM_TRACE ("PPM_Thread::setprio");
  return PPM_OS::thr_setprio (t_id, prio);
}
