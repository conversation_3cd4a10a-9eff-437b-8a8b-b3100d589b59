/* -*- C++ -*- */

//=============================================================================
/**
 *  @file    Handle_Set.h
 *
 *  Handle_Set.h,v 4.28 2002/01/02 13:38:28 bala Exp
 *
 *  <AUTHOR> <<EMAIL>>
 */
//=============================================================================

#ifndef PPM_HANDLE_SET_H
#define PPM_HANDLE_SET_H
#include "./PPM_OS.h"

#if !defined (PPM_LACKS_PRAGMA_ONCE)
# pragma once
#endif /* PPM_LACKS_PRAGMA_ONCE */

/**
包装提供一些处理fd_set的方法
 */
class PPM_Handle_Set
{
public:
  friend class PPM_Handle_Set_Iterator;

  // = Initialization and termination.

  enum
  {
    MAXSIZE = PPM_DEFAULT_SELECT_REACTOR_SIZE
  };

  // = Initialization methods.
  /// Constructor, initializes the bitmask to all 0s.
  PPM_Handle_Set (void);

  /**
   * Constructor, initializes the handle set from a given mask.
   * <PPM_FD_SET_TYPE> is a <typedef> based on the platform's native
   * type used for masks passed to <select>.
   */
  PPM_Handle_Set (const PPM_FD_SET_TYPE &mask);

  // = Methods for manipulating bitsets.
  /// Initialize the bitmask to all 0s and reset the associated fields.
  void reset (void);

  /**
   * Checks whether <handle> is enabled.  No range checking is
   * performed so <handle> must be less than
   * <PPM_DEFAULT_SELECT_REACTOR_SIZE>.
   */
  int is_set (PPM_HANDLE handle) const;

  /// Enables the <handle>.  No range checking is performed so <handle>
  /// must be less than <PPM_DEFAULT_SELECT_REACTOR_SIZE>.
  void set_bit (PPM_HANDLE handle);

  /// Disables the <handle>.  No range checking is performed so
  /// <handle> must be less than <PPM_DEFAULT_SELECT_REACTOR_SIZE>.
  void clr_bit (PPM_HANDLE handle);

  /// Returns a count of the number of enabled bits.
  int num_set (void) const;

  /// Returns the number of the large bit.
  PPM_HANDLE max_set (void) const;

  /**
   * Rescan the underlying <fd_set> up to handle <max> to find the new
   * <max_handle> (highest bit set) and <size> (how many bits set) values.
   * This is useful for evaluating the changes after the handle set has
   * been manipulated in some way other than member functions; for example,
   * after <select> modifies the <fd_set>.
   */
  void sync (PPM_HANDLE max);

  /// Returns a pointer to the underlying <fd_set>.  Returns 0 if
  /// there are no handle bits set (<size_> == 0).
  operator fd_set *();

  /// Returns a pointer to the underlying <fd_set>.  Returns 0 if
  /// there are no handle bits set (<size_> == 0).
  fd_set *fdset (void);


  /// Dump the state of an object.
  void dump (void) const;

private:
  /// Size of the set, i.e., a count of the number of enabled bits.
  int size_;

  /// Current max handle.
  PPM_HANDLE max_handle_;

  /// Bitmask.
  fd_set mask_;

  enum
  {
    WORDSIZE = 32,//BITS
    NBITS = 256
  };

  /// Counts the number of bits enabled in N.  Uses a table lookup to
  /// speed up the count.
  static int count_bits (u_long n);

  /// Resets the <max_handle_> after a clear of the original
  /// <max_handle_>.
  void set_max (PPM_HANDLE max);

  /// Table that maps bytes to counts of the enabled bits in each value
  /// from 0 to 255.
  static const char nbits_[NBITS];
};
/**
 * @class PPM_Handle_Set_Iterator
 *
 * @brief Iterator for the <PPM_Handle_Set> abstraction.
 */
class  PPM_Handle_Set_Iterator
{
public:
  /// Constructor.
  PPM_Handle_Set_Iterator (const PPM_Handle_Set &hs);

  /// Default dtor.
  ~PPM_Handle_Set_Iterator (void);

  /// Reset the state of the iterator by reinitializing the state
  /// that we maintain.
  void reset_state (void);

  /**
   * "Next" operator.  Returns the next unseen <PPM_HANDLE> in the
   * <Handle_Set> up to <handle_set_.max_handle_>).  When all the
   * handles have been seen returns <PPM_INVALID_HANDLE>.  Advances
   * the iterator automatically, so you need not call <operator++>
   * (which is now obsolete).
   */
  PPM_HANDLE operator () (void);

  /// This is a no-op and no longer does anything.  It's only here for
  /// backwards compatibility.
  void operator++ (void);

  /// Dump the state of an object.
  void dump (void) const;

  /// Declare the dynamic allocation hooks.
//  PPM_ALLOC_HOOK_DECLARE;

private:
  /// The <Handle_Set> we are iterating through.
  const PPM_Handle_Set &handles_;

#if defined (WIN32)
  u_int handle_index_;
#elif !defined (PPM_HAS_BIG_FD_SET)
  int handle_index_;
#elif defined (PPM_HAS_BIG_FD_SET)
  int handle_index_;
  u_long oldlsb_;
#endif /* WIN32 */
  // Index of the bit we're examining in the current <word_num_> word.

  /// Number of the word we're iterating over (typically between 0..7).
  int word_num_;

#if defined (PPM_HAS_BIG_FD_SET)
  /// Number max of the words with a possible bit on.
  int word_max_;
#endif /* PPM_HAS_BIG_FD_SET */

#if !defined (WIN32) && !defined (PPM_HAS_BIG_FD_SET)
  /// Value of the bits in the word we're iterating on.
  fd_mask word_val_;
#elif !defined (WIN32) && defined (PPM_HAS_BIG_FD_SET)
  /// Value of the bits in the word we're iterating on.
  u_long word_val_;
#endif /* !WIN32 && !PPM_HAS_BIG_FD_SET */
};
#if !defined (PPM_LACKS_INLINE_FUNCTIONS)
#include "./PPM_Handle_Set.inl"
#endif /* __PPM_INLINE__ */

#endif /* PPM_HANDLE_SET */
