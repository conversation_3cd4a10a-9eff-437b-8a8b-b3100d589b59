// PPM_SOCK_Stream.cpp: implementation of the PPM_SOCK_Stream class.
//
//////////////////////////////////////////////////////////////////////

#include "./PPM_SOCK_Stream.h"
#include "./PPM_Handle_Set.h"
#include "./PPM_Public_Var.h"

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

///////////////////////////////////////////////////////////////////////
PPM_SOCK_Stream::PPM_SOCK_Stream(void)
{

}

PPM_SOCK_Stream::PPM_SOCK_Stream (const PPM_HANDLE h)
{
	PPM_TRACE ("PPM_SOCK_Stream::PPM_SOCK_Stream");

	this->set_handle(h);
}

PPM_SOCK_Stream::~PPM_SOCK_Stream(void)
{

}
// 设置部分属性,TCP采用默认的8K收发缓冲区
int PPM_SOCK_Stream::open (int type, int protocol_family, 
                int protocol, int reuse_addr)
{
	if (PPM_SOCK::open(type, protocol_family, protocol, reuse_addr) == -1)
	{
		return -1;
	}

	const int one = 1;

    this->set_option(SOL_SOCKET,
                        SO_KEEPALIVE,
                        (void*)(&one),
                        sizeof(one));

#ifdef WIN32
	const int opt_value = 1;
    this->set_option(IPPROTO_TCP,
                        TCP_NODELAY,
                        (void*)(&one),
                        sizeof(one));
#endif

	linger  opt_linvalue;
	opt_linvalue.l_onoff = 0;
	opt_linvalue.l_linger = 0;
    this->set_option(SOL_SOCKET,
                        SO_LINGER,
                        (void*)(&opt_linvalue),
                        sizeof(opt_linvalue));

	return 0;
}

int PPM_SOCK_Stream::select_r(void)
{
	PPM_Handle_Set handle_set;
	handle_set.reset();
	handle_set.set_bit(this->get_handle());

	PPM_Time_Value timeout(0, 2000);

	int select_width = int (this->get_handle ()) + 1;
	int recv_rlt = PPM_OS::select (select_width,
						  handle_set,
						  0,
						  0,
						  &timeout);
	// case -1:// 出错
	// case 0:// 超时或者对端网线拔掉, 对端网线拔掉select_r返回0, 这里不处理，交由测试包发现问题。
	// default:// 对端关闭或正常

	return recv_rlt;
}

int PPM_SOCK_Stream::select_w(void)
{
	PPM_Handle_Set handle_set;
	handle_set.reset();
	handle_set.set_bit(this->get_handle());

	PPM_Time_Value timeout(0, 5000);

	int select_width = int (this->get_handle ()) + 1;
	int recv_rlt = PPM_OS::select (select_width,
						  0,
						  handle_set,
						  0,
						  &timeout);
	//case -1:// 出错
	//case 0:// 超时
	//default:// 正常

	return recv_rlt;
}
int PPM_SOCK_Stream::recv_n(char * const pBuf, const int nSize, const int flags )
{
	int nRt = 0;
	int nLen = 0;
	int retrytimes = 0;
	while(nLen < nSize)
	{
		nRt = recv((char*)(pBuf + nLen), nSize - nLen);
		if (nRt >= 0)
		{
			nLen += nRt;
		}
		else if(-2 == nRt){
			return -1;
		}
		else if(PPM_OS::last_error() == EWOULDBLOCK){
			PPM_OS::sleep(1);
			if(retrytimes++ > 200)
			{
				PPM_SOCKLOG((LM_ERROR,"cannot read so much %d -- %d!%m\n ",nLen,nSize));
				return 0;
			};
			continue;
		}
		else
		{
			PPM_SOCKLOG((LM_ERROR,"RECV ERROR %d - %d %m\n",nLen,nSize));
			return nRt;
		}
	}
	return nLen;
}

int PPM_SOCK_Stream::recv(char * const pBuf, const int nSize, const int flags)
{
	PPM_TRACE("PPM_SOCK_IO::recv");
	if(NULL == pBuf){
		return -2;
	}
	if (nSize == 0)
	{
		return 0;
	}

	int recv_rlt = PPM_OS::recv(this->get_handle(), pBuf, nSize, flags); 

	if (recv_rlt <= 0) // 接收数据错误(可能是socket 关闭)
	{
		if(recv_rlt == 0){
			PPM_SOCKLOG((LM_ERROR,"remote socket closed!\n"));
			recv_rlt = -2;
		}
	}

	return recv_rlt;
}
//考虑到阻塞和非阻塞方式
int PPM_SOCK_Stream::send(const char* const pBuf, const int nSize )
{
	int res = PPM_OS::send(this->get_handle(), pBuf, nSize);
	if(res == nSize){
		return res;
	}else{
		bool flag = false;
		if(res >= 0){
			PPM_SOCKLOG((LM_INFO,"send part of pakage %d - %d \n",res,nSize));
			flag = true;
		}else if(PPM_OS::last_error() == EWOULDBLOCK){
//			PPM_SOCKLOG((LM_INFO,"%m \n",res,nSize));
			flag = true;
			res = 0;
		}
		if(true == flag){
			//这里有两种情况,一种是发了一半,另一种就是非阻塞的发不过来
			int tm_size = nSize;
			int tm_offset = res;
			tm_size -= res;
			
			while(tm_size > 0){
				res = PPM_OS::send(this->get_handle(), pBuf + tm_offset, tm_size);
				if(res >= 0){
					tm_offset += res;
					tm_size -= res;
				}else if(PPM_OS::last_error() == EWOULDBLOCK){
					PPM_OS::sleep(1);
					continue;
				}else{
					return res;
				}
			}
			return nSize;
		}else{
			//这种情况就是真正出错的情况
			PPM_SOCKLOG((LM_ERROR,"Send error!%m\n"));
			return res;
		}
	}
// 	PPM_TRACE("PPM_SOCK_IO::send");
// 	if ((NULL == pBuf) || (nSize == 0))
// 	{
// 		return 0;
// 	}
// 
// 	PPM_Handle_Set handle_set;
// 	handle_set.reset();
// 	handle_set.set_bit(this->get_handle());
// 
// 	PPM_Time_Value timeout(0, 5000);
// 
// 	int select_width;
// 	int recv_rlt = 0;
// 	select_width = int (this->get_handle ()) + 1;
// 	switch (PPM_OS::select (select_width,
// 						  0,
// 						  handle_set,
// 						  0,
// 						  &timeout))
// 	{
// 	case -1:// 出错
// 		return -1;
// 	case 0:// 超时
// 		return 0;
// 	default:// 正常
// 		recv_rlt = PPM_OS::send(this->get_handle(), pBuf, nSize);
// 	}
// 
// 	if (recv_rlt <= 0) 
// 	{
// 		recv_rlt = -2;
// 	}
// 	return recv_rlt;

}




