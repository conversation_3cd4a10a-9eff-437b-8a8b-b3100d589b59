// PPM_Tcp_Client.h: interface for the PPM_Tcp_Client class.
//
//////////////////////////////////////////////////////////////////////
#pragma once

#include "./StdBuffer.h"
#include "./PPM_SOCK_Connector.h"
#include "./PPM_SOCK_Stream.h"
#include "./PPM_INET_Addr.h"
#include "./PPM_Event_Handler.h"
#include "./PPM_SOCK_Buffer.h"
#include "./PPM_Log_Msg.h"

#define TESTPACKHEAD 0XE7E7

class PPM_Tcp_Server_Actor;
class PPM_Tcp_Client_Actor;
struct stru_client;
struct stru_server;
///空的socket接收处理类,在输入处理类为空的情况使用
class CDataActorNull : public CDataActor  
{
public:
	CDataActorNull(){};
	virtual ~CDataActorNull(){};
	virtual BOOL DealData(const BYTE* const pData, const int nCount){
		return TRUE;
	};
};
class PPM_Tcp_Client_Actor;
class PPM_Tcp_Server_Actor;
/////////////////////////////////////////////////////////
/**
通讯处理基本类,每一个连接都有这么一个对象,服务端和客户端都调用这个类
进行处理

  modified by yht:
   PPM_Tcp_Client可能产生如下几个线程:
   1 有一个侦听数据线程
   2 可能有收数据线呈，如果收结点类型不为NullMode的话
   3 可能有发数据线呈，如果发节点类型不为NullMode的话

*/
class PPM_Tcp_Client : public PPM_Event_Handler
{
	friend class PPM_Tcp_Client_Actor;
	friend class PPM_Tcp_Server_Actor;
public:
	struct stru_clientctl{
		BYTE bClosetype;//关闭socket的类型,0表示被动关闭,1表示主动关闭
		BYTE bReconnect;//重连标志,0表示不需要重连,1表示重连
		BYTE bIsDestory;//用来标示收数据线程关闭时是否删除资源
		BOOL bIsInitSuc;//用来标示连接是否成功
		BOOL bActorNeedDel; //Actor是否需要删除
	};
	struct stru_serverctl{
		BYTE bClosetype;//关闭类型
		BYTE bIsDestory;//用来标示收数据线程关闭时是否删除资源
		int i_clientid;//用于标示连接的id
	};

	///客户端构造器
	PPM_Tcp_Client(const char* const server_ip,
		const u_short server_port,
		PPM_SOCK_Buffer* pSockBuffer,
		PPM_Reactor* preactor,
		BYTE bReconnect = 0, //是否需要重连,默认是不重连
		CDataActor* pActor = NULL,
		const long lDecDataThreadPriority = PPM_DEFAULT_THREAD_PRIORITY,//侦听数据线程,如果只是发送不接收数据,这个常数无效
		const long lRecDataThreadPriority = PPM_DEFAULT_THREAD_PRIORITY,//可能有收数据线呈，如果收结点类型不为NullMode的话
		const long lSendDataThreadPriority = PPM_DEFAULT_THREAD_PRIORITY,//可能有发数据线呈，如果发节点类型不为NullMode的话		
		const BYTE bSendHeadFlag = 1, //发送是否加AA55标示
		const BYTE bRecvHeadFlag = 1,  //接收是否加AA55标示
		BYTE isNeedDelReactor = 0,  //是否需要删除reactor
		const char* local_ip=NULL
		);

	///服务端构造器
	PPM_Tcp_Client(PPM_SOCK_Stream sockstream,
		PPM_Reactor* preactor,
		PPM_SOCK_Buffer* pSockBuffer,
		CDataActor* pActor,
		const long lDecDataThreadPriority = PPM_DEFAULT_THREAD_PRIORITY,//侦听数据线程
		const long lRecDataThreadPriority = PPM_DEFAULT_THREAD_PRIORITY,//可能有收数据线呈，如果收结点类型不为NullMode的话
		const long lSendDataThreadPriority = PPM_DEFAULT_THREAD_PRIORITY,//可能有发数据线呈，如果发节点类型不为NullMode的话
		const BYTE bSendHeadFlag = 1, //发送是否加AA55标示
		const BYTE bRecvHeadFlag = 1  //接收是否加AA55标示
		);


	///初始化函数
	void Initialize(void);

	///判断初始化是否成功,给客户端PPM_Client使用,在初始化失败的时候删除资源
	BOOL IsIniSucceed(){
		return m_stru_clientctl.bIsInitSuc;
	};

	/// 当客户端主动关闭连接时调用此函数，以防止错误发生
	void ActorDeleteNotify(){
		StopThread(3);
		CloseClient();
		SetDataActor(NULL);
	};
	///有数据接收时会调用此函数
	virtual int handle_input (PPM_HANDLE fd = PPM_INVALID_HANDLE);

	/// Called when an exceptional events occur (e.g., SIGURG).
	virtual int handle_exception (PPM_HANDLE fd = PPM_INVALID_HANDLE)
	{
		PPM_OS::sleep(1000);
		return 0;
	};

	///socket关闭时会调用此函数
	virtual int handle_close (PPM_HANDLE handle,
						PPM_Reactor_Mask close_mask);

	virtual int OnRecv_HaveHead();
	virtual int OnRecv_NoHead();
	///客户端主动调用关闭
	int CloseClient();

	///应用数据发送调用接口，保存到发送缓冲区中
	int SendData(const BYTE * const pBuf, const int nSize);

	int RecvData(const BYTE * const pBuf, const int nSize);
	///接收调用接口,属于内部调用,从接受缓冲中读取数据,读到数据就触发后续的数据处理
	int RecvData(BYTE ** const pBuf, int& nSize);

	///获取本连接的handle,实际上就是socket的handle
	virtual PPM_HANDLE get_handle (void) const{
		return m_skStream.get_handle();
	};

	///设置socket的关闭类型,0为被动关闭,1为主动关闭
	void SetCloseType(BYTE closetype);

	///设置客户端管理连接的对象,主要是管理所有通过PPM_Client建立的连接
    void SetClientActor(PPM_Tcp_Client_Actor* ptcpclientactor);

	///设置保存客户端相关信息的结构指针,里面包含buffer指针,客户端处理类对象指针等
	void SetClientStru(stru_client* pstru_client);

	///设置服务端端管理连接的对象,主要是管理所有通过PPM_Tcp_Server建立的连接
    void SetServerActor(PPM_Tcp_Server_Actor* ptcpserveractor);

	///设置保存服务端相关信息的结构指针,里面包含buffer指针,客户端处理类对象指针等
	void SetServerStru(stru_server* pstru_server);

	///重新置位tcp_client,在buffer重用的情况下使用
	int ServerReset(PPM_SOCK_Stream newsocket);

	///设置在线程结束时是否需要删除资源,0:不删除,1删除资源,2不删除资源,删除自己
	void SetServerDestoryFlag(BYTE flag)
	{
		m_lock_.Lock();
		m_stru_serverctl.bIsDestory = flag;
		m_lock_.Unlock();
	};

	PPM_INET_Addr* GetInetAddr()
	{
		return &m_Addr;
	}

	///获取删除资源的标示,0:不删除,1删除资源,2不删除资源,删除自己
	BYTE GetDestoryFlag()
	{
		CStdAutoLock tm_autolock(&m_lock_);
		return m_stru_serverctl.bIsDestory;
	}

	///获取相关的buffer对象的指针
    PPM_SOCK_Buffer* GetSockBuffer(){
		return m_pSockBuffer;
	}

	///获取相关的业务处理对象的指针
	CDataActor* GetDataActor(){
		return m_pActor;
	}

	///设置相关的业务处理对象的指针
	void SetDataActor(CDataActor* pactor){
		m_pActor = pactor;
	}

	///重置线程停止标志,置为非停止状态
	void ResetThrFlag()
	{
		m_bIsRecvThr_Stop = 0;
		m_bIsSendThr_Stop = 0;
	}

	PPM_INET_Addr GetRemoteAddr()
	{
		PPM_INET_Addr addr;
		m_skStream.get_remote_addr(addr);
		return addr;
	};

	PPM_INET_Addr GetLocalAddr()
	{
		PPM_INET_Addr addr;
		if( 0 > m_skStream.get_local_addr(addr) ){
			return addr;
		}
		return addr;
	};

	//将客户端注册到反应器
	void Registe();
	
	//取消注册
	void UnRegiste();
    
	///初始化tcp客户端,内部调用
	bool BeginInstance(void);

	unsigned int m_nRecvCount;

protected:
		///发送线程函数入口
	friend PPM_THR_FUNC_VOID_RETURN SendThread(void * argus);

	///接受线程函数入口
	friend PPM_THR_FUNC_VOID_RETURN RecvThread(void * argus);

	///客户端reactor线程函数入口
	PPM_THR_FUNC_RETURN ReactorThread(void * argus,long priority)
	{
		PPM_Tcp_Client* pClt = (PPM_Tcp_Client*)argus;
		
		return pClt->ReactorMain(priority);
	};

    
private:
	///私有析构,不允许外部直接调用delete删除
	virtual ~PPM_Tcp_Client(void);

	///反应器线程,客户端使用时如果需要反应器的话会创建反应器线程
	PPM_THR_FUNC_RETURN ReactorMain(long priority);

	///发数据线程主函数
	PPM_THR_FUNC_VOID_RETURN SendMain(void);

	///收线程主函数
	PPM_THR_FUNC_VOID_RETURN RecvMain(void);
	
	///客户端的handleclose处理,内部调用
	int HandleClose_client(PPM_HANDLE handle);

	///服务端的handleclose处理,主动关闭,内部调用
	int HandleClose_server_active(PPM_HANDLE handle);

	///服务端的handleclose处理,主动关闭,内部调用
	int HandleClose_server_passive(PPM_HANDLE handle);



	/// 终止tcp客户端,内部调用
	void EndInstance(void);

	/// 停止tcp客户端线程,内部调用
	void ResetRunFlag(void);

	///连接到服务端,内部调用
	bool Connect(void);

	/// 发送函数,发送至网络,内部调用
	int OnSend();

	/// 接收函数,从网络接收,内部调用
	int OnRecv();

	///处理服务端的socket异常不通的情况,内部调用
    int DealAbnormSock();

	///检测包的正确性
	BOOL CheckPackHead(const BYTE* const pBuf);

    ///关闭线程flag :1表示收线程 2表示发线程 3表示收发线程都Stop,
	///iwaitSeconds表示最长等待的时间,-1表示永远等待,内部调用
	void StopThread(BYTE flag,int iwaitSeconds = 10);

	/// 客户端连接器,全局唯一
	static PPM_SOCK_Connector m_sConner;

	/// 连接的SOCK_Stream
	PPM_SOCK_Stream  m_skStream;

	/// 保存服务端地址信息
	PPM_INET_Addr m_Addr;

	/// 保存本地地址信息
	PPM_INET_Addr m_LocalAddr;
	BOOL bBindLocal;

	///用于通讯发送接收的缓冲区对象指针
	PPM_SOCK_Buffer* m_pSockBuffer;

	/// 发线程运行标志
	bool m_bRunFlag_Send;

	///收线程运行标志
	bool m_bRunFlag_Recv;

	/// 测试包
	BYTE m_Test[PKG_HEADER_LEN];

	/// 接收指针
	BYTE * m_precvBuf;

	/// 发送指针
	BYTE * m_psendBuf;
	int  m_nSendCount;

	///  业务处理器对象
	CDataActor* m_pActor;

	///是否注册到反应器的标示,0:没有注册,1:已注册
	BYTE m_bRegisted;

	///保存客户端连接的相关控制信息
	stru_clientctl m_stru_clientctl;

	///保存服务端连接的相关控制信息
	stru_serverctl m_stru_serverctl;

//线程控制
	///收线程句柄
	PPM_hthread_t m_RecvThr_Handle;

    ///发线程句柄
	PPM_hthread_t m_SendThr_Handle;
	
	///收线程停止标志
	BYTE m_bIsRecvThr_Stop;

	///发线程停止标志
	BYTE m_bIsSendThr_Stop;

	///锁
	CStdLock m_lock_;

    ///在server创建client时的控制总的连接数的对象指针
	PPM_Tcp_Server_Actor* m_ptcp_server_actor_;

	///保存服务端连接的相关处理对象信息
	stru_server* m_pstru_server;

	//tcp_client类型 0表示客户端创建 1 表示又服务端侦听创建
	int m_type;

    ///在PPM_Client创建client时的控制总的连接数的对象指针
	PPM_Tcp_Client_Actor* m_ptcp_client_actor_;

	///保存客户端连接的相关处理对象信息
	stru_client* m_pstru_client;

	///是否handleclosed
	BOOL m_IsHandleClosed;

	///REACTOR是否需要关闭
	BOOL m_bReactorDelFlag;

	long m_lDecDataThreadPriority_;//每个连接有一个侦听数据线程
	long m_lRecDataThreadPriority_;//可能有收数据线呈，如果收结点类型不为NullMode的话
	long m_lSendDataThreadPriority_;//可能有发数据线呈，如果发节点类型不为NullMode的话

	BYTE m_bSendHeadFlag_;
	BYTE m_bRecvHeadFlag_;
};

inline void PPM_Tcp_Client::ResetRunFlag(void)
{
	m_bRunFlag_Send = false;
	m_bRunFlag_Recv = false;
};

inline 	int 
PPM_Tcp_Client::SendData(const BYTE * const pBuf, const int nSize)
{
	if (nSize > (NODE_BUF_SIZE - PKG_HEADER_LEN))
	{
		PPM_TRACE("SendData size is too big.");
		return 0;
	}
	else
	{
		//如果没有发送缓冲，直接发送
		if(m_pSockBuffer==NULL || m_pSockBuffer->m_SendMode == CNodeMng::NullMode){
			BYTE tm_buf[NODE_BUF_SIZE +4 ];
//			int res1 = m_skStream.send((char*)tm_buf, nSize + 4);
			int res1 = 0;
            int iSendSize = nSize;
			if(1 == m_bSendHeadFlag_){
				tm_buf[0] = DATAPACKHEAD & 0XFF;
				tm_buf[1] = (DATAPACKHEAD & 0XFF00) >> 8;
				tm_buf[2] = (nSize / 0x100) ;
				tm_buf[3] = (nSize % 0x100) ;
                iSendSize += 4;
				PPM_OS::memcpy(tm_buf + 4,pBuf,nSize);
				res1 = m_skStream.send((char*)tm_buf, iSendSize);			
			}else{
				PPM_OS::memcpy(tm_buf,pBuf,nSize);
				res1 = m_skStream.send((char*)tm_buf, iSendSize);
			}
			if( res1 < 0 ){
				//如果需要重连就再重连发送一次
				if(1 == m_stru_clientctl.bReconnect){
					if(true == Connect()){
						res1 = m_skStream.send((char*)tm_buf, iSendSize);
						if(res1 < 0){
							return res1;
						}
						if(1 == m_bSendHeadFlag_){
							return res1 - 4;
						}else{
							return res1;
						}
					}
				}
				return res1;
			}
			if(1 == m_bSendHeadFlag_){
				return res1 - 4;
			}else{
				return res1;
			}
		}
		else{
			//需要写入发送缓冲
			//当写数据写不进去时需要堵一下
			int res = m_pSockBuffer->WriteData(pBuf, nSize);
			while( (0 == res) && (nSize != 0)  && ( true == m_bRunFlag_Send) ){
				PPM_OS::sleep(10);
				res = m_pSockBuffer->WriteData(pBuf, nSize);
			}
			return res;
		}
	}
};

inline 	int 
PPM_Tcp_Client::RecvData(const BYTE * const pBuf, const int nSize)
{
	if (nSize > (NODE_BUF_SIZE - PKG_HEADER_LEN))
	{
		PPM_TRACE("SendData size is too big.");
		return 0;
	}
	else
	{
		//如果没有发送缓冲，直接发送

		int res1 = m_skStream.recv((char*)pBuf, nSize);			
	
		return res1;
	}
};

