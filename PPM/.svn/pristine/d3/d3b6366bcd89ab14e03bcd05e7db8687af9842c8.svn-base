// Select_Reactor_Base.cpp,v 4.48 2002/11/02 23:47:06 bala Exp

#include "./PPM_Select_Reactor_Base.h"
#include "./PPM_Reactor.h"
#include "./PPM_Thread.h"
//#include "./PPM_Synch_T.h"
#include "./PPM_Log_Msg.h"

#if defined (PPM_LACKS_INLINE_FUNCTIONS)
#include "./PPM_Select_Reactor_Base.inl"
#endif /* __PPM_INLINE__ */

// PPM_RCSID(ace, Select_Reactor_Base, "Select_Reactor_Base.cpp,v 4.48 2002/11/02 23:47:06 bala Exp")

#if defined (WIN32)
#define PPM_SELECT_REACTOR_HANDLE(H) (this->event_handlers_[(H)].handle_)
#define PPM_SELECT_REACTOR_EVENT_HANDLER(THIS,H) ((THIS)->event_handlers_[(H)].event_handler_)
#else
#define PPM_SELECT_REACTOR_HANDLE(H) (H)
#define PPM_SELECT_REACTOR_EVENT_HANDLER(THIS,H) ((THIS)->event_handlers_[(H)])
#endif /* WIN32 */

// Performs sanity checking on the PPM_HANDLE.

int
PPM_Select_Reactor_Handler_Repository::invalid_handle (PPM_HANDLE handle)
{
  PPM_TRACE ("PPM_Select_Reactor_Handler_Repository::invalid_handle");
#if defined (WIN32)
  // It's too expensive to perform more exhaustive validity checks on
  // Win32 due to the way that they implement SOCKET HANDLEs.
  if (handle == PPM_INVALID_HANDLE)
#else /* !WIN32 */
    if (handle < 0 || handle >= this->max_size_)
#endif /* WIN32 */
      {
        errno = EINVAL;
        return 1;
      }
    else
      return 0;
}

// Performs sanity checking on the PPM_HANDLE.

int
PPM_Select_Reactor_Handler_Repository::handle_in_range (PPM_HANDLE handle)
{
  PPM_TRACE ("PPM_Select_Reactor_Handler_Repository::handle_in_range");
#if defined (WIN32)
  // It's too expensive to perform more exhaustive validity checks on
  // Win32 due to the way that they implement SOCKET HANDLEs.
  if (handle != PPM_INVALID_HANDLE)
#else /* !WIN32 */
    if (handle >= 0 && handle < this->max_handlep1_)
#endif /* WIN32 */
      return 1;
    else
      {
        errno = EINVAL;
        return 0;
      }
}

size_t
PPM_Select_Reactor_Handler_Repository::max_handlep1 (void)
{
	CStdAutoLock autolock(&m_lock_Repository_);
  PPM_TRACE ("PPM_Select_Reactor_Handler_Repository::max_handlep1");

  return this->max_handlep1_;
}

int
PPM_Select_Reactor_Handler_Repository::open (size_t size)
{
	CStdAutoLock autolock(&m_lock_Repository_);
  PPM_TRACE ("PPM_Select_Reactor_Handler_Repository::open");
  this->max_size_ = size;
  this->max_handlep1_ = 0;

#if defined (WIN32)
  // Try to allocate the memory.
  PPM_NEW_RETURN (this->event_handlers_,
                  PPM_Event_Tuple[size],
                  -1);

  // Initialize the PPM_Event_Handler * to { PPM_INVALID_HANDLE, 0 }.
  for (size_t h = 0; h < size; h++)
    {
      PPM_SELECT_REACTOR_HANDLE (h) = PPM_INVALID_HANDLE;
      PPM_SELECT_REACTOR_EVENT_HANDLER (this, h) = 0;
    }
#else
  // Try to allocate the memory.
  PPM_NEW_RETURN (this->event_handlers_,
                  PPM_Event_Handler *[size],
                  -1);

  // Initialize the PPM_Event_Handler * to NULL.
  for (size_t h = 0; h < size; h++)
    PPM_SELECT_REACTOR_EVENT_HANDLER (this, h) = 0;
#endif /* WIN32 */

  // Try to increase the number of handles if <size> is greater than
  // the current limit.
  return PPM_OS::set_handle_limit (PPM_static_cast (int, size));
}

// Initialize a repository of the appropriate <size>.

PPM_Select_Reactor_Handler_Repository::PPM_Select_Reactor_Handler_Repository (PPM_Select_Reactor_Impl &select_reactor)
  : select_reactor_ (select_reactor),
    max_size_ (0),
    max_handlep1_ (0),
    event_handlers_ (0)
{
  PPM_TRACE ("PPM_Select_Reactor_Handler_Repository::PPM_Select_Reactor_Handler_Repository");
}

int
PPM_Select_Reactor_Handler_Repository::unbind_all (void)
{
  // Unbind all of the <handle, PPM_Event_Handler>s.
  for (int handle = 0;
       handle < this->max_handlep1_;
       handle++)
    this->unbind (PPM_SELECT_REACTOR_HANDLE (handle),
                  PPM_Event_Handler::ALL_EVENTS_MASK);

  return 0;
}

int
PPM_Select_Reactor_Handler_Repository::close (void)
{
	CStdAutoLock autolock(&m_lock_Repository_);
  PPM_TRACE ("PPM_Select_Reactor_Handler_Repository::close");

  if (this->event_handlers_ != 0)
    {
      this->unbind_all ();

      delete [] this->event_handlers_;
      this->event_handlers_ = 0;
    }
  return 0;
}

// Return the <PPM_Event_Handler *> associated with the <handle>.

PPM_Event_Handler *
PPM_Select_Reactor_Handler_Repository::find (PPM_HANDLE handle,
                                             size_t *index_p)
{
	CStdAutoLock autolock(&m_lock_Repository_);
  PPM_TRACE ("PPM_Select_Reactor_Handler_Repository::find");

  PPM_Event_Handler *eh = 0;
  int i;

  // Only bother to search for the <handle> if it's in range.
  if (this->handle_in_range (handle))
    {
      i = handle;

      eh = PPM_SELECT_REACTOR_EVENT_HANDLER (this, handle);
    }
  else
    // g++ can't figure out that <i> won't be used below if the handle
    // is out of range, so keep it happy by defining <i> here . . .
    i = 0;

  if (eh != 0)
    {
      if (index_p != 0)
        *index_p = i;
    }
  else
    errno = ENOENT;

  return eh;
}

// Bind the <PPM_Event_Handler *> to the <PPM_HANDLE>.

int
PPM_Select_Reactor_Handler_Repository::bind (PPM_HANDLE handle,
                                             PPM_Event_Handler *event_handler,
                                             PPM_Reactor_Mask mask)
{
	CStdAutoLock autolock(&m_lock_Repository_);
	
  PPM_TRACE ("PPM_Select_Reactor_Handler_Repository::bind");

  if (handle == PPM_INVALID_HANDLE)
    handle = event_handler->get_handle ();

  if (this->invalid_handle (handle))
    return -1;

  PPM_SELECT_REACTOR_EVENT_HANDLER (this, handle) = event_handler;

  if (this->max_handlep1_ < handle + 1)
    this->max_handlep1_ = handle + 1;

  if (this->select_reactor_.is_suspended_i (handle))
    {
      this->select_reactor_.bit_ops (handle,
                                     mask,
                                     this->select_reactor_.suspend_set_,
                                     PPM_Reactor::ADD_MASK);
    }
  else
    {
      this->select_reactor_.bit_ops (handle,
                                     mask,
                                     this->select_reactor_.wait_set_,
                                     PPM_Reactor::ADD_MASK);

      // Note the fact that we've changed the state of the <wait_set_>,
      // which is used by the dispatching loop to determine whether it can
      // keep going or if it needs to reconsult select().
      this->select_reactor_.state_changed_ = 1;
    }

  /*
  // @@NOTE: We used to do this in earlier versions of ACE+TAO. But
  // this is totally wrong..
  // Clear any suspend masks for it too.
  this->select_reactor_.bit_ops (handle,
                                 mask,
                                 this->select_reactor_.suspend_set_,
                                 PPM_Reactor::CLR_MASK);
  */

  return 0;
}

// Remove the binding of <PPM_HANDLE>.

int
PPM_Select_Reactor_Handler_Repository::unbind (PPM_HANDLE handle,
                                               PPM_Reactor_Mask mask)
{
	CStdAutoLock autolock(&m_lock_Repository_);
  PPM_TRACE ("PPM_Select_Reactor_Handler_Repository::unbind");

  size_t slot;
  PPM_Event_Handler *eh = this->find (handle, &slot);

  if (eh == 0)
    return -1;

  // Clear out the <mask> bits in the Select_Reactor's wait_set.
  this->select_reactor_.bit_ops (handle,
                                 mask,
                                 this->select_reactor_.wait_set_,
                                 PPM_Reactor::CLR_MASK);

  // And suspend_set.
  this->select_reactor_.bit_ops (handle,
                                 mask,
                                 this->select_reactor_.suspend_set_,
                                 PPM_Reactor::CLR_MASK);

  // Note the fact that we've changed the state of the <wait_set_>,
  // which is used by the dispatching loop to determine whether it can
  // keep going or if it needs to reconsult select().
  this->select_reactor_.state_changed_ = 1;

  // Close down the <Event_Handler> unless we've been instructed not
  // to.
  if (PPM_BIT_ENABLED (mask, PPM_Event_Handler::DONT_CALL) == 0)
    eh->handle_close (handle, mask);

  // If there are no longer any outstanding events on this <handle>
  // then we can totally shut down the Event_Handler.

  int has_any_wait_mask =
    (this->select_reactor_.wait_set_.rd_mask_.is_set (handle)
     || this->select_reactor_.wait_set_.wr_mask_.is_set (handle)
     || this->select_reactor_.wait_set_.ex_mask_.is_set (handle));
  int has_any_suspend_mask =
    (this->select_reactor_.suspend_set_.rd_mask_.is_set (handle)
     || this->select_reactor_.suspend_set_.wr_mask_.is_set (handle)
     || this->select_reactor_.suspend_set_.ex_mask_.is_set (handle));

  if (!has_any_wait_mask && !has_any_suspend_mask)
#if defined (WIN32)
    {
      PPM_SELECT_REACTOR_HANDLE (slot) = PPM_INVALID_HANDLE;
      PPM_SELECT_REACTOR_EVENT_HANDLER (this, slot) = 0;

      if (this->max_handlep1_ == (int) slot + 1)
        {
          // We've deleted the last entry (i.e., i + 1 == the current
          // size of the array), so we need to figure out the last
          // valid place in the array that we should consider in
          // subsequent searches.

          int i;

          for (i = this->max_handlep1_ - 1;
               i >= 0 && PPM_SELECT_REACTOR_HANDLE (i) == PPM_INVALID_HANDLE;
               i--)
            continue;

          this->max_handlep1_ = i + 1;
        }
    }
#else
  {
    PPM_SELECT_REACTOR_EVENT_HANDLER (this, handle) = 0;

    if (this->max_handlep1_ == handle + 1)
      {
        // We've deleted the last entry, so we need to figure out
        // the last valid place in the array that is worth looking
        // at.
        PPM_HANDLE wait_rd_max = this->select_reactor_.wait_set_.rd_mask_.max_set ();
        PPM_HANDLE wait_wr_max = this->select_reactor_.wait_set_.wr_mask_.max_set ();
        PPM_HANDLE wait_ex_max = this->select_reactor_.wait_set_.ex_mask_.max_set ();

        PPM_HANDLE suspend_rd_max = this->select_reactor_.suspend_set_.rd_mask_.max_set ();
        PPM_HANDLE suspend_wr_max = this->select_reactor_.suspend_set_.wr_mask_.max_set ();
        PPM_HANDLE suspend_ex_max = this->select_reactor_.suspend_set_.ex_mask_.max_set ();

        // Compute the maximum of six values.
        this->max_handlep1_ = wait_rd_max;
        if (this->max_handlep1_ < wait_wr_max)
          this->max_handlep1_ = wait_wr_max;
        if (this->max_handlep1_ < wait_ex_max)
          this->max_handlep1_ = wait_ex_max;

        if (this->max_handlep1_ < suspend_rd_max)
          this->max_handlep1_ = suspend_rd_max;
        if (this->max_handlep1_ < suspend_wr_max)
          this->max_handlep1_ = suspend_wr_max;
        if (this->max_handlep1_ < suspend_ex_max)
          this->max_handlep1_ = suspend_ex_max;

        this->max_handlep1_++;
      }
  }
#endif /* WIN32 */

  return 0;
}

PPM_Select_Reactor_Handler_Repository_Iterator::PPM_Select_Reactor_Handler_Repository_Iterator
  (const PPM_Select_Reactor_Handler_Repository *s)
    : rep_ (s),
      current_ (-1)
{
  this->advance ();
}

// Pass back the <next_item> that hasn't been seen in the Set.
// Returns 0 when all items have been seen, else 1.

int
PPM_Select_Reactor_Handler_Repository_Iterator::next (PPM_Event_Handler *&next_item)
{
  int result = 1;

  if (this->current_ >= this->rep_->max_handlep1_)
    result = 0;
  else
    next_item = PPM_SELECT_REACTOR_EVENT_HANDLER (this->rep_,
                                                  this->current_);
  return result;
}

int
PPM_Select_Reactor_Handler_Repository_Iterator::done (void) const
{
  return this->current_ >= this->rep_->max_handlep1_;
}

// Move forward by one element in the set.

int
PPM_Select_Reactor_Handler_Repository_Iterator::advance (void)
{
  if (this->current_ < this->rep_->max_handlep1_)
    this->current_++;

  while (this->current_ < this->rep_->max_handlep1_)
    if (PPM_SELECT_REACTOR_EVENT_HANDLER (this->rep_, this->current_) != 0)
      return 1;
    else
      this->current_++;

  return this->current_ < this->rep_->max_handlep1_;
}

// Dump the state of an object.

void
PPM_Select_Reactor_Handler_Repository_Iterator::dump (void) const
{
  PPM_TRACE ("PPM_Select_Reactor_Handler_Repository_Iterator::dump");

//  PPM_DEBUG ((LM_DEBUG, PPM_BEGIN_DUMP, this));
//  PPM_DEBUG ((LM_DEBUG, PPM_LIB_TEXT ("rep_ = %u"), this->rep_));
//  PPM_DEBUG ((LM_DEBUG, PPM_LIB_TEXT ("current_ = %d"), this->current_));
//  PPM_DEBUG ((LM_DEBUG, PPM_END_DUMP));
}

void
PPM_Select_Reactor_Handler_Repository::dump (void) const
{
  PPM_TRACE ("PPM_Select_Reactor_Handler_Repository::dump");

//  PPM_DEBUG ((LM_DEBUG, PPM_BEGIN_DUMP, this));
//  PPM_DEBUG ((LM_DEBUG,
//              PPM_LIB_TEXT ("(%t) max_handlep1_ = %d, max_size_ = %d\n"),
//              this->max_handlep1_, this->max_size_));
//  PPM_DEBUG ((LM_DEBUG,  PPM_LIB_TEXT ("[")));
//
//  PPM_Event_Handler *eh = 0;
//
//  for (PPM_Select_Reactor_Handler_Repository_Iterator iter (this);
//       iter.next (eh) != 0;
//       iter.advance ())
//    PPM_DEBUG ((LM_DEBUG, PPM_LIB_TEXT (" (eh = %x, eh->handle_ = %d)"),
//                eh, eh->get_handle ()));
//
//  PPM_DEBUG ((LM_DEBUG, PPM_LIB_TEXT (" ]")));
//  PPM_DEBUG ((LM_DEBUG, PPM_END_DUMP));
}

//PPM_ALLOC_HOOK_DEFINE(PPM_Select_Reactor_Handler_Repository_Iterator)

//PPM_Select_Reactor_Notify::PPM_Select_Reactor_Notify (void)
//  : max_notify_iterations_ (-1)
//{
//}
//
//void
//PPM_Select_Reactor_Notify::max_notify_iterations (int iterations)
//{
//  // Must always be > 0 or < 0 to optimize the loop exit condition.
//  if (iterations == 0)
//    iterations = 1;
//
//  this->max_notify_iterations_ = iterations;
//}
//
//int
//PPM_Select_Reactor_Notify::max_notify_iterations (void)
//{
//  return this->max_notify_iterations_;
//}
//
//// purge_pending_notifications
//// Removes all entries from the notify_queue_ and each one that
//// matches <eh> is put on the free_queue_. The rest are saved on a
//// local queue and copied back to the notify_queue_ at the end.
//// Returns the number of entries removed. Returns -1 on error.
//// PPM_NOTSUP_RETURN if PPM_HAS_REACTOR_NOTIFICATION_QUEUE is not defined.
//int
//PPM_Select_Reactor_Notify::purge_pending_notifications (PPM_Event_Handler *eh,
//                                                        PPM_Reactor_Mask  mask )
//{
//	return 0;
////  PPM_TRACE ("PPM_Select_Reactor_Notify::purge_pending_notifications");
////
////#if defined (PPM_HAS_REACTOR_NOTIFICATION_QUEUE)
////
////  PPM_GUARD_RETURN (PPM_SYNCH_MUTEX, mon, this->notify_queue_lock_, -1);
////
////  if (this->notify_queue_.is_empty ())
////    return 0;
////
////  PPM_Notification_Buffer *temp;
////  PPM_Unbounded_Queue <PPM_Notification_Buffer *> local_queue;
////
////  size_t queue_size = this->notify_queue_.size ();
////  int number_purged = 0;
////  size_t i;
////  for (i = 0; i < queue_size; ++i)
////    {
////      if (-1 == this->notify_queue_.dequeue_head (temp))
////        PPM_ERROR_RETURN ((LM_ERROR,
////                           PPM_LIB_TEXT ("%p\n"),
////                           PPM_LIB_TEXT ("dequeue_head")),
////                          -1);
////
////      // If this is not a Reactor notify (it is for a particular handler),
////      // and it matches the specified handler (or purging all),
////      // and applying the mask would totally eliminate the notification, then
////      // release it and count the number purged.
////      if ((0 != temp->eh_) &&
////          (0 == eh || eh == temp->eh_) &&
////          PPM_BIT_DISABLED (temp->mask_, ~mask)) // the existing notificationmask
////                                                 // is left with nothing when
////                                                 // applying the mask
////      {
////        if (-1 == this->free_queue_.enqueue_head (temp))
////          PPM_ERROR_RETURN ((LM_ERROR,
////                             PPM_LIB_TEXT ("%p\n"),
////                             PPM_LIB_TEXT ("enqueue_head")),
////                            -1);
////        ++number_purged;
////      }
////      else
////      {
////        // To preserve it, move it to the local_queue.
////        // But first, if this is not a Reactor notify (it is for a particularhandler),
////        // and it matches the specified handler (or purging all), then
////        // apply the mask
////        if ((0 != temp->eh_) &&
////            (0 == eh || eh == temp->eh_))
////          PPM_CLR_BITS(temp->mask_, mask);
////        if (-1 == local_queue.enqueue_head (temp))
////          return -1;
////      }
////    }
////
////  if (this->notify_queue_.size ())
////    { // should be empty!
////      PPM_ASSERT (0);
////      return -1;
////    }
////
////  // now put it back in the notify queue
////  queue_size = local_queue.size ();
////  for (i = 0; i < queue_size; ++i)
////    {
////      if (-1 == local_queue.dequeue_head (temp))
////        PPM_ERROR_RETURN ((LM_ERROR,
////                           PPM_LIB_TEXT ("%p\n"),
////                           PPM_LIB_TEXT ("dequeue_head")),
////                          -1);
////
////      if (-1 == this->notify_queue_.enqueue_head (temp))
////        PPM_ERROR_RETURN ((LM_ERROR,
////                           PPM_LIB_TEXT ("%p\n"),
////                           PPM_LIB_TEXT ("enqueue_head")),
////                          -1);
////    }
////
////  return number_purged;
////
////#else /* defined (PPM_HAS_REACTOR_NOTIFICATION_QUEUE) */
////  PPM_UNUSED_ARG (eh);
////  PPM_UNUSED_ARG (mask);
////  PPM_NOTSUP_RETURN (-1);
////#endif  /* defined (PPM_HAS_REACTOR_NOTIFICATION_QUEUE) */
//}
//
//void
//PPM_Select_Reactor_Notify::dump (void) const
//{
////  PPM_TRACE ("PPM_Select_Reactor_Notify::dump");
////
////  PPM_DEBUG ((LM_DEBUG, PPM_BEGIN_DUMP, this));
////  PPM_DEBUG ((LM_DEBUG, PPM_LIB_TEXT ("select_reactor_ = %x"), this->select_reactor_));
//  this->notification_pipe_.dump ();
////   PPM_DEBUG ((LM_DEBUG, PPM_END_DUMP));
//}
//
//int
//PPM_Select_Reactor_Notify::open (PPM_Reactor_Impl *r,
//                                 PPM_Timer_Queue *,
//                                 int disable_notify_pipe)
//{
//  PPM_TRACE ("PPM_Select_Reactor_Notify::open");
//
//  if (disable_notify_pipe == 0)
//    {
//      this->select_reactor_ =
//        PPM_dynamic_cast (PPM_Select_Reactor_Impl *, r);
//
//      if (select_reactor_ == 0)
//        {
//          errno = EINVAL;
//          return -1;
//        }
//
//      if (this->notification_pipe_.open () == -1)
//        return -1;
//#if defined (F_SETFD)
//      PPM_OS::fcntl (this->notification_pipe_.read_handle (), F_SETFD, 1);
//      PPM_OS::fcntl (this->notification_pipe_.write_handle (), F_SETFD, 1);
//#endif /* F_SETFD */
//
//#if defined (PPM_HAS_REACTOR_NOTIFICATION_QUEUE)
//      PPM_Notification_Buffer *temp;
//
//      PPM_NEW_RETURN (temp,
//                      PPM_Notification_Buffer[PPM_REACTOR_NOTIFICATION_ARRAY_SIZE],
//                      -1);
//
//      if (this->alloc_queue_.enqueue_head (temp) == -1)
//        {
//          delete [] temp;
//          return -1;
//        }
//
//      for (size_t i = 0; i < PPM_REACTOR_NOTIFICATION_ARRAY_SIZE; i++)
//        if (free_queue_.enqueue_head (temp + i) == -1)
//          return -1;
//
//#endif /* PPM_HAS_REACTOR_NOTIFICATION_QUEUE */
//
//      // There seems to be a Win32 bug with this...  Set this into
//      // non-blocking mode.
//      if (ACE::set_flags (this->notification_pipe_.read_handle (),
//                          PPM_NONBLOCK) == -1)
//        return -1;
//      else
//        return this->select_reactor_->register_handler
//          (this->notification_pipe_.read_handle (),
//           this,
//           PPM_Event_Handler::READ_MASK);
//    }
//  else
//    {
//      this->select_reactor_ = 0;
//      return 0;
//    }
//}
//
//int
//PPM_Select_Reactor_Notify::close (void)
//{
//  PPM_TRACE ("PPM_Select_Reactor_Notify::close");
//
//#if defined (PPM_HAS_REACTOR_NOTIFICATION_QUEUE)
//  // Free up the dynamically allocated resources.
//  PPM_Notification_Buffer **b;
//
//  for (PPM_Unbounded_Queue_Iterator<PPM_Notification_Buffer *> alloc_iter (this->alloc_queue_);
//       alloc_iter.next (b) != 0;
//       alloc_iter.advance ())
//    {
//      delete [] *b;
//      *b = 0;
//    }
//
//  this->alloc_queue_.reset ();
//  this->notify_queue_.reset ();
//  this->free_queue_.reset ();
//#endif /* PPM_HAS_REACTOR_NOTIFICATION_QUEUE */
//
//  return this->notification_pipe_.close ();
//}
//
//int
//PPM_Select_Reactor_Notify::notify (PPM_Event_Handler *eh,
//                                   PPM_Reactor_Mask mask,
//                                   PPM_Time_Value *timeout)
//{
//  PPM_TRACE ("PPM_Select_Reactor_Notify::notify");
//
//  // Just consider this method a "no-op" if there's no
//  // <PPM_Select_Reactor> configured.
//  if (this->select_reactor_ == 0)
//    return 0;
//
//  PPM_Notification_Buffer buffer (eh, mask);
//
//#if defined (PPM_HAS_REACTOR_NOTIFICATION_QUEUE)
//  // Artificial scope to limit the duration of the mutex.
//  {
//    // int notification_required = 0;
//
//    PPM_GUARD_RETURN (PPM_SYNCH_MUTEX, mon, this->notify_queue_lock_, -1);
//
//    // No pending notifications.
//
//    // We will send notify for every message..
//    // if (this->notify_queue_.is_empty ())
//    //   notification_required = 1;
//
//    PPM_Notification_Buffer *temp = 0;
//
//    if (free_queue_.dequeue_head (temp) == -1)
//      {
//        // Grow the queue of available buffers.
//        PPM_Notification_Buffer *temp1;
//
//        PPM_NEW_RETURN (temp1,
//                        PPM_Notification_Buffer[PPM_REACTOR_NOTIFICATION_ARRAY_SIZE],
//                        -1);
//
//        if (this->alloc_queue_.enqueue_head (temp1) == -1)
//          {
//            delete [] temp1;
//            return -1;
//          }
//
//        // Start at 1 and enqueue only
//        // (PPM_REACTOR_NOTIFICATION_ARRAY_SIZE - 1) elements since
//        // the first one will be used right now.
//        for (size_t i = 1;
//             i < PPM_REACTOR_NOTIFICATION_ARRAY_SIZE;
//             i++)
//          this->free_queue_.enqueue_head (temp1 + i);
//
//        temp = temp1;
//      }
//
//    PPM_ASSERT (temp != 0);
//    *temp = buffer;
//
//    if (notify_queue_.enqueue_tail (temp) == -1)
//      return -1;
//  }
//#endif /* PPM_HAS_REACTOR_NOTIFICATION_QUEUE */
//
//  int n = ACE::send (this->notification_pipe_.write_handle (),
//                         (char *) &buffer,
//                         sizeof buffer,
//                         timeout);
//  if (n == -1)
//    return -1;
//
//  return 0;
//}
//
//// Handles pending threads (if any) that are waiting to unblock the
//// Select_Reactor.
//
//int
//PPM_Select_Reactor_Notify::dispatch_notifications (int &number_of_active_handles,
//                                                   PPM_Handle_Set &rd_mask)
//{
//  PPM_TRACE ("PPM_Select_Reactor_Notify::dispatch_notifications");
//
//  PPM_HANDLE read_handle =
//    this->notification_pipe_.read_handle ();
//
//  if (read_handle != PPM_INVALID_HANDLE
//      && rd_mask.is_set (read_handle))
//    {
//      number_of_active_handles--;
//      rd_mask.clr_bit (read_handle);
//      return this->handle_input (read_handle);
//    }
//  else
//    return 0;
//}
//
//
//PPM_HANDLE
//PPM_Select_Reactor_Notify::notify_handle (void)
//{
//  PPM_TRACE ("PPM_Select_Reactor_Notify::notify_handle");
//
//  return this->notification_pipe_.read_handle ();
//}
//
//
//// Special trick to unblock <select> when updates occur in somewhere
//// other than the main <PPM_Select_Reactor> thread.  All we do is
//// write data to a pipe that the <PPM_Select_Reactor> is listening on.
//// Thanks to Paul Stephenson for suggesting this approach.
//int
//PPM_Select_Reactor_Notify::is_dispatchable (PPM_Notification_Buffer &buffer)
//{
//   // There is tonnes of code that can be abstracted...
//#if defined (PPM_HAS_REACTOR_NOTIFICATION_QUEUE)
//  {
//    PPM_GUARD_RETURN (PPM_SYNCH_MUTEX, mon, this->notify_queue_lock_, -1);
//
//    PPM_Notification_Buffer *temp;
//
//    PPM_UNUSED_ARG (buffer);
//
//    // If the queue is empty just return 0
//    if (notify_queue_.is_empty ())
//      return 0;
//
//    if (this->notify_queue_.dequeue_head (temp) == -1)
//      PPM_ERROR_RETURN ((LM_ERROR,
//                         PPM_LIB_TEXT ("%p\n"),
//                         PPM_LIB_TEXT ("dequeue_head")),
//                        -1);
//    if (temp->eh_ != 0)
//      {
//        // If the queue had a buffer that has an event handler, put
//        // the element  back in the queue and return a 1
//        if (this->notify_queue_.enqueue_head (temp) == -1)
//          {
//            PPM_ERROR_RETURN ((LM_ERROR,
//                               PPM_LIB_TEXT ("%p\n"),
//                               PPM_LIB_TEXT ("enque_head")),
//                              -1);
//          }
//
//        return 1;
//      }
//    // Else put the element in the free queue
//    if (free_queue_.enqueue_head (temp) == -1)
//      PPM_ERROR_RETURN ((LM_ERROR,
//                         PPM_LIB_TEXT ("%p\n"),
//                         PPM_LIB_TEXT ("enqueue_head")),
//                        -1);
//  }
//#else
//  // If eh == 0 then another thread is unblocking the
//  // <PPM_Select_Reactor> to update the <PPM_Select_Reactor>'s
//  // internal structures.  Otherwise, we need to dispatch the
//  // appropriate handle_* method on the <PPM_Event_Handler>
//  // pointer we've been passed.
//  if (buffer.eh_ != 0)
//    return 1;
//
//#endif /*PPM_HAS_REACTOR_NOTIFICATION_QUEUE */
//
//  // has no dispatchable buffer
//  return 0;
//}
//
//int
//PPM_Select_Reactor_Notify::dispatch_notify (PPM_Notification_Buffer &buffer)
//{
//  int result = 0;
//#if defined (PPM_HAS_REACTOR_NOTIFICATION_QUEUE)
//  // Dispatch all messages that are in the <notify_queue_>.
//  {
//    // We acquire the lock in a block to make sure we're not
//    // holding the lock while delivering callbacks...
//    PPM_GUARD_RETURN (PPM_SYNCH_MUTEX, mon, this->notify_queue_lock_, -1);
//
//    PPM_Notification_Buffer *temp;
//
//    if (notify_queue_.is_empty ())
//      return 0;
//    else if (notify_queue_.dequeue_head (temp) == -1)
//      PPM_ERROR_RETURN ((LM_ERROR,
//                         PPM_LIB_TEXT ("%p\n"),
//                         PPM_LIB_TEXT ("dequeue_head")),
//                        -1);
//    buffer = *temp;
//    if (free_queue_.enqueue_head (temp) == -1)
//      PPM_ERROR_RETURN ((LM_ERROR,
//                         PPM_LIB_TEXT ("%p\n"),
//                         PPM_LIB_TEXT ("enqueue_head")),
//                        -1);
//  }
//
//  // If eh == 0 then another thread is unblocking the
//  // <PPM_Select_Reactor> to update the <PPM_Select_Reactor>'s
//  // internal structures.  Otherwise, we need to dispatch the
//  // appropriate handle_* method on the <PPM_Event_Handler>
//  // pointer we've been passed.
//  if (buffer.eh_ != 0)
//    {
//
//      switch (buffer.mask_)
//        {
//        case PPM_Event_Handler::READ_MASK:
//        case PPM_Event_Handler::ACCEPT_MASK:
//          result = buffer.eh_->handle_input (PPM_INVALID_HANDLE);
//          break;
//        case PPM_Event_Handler::WRITE_MASK:
//          result = buffer.eh_->handle_output (PPM_INVALID_HANDLE);
//          break;
//        case PPM_Event_Handler::EXCEPT_MASK:
//          result = buffer.eh_->handle_exception (PPM_INVALID_HANDLE);
//          break;
//        default:
//          // Should we bail out if we get an invalid mask?
//          PPM_ERROR ((LM_ERROR, PPM_LIB_TEXT ("invalid mask = %d\n"), buffer.mask_));
//        }
//      if (result == -1)
//        buffer.eh_->handle_close (PPM_INVALID_HANDLE,
//                                  PPM_Event_Handler::EXCEPT_MASK);
//    }
//#else
//  // If eh == 0 then another thread is unblocking the
//  // <PPM_Select_Reactor> to update the <PPM_Select_Reactor>'s
//  // internal structures.  Otherwise, we need to dispatch the
//  // appropriate handle_* method on the <PPM_Event_Handler>
//  // pointer we've been passed.
//  if (buffer.eh_ != 0)
//    {
//      switch (buffer.mask_)
//        {
//        case PPM_Event_Handler::READ_MASK:
//        case PPM_Event_Handler::ACCEPT_MASK:
//          result = buffer.eh_->handle_input (PPM_INVALID_HANDLE);
//          break;
//        case PPM_Event_Handler::WRITE_MASK:
//          result = buffer.eh_->handle_output (PPM_INVALID_HANDLE);
//          break;
//        case PPM_Event_Handler::EXCEPT_MASK:
//          result = buffer.eh_->handle_exception (PPM_INVALID_HANDLE);
//          break;
//        case PPM_Event_Handler::QOS_MASK:
//          result = buffer.eh_->handle_qos (PPM_INVALID_HANDLE);
//          break;
//        case PPM_Event_Handler::GROUP_QOS_MASK:
//          result = buffer.eh_->handle_group_qos (PPM_INVALID_HANDLE);
//          break;
//        default:
//          // Should we bail out if we get an invalid mask?
//          PPM_ERROR ((LM_ERROR,
//                      PPM_LIB_TEXT ("invalid mask = %d\n"),
//                      buffer.mask_));
//        }
//      if (result == -1)
//        buffer.eh_->handle_close (PPM_INVALID_HANDLE,
//                                  PPM_Event_Handler::EXCEPT_MASK);
//    }
//
//#endif /* PPM_HAS_REACTOR_NOTIFICATION_QUEUE */
//
//  return 1;
//}
//
//int
//PPM_Select_Reactor_Notify::read_notify_pipe (PPM_HANDLE handle,
//                                             PPM_Notification_Buffer &buffer)
//{
//  PPM_TRACE ("PPM_Select_Reactor_Notify::read_notify_pipe");
//
//  int n = ACE::recv (handle, (char *) &buffer, sizeof buffer);
//
//  if (n > 0)
//    {
//      // Check to see if we've got a short read.
//      if (n != sizeof buffer)
//        {
//          int remainder = sizeof buffer - n;
//
//          // If so, try to recover by reading the remainder.  If this
//          // doesn't work we're in big trouble since the input stream
//          // won't be aligned correctly.  I'm not sure quite what to
//          // do at this point.  It's probably best just to return -1.
//          if (ACE::recv (handle,
//                         ((char *) &buffer) + n,
//                         remainder) != remainder)
//            return -1;
//        }
//
//
//      return 1;
//    }
//
//  // Return -1 if things have gone seriously  wrong.
//  if (n <= 0 && (errno != WSAEWOULDBLOCK && errno != EAGAIN))
//    return -1;
//
//  return 0;
//}
//
//
//int
//PPM_Select_Reactor_Notify::handle_input (PPM_HANDLE handle)
//{
//  PPM_TRACE ("PPM_Select_Reactor_Notify::handle_input");
//  // Precondition: this->select_reactor_.token_.current_owner () ==
//  // PPM_Thread::self ();
//
//  int number_dispatched = 0;
//  int result = 0;
//  PPM_Notification_Buffer buffer;
//
//  while ((result = this->read_notify_pipe (handle, buffer)) > 0)
//    {
//      // Dispatch the buffer
//      // NOTE: We count only if we made any dispatches ie. upcalls.
//      if (this->dispatch_notify (buffer) > 0)
//        number_dispatched++;
//
//      // Bail out if we've reached the <notify_threshold_>.  Note that
//      // by default <notify_threshold_> is -1, so we'll loop until all
//      // the notifications in the pipe have been dispatched.
//      if (number_dispatched == this->max_notify_iterations_)
//        break;
//    }
//
//  // Reassign number_dispatched to -1 if things have gone seriously
//  // wrong.
//  if (result < 0)
//    number_dispatched = -1;
//
//  // Enqueue ourselves into the list of waiting threads.  When we
//  // reacquire the token we'll be off and running again with ownership
//  // of the token.  The postcondition of this call is that
//  // <select_reactor_.token_.current_owner> == <PPM_Thread::self>.
//  this->select_reactor_->renew ();
//  return number_dispatched;
//}

// Perform GET, CLR, SET, and ADD operations on the Handle_Sets.
//
// GET = 1, Retrieve current value
// SET = 2, Set value of bits to new mask (changes the entire mask)
// ADD = 3, Bitwise "or" the value into the mask (only changes
//          enabled bits)
// CLR = 4  Bitwise "and" the negation of the value out of the mask
//          (only changes enabled bits)
//
// Returns the original mask.  Must be called with locks held.

int
PPM_Select_Reactor_Impl::bit_ops (PPM_HANDLE handle,
                                  PPM_Reactor_Mask mask,
                                  PPM_Select_Reactor_Handle_Set &handle_set,
                                  int ops)
{
  PPM_TRACE ("PPM_Select_Reactor_Impl::bit_ops");
  if (this->handler_rep_.handle_in_range (handle) == 0)
    return -1;

#if !defined (WIN32)
  PPM_Sig_Guard sb; // Block out all signals until method returns.
#endif /* WIN32 */

  PPM_FDS_PTMF ptmf  = &PPM_Handle_Set::set_bit;
  u_long omask = PPM_Event_Handler::NULL_MASK;

  // Find the old reactor masks.  This automatically does the work of
  // the GET_MASK operation.
  if (handle_set.rd_mask_.is_set (handle))
    PPM_SET_BITS (omask, PPM_Event_Handler::READ_MASK);
  if (handle_set.wr_mask_.is_set (handle))
    PPM_SET_BITS (omask, PPM_Event_Handler::WRITE_MASK);
  if (handle_set.ex_mask_.is_set (handle))
    PPM_SET_BITS (omask, PPM_Event_Handler::EXCEPT_MASK);

  switch (ops)
    {
    case PPM_Reactor::GET_MASK:
      // The work for this operation is done in all cases at the
      // begining of the function.
      break;
    case PPM_Reactor::CLR_MASK:
      ptmf = &PPM_Handle_Set::clr_bit;
      /* FALLTHRU */
    case PPM_Reactor::SET_MASK:
      /* FALLTHRU */
    case PPM_Reactor::ADD_MASK:

      // The following code is rather subtle...  Note that if we are
      // doing a PPM_Reactor::SET_MASK then if the bit is not enabled
      // in the mask we need to clear the bit from the PPM_Handle_Set.
      // On the other hand, if we are doing a PPM_Reactor::CLR_MASK or
      // a PPM_Reactor::ADD_MASK we just carry out the operations
      // specified by the mask.

      // READ, ACCEPT, and CONNECT flag will place the handle in the
      // read set.
      if (PPM_BIT_ENABLED (mask, PPM_Event_Handler::READ_MASK)
          || PPM_BIT_ENABLED (mask, PPM_Event_Handler::ACCEPT_MASK)
          || PPM_BIT_ENABLED (mask, PPM_Event_Handler::CONNECT_MASK))
        {
          (handle_set.rd_mask_.*ptmf) (handle);
        }
      else if (ops == PPM_Reactor::SET_MASK)
        handle_set.rd_mask_.clr_bit (handle);

      // WRITE and CONNECT flag will place the handle in the write set
      if (PPM_BIT_ENABLED (mask,
                           PPM_Event_Handler::WRITE_MASK)
          || PPM_BIT_ENABLED (mask,
                              PPM_Event_Handler::CONNECT_MASK))
        {
          (handle_set.wr_mask_.*ptmf) (handle);
        }
      else if (ops == PPM_Reactor::SET_MASK)
        handle_set.wr_mask_.clr_bit (handle);

      // EXCEPT (and CONNECT on Win32) flag will place the handle in
      // the except set.
      if (PPM_BIT_ENABLED (mask, PPM_Event_Handler::EXCEPT_MASK)
#if defined (WIN32)
          || PPM_BIT_ENABLED (mask, PPM_Event_Handler::CONNECT_MASK)
#endif /* WIN32 */
          )
        {
          (handle_set.ex_mask_.*ptmf) (handle);
        }
      else if (ops == PPM_Reactor::SET_MASK)
        handle_set.ex_mask_.clr_bit (handle);
      break;
    default:
      return -1;
    }
  return omask;
}

int
PPM_Select_Reactor_Impl::resumable_handler (void)
{
  // The select reactor has no handlers that can be resumed by the
  // application. So return 0;

  return 0;
}

//#if defined (PPM_HAS_EXPLICIT_TEMPLATE_INSTANTIATION)
//#if defined (PPM_HAS_REACTOR_NOTIFICATION_QUEUE)
//template class PPM_Unbounded_Queue <PPM_Notification_Buffer *>;
//template class PPM_Unbounded_Queue_Iterator <PPM_Notification_Buffer *>;
//template class PPM_Node <PPM_Notification_Buffer *>;
//#endif /* PPM_HAS_REACTOR_NOTIFICATION_QUEUE */
//#elif defined (PPM_HAS_TEMPLATE_INSTANTIATION_PRAGMA)
//#if defined (PPM_HAS_REACTOR_NOTIFICATION_QUEUE)
//#pragma instantiate PPM_Unbounded_Queue <PPM_Notification_Buffer *>
//#pragma instantiate PPM_Unbounded_Queue_Iterator <PPM_Notification_Buffer *>
//#pragma instantiate PPM_Node <PPM_Notification_Buffer *>
//#endif /* PPM_HAS_REACTOR_NOTIFICATION_QUEUE */
//#endif /* PPM_HAS_EXPLICIT_TEMPLATE_INSTANTIATION */
