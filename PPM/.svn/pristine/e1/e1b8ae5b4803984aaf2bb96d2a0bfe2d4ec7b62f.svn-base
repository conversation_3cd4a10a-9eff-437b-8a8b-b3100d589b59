
#ifndef _PPM_IPC_SAP_H
#define _PPM_IPC_SAP_H

#include "./PPM_typedef.h"
#include "./PPM_MACRO.h"
#include "./PPM_Flag_Manip.h"

#if !defined (PPM_LACKS_PRAGMA_ONCE)
# pragma once
#endif /* PPM_LACKS_PRAGMA_ONCE */

/**
 服务接入点，主要是针对sockethandle，iohandle等而言的基类
 * @class PPM_IPC_SAP
 *
 * @brief Defines the member functions for the base class of the
 * PPM_IPC_SAP abstraction.
 */
class PPM_IPC_SAP
{
public:
  /// Default dtor.
  ~PPM_IPC_SAP (void);

  /// Interface for <ioctl>.
  int control (int cmd, void *) const;

  // = Common I/O handle options related to sockets.

  /**
   * Enable asynchronous I/O (PPM_SIGIO), urgent data (PPM_SIGURG),
   * non-blocking I/O (PPM_NONBLOCK), or close-on-exec (PPM_CLOEXEC),
   * which is passed as the <value>.
   */
  int enable (int value) const;

  /**
   * Disable asynchronous I/O (PPM_SIGIO), urgent data (PPM_SIGURG),
   * non-blocking I/O (PPM_NONBLOCK), or close-on-exec (PPM_CLOEXEC),
   * which is passed as the <value>.
   */
  int disable (int value) const;

  /// Get the underlying handle.
  PPM_HANDLE get_handle (void) const;

  /// Set the underlying handle.
  void set_handle (PPM_HANDLE handle);

  /// Dump the state of an object.
  void dump (void) const;

protected:
  // = Ensure that PPM_IPC_SAP is an abstract base class.
  /// Default constructor.
  PPM_IPC_SAP (void);

private:
  /// Underlying I/O handle.
  PPM_HANDLE handle_;
};

#if !defined (PPM_LACKS_INLINE_FUNCTIONS)
#include "PPM_IPC_SAP.inl"
#endif /* PPM_LACKS_INLINE_FUNCTIONS */

#endif /* PPM_IPC_SAP_H */
