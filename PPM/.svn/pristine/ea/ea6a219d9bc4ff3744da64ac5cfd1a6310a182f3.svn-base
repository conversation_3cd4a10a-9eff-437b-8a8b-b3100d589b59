#include "./PPM_SOCK_Connector.h"
#include "./PPM_OS_Error.h"

/************************************************************************
输入：new_stream:需要连接的socket对象
	  remote_sap:服务端的ip和端口
	  timeout:连接最大时间
	  local_sap:本地的端口
输出: 连接情况 0 表示连接成功，-1表示连接失败
功能：将一个socket对象连接到服务端
************************************************************************/

int
PPM_SOCK_Connector::connect (PPM_SOCK_Stream &new_stream,
               const PPM_Addr &remote_sap,
               const PPM_Time_Value *timeout,
               const PPM_Addr &local_sap,
			   int reuse_addr)
{
  PPM_TRACE ("PPM_SOCK_Connector::connect");

  if (this->shared_open (new_stream,
                         remote_sap.get_type (),
                         0,
                         reuse_addr) == -1)
    return -1;
  else if (this->shared_connect_start (new_stream,
                                       timeout,
                                       local_sap) == -1)
    return -1;
  
  int result = PPM_OS::connect (new_stream.get_handle (),
                                PPM_reinterpret_cast (sockaddr *,
                                                      remote_sap.get_addr ()),
                                remote_sap.get_size ());

  this->shared_connect_finish (new_stream,
                                      timeout,
                                      result);
  return result;
}
/************************************************************************
输入：new_stream:需要创建的socket对象
	  protocol_family:连接最大时间(如果不为0，则是非阻塞式连接，需要上层继续处理)
	  local_sap:本地的端口
输出: 0 表示连接成功，-1表示连接失败
功能：新的socket连接前的准备，主要是针对非阻塞式的和本地端口需要绑定的情况
      每次连接前必须调用
************************************************************************/

int
PPM_SOCK_Connector::shared_open (PPM_SOCK_Stream &new_stream,
                                 int protocol_family,
                                 int protocol,
                                 int reuse_addr)
{
  PPM_TRACE ("PPM_SOCK_Connector::shared_open");

  // Only open a new socket if we don't already have a valid handle.
  if (new_stream.get_handle () == PPM_INVALID_HANDLE
      && new_stream.open (SOCK_STREAM,
                          protocol_family,
                          protocol,
                          reuse_addr) == -1)
    return -1;
  else
    return 0;
}
/************************************************************************
输入：new_stream:需要连接的socket对象
	  timeout:连接最大时间(如果不为0，则是非阻塞式连接，需要上层继续处理)
	  local_sap:本地的端口
输出: 0 表示连接成功，-1表示连接失败
功能：新的socket连接前的准备，主要是针对非阻塞式的和本地端口需要绑定的情况
      每次连接前必须调用
************************************************************************/
int
PPM_SOCK_Connector::shared_connect_start (PPM_SOCK_Stream &new_stream,
                                          const PPM_Time_Value *timeout,
                                          const PPM_Addr &local_sap)
{
  PPM_TRACE ("PPM_SOCK_Connector::shared_connect_start");

  if (local_sap != PPM_Addr::sap_any)
    {
      sockaddr *laddr = PPM_reinterpret_cast (sockaddr *,
                                              local_sap.get_addr ());
      int size = local_sap.get_size ();

      if (PPM_OS::bind (new_stream.get_handle (),
                        laddr,
                        size) == -1)
        {
          // Save/restore errno.
          PPM_Errno_Guard error (errno);
          new_stream.close ();
          return -1;
        }
    }

  // Enable non-blocking, if required.
  if (timeout != 0
      && new_stream.enable (PPM_NONBLOCK) == -1)
    return -1;
  else
    return 0;
}

/************************************************************************
输入：new_stream:需要连接的socket对象
	  timeout:连接最大时间(如果不为0，则是非阻塞式连接，需要上层继续处理)
	  result:前面连接的返回值
输出: 0 表示连接成功，-1表示连接失败
功能：新的socket连接后的处理，每次连接后必须调用
************************************************************************/
int
PPM_SOCK_Connector::shared_connect_finish (PPM_SOCK_Stream &new_stream,
                                           const PPM_Time_Value *timeout,
                                           int result)
{
  PPM_TRACE ("PPM_SOCK_Connector::shared_connect_finish");
  // Save/restore errno.
  PPM_Errno_Guard error (errno);

  if (result == -1 && timeout != 0)
    {
      // Check whether the connection is in progress.
      if (error == EINPROGRESS || error == EWOULDBLOCK)
        {
          // This expression checks if we were polling.
          if (timeout->sec () == 0
              && timeout->usec () == 0)
            error = EWOULDBLOCK;
          // Wait synchronously using timeout.
          else if (this->complete (new_stream,
                                   0,
                                   timeout) == -1)
            error = errno;
          else
            return 0;
        }
    }

  // WSAEISCONN is treated specially since this routine may be used to
  // check if we are already connected.
  if (result != -1 || error == EISCONN)
    // Start out with non-blocking disabled on the <new_stream>.
	new_stream.disable (PPM_NONBLOCK);
  else if (!(error == EWOULDBLOCK || error == ETIMEDOUT))
    new_stream.close ();

  return result;
}

// Try to complete a non-blocking connection.
int
PPM_SOCK_Connector::complete (PPM_SOCK_Stream &new_stream,
                              PPM_Addr *remote_sap,
                              const PPM_Time_Value *tv)
{
  PPM_TRACE ("PPM_SOCK_Connector::complete");
  PPM_HANDLE h = PPM_OS::handle_timed_complete (new_stream.get_handle (),
                                             tv);
  // We failed to get connected.
  if (h == PPM_INVALID_HANDLE)
    {
      // Win32 has a timing problem - if you check to see if the
      // connection has completed too fast, it will fail - so wait
      // <PPM_NON_BLOCKING_BUG_DELAY> microseconds to let it catch up
      // then retry to see if it's a real failure.
      PPM_Time_Value time (0, PPM_NON_BLOCKING_BUG_DELAY);
      PPM_OS::sleep (time);
      h = PPM_OS::handle_timed_complete (new_stream.get_handle (),
                                      tv);
      if (h == PPM_INVALID_HANDLE)
        {
		  // Save/restore errno.
		  PPM_Errno_Guard error (errno);
		  new_stream.close ();
		  return -1;
        }
    }

  if (remote_sap != 0)
    {
      int len = remote_sap->get_size ();
      sockaddr *addr = PPM_reinterpret_cast (sockaddr *,
                                             remote_sap->get_addr ());
      if (PPM_OS::getpeername (h,
                               addr,
                               &len) == -1)
        {
          // Save/restore errno.
          PPM_Errno_Guard error (errno);
          new_stream.close ();
          return -1;
        }
    }

  // Start out with non-blocking disabled on the <new_stream>.
  new_stream.disable (PPM_NONBLOCK);
  return 0;
}
