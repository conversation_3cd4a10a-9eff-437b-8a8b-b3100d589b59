
#ifndef _PPM_SOCK_H
#define _PPM_SOCK_H

#if !defined (PPM_LACKS_PRAGMA_ONCE)
# pragma once
#endif /* PPM_LACKS_PRAGMA_ONCE */

#include "PPM_Addr.h"
#include "PPM_IPC_SAP.h"
#include "PPM_OS.h"

/**
  socket的基类，提供了一些设置参数，创建socket，获取本异地ip，端口等一些方法
 */
class PPM_SOCK : public PPM_IPC_SAP
{
public:
  /// Default ctor/dtor.
  ~PPM_SOCK (void);

  /// Wrapper around the <setsockopt> system call.
  int set_option (int level,
                  int option,
                  void *optval,
                  int optlen) const;

  /// Wrapper around the <getsockopt> system call.
  int get_option (int level,
                  int option,
                  void *optval,
                  int *optlen) const;

  /// Close down the socket handle.
  int close (void);

  /// Return the local endpoint address in the referenced <PPM_Addr>.
  /// Returns 0 if successful, else -1.
  int get_local_addr (PPM_Addr &) const;

  /**
   * Return the address of the remotely connected peer (if there is
   * one), in the referenced <PPM_Addr>. Returns 0 if successful, else
   * -1.
   */
  int get_remote_addr (PPM_Addr &) const;

  /// Dump the state of an object.
  void dump (void) const;

  /// Wrapper around the BSD-style <socket> system call (no QoS).为了设置属性，改为虚拟
  virtual int open (int type,
            int protocol_family,
            int protocol,
            int reuse_addr);


protected:
  /// Constructor with arguments to call the BSD-style <socket> system
  /// call (no QoS).
  PPM_SOCK (int type,
            int protocol_family,
            int protocol = 0,
            int reuse_addr = 1);

  /// Default constructor is protected to prevent instances of this class
  /// from being defined.
  PPM_SOCK (void);

};

#if !defined (PPM_LACKS_INLINE_FUNCTIONS)
#include "./PPM_SOCK.inl"
#endif /* PPM_LACKS_INLINE_FUNCTIONS */

#endif /* PPM_SOCK_H */
