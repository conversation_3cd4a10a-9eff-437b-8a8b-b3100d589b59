#ifndef _PPM_SIGNAL_H_
#define _PPM_SIGNAL_H_ 
#include "./PPM_typedef.h"
#include "./PPM_Event_Handler.h"

/**
 * @class PPM_Sig_Set
 *
 * @brief Provide a C++ wrapper for the C sigset_t interface.
 *
 * Handle signals via a more elegant C++ interface (e.g.,
 * doesn't require the use of global variables or global
 * functions in an application).
 */
class PPM_Sig_Set
{
public:
  // = Initialization and termination methods.
  /// Initialize <sigset_> with <sigset>.  If <sigset> == 0 then fill
  /// the set.
  PPM_Sig_Set (sigset_t *sigset);

  /// Initialize <sigset_> with <sigset>.  If <sigset> == 0 then fill
  /// the set.
  PPM_Sig_Set (PPM_Sig_Set *sigset);

  /// If <fill> == 0 then initialize the <sigset_> to be empty, else
  /// full.
  PPM_Sig_Set (int fill = 0);

  ~PPM_Sig_Set (void);

  /// Create a set that excludes all signals defined by the system.
  int empty_set (void);

  /// Create a set that includes all signals defined by the system.
  int fill_set (void);

  /// Adds the individual signal specified by <signo> to the set.
  int sig_add (int signo);

  /// Deletes the individual signal specified by <signo> from the set.
  int sig_del (int signo);

  /// Checks whether the signal specified by <signo> is in the set.
  int is_member (int signo) const;

  /// Returns a pointer to the underlying <sigset_t>.
  operator sigset_t *();

  /// Returns a copy of the underlying <sigset_t>.
  sigset_t sigset (void) const;

  /// Dump the state of an object.
  void dump (void) const;

private:
  /// Set of signals.
  sigset_t sigset_;
};

/**
 * @class PPM_Sig_Action
 *
 * @brief C++ wrapper facade for the <sigaction> struct.
 */
class PPM_Sig_Action
{
public:
  // = Initialization methods.
  /// Default constructor.  Initializes everything to 0.
  PPM_Sig_Action (void);

  /// Assigns the various fields of a <sigaction> struct but doesn't
  /// register for signal handling via the <sigaction> function.
  PPM_Sig_Action (PPM_SignalHandler handler,
                  sigset_t *sigmask = 0,
                  int flags = 0);

  /// Assigns the various fields of a <sigaction> struct but doesn't
  /// register for signal handling via the <sigaction> function.
  PPM_Sig_Action (PPM_SignalHandler handler,
                  const PPM_Sig_Set &sigmask,
                  int flags = 0);

  /**
   * Assigns the various fields of a <sigaction> struct and registers
   * the <handler> to process signal <signum> via the <sigaction>
   * function.
   */
  PPM_Sig_Action (PPM_SignalHandler handler,
                  int signum,
                  sigset_t *sigmask = 0,
                  int flags = 0);

  /**
   * Assigns the various fields of a <sigaction> struct and registers
   * the <handler> to process signal <signum> via the <sigaction>
   * function.
   */
  PPM_Sig_Action (PPM_SignalHandler handler,
                  int signum,
                  const PPM_Sig_Set &sigmask,
                  int flags = 0);


  // @@ The next two methods have a parameter as "signalss". Please do
  // not change the argument name as "signals". This causes the
  // following problem as reported by
  // <<EMAIL>>.

  // In the file Signal.h two of the functions have and argument name
  // of signals. signals is a Qt macro (to do with their meta object
  // stuff.
  // We could as well have it as "signal", but I am nost sure whether
  // that would cause a problem with something else - Bala <bala@cs>

  /**
   * Assigns the various fields of a <sigaction> struct and registers
   * the <handler> to process all <signals> via the <sigaction>
   * function.
   */
  PPM_Sig_Action (const PPM_Sig_Set &signalss,
                  PPM_SignalHandler handler,
                  const PPM_Sig_Set &sigmask,
                  int flags = 0);

  /**
   * Assigns the various fields of a <sigaction> struct and registers
   * the <handler> to process all <signals> via the <sigaction>
   * function.
   */
  PPM_Sig_Action (const PPM_Sig_Set &signalss,
                  PPM_SignalHandler handler,
                  sigset_t *sigmask = 0,
                  int flags = 0);

  /// Copy constructor.
  PPM_Sig_Action (const PPM_Sig_Action &s);

  /// Default dtor.
  ~PPM_Sig_Action (void);

  // = Signal action management.
  /// Register <this> as the current disposition and store old
  /// disposition into <oaction> if it is non-NULL.
  int register_action (int signum,
                       PPM_Sig_Action *oaction = 0);

  /// Assign the value of <oaction> to <this> and make it become the
  /// new signal disposition.
  int restore_action (int signum,
                      PPM_Sig_Action &oaction);

  /// Retrieve the current disposition into <this>.
  int retrieve_action (int signum);

  /// Set current signal action.
  void set (struct sigaction *);

  /// Get current signal action.
  struct sigaction *get (void);
//  operator PPM_SIGACTION *();

  /// Set current signal flags.
  void flags (int);

  /// Get current signal flags.
  int flags (void);

  /// Set current signal mask.
  void mask (sigset_t *);
  void mask (PPM_Sig_Set &);

  /// Get current signal mask.
  sigset_t *mask (void);

  /// Set current signal handler (pointer to function).
  void handler (PPM_SignalHandler);

  /// Get current signal handler (pointer to function).
  PPM_SignalHandler handler (void);

  /// Dump the state of an object.
  void dump (void) const;

private:
  /// Controls signal behavior.
  struct sigaction sa_;
};

/**
 * @class PPM_Sig_Guard
 *
 * @brief Hold signals in MASK for duration of a C++ statement block.
 * Note that a "0" for mask causes all signals to be held.
 */
class PPM_Sig_Guard
{
public:
  // = Initialization and termination methods.
  /// Block out signals in <mask>.  Default is to block all signals!
  PPM_Sig_Guard (PPM_Sig_Set *mask = 0);

  /// Restore blocked signals.
  ~PPM_Sig_Guard (void);

  /// Dump the state of an object.
  void dump (void) const;

private:
  /// Original signal mask.
  PPM_Sig_Set omask_;
  
  static sigset_t *default_mask_;
};

class PPM_Sig_Handler
{
public:
  virtual ~PPM_Sig_Handler(){};
  // = Registration and removal methods.
  /**
   * Add a new <PPM_Event_Handler> and a new sigaction associated with
   * <signum>.  Passes back the existing <PPM_Event_Handler> and its
   * sigaction if pointers are non-zero.  Returns -1 on failure and >=
   * 0 on success.
   */
  virtual int register_handler (int signum,
                                PPM_Event_Handler *new_sh,
                                PPM_Sig_Action *new_disp = 0,
                                PPM_Event_Handler **old_sh = 0,
                                PPM_Sig_Action *old_disp = 0);

  /**
   * Remove the <PPM_Event_Handler> currently associated with
   * <signum>.  <sigkey> is ignored in this implementation since there
   * is only one instance of a signal handler.  Install the new
   * disposition (if given) and return the previous disposition (if
   * desired by the caller).  Returns 0 on success and -1 if <signum>
   * is invalid.
   */
  virtual int remove_handler (int signum,
                              PPM_Sig_Action *new_disp = 0,
                              PPM_Sig_Action *old_disp = 0,
                              int sigkey = -1);

  // Set/get signal status.
  /// True if there is a pending signal.
  static int sig_pending (void);

  /// Reset the value of <sig_pending_> so that no signal is pending.
  static void sig_pending (int);

  // = Set/get the handler associated with a particular signal.

  /// Return the <PPM_Sig_Handler> associated with <signum>.
  virtual PPM_Event_Handler *handler (int signum);

  /// Set a new <PPM_Event_Handler> that is associated with <signum>.
  /// Return the existing handler.
  virtual PPM_Event_Handler *handler (int signum,
                                      PPM_Event_Handler *);

  /**
   * Callback routine registered with sigaction(2) that dispatches the
   * <handle_signal> method of the appropriate pre-registered
   * PPM_Event_Handler.
   */
  static void dispatch (int, siginfo_t *,
                        ucontext_t *);

  /// Dump the state of an object.
  void dump (void) const;

protected:
  // = These methods and data members are shared by derived classes.

  /**
   * Set a new <PPM_Event_Handler> that is associated with <signum>.
   * Return the existing handler.  Does not acquire any locks so that
   * it can be called from a signal handler, such as <dispatch>.
   */
  static PPM_Event_Handler *handler_i (int signum,
                                       PPM_Event_Handler *);

  /**
   * This implementation method is called by <register_handler> and
   * <dispatch>.  It doesn't do any locking so that it can be called
   * within a signal handler, such as <dispatch>.  It adds a new
   * <PPM_Event_Handler> and a new sigaction associated with <signum>.
   * Passes back the existing <PPM_Event_Handler> and its sigaction if
   * pointers are non-zero.  Returns -1 on failure and >= 0 on
   * success.
   */
  static int register_handler_i (int signum,
                                 PPM_Event_Handler *new_sh,
                                 PPM_Sig_Action *new_disp = 0,
                                 PPM_Event_Handler **old_sh = 0,
                                 PPM_Sig_Action *old_disp = 0);

  /// Check whether the SIGNUM is within the legal range of signals.
  static int in_range (int signum);

  /// Keeps track of whether a signal is pending.
  static sig_atomic_t sig_pending_;

private:
  /// Array used to store one user-defined Event_Handler for every
  /// signal.
  static PPM_Event_Handler *signal_handlers_[PPM_NSIG];
};
#if !defined (PPM_LACKS_INLINE_FUNCTIONS)
#include "./PPM_Signal.inl"
#endif /* __PPM_INLINE__ */

#endif