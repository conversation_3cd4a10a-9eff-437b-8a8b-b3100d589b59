#include "./PPM_SOCK_Dgram.h"
#include "./PPM_Log_Msg.h"
// Here's the shared open function.  Note that if we are using the
// PF_INET protocol family and the address of LOCAL == the address of
// the special variable SAP_ANY then we are going to arbitrarily bind
// to a portnumber.

int
PPM_SOCK_Dgram::shared_open (const PPM_Addr &local,
                             int protocol_family)
{
  PPM_TRACE ("PPM_SOCK_Dgram::shared_open");
  int error = 0;

  if (local == PPM_Addr::sap_any)
    {
      if (protocol_family == PF_INET)
        {
          if (PPM_OS::bind_port (this->get_handle ()) == -1)
            error = 1;
        }
    }
  else if (PPM_OS::bind (this->get_handle (),
                         PPM_reinterpret_cast (sockaddr *,
                                               local.get_addr ()),
                         local.get_size ()) == -1)
    error = 1;

  if (error != 0)
    this->close ();

  return error ? -1 : 0;
}
// this is for client udp
int 
PPM_SOCK_Dgram::open()
{
	return PPM_SOCK::open(PPM_PROTOCOL_FAMILY_INET,SOCK_DGRAM,0,0);
}
// Here's the general-purpose open routine.

int
PPM_SOCK_Dgram::open (const PPM_Addr &local,
                      int protocol_family,
                      int protocol,
                      int reuse_addr)
{
  PPM_TRACE ("PPM_SOCK_Dgram::open");

  if (local != PPM_Addr::sap_any)
    protocol_family = local.get_type ();
  else if (protocol_family == PF_UNSPEC)
    {
      protocol_family = PF_INET;
    }

  if (PPM_SOCK::open (SOCK_DGRAM,
                      protocol_family,
                      protocol,
                      reuse_addr) == -1)
    return -1;
  else
    return this->shared_open (local,
                              protocol_family);
}
// Here's the general-purpose constructor used by a connectionless
// datagram ``server''...

PPM_SOCK_Dgram::PPM_SOCK_Dgram (const PPM_Addr &local,
                                int protocol_family,
                                int protocol,
                                int reuse_addr)
{
  PPM_TRACE ("PPM_SOCK_Dgram::PPM_SOCK_Dgram");

  if (this->open (local,
                  protocol_family,
                  protocol,
                  reuse_addr) == -1)
    PPM_ERROR ((LM_ERROR,
                PPM_LIB_TEXT ("%p\n"),
                PPM_LIB_TEXT ("PPM_SOCK_Dgram")));
}

PPM_SOCK_Dgram::PPM_SOCK_Dgram (void)
{
  PPM_TRACE ("PPM_SOCK_Dgram::PPM_SOCK_Dgram");
}


PPM_SOCK_Dgram::~PPM_SOCK_Dgram (void)
{
  PPM_TRACE ("PPM_SOCK_Dgram::~PPM_SOCK_Dgram");
}

// <sendto> an N byte datagram to <addr> (connectionless version).

int
PPM_SOCK_Dgram::send (const void *buf, 
		      size_t n, 
		      const PPM_Addr &addr, 
		      int flags) const
{
  PPM_TRACE ("PPM_SOCK_Dgram::send");
  sockaddr *saddr = (sockaddr *) addr.get_addr ();
  int len = addr.get_size ();
  return PPM_OS::sendto (this->get_handle (), 
			 (const char *) buf,
                         n,
                         flags, 
			 (struct sockaddr *) saddr,
                         len);
}

// <recvfrom> an n byte datagram (connectionless version).

int
PPM_SOCK_Dgram::recv (void *buf, 
		      size_t n, 
		      PPM_Addr &addr, 
		      int flags) const
{
  PPM_TRACE ("PPM_SOCK_Dgram::recv");
  sockaddr *saddr = (sockaddr *) addr.get_addr ();
  int addr_len = addr.get_size ();

  int status = PPM_OS::recvfrom (this->get_handle (), 
				     (char *) buf,
                                     n,
                                     flags, 
				     (sockaddr *) saddr,
                                     &addr_len);
  addr.set_size (addr_len);
  return status;
}
