/* -*- C++ -*- */
// Addr.i,v 4.5 2002/04/10 18:18:16 ossama Exp

// Addr.i

// Return the address of the address.

PPM_INLINE void *
PPM_Addr::get_addr (void) const
{
  return 0;
}

PPM_INLINE void 
PPM_Addr::set_addr (void *, int)
{
}

PPM_INLINE int 
PPM_Addr::operator == (const PPM_Addr &sap) const
{
  return (sap.addr_type_ == this->addr_type_ &&
	  sap.addr_size_ == this->addr_size_   );
}

PPM_INLINE int
PPM_Addr::operator != (const PPM_Addr &sap) const
{
  return (sap.addr_type_ != this->addr_type_ ||
	  sap.addr_size_ != this->addr_size_   );
}

// Return the size of the address. 

PPM_INLINE int
PPM_Addr::get_size (void) const
{
  return this->addr_size_;
}

// Sets the size of the address. 

PPM_INLINE void
PPM_Addr::set_size (int size)
{
  this->addr_size_ = size;
}

// Return the type of the address. 

PPM_INLINE int
PPM_Addr::get_type (void) const
{
  return this->addr_type_;
}

// Set the type of the address. 

PPM_INLINE void
PPM_Addr::set_type (int type)
{
  this->addr_type_ = type;
}

PPM_INLINE unsigned long 
PPM_Addr::hash (void) const
{
  return 0;
}
