// PPM_Client.h: interface for the PPM_Client class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_PPM_CLIENT_H__261F698A_834E_40EC_95D6_18B7CFB101F0__INCLUDED_)
#define AFX_PPM_CLIENT_H__261F698A_834E_40EC_95D6_18B7CFB101F0__INCLUDED_

#pragma once

#include "./PPM_Tcp_Client.h"
#include "../stdclass/StdHeader.h"
#include "./PPM_Public_Var.h"
#include "./PPM_Reactor.h"
#include "./PPM_CONN_Actor.h"
#define MAX_CONN 50 

#define NEW_TCP_CLIENT(ip, port, pSockType, pActor, eNodeType) \
	pSockType = PPM_Client<PPM_Tcp_Client>::Instance()->CreateClient(ip, port, pActor, eNodeType);
#define PPM_CLIENT PPM_Client<PPM_Tcp_Client>::Instance()

/**
/////////////////////////////////////////////
PPM_Client使用

//调用客户端，首先必须声明一个客户端处理类，从PPM_Client_Deal_Base继承,重载DealData和InvalidNotify两个函数，DealData是在收到数据后的处理，InvalidNotify是在socket关闭时的处理，包括主动的和被动的：

class CClientDealTest : public PPM_Client_Deal_Base 
{
public:
	CServerDealTest();
	virtual ~CServerDealTest();
	virtual void DealData(const BYTE* const pData, const int nCount){
		…;
		return;
	};
	virtual void InvalidNotify(){
		…;
        PPM_Client_Deal_Base：：InvalidNotify();
	}
};

//然后：
CServerDealTest  tm_clientactor ;
int res = 0;
	// res创建的返回值-1失败，1成功
	// ip：字符串，port端口，2表示缓冲类型
	// CreateClient还有可调的参数有：设置是否重连，设置是否处理完接受的数据，设置
	// 对于同一个服务端口ip只能建一个连接，设置缓冲类型及大小
	res = PPM_CLIENT->CreateClient(ip, port, &tm_clientactor, 2);
	if(res < 0)
	{ return;}
	…
	tm_clientactor.SendData(buf,size);
	…
	//主动关闭socket连接
	tm_clientactor.CloseClient();

/////////////////////////////////////////////

*/
template<class Conn_Type>
class PPM_Client 
{
public:
	PPM_Client(void)
	:m_Lock(){
#ifdef WIN32
		//首先初始化通讯的环境
		WSADATA wsaData;
		WORD wVersionRequested = MAKEWORD_NETSEQ(2, 2);
		int nResult = WSAStartup(wVersionRequested, &wsaData);
		if(nResult != 0){
			PPM_SOCKLOG((LM_ERROR,"Client WSAStartup Failed! %m\n"));
			return;
		} 
		//如果初始化的不是我们想要的版本,退出
		if (LOBYTE(wsaData.wVersion) != 2 || HIBYTE(wsaData.wVersion) != 2)
		{
		   WSACleanup();
		   PPM_SOCKLOG((LM_ERROR,"Client WSADATA version is%d.%d,is error! \n",
			   HIBYTE(wsaData.wVersion),
			   LOBYTE(wsaData.wVersion)));
		   return ;
		}
#endif
		m_pClient_Actor = NULL;
		m_needdel = 0;
		m_pClient_Actor = new PPM_Tcp_Client_Actor;
		m_needdel = 1;
	};
	
	~PPM_Client(void){
//		ReleaseAll();
		if( (NULL != m_pClient_Actor) && 
			(1 == m_needdel) )
		{
			delete m_pClient_Actor;
			m_pClient_Actor = NULL;
		}
#ifdef WIN32
		//清除通讯的环境
		if(0 > WSACleanup()){
			PPM_SOCKLOG((LM_ERROR,"Client Error ocurr when WSACleanup! %m \n"));
		}
#endif
	};

	/// 客户端单实例
	static PPM_Client<Conn_Type>* Instance(void);
    static void ExitInstance(void);

	/// 创建客户端
	int CreateClient(
		const char* const server_ip,
		const u_short server_port,
		PPM_Client_Deal_Base* const pActor,
		BYTE bReConnect = 0,//是否需要重连,默认是不需要
		BYTE bNoLoss = 0,//是否需要处理完接收到的数据,1表示收处理,2表示发处理,3表示收发都处理,0表示都不处理
		BYTE bIsExclusive = 0,//标示一个进程连接服务端是否只能连一个,默认不是
		const BYTE eRNodeType = CNodeMng::BothMode,
		const BYTE eSNodeType = CNodeMng::BothMode,
		const int nRecvNode = STD_NODE_COUNT, 
		const int nSendNode = STD_NODE_COUNT,
		const BYTE bSendHeadFlag = 1, //发送是否加AA55标示
		const BYTE bRecvHeadFlag = 1,  //接收是否加AA55标示
		PPM_Reactor* pReactorClient = NULL, //是否采用新的反应器
		BYTE isNeedDelReactor = 0,  //新的反应器是否需要删除
		const char* const local_ip = NULL
		);
	/// 释放客户端
	void ReleaseClient(stru_client* const pClient);

private:
	void ReleaseAll(void);
 	CStdLock m_Lock;
	PPM_Tcp_Client_Actor* m_pClient_Actor;
	BOOL m_needdel;
	PPM_hthread_t loopthr_handle_;
// 	BYTE m_IsInited;
//	vector<stru_client*> m_vecpConn;
};

template<typename Conn_Type>
inline PPM_Client<Conn_Type>* PPM_Client<Conn_Type>::Instance(void)
{
	static PPM_Client<Conn_Type>* theInst = NULL;
	if(theInst == NULL){
		theInst = new PPM_Client<Conn_Type>;
	}
	return theInst;
};

template<typename Conn_Type>
inline void PPM_Client<Conn_Type>::ExitInstance(void)
{
	static PPM_Client<Conn_Type>* theInst = NULL;
    if(theInst != NULL){
		delete theInst;
	}
};
/**
此函数用来创建新的客户端连接
输入：
		const char* const server_ip  : 需要连接的服务端ip地址
		const u_short server_port    : 服务端的端口号
		PPM_Client_Deal_Base* const pActor : 返回给客户端调用的处理类
		BYTE bReConnect : 是否重连,1表示重连，0不重连
		BYTE bNoLoss: 是否处理完接受到的数据1表示收处理,2表示发处理,3表示收发都处理,0表示都不处理
		BYTE bIsExclusive :是否对于同一服务ip和端口只能连一次
		const BYTE eRNodeType: 收缓冲类型
		const BYTE eSNodeType: 发缓冲类型
		const int nRecvNode: 收缓冲节点数
		const int nSendNode :发缓冲节点数
注:缓冲类型:
   CNodeMng::BuffMode 内存模式,对于收数据缓冲而言会注册到反应器,可以收数据,
   CNodeMng::FileMode 文件模式,直接通过文件进行缓存,不通过内存,一般不用
   CNodeMng::BothMode 两种模式,如果收缓冲定义为此模式,则会注册到反应器,收数据,先
                                 内存缓冲,再文件缓冲
   CNodeMng::NullMode 无缓冲,对于收缓冲定义这种情况的话,是不能够触发收数据的,适合
                               那种只发,不收的那种情况,socket为阻塞方式,不注册到反应器
	缓冲节点数:是针对有内存模式的情况,1个节点是8k
*/
template<class Conn_Type>
inline int PPM_Client<Conn_Type>::CreateClient(
		const char* const server_ip,
		const u_short server_port,
		PPM_Client_Deal_Base* const pActor,
		BYTE bReConnect ,
		BYTE bNoLoss,
		BYTE bIsExclusive,
		const BYTE eRNodeType,
		const BYTE eSNodeType,
		const int nRecvNode, 
		const int nSendNode,
		const BYTE bSendHeadFlag, //发送是否加AA55标示
		const BYTE bRecvHeadFlag, //接收是否加AA55标示
		PPM_Reactor* pReactorClient,//是否采用新的反应器
		BYTE isNeedDelReactor,//新的反应器是否需要删除
		const char* const local_ip
		)
{
	if (m_pClient_Actor->GetVecSize() >= MAX_CONN)
	{
		PPM_DEBUG((LM_ERROR,"PPM_Client::CreateTcpClient:maybe you have create too many connections or \
			forget to release unuseful connections."));

		return -1;
	}

	char pstr_ip[50];
	PPM_OS::snprintf(pstr_ip,sizeof(pstr_ip),"%s",server_ip);

//	if(eSNodeType == CNodeMng::NullMode){
//		bReConnect = 0;
//	}
	//新建连接的时候新建一个安全缓冲传入
	//先根据ip和端口察看是否已有连接
	PPM_SOCK_Buffer* sock_buffer = NULL;
	Conn_Type* pClient = NULL;
//	PPM_Reactor* preactor = PPM_Reactor::instance();
	BOOL isReused = FALSE;
	
	stru_client * reused_client = NULL;
	bool bneednewflag = false;
	if(0 == bIsExclusive){
		reused_client = NULL;
	}else{
		reused_client = m_pClient_Actor->IsSocketBufferExists(pstr_ip,server_port);
	}
	if(NULL != reused_client){
		isReused = TRUE;
	}
	if(FALSE == isReused){
		//没有重用
		bneednewflag = true;
	}
	else{
		//有重用
		sock_buffer = reused_client->psockbuffer;
		if(1 == sock_buffer->GetExclusiveFlag()){
			//同一个ip端口是唯一的
			//如果原先的连接是正常的
			if(1 == reused_client->bIsClientValid){
				//直接推出并提示
				PPM_SOCKLOG((LM_ERROR,"connect is exclusive,but client already exists!\n"));
				return -1;
			}else{
				//如果不正常，说明此时正等待收数据线程处理完毕
				//先处理掉原先的对象
				reused_client->pconn_type->ServerReset(NULL) ;
				//创建一个新的tcpclient对象，使用旧的buffer
			}

		}
		else{
			bneednewflag = true;
		}
	}
    //建立全新的
	if(true == bneednewflag){
			//创建新的buffer
		sock_buffer = new PPM_SOCK_Buffer((char*)server_ip,server_port,nRecvNode,eRNodeType,nSendNode,eSNodeType,bIsExclusive,bNoLoss);
		if(NULL == sock_buffer){
			PPM_SOCKLOG((LM_ERROR,"create sock buffer error %m\n"));
			return -1;
		}
// 		char tm_strip[20];
// 		PPM_OS::sprintf(tm_strip,"%s",server_ip);
// 		PPM_OS::memcpy(sock_buffer->m_pipaddress,server_ip,sizeof(sock_buffer->m_pipaddress));
// 		sock_buffer->m_portNo = server_port;
 
		//新建通讯对象
		pClient = new Conn_Type(server_ip, server_port,sock_buffer,pReactorClient,bReConnect,pActor,
								PPM_DEFAULT_THREAD_PRIORITY,
								PPM_DEFAULT_THREAD_PRIORITY,
								PPM_DEFAULT_THREAD_PRIORITY,		
								bSendHeadFlag,
								bRecvHeadFlag,
								isNeedDelReactor,local_ip
									);
		if(NULL == pClient){
			PPM_SOCKLOG((LM_ERROR,"create tcp client error %m\n"));
			return -1;
		}
		if( FALSE == pClient->IsIniSucceed()){
			//初始化失败
			//删除刚刚申请的资源，因为PPM_Tcp_Client析购是私有的，所以转了一个弯删除，
			//不允许外部直接调用delete删除
			if(m_pClient_Actor != NULL){
				m_pClient_Actor->DeleteClient(pClient);
			}
			delete sock_buffer;
			PPM_SOCKLOG((LM_ERROR,"Create Client error!\n"));
			return -1;
		}
		pClient->SetClientActor(m_pClient_Actor);

		if (pClient != NULL)
		{
			pActor->SetTcpClient(pClient);
			stru_client* tm_pstru_client = new stru_client;
			tm_pstru_client->pconn_type = pClient;
			tm_pstru_client->psockbuffer = sock_buffer;
			tm_pstru_client->bIsClientValid = 1;
			tm_pstru_client->pClientDeal = pActor;
			m_pClient_Actor->AddClient(tm_pstru_client);
			pClient->SetClientStru(tm_pstru_client);
		}
	}
	//只建立新的tcpclient
	else{
		//新建通讯对象
		pClient = new Conn_Type(server_ip, server_port,sock_buffer,NULL,bReConnect,pActor);
		if(NULL == pClient){
			PPM_SOCKLOG((LM_ERROR,"create tcp client error %m\n"));
			return -1;
		}
		pClient->SetClientActor(m_pClient_Actor);
		pClient->SetClientStru(reused_client);
        m_pClient_Actor->ResetSingleClient(reused_client,pActor);
		pActor->SetTcpClient(pClient);

	}

	pClient->Registe();
	return 1;
};

template<class Conn_Type>
inline void PPM_Client<Conn_Type>::ReleaseClient(stru_client* const pClient)
{
	if(NULL != m_pClient_Actor){
		m_pClient_Actor->ReleaseClient(pClient);
	}
	else{
		PPM_SOCKLOG((LM_ERROR,"Client_Actor is null when releaseclient!\n"));
	}
}

template<class Conn_Type>
inline void PPM_Client<Conn_Type>::ReleaseAll(void)
{
	if(NULL != m_pClient_Actor){
		m_pClient_Actor->ReleaseAll();
	}
	else{
		PPM_SOCKLOG((LM_ERROR,"Client_Actor is null when release all client!\n"));
	}
}

#endif // !defined(AFX_PPM_CLIENT_H__261F698A_834E_40EC_95D6_18B7CFB101F0__INCLUDED_)
