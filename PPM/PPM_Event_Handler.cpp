// Event_Handler.cpp
// Event_Handler.cpp,v 4.23 2002/12/08 07:18:11 nanbor Exp

#include "./PPM_Event_Handler.h"
#include "./PPM_OS_Error.h"
#include "./PPM_Reactor.h"

// Implement conceptually abstract virtual functions in the base class
// so derived classes don't have to implement unused ones.

PPM_Event_Handler::PPM_Event_Handler (PPM_Reactor *r,
                                      int p)
  : priority_ (p),
    reactor_ (r)
{
  // PPM_TRACE ("PPM_Event_Handler::PPM_Event_Handler");
}

PPM_Event_Handler::~PPM_Event_Handler (void)
{
  // PPM_TRACE ("PPM_Event_Handler::~PPM_Event_Handler");
  if (this->reactor_ != 0)
    {
      PPM_Errno_Guard guard (errno);     // purge may get ENOTSUP
//      this->reactor_->purge_pending_notifications (this);
    }
}

// Gets the file descriptor associated with this I/O device.

PPM_HANDLE
PPM_Event_Handler::get_handle (void) const
{
  PPM_TRACE ("PPM_Event_Handler::get_handle");
  return PPM_INVALID_HANDLE;
}

// Sets the file descriptor associated with this I/O device.

void
PPM_Event_Handler::set_handle (PPM_HANDLE)
{
  PPM_TRACE ("PPM_Event_Handler::set_handle");
}

// Gets the priority of this handler.

int
PPM_Event_Handler::priority (void) const
{
  PPM_TRACE ("PPM_Event_Handler::priority");
  return this->priority_;
}

// Sets the priority

void
PPM_Event_Handler::priority (int priority)
{
  PPM_TRACE ("PPM_Event_Handler::priority");
  this->priority_ = priority;
}

// Called when the object is about to be removed from the Dispatcher
// tables.

int
PPM_Event_Handler::handle_close (PPM_HANDLE, PPM_Reactor_Mask)
{
  PPM_TRACE ("PPM_Event_Handler::handle_close");
  return -1;
}

// Called when input becomes available on fd.

int
PPM_Event_Handler::handle_input (PPM_HANDLE)
{
  PPM_TRACE ("PPM_Event_Handler::handle_input");
  return -1;
}

// Called when output is possible on fd.

int
PPM_Event_Handler::handle_output (PPM_HANDLE)
{
  PPM_TRACE ("PPM_Event_Handler::handle_output");
  return -1;
}

// Called when urgent data is available on fd.

int
PPM_Event_Handler::handle_exception (PPM_HANDLE)
{
  PPM_TRACE ("PPM_Event_Handler::handle_exception");
  return -1;
}


int
PPM_Event_Handler::resume_handler (void)
{
  PPM_TRACE ("PPM_Event_Handler::resume_handler");

  // Return a default value and allow the reactor to take care of
  // resuming the handler
  return PPM_Event_Handler::PPM_REACTOR_RESUMES_HANDLER;
}

void
PPM_Event_Handler::reactor (PPM_Reactor *reactor)
{
  PPM_TRACE ("PPM_Event_Handler::reactor");
  this->reactor_ = reactor;
}

PPM_Reactor *
PPM_Event_Handler::reactor (void) const
{
  PPM_TRACE ("PPM_Event_Handler::reactor");
  return this->reactor_;
}
// Called when a registered signal occurs.

int
PPM_Event_Handler::handle_signal (int, siginfo_t *, ucontext_t *)
{
  PPM_TRACE ("PPM_Event_Handler::handle_signal");
  return -1;
}