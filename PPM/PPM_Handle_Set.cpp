// Handle_Set.cpp
// Handle_Set.cpp,v 4.41 2002/09/01 08:16:44 jwillemsen Exp

#include "./PPM_Handle_Set.h"

#if defined (PPM_LACKS_INLINE_FUNCTIONS)
#include "./PPM_Handle_Set.inl"
#endif /* __PPM_INLINE__ */

void
PPM_Handle_Set::dump (void) const
{
}

// Table that maps bytes to counts of the enabled bits in each value
// from 0 to 255,
//
// nbits_[0] == 0
//
// because there are no bits enabled for the value 0.
//
// nbits_[5] == 2
//
// because there are 2 bits enabled in the value 5, i.e., it's
// 101 in binary.

const char PPM_Handle_Set::nbits_[256] =
{
  0, 1, 1, 2, 1, 2, 2, 3, 1, 2, 2, 3, 2, 3, 3, 4,
  1, 2, 2, 3, 2, 3, 3, 4, 2, 3, 3, 4, 3, 4, 4, 5,
  1, 2, 2, 3, 2, 3, 3, 4, 2, 3, 3, 4, 3, 4, 4, 5,
  2, 3, 3, 4, 3, 4, 4, 5, 3, 4, 4, 5, 4, 5, 5, 6,
  1, 2, 2, 3, 2, 3, 3, 4, 2, 3, 3, 4, 3, 4, 4, 5,
  2, 3, 3, 4, 3, 4, 4, 5, 3, 4, 4, 5, 4, 5, 5, 6,
  2, 3, 3, 4, 3, 4, 4, 5, 3, 4, 4, 5, 4, 5, 5, 6,
  3, 4, 4, 5, 4, 5, 5, 6, 4, 5, 5, 6, 5, 6, 6, 7,
  1, 2, 2, 3, 2, 3, 3, 4, 2, 3, 3, 4, 3, 4, 4, 5,
  2, 3, 3, 4, 3, 4, 4, 5, 3, 4, 4, 5, 4, 5, 5, 6,
  2, 3, 3, 4, 3, 4, 4, 5, 3, 4, 4, 5, 4, 5, 5, 6,
  3, 4, 4, 5, 4, 5, 5, 6, 4, 5, 5, 6, 5, 6, 6, 7,
  2, 3, 3, 4, 3, 4, 4, 5, 3, 4, 4, 5, 4, 5, 5, 6,
  3, 4, 4, 5, 4, 5, 5, 6, 4, 5, 5, 6, 5, 6, 6, 7,
  3, 4, 4, 5, 4, 5, 5, 6, 4, 5, 5, 6, 5, 6, 6, 7,
  4, 5, 5, 6, 5, 6, 6, 7, 5, 6, 6, 7, 6, 7, 7, 8};

// Constructor, initializes the bitmask to all 0s.

PPM_Handle_Set::PPM_Handle_Set (void)
{
  PPM_TRACE ("PPM_Handle_Set::PPM_Handle_Set");
  this->reset ();
}

PPM_Handle_Set::PPM_Handle_Set (const PPM_FD_SET_TYPE &fd_mask)
{
  PPM_TRACE ("PPM_Handle_Set::PPM_Handle_Set");
  this->reset ();
  PPM_OS::memcpy ((void *) &this->mask_,
                  (void *) &fd_mask,
                  sizeof this->mask_);
#if !defined WIN32
  this->sync (PPM_Handle_Set::MAXSIZE);
#endif 
}

// Counts the number of bits enabled in N.  Uses a table lookup to
// speed up the count.

int
PPM_Handle_Set::count_bits (u_long n)
{
   return (PPM_Handle_Set::nbits_[n & 0xff]
          + PPM_Handle_Set::nbits_[(n >> 8) & 0xff]
          + PPM_Handle_Set::nbits_[(n >> 16) & 0xff]
          + PPM_Handle_Set::nbits_[(n >> 24) & 0xff]);
}



void
PPM_Handle_Set::sync (PPM_HANDLE max)
{
#if !defined WIN32
  fd_mask *maskp = (fd_mask *)(this->mask_.fds_bits);
  this->size_ = 0;

  for (int i = PPM_DIV_BY_WORDSIZE (max - 1);
       i >= 0;
       i--)
    this->size_ += PPM_Handle_Set::count_bits (maskp[i]);

  this->set_max (max);
#endif
}

// Resets the MAX_FD after a clear of the original MAX_FD.

void
PPM_Handle_Set::set_max (PPM_HANDLE current_max)
{
#if !defined WIN32
  fd_mask * maskp = (fd_mask *)(this->mask_.fds_bits);

  if (this->size_ == 0)
    this->max_handle_ = PPM_INVALID_HANDLE;
  else
    {
      int i;

      for (i = PPM_DIV_BY_WORDSIZE (current_max - 1);
           maskp[i] == 0;
           i--)
        continue;

      this->max_handle_ = PPM_MULT_BY_WORDSIZE (i);
      for (fd_mask val = maskp[i];
           (val & ~1) != 0; // This obscure code is needed since "bit 0" is in location 1...
           val = (val >> 1) & PPM_MSB_MASK)
        this->max_handle_++;
    }

  // Do some sanity checking...
  if (this->max_handle_ >= PPM_Handle_Set::MAXSIZE)
    this->max_handle_ = PPM_Handle_Set::MAXSIZE - 1;
#endif
}
void
PPM_Handle_Set_Iterator::dump (void) const
{
  PPM_TRACE ("PPM_Handle_Set_Iterator::dump");

//  PPM_DEBUG ((LM_DEBUG, PPM_BEGIN_DUMP, this));
//#if defined(WIN32) || !defined(PPM_HAS_BIG_FD_SET)
//  PPM_DEBUG ((LM_DEBUG, PPM_LIB_TEXT ("\nhandle_index_ = %d"), this->handle_index_));
//#elif defined(PPM_HAS_BIG_FD_SET)
//  PPM_DEBUG ((LM_DEBUG, PPM_LIB_TEXT ("\nword_max_ = %d"), this->word_max_));
//  PPM_DEBUG ((LM_DEBUG, PPM_LIB_TEXT ("\nword_val_ = %d"), this->word_val_));
//#endif
//  PPM_DEBUG ((LM_DEBUG, PPM_LIB_TEXT ("\nword_num_ = %d"), this->word_num_));
//   PPM_DEBUG ((LM_DEBUG, PPM_END_DUMP));
}

PPM_HANDLE
PPM_Handle_Set_Iterator::operator () (void)
{
  PPM_TRACE ("PPM_Handle_Set_Iterator::operator");
#if defined WIN32
  if (this->handle_index_ < this->handles_.mask_.fd_count)
    // Return the handle and advance the iterator.
    return (PPM_HANDLE) this->handles_.mask_.fd_array[this->handle_index_++];
  else
    return PPM_INVALID_HANDLE;

#elif !defined (PPM_HAS_BIG_FD_SET) /* !WIN32 */
  // No sense searching further than the max_handle_ + 1;
  PPM_HANDLE maxhandlep1 = this->handles_.max_handle_ + 1;

  // HP-UX 11 plays some games with the fd_mask type - fd_mask is
  // defined as an int_32t, but the fds_bits is an array of longs.
  // This makes plainly indexing through the array by hand tricky,
  // since the FD_* macros treat the array as int32_t.  So the bits
  // are in the right place for int32_t, even though the array is
  // long.  This, they say, is to preserve the same in-memory layout
  // for 32-bit and 64-bit processes.  So, we play the same game as
  // the FD_* macros to get the bits right.  On all other systems,
  // this amounts to practically a NOP, since this is what would have
  // been done anyway, without all this type jazz.
  fd_mask * maskp = (fd_mask *)(this->handles_.mask_.fds_bits);

  if (this->handle_index_ >= maxhandlep1)
    // We've seen all the handles we're interested in seeing for this
    // iterator.
    return PPM_INVALID_HANDLE;
  else
    {
      PPM_HANDLE result = this->handle_index_;

      // Increment the iterator and advance to the next bit in this
      // word.
      this->handle_index_++;
#  if defined (PPM_PSOS)
      this->word_val_ = (this->word_val_ << 1);
#  else
      this->word_val_ = (this->word_val_ >> 1) & PPM_MSB_MASK;
#  endif /* PPM_PSOS */

      // If we've examined all the bits in this word, we'll go onto
      // the next word.

      if (this->word_val_ == 0)
        {
          // Start the handle_index_ at the beginning of the next word
          // and then loop until we've found the first non-zero bit or
          // we run past the <maxhandlep1> of the bitset.

          for (this->handle_index_ = PPM_MULT_BY_WORDSIZE(++this->word_num_);
               this->handle_index_ < maxhandlep1
                 && maskp[this->word_num_] == 0;
               this->word_num_++)
            this->handle_index_ += PPM_Handle_Set::WORDSIZE;

          // If the bit index becomes >= the maxhandlep1 that means
          // there weren't any more bits set that we want to consider.
          // Therefore, we'll just store the maxhandlep1, which will
          // cause <operator()> to return <PPM_INVALID_HANDLE>
          // immediately next time it's called.
          if (this->handle_index_ >= maxhandlep1)
            {
              this->handle_index_ = maxhandlep1;
              return result;
            }
          else
            // Load the bits of the next word.
            this->word_val_ = maskp[this->word_num_];
        }

      // Loop until we get <word_val_> to have its least significant
      // bit enabled, keeping track of which <handle_index> this
      // represents (this information is used by subsequent calls to
      // <operator()>).

#  if defined (PPM_PSOS) // bits are in reverse order, MSB (sign bit) = bit 0.
      for (;
           this->word_val_ > 0;
           this->word_val_ = (this->word_val_ << 1))
        this->handle_index_++;
#  else
      for (;
           PPM_BIT_DISABLED (this->word_val_, 1);
           this->handle_index_++)
        this->word_val_ = (this->word_val_ >> 1) & PPM_MSB_MASK;
#  endif /* PPM_PSOS */

      return result;
    }
#else /* !PPM_HAS_BIG_FD_SET */
   // Find the first word in fds_bits with bit on
   register u_long lsb = this->word_val_;

   if (lsb == 0)
     {
       do
         {
           // We have exceeded the word count in Handle_Set?
           if (++this->word_num_ >= this->word_max_)
             return PPM_INVALID_HANDLE;

           lsb = this->handles_.mask_.fds_bits[this->word_num_];
         }
       while (lsb == 0);

       // Set index to word boundary.
       this->handle_index_ = PPM_MULT_BY_WORDSIZE(this->word_num_);

       // Put new word_val.
       this->word_val_ = lsb;

       // Find the least significative bit.
       lsb &= ~(lsb - 1);

       // Remove least significative bit.
       this->word_val_ ^= lsb;

       // Save to calculate bit distance.
       this->oldlsb_ = lsb;

       // Move index to least significative bit.
       while (lsb >>= 1)
         this->handle_index_++;
     }
   else
     {
        // Find the least significative bit.
        lsb &= ~(lsb - 1);

        // Remove least significative bit.
        this->word_val_ ^= lsb;

        register u_long n = lsb - this->oldlsb_;

        // Move index to bit distance between new lsb and old lsb.
        do
          {
            this->handle_index_++;
            n &= n >> 1;
          }
        while (n != 0);

        this->oldlsb_ = lsb;
      }

   return this->handle_index_;
#endif /* WIN32 */
}

void
PPM_Handle_Set_Iterator::operator++ (void)
{
  PPM_TRACE ("PPM_Handle_Set_Iterator::operator++");

  // This is now a no-op.
}

PPM_Handle_Set_Iterator::PPM_Handle_Set_Iterator (const PPM_Handle_Set &hs)
  : handles_ (hs),
#if !defined (PPM_HAS_BIG_FD_SET) || defined WIN32
    handle_index_ (0),
    word_num_ (-1)
#elif defined (PPM_HAS_BIG_FD_SET)
    oldlsb_ (0),
    word_max_ (hs.max_handle_ == PPM_INVALID_HANDLE
               ? 0
               : ((PPM_DIV_BY_WORDSIZE (hs.max_handle_)) + 1))
#endif /* PPM_HAS_BIG_FD_SET */
{
  PPM_TRACE ("PPM_Handle_Set_Iterator::PPM_Handle_Set_Iterator");
#if !defined WIN32 && !defined (PPM_HAS_BIG_FD_SET)
  // No sense searching further than the max_handle_ + 1;
  PPM_HANDLE maxhandlep1 =
    this->handles_.max_handle_ + 1;

  fd_mask *maskp =
    (fd_mask *)(this->handles_.mask_.fds_bits);

  // Loop until we've found the first non-zero bit or we run past the
  // <maxhandlep1> of the bitset.
  while (this->handle_index_ < maxhandlep1
         && maskp[++this->word_num_] == 0)
    this->handle_index_ += PPM_Handle_Set::WORDSIZE;

  // If the bit index becomes >= the maxhandlep1 that means there
  // weren't any bits set.  Therefore, we'll just store the
  // maxhandlep1, which will cause <operator()> to return
  // <PPM_INVALID_HANDLE> immediately.
  if (this->handle_index_ >= maxhandlep1)
    this->handle_index_ = maxhandlep1;
  else
    // Loop until we get <word_val_> to have its least significant bit
    // enabled, keeping track of which <handle_index> this represents
    // (this information is used by <operator()>).
#  if defined (PPM_PSOS) // bits are in reverse order, MSB (sign bit) = bit 0.
    for (this->word_val_ = maskp[this->word_num_];
         this->word_val_ > 0;
         this->word_val_ = (this->word_val_ << 1))
      this->handle_index_++;
#  else
    for (this->word_val_ = maskp[this->word_num_];
         PPM_BIT_DISABLED (this->word_val_, 1)
           && this->handle_index_ < maxhandlep1;
         this->handle_index_++)
      this->word_val_ = (this->word_val_ >> 1) & PPM_MSB_MASK;
#  endif /* PPM_PSOS */
#elif !defined (WIN32) && defined (PPM_HAS_BIG_FD_SET)
    if (this->word_max_==0)
      {
        this->word_num_ = -1;
        this->word_val_ = 0;
      }
    else
      {
        this->word_num_ =
          PPM_DIV_BY_WORDSIZE (this->handles_.min_handle_) - 1;
        this->word_val_ = 0;
      }
#endif /* !WIN32 && !PPM_HAS_BIG_FD_SET */
}


void
PPM_Handle_Set_Iterator::reset_state (void)
{
  PPM_TRACE ("PPM_Handle_Set_Iterator::reset_state");

#if !defined (PPM_HAS_BIG_FD_SET) || defined (WIN32)
  this->handle_index_  = 0;
  this->word_num_ = -1;
#elif defined (PPM_HAS_BIG_FD_SET)
  this->oldlsb_ = 0;
  this->word_max_  =
    this->handles_.max_handle_ == PPM_INVALID_HANDLE  ? 0
    : ((PPM_DIV_BY_WORDSIZE (this->handles_.max_handle_)) + 1);
#endif /* PPM_HAS_BIG_FD_SET */

#if !defined (WIN32) && !defined (PPM_HAS_BIG_FD_SET)
  // No sense searching further than the max_handle_ + 1;
  PPM_HANDLE maxhandlep1 =
    this->handles_.max_handle_ + 1;

  fd_mask *maskp =
    (fd_mask *)(this->handles_.mask_.fds_bits);

  // Loop until we've found the first non-zero bit or we run past the
  // <maxhandlep1> of the bitset.
  while (this->handle_index_ < maxhandlep1
         && maskp[++this->word_num_] == 0)
    this->handle_index_ += PPM_Handle_Set::WORDSIZE;

  // If the bit index becomes >= the maxhandlep1 that means there
  // weren't any bits set.  Therefore, we'll just store the
  // maxhandlep1, which will cause <operator()> to return
  // <PPM_INVALID_HANDLE> immediately.
  if (this->handle_index_ >= maxhandlep1)
    this->handle_index_ = maxhandlep1;
  else
    // Loop until we get <word_val_> to have its least significant bit
    // enabled, keeping track of which <handle_index> this represents
    // (this information is used by <operator()>).
#  if defined (PPM_PSOS) // bits are in reverse order, MSB (sign bit) = bit 0.
    for (this->word_val_ = maskp[this->word_num_];
         this->word_val_ > 0;
         this->word_val_ = (this->word_val_ << 1))
      this->handle_index_++;
#  else
    for (this->word_val_ = maskp[this->word_num_];
         PPM_BIT_DISABLED (this->word_val_, 1)
           && this->handle_index_ < maxhandlep1;
         this->handle_index_++)
      this->word_val_ = (this->word_val_ >> 1) & PPM_MSB_MASK;
#  endif /* PPM_PSOS */
#elif !defined (WIN32) && defined (PPM_HAS_BIG_FD_SET)
    if (this->word_max_==0)
      {
        this->word_num_ = -1;
        this->word_val_ = 0;
      }
    else
      {
        this->word_num_ =
          PPM_DIV_BY_WORDSIZE (this->handles_.min_handle_) - 1;
        this->word_val_ = 0;
      }
#endif /* !WIN32 && !PPM_HAS_BIG_FD_SET */
}
