
PPM_INLINE void
PPM_Handle_Set::reset (void)
{
  this->max_handle_ =
    PPM_INVALID_HANDLE;
  this->size_ = 0;
  // #if !defined (PPM_HAS_BIG_FD_SET)      Why is this here?  -<PERSON>
  FD_ZERO (&this->mask_);
  // #endif /* PPM_HAS_BIG_FD_SET */
}

// Returns the number of the large bit.

PPM_INLINE PPM_HANDLE
PPM_Handle_Set::max_set (void) const
{
  return this->max_handle_;
}

// Checks whether handle is enabled.

PPM_INLINE int
PPM_Handle_Set::is_set (PPM_HANDLE handle) const
{
  return FD_ISSET (handle,
                   &this->mask_);
}

// Enables the handle.

PPM_INLINE void
PPM_Handle_Set::set_bit (PPM_HANDLE handle)
{
#ifdef WIN32
  if ((handle != PPM_INVALID_HANDLE)
      && (!this->is_set (handle)))
    {
      FD_SET ((SOCKET) handle,
              &this->mask_);
      this->size_++;
    }
#else
      FD_SET (handle,
              &this->mask_);
      this->size_++;

      if (handle > this->max_handle_)
        this->max_handle_ = handle;
#endif
}

// Disables the handle.

PPM_INLINE void
PPM_Handle_Set::clr_bit (PPM_HANDLE handle)
{

  if ((handle != PPM_INVALID_HANDLE) &&
      (this->is_set (handle)))
    {
      FD_CLR ((PPM_SOCKET) handle,
              &this->mask_);
      this->size_--;
    }
#if !defined WIN32
      if (handle == this->max_handle_)
        this->set_max (this->max_handle_);
#endif 
}

// Returns a count of the number of enabled bits.

PPM_INLINE int
PPM_Handle_Set::num_set (void) const
{
#ifdef WIN32
  return this->mask_.fd_count;
#else
	return this->size_;
#endif
}

// Returns a pointer to the underlying fd_set.

PPM_INLINE
PPM_Handle_Set::operator fd_set *()
{
  if (this->size_ > 0)
    return (fd_set *) &this->mask_;
  else
    return (fd_set *) 0;
}

// Returns a pointer to the underlying fd_set.

PPM_INLINE fd_set *
PPM_Handle_Set::fdset (void)
{
  if (this->size_ > 0)
    return (fd_set *) &this->mask_;
  else
    return (fd_set *) 0;
}

PPM_INLINE
PPM_Handle_Set_Iterator::~PPM_Handle_Set_Iterator (void)
{
}
