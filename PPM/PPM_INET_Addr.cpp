// INET_Addr.cpp,v 4.81 2002/12/09 05:10:00 bala Exp

// Defines the Internet domain address family address format.

#include "./PPM_INET_Addr.h"
#if defined (PPM_LACKS_INLINE_FUNCTIONS)
# include "./PPM_INET_Addr.inl"
#endif

// Creates a PPM_INET_Addr from a PORT_NUMBER and an Internet address.

PPM_INET_Addr::PPM_INET_Addr (void)
  : PPM_Addr (determine_type(), sizeof (inet_addr_))
{
  // PPM_TRACE ("PPM_INET_Addr::PPM_INET_Addr");
  this->reset();
}

PPM_INET_Addr::PPM_INET_Addr (u_short port_number,
                              PPM_UINT32 inet_address)
  : PPM_Addr (determine_type(), sizeof (inet_addr_))
{
  PPM_TRACE ("PPM_INET_Addr::PPM_INET_Addr");
  this->reset();
  if (this->set (port_number, inet_address) == -1){
    PPM_TRACE ((LM_ERROR,
                PPM_LIB_TEXT ("%p\n"),
                PPM_LIB_TEXT ("PPM_INET_Addr::PPM_INET_Addr")))
  }
}

PPM_INET_Addr::PPM_INET_Addr (u_short port_number, int address_family)
: PPM_Addr (address_family, sizeof (inet6_addr_))
{
	PPM_TRACE ("PPM_INET_Addr::PPM_INET_Addr");
	PPM_OS::memset(&this->inet6_addr_, 0, sizeof(this->inet6_addr_));

	if (this->set_ipv6(port_number, address_family) == -1){
		PPM_TRACE ((LM_ERROR,
			PPM_LIB_TEXT ("%p\n"),
			PPM_LIB_TEXT ("PPM_INET_Addr::PPM_INET_Addr")))
	}
}


int
PPM_INET_Addr::addr_to_string (char s[],
                               size_t size,
                               int ipaddr_format) const
{
  PPM_TRACE ("PPM_INET_Addr::addr_to_string");

  // XXX Can we (should we) include the scope id for IPv6 addresses?

  size_t total_len = (ipaddr_format == 0 ?
                      PPM_OS::strlen (this->get_host_name ()) :
                      PPM_OS::strlen (this->get_host_addr ()))
    + PPM_OS::strlen ("65536") // Assume the max port number.
    + sizeof (':')
    + sizeof ('\0'); // For trailing '\0'.

  if (size < total_len)
    return -1;
  else
    {
      PPM_OS::sprintf (s,
                       ("%s:%d"),
                       (ipaddr_format == 0
                                               ? this->get_host_name ()
                                               : this->get_host_addr ()),
                       this->get_port_number ());
      return 0;
    }
}

void
PPM_INET_Addr::dump (void) const
{
}

// Compare two addresses for inequality.

int
PPM_INET_Addr::operator != (const PPM_INET_Addr &sap) const
{
  return !((*this) == sap);
}

// Compare two addresses for equality.

int
PPM_INET_Addr::operator == (const PPM_INET_Addr &sap) const
{
  if (this->get_type () != sap.get_type () ||
      this->get_size () != sap.get_size ()    )
    return 0;

  return (PPM_OS::memcmp (&this->inet_addr_,
                          &sap.inet_addr_,
                          this->get_size ()) == 0);
}

// Transform the string into the current addressing format.

int
PPM_INET_Addr::string_to_addr (const char s[])
{
  int result;
  char *ip_addr;

  // Need to make a duplicate since we'll be overwriting the string.
  PPM_ALLOCATOR_RETURN (ip_addr,
                        PPM_OS::strdup (s),
                        -1);
  // We use strrchr because of IPv6 addresses.
  char *port_p = PPM_OS::strrchr (ip_addr, ':');

  if (port_p == 0) // Assume it's a port number.
    {
      if (PPM_OS::strspn (ip_addr, "1234567890") == PPM_OS::strlen (ip_addr))
        { // port number
          u_short port = (u_short) PPM_OS::atoi (ip_addr);
          result = this->set (port, PPM_UINT32 (INADDR_ANY));
        }
      else // port name
        result = this->set (ip_addr, PPM_UINT32 (INADDR_ANY));
    }
  else
    {
      *port_p = '\0'; ++port_p; // skip over ':'

      if (PPM_OS::strspn (port_p, "1234567890") == PPM_OS::strlen (port_p))
        {
          u_short port = (u_short) PPM_OS::atoi (port_p);
          result = this->set (port, ip_addr);
        }
      else
        result = this->set (port_p, ip_addr);
    }

  PPM_OS::free ((void*) (ip_addr));
  return result;
}

int
PPM_INET_Addr::set (u_short port_number,
                    const char host_name[],
                    int encode,
                    int address_family)
{
  PPM_TRACE ("PPM_INET_Addr::set");

  // Yow, someone gave us a NULL host_name!
  if (host_name == 0)
    {
      errno = EINVAL;
      return -1;
    }

  if (address_family == AF_INET6)
  {
	  PPM_OS_String::memset ((void *) &this->inet6_addr_, 0, sizeof this->inet6_addr_);

	  if (this->set_ipv6(port_number, address_family) == -1){
		  PPM_TRACE ((LM_ERROR,
			  PPM_LIB_TEXT ("%p\n"),
			  PPM_LIB_TEXT ("PPM_INET_Addr::set (u_short port_number,const char host_name[],int encode,int address_family)")))
	  }

	  return 0;
  }
  else
  {
	  PPM_OS_String::memset ((void *) &this->inet_addr_,
							 0,
							 sizeof this->inet_addr_);

	  // IPv6 not supported... insure the family is set to IPv4
	  address_family = AF_INET;
	  this->set_type (address_family);
	  this->inet_addr_.in4_.sin_family = address_family;
	  struct in_addr addrv4;
	  if (PPM_OS::inet_aton (host_name,
							 &addrv4) == 1)
		return this->set (port_number,
						  encode ? ntohl (addrv4.s_addr) : addrv4.s_addr,
						  encode);
	  else
		{
		  hostent hentry;
		  PPM_HOSTENT_DATA buf;
		  int h_error;  // Not the same as errno!

		  hostent *hp = PPM_OS::gethostbyname_r (host_name, &hentry,
												 buf, &h_error);

		  if (hp == 0)
			{
			  return -1;
			}
		  else
			{
			  (void) PPM_OS_String::memcpy ((void *) &addrv4.s_addr,
											hp->h_addr,
											hp->h_length);
			  return this->set (port_number,
								encode ? ntohl (addrv4.s_addr) : addrv4.s_addr,
								encode);
			}
		}
	}
}


// Initializes a PPM_INET_Addr from a <port_name> and the remote
// <host_name>.



// Creates a PPM_INET_Addr from a PORT_NUMBER and the remote
// HOST_NAME.


PPM_INET_Addr::PPM_INET_Addr (u_short port_number,
                              const char host_name[],
                              int address_family)
  : PPM_Addr (determine_type(), sizeof (inet_addr_))
{
  PPM_TRACE ("PPM_INET_Addr::PPM_INET_Addr");
  std::string strhost_name(host_name);
  if (strhost_name.find(":") != std::string::npos)
  {
		address_family = AF_INET6;
		this->set_type(address_family);
		this->set_size(sizeof (inet6_addr_));
		PPM_OS::memset (&this->inet6_addr_, 0, sizeof (this->inet6_addr_));
  }

  PPM_OS::memset (&this->inet_addr_, 0, sizeof (this->inet_addr_));


  if (this->set (port_number,
	  host_name,
	  1,
	  address_family) == -1){
		  PPM_TRACE ((LM_ERROR,
			  PPM_LIB_TEXT ("PPM_INET_Addr::PPM_INET_Addr: %p\n"),
			  PPM_TEXT_CHAR_TO_TCHAR ((host_name == 0) ?
			  "<unknown>" : host_name)))
  }
}

// Set a pointer to the address.
void
PPM_INET_Addr::set_addr (void *addr, int /* len */)
{
  PPM_TRACE ("PPM_INET_Addr::set_addr");
  struct sockaddr_in *getfamily = PPM_static_cast (struct sockaddr_in *, addr);

  if (getfamily->sin_family == AF_INET)
    {
      this->set_port_number (getfamily->sin_port, 0);
      this->set_address (PPM_reinterpret_cast (const char*, &getfamily->sin_addr),
                         sizeof (getfamily->sin_addr),
                         0);
    }
}

// Creates a PPM_INET_Addr from a sockaddr_in structure.


int
PPM_INET_Addr::get_host_name (char hostname[],
                              size_t len) const
{
  PPM_TRACE ("PPM_INET_Addr::get_host_name");

  int result;
  if (len > 1)
    {
      result = get_host_name_i(hostname,len);
      if (result < 0)
        {
          if (result == -2)
            {
              result = -1;
              // We know that hostname is nul-terminated
            }
          else
            {
              //result == -1;
              // This could be worse than hostname[len -1] = '\0'?
              hostname[0] = '\0';
            }
        }
    }
  else
    {
      if (len == 1)
        {
          hostname[0] = '\0';
        }
      result = -1;
    }

  return result;
}

// Return the character representation of the hostname.

const char *
PPM_INET_Addr::get_host_name (void) const
{
  PPM_TRACE ("PPM_INET_Addr::get_host_name");

  static char name[MAXHOSTNAMELEN + 1];
  if (this->get_host_name (name, MAXHOSTNAMELEN + 1) == -1)
    PPM_OS::strcpy (name, "<unknown>");
  return name;
}

void
PPM_INET_Addr::set_port_number (u_short port_number,
                                int encode)
{
  PPM_TRACE ("PPM_INET_Addr::set_port_number");

  if (encode)
    port_number = htons (port_number);

	  this->inet_addr_.in4_.sin_port = port_number;
}

void
PPM_INET_Addr::set_ipv6_port_number (u_short port_number,
								int encode)
{
	PPM_TRACE ("PPM_INET_Addr::set_ipv6_port_number");

	if (encode)
	{
		port_number = htons (port_number);
	}

	this->inet6_addr_.in6_.sin6_port = port_number;
}

// returns -2 when the hostname is truncated
int
PPM_INET_Addr::get_host_name_i (char hostname[], size_t len) const
{
  PPM_TRACE ("PPM_INET_Addr::get_host_name_i");

  if (this->inet_addr_.in4_.sin_addr.s_addr == INADDR_ANY)
    {
      if (PPM_OS::hostname (hostname, len) == -1)
        return -1;
      else
        return 0;
    }
  else
    {
      int h_error;  // Not the same as errno!
      hostent hentry;
      PPM_HOSTENT_DATA buf;
      hostent *hp =
        PPM_OS::gethostbyaddr_r ((char *)this->ip_addr_pointer (),
                                 this->ip_addr_size (),
                                 this->get_type (),
                                 &hentry,
                                 buf,
                                 &h_error);

      if (hp == 0 || hp->h_name == 0)
        return -1;

      if (PPM_OS::strlen (hp->h_name) >= len)
        {
          // We know the length, so use memcpy
          if (len > 0)
            {
              PPM_OS::memcpy (hostname, hp->h_name, len - 1);
              hostname[len-1]= '\0';
            }
          errno = ENOSPC;
          return -2;  // -2 Means that we have a good string
          // Using errno looks ok, but ENOSPC could be set on
          // other places.
        }

      PPM_OS::strcpy (hostname, hp->h_name);
      return 0;
    }
}

int PPM_INET_Addr::set_address (const char *ip_addr,
                                int len,
                                int encode /* = 1 */)
{
  PPM_TRACE ("PPM_INET_Addr::set_address");
  // This is really intended for IPv4. If the object is IPv4, or the type
  // hasn't been set but it's a 4-byte address, go ahead. If this is an
  // IPv6 object and <encode> is requested, refuse.
  if (encode && len != 4)
    {
      errno = EAFNOSUPPORT;
      return -1;
    }

  if (len == 4)
    {
      PPM_UINT32 ip4 = *PPM_reinterpret_cast (const PPM_UINT32 *, ip_addr);
      if (encode)
        ip4 = PPM_HTONL (ip4);


      if (this->get_type () == AF_INET) {
        this->base_set (AF_INET, sizeof (this->inet_addr_.in4_));
        this->inet_addr_.in4_.sin_family = AF_INET;
        this->set_size (sizeof (this->inet_addr_.in4_));
        PPM_OS_String::memcpy (&this->inet_addr_.in4_.sin_addr,
                               &ip4,
                               len);
      }

      return 0;
    }   /* end if (len == 4) */

  // Here with an unrecognized length.
  errno = EAFNOSUPPORT;
  return -1;

}

int PPM_INET_Addr::set_ipv6address (void)
{
	PPM_TRACE ("PPM_INET_Addr::set_ipv6address");
	// This is really intended for IPv6.

	if (this->get_type () == AF_INET6) 
	{
		this->base_set (AF_INET6, sizeof (this->inet6_addr_.in6_));
		this->inet6_addr_.in6_.sin6_family = AF_INET6;
		this->set_size (sizeof (this->inet6_addr_.in6_));
		this->inet6_addr_.in6_.sin6_addr = in6addr_any;

		return 0;
	}

	errno = EAFNOSUPPORT;
	return -1;
}


const char *
PPM_INET_Addr::get_host_addr (char *dst, int size) const
{

  char *ch = PPM_OS::inet_ntoa (this->inet_addr_.in4_.sin_addr);
  PPM_OS::strsncpy (dst, ch, size);
  return ch;
}

// Return the dotted Internet address.
const char *
PPM_INET_Addr::get_host_addr (void) const
{
  PPM_TRACE ("PPM_INET_Addr::get_host_addr");
  if (this->get_type() == AF_INET6)
  {
	  PPM_OS::memset((char*)m_cszaddr, 0, sizeof(m_cszaddr));
	  return PPM_OS::inet_ntop(AF_INET6,&(this->inet6_addr_.in6_.sin6_addr),(char*)m_cszaddr,46);
  }
  else
  {
	  return PPM_OS::inet_ntoa (this->inet_addr_.in4_.sin_addr);
  }
}

// Return the 4-byte IP address, converting it into host byte order.

PPM_UINT32
PPM_INET_Addr::get_ip_address (void) const
{
  PPM_TRACE ("PPM_INET_Addr::get_ip_address");

  return ntohl (PPM_UINT32 (this->inet_addr_.in4_.sin_addr.s_addr));
}

// Initializes a PPM_INET_Addr from a PORT_NUMBER and a 32 bit Internet
// address.

int
PPM_INET_Addr::set (u_short port_number,
                    PPM_UINT32 inet_address,
                    int encode)
{
  PPM_TRACE ("PPM_INET_Addr::set");
  this->set_address (PPM_reinterpret_cast (const char *, &inet_address),
                     sizeof inet_address,
                     encode);
  this->set_port_number (port_number, encode);

  return 0;
}

int
PPM_INET_Addr::set_ipv6 (u_short port_number,
						int addr_family,
						int encode)
{
	PPM_TRACE ("PPM_INET_Addr::set_ipv6");
	if (addr_family == AF_INET6) 
	{
		this->set_ipv6address();
		this->set_ipv6_port_number(port_number, encode);
	}

	return 0;
}


// Creates a PPM_INET_Addr from a sockaddr_in structure.

int
PPM_INET_Addr::set (const sockaddr_in *addr, int len)
{
  PPM_TRACE ("PPM_INET_Addr::set");

  if (addr->sin_family == AF_INET)
    {
      PPM_OS::memcpy (&this->inet_addr_.in4_, addr, len);
      this->base_set (AF_INET, len);
      return 0;
    }


  errno = EAFNOSUPPORT;
  return -1;
}

// Initializes a PPM_INET_Addr from a <port_name> and a 32 bit Internet
// address.

int
PPM_INET_Addr::set (const char port_name[],
                    PPM_UINT32 inet_address,
                    const char protocol[])
{
  PPM_TRACE ("PPM_INET_Addr::set");

  servent sentry;
  PPM_SERVENT_DATA buf;

  servent *sp = PPM_OS::getservbyname_r (port_name,
                                         protocol,
                                         &sentry,
                                         buf);
  if (sp == 0)
    return -1;
  else
    return this->set (sp->s_port, inet_address, 0);
}


// Initializes a PPM_INET_Addr from a <port_name> and the remote
// <host_name>.

int
PPM_INET_Addr::set (const char port_name[],
                    const char host_name[],
                    const char protocol[])
{
  PPM_TRACE ("PPM_INET_Addr::set");

  servent sentry;
  PPM_SERVENT_DATA buf;

  servent *sp = PPM_OS::getservbyname_r (port_name,
                                         protocol,
                                         &sentry,
                                         buf);
  if (sp == 0)
    return -1;

  int address_family = AF_INET;

  return this->set (sp->s_port, host_name, 0, address_family);
}

