
#ifndef PPM_INET_ADDR_H
#define PPM_INET_ADDR_H

#include "./PPM_typedef.h"
#include "./PPM_OS.h"
#include "./PPM_Addr.h"

/**
对网络地址的一个包装，提供各种访问及设置方法
 */
class  PPM_INET_Addr : public PPM_Addr
{
public:
  // = Initialization methods.

  /// Default constructor.
  PPM_INET_Addr (void);

  /// Creates an <PPM_INET_Addr> from a <port_number> and the remote
  /// <host_name>. The port number is assumed to be in host byte order.
  /// To set a port already in network byte order, please @see set().
  /// Use address_family to select IPv6 (PF_INET6) vs. IPv4 (PF_INET).
  PPM_INET_Addr (u_short port_number,
                 const char host_name[],
                 int address_family = AF_INET);
  /**
   * Creates an <PPM_INET_Addr> from a <port_number> and an Internet
   * <ip_addr>.  This method assumes that <port_number> and <ip_addr>
   * are in host byte order. If you have addressing information in
   * network byte order, @see set().
   */
  PPM_INET_Addr (u_short port_number,
                 PPM_UINT32 ip_addr = INADDR_ANY);


  PPM_INET_Addr (u_short port_number,
				int address_family);
  /// Default dtor.
  ~PPM_INET_Addr (void);

  // = Direct initialization methods.

  // These methods are useful after the object has been constructed.

  /// Initializes from another <PPM_INET_Addr>.
  int set (const PPM_INET_Addr &);

  /**
   * Initializes an <PPM_INET_Addr> from a <port_number> and the
   * remote <host_name>.  If <encode> is non-zero then <port_number> is
   * converted into network byte order, otherwise it is assumed to be
   * in network byte order already and are passed straight through.
   * address_family can be used to select IPv4/IPv6 if the OS has
   * IPv6 capability (PPM_HAS_IPV6 is defined). To specify IPv6, use
   * the value AF_INET6. To specify IPv4, use AF_INET.
   */
  int set (u_short port_number,
           const char host_name[],
           int encode = 1,
           int address_family = AF_INET);
  /**
   * Initializes an <PPM_INET_Addr> from a @a port_number and an Internet
   * @a ip_addr.  If @a encode is non-zero then the port number and IP address
   * are converted into network byte order, otherwise they are assumed to be
   * in network byte order already and are passed straight through.
   */
  int set (u_short port_number,
           PPM_UINT32 ip_addr = INADDR_ANY,
           int encode = 1);

  int set_ipv6 (u_short port_number,
			  int addr_family = AF_INET6,
			  int encode = 1);
  /// Creates an <PPM_INET_Addr> from a sockaddr_in structure.
  int set (const sockaddr_in *,
           int len);
  /**
   * Uses <getservbyname> to initialize an <PPM_INET_Addr> from a
   * <port_name>, an <ip_addr>, and the <protocol>.  This assumes that
   * <ip_addr> is already in network byte order.
   */
  int set (const char port_name[],
           PPM_UINT32 ip_addr,
           const char protocol[] = "tcp");

  /// Uses <getservbyname> to initialize an <PPM_INET_Addr> from a
  /// <port_name>, the remote <host_name>, and the <protocol>.
  int set (const char port_name[],
           const char host_name[],
           const char protocol[] = "tcp");


 
  /// Return a pointer to the underlying network address.
  virtual void *get_addr (void) const;
  int get_addr_size(void) const;

  /// Set a pointer to the address.
  virtual void set_addr (void *, int len);

  /**
   * Transform the current <PPM_INET_Addr> address into string format.
   * If <ipaddr_format> is non-0 this produces "ip-number:port-number"
   * (e.g., "**************:1234"), whereas if <ipaddr_format> is 0
   * this produces "ip-name:port-number" (e.g.,
   * "tango.cs.wustl.edu:1234").  Returns -1 if the <size> of the
   * <buffer> is too small, else 0.
   */
  virtual int addr_to_string (char buffer[],
                              size_t size,
                              int ipaddr_format = 1) const;

  /**
   * Initializes an <PPM_INET_Addr> from the <address>, which can be
   * "ip-addr:port-number" (e.g., "tango.cs.wustl.edu:1234"),
   * "ip-addr:port-name" (e.g., "tango.cs.wustl.edu:telnet"),
   * "ip-number:port-number" (e.g., "**************:1234"), or
   * "ip-number:port-name" (e.g., "**************:telnet").  If there
   * is no ':' in the <address> it is assumed to be a port number,
   * with the IP address being INADDR_ANY.
   */
  virtual int string_to_addr (const char address[]);

  /**
   * Sets the port number without affecting the host name.  If
   * <encode> is enabled then <port_number> is converted into network
   * byte order, otherwise it is assumed to be in network byte order
   * already and are passed straight through.
   */
  void set_port_number (u_short,
                        int encode = 1);

  //this func is for IPv6
  void set_ipv6_port_number (u_short,
	  int encode = 1);

  /**
   * Sets the address without affecting the port number.  If
   * <encode> is enabled then <ip_addr> is converted into network
   * byte order, otherwise it is assumed to be in network byte order
   * already and are passed straight through.  The size of the address
   * is specified in the <len> parameter.
   */
  int set_address (const char *ip_addr,
                   int len,
                   int encode = 1);

  //this func is for IPv6
  int set_ipv6address(void);

  /// Return the port number, converting it into host byte-order.
  u_short get_port_number (void) const;

  /**
   * Return the character representation of the name of the host,
   * storing it in the <hostname> (which is assumed to be
   * <hostnamelen> bytes long).  This version is reentrant.  If
   * <hostnamelen> is greater than 0 then <hostname> will be
   * NUL-terminated even if -1 is returned.
   */
  int get_host_name (char hostname[],
                     size_t hostnamelen) const;

  /**
   * Return the character representation of the hostname (this version
   * is non-reentrant since it returns a pointer to a static data
   * area).
   */
  const char *get_host_name (void) const;

  /// Return the "dotted decimal" Internet address.
  const char *get_host_addr (void) const;
  const char *get_host_addr (char *dst, int size) const;

  /// Return the 4-byte IP address, converting it into host byte
  /// order.
  PPM_UINT32 get_ip_address (void) const;

  /**
   * Returns true if <this> is less than <rhs>.  In this context,
   * "less than" is defined in terms of IP address and TCP port
   * number.  This operator makes it possible to use <PPM_INET_Addr>s
   * in STL maps.
   */
  int operator < (const PPM_INET_Addr &rhs) const;

  /// Compare two addresses for equality.  The addresses are considered
  /// equal if they contain the same IP address and port number.
  int operator == (const PPM_INET_Addr &SAP) const;

  /// Compare two addresses for inequality.
  int operator != (const PPM_INET_Addr &SAP) const;

  /// Computes and returns hash value.
  virtual u_long hash (void) const;

  /// Dump the state of an object.
  void dump (void) const;


private:
  /// Insure that @a hostname is properly null-terminated.
  int get_host_name_i (char hostname[], size_t hostnamelen) const;

  // Methods to gain access to the actual address of
  // the underlying internet address structure.
  void *ip_addr_pointer (void) const;
  int ip_addr_size (void) const;
  int determine_type (void) const;

  /// Initialize underlying inet_addr_ to default values
  void reset (void);

  /// Underlying representation.
  /// This union uses the knowledge that the two structures share the
  /// first member, sa_family (as all sockaddr structures do).
  union
  {
    sockaddr_in  in4_;
  } inet_addr_;

  union
  {
	  sockaddr_in6  in6_;
  } inet6_addr_;

  char m_cszaddr[46]; //用于存放IPv6地址字符串
};
#if !defined (PPM_LACKS_INLINE_FUNCTIONS)
# include "./PPM_INET_Addr.inl"
#endif

#endif /* PPM_INET_ADDR_H */
