/* -*- C++ -*- */
// IPC_SAP.i,v 4.4 1998/09/25 17:24:22 irfan Exp

// IPC_SAP.i

// Used to return the underlying handle_. 

PPM_INLINE
PPM_IPC_SAP::~PPM_IPC_SAP (void)
{
  // PPM_TRACE ("PPM_IPC_SAP::~PPM_IPC_SAP");
}

PPM_INLINE PPM_HANDLE
PPM_IPC_SAP::get_handle (void) const
{
  PPM_TRACE ("PPM_IPC_SAP::get_handle");
  return this->handle_;
}

// Used to set the underlying handle_. 

PPM_INLINE void
PPM_IPC_SAP::set_handle (PPM_HANDLE handle)
{
  PPM_TRACE ("PPM_IPC_SAP::set_handle");
  this->handle_ = handle;
}

// Provides access to the PPM_OS::ioctl system call. 

PPM_INLINE int 
PPM_IPC_SAP::control (int cmd, void *arg) const
{
  PPM_TRACE ("PPM_IPC_SAP::control");
//  return PPM_OS::ioctl (this->handle_, cmd, arg);
  return 0;
}
