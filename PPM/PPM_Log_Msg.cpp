#include "PPM_Log_Msg.h"
#include "PPM_OS.h"
#include "PPM_OS_Error.h"

#define PPM_UPDATE_COUNT(COUNT, LEN) \
   do { if (PPM_static_cast (size_t, LEN) > COUNT) COUNT = 0; \
     else COUNT -= PPM_static_cast (size_t, LEN); \
   } while (0)

PPM_Log_Msg *
PPM_Log_Msg::instance (void)
{
  static PPM_Log_Msg *tss_log_msg = 0;

  if (tss_log_msg == 0)
    {
      PPM_NEW_RETURN (tss_log_msg, PPM_Log_Msg, 0);
      // Register the instance for destruction at program termination.
    }

  return tss_log_msg;
}

PPM_Log_Msg *
PPM_Log_Msg::instance_new (void)
{
  PPM_Log_Msg *tss_log_msg_new = 0;

  if (tss_log_msg_new == 0)
    {
      PPM_NEW_RETURN (tss_log_msg_new, PPM_Log_Msg, 0);
      // Register the instance for destruction at program termination.
    }

  return tss_log_msg_new;
}


void
PPM_Log_Msg::enable_debug_messages (PPM_Log_Priority priority)
{
  PPM_SET_BITS (PPM_Log_Msg::default_priority_mask_, priority);
  PPM_Log_Msg *i = PPM_Log_Msg::instance ();
  i->priority_mask (i->priority_mask () | priority);
}

// Clears the flag in the default priority mask used to initialize
// PPM_Log_Msg instances, as well as the current per-thread instance.
/*

*/
void
PPM_Log_Msg::disable_debug_messages (PPM_Log_Priority priority)
{
  CStdAutoLock tm_autolock(&staticlock_);
  PPM_CLR_BITS (PPM_Log_Msg::default_priority_mask_, priority);

  PPM_Log_Msg *i = PPM_Log_Msg::instance ();
  i->priority_mask (i->priority_mask () & ~priority);
}

const char *
PPM_Log_Msg::program_name (void)
{
  return PPM_Log_Msg::program_name_;
}

/// Name of the local host.
const char *PPM_Log_Msg::local_host_ = 0;

/// Records the program name.
const char *PPM_Log_Msg::program_name_ = 0;

/// Default is to use stderr.
u_long PPM_Log_Msg::flags_ = PPM_Log_Msg::STDERR;

/// Process id of the current process.
pid_t PPM_Log_Msg::pid_ = -1;

CStdLock PPM_Log_Msg::staticlock_ = CStdLock();
// / Current offset of msg_[].
// int PPM_Log_Msg::msg_off_ = 0;

/// Default per-thread priority mask
/// By default, no priorities are enabled.
u_long PPM_Log_Msg::default_priority_mask_ = 0;

/// Default per-process priority mask
/// By default, all priorities are enabled.
u_long PPM_Log_Msg::process_priority_mask_ = LM_SHUTDOWN
                                           | LM_TRACE
                                           | LM_DEBUG
                                           | LM_INFO
                                           | LM_NOTICE
                                           | LM_WARNING
                                           | LM_STARTUP
                                           | LM_ERROR
                                           | LM_CRITICAL
                                           | LM_ALERT
                                           | LM_EMERGENCY;

void
PPM_Log_Msg::close (void)
{

}

u_long
PPM_Log_Msg::flags (void)
{
  PPM_TRACE ("PPM_Log_Msg::flags");
  u_long result;
  result = PPM_Log_Msg::flags_;
  return result;
}

void
PPM_Log_Msg::set_flags (u_long flgs)
{
  CStdAutoLock tm_autolock(&staticlock_);

  PPM_SET_BITS (PPM_Log_Msg::flags_, flgs);
}

void
PPM_Log_Msg::clr_flags (u_long flgs)
{
  CStdAutoLock tm_autolock(&staticlock_);

  PPM_CLR_BITS (PPM_Log_Msg::flags_, flgs);
}


u_long
PPM_Log_Msg::priority_mask (u_long n_mask)
{
  CStdAutoLock tm_autolock(&staticlock_);

  u_long o_mask;

    o_mask = PPM_Log_Msg::process_priority_mask_;
        PPM_Log_Msg::process_priority_mask_ = n_mask;

  return o_mask;
}

u_long
PPM_Log_Msg::priority_mask ()
{
  return   this->process_priority_mask_;
}

int
PPM_Log_Msg::log_priority_enabled (PPM_Log_Priority log_priority)
{
  return PPM_BIT_ENABLED (this->priority_mask_ |
                            PPM_Log_Msg::process_priority_mask_,
                          log_priority);
}

PPM_Log_Msg::PPM_Log_Msg (void)
  : status_ (0),
    errnum_ (0),
    linenum_ (0),
    ostream_ (0),
    msg_callback_ (0),
    priority_mask_ (default_priority_mask_),
    delete_ostream_(0)
{
	msg_off_ = 0;
	memset(msg_,0,sizeof(msg_));
  // PPM_TRACE ("PPM_Log_Msg::PPM_Log_Msg");

  PPM_MT (PPM_GUARD (PPM_Recursive_Thread_Mutex, PPM_mon,
                     *PPM_Log_Msg_Manager::get_lock ()));
  stdlog_ = NULL;


  this->conditional_values_.is_set_ = 0;
}

PPM_Log_Msg::~PPM_Log_Msg (void)
{
      if (PPM_Log_Msg::program_name_)
        {
          PPM_OS::free ((void *) PPM_Log_Msg::program_name_);
          PPM_Log_Msg::program_name_ = 0;
        }

      if (PPM_Log_Msg::local_host_)
        {
          PPM_OS::free ((void *) PPM_Log_Msg::local_host_);
          PPM_Log_Msg::local_host_ = 0;
        }

  //
  // do we need to close and clean up?
  //
  if (this->delete_ostream_ == 1)
#if defined (PPM_LACKS_IOSTREAM_TOTALLY)
    {
      PPM_OS::fclose (this->ostream_);
    }
#else
    {
      delete ostream_;
    }
#endif
}

// Open the sender-side of the message queue.

int
PPM_Log_Msg::open (const char *prog_name,
                   u_long flags)
{
  PPM_TRACE ("PPM_Log_Msg::open");
  PPM_MT (PPM_GUARD_RETURN (PPM_Recursive_Thread_Mutex, PPM_mon,
                            *PPM_Log_Msg_Manager::get_lock (), -1));

  if (prog_name)
    {
      PPM_OS::free ((void *) PPM_Log_Msg::program_name_);

      // Stop heap checking, block will be freed by the destructor.
      {
        PPM_ALLOCATOR_RETURN (PPM_Log_Msg::program_name_,
                              PPM_OS::strdup (prog_name),
                              -1);
      }
    }
  else if (PPM_Log_Msg::program_name_ == 0)
    {
      // Stop heap checking, block will be freed by the destructor.
      PPM_ALLOCATOR_RETURN (PPM_Log_Msg::program_name_,
                            PPM_OS::strdup (PPM_LIB_TEXT ("<unknown>")),
                            -1);
    }

  int status = 0;


  if (PPM_BIT_ENABLED (PPM_Log_Msg::flags_, PPM_Log_Msg::LOGGER))
    {
      // If we are closing down logger, redirect logging to stderr.
      PPM_CLR_BITS (PPM_Log_Msg::flags_, PPM_Log_Msg::LOGGER);
      PPM_SET_BITS (PPM_Log_Msg::flags_, PPM_Log_Msg::STDERR);
    }

  // Remember, PPM_Log_Msg::STDERR bit is on by default...
  if (status != -1
      && PPM_BIT_ENABLED (flags,
                          PPM_Log_Msg::STDERR) == 0)
    PPM_CLR_BITS (PPM_Log_Msg::flags_,
                  PPM_Log_Msg::STDERR);


  if (PPM_BIT_ENABLED (flags,
                       PPM_Log_Msg::OSTREAM))
    {
      PPM_SET_BITS (PPM_Log_Msg::flags_,
                    PPM_Log_Msg::OSTREAM);
      // Only set this to cerr if it hasn't already been set.
      if (this->msg_ostream () == 0)
        this->msg_ostream (PPM_DEFAULT_LOG_STREAM,this->stdlog_);
    }

  if (PPM_BIT_ENABLED (flags,
                       PPM_Log_Msg::MSG_CALLBACK))
    PPM_SET_BITS (PPM_Log_Msg::flags_,
                  PPM_Log_Msg::MSG_CALLBACK);

  if (PPM_BIT_ENABLED (flags,
                       PPM_Log_Msg::SILENT))
    PPM_SET_BITS (PPM_Log_Msg::flags_,
                  PPM_Log_Msg::SILENT);

  return status;
}

/**
 * Valid Options (prefixed by '%', as in printf format strings) include:
 *   'A': print an PPM_timer_t value
 *   'a': exit the program at this point (var-argument is the exit status!)
 *   'c': print a character
 *   'C': print a character string
 *   'i', 'd': print a decimal number
 *   'I', indent according to nesting depth
 *   'e', 'E', 'f', 'F', 'g', 'G': print a double
 *   'l', print line number where an error occurred.
 *   'M': print the name of the priority of the message.
 *   'm': Return the message corresponding to errno value, e.g., as done by <strerror>
 *   'N': print file name where the error occurred.
 *   'n': print the name of the program (or "<unknown>" if not set)
 *   'o': print as an octal number
 *   'P': format the current process id
 *   'p': format the appropriate errno message from sys_errlist, e.g., as done by <perror>
 *   'Q': print out the uint64 number
 *   '@': print a void* pointer (in hexadecimal)
 *   'r': call the function pointed to by the corresponding argument
 *   'R': print return status
 *   'S': format the appropriate _sys_siglist entry corresponding to var-argument.
 *   's': format a character string
 *   'T': print timestamp in hour:minute:sec:usec format.
 *   'D': print timestamp in month/day/year hour:minute:sec:usec format.
 *   't': print thread id (1 if single-threaded)
 *   'u': print as unsigned int
 *   'x': print as a hex number
 *   'X': print as a hex number
 *   'w': print a wide character
 *   'W': print out a wide character string.
 *   'z': print an PPM_OS::WChar character
 *   'Z': print an PPM_OS::WChar character string
 *   '%': format a single percent sign, '%'
 */
int
PPM_Log_Msg::log (PPM_Log_Priority log_priority,
                  const char *format_str, ...)
{
	CStdAutoLock tm_autolock(&staticlock_);
	PPM_TRACE ("PPM_Log_Msg::log");

  // Start of variable args section.
  va_list argp;

  va_start (argp, format_str);

  int result = this->log (format_str,
                          log_priority,
                          argp);
  va_end (argp);

  return result;
}
int
PPM_Log_Msg::log_date (PPM_Log_Priority log_priority,
                  const char *format_str, ...)
{

	CStdAutoLock tm_autolock(&staticlock_);
  // Start of variable args section.
  va_list argp;
	char tm_str[1024];
	if(PPM_OS::strlen(format_str) > 1000){
		return 0;
	}
	PPM_OS::snprintf(tm_str,sizeof(tm_str),"[%%D][%%M]:%s",format_str);

	PPM_TRACE ("PPM_Log_Msg::log");

  va_start (argp, format_str);


  int result = this->log (tm_str,
                          log_priority,
                          argp);
  va_end (argp);

  return result;
}

int
PPM_Log_Msg::log (const char *format_str,
                  PPM_Log_Priority log_priority,
                  va_list argp)
{
  PPM_TRACE ("PPM_Log_Msg::log");
  // External decls.

  typedef void (*PTF)(...);

  // Check if there were any conditional values set.
  int conditional_values = this->conditional_values_.is_set_;

  // Reset conditional values.
  this->conditional_values_.is_set_ = 0;

  // Only print the message if <priority_mask_> hasn't been reset to
  // exclude this logging priority.
  if (this->log_priority_enabled (log_priority) == 0)
    return 0;

  // If conditional values were set and the log priority is correct,
  // then the values are actually set.
  if (conditional_values)
    this->set (this->conditional_values_.file_,
               this->conditional_values_.line_,
               this->conditional_values_.op_status_,
               this->conditional_values_.errnum_,
               this->msg_ostream (),
               this->msg_callback ());

  // Logging is a benign activity, so don't inadvertently smash errno.
  PPM_Errno_Guard guard (errno);

  PPM_Log_Record log_record (log_priority,
                             PPM_OS::gettimeofday ());
  // bp is pointer to where to put next part of logged message.
  // bspace is the number of characters remaining in msg_.
  char *bp = PPM_const_cast (char *, this->msg ());
  size_t bspace = PPM_Log_Record::MAXLOGMSGLEN;  // Leave room for Nul term.
  if (this->msg_off_ <= PPM_Log_Record::MAXLOGMSGLEN)
    bspace -= PPM_static_cast (size_t, this->msg_off_);

  // If this platform has snprintf() capability to prevent overrunning the
  // output buffer, use it. To avoid adding a maintenance-hassle compile-
  // time couple between here and OS.cpp, don't try to figure this out at
  // compile time. Instead, do a quick check now; if we get a -1 return,
  // the platform doesn't support the length-limiting capability.
  char test[2];
  int can_check = PPM_OS::snprintf (test, 1, PPM_LIB_TEXT ("x")) != -1;

  int abort_prog = 0;
  //int exit_value = 0;

  while (*format_str != '\0' && bspace > 0)
    {
      // Copy input to output until we encounter a %, however a
      // % followed by another % is not a format specification.

      if (*format_str != '%')
        {
          *bp++ = *format_str++;
          bspace--;
        }
      else if (format_str[1] == '%') // An "escaped" '%' (just print one '%').
        {
          *bp++ = *format_str++;    // Store first %
          format_str++;             // but skip second %
          bspace--;
        }
      else
        {
          // This is most likely a format specification that ends with
          // one of the valid options described previously. To enable full
          // use of all sprintf capabilities, save the format specifier
          // from the '%' up to the format letter in a new char array.
          // This allows the full sprintf capability for padding, field
          // widths, alignment, etc.  Any width/precision requiring a
          // caller-supplied argument is extracted and placed as text
          // into the format array. Lastly, we convert the caller-supplied
          // format specifier from the PPM_Log_Msg-supported list to the
          // equivalent sprintf specifier, and run the new format spec
          // through sprintf, adding it to the bp string.

          //const char *abort_str = PPM_LIB_TEXT ("Aborting...");
          const char *start_format = format_str;
          char format[1024]; // Converted format string
          char *fp;         // Current format pointer
          int       wp = 0;      // Width/precision extracted from args
          int       done = 0;
          int       skip_nul_locate = 0;
          int       this_len = 0;    // How many chars s[n]printf wrote

          fp = format;
          *fp++ = *format_str++;   // Copy in the %

          // Work through the format string to copy in the format
          // from the caller. While it's going across, extract ints
          // for '*' width/precision values from the argument list.
          // When the real format specifier is located, change it to
          // one recognized by sprintf, if needed, and do the sprintf
          // call.

          while (!done)
            {
              done = 1;               // Unless a conversion spec changes it

              switch (*format_str)
                {
                // The initial set of cases are the conversion
                // specifiers. Copy them in to the format array.
                // Note we don't use 'l', a normal conversion spec,
                // as a conversion because it is a PPM_Log_Msg format
                // specifier.
                case '-':
                case '+':
                case '0':
                case ' ':
                case '#':
                case '1':
                case '2':
                case '3':
                case '4':
                case '5':
                case '6':
                case '7':
                case '8':
                case '9':
                case '.':
                case 'L':
                case 'h':
                  *fp++ = *format_str;
                  done = 0;
                  break;

                case '*':
                  wp = va_arg (argp, int);
                  PPM_OS::sprintf (fp, PPM_LIB_TEXT ("%d"), wp);
                  fp += PPM_OS::strlen (fp);
                  done = 0;
                  break;

                case 'A':             // PPM_timer_t
                  {
                    PPM_OS::strcpy (fp, PPM_LIB_TEXT ("f"));
                    double value = va_arg (argp, double);
                    if (can_check)
                      this_len = PPM_OS::snprintf (bp, bspace, format, value);
                    else
                      this_len = PPM_OS::sprintf (bp, format, value);
                    PPM_UPDATE_COUNT (bspace, this_len);
                  }
                  break;

                case 'l':             // Source file line number
                  PPM_OS::strcpy (fp, PPM_LIB_TEXT ("d"));
                  if (can_check)
                    this_len = PPM_OS::snprintf (bp,
                                                 bspace,
                                                 format,
                                                 this->linenum ());
                  else
                    this_len = PPM_OS::sprintf (bp, format, this->linenum ());
                  PPM_UPDATE_COUNT (bspace, this_len);
                  break;

                case 'N':             // Source file name
                  // @@ UNICODE
                  PPM_OS::strcpy (fp, PPM_LIB_TEXT ("s"));
                  if (can_check)
                    this_len = PPM_OS::snprintf (bp, bspace, format,
                                                 this->file () ?
                                                 (this->file ())
                                                 : PPM_LIB_TEXT ("<unknown file>"));
                  else
                    this_len = PPM_OS::sprintf (bp, format,
                                                this->file () ?
                                                (this->file ())
                                                : PPM_LIB_TEXT ("<unknown file>"));
                  PPM_UPDATE_COUNT (bspace, this_len);
                  break;

                case 'n':             // Program name
                  // @@ UNICODE
                  PPM_OS::strcpy (fp, PPM_LIB_TEXT ("s"));
                  if (can_check)
                    this_len = PPM_OS::snprintf (bp, bspace, format,
                                                 PPM_Log_Msg::program_name_ ?
                                                 PPM_Log_Msg::program_name_ :
                                                 PPM_LIB_TEXT ("<unknown>"));
                  else
                    this_len = PPM_OS::sprintf (bp, format,
                                                PPM_Log_Msg::program_name_ ?
                                                PPM_Log_Msg::program_name_ :
                                                PPM_LIB_TEXT ("<unknown>"));
                  PPM_UPDATE_COUNT (bspace, this_len);
                  break;

                case 'M': // Print the name of the priority of the message.
                  PPM_OS::strcpy (fp, PPM_LIB_TEXT ("s"));
                  if (can_check)
                    this_len = PPM_OS::snprintf
                      (bp, bspace, format,
                       PPM_Log_Record::priority_name (log_priority));
                  else
                    this_len = PPM_OS::sprintf
                      (bp, format,
                       PPM_Log_Record::priority_name (log_priority));
                  PPM_UPDATE_COUNT (bspace, this_len);
                  break;

                case 'm': // Format the string assocated with the errno value.
                  {
                    errno = PPM_OS::map_errno (this->errnum ());
                    
                    if (errno >= 0 && errno < sys_nerr)
                      {
                        PPM_OS::strcpy (fp, PPM_LIB_TEXT ("s"));
                        if (can_check)
                          this_len = PPM_OS::snprintf
                            (bp, bspace, format,
                             PPM_OS_String::strerror (errno));
                        else
                          this_len = PPM_OS::sprintf
                            (bp, format, PPM_OS_String::strerror (errno));
                      }
                    else
                      {
#if defined (WIN32)
                        char *lpMsgBuf = 0;

     // PharLap can't do FormatMessage, so try for socket
     // error.
# if !defined (ACE_HAS_PHARLAP)
                        PPM_TEXT_FormatMessage (FORMAT_MESSAGE_ALLOCATE_BUFFER
                                                  | FORMAT_MESSAGE_MAX_WIDTH_MASK
                                                  | FORMAT_MESSAGE_FROM_SYSTEM,
                                                  0,
                                                  errno,
                                                  MAKELANGID (LANG_NEUTRAL,
                                                              SUBLANG_DEFAULT),
                                                              // Default language
                                                  (char *) &lpMsgBuf,
                                                  0,
                                                  0);
# endif /* ACE_HAS_PHARLAP */

                        // If we don't get a valid response from
                        // <FormatMessage>, we'll assume this is a
                        // WinSock error and so we'll try to convert
                        // it into a string.  If this doesn't work it
                        // returns "unknown error" which is fine for
                        // our purposes.
                        if (lpMsgBuf == 0)
                          {
                            const char *message =
                              PPM_OS::sock_error (errno);
                            PPM_OS::strcpy (fp, PPM_LIB_TEXT ("s"));
                            if (can_check)
                              this_len = PPM_OS::snprintf
                                (bp, bspace, format, message);
                            else
                              this_len = PPM_OS::sprintf (bp, format, message);
                          }
                        else
                          {
                            PPM_OS::strcpy (fp, PPM_LIB_TEXT ("s"));
                            if (can_check)
                              this_len = PPM_OS::snprintf
                                (bp, bspace, format, lpMsgBuf);
                            else
                              this_len = PPM_OS::sprintf
                                (bp, format, lpMsgBuf);
                            // Free the buffer.
                            ::LocalFree (lpMsgBuf);
                          }
#endif /* ACE_WIN32 */
                      }
                    PPM_UPDATE_COUNT (bspace, this_len);
                    break;
                  }

                case '$': // insert a newline, then indent the next line
                          // according to %I
                  *bp++ = '\n';
                  bspace--;
                  /* fallthrough */

                case 'r': // Run (invoke) this subroutine.
                  {
                    int osave = PPM_Log_Msg::msg_off_;

                    if (PPM_BIT_ENABLED (PPM_Log_Msg::flags_,
                                         PPM_Log_Msg::SILENT) &&
                        bspace > 1)
                      {
                        *bp++ = '{';
                        bspace--;
                      }
                    PPM_Log_Msg::msg_off_ =  bp - this->msg_;

                    (*va_arg (argp, PTF))();

                    if (PPM_BIT_ENABLED (PPM_Log_Msg::flags_,
                                         PPM_Log_Msg::SILENT) &&
                        bspace > (1 + PPM_OS::strlen (bp)))
                      {
                        bspace -= (PPM_OS::strlen (bp) + 1);
                        bp += PPM_OS::strlen (bp);
                        *bp++ =  '}';
                      }
                    *bp = '\0';
                    skip_nul_locate = 1;
                    PPM_Log_Msg::msg_off_ = osave;
                    break;
                  }

                case 'D': // Format the timestamp in month/day/year
                          // hour:minute:sec:usec format.
                  {
                    char day_and_time[35];
                    PPM_OS::timestamp (day_and_time,
                                    sizeof day_and_time);
                    PPM_OS::strcpy (fp, PPM_LIB_TEXT ("s"));
                    if (can_check)
                      this_len = PPM_OS::snprintf
                        (bp, bspace, format, day_and_time);
                    else
                      this_len = PPM_OS::sprintf (bp, format, day_and_time);
                    PPM_UPDATE_COUNT (bspace, this_len);
                    break;
                  }

                case 'T': // Format the timestamp in
                          // hour:minute:sec:usec format.
                  {
                    char day_and_time[35];
                    PPM_OS::strcpy (fp, PPM_LIB_TEXT ("s"));
                    if (can_check)
                      this_len = PPM_OS::snprintf
                        (bp, bspace, format,
                         PPM_OS::timestamp (day_and_time, sizeof day_and_time));
                    else
                      this_len = PPM_OS::sprintf
                        (bp, format, PPM_OS::timestamp (day_and_time,
                                                     sizeof day_and_time));
                    PPM_UPDATE_COUNT (bspace, this_len);
                    break;
                  }

                case 's':                       // String
                  PPM_OS::strcpy (fp, PPM_LIB_TEXT ("s"));
                  if (can_check)
                    this_len = PPM_OS::snprintf
                      (bp, bspace, format, va_arg (argp, char *));
                  else
                    this_len = PPM_OS::sprintf
                      (bp, format, va_arg (argp, char *));
                  PPM_UPDATE_COUNT (bspace, this_len);
                  break;

                case 'C':         // Char string, Unicode for Win32/WCHAR
// #if defined (WIN32) && defined (PPM_USES_WCHAR)
//                   PPM_OS::strcpy (fp, PPM_LIB_TEXT ("S"));
// #else /* WIN32 && PPM_USES_WCHAR */
                  PPM_OS::strcpy (fp, PPM_LIB_TEXT ("s"));
// #endif /* WIN32 && PPM_USES_WCHAR */
                  if (can_check)
                    this_len = PPM_OS::snprintf
                      (bp, bspace, format, va_arg (argp, char *));
                  else
                    this_len = PPM_OS::sprintf
                      (bp, format, va_arg (argp, char *));
                  PPM_UPDATE_COUNT (bspace, this_len);
                  break;

                case 'c':
// #if defined (WIN32) && defined (PPM_USES_WCHAR)
//                   PPM_OS::strcpy (fp, PPM_LIB_TEXT ("C"));
// #else
                  PPM_OS::strcpy (fp, PPM_LIB_TEXT ("c"));
// #endif /* WIN32 && PPM_USES_WCHAR */
                  if (can_check)
                    this_len = PPM_OS::snprintf
                      (bp, bspace, format, va_arg (argp, int));
                  else
                    this_len = PPM_OS::sprintf
                      (bp, format, va_arg (argp, int));
                  PPM_UPDATE_COUNT (bspace, this_len);
                  break;

                case 'd': case 'i': case 'o':
                case 'u': case 'x': case 'X':
                  fp[0] = *format_str;
                  fp[1] = '\0';
                  if (can_check)
                    this_len = PPM_OS::snprintf
                      (bp, bspace, format, va_arg (argp, int));
                  else
                    this_len = PPM_OS::sprintf
                      (bp, format, va_arg (argp, int));
                  PPM_UPDATE_COUNT (bspace, this_len);
                  break;

                case 'F': case 'f': case 'e': case 'E':
                case 'g': case 'G':
                  fp[0] = *format_str;
                  fp[1] = '\0';
                  if (can_check)
                    this_len = PPM_OS::snprintf
                      (bp, bspace, format, va_arg (argp, double));
                  else
                    this_len = PPM_OS::sprintf
                      (bp, format, va_arg (argp, double));
                  PPM_UPDATE_COUNT (bspace, this_len);
                  break;

                default:
                  // So, it's not a legit format specifier after all...
                  // Copy from the original % to where we are now, then
                  // continue with whatever comes next.
                  while (start_format != format_str && bspace > 0)
                    {
                      *bp++ = *start_format++;
                      bspace--;
                    }
                  if (bspace > 0)
                    {
                      *bp++ = *format_str;
                      bspace--;
                    }
				  //对于不能识别的，在后面加上0表示结束
				  *bp = 0;
                  break;
                }

              // Bump to the next char in the caller's format_str
              format_str++;
            }
          if (!skip_nul_locate)
            while (*bp != '\0') // Locate end of bp.
              bp++;
        }
    }

  *bp = '\0'; // Terminate bp, but don't auto-increment this!

  // Check that memory was not corrupted.
  if (bp >= this->msg_ + sizeof this->msg_)
    {
      abort_prog = 1;
      PPM_OS::fprintf (stderr,
                       "The following logged message is too long!\n");
    }

  // Copy the message from thread-specific storage into the transfer
  // buffer (this can be optimized away by changing other code...).
  log_record.msg_data (this->msg ());

  // Write the <log_record> to the appropriate location.
  int result = this->log (log_record,
                              abort_prog);

   return result;
}


int
PPM_Log_Msg::log (PPM_Log_Record &log_record,
                  int suppress_stderr)
{
  int result = 0;

  // Format the message and print it to stderr and/or ship it off to
  // the log_client daemon, and/or print it to the ostream.  Of
  // course, only print the message if "SILENT" mode is disabled.
  if (PPM_BIT_DISABLED (PPM_Log_Msg::flags_,
                        PPM_Log_Msg::SILENT))
    {
      // Do the callback, if needed, before acquiring the lock
      // to avoid holding the lock during the callback so we don't
      // have deadlock if the callback uses the logger.
      if (PPM_BIT_ENABLED (PPM_Log_Msg::flags_,
                           PPM_Log_Msg::MSG_CALLBACK)
          && this->msg_callback () != 0)
        this->msg_callback ()->Log (log_record);

      // Make sure that the lock is held during all this.
      PPM_MT (PPM_GUARD_RETURN (PPM_Recursive_Thread_Mutex, PPM_mon,
                                *PPM_Log_Msg_Manager::get_lock (),
                                -1));

      if (PPM_BIT_ENABLED (PPM_Log_Msg::flags_,
                           PPM_Log_Msg::STDERR)
          && !suppress_stderr) // This is taken care of by our caller.
        log_record.print (PPM_Log_Msg::local_host_,
//                          PPM_Log_Msg::flags_,
                          stderr);

      // This must come last, after the other two print operations
      // (see the <PPM_Log_Record::print> method for details).
      if (PPM_BIT_ENABLED (PPM_Log_Msg::flags_,
                           PPM_Log_Msg::OSTREAM)
          && this->msg_ostream () != 0)
        log_record.print (PPM_Log_Msg::local_host_,
//                          PPM_Log_Msg::flags_,
                          PPM_static_cast (FILE *,
                                           this->msg_ostream ()),
							stdlog_
                          );

   }

  return result;
}

// Calls log to do the actual print, but formats first.

int
PPM_Log_Msg::log_hexdump (PPM_Log_Priority log_priority,
                          const char *buffer,
                          size_t size,
                          const char *text)
{
	CStdAutoLock tm_autolock(&staticlock_);

  char buf[PPM_Log_Record::MAXLOGMSGLEN -
    PPM_Log_Record::VERBOSE_LEN - 58];
  // 58 for the HEXDUMP header;

  char *msg_buf;
  size_t text_sz = text ? PPM_OS_String::strlen(text) : 0;
  PPM_NEW_RETURN (msg_buf,
                  char[text_sz + 200],
                 -1);

  buf[0] = 0; // in case size = 0

  size_t len = PPM_OS::format_hexdump
    (buffer, size, buf, sizeof (buf) / sizeof (char) - text_sz);

  int sz = 0;

  if (text)
    sz = PPM_OS::sprintf (msg_buf,
                          PPM_LIB_TEXT ("%s - "),
                          text);

  sz += PPM_OS::sprintf (msg_buf + sz,
                         PPM_LIB_TEXT ("HEXDUMP %d bytes"),
                         size);

  if (len < size)
    PPM_OS::sprintf (msg_buf + sz,
                     PPM_LIB_TEXT (" (showing first %d bytes)"),
                     (int)len);

  // Now print out the formatted buffer.
  this->log (log_priority,
             PPM_LIB_TEXT ("%s\n%s"),
             msg_buf,
             buf);

  delete [] msg_buf;
  return 0;
}

void
PPM_Log_Msg::set (const char *filename,
                  int line,
                  int status,
                  int err,
                  PPM_OSTREAM_TYPE *os,
                  PPM_Log_Msg_Callback *c)
{
  PPM_TRACE ("PPM_Log_Msg::set");
  this->file (filename);
  this->linenum (line);
  this->op_status(status);
  this->msg_ostream (os,this->stdlog_);
  this->msg_callback (c);
  this->errnum(err);
}

void
PPM_Log_Msg::conditional_set (const char *filename,
                              int line,
                              int status,
                              int err)
{
  CStdAutoLock tm_autolock(&lock_);

  this->conditional_values_.is_set_ = 1;
  this->conditional_values_.file_ = filename;
  this->conditional_values_.line_ = line;
  this->conditional_values_.op_status_ = status;
  this->conditional_values_.errnum_ = err;
}

void
PPM_Log_Msg::op_status (int status)
{
  this->status_ = status;
}

int
PPM_Log_Msg::op_status (void)
{
  return this->status_;
}

int
PPM_Log_Msg::linenum (void)
{
  return this->linenum_;
}

void
PPM_Log_Msg::linenum (int l)
{
  this->linenum_ = l;
}

const char *
PPM_Log_Msg::file (void)
{
  return this->file_;
}

void
PPM_Log_Msg::file (const char *s)
{
  PPM_OS::strsncpy (this->file_, s, sizeof this->file_);
}

const char *
PPM_Log_Msg::msg (void)
{
  return this->msg_ + PPM_Log_Msg::msg_off_;
}

void
PPM_Log_Msg::msg (const char *m)
{
  PPM_OS::strsncpy (this->msg_, m,
                    (sizeof this->msg_ / sizeof (char)));
}

PPM_Log_Msg_Callback *
PPM_Log_Msg::msg_callback (void) const
{
  return this->msg_callback_;
}

PPM_Log_Msg_Callback *
PPM_Log_Msg::msg_callback (PPM_Log_Msg_Callback *c)
{
  PPM_Log_Msg_Callback *old = this->msg_callback_;
  this->msg_callback_ = c;
  return old;
}

PPM_OSTREAM_TYPE *
PPM_Log_Msg::msg_ostream (void) const
{
  return this->ostream_;
}

void
PPM_Log_Msg::msg_ostream (PPM_OSTREAM_TYPE *m,CStdLog *stdlog)
{
  CStdAutoLock tm_autolock(&lock_);

  this->ostream_ = m;
  this->stdlog_ = stdlog;
}

void
PPM_Log_Msg::local_host (const char *s)
{
  if (s)
    {
      PPM_OS::free ((void *) PPM_Log_Msg::local_host_);
      {
 //       PPM_NO_HEAP_CHECK;

        PPM_ALLOCATOR (PPM_Log_Msg::local_host_, PPM_OS::strdup (s));
      }
    }
}

const char *
PPM_Log_Msg::local_host (void) const
{
  return PPM_Log_Msg::local_host_;
}

pid_t
PPM_Log_Msg::getpid (void) const
{
  if (PPM_Log_Msg::pid_ == -1)
    PPM_Log_Msg::pid_ = PPM_OS::getpid ();

  return PPM_Log_Msg::pid_;
}

// Not inlined to help prevent having to include OS.h just to
// get PPM_DEBUG, et al, macros.
int
PPM_Log_Msg::last_error_adapter (void)
{
  return PPM_OS::last_error ();
}

int
PPM_Log_Msg::errnum (void)
{
  return this->errnum_;
}
void 
PPM_Log_Msg::errnum (int err)
{
  this->errnum_ = err;
}
