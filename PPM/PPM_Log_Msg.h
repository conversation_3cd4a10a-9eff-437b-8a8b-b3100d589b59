#ifndef PPM_LOG_MSG_H
#define PPM_LOG_MSG_H

#include "PPM_typedef.h"
#include "PPM_Log_Record.h"
#include "StdLock.h"



#if !defined (PPM_LACKS_PRAGMA_ONCE)
# pragma once
#endif /* PPM_LACKS_PRAGMA_ONCE */





typedef FILE PPM_OSTREAM_TYPE;

class PPM_Log_Msg_Callback
{
public:
  /// No-op virtual destructor.
	virtual ~PPM_Log_Msg_Callback (void){};

  /// Callback routine.  This is called when we want to log a message.
  /// Since this routine is pure virtual, it must be overwritten by the
  /// subclass.
  virtual void Log (PPM_Log_Record &log_record) = 0;
};

// ****************************************************************

#define PPM_LOG_MSG PPM_Log_Msg::instance ()

class PPM_Log_Record;
/**

包装的日志类，给外部提供的接口有：
instance 初始化一个日志管理器，注意，这个是初始化全局的，调用这个函数，得到的结果
         是相同的，调用方法：PPM_Log_Msg::instance()->log(...);
instance_new 初始化一个新的日志管理器，可以是单个对象自己的
program_name返回当前的程序名
open 初始化方法，外面可以不调用
set_flags(OSTREAM) 设置日志的输出如:STDERR, OSTREAM, MSG_CALLBACK等；
clr_flags(OSTREAM) 清除日志的输出
flags 返回日志的输出标志
disable_debug_messages  取消日志输出级别，如LM_DEBUG,LM_ERROR等
enable_debug_messages   设置日志输出级别，如LM_DEBUG,LM_ERROR等
msg_ostream    设置或获取文件输出的句柄
msg_callback   设置或获取回调对象的指针
priority_mask  设置或获取日志输出级别的掩码如 LM_DEBUG|LM_ERROR
log_priority_enabled 判断日志级别是否被设置
log  日子输出调用函数
log_hexdump 输出二进制

全局的变量有：
  static pid_t pid_;进程ID
  /// 标志输出到什么地方STDERR, LOGGER, OSTREAM, MSG_CALLBACK, etc.
  static u_long flags_;
  //  进程日志级别，这是所有的进程中的对象都有的级别，例如这里有LM_DEBUG ，而priority_mask_
//	中没有，照样输出
  static u_long process_priority_mask_;
   * 每个对象初始化时的初始化值，赋给priority_mask_
  static u_long default_priority_mask_;
  /// program name.
  static const char *program_name_;
  /// Name of the local host (used when printing messages).
  static const char *local_host_;

使用例:
    全局，程序开始:
	PPM_Log_Msg* tm_log = PPM_Log_Msg::instance();
	CStdFile tm_file;
	tm_file.Open("d:\\test_log.log","a+");
	tm_log->msg_ostream(tm_file.GetFile());
    tm_log->clr_flags(PPM_Log_Msg::STDERR);
    tm_log->set_flags(PPM_Log_Msg::OSTREAM);
    后续调用
    PPM_DEBUG((LM_DEBUG,"test\n"));

    局部
	PPM_Log_Msg* tm_lognew = PPM_Log_Msg::instance_new();
	CStdFile tm_filenew;
	tm_filenew.Open("d:\\testnew_log.log","a+");
	tm_lognew->msg_ostream(tm_filenew.GetFile());
    tm_lognew->clr_flags(PPM_Log_Msg::STDERR);
    tm_lognew->set_flags(PPM_Log_Msg::OSTREAM);
	调用
    tm_lognew->log(LM_DEBUG,"%D%d%s\n",1,"test");


*/


class PPM_Log_Msg
{

public:
  // Logger Flags.
  enum
  {
    /// Write messages to stderr.
    STDERR = 1,
    /// Write messages to the local client logger deamon.
    LOGGER = 2,
    /// Write messages to the ostream * stored in thread-specific
    /// storage.
    OSTREAM = 4,
    /// Write messages to the callback object.
    MSG_CALLBACK = 8,
    /// Do not print messages at all (just leave in thread-specific
    /// storage for later inspection).
    SILENT = 64,
 };

  // = Initialization and termination routines.

  /// Returns a pointer to the Singleton.
  static PPM_Log_Msg *instance (void);
  /// Returns a pointer to the PPM_Log_Msg.
  static PPM_Log_Msg *instance_new (void);

  /// Returns the current program name used for logging.
  static const char * program_name (void);
  /// Clears the flag from the default priority mask used to
  /// initialize PPM_Log_Msg instances.
  static void disable_debug_messages (PPM_Log_Priority priority = LM_DEBUG);

  /// Sets the flag in the default priority mask used to initialize
  /// PPM_Log_Msg instances.
  static void enable_debug_messages (PPM_Log_Priority priority = LM_DEBUG);

  static int last_error_adapter (void);
  
  /// Set the PPM_Log_Priority mask, returns original mask.
  //设置process_priority_mask_的值,全局的
  u_long priority_mask (u_long);
  
  /// Initialize logger.
  PPM_Log_Msg (void);

  /// cleanup logger.
  ~PPM_Log_Msg (void);

  int open (const char *prog_name,
            u_long options_flags = PPM_Log_Msg::STDERR);

  // = Set/get the options flags.

  /**
   * Enable the bits in the logger's options flags.
   */
  static void set_flags (u_long f);

  /**
   * Disable the bits in the logger's options flags.
   */
  static void clr_flags (u_long f);

  /**
   * Return the bits in the logger's options flags.
   */
  u_long flags (void);


  // = Notice that the following two function is equivalent to
  //   "void msg_ostream (HANDLE)" and "HANDLE msg_ostream (void)"
  //   on Windows CE.  There is no <iostream.h> support on CE.

  /// Update the ostream without overwriting the delete_ostream_ flag.
  void msg_ostream (PPM_OSTREAM_TYPE *,CStdLog *stdlog = NULL );

  /// Get the ostream that is used to print error messages.
  PPM_OSTREAM_TYPE *msg_ostream (void) const;
  // = Get/set the priority mask.
  /// Get the current PPM_Log_Priority mask.
  u_long priority_mask ();


  /// Return true if the requested priority is enabled.
  int log_priority_enabled (PPM_Log_Priority log_priority);

//   /// Return true if the requested priority is enabled.
//   int log_priority_enabled (PPM_Log_Priority log_priority,
//                             const char *,
//                             ...);


  /**
   * Set a new callback object and return the existing callback to
   * allow "chaining".  Note that PPM_Log_Msg_Callback objects are not
   * inherited when spawning a new thread, so you'll need to reset
   * them in each thread.
   */
  PPM_Log_Msg_Callback *msg_callback (PPM_Log_Msg_Callback *c);
  PPM_Log_Msg_Callback *msg_callback (void) const;

  /**
   * Set the line number, file name, operational status, error number,
   * restart flag, ostream, and the callback object.  This combines
   * all the other set methods into a single method.
   */
  void set (const char *file,
            int line,
            int op_status = -1,
            int errnum = 0,
            PPM_OSTREAM_TYPE *os = 0,
            PPM_Log_Msg_Callback *c = 0);

  /// These values are only actually set if the requested priority is
  /// enabled.
  void conditional_set (const char *file,
                        int line,
                        int op_status,
                        int errnum);

  /**
   * Format a message to the thread-safe ACE logging mechanism.  Valid
   * options (prefixed by '%', as in printf format strings) include:
   *  - 'A': print an PPM_timer_t value (which could be either double
   *         or PPM_UINT32.)
   *  - 'a': abort the program at this point abruptly.
   *  - 'c': print a character
   *  - 'C': print a character string
   *  - 'i', 'd': print a decimal number
   *  - 'I': indent according to nesting depth (obtained from
   *         PPM_Trace::get_nesting_indent()).
   *  - 'e', 'E', 'f', 'F', 'g', 'G': print a double
   *  - 'l': print line number where an error occurred.
   *  - 'M': print the name of the priority of the message.
   *  - 'm': return the message corresponding to errno value, e.g., as
   *         done by strerror()
   *  - 'N': print file name where the error occurred.
   *  - 'n': print the name of the program (or "<unknown>" if not set)
   *  - 'o': print as an octal number
   *  - 'P': print out the current process id
   *  - 'p': print out the appropriate errno message from sys_errlist,
   *         e.g., as done by perror()
   *  - 'Q': print out the uint64 number
   *  - '@': print a void* pointer (in hexadecimal)
   *  - 'r': call the function pointed to by the corresponding argument
   *  - 'R': print return status
   *  - 'S': print out the appropriate _sys_siglist entry corresponding
   *         to var-argument.
   *  - 's': print out a character string
   *  - 'T': print timestamp in hour:minute:sec:usec format.
   *  - 'D': print timestamp in month/day/year hour:minute:sec:usec format.
   *  - 't': print thread id (1 if single-threaded)
   *  - 'u': print as unsigned int
   *  - 'w': prints a wide character
   *  - 'W': print a wide character string
   *  - 'x': print as a hex number
   *  - 'X': print as a hex number
   *  - 'z': print an PPM_OS::WChar character
   *  - 'Z': print an PPM_OS::WChar character string
   *  - '%': print out a single percent sign, '%'
   */
  int log (PPM_Log_Priority priority, const char *format, ...);
  int log_date (PPM_Log_Priority priority, const char *format, ...);


  /**
   * Method to log hex dump.  This is useful for debugging.  Calls
   * log() to do the actual print, but formats first to make the chars
   * printable.
   */
  int log_hexdump (PPM_Log_Priority log_priority,
                   const char *buffer,
                   size_t size,
                   const char *text = 0);
  

private:
	  /// Get the value of the errnum (by convention this corresponds to
  /// errno).
  int errnum (void);
  void errnum (int err);

  void op_status (int status);
  int op_status ();
   /**
   * An alternative logging mechanism that makes it possible to
   * integrate variable argument lists from other logging mechanisms
   * into the ACE mechanism.
   */
  int log (const char *format,
               PPM_Log_Priority priority,
               va_list argp);

  /// Log a custom built log record to the currently enabled logging
  /// sinks.
  int log (PPM_Log_Record &log_record,
               int suppress_stderr = 0);
 /// Set the file name where an error occurred.
  void file (const char *);

  /// Get the file name where an error occurred.
  const char *file (void);

  /// Set the message that describes what type of error occurred.
  void msg (const char *);

  /// Get the message that describes what type of error occurred.
  const char *msg (void);

  /// Set the line number where an error occurred.
  void linenum (int);

  /// Get the line number where an error occurred.
  int linenum (void);

  /// Status of operation (-1 means failure, >= 0 means success).
  int status_;

  /// Type of error that occurred (see <sys/errno.h>).
  int errnum_;

  /// Line number where the error occurred.
  int linenum_;

  /// File where the error occurred.
  char file_[MAXPATHLEN + 1];

  /// The log message, which resides in thread-specific storage.  Note
  /// that only the current log message is stored here -- it will be
  /// overwritten by the subsequent call to log().
  char msg_[PPM_MAXLOGMSGLEN + 1]; // Add one for NUL-terminator.

  /// The ostream where logging messages can be written.
  PPM_OSTREAM_TYPE *ostream_;

  /// The ostream where logging messages can be written.
  CStdLog *stdlog_;

 /// The callback object.
  PPM_Log_Msg_Callback *msg_callback_;

  /**
    日志对象的日志级别，判别日志级别的时候与process_priority_mask_一起判断
   */
  u_long priority_mask_;

  // = The following fields are *not* kept in thread-specific storage.

  /// Process id of the current process.
  static pid_t pid_;

  /// Options flags used to hold the logger flag options, e.g., 
  /// STDERR, LOGGER, OSTREAM, MSG_CALLBACK, etc.
  static u_long flags_;

  /// Offset of msg_[].
//  static int msg_off_;
  int msg_off_;

  /**
    进程日志级别，这是所有的进程中的对象都有的级别，例如这里有LM_DEBUG ，而priority_mask_
	中没有，照样输出
   */
  static u_long process_priority_mask_;
    /**
   * 每个对象初始化时的初始化值，赋给priority_mask_
   */
  static u_long default_priority_mask_;

  /// Records the program name.
  static const char *program_name_;

  /// Name of the local host (used when printing messages).
  static const char *local_host_;

  /// Optimize reading of the pid (avoids a system call if the value is
  /// cached...).
  pid_t getpid (void) const;

  /// Get the name of the local host.
  const char *local_host (void) const;

  /// Set the name of the local host.
  static void local_host (const char *);


  /// Anonymous struct since there will only be one instance.  This
  /// struct keeps information stored away in case we actually end up
  /// calling log() if the log priority is correct.
  struct
  {
    int is_set_;
    const char *file_;
    int line_;
    int op_status_;
    int errnum_;
  } conditional_values_;
  /// Are we deleting this ostream?
  int delete_ostream_;

  static CStdLock staticlock_;
  CStdLock lock_;
  /// For cleanup, at program termination.
  static void close (void);

  // = Disallow these operations.
  PPM_Log_Msg &operator= (const PPM_Log_Msg &);
  PPM_Log_Msg (const PPM_Log_Msg &);
};


#endif /* PPM_LOG_MSG_H */
