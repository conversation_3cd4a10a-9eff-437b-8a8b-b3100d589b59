#ifndef PPM_LOG_RECORD_H
#define PPM_LOG_RECORD_H
#include "./PPM_Time_Value.h"
#include "./PPM_OS.h"
#include "../stdclass/StdMonitor.h"

#if !defined (PPM_LACKS_PRAGMA_ONCE)
# pragma once
#endif /* PPM_LACKS_PRAGMA_ONCE */


enum PPM_Log_Priority
{
  // = Note, this first argument *must* start at 1!

  /// Shutdown the logger (decimal 1).
  LM_SHUTDOWN = 01,

  /// Messages indicating function-calling sequence (decimal 2).
  LM_TRACE = 02,

  /// Messages that contain information normally of use only when
  /// debugging a program (decimal 4).
  LM_DEBUG = 04,

  /// Informational messages (decimal 8).
  LM_INFO = 010,

  /// Conditions that are not error conditions, but that may require
  /// special handling (decimal 16).
  LM_NOTICE = 020,

  /// Warning messages (decimal 32).
  LM_WARNING = 040,

  /// Initialize the logger (decimal 64).
  LM_STARTUP = 0100,

  /// Error messages (decimal 128).
  LM_ERROR = 0200,

  /// Critical conditions, such as hard device errors (decimal 256).
  LM_CRITICAL = 0400,

  /// A condition that should be corrected immediately, such as a
  /// corrupted system database (decimal 512).
  LM_ALERT = 01000,

  /// A panic condition.  This is normally broadcast to all users
  /// (decimal 1024).
  LM_EMERGENCY = 02000,

  /// The maximum logging priority.
  LM_MAX = LM_EMERGENCY,

  /// Do not use!!  This enum value ensures that the underlying
  /// integral type for this enum is at least 32 bits.
  LM_ENSURE_32_BITS = 0x7FFFFFFF
};

/// Defines the structure of an ACE logging record.
class PPM_Log_Record
{
public:
  enum
  {
    /// Maximum size of a logging message.
    MAXLOGMSGLEN = PPM_MAXLOGMSGLEN,

    /// Most restrictive alignment.
    ALIGN_WORDB  = 8,

    /// Size used by verbose mode.
    /// 20 (date) + 15 (host_name) + 10 (pid) + 10 (type)
    ///           + 4 (@) ... + ? (progname)
    VERBOSE_LEN = 128,

    /// Maximum size of a logging message with the verbose headers
    MAXVERBOSELOGMSGLEN = VERBOSE_LEN + MAXLOGMSGLEN
  };

  // = Initialization
  /**
   * Create a <Log_Record> and set its priority, time stamp, and
   * process id.
   * Create a <Log_Record> and set its priority, time stamp, and
   * process id.
   */
  PPM_Log_Record (void);
  PPM_Log_Record (PPM_Log_Priority lp,
                  const PPM_Time_Value &time_stamp,
                  long p = 0);
  /// Default dtor.
  ~PPM_Log_Record (void);


  /// Write the contents of the logging record to the appropriate
  /// <FILE>.
  int print (const char host_name[],
             FILE *fp,CStdLog * pStdLog = NULL);

  int format_msg (const char host_name[],
                  char *msg);

  /**
   * Returns a character array with the string form of the
   * <PPM_Log_Priority> parameter.  This is used for the verbose
   * printing format.
   */
  static const char *priority_name (PPM_Log_Priority p);

  // IMPORTANT: <name> must be a statically allocated const char*
  static void priority_name (PPM_Log_Priority p,
                             const char *name);

//   // = Marshall/demarshall
//   /// Encode the <Log_Record> for transmission on the network.
//   void encode (void);
// 
//   /// Decode the <Log_Record> received from the network.
//   void decode (void);

  // = Set/get methods

  /// Get the type of the <Log_Record>.
  long type (void) const;

  /// Set the type of the <Log_Record>.
  void type (long);

  /**
   * Get the priority of the <Log_Record> <type_>.  This is computed
   * as the base 2 logarithm of <type_> (which must be a power of 2,
   * as defined by the enums in <PPM_Log_Priority>).
   */
  u_long priority (void) const;

  /// Set the priority of the <Log_Record> <type_> (which must be a
  /// power of 2, as defined by the enums in <PPM_Log_Priority>).
  void priority (u_long num);

  /// Get the length of the <Log_Record>.
  long length (void) const;

  /// Set the length of the <Log_Record>.
  void length (long);

  /// Get the time stamp of the <Log_Record>.
  PPM_Time_Value time_stamp (void) const;

  /// Set the time stamp of the <Log_Record>.
  void time_stamp (const PPM_Time_Value &);

  /// Get the message data of the <Log_Record>.
  const char *msg_data (void) const;

  /// Set the message data of the <Log_Record>.
  void msg_data (const char *data);

  /// Get the size of the message data of the <Log_Record>, including
  /// a byte for the NUL.
  size_t msg_data_len (void) const;

  /// Dump the state of an object.
  void dump (void) const;

private:
  /// Round up to the alignment restrictions.
  void round_up (void);
  /**
   * Total length of the logging record in bytes.  This field *must*
   * come first in order for various IPC framing mechanisms to work
   * correctly.  In addition, the field must be an PPM_INT32 in order
   * to be passed portably across platforms.
   */
  int length_;

  /// Type of logging record.
  PPM_UINT32 type_;

  /// Time that the logging record was generated.
  PPM_UINT32 secs_;
  PPM_UINT32 usecs_;
  /// Id of process that generated the logging record.
  PPM_UINT32 pid_;

  /// Logging record data
  char msg_data_[MAXLOGMSGLEN + 1]; // Add one for NUL-terminator.

  /// Symbolic names for the <PPM_Log_Priority> enums.
  static const char *priority_names_[];
};

#if !defined (PPM_LACKS_INLINE_FUNCTIONS)
#include "./PPM_Log_Record.inl"
#endif

#endif /* PPM_LOG_RECORD_H */
