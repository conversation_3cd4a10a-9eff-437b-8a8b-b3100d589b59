#ifndef _PPM_MACRO_H_
#define _PPM_MACRO_H_

# if defined (PPM_LACKS_INLINE_FUNCTIONS)
#   define (PPM_INLINE)
# else
#   define PPM_INLINE inline
# endif

#define PPM_INT64_LITERAL(n) n ## i64

#define PPM_INVALID_HANDLE -1

#define PPM_PROTOCOL_FAMILY_INET PF_INET

#define PPM_NONBLOCK 1 
#define PPM_dynamic_cast(TYPE, EXPR)      dynamic_cast<TYPE> (EXPR)
#define PPM_static_cast(TYPE, EXPR)       static_cast<TYPE> (EXPR)
#define PPM_reinterpret_cast(TYPE, EXPR)  reinterpret_cast<TYPE> (EXPR)
#define PPM_const_cast(TYPE, EXPR)        const_cast<TYPE> (EXPR)
#define PPM_BIT_ENABLED(WORD, BIT) (((WORD) & (BIT)) != 0)
#define PPM_BIT_DISABLED(WORD, BIT) (((WORD) & (BIT)) == 0)
#define PPM_SET_BITS(WORD, BITS) (WORD |= (BITS))
#define PPM_CLR_BITS(WORD, BITS) (WORD &= ~(BITS))

#define PPM_OSCALL_RETURN(OP,TYPE,FAILVALUE) do { TYPE PPM_result_ = FAILVALUE; PPM_result_ = PPM_result_; return OP; } while (0)
#define PPM_OSCALL(OP,TYPE,FAILVALUE,RESULT) do { RESULT = (TYPE) OP; } while (0)

#define PPM_WIN32CALL_RETURN(X,TYPE,FAILVALUE) \
  do { \
    TYPE PPM_result_; \
    PPM_result_ = (TYPE) X; \
    return PPM_result_; \
  } while (0)

#define PPM_ADAPT_RETVAL(OP,RESULT) ((RESULT = (OP)) != 0 ? (errno = RESULT, -1) : 0)
#define PPM_ADAPT_ERETVAL(OP,RESULT) ((RESULT = (OP)) == 0 ? (errno = RESULT, -1) : 0)

#define PPM_ERRNO_TYPE int

#define PPM_NON_BLOCKING_BUG_DELAY 35000
#define PPM_DEFAULT_SELECT_REACTOR_SIZE 256
#define PPM_DEFAULT_BACKLOG 5 
#define MAXHOSTNAMELEN 256 


#define PPM_NEW_RETURN(POINTER,CONSTRUCTOR,RET_VAL) \
	do { POINTER = new CONSTRUCTOR; \
		if (POINTER == 0) { errno = ENOMEM; return RET_VAL; } \
	} while (0)
#define PPM_ALLOCATOR_RETURN(POINTER,ALLOCATOR,RET_VAL) \
	do { POINTER = ALLOCATOR; \
		if (POINTER == 0) { errno = ENOMEM; return RET_VAL; } \
	} while (0)
#define PPM_ALLOCATOR(POINTER,ALLOCATOR) \
	do { POINTER = ALLOCATOR; \
		if (POINTER == 0) { errno = ENOMEM; return; } \
	} while (0)
#define PPM_FREE_FUNC ::free 
#define PPM_NEW(POINTER,CONSTRUCTOR) do { POINTER = new CONSTRUCTOR; if (POINTER == 0) { errno = ENOMEM; return; } } while (0)
#define PPM_SEH_TRY if(1)
#define PPM_SEH_FINALLY if(1)

#define ETIME_ ETIMEDOUT

#define PPM_NSIG NSIG
#define PPM_SWAP_LONG(L) ((PPM_SWAP_WORD ((L) & 0xFFFF) << 16) \
            | PPM_SWAP_WORD(((L) >> 16) & 0xFFFF))
#define PPM_SWAP_WORD(L) ((((L) & 0x00FF) << 8) | (((L) & 0xFF00) >> 8))
#define PPM_HTONL(X) PPM_SWAP_LONG (X)

//THREADS
#define THR_NEW_LWP             0x00000002
#define THR_DETACHED            0x00000040
#define THR_SUSPENDED           0x00000080
#define THR_DAEMON              0x00000100
#define THR_JOINABLE            0x00010000
#define THR_SCHED_FIFO          0x00020000
#define THR_SCHED_RR            0x00040000
#define THR_SCHED_DEFAULT       0x00080000
#define THR_SCOPE_SYSTEM        0x00000001
#define THR_SCOPE_PROCESS       0x00200000
#define THR_INHERIT_SCHED       0x00400000
#define THR_EXPLICIT_SCHED      0x00800000
#define THR_SCHED_IO            0x01000000


#define PPM_TIMECRITICAL_THREAD_PRIORITY     4
#define PPM_HIGHEST_THREAD_PRIORITY          3
#define PPM_ABOVENORMAL_THREAD_PRIORITY      2
#define PPM_NORMAL_THREAD_PRIORITY           1
#define PPM_BELOWNORMAL_THREAD_PRIORITY      0

#define PPM_DEFAULT_THREAD_PRIORITY          PPM_NORMAL_THREAD_PRIORITY


#define PPM_SYSCALL_FAILED -1
//#ifndef THR_SUSPENDED

//log msg
#define MAXPATHLEN  1024

//#ifndef MAXNAMLEN

#define PPM_MAXLOGMSGLEN (4 * 1024) 
#define PPM_LIB_TEXT(STRING) STRING
#define PPM_DEFAULT_LOG_STREAM 0
#define PPM_MT(x)

#define PPM_TRACE(x)
#define PPM_OS_TRACE(x)
#define PPM_ERROR_RETURN(x,y)
#define PPM_UNUSED_ARG(x)
#define PPM_ERROR(X) \
  do { \
    int __PPM_error = PPM_Log_Msg::last_error_adapter (); \
    PPM_Log_Msg *PPM___ = PPM_Log_Msg::instance (); \
    PPM___->conditional_set (__FILE__, __LINE__, -1, __PPM_error); \
    PPM___->log X; \
  } while (0)
#define PPM_DEBUG(X) \
  do { \
    int __PPM_error = PPM_Log_Msg::last_error_adapter (); \
    PPM_Log_Msg *PPM___ = PPM_Log_Msg::instance (); \
    PPM___->conditional_set (__FILE__, __LINE__, 0, __PPM_error); \
    PPM___->log_date X; \
  } while (0)

#define PPM_TEXT_FormatMessage ::FormatMessageA


#define PPM_FINI\
	delete PPM_Client<PPM_Tcp_Client>::Instance(); \
	delete PPM_Reactor::instance(); \
 	delete PPM_Thread_Manager::instance(); \
	delete PPM_Log_Msg::instance(); \
    PPM_Log_Msg *PPM___ = PPM_Public_Var::instance_SockLog(); \
	delete PPM___; \
	
//add for unix
#define PPM_NOTSUP_RETURN(x) return x
#define PPM_FAIL_RETURN(x) return x
#define PPM_NOTREACHED(x)

// #     if !defined (PTHREAD_CREATE_JOINABLE)
// #       if defined (PTHREAD_CREATE_UNDETACHED)
// #         define PTHREAD_CREATE_JOINABLE PTHREAD_CREATE_UNDETACHED
// #       else
// #         define PTHREAD_CREATE_JOINABLE 0
// #       endif /* PTHREAD_CREATE_UNDETACHED */
// #     endif /* PTHREAD_CREATE_JOINABLE */

//#     if !defined (PTHREAD_CREATE_DETACHED)
//#       define PTHREAD_CREATE_DETACHED 1
//#     endif /* PTHREAD_CREATE_DETACHED */


//#     define THR_DETACHED            0x00000040
//#     define THR_SUSPENDED           0x00000080
#     define THR_DAEMON              0x00000100
//#     define THR_JOINABLE            0x00010000
#     define THR_SCHED_FIFO          0x00020000
#     define THR_SCHED_RR            0x00040000
#     define THR_SCHED_DEFAULT       0x00080000

#   if !defined (PPM_HAS_PTHREADS)
#     define PPM_SCHED_OTHER 0
#     define PPM_SCHED_FIFO 1
#     define PPM_SCHED_RR 2
#   endif /* ! PPM_HAS_PTHREADS */

#   if defined (PPM_HAS_PTHREADS)
#     define PPM_SCHED_OTHER SCHED_OTHER
#     define PPM_SCHED_FIFO SCHED_FIFO
#     define PPM_SCHED_RR SCHED_RR
#   endif

#define LPSECURITY_ATTRIBUTES int
//	#ifndef INADDR_ANY
//		#define INADDR_ANY  (unsigned long)0x00000000
//	#endif
#define SOCKET_ERROR            (-1)


#define PPM_DEFAULT_SYNCH_TYPE 0
#define PPM_DIV_BY_WORDSIZE(x) ((x) / ((int) PPM_Handle_Set::WORDSIZE))
#define PPM_MULT_BY_WORDSIZE(x) ((x) * ((int) PPM_Handle_Set::WORDSIZE))
	

#define PPM_MSB_MASK (~((fd_mask) 1 << (NFDBITS - 1)))


#if ! defined WIN32
	#define sys_nerr _sys_nerr
	#if !defined (INADDR_NONE)
		#define INADDR_NONE ((PPM_UINT32) 0xffffffff)
	#endif /* INADDR_NONE */
#endif


#if defined (PPM_HAS_CHARPTR_SPRINTF)
#  define PPM_SPRINTF_ADAPTER(X) ::strlen (X)
#else
#  define PPM_SPRINTF_ADAPTER(X) X
#endif /* PPM_HAS_CHARPTR_SPRINTF */

#ifndef THR_SCOPE_SYSTEM
	#define THR_SCOPE_SYSTEM 0
#endif


#endif

