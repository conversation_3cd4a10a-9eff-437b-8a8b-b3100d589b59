#include "./PPM_OS.h"
#include "./PPM_Handle_Set.h"
#include "./PPM_INET_Addr.h"
#include "./PPM_Thread_Adapter.h"


#if defined (PPM_LACKS_INLINE_FUNCTIONS)
# include "./PPM_OS.inl"
#endif

#ifdef WIN32
siginfo_t::siginfo_t (PPM_HANDLE handle)
  : si_handle_ (handle),
    si_handles_ (&handle)
{
}

siginfo_t::siginfo_t (PPM_HANDLE *handles)
  : si_handle_ (handles[0]),
    si_handles_ (handles)
{
}
#endif

PPM_Countdown_Time::PPM_Countdown_Time (PPM_Time_Value *max_wait_time)
  : max_wait_time_ (max_wait_time),
    stopped_ (0)
{
  this->start ();
}

PPM_Countdown_Time::~PPM_Countdown_Time (void)
{
  this->stop ();
}

int
PPM_OS::inet_aton (const char *host_name, struct in_addr *addr)
{

  PPM_UINT32 ip_addr = PPM_OS::inet_addr (host_name);

  if (ip_addr == INADDR_NONE
      // Broadcast addresses are weird...
      && PPM_OS::strcmp (host_name, "***************") != 0)
    return 0;
  else if (addr == 0)
    return 0;
  else
    {
      addr->s_addr = ip_addr;  // Network byte ordered
      return 1;
    }
}
//处理非阻塞的连接
PPM_HANDLE
PPM_OS::handle_timed_complete (PPM_HANDLE h,
                            const PPM_Time_Value *timeout,
                            int is_tli)
{

#if !defined (WIN32) && defined (PPM_HAS_POLL) && defined (PPM_HAS_LIMITED_SELECT)

  struct pollfd fds;

  fds.fd = h;
  fds.events = POLLIN | POLLOUT;
  fds.revents = 0;

#else
  PPM_Handle_Set rd_handles;
  PPM_Handle_Set wr_handles;

  rd_handles.set_bit (h);
  wr_handles.set_bit (h);
#endif /* !WIN32 && PPM_HAS_POLL && PPM_HAS_LIMITED_SELECT */

#if defined (WIN32)
  // Winsock is different - it sets the exception bit for failed connect,
  // unlike other platforms, where the read bit is set.
  PPM_Handle_Set ex_handles;
  ex_handles.set_bit (h);
#endif /* WIN32 */

  int need_to_check = 0;
  int known_failure = 0;

#if defined (WIN32)
  int n = PPM_OS::select (0,    // Ignored on Windows: int (h) + 1,
                          0,
                          wr_handles,
                          ex_handles,
                          timeout);
#else
# if defined (PPM_HAS_POLL) && defined (PPM_HAS_LIMITED_SELECT)

  int n = PPM_OS::poll (&fds, 1, timeout);

# else
  int n = PPM_OS::select (int (h) + 1,
                          rd_handles,
                          wr_handles,
                          0,
                          timeout);
# endif /* PPM_HAS_POLL && PPM_HAS_LIMITED_SELECT */
#endif /* WIN32 */

  // If we failed to connect within the time period allocated by the
  // caller, then we fail (e.g., the remote host might have been too
  // busy to accept our call).
  if (n <= 0)
    {
      if (n == 0 && timeout != 0)
        errno = ETIME_;
      return PPM_INVALID_HANDLE;
    }

  // Usually, a ready-for-write handle is successfully connected, and
  // ready-for-read (exception on Win32) is a failure. On fails, we
  // need to grab the error code via getsockopt. On possible success for
  // any platform where we can't tell just from select() (e.g. AIX),
  // we also need to check for success/fail.
#if defined (WIN32)
  PPM_UNUSED_ARG (is_tli);

  // On Win32, ex_handle set indicates a failure. We'll do the check
  // to try and get an errno value, but the connect failed regardless of
  // what getsockopt says about the error.
  if (ex_handles.is_set (h))
    {
      need_to_check = 1;
      known_failure = 1;
    }
#elif defined (VXWORKS)
  PPM_UNUSED_ARG (is_tli);

  // Force the check on VxWorks.  The read handle for "h" is not set,
  // so "need_to_check" is false at this point.  The write handle is
  // set, for what it's worth.
  need_to_check = 1;
#else
  if (is_tli)

# if defined (PPM_HAS_POLL) && defined (PPM_HAS_LIMITED_SELECT)
    need_to_check = (fds.revents & POLLIN) && !(fds.revents & POLLOUT);
# else
    need_to_check = rd_handles.is_set (h) && !wr_handles.is_set (h);
# endif /* PPM_HAS_POLL && PPM_HAS_LIMITED_SELECT */

  else
#if defined(AIX)
    // AIX is broken... both success and failed connect will set the
    // write handle only, so always check.
    need_to_check = 1;
#else
# if defined (PPM_HAS_POLL) && defined (PPM_HAS_LIMITED_SELECT)
  need_to_check = (fds.revents & POLLIN);
# else
  need_to_check = rd_handles.is_set (h);
# endif /* PPM_HAS_POLL && PPM_HAS_LIMITED_SELECT */
#endif /* AIX */
#endif /* WIN32 */

  if (need_to_check)
    {
#if defined (SOL_SOCKET) && defined (SO_ERROR)
      int sock_err = 0;
      int sock_err_len = sizeof (sock_err);
      int sockopt_ret = PPM_OS::getsockopt (h, SOL_SOCKET, SO_ERROR,
                                            (char *)&sock_err, &sock_err_len);
      if (sockopt_ret < 0)
        {
          h = PPM_INVALID_HANDLE;
        }

      if (sock_err != 0 || known_failure)
        {
          h = PPM_INVALID_HANDLE;
          errno = sock_err;
        }
#else
      char dummy;

      // The following recv() won't block provided that the
      // PPM_NONBLOCK flag has not been turned off .
      n = PPM_OS::recv (h, &dummy, 1, MSG_PEEK);

      // If no data was read/peeked at, check to see if it's because
      // of a non-connected socket (and therefore an error) or there's
      // just no data yet.
      if (n <= 0)
        {
          if (n == 0)
            {
              errno = WSAECONNREFUSED;
              h = PPM_INVALID_HANDLE;
            }
          else if (errno != WSAEWOULDBLOCK && errno != EAGAIN)
            h = PPM_INVALID_HANDLE;
        }
#endif
    }

  // 1. The HANDLE is ready for writing and doesn't need to be checked or
  // 2. recv() returned an indication of the state of the socket - if there is
  // either data present, or a recv is legit but there's no data yet,
  // the connection was successfully established.
  return h;
}
// Wait up to <timeout> amount of time to accept a connection.

int
PPM_OS::handle_timed_accept (PPM_HANDLE listener,
                          PPM_Time_Value *timeout,
                          int restart)
{
  // Make sure we don't bomb out on erroneous values.
  if (listener == PPM_INVALID_HANDLE)
    return -1;

#if defined (PPM_HAS_POLL) && defined (PPM_HAS_LIMITED_SELECT)

  struct pollfd fds;

  fds.fd = listener;
  fds.events = POLLIN;
  fds.revents = 0;

#else
  // Use the select() implementation rather than poll().
  PPM_Handle_Set rd_handle;
  rd_handle.set_bit (listener);
#endif /* PPM_HAS_POLL && PPM_HAS_LIMITED_SELECT */

  // We need a loop here if <restart> is enabled.

  for (;;)
    {
#if defined (PPM_HAS_POLL) && defined (PPM_HAS_LIMITED_SELECT)

      int n = PPM_OS::poll (&fds, 1, timeout);

#else
      int select_width;
#  if defined (PPM_WIN64)
      // This arg is ignored on Windows and causes pointer truncation
      // warnings on 64-bit compiles.
      select_width = 0;
#  else
      select_width = int (listener) + 1;
#  endif /* PPM_WIN64 */
      int n = PPM_OS::select (select_width,
                              rd_handle, 0, 0,
                              timeout);
#endif /* PPM_HAS_POLL && PPM_HAS_LIMITED_SELECT */

      switch (n)
        {
        case -1:
          if (errno == EINTR && restart)
            continue;
          else
            return -1;
          /* NOTREACHED */
        case 0:
          if (timeout != 0
              && timeout->sec () == 0
              && timeout->usec () == 0)
            errno = EWOULDBLOCK;
          else
            errno = ETIMEDOUT;
          return -1;
          /* NOTREACHED */
        case 1:
          return 0;
          /* NOTREACHED */
        default:
          errno = EINVAL;
          return -1;
          /* NOTREACHED */
        }
    }
}
// Flags are file status flags to turn on.

int
PPM_OS::set_flags (PPM_HANDLE handle, int flags)
{
  PPM_TRACE ("PPM_Flag_Manip::set_flags");
#if defined (WIN32) || defined (VXWORKS) || defined (PPM_LACKS_FCNTL)
  switch (flags)
    {
    case PPM_NONBLOCK:
      // nonblocking argument (1)
      // blocking:            (0)
      {
        u_long nonblock = 1;
        return PPM_OS::ioctl (handle, FIONBIO, &nonblock);
      }
    default:
      PPM_NOTSUP_RETURN (-1);
    }
#else
  int val = PPM_OS::fcntl (handle, F_GETFL, 0);

  if (val == -1)
    return -1;

  // Turn on flags.
  PPM_SET_BITS (val, flags);

  if (PPM_OS::fcntl (handle, F_SETFL, val) == -1)
    return -1;
  else
    return 0;
#endif /* WIN32 || PPM_LACKS_FCNTL */
	
}

// Flags are the file status flags to turn off.

int
PPM_OS::clr_flags (PPM_HANDLE handle, int flags)
{
  PPM_TRACE ("PPM_Flag_Manip::clr_flags");

#if defined (WIN32) || defined (VXWORKS) || defined (PPM_LACKS_FCNTL)
  switch (flags)
    {
    case PPM_NONBLOCK:
      // nonblocking argument (1)
      // blocking:            (0)
      {
        u_long nonblock = 0;
        return PPM_OS::ioctl (handle, FIONBIO, &nonblock);
      }
    default:
      PPM_NOTSUP_RETURN (-1);
    }
#else
  int val = PPM_OS::fcntl (handle, F_GETFL, 0);

  if (val == -1)
    return -1;

  // Turn flags off.
  PPM_CLR_BITS (val, flags);

  if (PPM_OS::fcntl (handle, F_SETFL, val) == -1)
    return -1;
  else
    return 0;
#endif /* WIN32 || PPM_LACKS_FCNTL */
	
}

// Bind socket to an unused port.

int
PPM_OS::bind_port (PPM_HANDLE handle,
                             PPM_UINT32 ip_addr)
{
  PPM_TRACE ("PPM_Sock_Connect::bind_port");

  PPM_INET_Addr addr ((u_short)0, ip_addr);

#if !defined (PPM_LACKS_WILDCARD_BIND)
  // The OS kernel should select a free port for us.
  return PPM_OS::bind (handle,
                       (sockaddr*)addr.get_addr(),
                       addr.get_size());
#else
  static u_short upper_limit = PPM_MAX_DEFAULT_PORT;
  int round_trip = upper_limit;
  int lower_limit = IPPORT_RESERVED;

  // We have to select the port explicitly.

  for (;;)
    {
      addr.set((u_short)upper_limit,ip_addr);

      if (PPM_OS::bind (handle,
                        (sockaddr*)addr.get_addr()
                        addr.get_size()) >= 0)
        {
#if defined (WIN32)
          upper_limit--;
#endif /* WIN32 */
          return 0;
        }
      else if (errno != EADDRINUSE)
        return -1;
      else
        {
          upper_limit--;

          // Wrap back around when we reach the bottom.
          if (upper_limit <= lower_limit)
            upper_limit = PPM_MAX_DEFAULT_PORT;

          // See if we have already gone around once!
          if (upper_limit == round_trip)
            {
              errno = EAGAIN;
              return -1;
            }
        }
    }
#endif /* PPM_HAS_WILDCARD_BIND */
  
//  PPM_TRACE ("PPM_Sock_Connect::bind_port");
//
//  PPM_INET_Addr addr ((u_short)0, ip_addr);
//
//  // The OS kernel should select a free port for us.
//  return PPM_OS::bind (handle,
//                       (sockaddr*)addr.get_addr(),
//                       addr.get_size());

}

void
PPM_OS::free (void *ptr)
{
  PPM_FREE_FUNC ((void*) (ptr));
}


int
PPM_OS::thr_create (PPM_THR_VOID_FUNC func,
                    void *args,
                    long flags,
                    PPM_thread_t *thr_id,
                    PPM_hthread_t *thr_handle,
                    long priority,
                    void *stack,
                    size_t stacksize,
                    PPM_Base_Thread_Adapter *thread_adapter)
{
	PPM_OS_TRACE ("PPM_OS::thr_create");

	if (PPM_BIT_DISABLED (flags, THR_DETACHED) &&
			PPM_BIT_DISABLED (flags, THR_JOINABLE))
		PPM_SET_BITS (flags, THR_JOINABLE);

# if defined (PPM_NO_THREAD_ADAPTER)
#   define  PPM_THREAD_FUNCTION  func
#   define  PPM_THREAD_ARGUMENT  args
# else /* ! defined (PPM_NO_THREAD_ADAPTER) */
#   if defined (PPM_PSOS)
#     define  PPM_THREAD_FUNCTION (PSOS_TASK_ENTRY_POINT) thread_args->entry_point ()
#   else
#     define  PPM_THREAD_FUNCTION  thread_args->entry_point ()
#   endif /* defined (PPM_PSOS) */
#   define  PPM_THREAD_ARGUMENT  thread_args
# endif /* ! defined (PPM_NO_THREAD_ADAPTER) */


	PPM_Base_Thread_Adapter *thread_args;
	if (thread_adapter == 0)

# if defined (PPM_HAS_WIN32_STRUCTURAL_EXCEPTIONS)
		PPM_NEW_RETURN (thread_args,
                    PPM_OS_Thread_Adapter (func, args,
                                           (PPM_THR_C_FUNC) PPM_thread_adapter,
                                           PPM_OS_Object_Manager::seh_except_selector(),
                                           PPM_OS_Object_Manager::seh_except_handler()),
                    -1);
# else
  PPM_NEW_RETURN (thread_args,
                  PPM_Thread_Adapter (func, args,
								 (PPM_THR_C_VOID_FUNC) PPM_thread_adapter),
		  -1);

# endif /* PPM_HAS_WIN32_STRUCTURAL_EXCEPTIONS */
	else
		thread_args = thread_adapter;


	PPM_thread_t tmp_thr;

	if (thr_id == 0)
		thr_id = &tmp_thr;

	PPM_hthread_t tmp_handle;
	if (thr_handle == 0)
		thr_handle = &tmp_handle;


#if defined WIN32
  PPM_UNUSED_ARG (stack);
    {
	int start_suspended = PPM_BIT_ENABLED (flags, THR_SUSPENDED);

	if (priority != PPM_DEFAULT_THREAD_PRIORITY)
	// If we need to set the priority, then we need to start the
	// thread in a suspended mode.
	PPM_SET_BITS (flags, THR_SUSPENDED);

	*thr_handle = (void *) PPM_BEGINTHREADEX (0,
											PPM_static_cast
											   (u_int, stacksize),
											thread_args->entry_point (),
											thread_args,
											flags,
											thr_id);

	if (priority != PPM_DEFAULT_THREAD_PRIORITY && *thr_handle != 0)
	{
	  // Set the priority of the new thread and then let it
	  // continue, but only if the user didn't start it suspended
	  // in the first place!
	  PPM_OS::thr_setprio (*thr_handle, priority);

	  if (start_suspended == 0)
		PPM_OS::thr_continue (*thr_handle);
	}
    }

  // Close down the handle if no one wants to use it.
  if (thr_handle == &tmp_handle)
    ::CloseHandle (tmp_handle);

  if (*thr_handle != 0)
    return 0;
  else
    PPM_FAIL_RETURN (-1);
  /* NOTREACHED */
#else
  int result;
  pthread_attr_t attr;

  if (::pthread_attr_init (&attr) != 0)
    return -1;

  // if (stacksize != 0)
  //   {
  //     size_t size = stacksize;

  //       if (PPM_ADAPT_RETVAL(pthread_attr_setstacksize (&attr, size), result) == -1)
  //         {
  //           ::pthread_attr_destroy (&attr);
  //           return -1;
  //         }
  //   }

  // // *** Set Stack Address
  // if (stack != 0)
  //   {
  //     if (::pthread_attr_setstackaddr (&attr, stack) != 0)
  //       {
  //         ::pthread_attr_destroy (&attr);
  //         return -1;
  //       }
  //   }

  if (stacksize != 0 && stack != 0)
  {
    if (pthread_attr_setstack(&attr, stack, stacksize) != 0)
    {
      ::pthread_attr_destroy (&attr);
      return -1;
    }
  }

  // *** Deal with various attributes
  if (flags != 0)
    {
      // *** Set Detach state
      if (PPM_BIT_ENABLED (flags, THR_DETACHED)
          || PPM_BIT_ENABLED (flags, THR_JOINABLE))
        {
          int dstate = PTHREAD_CREATE_JOINABLE;

          if (PPM_BIT_ENABLED (flags, THR_DETACHED))
            dstate = PTHREAD_CREATE_DETACHED;

              if (PPM_ADAPT_RETVAL(::pthread_attr_setdetachstate (&attr, dstate),
                                   result) != 0)
                {
                  ::pthread_attr_destroy (&attr);
                  return -1;
                }
        }

      // Note: if PPM_LACKS_SETDETACH and THR_DETACHED is enabled, we
      // call ::pthread_detach () below.  If THR_DETACHED is not
      // enabled, we call ::pthread_detach () in the Thread_Manager,
      // after joining with the thread.

      // If we wish to set the priority explicitly, we have to enable
      // explicit scheduling, and a policy, too.
      if (priority != PPM_DEFAULT_THREAD_PRIORITY)
        {
          PPM_SET_BITS (flags, THR_EXPLICIT_SCHED);
          if (PPM_BIT_DISABLED (flags, THR_SCHED_FIFO)
              && PPM_BIT_DISABLED (flags, THR_SCHED_RR)
              && PPM_BIT_DISABLED (flags, THR_SCHED_DEFAULT))
            PPM_SET_BITS (flags, THR_SCHED_DEFAULT);
        }

      if (PPM_BIT_ENABLED (flags, THR_SCHED_FIFO)
          || PPM_BIT_ENABLED (flags, THR_SCHED_RR)
          || PPM_BIT_ENABLED (flags, THR_SCHED_DEFAULT))
        {
          int spolicy;

          // SunOS, thru version 5.6, only supports SCHED_OTHER.
          spolicy = SCHED_OTHER;

          PPM_ADAPT_RETVAL(::pthread_attr_setschedpolicy (&attr, spolicy),
                           result);
          if (result != 0)
            {
              ::pthread_attr_destroy (&attr);
              return -1;
            }
        }

      // *** Set Priority (use reasonable default priorities)
      if (priority != PPM_DEFAULT_THREAD_PRIORITY)
        {
          struct sched_param sparam;
          PPM_OS::memset ((void *) &sparam, 0, sizeof sparam);

          sparam.sched_priority = priority;

          {
            // SunOS, through 5.6, POSIX only allows priorities > 0 to
            // ::pthread_attr_setschedparam.  If a priority of 0 was
            // requested, set the thread priority after creating it, below.
            if (priority > 0)
              {
                PPM_ADAPT_RETVAL(::pthread_attr_setschedparam (&attr, &sparam),
                                 result);
                if (result != 0)
                  {
                    ::pthread_attr_destroy (&attr);
                    return -1;
                  }
              }
          }
        }

      // *** Set scheduling explicit or inherited
      if (PPM_BIT_ENABLED (flags, THR_INHERIT_SCHED)
          || PPM_BIT_ENABLED (flags, THR_EXPLICIT_SCHED))
        {
          int sched = PTHREAD_EXPLICIT_SCHED;
          if (PPM_BIT_ENABLED (flags, THR_INHERIT_SCHED))
            sched = PTHREAD_INHERIT_SCHED;
          if (::pthread_attr_setinheritsched (&attr, sched) != 0)
            {
              ::pthread_attr_destroy (&attr);
              return -1;
            }
        }

      // *** Set Scope
      if (PPM_BIT_ENABLED (flags, THR_SCOPE_SYSTEM)
          || PPM_BIT_ENABLED (flags, THR_SCOPE_PROCESS))
        {
          int scope = PTHREAD_SCOPE_PROCESS;
          if (PPM_BIT_ENABLED (flags, THR_SCOPE_SYSTEM))
            scope = PTHREAD_SCOPE_SYSTEM;

          if (::pthread_attr_setscope (&attr, scope) != 0)
            {
              ::pthread_attr_destroy (&attr);
              return -1;
            }
        }

      if (PPM_BIT_ENABLED (flags, THR_NEW_LWP))
        {
          // Increment the number of LWPs by one to emulate the
          // SunOS semantics.
          int lwps = PPM_OS::thr_getconcurrency ();
          if (lwps == -1)
            {
              if (errno == ENOTSUP)
                // Suppress the ENOTSUP because it's harmless.
                errno = 0;
              else
                // This should never happen on SunOS:
                // ::thr_getconcurrency () should always succeed.
                return -1;
            }
          else if (PPM_OS::thr_setconcurrency (lwps + 1) == -1)
            {
              if (errno == ENOTSUP)
                {
                  // Unlikely:  ::thr_getconcurrency () is supported but
                  // ::thr_setconcurrency () is not?
                }
              else
                return -1;
            }
        }
    }

  PPM_OSCALL (PPM_ADAPT_RETVAL (::pthread_create (thr_id,
                                                  &attr,
                                                  thread_args->entry_point (),
                                                  thread_args),
                                result),
              int, -1, result);
  ::pthread_attr_destroy (&attr);

  // This is a SunOS or POSIX implementation of pthreads, where we
  // assume that PPM_thread_t and PPM_hthread_t are the same.  If this
  // *isn't* correct on some platform, please let us know.
  if (result != -1)
    *thr_handle = *thr_id;

  return result;


#endif

}

// The following *printf functions aren't inline because
// they use varargs.

int
PPM_OS::fprintf (FILE *fp, const char *format, ...)
{
  int result = 0;
  va_list ap;
  va_start (ap, format);
  PPM_OSCALL (::vfprintf (fp, format, ap), int, -1, result);
  va_end (ap);
  return result;
}

int
PPM_OS::printf (const char *format, ...)
{
  int result;
  va_list ap;
  va_start (ap, format);
  PPM_OSCALL (::vprintf (format, ap), int, -1, result);
  va_end (ap);
  return result;
}
//字符串赋值，无长度限制
int
PPM_OS::sprintf (char *buf, const char *format, ...)
{
  // PPM_OS_TRACE ("PPM_OS::sprintf");

  int result;
  va_list ap;
  va_start (ap, format);
  result =  (::vsprintf (buf, format, ap));
  va_end (ap);
  return result;
}

int
PPM_OS::snprintf (char *buf, size_t maxlen, const char *format, ...)
{
  int result;
  va_list ap;
  va_start (ap, format);
#ifdef WIN32
  PPM_OSCALL ((::_vsnprintf (buf, maxlen, format, ap)),
              int, -1, result);
  // Win32 doesn't 0-terminate the string if it overruns maxlen.
  if (result == -1)
    buf[maxlen-1] = '\0';
#else
  PPM_OSCALL (PPM_SPRINTF_ADAPTER (::vsnprintf (buf, maxlen, format, ap)),
              int, -1, result);
#endif
  va_end (ap);
  // In out-of-range conditions, C99 defines vsnprintf to return the number
  // of characters that would have been written if enough space was available.
  // Earlier variants of the vsnprintf() (e.g. UNIX98) defined it to return
  // -1. This method follows the C99 standard, but needs to guess at the
  // value; uses maxlen + 1.
  if (result == -1)
    result = PPM_static_cast (int, (maxlen + 1));
  return result;

}
// Returns the current timestamp in the form
// "hour:minute:second:microsecond."  The month, day, and year are
// also stored in the beginning of the date_and_time array.

char *
PPM_OS::timestamp (char date_and_time[],
                int date_and_timelen,
                int return_pointer_to_first_digit)
{
  //PPM_TRACE ("ACE::timestamp");

  if (date_and_timelen < 35)
    {
      errno = EINVAL;
      return 0;
    }

#if defined (WIN32)
   // Emulate Unix.  Win32 does NOT support all the UNIX versions
   // below, so DO we need this ifdef.
  static const char *day_of_week_name[] =
  {
    PPM_LIB_TEXT ("Sun"),
    PPM_LIB_TEXT ("Mon"),
    PPM_LIB_TEXT ("Tue"),
    PPM_LIB_TEXT ("Wed"),
    PPM_LIB_TEXT ("Thu"),
    PPM_LIB_TEXT ("Fri"),
    PPM_LIB_TEXT ("Sat")
  };

  static const char *month_name[] =
  {
    PPM_LIB_TEXT ("Jan"),
    PPM_LIB_TEXT ("Feb"),
    PPM_LIB_TEXT ("Mar"),
    PPM_LIB_TEXT ("Apr"),
    PPM_LIB_TEXT ("May"),
    PPM_LIB_TEXT ("Jun"),
    PPM_LIB_TEXT ("Jul"),
    PPM_LIB_TEXT ("Aug"),
    PPM_LIB_TEXT ("Sep"),
    PPM_LIB_TEXT ("Oct"),
    PPM_LIB_TEXT ("Nov"),
    PPM_LIB_TEXT ("Dec")
  };

  SYSTEMTIME local;
  ::GetLocalTime (&local);

  PPM_OS::sprintf (date_and_time,
                   PPM_LIB_TEXT ("%3s %3s %2d %04d %02d:%02d:%02d.%03d"),
                   day_of_week_name[local.wDayOfWeek],
                   month_name[local.wMonth - 1],
                   (int) local.wDay,
                   (int) local.wYear,
                   (int) local.wHour,
                   (int) local.wMinute,
                   (int) local.wSecond,
                   (int) (local.wMilliseconds));
  return &date_and_time[15 + (return_pointer_to_first_digit != 0)];
#else  /* UNIX */
  char timebuf[26]; // This magic number is based on the ctime(3c) man page.
  PPM_Time_Value cur_time = PPM_OS::gettimeofday ();
  time_t secs = cur_time.sec ();

  PPM_OS::ctime_r (&secs,
                   timebuf,
                   sizeof timebuf);
  // date_and_timelen > sizeof timebuf!
  PPM_OS::strsncpy (date_and_time,
                    timebuf,
                    date_and_timelen);
  char yeartmp[5];
  PPM_OS::strsncpy (yeartmp,
                    &date_and_time[20],
                    5);
  char timetmp[9];
  PPM_OS::strsncpy (timetmp,
                    &date_and_time[11],
                    9);
  PPM_OS::sprintf (&date_and_time[11],
                   "%s %s.%03ld",
                   yeartmp,
                   timetmp,
                   cur_time.usec ());
  date_and_time[33] = '\0';
  return &date_and_time[15 + (return_pointer_to_first_digit != 0)];
#endif /* WIN32 */
}

// Format buffer into printable format.  This is useful for debugging.
// Portions taken from mdump by J.P. Knight (<EMAIL>)
// Modifications by Todd Montgomery.

size_t
PPM_OS::format_hexdump (const char *buffer,
                     size_t size,
                     char *obuf,
                     size_t obuf_sz)
{

  u_char c;
  char textver[16 + 1];

  // We can fit 16 bytes output in text mode per line, 4 chars per byte.
  size_t maxlen = (obuf_sz / 68) * 16;

  if (size > maxlen)
    size = maxlen;

  size_t i;

  size_t lines = size / 16;
  for (i = 0; i < lines; i++)
    {
      size_t j;

      for (j = 0 ; j < 16; j++)
        {
          c = (u_char) buffer[(i << 4) + j];    // or, buffer[i*16+j]
          PPM_OS::sprintf (obuf,
                           PPM_LIB_TEXT ("%02x "),
                           c);
          obuf += 3;
          if (j == 7)
            {
              PPM_OS::sprintf (obuf,
                               PPM_LIB_TEXT (" "));
              obuf++;
            }
          textver[j] = ::isprint(c) ? c : '.';
        }

      textver[j] = 0;

      PPM_OS::sprintf (obuf,
                       PPM_LIB_TEXT ("  %s\n"),
                       textver);

      while (*obuf != '\0')
        obuf++;
    }

  if (size % 16)
    {
      for (i = 0 ; i < size % 16; i++)
        {
          c = (u_char) buffer[size - size % 16 + i];
          PPM_OS::sprintf (obuf,
                           PPM_LIB_TEXT ("%02x "),
                           c);
          obuf += 3;
          if (i == 7)
            {
              PPM_OS::sprintf (obuf,
                               PPM_LIB_TEXT (" "));
              obuf++;
            }
          textver[i] = ::isprint(c) ? c : '.';
        }

      for (i = size % 16; i < 16; i++)
        {
          PPM_OS::sprintf (obuf,
                           PPM_LIB_TEXT ("   "));
          obuf += 3;
          if (i == 7)
            {
              PPM_OS::sprintf (obuf,
                               PPM_LIB_TEXT (" "));
              obuf++;
            }
          textver[i] = ' ';
        }

      textver[i] = 0;
      PPM_OS::sprintf (obuf,
                       PPM_LIB_TEXT ("  %s\n"),
                       textver);
    }
  return size;
}

int
PPM_OS::map_errno (int error)
{
  switch (error)
    {
#if defined (WIN32)
    case WSAEWOULDBLOCK:
      return EAGAIN; // Same as UNIX errno WSAEWOULDBLOCK.
#endif /* WIN32 */
		default:
			break;
    }

  return error;
}

const char *
PPM_OS::sock_error (int error)
{
#if defined (WIN32)
  static char unknown_msg[64];

  switch (error)
    {
    case WSAVERNOTSUPPORTED:
      return PPM_LIB_TEXT ("version of WinSock not supported");
      /* NOTREACHED */
    case WSASYSNOTREADY:
      return PPM_LIB_TEXT ("WinSock not present or not responding");
      /* NOTREACHED */
    case WSAEINVAL:
      return PPM_LIB_TEXT ("app version not supported by DLL");
      /* NOTREACHED */
    case WSAHOST_NOT_FOUND:
      return PPM_LIB_TEXT ("Authoritive: Host not found");
      /* NOTREACHED */
    case WSATRY_AGAIN:
      return PPM_LIB_TEXT ("Non-authoritive: host not found or server failure");
      /* NOTREACHED */
    case WSANO_RECOVERY:
      return PPM_LIB_TEXT ("Non-recoverable: refused or not implemented");
      /* NOTREACHED */
    case WSANO_DATA:
      return PPM_LIB_TEXT ("Valid name, no data record for type");
      /* NOTREACHED */
      /*
        case WSANO_ADDRESS:
        return "Valid name, no MX record";
      */
    case WSANOTINITIALISED:
      return PPM_LIB_TEXT ("WSA Startup not initialized");
      /* NOTREACHED */
    case WSAENETDOWN:
      return PPM_LIB_TEXT ("Network subsystem failed");
      /* NOTREACHED */
    case WSAEINPROGRESS:
      return PPM_LIB_TEXT ("Blocking operation in progress");
      /* NOTREACHED */
    case WSAEINTR:
      return PPM_LIB_TEXT ("Blocking call cancelled");
      /* NOTREACHED */
    case WSAEAFNOSUPPORT:
      return PPM_LIB_TEXT ("address family not supported");
      /* NOTREACHED */
    case WSAEMFILE:
      return PPM_LIB_TEXT ("no file handles available");
      /* NOTREACHED */
    case WSAENOBUFS:
      return PPM_LIB_TEXT ("no buffer space available");
      /* NOTREACHED */
    case WSAEPROTONOSUPPORT:
      return PPM_LIB_TEXT ("specified protocol not supported");
      /* NOTREACHED */
    case WSAEPROTOTYPE:
      return PPM_LIB_TEXT ("protocol wrong type for this socket");
      /* NOTREACHED */
    case WSAESOCKTNOSUPPORT:
      return PPM_LIB_TEXT ("socket type not supported for address family");
      /* NOTREACHED */
    case WSAENOTSOCK:
      return PPM_LIB_TEXT ("handle is not a socket");
      /* NOTREACHED */
    case WSAEWOULDBLOCK:
      return PPM_LIB_TEXT ("socket marked as non-blocking and SO_LINGER set not 0");
      /* NOTREACHED */
    case WSAEADDRINUSE:
      return PPM_LIB_TEXT ("address already in use");
      /* NOTREACHED */
    case WSAECONNABORTED:
      return PPM_LIB_TEXT ("connection aborted");
      /* NOTREACHED */
    case WSAECONNRESET:
      return PPM_LIB_TEXT ("connection reset");
      /* NOTREACHED */
    case WSAENOTCONN:
      return PPM_LIB_TEXT ("not connected");
      /* NOTREACHED */
    case WSAETIMEDOUT:
      return PPM_LIB_TEXT ("connection timed out");
      /* NOTREACHED */
    case WSAECONNREFUSED:
      return PPM_LIB_TEXT ("connection refused");
      /* NOTREACHED */
    case WSAEHOSTDOWN:
      return PPM_LIB_TEXT ("host down");
      /* NOTREACHED */
    case WSAEHOSTUNREACH:
      return PPM_LIB_TEXT ("host unreachable");
      /* NOTREACHED */
    case WSAEADDRNOTAVAIL:
      return PPM_LIB_TEXT ("address not available");
      /* NOTREACHED */
    default:
      PPM_OS::sprintf (unknown_msg, PPM_LIB_TEXT ("unknown error: %d"), error);
      return unknown_msg;
      /* NOTREACHED */
    }
#else
  PPM_UNUSED_ARG (error);
  PPM_NOTSUP_RETURN (0);
#endif /* WIN32 */

}

//add for unix
// Set the number of currently open handles in the process.
//
// If NEW_LIMIT == -1 set the limit to the maximum allowable.
// Otherwise, set it to be the value of NEW_LIMIT.

int
PPM_OS::set_handle_limit (int new_limit)
{
  PPM_TRACE ("ACE::set_handle_limit");
  int cur_limit = max_handles ();
  int max_limit = cur_limit;

  if (cur_limit == -1)
    return -1;

#if !defined (PPM_LACKS_RLIMIT) && defined (RLIMIT_NOFILE)
  struct rlimit rl;

  PPM_OS::memset ((void *) &rl, 0, sizeof rl);
  int r = PPM_OS::getrlimit (RLIMIT_NOFILE, &rl);
  if (r == 0)
    max_limit = rl.rlim_max;
#endif /* PPM_LACKS_RLIMIT */

  if (new_limit == -1)
    new_limit = max_limit;

  if (new_limit < 0)
    {
      errno = EINVAL;
      return -1;
    }
  else if (new_limit > cur_limit)
    {
#if !defined (PPM_LACKS_RLIMIT) && defined (RLIMIT_NOFILE)
      rl.rlim_cur = new_limit;
      return PPM_OS::setrlimit (RLIMIT_NOFILE, &rl);
#else
      // Must return EINVAL errno.
      PPM_NOTSUP_RETURN (-1);
#endif /* PPM_LACKS_RLIMIT */
    }
  else
    {
#if !defined (PPM_LACKS_RLIMIT) && defined (RLIMIT_NOFILE)
      rl.rlim_cur = new_limit;
      return PPM_OS::setrlimit (RLIMIT_NOFILE, &rl);
#else
      // We give a chance to platforms without RLIMIT to work.
      // Instead of PPM_NOTSUP_RETURN (0), just return 0 because
      // new_limit is <= cur_limit, so it's a no-op.
      return 0;
#endif /* PPM_LACKS_RLIMIT */
    }

  // Irix complains without this return statement.  DEC cxx
  // (correctly) says that it's not reachable.  PPM_NOTREACHED won't
  // work here, because it handles both platforms the same.
  // IRIX does not complain anymore [7.2]
  PPM_NOTREACHED (return 0);
}


int
PPM_OS::max_handles (void)
{
  PPM_TRACE ("ACE::max_handles");

#if defined (PPM_SOLARIS)
  return PPM_OS::sysconf (_SC_OPEN_MAX);
#elif defined (FD_SETSIZE)
  return FD_SETSIZE;
#else
  PPM_NOTSUP_RETURN (-1);
#endif /* _SC_OPEN_MAX */
}
//bTrimflag:0 表示不需要将两边的空格去掉，1表示需要将两边的空格去掉
BOOL
PPM_OS::fStrRead(char* strsource,BYTE bTrimflag,char* strformat,...)
{
   va_list marker;
   std::string tm_strsource = strsource;
   std::string tm_strformat = strformat;

   char tm_chformat_mid; //中间变量
   std::string tm_strsource_mid; //中间变量
   char* tm_pstr = NULL;
   int* tm_pi = NULL;
   DWORDLONG* tm_pi64 = NULL;
   WORD* tm_pw = NULL;
   BYTE* tm_pb = NULL;
   
   va_start( marker, strformat );     /* Initialize variable arguments. */

   size_t ipos_format = 0;
   size_t ipos_source = 0;
   while((tm_strformat.length() > 0) && (tm_strsource.length() > 0))
   {
	   ipos_format = tm_strformat.find('%');
	   if(ipos_format > 0){
		   if(ipos_format >= tm_strformat.length()){
			   break;
		   }
		   ipos_source = tm_strsource.find(tm_strformat.substr(0,ipos_format));
		   if(ipos_source >= tm_strsource.length()){
			   break;
		   }
		   //源删除之前匹配上的
		   tm_strsource.erase(0,ipos_source + ipos_format);
		   if(tm_strsource.length() <= 0){
			   break;
		   }
	   }
	   //格式也删除%和之前的东西
	   tm_strformat.erase(0,ipos_format + 1);
	   int tm_maxlen = PPM_OS::atoi(tm_strformat.c_str());
	   if(tm_maxlen > 0){
		   if(tm_maxlen >= 100){
			   tm_strformat.erase(0,3);
		   }
		   else if(tm_maxlen >= 10){
			   tm_strformat.erase(0,2);
		   }else{
			   tm_strformat.erase(0,1);
		   }
	   }else{
		   tm_maxlen = 255;
	   }
	   if(tm_strformat.length() > 0){
		   tm_chformat_mid = tm_strformat[0];
		   tm_strformat.erase(0,1);
	   }else{
		   break; //表明已经没有参数了
	   }
	   //到这里，截取了%号前面的字符
	   ipos_format = tm_strformat.find('%'); //查找下一个%
	   if(ipos_format < 0){
		   ipos_source = tm_strsource.length();
		   ipos_format = 0;
	   }else{
		   ipos_source = tm_strsource.find(tm_strformat.substr(0,ipos_format));
		   tm_strformat.erase(0,ipos_format);
	   }
	   tm_strsource_mid = tm_strsource.substr(0,ipos_source);
	   tm_strsource.erase(0,ipos_source + ipos_format);//从source中去掉查找过的和下一个匹配的字符串
	   if(1 == bTrimflag){
		   TrimString(tm_strsource_mid);
	   }
	   switch(tm_chformat_mid){
	   case 's':
		   //字符串
		   tm_pstr = va_arg(marker, char*);
		   if(NULL != tm_pstr){
			   PPM_OS::snprintf(tm_pstr,tm_maxlen,"%s",tm_strsource_mid.c_str());
		   }
		   break;
	   case 'd':
		   //int
		   tm_pi = va_arg(marker,int*);
		   if(NULL != tm_pi){
			   (*tm_pi) = PPM_OS::atoi(tm_strsource_mid.c_str());
		   }
		   break;
	   case 'D':
		   //int
		   tm_pi64 = va_arg(marker,DWORDLONG*);
		   if(NULL != tm_pi64){
			   (*tm_pi64) = PPM_OS::atoi(tm_strsource_mid.c_str());
		   }
		   break;
	   case 'w':
		   //WORD
		   tm_pw = va_arg(marker,WORD*);
		   if(NULL != tm_pw){
			   (*tm_pw) = PPM_OS::atoi(tm_strsource_mid.c_str());
		   }
		   break;
	   case 'b':
		   //BYTE
		   tm_pb = va_arg(marker,BYTE*);
		   if(NULL != tm_pb){
			(*tm_pb) = PPM_OS::atoi(tm_strsource_mid.c_str());
		   }
		   break;
	   default:
		   break;
	   }	   
   } //end while

   va_end( marker );              /* Reset variable arguments.      */	
   return TRUE;
}

void 
PPM_OS::TrimString(std::string& str)
{
	while(str[0] == ' '){
		str.erase(0,1);
	}
	int istrlen = str.length();
	while(str[istrlen - 1] == ' '){
		str.erase(istrlen - 1,istrlen);
		istrlen = str.length();
	}
};

PPM_Time_Value 
PPM_OS::GetSelfCreateTime(char* pchProgramname)
{
	PPM_Time_Value tm_timevalue(0);
	//获取自身全地址
	char path_buffer[MAX_PATH];
#ifdef WIN32
	GetModuleFileName(NULL, path_buffer, MAX_PATH);
#else
	getcwd(path_buffer, MAX_PATH);
	char tm_str[50];
	PPM_OS::memset(tm_str,0,sizeof(tm_str));
	PPM_OS::sprintf(tm_str,"\\%s",pchProgramname);
	strcat(path_buffer,tm_str);
#endif
	struct stat filestat;
	if (stat(path_buffer, &filestat) == 0)// 兼容问题用stat,否则unix用lstat
	{
		tm_timevalue.sec(filestat.st_mtime);
	}
	return tm_timevalue;

}