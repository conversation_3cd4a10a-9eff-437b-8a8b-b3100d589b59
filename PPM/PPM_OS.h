#ifndef PPM_OS_H
#define PPM_OS_H

 
#include "./PPM_OS_String.h"
#include "./PPM_Time_Value.h"
class PPM_Base_Thread_Adapter;

/**
 * @class PPM_event_t
 *
 * @brief Wrapper for NT events on UNIX.
 */
class PPM_event_t
{
  friend class PPM_OS;
protected:
  /// Protect critical section.
  PPM_mutex_t lock_;

  /// Keeps track of waiters.
  PPM_cond_t condition_;

  /// Specifies if this is an auto- or manual-reset event.
  int manual_reset_;

  /// "True" if signaled.
  int is_signaled_;

  /// Number of waiting threads.
  u_long waiting_threads_;
};

/**
 * @class PPM_Thread_ID
 *
 * @brief Defines a platform-independent thread ID.
 */
class PPM_Thread_ID
{
public:
  // = Initialization method.
  PPM_Thread_ID (PPM_thread_t t, PPM_hthread_t ht)
  {
	  thread_id_ = t;
	  thread_handle_ = ht;
  };

  // = Set/Get the Thread ID.
  PPM_thread_t id (void){ return thread_id_;};
  void id (PPM_thread_t t){ thread_id_ = t;};

  // = Set/Get the Thread handle.
  PPM_hthread_t handle (void){ return thread_handle_;};
  void handle (PPM_hthread_t ht){ thread_handle_ = ht;};

  // != Comparison operator.
  int operator== (const PPM_Thread_ID &t) const
  {
	  return ( (thread_id_ == t.thread_id_)&&(thread_handle_ == t.thread_handle_) );
  };
  int operator!= (const PPM_Thread_ID &t) const
  {
	  return !( (thread_id_ == t.thread_id_)&&(thread_handle_ == t.thread_handle_) );
  };

private:
  /// Identify the thread.
  PPM_thread_t thread_id_;

  /// Handle to the thread (typically used to "wait" on Win32).
  PPM_hthread_t thread_handle_;
};

/**
一个计时的类，设置起始时间和停止时间，提供一些方法指示超时
 */
class PPM_Countdown_Time
{
public:
  // = Initialization and termination methods.
  /// Cache the <max_wait_time> and call <start>.
  PPM_Countdown_Time (PPM_Time_Value *max_wait_time);

  /// Call <stop>.
  ~PPM_Countdown_Time (void);

  /// Cache the current time and enter a start state.
  int start (void);

  /// Subtract the elapsed time from max_wait_time_ and enter a stopped
  /// state.
  int stop (void);

  /// Calls stop and then start.  max_wait_time_ is modified by the
  /// call to stop.
  int update (void);

  /// Returns 1 if we've already been stopped, else 0.
  int stopped (void) const;

private:
  /// Maximum time we were willing to wait.
  PPM_Time_Value *max_wait_time_;

  /// Beginning of the start time.
  PPM_Time_Value start_time_;

  /// Keeps track of whether we've already been stopped.
  int stopped_;
};
/**
包装了操作系统相关的一些API函数，给跨平台提供方便
 */

class PPM_OS : public PPM_OS_String
{
public:
  enum PPM_HRTimer_Op
    {
      PPM_HRTIMER_START = 0x0,  // Only use these if you can stand
      PPM_HRTIMER_INCR = 0x1,   // for interrupts to be disabled during
      PPM_HRTIMER_STOP = 0x2,   // the timed interval!!!!
      PPM_HRTIMER_GETTIME = 0xFFFF
    };

	//SOCKET
  //更改socket的类型，阻塞，非阻塞等。
  static int ioctl (PPM_HANDLE handle,
                    int cmd,
                    void * = 0);
  /// Manipulate the options associated with a socket.
  static int setsockopt (PPM_HANDLE handle,
                         int level,
                         int optname,
                         const char *optval,
                         int optlen);
  static int getsockopt (PPM_HANDLE handle,
                         int level,
                         int optname,
                         char *optval,
                         int *optlen);
  static int getpeername (PPM_HANDLE handle,
                          struct sockaddr *addr,
                          int *addrlen);
  static int getsockname (PPM_HANDLE handle,
                          struct sockaddr *addr,
                          int *addrlen);
  static int closesocket (PPM_HANDLE s);
  /// Create a BSD-style socket (no QoS).
  static PPM_HANDLE socket (int protocol_family,
                            int type,
                            int proto);

  //@{ @name A set of wrappers for sockets.
  /// BSD-style <accept> (no QoS).
  static PPM_HANDLE accept (PPM_HANDLE handle,
                            struct sockaddr *addr,
                            int *addrlen);
  static int bind (PPM_HANDLE s,
                   struct sockaddr *name,
                   int namelen);

  /// BSD-style <connect> (no QoS).
  static int connect (PPM_HANDLE handle,
                      struct sockaddr *addr,
                      int addrlen);
  static int listen (PPM_HANDLE handle,
                     int backlog);
  static int recv (PPM_HANDLE handle,
                   char *buf,
                   size_t len,
                   int flags = 0);
  static int recvfrom (PPM_HANDLE handle,
                       char *buf,
                       size_t len,
                       int flags,
                       struct sockaddr *addr,
                       int *addrlen);
  static int send (PPM_HANDLE handle,
                   const char *buf,
                   size_t len,
                   int flags = 0);
  static int sendto (PPM_HANDLE handle,
                     const char *buf,
                     size_t len,
                     int flags,
                     const struct sockaddr *addr,
                     int addrlen);
  static int set_errno_to_wsa_last_error (void);

  static int last_error (void);

  //@{ @name A set of wrappers for event demultiplexing and IPC.
  static int select (int width,
                     fd_set *rfds,
                     fd_set *wfds = 0,
                     fd_set *efds = 0,
                     const PPM_Time_Value *tv = 0);
  static int select (int width,
                     fd_set *rfds,
                     fd_set *wfds,
                     fd_set *efds,
                     const PPM_Time_Value &tv);
  static int shutdown (PPM_HANDLE handle,
                       int how);
  // = A set of wrappers for miscellaneous operations.
  static int atoi (const char *s);
  static DWORDLONG atoi64_16 (const char *s);
  static int inet_aton (const char *strptr,
                        struct in_addr *addr);
  static unsigned long inet_addr (const char *name);
  static int hostname (char *name,
                       size_t maxnamelen);
  static std::string getlocalhostip();
  static int getlocalhost();
  static struct hostent *gethostbyname_r (const char *name,
                                          struct hostent *result,
                                          PPM_HOSTENT_DATA buffer,
                                          int *h_errnop);

  static struct hostent *gethostbyaddr_r (const char *addr,
                                          int length,
                                          int type,
                                          struct hostent *result,
                                          PPM_HOSTENT_DATA buffer,
                                          int *h_errnop);
  static struct servent *getservbyname_r (const char *svc,
                                          const char *proto,
                                          struct servent *result,
                                          PPM_SERVENT_DATA buf);
  static char *inet_ntoa (const struct in_addr addr);
  static char *inet_ntoa (const int iaddr);
  
  //函数功能：用于将IPv6地址结构体转换为字符串
  static char *inet_ntop (int family, const void *addrptr, char *strptr, size_t len);
  static int close (PPM_HANDLE handle);
  /**
   * Wait up to <timeout> amount of time to passively establish a
   * connection.  This method doesn't perform the <accept>, it just
   * does the timed wait...
   */
  static int handle_timed_accept (PPM_HANDLE listener,
                                  PPM_Time_Value *timeout,
                                  int restart);
  /**
   * Wait up to <timeout> amount of time to complete an actively
   * established non-blocking connection.  If <is_tli> is non-0 then
   * we are being called by a TLI wrapper (which behaves slightly
   * differently from a socket wrapper).
   */
  static PPM_HANDLE handle_timed_complete (PPM_HANDLE listener,
                                           const PPM_Time_Value *timeout,
                                           int is_tli = 0);


  static int sleep (u_int mseconds);
  static int sleep (const PPM_Time_Value &tv);


    // = Set/get/clear various flags related to I/O HANDLE.
  /// Set flags associated with <handle>.
  static int set_flags (PPM_HANDLE handle,
                        int flags);

  /// Clear flags associated with <handle>.
  static int clr_flags (PPM_HANDLE handle,
                        int flags);

  /// Return the current setting of flags associated with <handle>.
  static int get_flags (PPM_HANDLE handle);

  /// Bind a new unused port to <handle>.
  static int bind_port (PPM_HANDLE handle,
                        PPM_UINT32 ip_addr = INADDR_ANY);

  static int event_destroy (PPM_event_t *event);
  //@{ @name A set of wrappers for auto-reset and manual events.
  static int event_init (PPM_event_t *event,
                         int manual_reset = 0,
                         int initial_state = 0,
                         int type = 0,
                         const char *name = 0,
                         void *arg = 0,
                         LPSECURITY_ATTRIBUTES sa = 0);
  static int event_wait (PPM_event_t *event);
  static int event_timedwait (PPM_event_t *event,
                              PPM_Time_Value *timeout,
                              int use_absolute_time = 1);
  static int event_signal (PPM_event_t *event);
  static int event_pulse (PPM_event_t *event);
  static int event_reset (PPM_event_t *event);

  static PPM_Time_Value gettimeofday (void);
  static int set_errno_to_last_error (void);
//signal
  static int kill (pid_t pid,
                   int signum);
  static int sigaction (int signum,
                        const struct sigaction *nsa,
                        struct sigaction *osa);
  static int sigaddset (sigset_t *s,
                        int signum);
  static int sigdelset (sigset_t *s,
                        int signum);
  static int sigemptyset (sigset_t *s);
  static int sigfillset (sigset_t *s);
  static int sigismember (sigset_t *s,
                          int signum);
  static PPM_SignalHandler signal (int signum,
                                   PPM_SignalHandler);
  static int sigsuspend (const sigset_t *set);
  static int sigprocmask (int how,
                          const sigset_t *nsp,
                          sigset_t *osp);

  static int pthread_sigmask (int how,
                              const sigset_t *nsp,
                              sigset_t *osp);
//memory
  static void free (void *);


//thread
  // These are non-portable since they use PPM_thread_t and
  // PPM_hthread_t and will go away in a future release.
  static int thr_continue (PPM_hthread_t target_thread);

  /*
   * Creates a new thread having <flags> attributes and running <func>
   * with <args> (if <thread_adapter> is non-0 then <func> and <args>
   * are ignored and are obtained from <thread_adapter>).  <thr_id>
   * and <t_handle> are set to the thread's ID and handle (?),
   * respectively.  The thread runs at <priority> priority (see
   * below).
   *
   * The <flags> are a bitwise-OR of the following:
   * = BEGIN<INDENT>
   * THR_CANCEL_DISABLE, THR_CANCEL_ENABLE, THR_CANCEL_DEFERRED,
   * THR_CANCEL_ASYNCHRONOUS, THR_BOUND, THR_NEW_LWP, THR_DETACHED,
   * THR_SUSPENDED, THR_DAEMON, THR_JOINABLE, THR_SCHED_FIFO,
   * THR_SCHED_RR, THR_SCHED_DEFAULT, THR_EXPLICIT_SCHED,
   * THR_SCOPE_SYSTEM, THR_SCOPE_PROCESS
   * = END<INDENT>
   *
   * By default, or if <priority> is set to
   * PPM_DEFAULT_THREAD_PRIORITY, an "appropriate" priority value for
   * the given scheduling policy (specified in <flags}>, e.g.,
   * <THR_SCHED_DEFAULT>) is used.  This value is calculated
   * dynamically, and is the median value between the minimum and
   * maximum priority values for the given policy.  If an explicit
   * value is given, it is used.  Note that actual priority values are
   * EXTREMEMLY implementation-dependent, and are probably best
   * avoided.
   *
   * Note that <thread_adapter> is always deleted by <thr_create>,
   * therefore it must be allocated with global operator new.
   */
  static int thr_create (PPM_THR_VOID_FUNC func,
                         void *args,
                         long flags,
                         PPM_thread_t *thr_id,
                         PPM_hthread_t *t_handle = 0,
                         long priority = PPM_DEFAULT_THREAD_PRIORITY,
                         void *stack = 0,
                         size_t stacksize = 0,
                         PPM_Base_Thread_Adapter *thread_adapter = 0);

  static int thr_suspend (PPM_hthread_t target_thread);

  static int thr_getprio (PPM_hthread_t thr_id,
                          int &prio);
  static int thr_kill (PPM_thread_t thr_id,
                       int signum);
  static PPM_thread_t thr_self (void);
  static void thr_self (PPM_hthread_t &);
  static int thr_setprio (PPM_hthread_t thr_id,
                          int prio);

//STRING
  static int fprintf (FILE *fp, const char *format, ...);
  static int sprintf (char *buf, const char *format, ...);
  static int snprintf (char *buf, size_t maxlen, const char *format, ...);
  static int vsprintf (char *buffer, const char *format, va_list argptr);
  static int printf (const char *format, ...);
    /**
   * Returns the current timestamp in the form
   * "hour:minute:second:microsecond."  The month, day, and year are
   * also stored in the beginning of the <date_and_time> array, which
   * is a user-supplied array of size <time_len> <char>s.  Returns
   * 0 if unsuccessful, else returns pointer to beginning of the
   * "time" portion of <date_and_time>.  If
   * <return_pointer_to_first_digit> is 0 then return a pointer to the
   * space before the time, else return a pointer to the beginning of
   * the time portion.
   */
  static char *timestamp (char date_and_time[],
                               int time_len,
                               int return_pointer_to_first_digit = 0);

  // @@ UNICODE what about buffer?
  /// Format buffer into printable format.  This is useful for
  /// debugging.
  static size_t format_hexdump (const char *buffer, size_t size,
                                char *obuf, size_t obuf_sz);
  /// Computes the base 2 logarithm of <num>.
  static u_long log2 (u_long num);
  static pid_t getpid (void);
  static char *ctime_r (const time_t *clock, char *buf, int buflen);
  static int fflush (FILE *fp);
  static int map_errno (int error);
  static const char* sock_error (int error);

  static std::string i64toa(DWORDLONG keyin,int type = 10);
  static DWORDLONG atoi64(const char* strin);
  
//add for unix
    /**
   * Reset the limit on the number of open handles.  If <new_limit> ==
   * -1 set the limit to the maximum allowable.  Otherwise, set it to
   * be the value of <new_limit>.
   */
  static int set_handle_limit (int new_limit = -1);
  /**
   * Returns the maximum number of open handles currently permitted in
   * this process.  This maximum may be extended using
   * <ACE::set_handle_limit>.
   */
  static int max_handles (void);

  static int fcntl (PPM_HANDLE handle,
                    int cmd,
                    long arg = 0);
  static int thr_getconcurrency (void);
  static int thr_setconcurrency (int hint);

  static int mutex_destroy (PPM_mutex_t *m);
  static int cond_destroy (PPM_cond_t *cv);
	static int cond_init (PPM_cond_t *cv,PPM_condattr_t &attributes,
												const char *name = 0,void *arg = 0);
	static int cond_init (PPM_cond_t *cv, short type, 
												const char *name = 0, void *arg = 0);
	static int condattr_init (PPM_condattr_t &attributes,int type);
	static int condattr_destroy (PPM_condattr_t &attributes);
	static int mutex_init (PPM_mutex_t *m,
                    int type = PPM_DEFAULT_SYNCH_TYPE,
                    const char *name = 0,
                    PPM_mutexattr_t *attributes = 0,
                    LPSECURITY_ATTRIBUTES sa = 0);
  static int mutex_lock (PPM_mutex_t *m);
  static int mutex_unlock (PPM_mutex_t *m);
  
  static int cond_signal (PPM_cond_t *cv);
  static int cond_timedwait (PPM_cond_t *cv,
                             PPM_mutex_t *m,
                             PPM_Time_Value *);
  static int cond_wait (PPM_cond_t *cv,
                        PPM_mutex_t *m);

  static int cond_broadcast (PPM_cond_t *cv);
  static int getrlimit (int resource, struct rlimit *rl);
  static int setrlimit (int resource, PPM_SETRLIMIT_TYPE *rl);
  static int thr_sigsetmask (int how,
                             const sigset_t *nsm,
                             sigset_t *osm);
  static int fstat (PPM_HANDLE handle, PPM_stat *stp);

  //按照指定格式读取字符串
  static BOOL fStrRead(char* strsource,BYTE bTrimflag,char* strformat,...);
  static void TrimString(std::string& str);

  static PPM_Time_Value GetSelfCreateTime(char* pchProgramname);
};

#if !defined (PPM_LACKS_INLINE_FUNCTIONS)
# include "PPM_OS.inl"
#endif

#endif
