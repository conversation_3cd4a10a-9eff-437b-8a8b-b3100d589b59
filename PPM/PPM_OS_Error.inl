
PPM_INLINE
PPM_Errno_Guard::PPM_Errno_Guard (PPM_ERRNO_TYPE &errno_ref,
                                  int error)
  :
#if defined (PPM_MT_SAFE)
    errno_ptr_ (&errno_ref),
#endif /* PPM_MT_SAFE */
    error_ (error)
{
}

PPM_INLINE
PPM_Errno_Guard::PPM_Errno_Guard (PPM_ERRNO_TYPE &errno_ref)
  :
#if defined (PPM_MT_SAFE)
    errno_ptr_ (&errno_ref),
#endif /* PPM_MT_SAFE */
    error_ (errno_ref)
{
}

PPM_INLINE
PPM_Errno_Guard::~PPM_Errno_Guard (void)
{
#if defined (PPM_MT_SAFE)
  *errno_ptr_ = this->error_;
#else
  errno = this->error_;
#endif /* PPM_MT_SAFE */
}

PPM_INLINE int
PPM_Errno_Guard::operator= (int error)
{
  return this->error_ = error;
}

PPM_INLINE int
PPM_Errno_Guard::operator== (int error)
{
  return this->error_ == error;
}

PPM_INLINE int
PPM_Errno_Guard::operator!= (int error)
{
  return this->error_ != error;
}
