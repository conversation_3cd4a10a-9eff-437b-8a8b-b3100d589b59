#ifndef PPM_OSSTRING_H
#define PPM_OSSTRING_H
#include "./PPM_MACRO.h"
#include "./PPM_typedef.h"
/**
包装了字符串的一些处理函数
 */

class PPM_OS_String
{
public:
	  /// Finds the length of a string (char version).
  static size_t strlen (const char *s);
  /// This is a "safe" c string copy function (char version).
  /**
   * Unlike strncpy() this function will always add a terminating '\0'
   * char if maxlen > 0.  So the user doesn't has to provide an extra
   * '\0' if the user wants a '\0' terminated dst.  The function
   * doesn't check for a 0 <dst>, because this will give problems
   * anyway.  When <src> is 0 an empty string is made.  We do not
   * "touch" *<dst> if maxlen is 0.  Returns <dst>.  Care should be
   * taken when replacing strncpy() calls, because in some cases a
   * strncpy() user is using the "not '\0' terminating" feature from
   * strncpy().  This happens most when the call to strncpy() was
   * optimized by using a maxlen which is 1 smaller than the size
   * because there's always written a '\0' inside this last position.
   * Very seldom it's possible that the '\0' padding feature from
   * strncpy() is needed.
   */
  static char *strsncpy (char *dst,
                         const char *src,
                         size_t maxlen);
  /// Appends part of a string to another string (char version).
  static char *strncat (char *s, const char *t, size_t len);
  
  /// Returns a malloced duplicated string (char version).
  static char *strdup (const char *s);
  /// Returns a system error message.
  static char *strerror (int errnum);
  /// Copies a string (char version).
  static char *strcpy (char *t, const char *s);
  static char *strncpy (char *t, const char *s, size_t len);
  /// Copies one buffer to another.
  static void *memcpy (void *t, const void *s, size_t len);
  /// Compares two buffers.
  static int memcmp (const void *t, const void *s, size_t len);
  /// Moves one buffer to another.
  static void *memmove (void *t, const void *s, size_t len);
  /// Finds the last occurance of a character in a string (char version).
  static char *strrchr (const char *s, int c);
  /// Searches for the first substring containing only the specified
  /// characters and returns the size of the substring (char version).
  static size_t strspn (const char *s1, const char *s2);
  /// Fills a buffer with a character value.
  static void *memset (void *s, int c, size_t len);
  /// Compares two strings (char version).
  static int strcmp (const char *s, const char *t);

};
#if !defined (PPM_LACKS_INLINE_FUNCTIONS)
# include "./PPM_OS_String.inl"
#endif

#endif