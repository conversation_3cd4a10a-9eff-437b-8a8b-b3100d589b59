PPM_INLINE size_t
PPM_OS_String::strlen (const char *s)
{
  return ::strlen (s);
}

PPM_INLINE char *
PPM_OS_String::strncat (char *s, const char *t, size_t len)
{
  return ::strncat (s, t, len);
}

PPM_INLINE char *
PPM_OS_String::strerror (int errnum)
{
  return ::strerror (errnum);
}
PPM_INLINE char *
PPM_OS_String::strcpy (char *t, const char *s)
{
  return ::strcpy (t, s);
}

PPM_INLINE char *
PPM_OS_String::strncpy (char *t, const char *s, size_t len)
{
  return ::strncpy (t, s, len);
}

PPM_INLINE void *
PPM_OS_String::memcpy (void *t, const void *s, size_t len)
{
  return ::memcpy (t, s, len);
}

PPM_INLINE void *
PPM_OS_String::memmove (void *t, const void *s, size_t len)
{
  return ::memmove (t, s, len);
}
PPM_INLINE int
PPM_OS_String::memcmp (const void *t, const void *s, size_t len)
{
  return ::memcmp (t, s, len);
}

PPM_INLINE char *
PPM_OS_String::strrchr (const char *s, int c)
{
  return (char *) ::strrchr (s, c);
}
PPM_INLINE size_t
PPM_OS_String::strspn (const char *s, const char *t)
{
  return ::strspn (s, t);
}

PPM_INLINE void *
PPM_OS_String::memset (void *s, int c, size_t len)
{
  return ::memset (s, c, len);
}

PPM_INLINE int
PPM_OS_String::strcmp (const char *s, const char *t)
{
  return ::strcmp (s, t);
}

