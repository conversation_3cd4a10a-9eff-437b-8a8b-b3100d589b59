#ifndef _PPM_PUBLIC_VAR_H_
#define _PPM_PUBLIC_VAR_H_

#include "PPM_Log_Msg.h"
#include "StdHeader.h"

#define PPM_SOCKLOG(X) \
  do { \
    int __PPM_error = PPM_Log_Msg::last_error_adapter (); \
    PPM_Log_Msg *PPM___ = PPM_Public_Var::instance_SockLog (); \
    PPM___->conditional_set (__FILE__, __LINE__, 0, __PPM_error); \
    PPM___->log_date X; \
  } while (0)


class PPM_Public_Var
{
public:
	static PPM_Log_Msg *instance_SockLog (void){
		  static PPM_Log_Msg *sock_log_msg = 0;
		  if (sock_log_msg == 0)
			{
			  sock_log_msg = PPM_Log_Msg::instance_new();
			}
		  return sock_log_msg;
	};
};



#endif