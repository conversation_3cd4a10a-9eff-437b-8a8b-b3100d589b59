#include "./PPM_Reactor.h"
#include "./PPM_Reactor_Impl.h"
#include "./PPM_Log_Msg.h"
#include "./PPM_Select_Reactor.h"

#if defined (PPM_LACKS_INLINE_FUNCTIONS)
#include "./PPM_Reactor.inl"
#endif /* __PPM_INLINE__ */

PPM_Reactor::PPM_Reactor (PPM_Reactor_Impl *impl,
                          int delete_implementation)
  : implementation_ (0),
    delete_implementation_ (delete_implementation)
{
	delete_reactor_ = 0;
//	isrun_ = 0;
//	isOver_ = 1;
  this->implementation (impl);

  if (this->implementation () == 0)
    {
	  
#ifdef WIN32_
	  PPM_NEW(impl,PPM_WFMO_Reactor); 
#else
	  PPM_NEW(impl,PPM_Select_Reactor);
#endif
      this->implementation (impl);
      this->delete_implementation_ = 1;
    }
}

PPM_Reactor::~PPM_Reactor (void)
{
  this->end_event_loop();
//  PPM_OS::sleep(500);
  int nCount = 0;
  while(((m_bIsStop_ != 1) || (m_bRunFlag_ != 0)) && (nCount++<20)){
  	PPM_OS::sleep(500);
  	PPM_DEBUG((LM_ERROR,"waitting for reactorloop exit!\n"));
  	}
//    PPM_OS::sleep(10000);

  if (this->delete_implementation_)
    delete this->implementation ();
}

// Process-wide PPM_Reactor.
PPM_Reactor *PPM_Reactor::reactor_ = 0;

// Controls whether the Reactor is deleted when we shut down (we can
// only delete it safely if we created it!)
//int PPM_Reactor::delete_reactor_ = 0;
//BYTE PPM_Reactor::isrun_ = 0;

PPM_Reactor *
PPM_Reactor::instance (void)
{
  PPM_TRACE ("PPM_Reactor::instance");

  if (PPM_Reactor::reactor_ == 0)
  {
    PPM_NEW_RETURN (PPM_Reactor::reactor_,
                    PPM_Reactor,
                    0);
    reactor_->delete_reactor_ = 1;
  }
  return PPM_Reactor::reactor_;
}

void
PPM_Reactor::dump (void) const
{
  PPM_TRACE ("PPM_Reactor::dump");

  implementation_->dump ();
}
BOOL 
PPM_Reactor::Main()
{
	return TRUE;
}
PPM_THR_FUNC_VOID_RETURN 
PPM_Reactor::CommMain(void)
{
	
  PPM_TRACE ("PPM_Reactor::run_reactor_event_loop");

  if (this->reactor_event_loop_done ())
    return 0;

	m_bRunFlag_ = 1;
	m_bIsStop_ = 0;

//  isrun_ = 1;
//  isOver_ = 0;

  while (m_bRunFlag_)
  {
	  SetThreadFlag(TRUE);
	  //检测事件，返回-1，循环就结束，或者是去活了，循环结束
		int result = this->implementation_->handle_events ();

		if (result == -1 && this->implementation_->deactivated ()){
      m_bRunFlag_ = 0;
      //isOver_ = 1;
      m_bIsStop_ = 1;
      return 0;
		}
		else if (result == -1){
      m_bRunFlag_ = 0;
      //isOver_ = 1;
      m_bIsStop_ = 1;
      return nullptr;
		}
    //continue;
  }
  m_bRunFlag_ = 0;
  // isOver_ = 1;

	m_bIsStop_ = 1;
	
  return 0;	
}

BOOL 
PPM_Reactor::reactor_event_loop_done()
{
	return (m_bRunFlag_ == 1);
}
//启动时循环侦听登记上的句柄的io等变化
int
PPM_Reactor::run_reactor_event_loop ()
{
//  PPM_TRACE ("PPM_Reactor::run_reactor_event_loop");
//  if (this->reactor_event_loop_done ())
//    return 0;
//  isrun_ = 1;
//  isOver_ = 0;
//  while (isrun_)
//    {
//	    //检测事件，返回-1，循环就结束，或者是去活了，循环结束
//		int result = this->implementation_->handle_events ();
//		if (result == -1 && this->implementation_->deactivated ()){
//			  isrun_ = 0;
//			  isOver_ = 1;
//			  return 0;
//		}
//		else if (result == -1){
//			  isrun_ = 0;
//			  isOver_ = 1;
//			  return -1;
//		}
////			continue;
//    }
//    isrun_ = 0;
//    isOver_ = 1;
    return 0;
}

int
PPM_Reactor::end_reactor_event_loop (void)
{
  this->implementation_->deactivate (1);

  m_bRunFlag_ = 0;
  
  return 0;
}
