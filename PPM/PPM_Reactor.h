/* -*- C++ -*- */

//=============================================================================
/**
 *  @file    Reactor.h
 *
 *  Reactor.h,v 4.89 2002/06/10 19:42:11 schmidt Exp
 *
 *  <AUTHOR> <<EMAIL>>
 *  <AUTHOR> <<EMAIL>>
 */
//=============================================================================

#ifndef PPM_REACTOR_H
#define PPM_REACTOR_H

class PPM_Reactor_Impl;

// Need the class def for PPM_Handle_Set to compile references to it in
// programs.
#include "./PPM_Handle_Set.h"

// Event_Handler.h contains the definition of PPM_Reactor_Mask
#include "./PPM_Event_Handler.h"
#include "./PPM_Thread_Manager.h"

/**
反应器类的框架，在调用反应器的时候，需要执行run_event_loop才能够开始侦听
句柄上的事件
 */
class PPM_Reactor : public PPM_ThreadDeal_Base
{
public:
  /// Operations on the "ready" mask and the "dispatch" mask.
  enum
  {
    /// Retrieve current value of the the "ready" mask or the
    /// "dispatch" mask.
    GET_MASK = 1,
    /// Set value of bits to new mask (changes the entire mask).
    SET_MASK = 2,
    /// Bitwise "or" the value into the mask (only changes enabled
    /// bits).
    ADD_MASK = 3,
    /// Bitwise "and" the negation of the value out of the mask (only
    /// changes enabled bits).
    CLR_MASK = 4
  };

  /// Get pointer to a process-wide <PPM_Reactor>.
  static PPM_Reactor *instance (void);

  // = Singleton reactor event loop management methods.

  // Note that these method ONLY work on the "Singleton Reactor,"
  // i.e., the one returned from <PPM_Reactor::instance>.
  /**
   * Run the event loop until the
   * <PPM_Reactor::handle_events/PPM_Reactor::alertable_handle_events>
   * method returns -1 or the <end_event_loop> method is invoked.
   * Note that this method can only be used by the singleton
   * <PPM_Reactor::instance>.  Thus, to run another reactor use
   * <PPM_Reactor::run_reactor_event_loop>.
   *
   * @deprecated Use PPM_Reactor::instance()->run_reactor_event_loop() instead
   */
  int run_event_loop (void);
  /**
   * Instruct the Reactor to terminate its event loop and notifies the
   * Reactor so that it can wake up and deactivate
   * itself. Deactivating the Reactor would allow the Reactor to be
   * shutdown gracefully. Internally the Reactor calls deactivate ()
   * on the underlying implementation.
   * Any queued notifications remain queued on return from this method.
   * If the event loop is restarted in the future, the notifications
   * will be dispatched then. If the reactor is closed or deleted without
   * further dispatching, the notifications will be lost.
   */
  virtual int end_reactor_event_loop (void);
  // These methods work with an instance of a reactor.
  /**
   * Run the event loop until the
   * <ACE_Reactor::handle_events/ACE_Reactor::alertable_handle_events>
   * method returns -1 or the <end_reactor_event_loop> method is invoked.
   */
  virtual int run_reactor_event_loop ();
  virtual BOOL reactor_event_loop_done ();

  /**
   * Instruct the <PPM_Reactor::instance> to terminate its event loop
   * and notifies the <PPM_Reactor::instance> so that it can wake up
   * and close down gracefully.  Note that this method can only be
   * used by the singleton <PPM_Reactor::instance>.  Thus, to
   * terminate another reactor, use
   * <PPM_Reactor::end_reactor_event_loop>.
   *
   * @deprecated Use PPM_Reactor::instance()->end_reactor_event_loop() instead
   */
  int end_event_loop (void);

  //check if te loop is over
  BOOL isEventLoop_Over(void){
	  return (1 == m_bIsStop_);
  };

  /**
   * Create the Reactor using <implementation>.  The flag
   * <delete_implementation> tells the Reactor whether or not to
   * delete the <implementation> on destruction.
   */
  PPM_Reactor (PPM_Reactor_Impl *implementation = 0,
               int delete_implementation = 0);

  /// Close down and release all resources.
  /**
   * Any notifications that remain queued on this reactor instance are
   * lost.
   */
  virtual ~PPM_Reactor (void);


  // = Event loop drivers.

//   /**
//    * Returns non-zero if there are I/O events "ready" for dispatching,
//    * but does not actually dispatch the event handlers.  By default,
//    * don't block while checking this, i.e., "poll".
//    */
//   virtual int work_pending (const PPM_Time_Value &max_wait_time =  PPM_Time_Value::zero);

  /**
   * This event loop driver blocks for up to <max_wait_time> before
   * returning.  It will return earlier if events occur.  Note that
   * <max_wait_time> can be 0, in which case this method blocks
   * indefinitely until events occur.
   *
   * <max_wait_time> is decremented to reflect how much time this call
   * took.  For instance, if a time value of 3 seconds is passed to
   * handle_events and an event occurs after 2 seconds,
   * <max_wait_time> will equal 1 second.  This can be used if an
   * application wishes to handle events for some fixed amount of
   * time.
   *
   * Returns the total number of timers and I/O <PPM_Event_Handler>s
   * that were dispatched, 0 if the <max_wait_time> elapsed without
   * dispatching any handlers, or -1 if an error occurs.
   *
   * The only difference between <alertable_handle_events> and
   * <handle_events> is that in the alertable case, the eventloop will
   * return when the system queues an I/O completion routine or an
   * Asynchronous Procedure Call.
   */
  virtual int handle_events (PPM_Time_Value *max_wait_time = 0);

  /**
   * This method is just like the one above, except the
   * <max_wait_time> value is a reference and can therefore never be
   * NULL.
   *
   * The only difference between <alertable_handle_events> and
   * <handle_events> is that in the alertable case, the eventloop will
   * return when the system queues an I/O completion routine or an
   * Asynchronous Procedure Call.
   */
  virtual int handle_events (PPM_Time_Value &max_wait_time);

  // = Register and remove Handlers.

  /// Register <event_handler> with <mask>.  The I/O handle will always
  /// come from <get_handle> on the <event_handler>.
  virtual int register_handler (PPM_Event_Handler *event_handler,
                                PPM_Reactor_Mask mask);


  /**
   * Removes <event_handler>.  Note that the I/O handle will be
   * obtained using <get_handle> method of <event_handler> .  If
   * <mask> includes <PPM_Event_Handler::DONT_CALL> then the
   * <handle_close> method of the <event_handler> is not invoked.
   */
  virtual int remove_handler (PPM_Event_Handler *event_handler,
                              PPM_Reactor_Mask mask);


  // = Suspend and resume Handlers.

  /// Suspend <event_handler> temporarily.  Use
  /// <PPM_Event_Handler::get_handle> to get the handle.
  virtual int suspend_handler (PPM_Event_Handler *event_handler);

  /// Suspend all <handles> temporarily.
  virtual int suspend_handlers (void);

  /// Resume <event_handler>. Use <PPM_Event_Handler::get_handle> to
  /// get the handle.
  virtual int resume_handler (PPM_Event_Handler *event_handler);

  /// Resume all <handles>.
  virtual int resume_handlers (void);


  /// Returns the current size of the Reactor's internal descriptor
  /// table.
  virtual size_t size (void) const;


  // = Low-level wait_set mask manipulation methods.

  /// GET/SET/ADD/CLR the dispatch mask "bit" bound with the
  /// <event_handler> and <mask>.
  virtual int mask_ops (PPM_Event_Handler *event_handler,
                        PPM_Reactor_Mask mask,
                        int ops);

  // = Low-level ready_set mask manipulation methods.
  /// GET/SET/ADD/CLR the ready "bit" bound with the <event_handler>
  /// and <mask>.
  virtual int ready_ops (PPM_Event_Handler *event_handler,
                         PPM_Reactor_Mask mask,
                         int ops);


  /// Get the implementation class
  virtual PPM_Reactor_Impl *implementation (void) const;
  
  /// Close down and release all resources.
  virtual int close (void);

  /// Dump the state of the object.
  void dump (void) const;

	///复位线程
  virtual BOOL ResetThread(){return TRUE;};

	///线程处理函数
	virtual BOOL Main();

	///线程运行的函数，是一个死循环
	virtual PPM_THR_FUNC_VOID_RETURN CommMain(void);


protected:
  /// Set the implementation class.
  virtual void implementation (PPM_Reactor_Impl *implementation);

  /// Delegation/implementation class that all methods will be
  /// forwarded to.
  PPM_Reactor_Impl *implementation_;

  /// Flag used to indicate whether we are responsible for cleaning up
  /// the implementation instance
  int delete_implementation_;

  /// Pointer to a process-wide <PPM_Reactor> singleton.
  static PPM_Reactor *reactor_;

  PPM_Reactor* reactor_new_;

  /// Must delete the <reactor_> singleton if non-0.
  int delete_reactor_;

//  BYTE isrun_;
//  BYTE isOver_;
  /// Deny access since member-wise won't work...
  PPM_Reactor (const PPM_Reactor &);
  PPM_Reactor &operator = (const PPM_Reactor &);
};

#if !defined (PPM_LACKS_INLINE_FUNCTIONS)
#include "./PPM_Reactor.inl"
#endif /* __PPM_INLINE__ */

#endif /* PPM_REACTOR_H */
