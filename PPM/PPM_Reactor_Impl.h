#ifndef PPM_REACTOR_IMPL_H
#define PPM_REACTOR_IMPL_H

#if !defined (PPM_LACKS_PRAGMA_ONCE)
# pragma once
#endif /* PPM_LACKS_PRAGMA_ONCE */

// Event_Handler.h contains the definition of PPM_Reactor_Mask
#include "./PPM_Event_Handler.h"
#include "./PPM_Signal.h"

// Forward decls
class PPM_Handle_Set;
class PPM_Reactor_Impl;


/**
反应器具体实现的基类，类似于facade模式，给各种不同的实现定义一个公共
的接口
 */
class PPM_Reactor_Impl
{
public:
  /// Close down and release all resources.
  virtual ~PPM_Reactor_Impl (void) {}

  /// Initialization.
  virtual int open (size_t size,
                    int restart = 0,
					PPM_Sig_Handler * = 0) = 0;


  /// Close down and release all resources.
  virtual int close (void) = 0;

//   // = Event loop drivers.
//   /**
//    * Returns non-zero if there are I/O events "ready" for dispatching,
//    * but does not actually dispatch the event handlers.  By default,
//    * don't block while checking this, i.e., "poll".
//    */
//   virtual int work_pending (const PPM_Time_Value &max_wait_time =  PPM_Time_Value::zero)  = 0;


  virtual int handle_events (PPM_Time_Value *max_wait_time = 0) = 0;

  virtual int handle_events (PPM_Time_Value &max_wait_time) = 0;

  // = Event handling control.

  /**
   * Return the status of Reactor.  If this function returns 0, the reactor is
   * actively handling events.  If it returns non-zero, <handling_events> and
   * <handle_alertable_events> return -1 immediately.
   */
  virtual int deactivated (void) = 0;

  /**
   * Control whether the Reactor will handle any more incoming events or not.
   * If <do_stop> == 1, the Reactor will be disabled.  By default, a reactor
   * is in active state and can be deactivated/reactived as wish.
   */
  virtual void deactivate (int do_stop) = 0;

  // = Register and remove Handlers.

  /// Register <event_handler> with <mask>.  The I/O handle will always
  /// come from <get_handle> on the <event_handler>.
  virtual int register_handler (PPM_Event_Handler *event_handler,
                                PPM_Reactor_Mask mask) = 0;

  /**
   * Removes <event_handler>.  Note that the I/O handle will be
   * obtained using <get_handle> method of <event_handler> .  If
   * <mask> == <PPM_Event_Handler::DONT_CALL> then the <handle_close>
   * method of the <event_handler> is not invoked.
   */
  virtual int remove_handler (PPM_Event_Handler *event_handler,
                              PPM_Reactor_Mask mask) = 0;


  /// Suspend <event_handler> temporarily.  Use
  /// <PPM_Event_Handler::get_handle> to get the handle.
  virtual int suspend_handler (PPM_Event_Handler *event_handler) = 0;

  /// Suspend all <handles> temporarily.
  virtual int suspend_handlers (void) = 0;

  /// Resume <event_handler>. Use <PPM_Event_Handler::get_handle> to
  /// get the handle.
  virtual int resume_handler (PPM_Event_Handler *event_handler) = 0;

  /// Resume all <handles>.
  virtual int resume_handlers (void) = 0;

  /// Returns the current size of the Reactor's internal descriptor
  /// table.
  virtual size_t size (void) const = 0;

  // = Low-level wait_set mask manipulation methods.

  /// GET/SET/ADD/CLR the dispatch mask "bit" bound with the
  /// <event_handler> and <mask>.
  virtual int mask_ops (PPM_Event_Handler *event_handler,
                        PPM_Reactor_Mask mask,
                        int ops) = 0;

  // = Low-level ready_set mask manipulation methods.
  /// GET/SET/ADD/CLR the ready "bit" bound with the <event_handler>
  /// and <mask>.
  virtual int ready_ops (PPM_Event_Handler *event_handler,
                         PPM_Reactor_Mask mask,
                         int ops) = 0;


  /// Dump the state of an object.
  virtual void dump (void) const = 0;
};

#endif /* PPM_REACTOR_IMPL_H */
