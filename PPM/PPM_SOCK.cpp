// SOCK.cpp
// SOCK.cpp,v 4.26 2001/12/03 21:30:09 shuston Exp

#include "./PPM_SOCK.h"
//#include "Log_Msg.h"

#if defined (PPM_LACKS_INLINE_FUNCTIONS)
#include "./PPM_SOCK.inl"
#endif /* PPM_LACKS_INLINE_FUNCTIONS */

void
PPM_SOCK::dump (void) const
{
  PPM_TRACE ("PPM_SOCK::dump");
}

PPM_SOCK::PPM_SOCK (void)
{
  // PPM_TRACE ("PPM_SOCK::PPM_SOCK");
}

// Returns information about the remote peer endpoint (if there is
// one).

int
PPM_SOCK::get_remote_addr (PPM_Addr &sa) const
{
  PPM_TRACE ("PPM_SOCK::get_remote_addr");

  int len = sa.get_size ();
  sockaddr *addr = PPM_reinterpret_cast (sockaddr *,
                                         sa.get_addr ());

  if (PPM_OS::getpeername (this->get_handle (),
                           addr,
                           &len) == -1)
    return -1;
  
  sa.set_size (len);
  sa.set_type (addr->sa_family);
  return 0;
}

int
PPM_SOCK::get_local_addr (PPM_Addr &sa) const
{
  PPM_TRACE ("PPM_SOCK::get_local_addr");

  int len = sa.get_size ();
  sockaddr *addr = PPM_reinterpret_cast (sockaddr *,
                                         sa.get_addr ());

  if (PPM_OS::getsockname (this->get_handle (),
                           addr,
                           &len) == -1)
    return -1;

  sa.set_type (addr->sa_family);
  sa.set_size (len);
  return 0;
}

// Close down a PPM_SOCK.

int
PPM_SOCK::close (void)
{
  PPM_TRACE ("PPM_SOCK::close");
  int result = 0;

  if (this->get_handle () != PPM_INVALID_HANDLE)
    {
      result = PPM_OS::closesocket (this->get_handle ());
      this->set_handle (PPM_INVALID_HANDLE);
    }
  return result;
}

int
PPM_SOCK::open (int type, 
                int protocol_family, 
                int protocol,
                int reuse_addr)
{
  PPM_TRACE ("PPM_SOCK::open");
  int one = 1;

  this->set_handle (PPM_OS::socket (protocol_family,
                                    type,
                                    protocol));

  if (this->get_handle () == PPM_INVALID_HANDLE)
    return -1;
  else if (protocol_family != PF_UNIX 
           && reuse_addr 
           && this->set_option (SOL_SOCKET,
                                SO_REUSEADDR,
                                &one,
                                sizeof one) == -1)
    {
      this->close ();
      return -1;
    }
  return 0;
}

// General purpose constructor for performing server PPM_SOCK
// creation.

PPM_SOCK::PPM_SOCK (int type, 
                    int protocol_family, 
                    int protocol,
                    int reuse_addr)
{
  // PPM_TRACE ("PPM_SOCK::PPM_SOCK");
  if (this->open (type,
                  protocol_family, 
                  protocol,
                  reuse_addr) == -1)
  {
	  PPM_TRACE("ppm_sock open error\n");
  }
}
