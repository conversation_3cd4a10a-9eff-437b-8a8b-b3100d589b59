#ifndef PPM_SOCK_ACCEPTOR_H
#define PPM_SOCK_ACCEPTOR_H


#if !defined (PPM_LACKS_PRAGMA_ONCE)
# pragma once
#endif /* PPM_LACKS_PRAGMA_ONCE */

#include "./PPM_SOCK_Stream.h"
#include "./PPM_Time_Value.h"
#include "./PPM_INET_Addr.h"

/**
此类是一个接收器，一个工厂，专门用来生产socket，自身带有一个socket的handle
当有连接接入时，由接收器上层传入新的socket句柄
 */
class PPM_SOCK_Acceptor : public PPM_SOCK
{
public:
  // = Initialization and termination methods.
  /// Default constructor.
  PPM_SOCK_Acceptor (void);

  /**
   * Initialize a passive-mode BSD-style acceptor socket (no QoS).
   * <local_sap> is the address that we're going to listen for
   * connections on.  If <reuse_addr> is 1 then we'll use the
   * <SO_REUSEADDR> to reuse this address.
   */
  PPM_SOCK_Acceptor (const PPM_Addr &local_sap,
                     int reuse_addr = 1,
                     int protocol_family = PF_INET,
                     int backlog = PPM_DEFAULT_BACKLOG,
                     int protocol = 0);

  /**
   * Initialize a passive-mode BSD-style acceptor socket (no QoS).
   * <local_sap> is the address that we're going to listen for
   * connections on.  If <reuse_addr> is 1 then we'll use the
   * <SO_REUSEADDR> to reuse this address.  Returns 0 on success and
   * -1 on failure.
   */
  int open (const PPM_Addr &local_sap,
            int reuse_addr = 1,
            int protocol_family = PF_INET,
            int backlog = PPM_DEFAULT_BACKLOG,
            int protocol = 0);

 
  /// Close the socket.  Returns 0 on success and -1 on failure.
  int close (void);

  /// Default dtor.
  ~PPM_SOCK_Acceptor (void);

  // = Passive connection <accept> methods.
  /**
   * Accept a new <PPM_SOCK_Stream> connection.  A <timeout> of 0
   * means block forever, a <timeout> of {0, 0} means poll.  <restart>
   * == 1 means "restart if interrupted," i.e., if errno == EINTR.
   * Note that <new_stream> inherits the "blocking mode" of <this>
   * <PPM_SOCK_Acceptor>, i.e., if <this> acceptor factory is in
   * non-blocking mode, the <net_stream> will be in non-blocking mode
   * and vice versa.
   */
  int accept (PPM_SOCK_Stream &new_stream,
              PPM_Addr *remote_addr = 0,
              PPM_Time_Value *timeout = 0,
              int restart = 1,
              int reset_new_handle = 0) const;

  // = Meta-type info
  typedef PPM_INET_Addr PEER_ADDR;
  typedef PPM_SOCK_Stream PEER_STREAM;

  /// Dump the state of an object.
  void dump (void) const;

protected:
  /// Perform operations that must occur before <PPM_OS::accept> is
  /// called.
  int shared_accept_start (PPM_Time_Value *timeout,
                           int restart,
                           int &in_blocking_mode) const;

  /// Perform operations that must occur after <PPM_OS::accept> is
  /// called.
  int shared_accept_finish (PPM_SOCK_Stream new_stream,
                            int in_blocking_mode,
                            int reset_new_handle) const;

  /**
   * This method factors out the common <open> code and is called by
   * both the QoS-enabled <open> method and the BSD-style <open>
   * method.
   */
  int shared_open (const PPM_Addr &local_sap,
                   int protocol_family,
                   int backlog);

private:
  /// Do not allow this function to percolate up to this interface...
  int get_remote_addr (PPM_Addr &) const;
};

#endif /* PPM_SOCK_ACCEPTOR_H */
