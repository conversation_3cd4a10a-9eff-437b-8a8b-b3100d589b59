#include "./PPM_SOCK_Buffer.h"
#include "./PPM_Public_Var.h"

PPM_SOCK_Buffer::PPM_SOCK_Buffer(char* strip,u_short portno,
								 int ReadSize, BYTE RNodeType, 
								 int WriteSize, BYTE WNodeType,
								 BYTE exclusive_flag,BYTE DealDataFlag)
{
	char tm_strip[46]; //为了兼容IPv6地址长度，改为了46 [fhx]
	PPM_OS::snprintf(tm_strip,sizeof(tm_strip),"%s",strip);
	PPM_OS::memcpy(m_pipaddress,tm_strip,sizeof(m_pipaddress));
	m_portNo = portno;

	Init(ReadSize,RNodeType,WriteSize,WNodeType,exclusive_flag,DealDataFlag);
}

PPM_SOCK_Buffer::~PPM_SOCK_Buffer()
{
	if(NULL != m_precvProxy){
		delete m_precvProxy;
		m_precvProxy = NULL;
	}
	if(NULL != m_psendProxy){
		delete m_psendProxy;
		m_psendProxy = NULL;
	}
}

BOOL
PPM_SOCK_Buffer::Init(int ReadSize,BYTE RNodeType,int WriteSize, BYTE WNodeType, BYTE exclusive_flag,BYTE DealDataFlag)
{
	m_RecvMode = RNodeType;
	m_SendMode = WNodeType;
	//传给CStdBuffer的数据处理参数，false表示不需要获取历史数据,将历史数据删除,true表示需要获取历史数据
	bool bDealDataFlag = false;
	if(CNodeMng::NullMode != RNodeType){
		if( (DealDataFlag & 0x01) > 0 ){
			//表示需要处理完收的数据
			bDealDataFlag = true;
		}else{
			bDealDataFlag = false;
		}
		m_precvProxy = new CStdBuffer(ReadSize,RNodeType,this->m_pipaddress,this->m_portNo, 0, bDealDataFlag);
	//	m_precvProxy = new CStdBuffer();
		if(NULL == m_precvProxy){
			PPM_SOCKLOG((LM_ERROR,"初始化m_precvProxy出错:%m \n"));
			return FALSE;
		}
	}
	else{
		m_precvProxy = NULL;
	}
	if(CNodeMng::NullMode != WNodeType){
		if( (DealDataFlag & 0x02) > 0 ){
			//表示需要处理完发的数据
			bDealDataFlag = true;
		}else{
			bDealDataFlag = false;
		}
		m_psendProxy = new CStdBuffer(WriteSize,WNodeType,this->m_pipaddress,this->m_portNo, 1, bDealDataFlag);
	//	m_psendProxy = new CStdBuffer();
		if(NULL == m_psendProxy){
			PPM_SOCKLOG((LM_ERROR,"初始化m_psendProxy出错:%m \n"));
			return FALSE;
		}
	}
	else{
		m_psendProxy = NULL;
	}
	m_exclusive_flag = exclusive_flag;
	m_DealDataFlag = DealDataFlag;

	return TRUE;
}
//向发缓冲区中写入指定长度的数据,返回写入的长度，应用层使用
int
PPM_SOCK_Buffer::WriteData(const BYTE * const pBuf, int nSize)
{
	if (nSize > (NODE_BUF_SIZE - PKG_HEADER_LEN))
	{
		PPM_SOCKLOG((LM_ERROR,"SendData size is too big.\n"));
		return -1;
	}
	else
	{
		return m_psendProxy->Write(pBuf, nSize);
	}

}
//从收缓冲区中读取指定长度的数据，返回读取的长度,应用层使用
int
PPM_SOCK_Buffer::ReadData(BYTE ** const pBuf, int& nSize)
{
	if (m_precvProxy->Read(pBuf, nSize) <= 0)
	{
//		m_precvProxy->Dump();

		return 0;
	}
	
	if (((*pBuf) != NULL) && ((nSize - PKG_HEADER_LEN) > 0))
	{
		(*pBuf) = (*pBuf) + PKG_HEADER_LEN;

		nSize = nSize - PKG_HEADER_LEN;
	}
	else
	{
		*pBuf = NULL;

		nSize = 0;
	}
	return nSize;
}

//从发缓冲区中取数据,用于socket发送,返回取到的数据长度
int
PPM_SOCK_Buffer::GetSendData(BYTE** pBuf, int& nCount)
{
	int res = m_psendProxy->Read(pBuf, nCount);
	if(res > 0){
		return res;
	}
	else{
//		m_psendProxy->Dump();
		return 0;
	}

}
//向收缓冲区中写数据，给应用程读数据使用,返回写入数据的长度
int
PPM_SOCK_Buffer::PutRecvData(const BYTE* const pBuf, const int nCount)
{
	return m_precvProxy->Write(pBuf, nCount);
}