#ifndef _PPM_SOCK_CONNECTOR_H_
#define  _PPM_SOCK_CONNECTOR_H_

#include "PPM_MACRO.h"
#include "PPM_Addr.h"
#include "PPM_Time_Value.h"
#include "PPM_SOCK_Stream.h"

/************************************************************************
    此类仅提供一个连接socket句柄的方法，连接后的数据收发由socket自身处理，
	重连机制由PPM_SOCK_Connector上一层包装
************************************************************************/

class PPM_SOCK_Connector
{
public:
  //构造函数
  PPM_SOCK_Connector (void);

  //作为客户端连接服务端的方法
  //连接不上重连采用新增线程重连的方式，表现在应用程就是一个socket的ID，具体的句柄由数据通讯模块解析
  //连接有阻塞方式和非阻塞方式，如果是非阻塞方式，则在complete函数中处理
  int connect (PPM_SOCK_Stream &new_stream,
               const PPM_Addr &remote_sap,
               const PPM_Time_Value *timeout = 0,
               const PPM_Addr &local_sap = PPM_Addr::sap_any,
			   int reuse_addr = 1);

  /// 析购函数
  ~PPM_SOCK_Connector (void);

protected:
  /// Perform operations that ensure the socket is opened using
  /// BSD-style semantics (no QoS).
  int shared_open (PPM_SOCK_Stream &new_stream,
                   int protocol_family,
                   int protocol,
                   int reuse_addr);
  /// Perform operations that must be called before <PPM_OS::connect>.
  int shared_connect_start (PPM_SOCK_Stream &new_stream,
                            const PPM_Time_Value *timeout,
                            const PPM_Addr &local_sap);
  /// Perform operations that must be called after <PPM_OS::connect>.
  int shared_connect_finish (PPM_SOCK_Stream &new_stream,
                             const PPM_Time_Value *timeout,
                             int result);
  // = Completion routine.
  /**
   * Try to complete a nonblocking connection that was begun by a
   * previous call to connect with a {0, 0} ACE_Time_Value timeout.
   * @see connect().
   *
   * @param new_stream  The @c ACE_SOCK_Stream object that will be connected
   *                    to the peer.
   * @param remote_sap  If non-0, it points to the @c ACE_INET_Addr object
   *                    that will contain the address of the connected peer.
   * @param timeout     Same values and return value possibilites as for
   *                    connect(). @see connect().
   */
  int complete (PPM_SOCK_Stream &new_stream,
                PPM_Addr *remote_sap = 0,
                const PPM_Time_Value *timeout = 0);

};

#if !defined (PPM_LACKS_INLINE_FUNCTIONS)
#include "PPM_SOCK_Connector.inl"
#endif

#endif