#ifndef _PPM_SOCK_DGRAM_H_
#define _PPM_SOCK_DGRAM_H_
#include "./PPM_SOCK.h"

class PPM_SOCK_Dgram : public PPM_SOCK
{
public:
  // = Initialization and termination methods.
  /// Default constructor.
  PPM_SOCK_Dgram (void);

  /// This is a BSD-style method (i.e., no QoS) for initiating a socket
  /// dgram that will accept datagrams at the <local> address.
  PPM_SOCK_Dgram (const PPM_Addr &local,
                  int protocol_family = PPM_PROTOCOL_FAMILY_INET,
                  int protocol = 0,
                  int reuse_addr = 0);
  
  /// This is a BSD-style method (i.e., no QoS) for initiating a socket
  /// dgram that will accept datagrams at the <local> address.
  int open (const PPM_Addr &local,
            int protocol_family = PPM_PROTOCOL_FAMILY_INET,
            int protocol = 0,
            int reuse_addr = 0);
  ///for client
  int open();

	// 关闭socket
	int close(void)
	{
		return PPM_SOCK::close();
	};

  /// Default dtor.
  ~PPM_SOCK_Dgram (void);

  // = Data transfer routines.
  /// Send an <n> byte <buf> to the datagram socket (uses <sendto(3)>).
  int send (const void *buf,
                size_t n,
                const PPM_Addr &addr,
                int flags = 0) const;

  /// Receive an <n> byte <buf> from the datagram socket (uses
  /// <recvfrom(3)>).
  int recv (void *buf,
                size_t n,
                PPM_Addr &addr,
                int flags = 0) const;

protected:
  /// Open is shared by this and by <LSOCK_Dgram>.
  int shared_open (const PPM_Addr &local,
                   int protocol_family);

private:
  /// Do not allow this function to percolate up to this interface...
  int  get_remote_addr (PPM_Addr &) const;
	
};



#endif
