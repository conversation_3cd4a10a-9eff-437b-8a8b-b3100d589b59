// PPM_SOCK_Stream.h: interface for the PPM_SOCK_Stream class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_PPM_SOCK_STREAM_H__3F37279F_3255_4680_8232_813FD6D8B038__INCLUDED_)
#define AFX_PPM_SOCK_STREAM_H__3F37279F_3255_4680_8232_813FD6D8B038__INCLUDED_

#pragma once

#include "./PPM_SOCK.h"

class PPM_SOCK_Stream : public PPM_SOCK  
{
public:
	PPM_SOCK_Stream(void);
	virtual ~PPM_SOCK_Stream(void);

	PPM_SOCK_Stream (const PPM_HANDLE h);
	// 创建socket, 设置属性
	virtual int open (int type, int protocol_family, 
                int protocol, int reuse_addr);
	// 关闭socket
	int close(void)
	{
		return PPM_SOCK::close();
	};
	
	// socket接收数据
	int recv(char * const pBuf, const int nSize, const int flags = 0);
	int recv_n(char * const pBuf, const int nSize, const int flags = 0);
	// socket发送数据
	int send(const char* const pBuf , const int nSize );
	// 读监测
	int select_r(void);
	// 写监测
	int select_w(void);
};


#endif // !defined(AFX_PPM_SOCK_STREAM_H__3F37279F_3255_4680_8232_813FD6D8B038__INCLUDED_)
