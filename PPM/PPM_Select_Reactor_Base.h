/* -*- C++ -*- */

#ifndef PPM_SELECT_REACTOR_BASE_H
#define PPM_SELECT_REACTOR_BASE_H
#include "../stdclass/StdHeader.h"
#include "../stdclass/StdLock.h"

#include "./PPM_Signal.h"

#if !defined (PPM_LACKS_PRAGMA_ONCE)
# pragma once
#endif /* PPM_LACKS_PRAGMA_ONCE */

#include "./PPM_Event_Handler.h"
#include "./PPM_Handle_Set.h"
#include "./PPM_Reactor_Impl.h"

// Add useful typedefs to simplify the following code.
typedef void (PPM_Handle_Set::*PPM_FDS_PTMF) (PPM_HANDLE);
typedef int (PPM_Event_Handler::*PPM_EH_PTMF) (PPM_HANDLE);

//#if defined (PPM_MT_SAFE) && (PPM_MT_SAFE != 0)
//typedef PPM_Token PPM_SELECT_TOKEN;
//#else
//typedef PPM_Noop_Token PPM_SELECT_TOKEN;
//#endif /* PPM_MT_SAFE && PPM_MT_SAFE != 0 */

// Forward declaration.
class PPM_Select_Reactor_Impl;

/**
 * @class PPM_Select_Reactor_Handle_Set
 *
 * @brief Track handles we are interested for various events.
 */
class  PPM_Select_Reactor_Handle_Set
{
public:
  /// Read events (e.g., input pending, accept pending).
  PPM_Handle_Set rd_mask_;

  /// Write events (e.g., flow control abated, non-blocking connection
  /// complete).
  PPM_Handle_Set wr_mask_;

  /// Exception events (e.g., SIG_URG).
  PPM_Handle_Set ex_mask_;
};

/**
 * @class PPM_Event_Tuple
 *
 * @brief An PPM_Event_Handler and its associated PPM_HANDLE.
 *
 * One <PPM_Event_Handler> is registered for one or more
 * <PPM_HANDLE>.  At various points, this information must be
 * stored explicitly.  This class provides a lightweight
 * mechanism to do so.
 */
class  PPM_Event_Tuple
{
public:
  /// Default constructor.
  PPM_Event_Tuple (void);

  /// Constructor.
  PPM_Event_Tuple (PPM_Event_Handler *eh,
                   PPM_HANDLE h);

  /// Destructor.
  ~PPM_Event_Tuple (void);

  /// Equality operator.
  int operator== (const PPM_Event_Tuple &rhs) const;

  /// Inequality operator.
  int operator!= (const PPM_Event_Tuple &rhs) const;

  /// Handle.
  PPM_HANDLE handle_;

  /// <PPM_Event_Handler> associated with the <PPM_HANDLE>.
  PPM_Event_Handler *event_handler_;
};

/**
 * @class PPM_Select_Reactor_Notify
 *
 * @brief Unblock the <PPM_Select_Reactor> from its event loop.
 *
 * This implementation is necessary for cases where the
 * <PPM_Select_Reactor> is run in a multi-threaded program.  In
 * this case, we need to be able to unblock <select> or <poll>
 * when updates occur other than in the main
 * <PPM_Select_Reactor> thread.  To do this, we signal an
 * auto-reset event the <PPM_Select_Reactor> is listening on.
 * If an <PPM_Event_Handler> and <PPM_Select_Reactor_Mask> is
 * passed to <notify>, the appropriate <handle_*> method is
 * dispatched in the context of the <PPM_Select_Reactor> thread.
 */
//class  PPM_Select_Reactor_Notify : public PPM_Reactor_Notify
//{
//public:
//  /// Constructor.
//  PPM_Select_Reactor_Notify (void);
//
//  /// Destructor.
//  ~PPM_Select_Reactor_Notify (void);
//
//  // = Initialization and termination methods.
//  /// Initialize.
//  virtual int open (PPM_Reactor_Impl *,
//                    PPM_Timer_Queue * = 0,
//                    int disable_notify_pipe = 0);
//
//  /// Destroy.
//  virtual int close (void);
//
//  /**
//   * Called by a thread when it wants to unblock the
//   * <PPM_Select_Reactor>.  This wakeups the <PPM_Select_Reactor> if
//   * currently blocked in <select>/<poll>.  Pass over both the
//   * <Event_Handler> *and* the <mask> to allow the caller to dictate
//   * which <Event_Handler> method the <PPM_Select_Reactor> will
//   * invoke.  The <PPM_Time_Value> indicates how long to blocking
//   * trying to notify the <PPM_Select_Reactor>.  If <timeout> == 0,
//   * the caller will block until action is possible, else will wait
//   * until the relative time specified in *<timeout> elapses).
//   */
//  virtual int notify (PPM_Event_Handler * = 0,
//                      PPM_Reactor_Mask = PPM_Event_Handler::EXCEPT_MASK,
//                      PPM_Time_Value * = 0);
//
//  /// Handles pending threads (if any) that are waiting to unblock the
//  /// <PPM_Select_Reactor>.
//  virtual int dispatch_notifications (int &number_of_active_handles,
//                                      PPM_Handle_Set &rd_mask);
//
//  /// Returns the PPM_HANDLE of the notify pipe on which the reactor
//  /// is listening for notifications so that other threads can unblock
//  /// the Select_Reactor
//  virtual PPM_HANDLE notify_handle (void);
//
//  /// Handle one of the notify call on the <handle>. This could be
//  /// because of a thread trying to unblock the <Reactor_Impl>
//  virtual int dispatch_notify (PPM_Notification_Buffer &buffer);
//
//  /// Read one of the notify call on the <handle> into the
//  /// <buffer>. This could be because of a thread trying to unblock
//  /// the <Reactor_Impl>
//  virtual int read_notify_pipe (PPM_HANDLE handle,
//                                PPM_Notification_Buffer &buffer);
//
//  /// Verify whether the buffer has dispatchable info  or not.
//  virtual int is_dispatchable (PPM_Notification_Buffer &buffer);
//
//  /// Called back by the <PPM_Select_Reactor> when a thread wants to
//  /// unblock us.
//  virtual int handle_input (PPM_HANDLE handle);
//
//  /**
//   * Set the maximum number of times that the
//   * <PPM_Select_Reactor_Notify::handle_input> method will iterate and
//   * dispatch the <PPM_Event_Handlers> that are passed in via the
//   * notify pipe before breaking out of its <recv> loop.  By default,
//   * this is set to -1, which means "iterate until the pipe is empty."
//   * Setting this to a value like "1 or 2" will increase "fairness"
//   * (and thus prevent starvation) at the expense of slightly higher
//   * dispatching overhead.
//   */
//  virtual void max_notify_iterations (int);
//
//  /**
//   * Get the maximum number of times that the
//   * <PPM_Select_Reactor_Notify::handle_input> method will iterate and
//   * dispatch the <PPM_Event_Handlers> that are passed in via the
//   * notify pipe before breaking out of its <recv> loop.
//   */
//  virtual int max_notify_iterations (void);
//
//  /**
//   * Purge any notifications pending in this reactor for the specified
//   * <PPM_Event_Handler> object. If <eh> == 0, all notifications for all
//   * handlers are removed (but not any notifications posted just to wake up
//   * the reactor itself). Returns the number of notifications purged.
//   * Returns -1 on error.
//   */
//  virtual int purge_pending_notifications (PPM_Event_Handler *,
//                                           PPM_Reactor_Mask = PPM_Event_Handler::ALL_EVENTS_MASK);
//
//  /// Dump the state of an object.
//  virtual void dump (void) const;
//
//  /// Declare the dynamic allocation hooks.
//  PPM_ALLOC_HOOK_DECLARE;
//
//protected:
//  /**
//   * Keep a back pointer to the <PPM_Select_Reactor>.  If this value
//   * if NULL then the <PPM_Select_Reactor> has been initialized with
//   * <disable_notify_pipe>.
//   */
//  PPM_Select_Reactor_Impl *select_reactor_;
//
//  /**
//   * Contains the <PPM_HANDLE> the <PPM_Select_Reactor> is listening
//   * on, as well as the <PPM_HANDLE> that threads wanting the
//   * attention of the <PPM_Select_Reactor> will write to.
//   */
//  PPM_Pipe notification_pipe_;
//
//  /**
//   * Keeps track of the maximum number of times that the
//   * <PPM_Select_Reactor_Notify::handle_input> method will iterate and
//   * dispatch the <PPM_Event_Handlers> that are passed in via the
//   * notify pipe before breaking out of its <recv> loop.  By default,
//   * this is set to -1, which means "iterate until the pipe is empty."
//   */
//  int max_notify_iterations_;
//
//#if defined (PPM_HAS_REACTOR_NOTIFICATION_QUEUE)
//  // = This configuration queues up notifications in separate buffers that
//  //   are in user-space, rather than stored in a pipe in the OS
//  //   kernel.  The kernel-level notifications are used only to trigger
//  //   the Reactor to check its notification queue.  This enables many
//  //   more notifications to be stored than would otherwise be the case.
//
//  /// Keeps track of allocated arrays of type
//  /// <PPM_Notification_Buffer>.
//  PPM_Unbounded_Queue <PPM_Notification_Buffer *> alloc_queue_;
//
//  /// Keeps track of all pending notifications.
//  PPM_Unbounded_Queue <PPM_Notification_Buffer *> notify_queue_;
//
//  /// Keeps track of all free buffers.
//  PPM_Unbounded_Queue <PPM_Notification_Buffer *> free_queue_;
//
//  /// Synchronization for handling of queues.
//  PPM_SYNCH_MUTEX notify_queue_lock_;
//#endif /* PPM_HAS_REACTOR_NOTIFICATION_QUEUE */
//};

/**
 * @class PPM_Select_Reactor_Handler_Repository
 *
 * @brief Used to map <PPM_HANDLE>s onto the appropriate
 * <PPM_Event_Handler> *.
 *
 * This class is necessary to shield differences between UNIX
 * and Win32.  In UNIX, <PPM_HANDLE> is an int, whereas in Win32
 * it's a void *.  This class hides all these details from the
 * bulk of the <PPM_Select_Reactor> code.  All of these methods
 * are called with the main <Select_Reactor> token lock held.
 */
class  PPM_Select_Reactor_Handler_Repository
{
public:
  friend class PPM_Select_Reactor_Handler_Repository_Iterator;

  // = Initialization and termination methods.
  /// Default "do-nothing" constructor.
  PPM_Select_Reactor_Handler_Repository (PPM_Select_Reactor_Impl &);

  /// Destructor.
  ~PPM_Select_Reactor_Handler_Repository (void);

  /// Initialize a repository of the appropriate <size>.
  /**
   * On Unix platforms, the size parameter should be as large as the
   * maximum number of file descriptors allowed for a given process.
   * This is necessary since a file descriptor is used to directly
   * index the array of event handlers maintained by the Reactor's
   * handler repository.  Direct indexing is used for efficiency
   * reasons.
   */
  int open (size_t size);

  /// Close down the repository.
  int close (void);

  // = Search structure operations.

  /**
   * Return the <PPM_Event_Handler *> associated with <PPM_HANDLE>.
   * If <index_p> is non-0, then return the index location of the
   * <handle>, if found.
   */
  PPM_Event_Handler *find (PPM_HANDLE handle, size_t *index_p = 0);

  /// Bind the <PPM_Event_Handler *> to the <PPM_HANDLE> with the
  /// appropriate <PPM_Reactor_Mask> settings.
  int bind (PPM_HANDLE,
            PPM_Event_Handler *,
            PPM_Reactor_Mask);

  /// Remove the binding of <PPM_HANDLE> in accordance with the <mask>.
  int unbind (PPM_HANDLE,
              PPM_Reactor_Mask mask);

  /// Remove all the <PPM_HANDLE, PPM_Event_Handler> tuples.
  int unbind_all (void);

  // = Sanity checking.

  // Check the <handle> to make sure it's a valid PPM_HANDLE that
  // within the range of legal handles (i.e., >= 0 && < max_size_).
  int invalid_handle (PPM_HANDLE handle);

  // Check the <handle> to make sure it's a valid PPM_HANDLE that
  // within the range of currently registered handles (i.e., >= 0 && <
  // max_handlep1_).
  int handle_in_range (PPM_HANDLE handle);

  // = Accessors.
  /// Returns the current table size.
  size_t size (void) const;

  /// Maximum PPM_HANDLE value, plus 1.
  size_t max_handlep1 (void);

  /// Dump the state of an object.
  void dump (void) const;

private:
  /// Reference to our <Select_Reactor>.
  PPM_Select_Reactor_Impl &select_reactor_;

  /// Maximum number of handles.
  int max_size_;

  CStdLock m_lock_Repository_;

  /// The highest currently active handle, plus 1 (ranges between 0 and
  /// <max_size_>.
  int max_handlep1_;

#if defined (WIN32)
  // = The mapping from <HANDLES> to <Event_Handlers>.

  /**
   * The NT version implements this via a dynamically allocated
   * array of <PPM_Event_Tuple *>.  Since NT implements PPM_HANDLE
   * as a void * we can't directly index into this array.  Therefore,
   * we just do a linear search (for now).  Next, we'll modify
   * things to use hashing or something faster...
   */
  PPM_Event_Tuple *event_handlers_;
#else
  /**
   * The UNIX version implements this via a dynamically allocated
   * array of <PPM_Event_Handler *> that is indexed directly using
   * the PPM_HANDLE value.
   */
  PPM_Event_Handler **event_handlers_;
#endif /* WIN32 */
};

/**
 * @class PPM_Select_Reactor_Handler_Repository_Iterator
 *
 * @brief Iterate through the <PPM_Select_Reactor_Handler_Repository>.
 */
class  PPM_Select_Reactor_Handler_Repository_Iterator
{
public:
  // = Initialization method.
  PPM_Select_Reactor_Handler_Repository_Iterator (const PPM_Select_Reactor_Handler_Repository *s);

  /// dtor.
  ~PPM_Select_Reactor_Handler_Repository_Iterator (void);

  // = Iteration methods.

  /// Pass back the <next_item> that hasn't been seen in the Set.
  /// Returns 0 when all items have been seen, else 1.
  int next (PPM_Event_Handler *&next_item);

  /// Returns 1 when all items have been seen, else 0.
  int done (void) const;

  /// Move forward by one element in the set.  Returns 0 when all the
  /// items in the set have been seen, else 1.
  int advance (void);

  /// Dump the state of an object.
  void dump (void) const;

private:
  /// Reference to the Handler_Repository we are iterating over.
  const PPM_Select_Reactor_Handler_Repository *rep_;

  /// Pointer to the current iteration level.
  int current_;
};

/**
 * @class PPM_Select_Reactor_Impl
 *
 * @brief This class simply defines how Select_Reactor's basic interface
 * functions should look like and provides a common base class for
 * <Select_Reactor> using various locking mechanism.
 */
class  PPM_Select_Reactor_Impl : public PPM_Reactor_Impl
{
public:
  enum
  {
    /// Default size of the Select_Reactor's handle table.
    DEFAULT_SIZE = PPM_DEFAULT_SELECT_REACTOR_SIZE
  };

  /// Constructor.
  PPM_Select_Reactor_Impl (void);

  friend class PPM_Select_Reactor_Notify;
  friend class PPM_Select_Reactor_Handler_Repository;

  /**
   * Purge any notifications pending in this reactor for the specified
   * <PPM_Event_Handler> object. Returns the number of notifications
   * purged. Returns -1 on error.
   */
  virtual int purge_pending_notifications (PPM_Event_Handler * = 0,
                                           PPM_Reactor_Mask    = PPM_Event_Handler::ALL_EVENTS_MASK);

  /// Does the reactor allow the application to resume the handle on
  /// its own ie. can it pass on the control of handle resumption to
  /// the application.  The select reactor has no handlers that can be
  /// resumed by the  application. So return 0;
  virtual int resumable_handler (void);

protected:
  /// Allow manipulation of the <wait_set_> mask and <ready_set_> mask.
  virtual int bit_ops (PPM_HANDLE handle,
                       PPM_Reactor_Mask mask,
                       PPM_Select_Reactor_Handle_Set &wait_Set,
                       int ops);

  /// Enqueue ourselves into the list of waiting threads at the
  /// appropriate point specified by <requeue_position_>.
  virtual void renew (void) = 0;

  /// Check to see if the <Event_Handler> associated with <handle> is
  /// suspended. Returns 0 if not, 1 if so.
  virtual int is_suspended_i (PPM_HANDLE handle) = 0;

  /// Table that maps <PPM_HANDLEs> to <PPM_Event_Handler *>'s.
  PPM_Select_Reactor_Handler_Repository handler_rep_;

  /// Tracks handles that are waited for by <select>.
  PPM_Select_Reactor_Handle_Set wait_set_;

  /// Tracks handles that are currently suspended.
  PPM_Select_Reactor_Handle_Set suspend_set_;

  /// Track HANDLES we are interested in for various events that must
  /// be dispatched *without* going through <select>.
  PPM_Select_Reactor_Handle_Set ready_set_;

  /// Defined as a pointer to allow overriding by derived classes...
//   PPM_Timer_Queue *timer_queue_;

  /// Keeps track of whether we should delete the timer queue (if we
  /// didn't create it, then we don't delete it).
  int delete_timer_queue_;

  /// Handle signals without requiring global/static variables.
  PPM_Sig_Handler *signal_handler_;

  /// Keeps track of whether we should delete the signal handler (if we
  /// didn't create it, then we don't delete it).
  int delete_signal_handler_;

  /// Callback object that unblocks the <PPM_Select_Reactor> if it's
  /// sleeping.
//   PPM_Reactor_Notify *notify_handler_;

  /// Keeps track of whether we need to delete the notify handler (if
  /// we didn't create it, then we don't delete it).
  int delete_notify_handler_;

  /// Restart the <handle_events> event-loop method automatically when
  /// <select> is interrupted via <EINTR>.
  int restart_;

  /**
   * Position that the main PPM_Select_Reactor thread is requeued in
   * the list of waiters during a <notify> callback.  If this value ==
   * -1 we are requeued at the end of the list.  Else if it's 0 then
   * we are requeued at the front of the list.  Else if it's > 1 then
   * that indicates the number of waiters to skip over.
   */
  int requeue_position_;

  /// True if we've been initialized yet...
  int initialized_;

  /// The original thread that created this Select_Reactor.
  PPM_thread_t owner_;

  /**
   * True if state has changed during dispatching of
   * <PPM_Event_Handlers>, else false.  This is used to determine
   * whether we need to make another trip through the
   * <Select_Reactor>'s <wait_for_multiple_events> loop.
   */
  int state_changed_;

  /// Controls/access whether the notify handler should renew the
  /// Select_Reactor's token or not.
  int supress_notify_renew (void);
  void supress_notify_renew (int sr);

private:
  /// Determine whether we should renew Select_Reactor's token after handling
  /// the notification message.
  int supress_renew_;


  /// Deny access since member-wise won't work...
  PPM_Select_Reactor_Impl (const PPM_Select_Reactor_Impl &);
  PPM_Select_Reactor_Impl &operator = (const PPM_Select_Reactor_Impl &);
};

#if !defined (PPM_LACKS_INLINE_FUNCTIONS)
#include "./PPM_Select_Reactor_Base.inl"
#endif /* __PPM_INLINE__ */

#endif /* PPM_SELECT_REACTOR_BASE_H */
