/* -*- C++ -*- */
// Select_Reactor_Base.i,v 4.7 2001/08/14 12:16:59 schmidt Exp

#include "./PPM_Reactor.h"

PPM_INLINE
PPM_Event_Tuple::~PPM_Event_Tuple (void)
{
}

//PPM_INLINE
//PPM_Select_Reactor_Notify::~PPM_Select_Reactor_Notify (void)
//{
//}

PPM_INLINE
PPM_Select_Reactor_Handler_Repository::~PPM_Select_Reactor_Handler_Repository (void)
{
}

PPM_INLINE
PPM_Select_Reactor_Handler_Repository_Iterator::~PPM_Select_Reactor_Handler_Repository_Iterator (void)
{
}

PPM_INLINE size_t
PPM_Select_Reactor_Handler_Repository::size (void) const
{
  return this->max_size_;
}

PPM_INLINE
PPM_Event_Tuple::PPM_Event_Tuple (void)
:  handle_ (PPM_INVALID_HANDLE),
   event_handler_ (0)
{
}

PPM_INLINE
PPM_Event_Tuple::PPM_Event_Tuple (PPM_Event_Handler* eh,
				  PPM_HANDLE h)
: handle_ (h),
  event_handler_ (eh)
{
}

PPM_INLINE int
PPM_Event_Tuple::operator== (const PPM_Event_Tuple &rhs) const
{
  return this->handle_ == rhs.handle_;
}

PPM_INLINE int
PPM_Event_Tuple::operator!= (const PPM_Event_Tuple &rhs) const
{
  return !(*this == rhs);
}

PPM_INLINE
PPM_Select_Reactor_Impl::PPM_Select_Reactor_Impl ()
    : handler_rep_ (*this),
      delete_timer_queue_ (0),
      delete_signal_handler_ (0),
      delete_notify_handler_ (0),
      requeue_position_ (-1), // Requeue at end of waiters by default.
      initialized_ (0),
      state_changed_ (0),
      supress_renew_ (0)
{
}

PPM_INLINE int
PPM_Select_Reactor_Impl::purge_pending_notifications (PPM_Event_Handler *eh,
                                                      PPM_Reactor_Mask mask)
{
	return 0;
//  if (this->notify_handler_ == 0)
//    return 0;
//  else
//    return this->notify_handler_->purge_pending_notifications (eh, mask);
}

PPM_INLINE int
PPM_Select_Reactor_Impl::supress_notify_renew (void)
{
  return this->supress_renew_;
}

PPM_INLINE void
PPM_Select_Reactor_Impl::supress_notify_renew (int sr)
{
  this->supress_renew_ = sr;
}
