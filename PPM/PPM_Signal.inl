/* -*- C++ -*- */
// Signal.i,v 4.29 2001/03/04 00:55:30 brunsch Exp

PPM_INLINE
PPM_Sig_Set::PPM_Sig_Set (sigset_t *ss)
  // : sigset_ ()
{
  PPM_TRACE ("PPM_Sig_Set::PPM_Sig_Set");

  if (ss == 0)
    PPM_OS::sigfillset (&this->sigset_);
  else
    // Structure assignment.
    this->sigset_ = *ss;
}

PPM_INLINE
PPM_Sig_Set::PPM_Sig_Set (int fill)
  // : sigset_ ()
{
  PPM_TRACE ("PPM_Sig_Set::PPM_Sig_Set");

  if (fill)
    PPM_OS::sigfillset (&this->sigset_);
  else
    PPM_OS::sigemptyset (&this->sigset_);
}

PPM_INLINE
PPM_Sig_Set::PPM_Sig_Set (PPM_Sig_Set *ss)
  // : sigset_ ()
{
  PPM_TRACE ("PPM_Sig_Set::PPM_Sig_Set");

  if (ss == 0)
    PPM_OS::sigfillset (&this->sigset_);
  else
    this->sigset_ = ss->sigset_;
}

PPM_INLINE
PPM_Sig_Set::~PPM_Sig_Set (void)
{
  PPM_TRACE ("PPM_Sig_Set::~PPM_Sig_Set");
  PPM_OS::sigemptyset (&this->sigset_);
}

PPM_INLINE int
PPM_Sig_Set::empty_set (void)
{
  PPM_TRACE ("PPM_Sig_Set::empty_set");
  return PPM_OS::sigemptyset (&this->sigset_);
}

PPM_INLINE int
PPM_Sig_Set::fill_set (void)
{
  PPM_TRACE ("PPM_Sig_Set::fill_set");
  return PPM_OS::sigfillset (&this->sigset_);
}

PPM_INLINE int
PPM_Sig_Set::sig_add (int signo)
{
  PPM_TRACE ("PPM_Sig_Set::sig_add");
  return PPM_OS::sigaddset (&this->sigset_, signo);
}

PPM_INLINE int
PPM_Sig_Set::sig_del (int signo)
{
  PPM_TRACE ("PPM_Sig_Set::sig_del");
  return PPM_OS::sigdelset (&this->sigset_, signo);
}

PPM_INLINE int
PPM_Sig_Set::is_member (int signo) const
{
  PPM_TRACE ("PPM_Sig_Set::is_member");
  return PPM_OS::sigismember (PPM_const_cast (sigset_t *, &this->sigset_), signo);
}

PPM_INLINE
PPM_Sig_Set::operator sigset_t *(void)
{
  PPM_TRACE ("PPM_Sig_Set::operator sigset_t *");
  return &this->sigset_;
}

PPM_INLINE sigset_t
PPM_Sig_Set::sigset (void) const
{
  PPM_TRACE ("PPM_Sig_Set::sigset");
  return this->sigset_;
}

PPM_INLINE
PPM_Sig_Action::~PPM_Sig_Action (void)
{
  PPM_TRACE ("PPM_Sig_Action::~PPM_Sig_Action");
}

PPM_INLINE int
PPM_Sig_Action::flags (void)
{
  PPM_TRACE ("PPM_Sig_Action::flags");
  return this->sa_.sa_flags;
}

PPM_INLINE void
PPM_Sig_Action::flags (int flags)
{
  PPM_TRACE ("PPM_Sig_Action::flags");
  this->sa_.sa_flags = flags;
}

PPM_INLINE sigset_t *
PPM_Sig_Action::mask (void)
{
  PPM_TRACE ("PPM_Sig_Action::mask");
  return &this->sa_.sa_mask;
}

PPM_INLINE void
PPM_Sig_Action::mask (sigset_t *ss)
{
  PPM_TRACE ("PPM_Sig_Action::mask");
  if (ss != 0)
    this->sa_.sa_mask = *ss; // Structure assignment
}

PPM_INLINE void
PPM_Sig_Action::mask (PPM_Sig_Set &ss)
{
  PPM_TRACE ("PPM_Sig_Action::mask");
  this->sa_.sa_mask = ss.sigset (); // Structure assignment
}

PPM_INLINE PPM_SignalHandler
PPM_Sig_Action::handler (void)
{
  PPM_TRACE ("PPM_Sig_Action::handler");
  return PPM_SignalHandler (this->sa_.sa_handler);
}

PPM_INLINE void
PPM_Sig_Action::handler (PPM_SignalHandler handler)
{
  PPM_TRACE ("PPM_Sig_Action::handler");
  this->sa_.sa_handler = PPM_SignalHandlerV (handler);
}

PPM_INLINE void
PPM_Sig_Action::set (struct sigaction *sa)
{
  PPM_TRACE ("PPM_Sig_Action::set");
  this->sa_ = *sa; // Structure assignment.
}

PPM_INLINE struct sigaction *
PPM_Sig_Action::get (void)
{
  PPM_TRACE ("PPM_Sig_Action::get");
  return &this->sa_;
}
// 
// PPM_INLINE
// PPM_Sig_Action::operator PPM_SIGACTION * ()
// {
//   PPM_TRACE ("PPM_Sig_Action::operator PPM_SIGACTION *");
//   return &this->sa_;
// }

PPM_INLINE
PPM_Sig_Action::PPM_Sig_Action (const PPM_Sig_Action &s)
  // : sa_ ()
{
  PPM_TRACE ("PPM_Sig_Action::PPM_Sig_Action");
  *this = s; // structure copy.
}

PPM_INLINE int
PPM_Sig_Action::register_action (int signum, PPM_Sig_Action *oaction)
{
  PPM_TRACE ("PPM_Sig_Action::register_action");
  struct sigaction *sa = oaction == 0 ? 0 : oaction->get ();

  return PPM_OS::sigaction (signum, &this->sa_, sa);
}

PPM_INLINE int
PPM_Sig_Action::retrieve_action (int signum)
{
  PPM_TRACE ("PPM_Sig_Action::retrieve_action");
  return PPM_OS::sigaction (signum, 0, &this->sa_);
}

PPM_INLINE int
PPM_Sig_Action::restore_action (int signum, PPM_Sig_Action &oaction)
{
  PPM_TRACE ("PPM_Sig_Action::restore_action");
  this->sa_ = *oaction.get (); // Structure assignment
  return PPM_OS::sigaction (signum, &this->sa_, 0);
}

// Block out the signal MASK until the destructor is called.

PPM_INLINE
PPM_Sig_Guard::PPM_Sig_Guard (PPM_Sig_Set *mask)
  : omask_ ()
{
  //PPM_TRACE ("PPM_Sig_Guard::PPM_Sig_Guard");
	if(NULL == default_mask_){
		  PPM_NEW(default_mask_, sigset_t);
      PPM_OS::sigfillset (default_mask_);
	}

#if defined (WIN32)
  PPM_UNUSED_ARG (mask);
#else
  // If MASK is 0 then block all signals!
  if (mask == 0)
    {
#  if defined (PPM_LACKS_PTHREAD_THR_SIGSETMASK)
      PPM_OS::sigprocmask (SIG_BLOCK,
                           default_mask_,
                           (sigset_t *) this->omask_);
#  else
      PPM_OS::thr_sigsetmask (SIG_BLOCK,
                              default_mask_,
                              (sigset_t *) this->omask_);
#  endif /* PPM_LACKS_PTHREAD_THR_SIGSETMASK */
    }
  else
#  if defined (PPM_LACKS_PTHREAD_THR_SIGSETMASK)
    PPM_OS::sigprocmask (SIG_BLOCK,
                         (sigset_t *) *mask,
                         (sigset_t *)
                         this->omask_);
#  else
    PPM_OS::thr_sigsetmask (SIG_BLOCK,
                            (sigset_t *) *mask,
                            (sigset_t *)
                            this->omask_);
#  endif /* PPM_LACKS_PTHREAD_THR_SIGSETMASK */
#endif /* WIN32 */
}

// Restore the signal mask.

PPM_INLINE
PPM_Sig_Guard::~PPM_Sig_Guard (void)
{
  //PPM_TRACE ("PPM_Sig_Guard::~PPM_Sig_Guard");
#if defined (WIN32)
	return;
#else
	#if defined (PPM_LACKS_PTHREAD_THR_SIGSETMASK)
	  PPM_OS::sigprocmask (SIG_SETMASK,
						   (sigset_t *) this->omask_,
						   0);
	#else
	  PPM_OS::thr_sigsetmask (SIG_SETMASK,
							  (sigset_t *) this->omask_,
							  0);
	#endif /* PPM_LACKS_PTHREAD_THR_SIGSETMASK */
#endif /* WIN32 */
}

PPM_INLINE int
PPM_Sig_Handler::in_range (int signum)
{
  PPM_TRACE ("PPM_Sig_Handler::in_range");
  return signum > 0 && signum < PPM_NSIG;
}
