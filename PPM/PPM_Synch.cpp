#include "./PPM_Synch.h"
#include "./PPM_Log_Msg.h"

PPM_Event::PPM_Event (int manual_reset,
                      int initial_state,
                      int type,
                      const char *name,
                      void *arg)
  : removed_ (0)
{
  if (PPM_OS::event_init (&this->handle_,
                          manual_reset,
                          initial_state,
                          type,
                          name,
                          arg) != 0){};
}

PPM_Event::~PPM_Event (void)
{
  this->remove ();
}

int
PPM_Event::remove (void)
{
  int result = 0;
  if (this->removed_ == 0)
    {
      this->removed_ = 1;
      result = PPM_OS::event_destroy (&this->handle_);
    }
  return result;
}

PPM_event_t
PPM_Event::handle (void) const
{
  return this->handle_;
}

void
PPM_Event::handle (PPM_event_t new_handle)
{
  this->handle_ = new_handle;
}

int
PPM_Event::wait (void)
{
  return PPM_OS::event_wait (&this->handle_);
}

int
PPM_Event::wait (const PPM_Time_Value *abstime, int use_absolute_time)
{
  return PPM_OS::event_timedwait (&this->handle_,
                                  (PPM_Time_Value *) abstime,
                                  use_absolute_time);
}

int
PPM_Event::signal (void)
{
  return PPM_OS::event_signal (&this->handle_);
}

int
PPM_Event::pulse (void)
{
  return PPM_OS::event_pulse (&this->handle_);
}

int
PPM_Event::reset (void)
{
  return PPM_OS::event_reset (&this->handle_);
}

void
PPM_Event::dump (void) const
{
}

PPM_Manual_Event::PPM_Manual_Event (int initial_state,
                                    int type,
                                    const char *name,
                                    void *arg)
  : PPM_Event (1,
               initial_state,
               type,
               (name),
               arg)
{
}

void
PPM_Manual_Event::dump (void) const
{
  PPM_Event::dump ();
}

PPM_Auto_Event::PPM_Auto_Event (int initial_state,
                                int type,
                                const char *name,
                                void *arg)
  : PPM_Event (0,
               initial_state,
               type,
               (name),
               arg)
{
}

void
PPM_Auto_Event::dump (void) const
{
  PPM_Event::dump ();
}
