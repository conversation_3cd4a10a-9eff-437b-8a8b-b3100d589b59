#ifndef _PPM_SYNCH_H_
#define _PPM_SYNCH_H_
/**
 * @class PPM_event_t
 *
 * @brief Wrapper for NT events on UNIX.
 */
#include "./PPM_typedef.h"
#include "./PPM_Time_Value.h"
#include "./PPM_OS.h"

/**
 * @class PPM_Event
 *
 * @brief A wrapper around the Win32 event locking mechanism.
 *
 * Portable implementation of an Event mechanism, which is native to
 * Win32, but must be emulated on UNIX.  All platforms support
 * process-scope locking support.  However, only Win32 platforms
 * support global naming and system-scope locking support.
 */
class PPM_Event
{
public:
  /// Constructor that creates event.
  PPM_Event (int manual_reset = 0,
             int initial_state = 0,
             int type = 0,
             const char *name = 0,
             void *arg = 0);

  /// Implicitly destroy the event variable.
  ~PPM_Event (void);

  /**
   * Explicitly destroy the event variable.  Note that only one thread
   * should call this method since it doesn't protect against race
   * conditions.
   */
  int remove (void);

  /// Underlying handle to event.
  PPM_event_t handle (void) const;

  /**
   * Set the underlying handle to event. Note that this method assumes
   * ownership of the <handle> and will close it down in <remove>.  If
   * you want the <handle> to stay open when <remove> is called make
   * sure to call <dup> on the <handle> before closing it.  You are
   * responsible for the closing the existing <handle> before
   * overwriting it.
   */
  void handle (PPM_event_t new_handle);

  /**
   * if MANUAL reset
   *    sleep till the event becomes signaled
   *    event remains signaled after wait() completes.
   * else AUTO reset
   *    sleep till the event becomes signaled
   *    event resets wait() completes.
   */
  int wait (void);

  /// Same as wait() above, but this one can be timed
  /// <abstime> is absolute time-of-day if if <use_absolute_time>
  /// is non-0, else it is relative time.
  int wait (const PPM_Time_Value *abstime,
            int use_absolute_time = 1);

  /**
   * if MANUAL reset
   *    wake up all waiting threads
   *    set to signaled state
   * else AUTO reset
   *    if no thread is waiting, set to signaled state
   *    if thread(s) are waiting, wake up one waiting thread and
   *    reset event
   */
  int signal (void);

  /**
   * if MANUAL reset
   *    wakeup all waiting threads and
   *    reset event
   * else AUTO reset
   *    wakeup one waiting thread (if present) and
   *    reset event
   */
  int pulse (void);

  /// Set to nonsignaled state.
  int reset (void);

  /// Dump the state of an object.
  void dump (void) const;

protected:
  /// The underlying handle.
  PPM_event_t handle_;

  /// Keeps track of whether <remove> has been called yet to avoid
  /// multiple <remove> calls, e.g., explicitly and implicitly in the
  /// destructor.  This flag isn't protected by a lock, so make sure
  /// that you don't have multiple threads simultaneously calling
  /// <remove> on the same object, which is a bad idea anyway...
  int removed_;

private:
  // = Prevent copying.
  PPM_Event (const PPM_Event& event);
  const PPM_Event &operator= (const PPM_Event &rhs);
};

/**
 * @class PPM_Manual_Event
 *
 * @brief Manual Events.
 *
 * Specialization of Event mechanism which wakes up all waiting
 * thread on <signal>.  All platforms support process-scope locking
 * support.  However, only Win32 platforms support global naming and
 * system-scope locking support.
 */
class PPM_Manual_Event : public PPM_Event
{
public:
  /// constructor which will create manual event
  PPM_Manual_Event (int initial_state = 0,
                    int type = 0,
                    const char *name = 0,
                    void *arg = 0);

  /// Default dtor.
  ~PPM_Manual_Event (void);

  /// Dump the state of an object.
  void dump (void) const;

};

/**
 * @class PPM_Auto_Event
 *
 * @brief Auto Events.
 *
 * Specialization of Event mechanism which wakes up one waiting
 * thread on <signal>.  All platforms support process-scope locking
 * support.  However, only Win32 platforms support global naming and
 * system-scope locking support.
 */
class PPM_Auto_Event : public PPM_Event
{
public:
  /// constructor which will create auto event
  PPM_Auto_Event (int initial_state = 0,
                  int type = 0,
                  const char *name = 0,
                  void *arg = 0);

  /// Default dtor.
  ~PPM_Auto_Event (void);

  /// Dump the state of an object.
  void dump (void) const;

};


PPM_INLINE
PPM_Manual_Event::~PPM_Manual_Event (void)
{
}

PPM_INLINE
PPM_Auto_Event::~PPM_Auto_Event (void)
{
}
#endif
