#include "./PPM_Thread_Adapter.h"
#include "./PPM_Thread_Manager.h"

#if defined (PPM_LACKS_INLINE_FUNCTIONS)
#   include "./PPM_Thread_Adapter.inl"
# endif /* PPM_HAS_INLINED_OSCALLS */


PPM_Base_Thread_Adapter::PPM_Base_Thread_Adapter (
     PPM_THR_VOID_FUNC user_func,
     void *arg,
     PPM_THR_C_VOID_FUNC entry_point
   )
  : user_func_ (user_func)
  , arg_ (arg)
  , entry_point_ (entry_point)
{
}

PPM_Base_Thread_Adapter::~PPM_Base_Thread_Adapter (void)
{
}

// Run the thread entry point for the <PPM_Thread_Adapter>.  This must
// be an extern "C" to make certain compilers happy...

extern "C" PPM_THR_FUNC_VOID_RETURN
PPM_thread_adapter (void *args)
{
  PPM_Base_Thread_Adapter *thread_args =
    PPM_static_cast (PPM_Base_Thread_Adapter *, args);

  // Invoke the user-supplied function with the args.
  PPM_THR_FUNC_VOID_RETURN status = thread_args->invoke ();

  return status;
}

PPM_Thread_Adapter::PPM_Thread_Adapter (PPM_THR_VOID_FUNC user_func,
                                        void *arg,
                                        PPM_THR_C_VOID_FUNC entry_point,
                                        PPM_Thread_Manager *tm
                                        )
  : PPM_Base_Thread_Adapter (
        user_func
        , arg
        , entry_point
        )
  , thr_mgr_ (tm)
{
	if(0 == thr_mgr_)
	{
		thr_mgr_ = PPM_Thread_Manager::instance();
	}
}

PPM_Thread_Adapter::~PPM_Thread_Adapter (void)
{
}

PPM_THR_FUNC_VOID_RETURN
PPM_Thread_Adapter::invoke (void)
{
  //这里可以加一些回调函数
  return this->invoke_i ();
}

PPM_THR_FUNC_VOID_RETURN
PPM_Thread_Adapter::invoke_i (void)
{
  // Extract the arguments.
  PPM_THR_VOID_FUNC func = PPM_reinterpret_cast (PPM_THR_VOID_FUNC,
                                            this->user_func_);
  void *arg = this->arg_;

  // Delete ourselves since we don't need <this> anymore.  Make sure
  // not to access <this> anywhere below this point.
  delete this;

  PPM_THR_FUNC_VOID_RETURN status = 0;

  PPM_SEH_TRY
    {
      PPM_SEH_TRY
        {
              status = (*func) (arg);
        }
   }

  PPM_SEH_FINALLY
    {
      // If we changed this to 1, change the respective if in
      // Task::svc_run to 0.

      // Exit the thread.  Allow CWinThread-destructor to be invoked
      // from AfxEndThread.  _endthreadex will be called from
      // AfxEndThread so don't exit the thread now if we are running
      // an MFC thread.

//      PPM_ENDTHREADEX (status);
    }

  PPM_Thread_Manager::instance()->clear_selfthr();
  return status;
}


PPM_OS_Thread_Adapter::PPM_OS_Thread_Adapter (
     PPM_THR_VOID_FUNC user_func
     , void *arg
     , PPM_THR_C_VOID_FUNC entry_point
     )
  : PPM_Base_Thread_Adapter (user_func, arg, entry_point)
{
}

PPM_OS_Thread_Adapter::~PPM_OS_Thread_Adapter (void)
{
}

PPM_THR_FUNC_VOID_RETURN
PPM_OS_Thread_Adapter::invoke (void)
{
  // Inherit the logging features if the parent thread has an
  // PPM_Log_Msg instance in thread-specific storage.
//   this->inherit_log_msg ();

  // Extract the arguments.
  PPM_THR_VOID_FUNC_INTERNAL func =
    PPM_reinterpret_cast (PPM_THR_VOID_FUNC_INTERNAL, this->user_func_);
  void *arg = this->arg_;

  // Delete ourselves since we don't need <this> anymore.  Make sure
  // not to access <this> anywhere below this point.
  delete this;


  PPM_THR_FUNC_VOID_RETURN status = 0;

  PPM_SEH_TRY
    {
      PPM_SEH_TRY
        {
//          PPM_Thread_Hook *hook =
//            PPM_OS_Object_Manager::thread_hook ();
//
//          if (hook)
//            // Invoke the start hook to give the user a chance to
//            // perform some initialization processing before the
//            // <func> is invoked.
//            status = hook->start (PPM_reinterpret_cast (PPM_THR_FUNC, func),
//                                  arg);
//          else
            {
              // Call thread entry point.
              status = (*func) (arg);
            }
        }
    }

  PPM_SEH_FINALLY
    {
      // Exit the thread.  Allow CWinThread-destructor to be invoked
      // from AfxEndThread.  _endthreadex will be called from
      // AfxEndThread so don't exit the thread now if we are running
      // an MFC thread.
//      PPM_ENDTHREADEX (status);

    }
  PPM_Thread_Manager::instance()->clear_selfthr();

  return status;
}

