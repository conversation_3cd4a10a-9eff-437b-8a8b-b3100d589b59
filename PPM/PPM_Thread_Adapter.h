#ifndef PPM_BASE_THREAD_ADAPTER_H
#define PPM_BASE_THREAD_ADAPTER_H

#if !defined (PPM_LACKS_PRAGMA_ONCE)
# pragma once
#endif /* PPM_LACKS_PRAGMA_ONCE */

#include "./PPM_typedef.h"


extern "C" PPM_THR_FUNC_VOID_RETURN PPM_thread_adapter (void *args);


class PPM_OS_Thread_Descriptor
{
public:
  /// Get the thread creation flags.
  long flags (void) const;

protected:
  /// For use by PPM_Thread_Descriptor.
  PPM_OS_Thread_Descriptor (long flags = 0);

  /**
   * Keeps track of whether this thread was created "detached" or not.
   * If a thread is *not* created detached then if someone calls
   * <PPM_Thread_Manager::wait>, we need to join with that thread (and
   * close down the handle).
   */
  long flags_;
};

/**
 * @class PPM_Base_Thread_Adapter
 *
 * @brief Base class for all the Thread_Adapters.
 *
 * Converts a C++ function into a function that can be
 * called from a thread creation routine
 * (e.g., pthread_create() or _beginthreadex()) that expects an
 * extern "C" entry point.  This class also makes it possible to
 * transparently provide hooks to register a thread with an
 * PPM_Thread_Manager.
 * This class is used in PPM_OS::thr_create().  In general, the
 * thread that creates an object of this class is different from
 * the thread that calls @c invoke() on this object.  Therefore,
 * the @c invoke() method is responsible for deleting itself.
 */
class PPM_Base_Thread_Adapter
{
public:
  /// Constructor.
  PPM_Base_Thread_Adapter (PPM_THR_VOID_FUNC user_func,
                           void *arg,
                           PPM_THR_C_VOID_FUNC entry_point = (PPM_THR_C_VOID_FUNC) PPM_thread_adapter
                      );

  /// Virtual method invoked by the thread entry point.
  virtual PPM_THR_FUNC_VOID_RETURN invoke (void) = 0;

  /// Accessor for the C entry point function to the OS thread creation
  /// routine.
  PPM_THR_C_VOID_FUNC entry_point (void);

  /// Invoke the close_log_msg_hook, if it is present
  static void close_log_msg (void);

  /// Invoke the sync_log_msg_hook, if it is present
  static void sync_log_msg (const char *prog_name);

protected:
  /// Destructor, making it private ensures that objects of this class
  /// are allocated on the heap.
  virtual ~PPM_Base_Thread_Adapter (void);


protected:
  /// Thread startup function passed in by the user (C++ linkage).
  PPM_THR_VOID_FUNC user_func_;

  /// Argument to thread startup function.
  void *arg_;

  /// Entry point to the underlying OS thread creation call (C
  /// linkage).
  PPM_THR_C_VOID_FUNC entry_point_;

  /**
   * Optional thread descriptor.  Passing this pointer in will force
   * the spawned thread to cache this location in <Log_Msg> and wait
   * until <Thread_Manager> fills in all information in thread
   * descriptor.
   */
  PPM_OS_Thread_Descriptor *thr_desc_;

};

// Forward decl.
class PPM_Thread_Manager;

/**
 * @class PPM_Thread_Adapter
 *
 * @brief Converts a C++ function into a function that
 * can be called from a thread creation routine
 * (e.g., pthread_create() or _beginthreadex()) that expects an
 * extern "C" entry point.  This class also makes it possible to
 * transparently provide hooks to register a thread with an
 * PPM_Thread_Manager.
 *
 * This class is used in PPM_OS::thr_create().  In general, the
 * thread that creates an object of this class is different from
 * the thread that calls @c invoke() on this object.  Therefore,
 * the @c invoke() method is responsible for deleting itself.
 */
class PPM_Thread_Adapter : public PPM_Base_Thread_Adapter
{
public:
  /// Constructor.
  PPM_Thread_Adapter (PPM_THR_VOID_FUNC user_func,
                      void *arg,
                      PPM_THR_C_VOID_FUNC entry_point = (PPM_THR_C_VOID_FUNC) PPM_thread_adapter,
                      PPM_Thread_Manager *thr_mgr = 0
                      );

  /**
   * Execute the <user_func_> with the <arg>.  This function deletes
   * <this>, thereby rendering the object useless after the call
   * returns.
   */
  virtual PPM_THR_FUNC_VOID_RETURN invoke (void);

  /// Accessor for the optional <Thread_Manager>.
  PPM_Thread_Manager *thr_mgr (void);

private:
  /// Ensure that this object must be allocated on the heap.
  ~PPM_Thread_Adapter (void);

  /// Called by invoke, mainly here to separate the SEH stuff because
  /// SEH on Win32 doesn't compile with local vars with destructors.
  virtual PPM_THR_FUNC_VOID_RETURN invoke_i (void);

private:
  /// Optional thread manager.
  PPM_Thread_Manager *thr_mgr_;

};


/**
 线程适配器，主要是用来在线程和具体执行函数之间的适配，可以在执行函数和
 线程管理器之间加入一些其他的操作，例如在线程完毕后删除管理器中的list中
 的相应数据，线程完毕后关闭handle，以防止handle无限增加，用户不用理会handle
 的关闭等等
 */
class  PPM_OS_Thread_Adapter : public PPM_Base_Thread_Adapter
{
public:
  /// Constructor.
  PPM_OS_Thread_Adapter (PPM_THR_VOID_FUNC user_func,
                         void *arg,
                         PPM_THR_C_VOID_FUNC entry_point = (PPM_THR_C_VOID_FUNC) PPM_thread_adapter
                         );

  /**
   * Execute the @a user_func_ with the @a arg.  This function deletes
   * @c this, thereby rendering the object useless after the call
   * returns.
   */
  virtual PPM_THR_FUNC_VOID_RETURN invoke (void);

private:
  /// Ensure that this object must be allocated on the heap.
  ~PPM_OS_Thread_Adapter (void);

private:
  /// Friend declaration to avoid compiler warning:  only defines a private
  /// destructor and has no friends.
//  friend class PPM_Thread_Adapter_Has_Private_Destructor;
};


#if !defined (PPM_LACKS_INLINE_FUNCTIONS)
#   include "./PPM_Thread_Adapter.inl"
# endif /* PPM_HAS_INLINED_OSCALLS */

#endif /* PPM_BASE_THREAD_ADAPTER_H */
