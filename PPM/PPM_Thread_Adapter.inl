// Thread_Adapter.inl,v 4.3 2000/11/13 01:04:57 brunsch Exp
// Base_Thread_Adapter.inl,v 4.3 2001/11/23 16:37:09 schmidt Exp

PPM_INLINE long
PPM_OS_Thread_Descriptor::flags (void) const
{
  return flags_;
}

PPM_INLINE
PPM_OS_Thread_Descriptor::PPM_OS_Thread_Descriptor (long flags)
  : flags_ (flags)
{
}

PPM_INLINE PPM_THR_C_VOID_FUNC
PPM_Base_Thread_Adapter::entry_point (void)
{
  return this->entry_point_;
}


PPM_INLINE PPM_Thread_Manager *
PPM_Thread_Adapter::thr_mgr (void)
{
  return this->thr_mgr_;
}
