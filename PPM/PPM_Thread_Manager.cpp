#include "PPM_Thread_Manager.h"
#include "PPM_Thread_Adapter.h"
#include "PPM_OS_Error.h"
#include "PPM_Log_Msg.h"

#if defined (PPM_LACKS_INLINE_FUNCTIONS)
#include "./PPM_Thread_Manager.inl"
#endif /* __PPM_INLINE__ */

// Process-wide PPM_Reactor.
PPM_Thread_Manager *PPM_Thread_Manager::thr_mgr_ = 0;

PPM_Thread_Manager *
PPM_Thread_Manager::instance (void)
{
  PPM_TRACE ("PPM_Thread_Manager::instance");

  if (PPM_Thread_Manager::thr_mgr_ == 0)
    {
      PPM_NEW_RETURN (PPM_Thread_Manager::thr_mgr_,
                      PPM_Thread_Manager,
                      0);
    }
  return PPM_Thread_Manager::thr_mgr_;
}

void PPM_Thread_Manager::exitinstance (void)
{
    if (PPM_Thread_Manager::thr_mgr_ != NULL)
    {
        delete PPM_Thread_Manager::thr_mgr_;
        PPM_Thread_Manager::thr_mgr_ = NULL;
    }
}

// Initialize the synchronization variables.

PPM_Thread_Manager::PPM_Thread_Manager ()
{
}

// Close up and release all resources.

int
PPM_Thread_Manager::close ()
{
  PPM_TRACE ("PPM_Thread_Manager::close");
   CStdAutoLock tm_autolock(&lock_);

  std::list<PPM_Thread_Info>::iterator itr;
  while(thr_list_.begin() != thr_list_.end()){
	  itr = thr_list_.begin();
	  PPM_DEBUG((LM_ERROR,"terminate_thr! when destory PPM_Thread_Manager %m\n"));
	  PPM_hthread_t hthr_t_  = (*itr).hthr_t_;
	  terminate_thr(hthr_t_);
//	  thr_list_.pop_front();
  }
  this->thr_list_.clear();

  return 0;
}

PPM_Thread_Manager::~PPM_Thread_Manager (void)
{
  PPM_TRACE ("PPM_Thread_Manager::~PPM_Thread_Manager");
  this->close ();
}

int
PPM_Thread_Manager::spawn (PPM_THR_VOID_FUNC func,
                           PPM_hthread_t *t_handle,
                           void *args,
                           long flags,
                           PPM_thread_t *t_id,
                           long priority,
                           void *stack,
                           size_t stack_size)
{
  CStdAutoLock tm_lock(&lock_);
  PPM_TRACE ("PPM_Thread_Manager::spawn");
  if(t_handle == 0){
	  return -1;
  }
  if (this->spawn_i (func, args, flags, t_id, t_handle,
	  priority, stack, stack_size) == -1){
    return -1;
  }

  return 1;
}

// Call the appropriate OS routine to spawn a thread.  Should *not* be
// called with the lock_ held...

int
PPM_Thread_Manager::spawn_i (PPM_THR_VOID_FUNC func,
                             void *args,
                             long flags,
                             PPM_thread_t *t_id,
                             PPM_hthread_t *t_handle,
                             long priority,
                             void *stack,
                             size_t stack_size)
{
  CStdAutoLock tm_lock(&lock_);

  PPM_thread_t thr_id;
  if (t_id == 0)
    t_id = &thr_id;

  // Acquire the <sync_> lock to block the spawned thread from
  // removing this Thread Descriptor before it gets put into our
  // thread table.

  int result = PPM_Thread::spawn (func,
                                  args,
                                  flags,
                                  t_id,
                                  t_handle,
                                  priority,
                                  stack,
                                  stack_size);

  if (result != 0)
    {
      // _Don't_ clobber errno here!  result is either 0 or -1, and
      // PPM_OS::thr_create () already set errno!  D. Levine 28 Mar 1997
      // errno = result;
      PPM_Errno_Guard guard (errno);     // Lock release may smash errno
      return -1;
    }
  else
    {
      // append_thr also put the <new_thr_desc> into Thread_Manager's
      // double-linked list.  Only after this point, can we manipulate
      // double-linked list from a spawned thread's context.
      return this->append_thr (*t_id,
                               *t_handle,
                               PPM_THR_SPAWNED,
                               flags);
    }
}


// Append a thread into the pool (does not check for duplicates).
// Must be called with locks held.

int
PPM_Thread_Manager::append_thr (PPM_thread_t t_id,
                                PPM_hthread_t t_handle,
                                PPM_UINT32 thr_state,
                                long flags)
{
  CStdAutoLock tm_autolock(&lock_);

  PPM_Thread_Info tm_info;
  tm_info.hthr_t_ = t_handle;
  tm_info.thr_t_ = t_id;
  tm_info.thr_state_ = thr_state;
  this->thr_list_.push_back(tm_info);

  return 0;
}
// remove a thread into the pool (does not check for duplicates).
// Must be called with locks held.

BOOL
PPM_Thread_Manager::remove_thr(PPM_hthread_t t_handle)
{
  CStdAutoLock tm_autolock(&lock_);

  std::list<PPM_Thread_Info>::iterator itr = thr_list_.begin();
  while(itr != thr_list_.end()){
	  if(t_handle == itr->hthr_t_){
		  break;
	  }
	  itr++;
  }
  if (itr != thr_list_.end())
  {
     PPM_Thread_Info info = *itr;
	 thr_list_.remove(info);
	 return TRUE;
  }

  return FALSE;
}
BOOL
PPM_Thread_Manager::isthr_exists(PPM_hthread_t t_handle)
{
  CStdAutoLock tm_autolock(&lock_);

  std::list<PPM_Thread_Info>::iterator itr;
  itr = thr_list_.begin();
  while(itr != thr_list_.end()){
	  if(t_handle == itr->hthr_t_){
		  return TRUE;
	  }
	  itr++;
  }

  return FALSE;
}

/**
强行杀掉一个线程,会有内存泄露，不到万不得已，不调用
*/  
int
PPM_Thread_Manager::terminate_thr(PPM_hthread_t thr_handle,DWORD flag)
{
  CStdAutoLock tm_autolock(&lock_);

  return remove_thr(thr_handle);
//	PPM_thread_t i = ::pthread_self();
//	printf("mainthread:%d,childThread%d\n",i,thr_handle);
//	return ::pthread_kill(thr_handle,SIGQUIT);

  return -1;
}

BOOL
PPM_Thread_Manager::clear_selfthr()
{
  CStdAutoLock tm_autolock(&lock_);
  PPM_hthread_t tm_ht = thr_hself();
  PPM_OS::close(tm_ht);
  return remove_thr(tm_ht);
}
PPM_hthread_t 
PPM_Thread_Manager::findhandlebyid(PPM_thread_t thr_id)
{
  CStdAutoLock tm_autolock(&lock_);

	std::list<PPM_Thread_Info>::iterator itr;
//	int i = thr_list_.size();
  itr = thr_list_.begin();
  while(itr != thr_list_.end()){
	  if(thr_id == itr->thr_t_){
		  return itr->hthr_t_;
	  }
	  itr++;
  }
  return 0;
}

/*
获取自身线程的句柄
*/
PPM_hthread_t
PPM_Thread_Manager::thr_hself (void)
{
  CStdAutoLock tm_autolock(&lock_);
  PPM_TRACE ("PPM_Thread_Manager::thr_self");
  PPM_thread_t tm_t = PPM_Thread::self ();
  return findhandlebyid(tm_t);
}

// Resume a single thread.

int
PPM_Thread_Manager::resume (PPM_hthread_t thr_handle)
{
  CStdAutoLock tm_autolock(&lock_);
	return PPM_Thread::resume(thr_handle);
}

/**
线程函数入口
*/
PPM_THR_FUNC_VOID_RETURN PPM_ThreadDeal_Base::CommMain(void)
{
	m_bRunFlag_ = 1;
	m_bIsStop_ = 0;
	
	while(m_bRunFlag_)
	{
		SetThreadFlag(1);
		Main();
	}
	m_bIsStop_ = 1;
	
	return 0;
}
/**
停止线程函数
停止线程时等待30秒，如果30秒内还没有结束就强行杀掉线程
*/
void
PPM_ThreadDeal_Base::StopThread()
{
	//判断线程是否启动
	if(1 == m_bIsStop_ ){
		return;
	}
    BOOL isTrue = FALSE;
	//停掉线程
	m_bRunFlag_ = 0;
	
	
	//一直等待线程结束,等30秒
	for(int i = 0; i < (30 * 100) ;i++){
		if(1 == m_bIsStop_){
			isTrue = TRUE;
			break;
		}
		else{
			PPM_OS::sleep(10);
		}
	}
	if(FALSE == isTrue){
		if((0 == m_bIsStop_)){
			PPM_Thread_Manager::instance()->terminate_thr(m_Thr_Handle);
			PPM_DEBUG((LM_ERROR,"强行退出线程%s!\n",this->m_name_));
		}
	}
	return;
}

PPM_THR_FUNC_VOID_RETURN CommThread(void * argus)
{
  PPM_ThreadDeal_Base* pThreadDeal = (PPM_ThreadDeal_Base*)argus;
  
  return pThreadDeal->CommMain();
};

/**
启动线程函数
*/
void
PPM_ThreadDeal_Base::StartThread(long lThreadPriority)
{
	if(1 == m_bRunFlag_){
		return;
	}
	PPM_Thread_Manager* thrmng = PPM_Thread_Manager::instance();
	
	//服务的侦听线程
	//(PPM_THR_FUNC)CommThread 是线程的主体，所有继承于PPM_ThreadDeal_Base，都必然具有该方法。
	//CommThread用于死循环执行线程的Main，Main可以给重载。
	//当线程CommThread给创建后，会不断的执行Main循环。Main是业务执行的主体。
	thrmng->spawn((PPM_THR_VOID_FUNC)CommThread, &m_Thr_Handle, this,
		THR_NEW_LWP | THR_JOINABLE | THR_DETACHED, 0, lThreadPriority);
}



