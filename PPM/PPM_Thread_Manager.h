#ifndef PPM_THREAD_MANAGER_H
#define PPM_THREAD_MANAGER_H

#include <list>
#include "PPM_Thread.h"
#include "StdLock.h"

class PPM_Thread_Info
{
public:
	PPM_thread_t thr_t_;

	PPM_hthread_t hthr_t_;

	UINT32 thr_state_ ;

  /// True if @a tv1 == @a tv2.
  friend  int operator == (const PPM_Thread_Info &tv1,
                                        const PPM_Thread_Info &tv2);

};
// True if tv1 == tv2.

PPM_INLINE int
operator == (const PPM_Thread_Info &tv1,
             const PPM_Thread_Info &tv2)
{
  // PPM_OS_TRACE ("operator ==");
  return tv1.hthr_t_ == tv2.hthr_t_
    && tv1.thr_state_ == tv2.thr_state_
	&& tv1.thr_t_ == tv2.thr_t_;
}
/*
包装了线程管理类，提供给外部的主要接口有：
instance：初始化函数，单实例
spawn:新创建一个线程
terminate_thr:强行删除指定的线程
resume:运行挂起的线程，在初始化时传入悬挂参数时的情况使用
*/


class PPM_Thread_Manager
{
	friend class PPM_Thread_Adapter;
	friend class PPM_OS_Thread_Adapter;
public:

  // These are the various states a thread managed by the
  // <Thread_Manager> can be in.
  enum
  {
    /// Uninitialized.
    PPM_THR_IDLE = 0x00000000,

    /// Created but not yet running.
    PPM_THR_SPAWNED = 0x00000001,

    /// Thread is active (naturally, we don't know if it's actually
    /// *running* because we aren't the scheduler...).
    PPM_THR_RUNNING = 0x00000002,

    /// Thread is suspended.
    PPM_THR_SUSPENDED = 0x00000004,

    /// Thread has been cancelled (which is an indiction that it needs to
    /// terminate...).
    PPM_THR_CANCELLED = 0x00000008,

    /// Thread has shutdown, but the slot in the thread manager hasn't
    /// been reclaimed yet.
    PPM_THR_TERMINATED = 0x00000010,

    /// Join operation has been invoked on the thread by thread manager.
    PPM_THR_JOINING = 0x10000000
  };

  // = Initialization and termination methods.
  /**
   * @brief Initialization and termination methods.
   *
   * Internally, PPM_Thread_Manager keeps a freelist for caching
   * resources it uses to keep track of managed threads (not the
   * threads themselves.)  @a prealloc, @a lwm, @a inc, @hwm
   * determine the initial size, the low water mark, increment step,
   * and high water mark of the freelist.
   *
   * @sa PPM_Free_List
   */
  PPM_Thread_Manager ();
  virtual ~PPM_Thread_Manager (void);

  /// Get pointer to a process-wide <PPM_Thread_Manager>.
  //初始化单实例对象
  static PPM_Thread_Manager *instance (void);
  static void exitinstance (void);




  /**
   创建一个新的线程，用来执行函数func,参数如下：
   func是指向void* 的函数，
   t_handle是一个指向PPM_hthread_t的指针,指针在外面需初始化
   flags 表示初始化线程的状态，在windows中0表示运行,THR_SUSPENDED表示先挂起
   PPM_thread_t 是一个指向线程id的指针，用法同t_handle
   priority表示线程的优先级
   stack:Start address of routine that begins execution of new thread
   stack_size:Stack size for new thread or 0
   */
  int spawn (PPM_THR_VOID_FUNC func,
             PPM_hthread_t *t_handle,
             void *args = 0,
             long flags = THR_NEW_LWP | THR_JOINABLE,
             PPM_thread_t * t_id= 0,
             long priority = PPM_DEFAULT_THREAD_PRIORITY,
             void *stack = 0,
             size_t stack_size = 3072*1024);

  /**
   强行关闭一个线程，传入为线程的句柄
   */
  int terminate_thr(PPM_hthread_t thr_handle,DWORD flag = 0);

    /// Resume a single thread.传入为线程的句柄
  int resume (PPM_hthread_t thr_handle);

protected:
  // = Accessors for PPM_Thread_Descriptors.
  /**
   * Return the unique ID of the thread.  This is not strictly
   * necessary (because a thread can always just call
   * <ACE_Thread::self>).  However, we put it here to be complete.
   */
  PPM_thread_t thr_self (void);
  PPM_hthread_t thr_hself (void);
  //调用此函数的线程清除线程管理器中的list中的自身句柄
  BOOL  clear_selfthr();
  /**
	线程管理器析购中调用
   */
  int close (void);

  //通过thrid查找handle
  PPM_hthread_t findhandlebyid(PPM_thread_t thr_id);

  /// Create a new thread (must be called with locks held).
  virtual int spawn_i (PPM_THR_VOID_FUNC func,
                       void *args,
                       long flags,
                       PPM_thread_t * t_id= 0,
                       PPM_hthread_t *t_handle = 0,
                       long priority = PPM_DEFAULT_THREAD_PRIORITY,
                       void *stack = 0,
                       size_t stack_size = 0);


  /// Append a thread in the table (adds at the end, growing the table
  /// if necessary).
  int append_thr (PPM_thread_t t_id, PPM_hthread_t t_handle,
                  PPM_UINT32 thr_state,
                  long flags = 0);
  /// Append a thread in the table (adds at the end, growing the table
  /// if necessary).
  BOOL remove_thr (PPM_hthread_t t_handle);
  

  BOOL isthr_exists(PPM_hthread_t t_handle);
  /**
   * Keeping a list of thread descriptors within the thread manager.
   * Double-linked list enables us to cache the entries in TSS
   * and adding/removing thread descriptor entries without
   * affecting other thread's descriptor entries.
   */
  std::list<PPM_Thread_Info> thr_list_;

  /// Serialize access to the <zero_cond_>.
  CStdLock lock_;

private:
  static PPM_Thread_Manager *thr_mgr_;

};

/**
如果线程需要监控，请将线程的附着类从此类继承

此类主要包装了启动线程，停止线程，还提供了与自身监控相关的一些函数和成员变量变量
的调用
*/
class PPM_ThreadDeal_Base
{
public:
	PPM_ThreadDeal_Base(){
		m_ThreadFlag_ = 1;
		m_Resetable_ = 0;
		m_bIsStop_ = 1;
		m_bRunFlag_ = 0;
	};
	virtual ~PPM_ThreadDeal_Base(){
		StopThread();    
	}
	///设置线程运行标示
	void SetThreadFlag(BYTE flag){
		m_ThreadFlag_ = flag;
	};



	///获取线程运行标示
	BYTE GetThreadFlag(){
		return m_ThreadFlag_;
	}

	///复位线程
	virtual BOOL ResetThread() = 0;

	///线程处理函数
	virtual BOOL Main() = 0;

	///是否线程在出现问题时可复位
	BYTE IsResetable(){
		return m_Resetable_;
	};

	///设置线程是否在出现问题时可复位
	void SetResetFlag(BYTE flag){
		m_Resetable_ = flag;
	};

	///设置线程的名称
	void SetThreadName(const char* name){
		PPM_OS::strcpy(m_name_,name);
	};

	///对象名称，用于写日志
	char m_name_[50];

	///启动线程
	void StartThread(long lThreadPriority = PPM_DEFAULT_THREAD_PRIORITY);
	///停止线程
	virtual void StopThread();

	///设置线程起停标志
	void SetRunningFlag(BYTE flag){
		m_bRunFlag_ = flag;
	}
	///获取线程起停标志
	BYTE GetRunningFlag(){
		return m_bRunFlag_ ;
	}
	///控制线程运行的标志
	BYTE m_bRunFlag_;
	BYTE m_bIsStop_;

protected:
	friend PPM_THR_FUNC_VOID_RETURN CommThread(void * argus);

private:
	///线程运行的函数，是一个死循环
	virtual PPM_THR_FUNC_VOID_RETURN CommMain(void);

	///线程句柄
	PPM_hthread_t m_Thr_Handle;

	///线程正常运行标志
	BYTE m_ThreadFlag_;

	///是否可以复位
	BYTE m_Resetable_;


};


#if !defined (PPM_LACKS_INLINE_FUNCTIONS)
#include "./PPM_Thread_Manager.inl"
#endif /* __PPM_INLINE__ */

#endif /* PPM_THREAD_MANAGER_H */
