#include "PPM_Time_Value.h"

#include "PPM_OS.h"
#if defined (PPM_LACKS_INLINE_FUNCTIONS)
# include "PPM_Time_Value.inl"
#endif


// Static constant representing `zero-time'.
// Note: this object requires static construction.
const PPM_Time_Value PPM_Time_Value::zero;

// Constant for maximum time representable.  Note that this time
// is not intended for use with select () or other calls that may
// have *their own* implementation-specific maximum time representations.
// Its primary use is in time computations such as those used by the
// dynamic subpriority strategies in the PPM_Dynamic_Message_Queue class.
// Note: this object requires static construction.
const PPM_Time_Value PPM_Time_Value::max_time (LONG_MAX,
                                               PPM_ONE_SECOND_IN_USECS - 1);


// Increment microseconds (the only reason this is here is to allow
// the use of PPM_Atomic_Op with PPM_Time_Value).

PPM_Time_Value
PPM_Time_Value::operator ++ (int)
{
  // PPM_OS_TRACE ("PPM_Time_Value::operator ++ (int)");
  PPM_Time_Value tv (*this);
  ++*this;
  return tv;
}
//秒数加一
PPM_Time_Value &
PPM_Time_Value::operator ++ (void)
{
  // PPM_OS_TRACE ("PPM_Time_Value::operator ++ (void)");
  this->usec (this->usec () + 1);
  this->normalize ();
  return *this;
}

// Decrement microseconds (the only reason this is here is / to allow
// the use of PPM_Atomic_Op with PPM_Time_Value).
PPM_Time_Value
PPM_Time_Value::operator -- (int)
{
  // PPM_OS_TRACE ("PPM_Time_Value::operator -- (int)");
  PPM_Time_Value tv (*this);
  --*this;
  return tv;
}

//秒数减一
PPM_Time_Value &
PPM_Time_Value::operator -- (void)
{
  // PPM_OS_TRACE ("PPM_Time_Value::operator -- (void)");
  this->usec (this->usec () - 1);
  this->normalize ();
  return *this;
}

// Static constant to remove time skew between FILETIME and POSIX
// time.  POSIX and Win32 use different epochs (Jan. 1, 1970 v.s.
// Jan. 1, 1601).  The following constant defines the difference
// in 100ns ticks.
//  由于win32时间从1601年开始，而posix时间从1970年开始，所以需要将其间的时间差消除
// In the beginning (Jan. 1, 1601), there was no time and no computer.
// And Bill said: "Let there be time," and there was time....

void
PPM_Time_Value::dump (void) const
{
  // PPM_OS_TRACE ("PPM_Time_Value::dump");
}

void
PPM_Time_Value::normalize (void)
{
  // // PPM_OS_TRACE ("PPM_Time_Value::normalize");
  // From Hans Rohnert...

  if (this->tv_.tv_usec >= PPM_ONE_SECOND_IN_USECS)
    {
      do
        {
          this->tv_.tv_sec++;
          this->tv_.tv_usec -= PPM_ONE_SECOND_IN_USECS;
        }
      while (this->tv_.tv_usec >= PPM_ONE_SECOND_IN_USECS);
    }
  else if (this->tv_.tv_usec <= -PPM_ONE_SECOND_IN_USECS)
    {
      do
        {
          this->tv_.tv_sec--;
          this->tv_.tv_usec += PPM_ONE_SECOND_IN_USECS;
        }
      while (this->tv_.tv_usec <= -PPM_ONE_SECOND_IN_USECS);
    }

  if (this->tv_.tv_sec >= 1 && this->tv_.tv_usec < 0)
    {
      this->tv_.tv_sec--;
      this->tv_.tv_usec += PPM_ONE_SECOND_IN_USECS;
    }
  else if (this->tv_.tv_sec < 0 && this->tv_.tv_usec > 0)
    {
      this->tv_.tv_sec++;
      this->tv_.tv_usec -= PPM_ONE_SECOND_IN_USECS;
    }
}

std::string 
PPM_Time_Value::timetostring() const
{
	char strtime[50] = "";
	struct tm tmCurr;
	time_t secs = sec ();
	tmCurr = *localtime_r(&secs,&tmCurr);
	sprintf(strtime,"%4d-%02d-%02d %2d:%2d:%2d.%3ld",
		tmCurr.tm_year+1900 ,tmCurr.tm_mon + 1,tmCurr.tm_mday,
		tmCurr.tm_hour,tmCurr.tm_min,tmCurr.tm_sec,
		usec()
	);

	std::string tm_str(strtime);
	return tm_str;
}

std::string 
PPM_Time_Value::timetostring2() const
{
	char strtime[50] = "";

	struct tm tmCurr;
	time_t secs = sec ();
	tmCurr = *localtime_r(&secs,&tmCurr);
	sprintf(strtime,"%4d/%d/%d %2d:%2d:%2d",
		tmCurr.tm_year+1900 ,tmCurr.tm_mon + 1,tmCurr.tm_mday,
		tmCurr.tm_hour,tmCurr.tm_min,tmCurr.tm_sec
		);

	std::string tm_str(strtime);
	return tm_str;
}

int 
PPM_Time_Value::GetYear()
{
	struct tm tmCurr;
	time_t secs = sec ();
	tmCurr = *localtime_r(&secs,&tmCurr);
	return (tmCurr.tm_year+1900);
}

int 
PPM_Time_Value::GetTotalSeconds()
{
    return this->tv_.tv_sec;
}
 
int 
PPM_Time_Value::GetMonth()
{
	struct tm tmCurr;
	time_t secs = sec();
	tmCurr = *localtime_r(&secs,&tmCurr);
	return tmCurr.tm_mon + 1;
}
int 
PPM_Time_Value::GetDay()
{
	struct tm tmCurr;
	time_t secs = sec();
	tmCurr = *localtime_r(&secs,&tmCurr);
	return tmCurr.tm_mday ;
}

int PPM_Time_Value::GetHour()
{
	struct tm tmCurr;
	time_t secs = sec();
	tmCurr = *localtime_r(&secs,&tmCurr);
	return tmCurr.tm_hour;
}
int PPM_Time_Value::GetMinute()
{
	struct tm tmCurr;
	time_t secs = sec();
	tmCurr = *localtime_r(&secs,&tmCurr);
	return tmCurr.tm_min ;
}
int PPM_Time_Value::GetSecond()
{
	struct tm tmCurr;
	time_t secs = sec();
	tmCurr = *localtime_r(&secs,&tmCurr);
	return tmCurr.tm_sec;
}

int PPM_Time_Value::GetMilliSecond()
{
	return tv_.tv_usec/1000;
}

//计算两个时间之间的毫秒差
__int64 PPM_Time_Value::DateDiff(const PPM_Time_Value& tBegin)
{
    __int64 second = tv_.tv_sec-tBegin.tv_.tv_sec;
	return second*1000 + (tv_.tv_usec-tBegin.tv_.tv_usec)/1000;
}

//计算两个时间之间的天数差
int PPM_Time_Value::DayDiff(const PPM_Time_Value& tBegin)
{
	PPM_Time_Value tm1,tm2=tBegin;
	tm1.SetTime(GetYear(), GetMonth(), GetDay(),0,0,0,0);
	tm2.SetTime(tm2.GetYear(), tm2.GetMonth(), tm2.GetDay(),0,0,0,0);
	int secDiff = tm1.tv_.tv_sec - tm2.tv_.tv_sec;
	return secDiff/(3600*24);
}


std::string PPM_Time_Value::SecondToString(const int nSecond)
{
	PPM_Time_Value tm1;
	tm1.sec(nSecond);
	return tm1.timetostring().substr(0,19);
}
