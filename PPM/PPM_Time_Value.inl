// -*- C++ -*-
//
// Time_Value.inl,v 4.5 2002/09/13 15:37:53 ossama Exp


// Returns the value of the object as a timeval.
//下面既是在timeval强制转换时返回this->tv_
PPM_INLINE
PPM_Time_Value::operator timeval () const
{
  // PPM_OS_TRACE ("PPM_Time_Value::operator timeval");
  return this->tv_;
}

// Returns a pointer to the object as a timeval.
//同理强制转换时返回timeval的指针
PPM_INLINE
PPM_Time_Value::operator const timeval * () const
{
  // PPM_OS_TRACE ("PPM_Time_Value::operator const timeval *");
  return (const timeval *) &this->tv_;
}
//设置时间并正则化
PPM_INLINE void
PPM_Time_Value::set (long sec, long usec)
{
  // PPM_OS_TRACE ("PPM_Time_Value::set");
  this->tv_.tv_sec = sec;
  this->tv_.tv_usec = usec;
  this->normalize ();
}
//个位以上是秒,小数*1百万,即微妙
PPM_INLINE void
PPM_Time_Value::set (double d)
{
  // PPM_OS_TRACE ("PPM_Time_Value::set");
  long l = (long) d;
  this->tv_.tv_sec = l;
  this->tv_.tv_usec = (long) ((d - (double) l) * PPM_ONE_SECOND_IN_USECS);
  this->normalize ();
}

// Initializes a timespec_t.  Note that this approach loses precision
// since it converts the nano-seconds into micro-seconds.  But then
// again, do any real systems have nano-second timer precision?!

PPM_INLINE void
PPM_Time_Value::set (const timespec_t &tv)
{
  // PPM_OS_TRACE ("PPM_Time_Value::set");
  this->tv_.tv_sec = (long)tv.tv_sec;
  // Convert nanoseconds into microseconds.
  this->tv_.tv_usec = tv.tv_nsec / 1000;

  this->normalize ();
}

PPM_INLINE void
PPM_Time_Value::set (const timeval &tv)
{
  // PPM_OS_TRACE ("PPM_Time_Value::set");
  this->tv_.tv_sec = tv.tv_sec;
  this->tv_.tv_usec = tv.tv_usec;

  this->normalize ();
}

//通过设置年月日时分秒毫秒设置时间
PPM_INLINE BOOL 
PPM_Time_Value::SetTime(WORD wyear,BYTE bmonth,BYTE bday,BYTE bhour,BYTE bminute,BYTE bsecond,WORD wms)
{
	struct tm tmCurr;
	tmCurr.tm_year = wyear - 1900;
	tmCurr.tm_mon = bmonth - 1;
	tmCurr.tm_mday = bday;
	tmCurr.tm_hour = bhour;
	tmCurr.tm_min = bminute;
	tmCurr.tm_sec = bsecond;
	
	time_t secs = mktime(&tmCurr);
	if(-1 == secs){
		return FALSE;
             this->msec(0);
	}
	this->sec(secs);

	return TRUE;

}
/* TIME FORMAT: 09:21:00.81 */
PPM_INLINE BOOL 
PPM_Time_Value::SetTime_NoDate(char* strTime,int len)
{
	if(len < 8){
		return FALSE;
	}
	char tm_buf[4];
	memset(tm_buf,0,sizeof(tm_buf));
	int tm_ioffset = 0;
	
	BYTE bhour,bminute,bsecond;
	int tm_ms = 0;
	memcpy(tm_buf,strTime + tm_ioffset,2);
	bhour = atoi(tm_buf);
	tm_ioffset += 3;
	memcpy(tm_buf,strTime + tm_ioffset,2);
	bminute = atoi(tm_buf);
	tm_ioffset += 3;
	memcpy(tm_buf,strTime + tm_ioffset,2);
	bsecond = atoi(tm_buf);
	tm_ioffset += 3;
	if(len < 11){
		tm_ms = 0;
	}else{
		memcpy(tm_buf,strTime + tm_ioffset,2);
		tm_ms = atoi(tm_buf);
	}

	tm_ioffset = bhour * 60 * 60 + bminute * 60 + bsecond;

	this->sec(tm_ioffset);
	this->usec(tm_ms * 10000);

	return TRUE;
	
}
/* TIME FORMAT: 16:48:21.953 */
PPM_INLINE BOOL 
PPM_Time_Value::SetTime_NoDateMS(char* strTime,int len)
{
	if(len < 8){
		return FALSE;
	}
	char tm_buf[4];
	memset(tm_buf,0,sizeof(tm_buf));
	int tm_ioffset = 0;
	
	BYTE bhour,bminute,bsecond;
	int tm_ms = 0;
	memcpy(tm_buf,strTime + tm_ioffset,2);
	bhour = atoi(tm_buf);
	tm_ioffset += 3;
	memcpy(tm_buf,strTime + tm_ioffset,2);
	bminute = atoi(tm_buf);
	tm_ioffset += 3;
	memcpy(tm_buf,strTime + tm_ioffset,2);
	bsecond = atoi(tm_buf);
	tm_ioffset += 3;
	if(len < 12){
		tm_ms = 0;
	}else{
		memcpy(tm_buf,strTime + tm_ioffset,3);
		tm_ms = atoi(tm_buf);
	}

	tm_ioffset = bhour * 60 * 60 + bminute * 60 + bsecond;

	this->sec(tm_ioffset);
	this->usec(tm_ms * 1000);

	return TRUE;
	
}

/************************************************************************
YYMMDDHHMM 长度等于10
HH:MM:SS 长度等于8
************************************************************************/
PPM_INLINE BOOL 
PPM_Time_Value::SetTime(char* strTime,int len)
{
	if(10 == len){
		char tm_buf[2];
		int tm_ioffset = 0;
		WORD wyear;
		BYTE bmonth,bday,bhour,bminute;
		memcpy(tm_buf,strTime + tm_ioffset,2);
		wyear = atoi(tm_buf) + 2000;
		tm_ioffset += 2;
		memcpy(tm_buf,strTime + tm_ioffset,2);
		bmonth = atoi(tm_buf);
		tm_ioffset += 2;
		memcpy(tm_buf,strTime + tm_ioffset,2);
		bday = atoi(tm_buf);
		tm_ioffset += 2;
		memcpy(tm_buf,strTime + tm_ioffset,2);
		bhour = atoi(tm_buf);
		tm_ioffset += 2;
		memcpy(tm_buf,strTime + tm_ioffset,2);
		bminute = atoi(tm_buf);
		SetTime(wyear,bmonth,bday,bhour,bminute,0,0);
		return TRUE;
	}else if(8 == len){
		char tm_buf[2];
		int tm_ioffset = 0;

		BYTE bhour,bminute,bsecond;
		memcpy(tm_buf,strTime + tm_ioffset,2);
		bhour = atoi(tm_buf);
		tm_ioffset += 3;
		memcpy(tm_buf,strTime + tm_ioffset,2);
		bminute = atoi(tm_buf);
		tm_ioffset += 3;
		memcpy(tm_buf,strTime + tm_ioffset,2);
		bsecond = atoi(tm_buf);
		tm_ioffset = bhour * 60 * 60 + bminute * 60 + bsecond;
		this->sec(tm_ioffset);

		return TRUE;
	}else{
		return FALSE;
	}
}

PPM_INLINE
PPM_Time_Value::PPM_Time_Value (const struct timeval &tv)
  // : tv_ ()
{
  // PPM_OS_TRACE ("PPM_Time_Value::PPM_Time_Value");
  this->set (tv);
}

PPM_INLINE
PPM_Time_Value::PPM_Time_Value (void)
  // : tv_ ()
{
  // PPM_OS_TRACE ("PPM_Time_Value::PPM_Time_Value");
  this->set (0, 0);
}

PPM_INLINE
PPM_Time_Value::PPM_Time_Value (long sec, long usec)
{
  // PPM_OS_TRACE ("PPM_Time_Value::PPM_Time_Value");
  this->set (sec, usec);
}

// Returns number of seconds.
//返回秒数
PPM_INLINE long
PPM_Time_Value::sec (void) const
{
  // PPM_OS_TRACE ("PPM_Time_Value::sec");
  return this->tv_.tv_sec;
}

// Sets the number of seconds.
//设置秒数
PPM_INLINE void
PPM_Time_Value::sec (long sec)
{
  // PPM_OS_TRACE ("PPM_Time_Value::sec");
  this->tv_.tv_sec = sec;
}

// Converts from Time_Value format into milli-seconds format.
//返回毫秒
PPM_INLINE __int64
PPM_Time_Value::msec (void) const
{
  // PPM_OS_TRACE ("PPM_Time_Value::msec");
  return ((__int64)this->tv_.tv_sec) * 1000 + ((__int64)this->tv_.tv_usec) / 1000;
}

// Converts from milli-seconds format into Time_Value format.
//将毫秒数设置为PPM_Time_Value表达方式
PPM_INLINE void
PPM_Time_Value::msec (__int64 milliseconds)
{
  // PPM_OS_TRACE ("PPM_Time_Value::msec");
  // Convert millisecond units to seconds;
  this->tv_.tv_sec = (long)(milliseconds / 1000);
  // Convert remainder to microseconds;
  this->tv_.tv_usec = (long)((milliseconds - (this->tv_.tv_sec * 1000)) * 1000);
}

// Returns number of micro-seconds.
//返回微妙
PPM_INLINE long
PPM_Time_Value::usec (void) const
{
  // PPM_OS_TRACE ("PPM_Time_Value::usec");
  return this->tv_.tv_usec;
}

// Sets the number of micro-seconds.
//设置微妙
PPM_INLINE void
PPM_Time_Value::usec (long usec)
{
  // PPM_OS_TRACE ("PPM_Time_Value::usec");
  this->tv_.tv_usec = usec;
}
//微妙数的*=操作,作用不大
PPM_INLINE PPM_Time_Value &
PPM_Time_Value::operator *= (double d)
{
  double time =
    ((double) this->sec ()) * PPM_ONE_SECOND_IN_USECS + this->usec ();
  time *= d;
  this->sec ((long)(time / PPM_ONE_SECOND_IN_USECS));
  this->usec (((long)time) % PPM_ONE_SECOND_IN_USECS);
  this->normalize ();
  return *this;
}
//支持一个double*PPM_Time_Value 返回一个PPM_Time_Value
PPM_INLINE PPM_Time_Value
operator * (double d, const PPM_Time_Value &tv)
{
  return PPM_Time_Value (tv) *= d;
}

PPM_INLINE PPM_Time_Value
operator * (const PPM_Time_Value &tv, double d)
{
  return PPM_Time_Value (tv) *= d;
}

// Returns the value of the object as a timespec_t.
//强制转换时返回timespec_t
PPM_INLINE
PPM_Time_Value::operator timespec_t () const
{
  // PPM_OS_TRACE ("PPM_Time_Value::operator timespec_t");
  timespec_t tv;
  tv.tv_sec = this->sec ();
  // Convert microseconds into nanoseconds.
  tv.tv_nsec = this->tv_.tv_usec * 1000;
  return tv;
}

// Initializes the PPM_Time_Value object from a timespec_t.

PPM_INLINE
PPM_Time_Value::PPM_Time_Value (const timespec_t &tv)
  // : tv_ ()
{
  // PPM_OS_TRACE ("PPM_Time_Value::PPM_Time_Value");
  this->set (tv);
}

// Add TV to this.

PPM_INLINE PPM_Time_Value &
PPM_Time_Value::operator+= (const PPM_Time_Value &tv)
{
  // PPM_OS_TRACE ("PPM_Time_Value::operator+=");
  this->sec (this->sec () + tv.sec ());
  this->usec (this->usec () + tv.usec ());
  this->normalize ();
  return *this;
}

// Subtract TV to this.

PPM_INLINE PPM_Time_Value &
PPM_Time_Value::operator-= (const PPM_Time_Value &tv)
{
  // PPM_OS_TRACE ("PPM_Time_Value::operator-=");
  this->sec (this->sec () - tv.sec ());
  this->usec (this->usec () - tv.usec ());
  this->normalize ();
  return *this;
}

// Adds two PPM_Time_Value objects together, returns the sum.

PPM_INLINE PPM_Time_Value
operator + (const PPM_Time_Value &tv1,
            const PPM_Time_Value &tv2)
{
  // PPM_OS_TRACE ("operator +");
  PPM_Time_Value sum (tv1.sec () + tv2.sec (),
                      tv1.usec () + tv2.usec ());

  sum.normalize ();
  return sum;
}

// Subtracts two PPM_Time_Value objects, returns the difference.

PPM_INLINE PPM_Time_Value
operator - (const PPM_Time_Value &tv1,
            const PPM_Time_Value &tv2)
{
  // PPM_OS_TRACE ("operator -");
  PPM_Time_Value delta (tv1.sec () - tv2.sec (),
                        tv1.usec () - tv2.usec ());
  delta.normalize ();
  return delta;
}

// True if tv1 > tv2.
// 时间比较
PPM_INLINE int
operator > (const PPM_Time_Value &tv1,
            const PPM_Time_Value &tv2)
{
  // PPM_OS_TRACE ("operator >");
  if (tv1.sec () > tv2.sec ())
    return 1;
  else if (tv1.sec () == tv2.sec ()
           && tv1.usec () > tv2.usec ())
    return 1;
  else
    return 0;
}


// True if tv1 >= tv2.
PPM_INLINE int
operator >= (const PPM_Time_Value &tv1,
             const PPM_Time_Value &tv2)
{
  // PPM_OS_TRACE ("operator >=");
  if (tv1.sec () > tv2.sec ())
    return 1;
  else if (tv1.sec () == tv2.sec ()
           && tv1.usec () >= tv2.usec ())
    return 1;
  else
    return 0;
}

// True if tv1 < tv2.
PPM_INLINE int
operator < (const PPM_Time_Value &tv1,
            const PPM_Time_Value &tv2)
{
  // PPM_OS_TRACE ("operator <");
  return tv2 > tv1;
// 
//   if (tv1.sec () < tv2.sec ())
//     return 1;
//   else if (tv1.sec () == tv2.sec ()
//            && tv1.usec () < tv2.usec ())
//     return 1;
//   else
//     return 0;
}

// True if tv1 <= tv2.

PPM_INLINE int
operator <= (const PPM_Time_Value &tv1,
             const PPM_Time_Value &tv2)
{
  // PPM_OS_TRACE ("operator <=");
  return tv2 >= tv1;

//   if (tv1.sec () < tv2.sec ())
//     return 1;
//   else if (tv1.sec () == tv2.sec ()
//            && tv1.usec () <= tv2.usec ())
//     return 1;
//   else
//     return 0;
}

// True if tv1 == tv2.

PPM_INLINE int
operator == (const PPM_Time_Value &tv1,
             const PPM_Time_Value &tv2)
{
  // PPM_OS_TRACE ("operator ==");
  return tv1.sec () == tv2.sec ()
    && tv1.usec () == tv2.usec ();
}

// True if tv1 != tv2.
PPM_INLINE int
operator != (const PPM_Time_Value &tv1,
             const PPM_Time_Value &tv2)
{
  // PPM_OS_TRACE ("operator !=");
  return !(tv1 == tv2);
//   return !(tv1.sec () == tv2.sec ()
//     && tv1.usec () == tv2.usec ());
}
