#include "./PPM_UDP_Connector.h"
#include "./PPM_Public_Var.h"
//客户端构造函数
PPM_UDP_Connector::PPM_UDP_Connector()
{
#ifdef WIN32
		//首先初始化通讯的环境
		WSADATA wsaData;
		WORD wVersionRequested = MAKEWORD_NETSEQ(2, 2);
		int nResult = WSAStartup(wVersionRequested, &wsaData);
		if(nResult != 0){
			PPM_SOCKLOG((LM_ERROR,"Server WSAStartup Failed! %m\n"));
			return;
		} 
		//如果初始化的不是我们想要的版本,退出
		if (LOBYTE(wsaData.wVersion) != 2 || HIBYTE(wsaData.wVersion) != 2)
		{
		   WSACleanup();
		   PPM_SOCKLOG((LM_ERROR,"Server WSADATA version is%d.%d,is error! \n",
			   HIBYTE(wsaData.wVersion),
			   LOBYTE(wsaData.wVersion)));
		   return ;
		}
#endif
	m_bDetectFlag_ = 0;
	m_pUdpRecvDeal_ = NULL;
	if(0 > m_Sock_Dgram_.open())
	{
		PPM_SOCKLOG((LM_ERROR,"Open Udp Client error %m\n"));
	}

};
//服务端构造函数
PPM_UDP_Connector::PPM_UDP_Connector(PPM_Addr& addr,PPM_UDP_RecvDeal* pRecvDeal,PPM_Reactor* pReactor,BYTE bDetectFlag)
{
#ifdef WIN32
		//首先初始化通讯的环境
		WSADATA wsaData;
		WORD wVersionRequested = MAKEWORD_NETSEQ(2, 2);
		int nResult = WSAStartup(wVersionRequested, &wsaData);
		if(nResult != 0){
			PPM_SOCKLOG((LM_ERROR,"Server WSAStartup Failed! %m\n"));
			return;
		} 
		//如果初始化的不是我们想要的版本,退出
		if (LOBYTE(wsaData.wVersion) != 2 || HIBYTE(wsaData.wVersion) != 2)
		{
		   WSACleanup();
		   PPM_SOCKLOG((LM_ERROR,"Server WSADATA version is%d.%d,is error! \n",
			   HIBYTE(wsaData.wVersion),
			   LOBYTE(wsaData.wVersion)));
		   return ;
		}
#endif
	if(NULL == pReactor){
		this->reactor(PPM_Reactor::instance());
	}else{
		this->reactor(pReactor);
	}
	m_bDetectFlag_ = bDetectFlag;
	m_pUdpRecvDeal_ = pRecvDeal;
	if(0 <= m_Sock_Dgram_.open(addr)){
		if(1 == bDetectFlag){
			if(FALSE == this->reactor()->reactor_event_loop_done()){
				this->reactor()->StartThread();
			}
			this->reactor()->register_handler(this,READ_MASK);
		}
	}else{
		PPM_SOCKLOG((LM_ERROR,"Open Udp Server error %m\n"));
	}

};
PPM_UDP_Connector::~PPM_UDP_Connector()
{
	m_Sock_Dgram_.close();
#ifdef WIN32
		//清除通讯的环境
		if(0 > WSACleanup()){
			PPM_SOCKLOG((LM_ERROR,"Server Error ocurr when WSACleanup! %m \n"));
		}
#endif
};

//有数据来的时候会触发此函数，并将UDPSock传出，由具体应用进行收数据处理
int 
PPM_UDP_Connector::handle_input (PPM_HANDLE fd)
{
	if(NULL != m_pUdpRecvDeal_){
		m_pUdpRecvDeal_->OnRecvFrom(&m_Sock_Dgram_);
		return 1;
	}
	else{
		return 0;
	}
}

int 
PPM_UDP_Connector::SendTo( const void *buf,
            size_t n,
            const PPM_Addr &addr,
            int flags)
{
	return m_Sock_Dgram_.send(buf,n,addr,flags);
};
int 
PPM_UDP_Connector::RecvFrom(void *buf, 
		  size_t n, 
		  PPM_Addr &addr, 
		  int flags)
{
	return m_Sock_Dgram_.recv(buf,n,addr,flags);
};
