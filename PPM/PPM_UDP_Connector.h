#ifndef _PPM_UDP_Connector_H_
#define _PPM_UDP_Connector_H_

#include "./PPM_SOCK_Dgram.h"
#include "./PPM_Reactor.h"
/**
使用方法:
客户端:
定义一个PPM_UDP_Connector对象就可以调用SendTo和RecvFrom使用了
示例:
int main(int argc, char* argv[])
{
	PPM_UDP_Connector tm_client;
	PPM_INET_Addr tm_addr(12345,"************");
	char tm_string[100];
	PPM_OS::sprintf(tm_string,"Send test!");
	tm_client.SendTo(tm_string,PPM_OS::strlen(tm_string),tm_addr);
	PPM_INET_Addr tm_oppaddr;
	tm_client.RecvFrom(tm_string,sizeof(tm_string),tm_oppaddr);
}
服务端:
示例:
//先定义一个收数据处理类:
class CUDPDeal : public PPM_UDP_RecvDeal
{
public:
	virtual int OnRecvFrom(PPM_SOCK_Dgram* pUDPSock)
	{
		char tm_buffer[300];
		PPM_OS::memset(tm_buffer,0,sizeof(tm_buffer));
		PPM_INET_Addr tm_addr;
		int recvsize = pUDPSock->recv(tm_buffer,300,tm_addr);
		PPM_DEBUG((LM_ERROR,"back%sport%d:%s,%m\n",
			tm_addr.get_host_addr(),
			tm_addr.get_port_number(),
			tm_buffer
			));
		//可以回送消息
		pUDPSock->send(tm_buffer,PPM_OS::strlen(tm_buffer),tm_addr);
		return recvsize;
	};
};
int main(int argc, char* argv[])
{
	CUDPDeal tm_udpdeal;
	PPM_UDP_Connector* tm_pServer;
	PPM_INET_Addr tm_addr(12345); //12345 is listen port
	tm_pServer = new PPM_UDP_Connector(tm_addr,&tm_udpdeal,NULL,1);

	//如果有数据来就会触发CUDPDeal的OnRecvFrom函数
	//同时也可以像客户端一样进行数据发送
	PPM_INET_Addr tm_addr_send(12345,"************");
	char tm_string[100];
	PPM_OS::sprintf(tm_string,"Send test!");
	tm_pServer->SendTo(tm_string,PPM_OS::strlen(tm_string),tm_addr_send);
}
  */
class PPM_UDP_RecvDeal
{
public:
	virtual int OnRecvFrom(PPM_SOCK_Dgram* pUDPSock) = 0;
};
class PPM_UDP_Connector : public PPM_Event_Handler
{
public:
	//参数bDetectFlag表示是否需要侦听收状态0:不需要，1需要
	//客户端构造函数
	PPM_UDP_Connector();
	//服务端构造函数
	PPM_UDP_Connector(PPM_Addr& addr,PPM_UDP_RecvDeal* pRecvDeal = NULL,PPM_Reactor* pReactor = NULL,BYTE bDetectFlag = 0);

	~PPM_UDP_Connector();
	
	virtual PPM_HANDLE get_handle (void) const{
		return m_Sock_Dgram_.get_handle();
	};

	void SetRecvDeal(PPM_UDP_RecvDeal* pRecvDeal){
		m_pUdpRecvDeal_ = pRecvDeal;
	}
	///有数据接收时会调用此函数
	virtual int handle_input (PPM_HANDLE fd = PPM_INVALID_HANDLE);

	int SendTo( const void *buf,size_t n,const PPM_Addr &addr,int flags = 0);
	int RecvFrom(void *buf,size_t n,PPM_Addr &addr,int flags = 0);

private:
	PPM_SOCK_Dgram m_Sock_Dgram_;
	BYTE m_bDetectFlag_; //是否需要侦听收数据的状态0：不需要，1：需要
	PPM_UDP_RecvDeal* m_pUdpRecvDeal_;
};


#endif
