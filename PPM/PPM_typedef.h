#ifndef _PPM_TYPEDEF_H_
#define _PPM_TYPEDEF_H_
#include "StdHeader.h"
#include "PPM_MACRO.h"

typedef int PPM_HANDLE;
typedef PPM_HANDLE PPM_SOCKET;
typedef fd_set PPM_FD_SET_TYPE;
typedef long long __int64;
typedef unsigned long long DWORDLONG;
typedef socklen_t PPM_SOCKET_LEN;


typedef unsigned int PPM_UINT32;
// typedef int nssize_t;
#define PPM_HOSTENT_DATA_SIZE (4*1024) 
#define PPM_SERVENT_DATA_SIZE (4*1024)
#define PPM_PROTOENT_DATA_SIZE (2*1024)
typedef char PPM_HOSTENT_DATA[PPM_HOSTENT_DATA_SIZE];
typedef char PPM_SERVENT_DATA[PPM_SERVENT_DATA_SIZE];
typedef char PPM_PROTOENT_DATA[PPM_PROTOENT_DATA_SIZE];

extern "C"
{
	typedef void (* PPM_SignalHandler) (int);
	typedef void (* PPM_SignalHandlerV) (int);
}

typedef unsigned long DWORD ;
typedef PPM_UINT32 UINT32 ;
typedef pthread_mutex_t PPM_mutex_t;
typedef pthread_cond_t PPM_cond_t;
typedef pthread_condattr_t PPM_condattr_t;
typedef pthread_mutexattr_t PPM_mutexattr_t;
typedef unsigned char byte;
typedef unsigned int UINT;
typedef unsigned short WORD;

//threads
typedef pthread_t PPM_thread_t;
typedef pthread_t  PPM_hthread_t;
typedef DWORD PPM_THR_FUNC_RETURN;
typedef void* PPM_THR_FUNC_VOID_RETURN;

typedef PPM_THR_FUNC_RETURN (*PPM_THR_C_FUNC)(void *);
typedef PPM_THR_FUNC_RETURN (*PPM_THR_FUNC)(void *);
typedef PPM_THR_FUNC PPM_THR_FUNC_INTERNAL;
typedef PPM_THR_FUNC_VOID_RETURN (*PPM_THR_C_VOID_FUNC)(void *);
typedef PPM_THR_FUNC_VOID_RETURN (*PPM_THR_VOID_FUNC)(void *);
typedef PPM_THR_VOID_FUNC PPM_THR_VOID_FUNC_INTERNAL;

typedef char PPM_TCHAR;

typedef timespec timespec_t;

typedef timespec_t* PPM_TIMESPEC_PTR;

typedef const struct rlimit PPM_SETRLIMIT_TYPE;

typedef struct stat PPM_stat;

#define MAKEWORD(low,high)   ((WORD)((BYTE)(low)) | (((WORD)(BYTE)(high))<<8))

#define MAKEWORD_NETSEQ(low,high)  ((WORD)((BYTE)(high)) | (((WORD)(BYTE)(low))<<8))
#define MAKEWORD_NETSEQ1(wNum) MAKEWORD_NETSEQ((BYTE)(wNum & 0xff),(BYTE)((wNum & 0xff00) >> 8))  
#define REVERTWORD_NETSEQ(low,high)  ((WORD)((BYTE)(low)) | (((WORD)(BYTE)(high))<<8))
#define REVERTWORD_NETSEQ1(wNum)  REVERTWORD_NETSEQ((BYTE)(wNum & 0xff),(BYTE)((wNum & 0xff00) >> 8))

#define MAKEINT_NETSEQ(wLow,wHigh) ((UINT)MAKEWORD_NETSEQ1(wHigh) | (((UINT)MAKEWORD_NETSEQ1(wLow))<<16))
#define MAKEINT_NETSEQ1(iNum) MAKEINT_NETSEQ((WORD)(iNum & 0xffff),(WORD)((iNum & 0xffff0000) >> 16) )
#define REVERTINT_NETSEQ(wLow,wHigh) ((UINT)MAKEWORD_NETSEQ1(wLow) | (((UINT)MAKEWORD_NETSEQ1(wHigh))<<16))
#define REVERTINT_NETSEQ1(iNum) REVERTINT_NETSEQ((WORD)(iNum & 0xffff),(WORD)((iNum & 0xffff0000) >> 16) )

const DWORDLONG  gm_dwordlongmask = (DWORDLONG)0xffffffff * (DWORDLONG)0x10000 * (DWORDLONG)0x10000;

#define MAKEINT64_NETSEQ(iLow,iHigh) ((DWORDLONG)MAKEINT_NETSEQ1(iHigh) | (((DWORDLONG)MAKEINT_NETSEQ1(iLow))<<32))
#define MAKEINT64_NETSEQ1(i64Num) MAKEINT64_NETSEQ((UINT)(i64Num & 0xffffffff),(UINT)((i64Num & gm_dwordlongmask) >> 32) )
#define REVERTINT64_NETSEQ(iLow,iHigh) ((DWORDLONG)REVERTINT_NETSEQ1(iLow) | (((DWORDLONG)REVERTINT_NETSEQ1(iHigh))<<32))
#define REVERTINT64_NETSEQ1(i64Num) REVERTINT64_NETSEQ((UINT)(i64Num & 0xffffffff),(UINT)((i64Num & gm_dwordlongmask) >> 32) )



#endif

