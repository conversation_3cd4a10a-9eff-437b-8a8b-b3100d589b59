{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Debug-f5ebdc15457944623624.json", "minimumCMakeVersion": {"string": "3.8"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "DtDrvApp", "targetIndexes": [0]}], "targets": [{"directoryIndex": 0, "id": "DtDrvApp::@6890427a1f51a3e7e1df", "jsonFile": "target-DtDrvApp-Debug-35f4d395d44cd19925ab.json", "name": "DtDrvApp", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "source": "/home/<USER>/code/DtDrvApp_AI_CSharp"}, "version": {"major": 2, "minor": 7}}