{"artifacts": [{"path": "DtDrvApp"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "link_directories", "target_link_libraries", "include_directories"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 145, "parent": 0}, {"command": 1, "file": 0, "line": 137, "parent": 0}, {"command": 2, "file": 0, "line": 152, "parent": 0}, {"command": 2, "file": 0, "line": 155, "parent": 0}, {"command": 3, "file": 0, "line": 23, "parent": 0}, {"command": 3, "file": 0, "line": 64, "parent": 0}, {"command": 3, "file": 0, "line": 65, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-Wall -g -std=gnu++11"}], "includes": [{"backtrace": 5, "path": "/home/<USER>/code/DtDrvApp_AI_CSharp/src"}, {"backtrace": 5, "path": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource"}, {"backtrace": 5, "path": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult"}, {"backtrace": 5, "path": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY"}, {"backtrace": 5, "path": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock"}, {"backtrace": 5, "path": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model"}, {"backtrace": 5, "path": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area"}, {"backtrace": 5, "path": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete"}, {"backtrace": 5, "path": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log"}, {"backtrace": 5, "path": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete"}, {"backtrace": 5, "path": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell"}, {"backtrace": 5, "path": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete"}, {"backtrace": 5, "path": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem"}, {"backtrace": 5, "path": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed"}, {"backtrace": 5, "path": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication"}, {"backtrace": 5, "path": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement"}, {"backtrace": 5, "path": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock"}, {"backtrace": 5, "path": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete"}, {"backtrace": 5, "path": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community"}, {"backtrace": 5, "path": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete"}, {"backtrace": 5, "path": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock"}, {"backtrace": 5, "path": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal"}, {"backtrace": 5, "path": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete"}, {"backtrace": 5, "path": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock"}, {"backtrace": 5, "path": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic"}, {"backtrace": 5, "path": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete"}, {"backtrace": 5, "path": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock"}, {"backtrace": 5, "path": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement"}, {"backtrace": 5, "path": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock"}, {"backtrace": 5, "path": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement"}, {"backtrace": 5, "path": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock"}, {"backtrace": 5, "path": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete"}, {"backtrace": 5, "path": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement"}, {"backtrace": 5, "path": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete"}, {"backtrace": 5, "path": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock"}, {"backtrace": 5, "path": "/home/<USER>/code/DtDrvApp_AI_CSharp/utils"}, {"backtrace": 5, "path": "/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass"}, {"backtrace": 5, "path": "/home/<USER>/code/DtDrvApp_AI_CSharp/PPM"}, {"backtrace": 5, "path": "/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils"}, {"backtrace": 6, "path": "/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds"}, {"backtrace": 7, "path": "/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds"}], "language": "CXX", "languageStandard": {"backtraces": [1], "standard": "11"}, "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128]}], "id": "DtDrvApp::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "-Wall -g", "role": "flags"}, {"fragment": "", "role": "flags"}, {"backtrace": 2, "fragment": "-L/home/<USER>/code/DtDrvApp_AI_CSharp/lib", "role": "libraryPath"}, {"fragment": "-Wl,-rpath,/home/<USER>/code/DtDrvApp_AI_CSharp/lib", "role": "libraries"}, {"backtrace": 3, "fragment": "-l<PERSON><PERSON><PERSON>", "role": "libraries"}, {"backtrace": 3, "fragment": "-lrt", "role": "libraries"}, {"backtrace": 3, "fragment": "-lcrypto", "role": "libraries"}, {"backtrace": 3, "fragment": "-lEncodeUtils", "role": "libraries"}, {"backtrace": 3, "fragment": "-lzip", "role": "libraries"}, {"backtrace": 3, "fragment": "-lz", "role": "libraries"}, {"backtrace": 3, "fragment": "-ldl", "role": "libraries"}, {"backtrace": 4, "fragment": "-lsybdb", "role": "libraries"}], "language": "CXX"}, "name": "DtDrvApp", "nameOnDisk": "DtDrvApp", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "DtDrvApp.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "PPM/PPM_Addr.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "PPM/PPM_Client.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "PPM/PPM_Event_Handler.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "PPM/PPM_Flag_Manip.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "PPM/PPM_Handle_Set.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "PPM/PPM_INET_Addr.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "PPM/PPM_IPC_SAP.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "PPM/PPM_Log_Msg.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "PPM/PPM_Log_Record.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "PPM/PPM_OS.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "PPM/PPM_OS_Error.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "PPM/PPM_OS_String.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "PPM/PPM_Reactor.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "PPM/PPM_SOCK.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "PPM/PPM_SOCK_Acceptor.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "PPM/PPM_SOCK_Buffer.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "PPM/PPM_SOCK_Connector.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "PPM/PPM_SOCK_Dgram.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "PPM/PPM_SOCK_Stream.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "PPM/PPM_Select_Reactor.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "PPM/PPM_Select_Reactor_Base.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "PPM/PPM_Select_Reactor_T.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "PPM/PPM_SelfMonitor.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "PPM/PPM_Signal.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "PPM/PPM_Synch.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "PPM/PPM_Tcp_Client.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "PPM/PPM_Thread.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "PPM/PPM_Thread_Adapter.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "PPM/PPM_Thread_Manager.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "PPM/PPM_Time_Value.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "PPM/PPM_UDP_Connector.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/CommSource/ColumnDef.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/CommSource/ColumnResult/ColumnResult.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/CommSource/ColumnResult/ParamParser.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/CommSource/DBColumnDef.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/CommSource/DtdrvApp_Public_Var.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/CommSource/ParaResult.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/CommSource/ResultDef.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/CommSource/SearchSock.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/CommSource/Search_PI.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/CommSource/SockFactory.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/ConfigSetting.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/DataBaseInfo.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/DataItem/DataItem.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/DtDrvAppMain.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/DtSockServer.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/DtSockServerActor.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/MiniDog.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/Search_DIY/DIY_Sock/SockDIYDeal.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/Search_DIY/DataSearch_Area/SearchArea.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/Search_DIY/DataSearch_Area/SearchArea_Concrete/SearchAreaStat_Event.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/Search_DIY/DataSearch_Area/SearchArea_Concrete/SearchAreaStat_Kpi.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/Search_DIY/DataSearch_Area/SearchArea_Concrete/SearchArea_Cell_Grid.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/Search_DIY/DataSearch_Area/SearchArea_Concrete/SearchArea_Cell_Kpi.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/Search_DIY/DataSearch_Area/SearchArea_Concrete/SearchArea_Event.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/Search_DIY/DataSearch_Area/SearchArea_Concrete/SearchArea_Event_ES.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/Search_DIY/DataSearch_Area/SearchArea_Concrete/SearchArea_Grid.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/Search_DIY/DataSearch_Area/SearchArea_Concrete/SearchArea_Gsmr_Event.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/Search_DIY/DataSearch_Area/SearchArea_Concrete/SearchArea_Log.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/Search_DIY/DataSearch_Area/SearchArea_Concrete/SearchArea_NB_Grid.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/Search_DIY/DataSearch_Area/SearchArea_Concrete/SearchArea_Sample.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/Search_DIY/DataSearch_Area/SearchArea_Concrete/SearchArea_Sample_CoverLeak.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/Search_DIY/DataSearch_Area/SearchArea_Concrete/SearchArea_Scan_Grid.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/Search_DIY/DataSearch_Area/SearchArea_Concrete/SearchLog_Kpi.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/Search_DIY/DataSearch_Area/SearchArea_Sub.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/Search_DIY/DataSearch_Cell/SearchCell.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/Search_DIY/DataSearch_Cell/SearchCell_Concrete/SearchCell_Sample.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/Search_DIY/DataSearch_Log/SearchLog.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/Search_DIY/DataSearch_Log/SearchLog_Concrete/SearchLog_Event.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/Search_DIY/DataSearch_Log/SearchLog_Concrete/SearchLog_Gsmr_Event.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/Search_DIY/DataSearch_Log/SearchLog_Concrete/SearchLog_Msg.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/Search_DIY/DataSearch_Log/SearchLog_Concrete/SearchLog_Sample.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/Search_DIY/DataSearch_Model/SearchModel.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/Search_DIY/DataSearch_Model/SearchModel_SqlSearch.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/Search_DIY/DataSearch_Model/SearchModel_Table.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/Search_DIY/SearchConditionMaker.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/Search_DIY/SearchDIY.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/Search_DIY/SqlMaker.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/Search_DIY/UserStatus.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/Search_Passed/Authentication/SockAuthenDeal.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/Search_Passed/ConfigManagement/ConfigMng_Concrete/ConfigMngSock_Area.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/Search_Passed/ConfigManagement/ConfigMng_Concrete/ConfigMngSock_Cell.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/Search_Passed/ConfigManagement/ConfigMng_Concrete/ConfigMngSock_Other.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/Search_Passed/ConfigManagement/ConfigMng_Sock/SockConfigMngDeal.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/Search_Passed/DBManagement/DBMng_Sock/SockDBMngDeal.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/Search_Passed/DataSearch_Community/CommunityMng_Concrete/CommMngSock_Other.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/Search_Passed/DataSearch_Community/CommunityMng_Sock/SockCommMngDeal.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/Search_Passed/DataSearch_Deal/Search_Concrete/SearchSock_Area.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/Search_Passed/DataSearch_Deal/Search_Concrete/SearchSock_BlackBlock.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/Search_Passed/DataSearch_Deal/Search_Concrete/SearchSock_Cell.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/Search_Passed/DataSearch_Deal/Search_Concrete/SearchSock_Cluster.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/Search_Passed/DataSearch_Deal/Search_Concrete/SearchSock_ComplainBlock.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/Search_Passed/DataSearch_Deal/Search_Concrete/SearchSock_ES.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/Search_Passed/DataSearch_Deal/Search_Concrete/SearchSock_Health.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/Search_Passed/DataSearch_Deal/Search_Concrete/SearchSock_LogFile.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/Search_Passed/DataSearch_Deal/Search_Concrete/SearchSock_LogReview.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/Search_Passed/DataSearch_Deal/Search_Concrete/SearchSock_Other.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/Search_Passed/DataSearch_Deal/Search_Sock/SockSearchDeal.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/Search_Passed/DataSearch_Statistic/Statistic_Concrete/StatisticSock_Area.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/Search_Passed/DataSearch_Statistic/Statistic_Concrete/StatisticSock_Cell.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/Search_Passed/DataSearch_Statistic/Statistic_Concrete/StatisticSock_Log.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/Search_Passed/DataSearch_Statistic/Statistic_Concrete/StatisticSock_Other.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/Search_Passed/DataSearch_Statistic/Statistic_Sock/SockStatisticDeal.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/Search_Passed/TestDataManagement/TestDataMng_Concrete/TestDataMngSock_Other.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/Search_Passed/TestDataManagement/TestDataMng_Sock/SockTestDataMngDeal.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/Search_Passed/UserManagement/UserMng_Concrete/UserMngSock_Other.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/Search_Passed/UserManagement/UserMng_Sock/SockUserMngDeal.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/Utility.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "stdclass/CycBuffer.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "stdclass/StdBuffer.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "stdclass/StdCommond.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "stdclass/StdDataObject.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "stdclass/StdFile.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "stdclass/StdFileFind.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "stdclass/StdFileMapping.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "stdclass/StdLock.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "stdclass/StdMonitor.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "stdclass/StdNetBuffer.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "stdclass/StdProfile.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "stdclass/StdThread.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "stdclass/StdTime.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/Strutils.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/linux_api.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "stdclass/freetds/StdBcp_Sybase.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "stdclass/freetds/StdFreetds.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "stdclass/freetds/StdSqlOpHelper.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "stdclass/freetds/StdSybase_Blk.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/freetds/DataBaseDeal.cpp", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}