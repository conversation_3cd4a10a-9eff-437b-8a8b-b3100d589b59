{"kind": "toolchains", "toolchains": [{"compiler": {"id": "GNU", "implicit": {"includeDirectories": ["/usr/lib/gcc/x86_64-redhat-linux/8/include", "/usr/local/include", "/usr/include"], "linkDirectories": ["/usr/lib/gcc/x86_64-redhat-linux/8", "/usr/lib64", "/lib64", "/usr/lib"], "linkFrameworkDirectories": [], "linkLibraries": ["gcc", "gcc_s", "c", "gcc", "gcc_s"]}, "path": "/usr/bin/gcc", "version": "8.5.0"}, "language": "C", "sourceFileExtensions": ["c", "m"]}, {"compiler": {"id": "GNU", "implicit": {"includeDirectories": ["/usr/include/c++/8", "/usr/include/c++/8/x86_64-redhat-linux", "/usr/include/c++/8/backward", "/usr/lib/gcc/x86_64-redhat-linux/8/include", "/usr/local/include", "/usr/include"], "linkDirectories": ["/usr/lib/gcc/x86_64-redhat-linux/8", "/usr/lib64", "/lib64", "/usr/lib"], "linkFrameworkDirectories": [], "linkLibraries": ["stdc++", "m", "gcc_s", "gcc", "c", "gcc_s", "gcc"]}, "path": "c++", "version": "8.5.0"}, "language": "CXX", "sourceFileExtensions": ["C", "M", "c++", "cc", "cpp", "cxx", "mm", "mpp", "CPP", "ixx", "cppm", "ccm", "cxxm", "c++m"]}], "version": {"major": 1, "minor": 0}}