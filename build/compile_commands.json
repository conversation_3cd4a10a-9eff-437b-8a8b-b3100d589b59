[{"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/DtDrvApp.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/DtDrvApp.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/DtDrvApp.cpp", "output": "CMakeFiles/DtDrvApp.dir/DtDrvApp.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/PPM/PPM_Addr.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/PPM/PPM_Addr.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/PPM/PPM_Addr.cpp", "output": "CMakeFiles/DtDrvApp.dir/PPM/PPM_Addr.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/PPM/PPM_Client.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/PPM/PPM_Client.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/PPM/PPM_Client.cpp", "output": "CMakeFiles/DtDrvApp.dir/PPM/PPM_Client.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/PPM/PPM_Event_Handler.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/PPM/PPM_Event_Handler.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/PPM/PPM_Event_Handler.cpp", "output": "CMakeFiles/DtDrvApp.dir/PPM/PPM_Event_Handler.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/PPM/PPM_Flag_Manip.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/PPM/PPM_Flag_Manip.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/PPM/PPM_Flag_Manip.cpp", "output": "CMakeFiles/DtDrvApp.dir/PPM/PPM_Flag_Manip.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/PPM/PPM_Handle_Set.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/PPM/PPM_Handle_Set.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/PPM/PPM_Handle_Set.cpp", "output": "CMakeFiles/DtDrvApp.dir/PPM/PPM_Handle_Set.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/PPM/PPM_INET_Addr.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/PPM/PPM_INET_Addr.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/PPM/PPM_INET_Addr.cpp", "output": "CMakeFiles/DtDrvApp.dir/PPM/PPM_INET_Addr.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/PPM/PPM_IPC_SAP.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/PPM/PPM_IPC_SAP.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/PPM/PPM_IPC_SAP.cpp", "output": "CMakeFiles/DtDrvApp.dir/PPM/PPM_IPC_SAP.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/PPM/PPM_Log_Msg.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/PPM/PPM_Log_Msg.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/PPM/PPM_Log_Msg.cpp", "output": "CMakeFiles/DtDrvApp.dir/PPM/PPM_Log_Msg.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/PPM/PPM_Log_Record.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/PPM/PPM_Log_Record.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/PPM/PPM_Log_Record.cpp", "output": "CMakeFiles/DtDrvApp.dir/PPM/PPM_Log_Record.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/PPM/PPM_OS.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/PPM/PPM_OS.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/PPM/PPM_OS.cpp", "output": "CMakeFiles/DtDrvApp.dir/PPM/PPM_OS.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/PPM/PPM_OS_Error.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/PPM/PPM_OS_Error.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/PPM/PPM_OS_Error.cpp", "output": "CMakeFiles/DtDrvApp.dir/PPM/PPM_OS_Error.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/PPM/PPM_OS_String.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/PPM/PPM_OS_String.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/PPM/PPM_OS_String.cpp", "output": "CMakeFiles/DtDrvApp.dir/PPM/PPM_OS_String.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/PPM/PPM_Reactor.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/PPM/PPM_Reactor.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/PPM/PPM_Reactor.cpp", "output": "CMakeFiles/DtDrvApp.dir/PPM/PPM_Reactor.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/PPM/PPM_SOCK.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/PPM/PPM_SOCK.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/PPM/PPM_SOCK.cpp", "output": "CMakeFiles/DtDrvApp.dir/PPM/PPM_SOCK.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/PPM/PPM_SOCK_Acceptor.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/PPM/PPM_SOCK_Acceptor.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/PPM/PPM_SOCK_Acceptor.cpp", "output": "CMakeFiles/DtDrvApp.dir/PPM/PPM_SOCK_Acceptor.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/PPM/PPM_SOCK_Buffer.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/PPM/PPM_SOCK_Buffer.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/PPM/PPM_SOCK_Buffer.cpp", "output": "CMakeFiles/DtDrvApp.dir/PPM/PPM_SOCK_Buffer.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/PPM/PPM_SOCK_Connector.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/PPM/PPM_SOCK_Connector.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/PPM/PPM_SOCK_Connector.cpp", "output": "CMakeFiles/DtDrvApp.dir/PPM/PPM_SOCK_Connector.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/PPM/PPM_SOCK_Dgram.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/PPM/PPM_SOCK_Dgram.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/PPM/PPM_SOCK_Dgram.cpp", "output": "CMakeFiles/DtDrvApp.dir/PPM/PPM_SOCK_Dgram.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/PPM/PPM_SOCK_Stream.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/PPM/PPM_SOCK_Stream.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/PPM/PPM_SOCK_Stream.cpp", "output": "CMakeFiles/DtDrvApp.dir/PPM/PPM_SOCK_Stream.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/PPM/PPM_Select_Reactor.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/PPM/PPM_Select_Reactor.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/PPM/PPM_Select_Reactor.cpp", "output": "CMakeFiles/DtDrvApp.dir/PPM/PPM_Select_Reactor.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/PPM/PPM_Select_Reactor_Base.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/PPM/PPM_Select_Reactor_Base.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/PPM/PPM_Select_Reactor_Base.cpp", "output": "CMakeFiles/DtDrvApp.dir/PPM/PPM_Select_Reactor_Base.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/PPM/PPM_Select_Reactor_T.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/PPM/PPM_Select_Reactor_T.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/PPM/PPM_Select_Reactor_T.cpp", "output": "CMakeFiles/DtDrvApp.dir/PPM/PPM_Select_Reactor_T.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/PPM/PPM_SelfMonitor.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/PPM/PPM_SelfMonitor.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/PPM/PPM_SelfMonitor.cpp", "output": "CMakeFiles/DtDrvApp.dir/PPM/PPM_SelfMonitor.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/PPM/PPM_Signal.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/PPM/PPM_Signal.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/PPM/PPM_Signal.cpp", "output": "CMakeFiles/DtDrvApp.dir/PPM/PPM_Signal.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/PPM/PPM_Synch.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/PPM/PPM_Synch.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/PPM/PPM_Synch.cpp", "output": "CMakeFiles/DtDrvApp.dir/PPM/PPM_Synch.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/PPM/PPM_Tcp_Client.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/PPM/PPM_Tcp_Client.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/PPM/PPM_Tcp_Client.cpp", "output": "CMakeFiles/DtDrvApp.dir/PPM/PPM_Tcp_Client.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/PPM/PPM_Thread.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/PPM/PPM_Thread.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/PPM/PPM_Thread.cpp", "output": "CMakeFiles/DtDrvApp.dir/PPM/PPM_Thread.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/PPM/PPM_Thread_Adapter.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/PPM/PPM_Thread_Adapter.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/PPM/PPM_Thread_Adapter.cpp", "output": "CMakeFiles/DtDrvApp.dir/PPM/PPM_Thread_Adapter.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/PPM/PPM_Thread_Manager.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/PPM/PPM_Thread_Manager.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/PPM/PPM_Thread_Manager.cpp", "output": "CMakeFiles/DtDrvApp.dir/PPM/PPM_Thread_Manager.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/PPM/PPM_Time_Value.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/PPM/PPM_Time_Value.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/PPM/PPM_Time_Value.cpp", "output": "CMakeFiles/DtDrvApp.dir/PPM/PPM_Time_Value.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/PPM/PPM_UDP_Connector.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/PPM/PPM_UDP_Connector.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/PPM/PPM_UDP_Connector.cpp", "output": "CMakeFiles/DtDrvApp.dir/PPM/PPM_UDP_Connector.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/src/CommSource/ColumnDef.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnDef.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnDef.cpp", "output": "CMakeFiles/DtDrvApp.dir/src/CommSource/ColumnDef.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/src/CommSource/ColumnResult/ColumnResult.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult/ColumnResult.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult/ColumnResult.cpp", "output": "CMakeFiles/DtDrvApp.dir/src/CommSource/ColumnResult/ColumnResult.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/src/CommSource/ColumnResult/ParamParser.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult/ParamParser.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult/ParamParser.cpp", "output": "CMakeFiles/DtDrvApp.dir/src/CommSource/ColumnResult/ParamParser.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/src/CommSource/DBColumnDef.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/DBColumnDef.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/DBColumnDef.cpp", "output": "CMakeFiles/DtDrvApp.dir/src/CommSource/DBColumnDef.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/src/CommSource/DtdrvApp_Public_Var.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/DtdrvApp_Public_Var.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/DtdrvApp_Public_Var.cpp", "output": "CMakeFiles/DtDrvApp.dir/src/CommSource/DtdrvApp_Public_Var.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/src/CommSource/ParaResult.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ParaResult.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ParaResult.cpp", "output": "CMakeFiles/DtDrvApp.dir/src/CommSource/ParaResult.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/src/CommSource/ResultDef.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ResultDef.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ResultDef.cpp", "output": "CMakeFiles/DtDrvApp.dir/src/CommSource/ResultDef.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/src/CommSource/SearchSock.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/SearchSock.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/SearchSock.cpp", "output": "CMakeFiles/DtDrvApp.dir/src/CommSource/SearchSock.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/src/CommSource/Search_PI.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/Search_PI.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/Search_PI.cpp", "output": "CMakeFiles/DtDrvApp.dir/src/CommSource/Search_PI.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/src/CommSource/SockFactory.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/SockFactory.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/SockFactory.cpp", "output": "CMakeFiles/DtDrvApp.dir/src/CommSource/SockFactory.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/src/ConfigSetting.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/src/ConfigSetting.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/ConfigSetting.cpp", "output": "CMakeFiles/DtDrvApp.dir/src/ConfigSetting.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/src/DataBaseInfo.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/src/DataBaseInfo.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataBaseInfo.cpp", "output": "CMakeFiles/DtDrvApp.dir/src/DataBaseInfo.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/src/DataItem/DataItem.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem/DataItem.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem/DataItem.cpp", "output": "CMakeFiles/DtDrvApp.dir/src/DataItem/DataItem.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/src/DtDrvAppMain.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/src/DtDrvAppMain.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/DtDrvAppMain.cpp", "output": "CMakeFiles/DtDrvApp.dir/src/DtDrvAppMain.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/src/DtSockServer.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/src/DtSockServer.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/DtSockServer.cpp", "output": "CMakeFiles/DtDrvApp.dir/src/DtSockServer.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/src/DtSockServerActor.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/src/DtSockServerActor.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/DtSockServerActor.cpp", "output": "CMakeFiles/DtDrvApp.dir/src/DtSockServerActor.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/src/MiniDog.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/src/MiniDog.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/MiniDog.cpp", "output": "CMakeFiles/DtDrvApp.dir/src/MiniDog.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/src/Search_DIY/DIY_Sock/SockDIYDeal.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock/SockDIYDeal.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock/SockDIYDeal.cpp", "output": "CMakeFiles/DtDrvApp.dir/src/Search_DIY/DIY_Sock/SockDIYDeal.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/src/Search_DIY/DataSearch_Area/SearchArea.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea.cpp", "output": "CMakeFiles/DtDrvApp.dir/src/Search_DIY/DataSearch_Area/SearchArea.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/src/Search_DIY/DataSearch_Area/SearchArea_Concrete/SearchAreaStat_Event.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete/SearchAreaStat_Event.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete/SearchAreaStat_Event.cpp", "output": "CMakeFiles/DtDrvApp.dir/src/Search_DIY/DataSearch_Area/SearchArea_Concrete/SearchAreaStat_Event.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/src/Search_DIY/DataSearch_Area/SearchArea_Concrete/SearchAreaStat_Kpi.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete/SearchAreaStat_Kpi.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete/SearchAreaStat_Kpi.cpp", "output": "CMakeFiles/DtDrvApp.dir/src/Search_DIY/DataSearch_Area/SearchArea_Concrete/SearchAreaStat_Kpi.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/src/Search_DIY/DataSearch_Area/SearchArea_Concrete/SearchArea_Cell_Grid.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete/SearchArea_Cell_Grid.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete/SearchArea_Cell_Grid.cpp", "output": "CMakeFiles/DtDrvApp.dir/src/Search_DIY/DataSearch_Area/SearchArea_Concrete/SearchArea_Cell_Grid.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/src/Search_DIY/DataSearch_Area/SearchArea_Concrete/SearchArea_Cell_Kpi.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete/SearchArea_Cell_Kpi.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete/SearchArea_Cell_Kpi.cpp", "output": "CMakeFiles/DtDrvApp.dir/src/Search_DIY/DataSearch_Area/SearchArea_Concrete/SearchArea_Cell_Kpi.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/src/Search_DIY/DataSearch_Area/SearchArea_Concrete/SearchArea_Event.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete/SearchArea_Event.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete/SearchArea_Event.cpp", "output": "CMakeFiles/DtDrvApp.dir/src/Search_DIY/DataSearch_Area/SearchArea_Concrete/SearchArea_Event.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/src/Search_DIY/DataSearch_Area/SearchArea_Concrete/SearchArea_Event_ES.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete/SearchArea_Event_ES.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete/SearchArea_Event_ES.cpp", "output": "CMakeFiles/DtDrvApp.dir/src/Search_DIY/DataSearch_Area/SearchArea_Concrete/SearchArea_Event_ES.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/src/Search_DIY/DataSearch_Area/SearchArea_Concrete/SearchArea_Grid.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete/SearchArea_Grid.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete/SearchArea_Grid.cpp", "output": "CMakeFiles/DtDrvApp.dir/src/Search_DIY/DataSearch_Area/SearchArea_Concrete/SearchArea_Grid.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/src/Search_DIY/DataSearch_Area/SearchArea_Concrete/SearchArea_Gsmr_Event.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete/SearchArea_Gsmr_Event.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete/SearchArea_Gsmr_Event.cpp", "output": "CMakeFiles/DtDrvApp.dir/src/Search_DIY/DataSearch_Area/SearchArea_Concrete/SearchArea_Gsmr_Event.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/src/Search_DIY/DataSearch_Area/SearchArea_Concrete/SearchArea_Log.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete/SearchArea_Log.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete/SearchArea_Log.cpp", "output": "CMakeFiles/DtDrvApp.dir/src/Search_DIY/DataSearch_Area/SearchArea_Concrete/SearchArea_Log.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/src/Search_DIY/DataSearch_Area/SearchArea_Concrete/SearchArea_NB_Grid.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete/SearchArea_NB_Grid.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete/SearchArea_NB_Grid.cpp", "output": "CMakeFiles/DtDrvApp.dir/src/Search_DIY/DataSearch_Area/SearchArea_Concrete/SearchArea_NB_Grid.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/src/Search_DIY/DataSearch_Area/SearchArea_Concrete/SearchArea_Sample.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete/SearchArea_Sample.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete/SearchArea_Sample.cpp", "output": "CMakeFiles/DtDrvApp.dir/src/Search_DIY/DataSearch_Area/SearchArea_Concrete/SearchArea_Sample.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/src/Search_DIY/DataSearch_Area/SearchArea_Concrete/SearchArea_Sample_CoverLeak.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete/SearchArea_Sample_CoverLeak.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete/SearchArea_Sample_CoverLeak.cpp", "output": "CMakeFiles/DtDrvApp.dir/src/Search_DIY/DataSearch_Area/SearchArea_Concrete/SearchArea_Sample_CoverLeak.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/src/Search_DIY/DataSearch_Area/SearchArea_Concrete/SearchArea_Scan_Grid.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete/SearchArea_Scan_Grid.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete/SearchArea_Scan_Grid.cpp", "output": "CMakeFiles/DtDrvApp.dir/src/Search_DIY/DataSearch_Area/SearchArea_Concrete/SearchArea_Scan_Grid.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/src/Search_DIY/DataSearch_Area/SearchArea_Concrete/SearchLog_Kpi.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete/SearchLog_Kpi.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete/SearchLog_Kpi.cpp", "output": "CMakeFiles/DtDrvApp.dir/src/Search_DIY/DataSearch_Area/SearchArea_Concrete/SearchLog_Kpi.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/src/Search_DIY/DataSearch_Area/SearchArea_Sub.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Sub.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Sub.cpp", "output": "CMakeFiles/DtDrvApp.dir/src/Search_DIY/DataSearch_Area/SearchArea_Sub.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/src/Search_DIY/DataSearch_Cell/SearchCell.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell.cpp", "output": "CMakeFiles/DtDrvApp.dir/src/Search_DIY/DataSearch_Cell/SearchCell.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete/SearchCell_Sample.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete/SearchCell_Sample.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete/SearchCell_Sample.cpp", "output": "CMakeFiles/DtDrvApp.dir/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete/SearchCell_Sample.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/src/Search_DIY/DataSearch_Log/SearchLog.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog.cpp", "output": "CMakeFiles/DtDrvApp.dir/src/Search_DIY/DataSearch_Log/SearchLog.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/src/Search_DIY/DataSearch_Log/SearchLog_Concrete/SearchLog_Event.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete/SearchLog_Event.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete/SearchLog_Event.cpp", "output": "CMakeFiles/DtDrvApp.dir/src/Search_DIY/DataSearch_Log/SearchLog_Concrete/SearchLog_Event.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/src/Search_DIY/DataSearch_Log/SearchLog_Concrete/SearchLog_Gsmr_Event.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete/SearchLog_Gsmr_Event.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete/SearchLog_Gsmr_Event.cpp", "output": "CMakeFiles/DtDrvApp.dir/src/Search_DIY/DataSearch_Log/SearchLog_Concrete/SearchLog_Gsmr_Event.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/src/Search_DIY/DataSearch_Log/SearchLog_Concrete/SearchLog_Msg.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete/SearchLog_Msg.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete/SearchLog_Msg.cpp", "output": "CMakeFiles/DtDrvApp.dir/src/Search_DIY/DataSearch_Log/SearchLog_Concrete/SearchLog_Msg.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/src/Search_DIY/DataSearch_Log/SearchLog_Concrete/SearchLog_Sample.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete/SearchLog_Sample.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete/SearchLog_Sample.cpp", "output": "CMakeFiles/DtDrvApp.dir/src/Search_DIY/DataSearch_Log/SearchLog_Concrete/SearchLog_Sample.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/src/Search_DIY/DataSearch_Model/SearchModel.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model/SearchModel.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model/SearchModel.cpp", "output": "CMakeFiles/DtDrvApp.dir/src/Search_DIY/DataSearch_Model/SearchModel.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/src/Search_DIY/DataSearch_Model/SearchModel_SqlSearch.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model/SearchModel_SqlSearch.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model/SearchModel_SqlSearch.cpp", "output": "CMakeFiles/DtDrvApp.dir/src/Search_DIY/DataSearch_Model/SearchModel_SqlSearch.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/src/Search_DIY/DataSearch_Model/SearchModel_Table.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model/SearchModel_Table.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model/SearchModel_Table.cpp", "output": "CMakeFiles/DtDrvApp.dir/src/Search_DIY/DataSearch_Model/SearchModel_Table.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/src/Search_DIY/SearchConditionMaker.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/SearchConditionMaker.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/SearchConditionMaker.cpp", "output": "CMakeFiles/DtDrvApp.dir/src/Search_DIY/SearchConditionMaker.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/src/Search_DIY/SearchDIY.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/SearchDIY.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/SearchDIY.cpp", "output": "CMakeFiles/DtDrvApp.dir/src/Search_DIY/SearchDIY.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/src/Search_DIY/SqlMaker.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/SqlMaker.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/SqlMaker.cpp", "output": "CMakeFiles/DtDrvApp.dir/src/Search_DIY/SqlMaker.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/src/Search_DIY/UserStatus.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/UserStatus.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/UserStatus.cpp", "output": "CMakeFiles/DtDrvApp.dir/src/Search_DIY/UserStatus.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/src/Search_Passed/Authentication/SockAuthenDeal.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication/SockAuthenDeal.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication/SockAuthenDeal.cpp", "output": "CMakeFiles/DtDrvApp.dir/src/Search_Passed/Authentication/SockAuthenDeal.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/src/Search_Passed/ConfigManagement/ConfigMng_Concrete/ConfigMngSock_Area.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete/ConfigMngSock_Area.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete/ConfigMngSock_Area.cpp", "output": "CMakeFiles/DtDrvApp.dir/src/Search_Passed/ConfigManagement/ConfigMng_Concrete/ConfigMngSock_Area.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/src/Search_Passed/ConfigManagement/ConfigMng_Concrete/ConfigMngSock_Cell.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete/ConfigMngSock_Cell.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete/ConfigMngSock_Cell.cpp", "output": "CMakeFiles/DtDrvApp.dir/src/Search_Passed/ConfigManagement/ConfigMng_Concrete/ConfigMngSock_Cell.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/src/Search_Passed/ConfigManagement/ConfigMng_Concrete/ConfigMngSock_Other.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete/ConfigMngSock_Other.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete/ConfigMngSock_Other.cpp", "output": "CMakeFiles/DtDrvApp.dir/src/Search_Passed/ConfigManagement/ConfigMng_Concrete/ConfigMngSock_Other.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/src/Search_Passed/ConfigManagement/ConfigMng_Sock/SockConfigMngDeal.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock/SockConfigMngDeal.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock/SockConfigMngDeal.cpp", "output": "CMakeFiles/DtDrvApp.dir/src/Search_Passed/ConfigManagement/ConfigMng_Sock/SockConfigMngDeal.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/src/Search_Passed/DBManagement/DBMng_Sock/SockDBMngDeal.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock/SockDBMngDeal.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock/SockDBMngDeal.cpp", "output": "CMakeFiles/DtDrvApp.dir/src/Search_Passed/DBManagement/DBMng_Sock/SockDBMngDeal.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete/CommMngSock_Other.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete/CommMngSock_Other.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete/CommMngSock_Other.cpp", "output": "CMakeFiles/DtDrvApp.dir/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete/CommMngSock_Other.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/src/Search_Passed/DataSearch_Community/CommunityMng_Sock/SockCommMngDeal.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock/SockCommMngDeal.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock/SockCommMngDeal.cpp", "output": "CMakeFiles/DtDrvApp.dir/src/Search_Passed/DataSearch_Community/CommunityMng_Sock/SockCommMngDeal.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/src/Search_Passed/DataSearch_Deal/Search_Concrete/SearchSock_Area.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete/SearchSock_Area.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete/SearchSock_Area.cpp", "output": "CMakeFiles/DtDrvApp.dir/src/Search_Passed/DataSearch_Deal/Search_Concrete/SearchSock_Area.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/src/Search_Passed/DataSearch_Deal/Search_Concrete/SearchSock_BlackBlock.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete/SearchSock_BlackBlock.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete/SearchSock_BlackBlock.cpp", "output": "CMakeFiles/DtDrvApp.dir/src/Search_Passed/DataSearch_Deal/Search_Concrete/SearchSock_BlackBlock.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/src/Search_Passed/DataSearch_Deal/Search_Concrete/SearchSock_Cell.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete/SearchSock_Cell.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete/SearchSock_Cell.cpp", "output": "CMakeFiles/DtDrvApp.dir/src/Search_Passed/DataSearch_Deal/Search_Concrete/SearchSock_Cell.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/src/Search_Passed/DataSearch_Deal/Search_Concrete/SearchSock_Cluster.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete/SearchSock_Cluster.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete/SearchSock_Cluster.cpp", "output": "CMakeFiles/DtDrvApp.dir/src/Search_Passed/DataSearch_Deal/Search_Concrete/SearchSock_Cluster.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/src/Search_Passed/DataSearch_Deal/Search_Concrete/SearchSock_ComplainBlock.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete/SearchSock_ComplainBlock.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete/SearchSock_ComplainBlock.cpp", "output": "CMakeFiles/DtDrvApp.dir/src/Search_Passed/DataSearch_Deal/Search_Concrete/SearchSock_ComplainBlock.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/src/Search_Passed/DataSearch_Deal/Search_Concrete/SearchSock_ES.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete/SearchSock_ES.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete/SearchSock_ES.cpp", "output": "CMakeFiles/DtDrvApp.dir/src/Search_Passed/DataSearch_Deal/Search_Concrete/SearchSock_ES.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/src/Search_Passed/DataSearch_Deal/Search_Concrete/SearchSock_Health.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete/SearchSock_Health.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete/SearchSock_Health.cpp", "output": "CMakeFiles/DtDrvApp.dir/src/Search_Passed/DataSearch_Deal/Search_Concrete/SearchSock_Health.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/src/Search_Passed/DataSearch_Deal/Search_Concrete/SearchSock_LogFile.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete/SearchSock_LogFile.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete/SearchSock_LogFile.cpp", "output": "CMakeFiles/DtDrvApp.dir/src/Search_Passed/DataSearch_Deal/Search_Concrete/SearchSock_LogFile.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/src/Search_Passed/DataSearch_Deal/Search_Concrete/SearchSock_LogReview.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete/SearchSock_LogReview.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete/SearchSock_LogReview.cpp", "output": "CMakeFiles/DtDrvApp.dir/src/Search_Passed/DataSearch_Deal/Search_Concrete/SearchSock_LogReview.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/src/Search_Passed/DataSearch_Deal/Search_Concrete/SearchSock_Other.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete/SearchSock_Other.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete/SearchSock_Other.cpp", "output": "CMakeFiles/DtDrvApp.dir/src/Search_Passed/DataSearch_Deal/Search_Concrete/SearchSock_Other.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/src/Search_Passed/DataSearch_Deal/Search_Sock/SockSearchDeal.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock/SockSearchDeal.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock/SockSearchDeal.cpp", "output": "CMakeFiles/DtDrvApp.dir/src/Search_Passed/DataSearch_Deal/Search_Sock/SockSearchDeal.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete/StatisticSock_Area.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete/StatisticSock_Area.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete/StatisticSock_Area.cpp", "output": "CMakeFiles/DtDrvApp.dir/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete/StatisticSock_Area.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete/StatisticSock_Cell.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete/StatisticSock_Cell.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete/StatisticSock_Cell.cpp", "output": "CMakeFiles/DtDrvApp.dir/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete/StatisticSock_Cell.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete/StatisticSock_Log.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete/StatisticSock_Log.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete/StatisticSock_Log.cpp", "output": "CMakeFiles/DtDrvApp.dir/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete/StatisticSock_Log.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete/StatisticSock_Other.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete/StatisticSock_Other.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete/StatisticSock_Other.cpp", "output": "CMakeFiles/DtDrvApp.dir/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete/StatisticSock_Other.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/src/Search_Passed/DataSearch_Statistic/Statistic_Sock/SockStatisticDeal.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock/SockStatisticDeal.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock/SockStatisticDeal.cpp", "output": "CMakeFiles/DtDrvApp.dir/src/Search_Passed/DataSearch_Statistic/Statistic_Sock/SockStatisticDeal.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/src/Search_Passed/TestDataManagement/TestDataMng_Concrete/TestDataMngSock_Other.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete/TestDataMngSock_Other.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete/TestDataMngSock_Other.cpp", "output": "CMakeFiles/DtDrvApp.dir/src/Search_Passed/TestDataManagement/TestDataMng_Concrete/TestDataMngSock_Other.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/src/Search_Passed/TestDataManagement/TestDataMng_Sock/SockTestDataMngDeal.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock/SockTestDataMngDeal.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock/SockTestDataMngDeal.cpp", "output": "CMakeFiles/DtDrvApp.dir/src/Search_Passed/TestDataManagement/TestDataMng_Sock/SockTestDataMngDeal.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/src/Search_Passed/UserManagement/UserMng_Concrete/UserMngSock_Other.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete/UserMngSock_Other.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete/UserMngSock_Other.cpp", "output": "CMakeFiles/DtDrvApp.dir/src/Search_Passed/UserManagement/UserMng_Concrete/UserMngSock_Other.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/src/Search_Passed/UserManagement/UserMng_Sock/SockUserMngDeal.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock/SockUserMngDeal.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock/SockUserMngDeal.cpp", "output": "CMakeFiles/DtDrvApp.dir/src/Search_Passed/UserManagement/UserMng_Sock/SockUserMngDeal.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/src/Utility.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/src/Utility.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/src/Utility.cpp", "output": "CMakeFiles/DtDrvApp.dir/src/Utility.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/stdclass/CycBuffer.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/CycBuffer.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/CycBuffer.cpp", "output": "CMakeFiles/DtDrvApp.dir/stdclass/CycBuffer.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/stdclass/StdBuffer.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/StdBuffer.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/StdBuffer.cpp", "output": "CMakeFiles/DtDrvApp.dir/stdclass/StdBuffer.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/stdclass/StdCommond.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/StdCommond.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/StdCommond.cpp", "output": "CMakeFiles/DtDrvApp.dir/stdclass/StdCommond.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/stdclass/StdDataObject.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/StdDataObject.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/StdDataObject.cpp", "output": "CMakeFiles/DtDrvApp.dir/stdclass/StdDataObject.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/stdclass/StdFile.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/StdFile.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/StdFile.cpp", "output": "CMakeFiles/DtDrvApp.dir/stdclass/StdFile.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/stdclass/StdFileFind.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/StdFileFind.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/StdFileFind.cpp", "output": "CMakeFiles/DtDrvApp.dir/stdclass/StdFileFind.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/stdclass/StdFileMapping.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/StdFileMapping.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/StdFileMapping.cpp", "output": "CMakeFiles/DtDrvApp.dir/stdclass/StdFileMapping.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/stdclass/StdLock.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/StdLock.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/StdLock.cpp", "output": "CMakeFiles/DtDrvApp.dir/stdclass/StdLock.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/stdclass/StdMonitor.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/StdMonitor.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/StdMonitor.cpp", "output": "CMakeFiles/DtDrvApp.dir/stdclass/StdMonitor.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/stdclass/StdNetBuffer.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/StdNetBuffer.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/StdNetBuffer.cpp", "output": "CMakeFiles/DtDrvApp.dir/stdclass/StdNetBuffer.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/stdclass/StdProfile.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/StdProfile.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/StdProfile.cpp", "output": "CMakeFiles/DtDrvApp.dir/stdclass/StdProfile.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/stdclass/StdThread.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/StdThread.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/StdThread.cpp", "output": "CMakeFiles/DtDrvApp.dir/stdclass/StdThread.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/stdclass/StdTime.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/StdTime.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/StdTime.cpp", "output": "CMakeFiles/DtDrvApp.dir/stdclass/StdTime.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/utils/Strutils.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/utils/Strutils.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/utils/Strutils.cpp", "output": "CMakeFiles/DtDrvApp.dir/utils/Strutils.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/utils/linux_api.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/utils/linux_api.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/utils/linux_api.cpp", "output": "CMakeFiles/DtDrvApp.dir/utils/linux_api.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/stdclass/freetds/StdBcp_Sybase.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds/StdBcp_Sybase.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds/StdBcp_Sybase.cpp", "output": "CMakeFiles/DtDrvApp.dir/stdclass/freetds/StdBcp_Sybase.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/stdclass/freetds/StdFreetds.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds/StdFreetds.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds/StdFreetds.cpp", "output": "CMakeFiles/DtDrvApp.dir/stdclass/freetds/StdFreetds.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/stdclass/freetds/StdSqlOpHelper.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds/StdSqlOpHelper.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds/StdSqlOpHelper.cpp", "output": "CMakeFiles/DtDrvApp.dir/stdclass/freetds/StdSqlOpHelper.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/stdclass/freetds/StdSybase_Blk.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds/StdSybase_Blk.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds/StdSybase_Blk.cpp", "output": "CMakeFiles/DtDrvApp.dir/stdclass/freetds/StdSybase_Blk.cpp.o"}, {"directory": "/home/<USER>/code/DtDrvApp_AI_CSharp/build", "command": "c++ -I/home/<USER>/code/DtDrvApp_AI_CSharp/src -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/CommSource/ColumnResult -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DIY_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Model -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Area/SearchArea_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Log/SearchLog_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_DIY/DataSearch_Cell/SearchCell_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/DataItem -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/Authentication -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/ConfigManagement/ConfigMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Community/CommunityMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Deal/Search_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DataSearch_Statistic/Statistic_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/DBManagement/DBMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/TestDataManagement/TestDataMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Concrete -I/home/<USER>/code/DtDrvApp_AI_CSharp/src/Search_Passed/UserManagement/UserMng_Sock -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass -I/home/<USER>/code/DtDrvApp_AI_CSharp/PPM -I/home/<USER>/code/DtDrvApp_AI_CSharp/EncodeUtils -I/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds -I/home/<USER>/code/DtDrvApp_AI_CSharp/stdclass/freetds -Wall -g -std=gnu++11 -o CMakeFiles/DtDrvApp.dir/utils/freetds/DataBaseDeal.cpp.o -c /home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds/DataBaseDeal.cpp", "file": "/home/<USER>/code/DtDrvApp_AI_CSharp/utils/freetds/DataBaseDeal.cpp", "output": "CMakeFiles/DtDrvApp.dir/utils/freetds/DataBaseDeal.cpp.o"}]