900	SEARCHTYPE_AUTHUSERCHEK	1	8	userid
900	SEARCHTYPE_AUTHUSERCHEK	2	6	password
900	SEARCHTYPE_AUTHUSERCHEK	3	8	cityid
901	SEARCHTYPE_AUTHUSERFUNCGET	1	8	funcid
1000	SEARCHTYPE_LOGSEARCH	1	8	测试类型4BYTE
1000	SEARCHTYPE_LOGSEARCH	2	8	设备类型4BYTE
1000	SEARCHTYPE_LOGSEARCH	3	8	文件类型4BYTE
1000	SEARCHTYPE_LOGSEARCH	4	8	业务类型4BYTE
1000	SEARCHTYPE_LOGSEARCH	5	8	运营商类型4BYTE
1000	SEARCHTYPE_LOGSEARCH	6	8	项目类型4BYTE
1000	SEARCHTYPE_LOGSEARCH	7	8	所属轮次4BYTE
1000	SEARCHTYPE_LOGSEARCH	8	8	文件ID4BYTES
1000	SEARCHTYPE_LOGSEARCH	9	6	文件名称string
1000	SEARCHTYPE_LOGSEARCH	10	8	数据导入时间4BYTES
1000	SEARCHTYPE_LOGSEARCH	11	8	所属地域类型ID4BYTES
1000	SEARCHTYPE_LOGSEARCH	12	6	所属地域类型名称string
1000	SEARCHTYPE_LOGSEARCH	13	8	所属地域ID4BYTES
1000	SEARCHTYPE_LOGSEARCH	14	6	所属地域名称string
1000	SEARCHTYPE_LOGSEARCH	15	8	所属代维公司ID4BYTES
1000	SEARCHTYPE_LOGSEARCH	16	6	所属代维公司名称string
1000	SEARCHTYPE_LOGSEARCH	17	6	项目数据人员名称string
1000	SEARCHTYPE_LOGSEARCH	18	8	文件起始时间4BYTES
1000	SEARCHTYPE_LOGSEARCH	19	8	文件结束时间4BYTES
1000	SEARCHTYPE_LOGSEARCH	20	8	文件测试距离4BYTES
1000	SEARCHTYPE_LOGSEARCH	21	8	文件左上经度4BYTES
1000	SEARCHTYPE_LOGSEARCH	22	8	文件左上纬度4BYTES
1000	SEARCHTYPE_LOGSEARCH	23	8	文件右下经度4BYTES
1000	SEARCHTYPE_LOGSEARCH	24	8	文件右下纬度4BYTES
1000	SEARCHTYPE_LOGSEARCH	25	8	文件消息个数4BYTES
1000	SEARCHTYPE_LOGSEARCH	26	8	文件事件个数4BYTES
1000	SEARCHTYPE_LOGSEARCH	27	8	文件采样点个数4BYTES
1000	SEARCHTYPE_LOGSEARCH	28	8	文件大小4BYTES
1000	SEARCHTYPE_LOGSEARCH	29	6	文件保存路径String
1000	SEARCHTYPE_LOGSEARCH	30	6	文件采样点信息表String
1000	SEARCHTYPE_LOGSEARCH	31	6	文件消息信息表String
1000	SEARCHTYPE_LOGSEARCH	32	6	文件事件信息表String
1000	SEARCHTYPE_LOGSEARCH	33	6	logtbname string
1001	SEARCHTYPE_LOGSEARCH_MSG_DETAIL	1	8	文件ID4BYTES
1001	SEARCHTYPE_LOGSEARCH_MSG_DETAIL	2	8	消息序列ID（4BYTES）
1001	SEARCHTYPE_LOGSEARCH_MSG_DETAIL	3	8	消息时间（4BYTES）
1001	SEARCHTYPE_LOGSEARCH_MSG_DETAIL	4	1	手机序列（1BYTE）
1001	SEARCHTYPE_LOGSEARCH_MSG_DETAIL	5	2	消息时间毫秒（2BYTES）
1001	SEARCHTYPE_LOGSEARCH_MSG_DETAIL	6	8	消息ID（4BYTES）
1001	SEARCHTYPE_LOGSEARCH_MSG_DETAIL	7	1	消息方向（1BYTE）
1002	SEARCHTYPE_LOGSEARCH_MSG_DEPTH	1	8	文件ID4BYTES
1002	SEARCHTYPE_LOGSEARCH_MSG_DEPTH	2	8	消息序列ID（4BYTES）
1002	SEARCHTYPE_LOGSEARCH_MSG_DEPTH	3	8	消息时间（4BYTES）
1002	SEARCHTYPE_LOGSEARCH_MSG_DEPTH	4	1	手机序列（1BYTE）
1002	SEARCHTYPE_LOGSEARCH_MSG_DEPTH	5	2	消息时间毫秒（2BYTES）
1002	SEARCHTYPE_LOGSEARCH_MSG_DEPTH	6	8	消息ID（4BYTES）
1002	SEARCHTYPE_LOGSEARCH_MSG_DEPTH	7	1	消息方向（1BYTE）
1002	SEARCHTYPE_LOGSEARCH_MSG_DEPTH	8	9	消息二进制码流（2BYTES码流长度后续为二进制码）
1003	SEARCHTYPE_LOGSEARCH_EVENT	1	8	文件ID4BYTES
1003	SEARCHTYPE_LOGSEARCH_EVENT	2	8	事件点id（4BYTES）用于与采样点ID的联动
1003	SEARCHTYPE_LOGSEARCH_EVENT	3	8	事件点时间（4BYTES）
1003	SEARCHTYPE_LOGSEARCH_EVENT	4	1	手机序列（1BYTE）
1003	SEARCHTYPE_LOGSEARCH_EVENT	5	2	毫秒
1003	SEARCHTYPE_LOGSEARCH_EVENT	6	8	经度（4BYTES）
1003	SEARCHTYPE_LOGSEARCH_EVENT	7	8	纬度（4BYTES）
1003	SEARCHTYPE_LOGSEARCH_EVENT	8	8	事件ID（4BYTES）
1003	SEARCHTYPE_LOGSEARCH_EVENT	9	8	iLAC（4BYTES）主服小区
1003	SEARCHTYPE_LOGSEARCH_EVENT	10	2	wRAC（2BYTES）
1003	SEARCHTYPE_LOGSEARCH_EVENT	11	8	iCI（4BYTES）
1003	SEARCHTYPE_LOGSEARCH_EVENT	12	8	iTargetLAC（4BYTES）目标小区如切换小区重选等
1003	SEARCHTYPE_LOGSEARCH_EVENT	13	2	wTargetRAC（2BYTES）
1003	SEARCHTYPE_LOGSEARCH_EVENT	14	8	iTargetCI（4BYTES）
1003	SEARCHTYPE_LOGSEARCH_EVENT	15	14	事件value1(4BYTES)
1003	SEARCHTYPE_LOGSEARCH_EVENT	16	14	事件value2(4BYTES)
1003	SEARCHTYPE_LOGSEARCH_EVENT	17	14	事件value3(4BYTES)
1003	SEARCHTYPE_LOGSEARCH_EVENT	18	14	事件value4(4BYTES)
1003	SEARCHTYPE_LOGSEARCH_EVENT	19	14	事件value5(4BYTES)
1003	SEARCHTYPE_LOGSEARCH_EVENT	20	14	事件value6(4BYTES)
1003	SEARCHTYPE_LOGSEARCH_EVENT	21	14	事件value7(4BYTES)
1003	SEARCHTYPE_LOGSEARCH_EVENT	22	14	事件value8(4BYTES)
1003	SEARCHTYPE_LOGSEARCH_EVENT	23	14	事件value9(4BYTES)
1003	SEARCHTYPE_LOGSEARCH_EVENT	24	14	事件value10(4BYTES)
1004	SEARCHTYPE_LOGSEARCH_SAMPLE_SUMMARY	1	8	文件ID4BYTES
1004	SEARCHTYPE_LOGSEARCH_SAMPLE_SUMMARY	2	8	采样点id（4BYTES）
1004	SEARCHTYPE_LOGSEARCH_SAMPLE_SUMMARY	3	8	采样点时间（4BYTES）
1004	SEARCHTYPE_LOGSEARCH_SAMPLE_SUMMARY	4	1	手机序列（1BYTE）
1004	SEARCHTYPE_LOGSEARCH_SAMPLE_SUMMARY	5	8	经度（4BYTES）
1004	SEARCHTYPE_LOGSEARCH_SAMPLE_SUMMARY	6	8	纬度（4BYTES）
1004	SEARCHTYPE_LOGSEARCH_SAMPLE_SUMMARY	7	2	wRxLevSub（2BYTES）
1004	SEARCHTYPE_LOGSEARCH_SAMPLE_SUMMARY	8	1	bRxQualSub（1BYTE）
1004	SEARCHTYPE_LOGSEARCH_SAMPLE_SUMMARY	9	8	iLAC（4BYTES）
1004	SEARCHTYPE_LOGSEARCH_SAMPLE_SUMMARY	10	8	iCI（4BYTES）
1004	SEARCHTYPE_LOGSEARCH_SAMPLE_SUMMARY	11	2	wRAC（2BYTES）
1004	SEARCHTYPE_LOGSEARCH_SAMPLE_SUMMARY	12	2	wBCCH（2BYTES）
1004	SEARCHTYPE_LOGSEARCH_SAMPLE_SUMMARY	13	2	wN1_BCCH（2BYTES）
1004	SEARCHTYPE_LOGSEARCH_SAMPLE_SUMMARY	14	1	bN1_BSIC（1BYTE）
1004	SEARCHTYPE_LOGSEARCH_SAMPLE_SUMMARY	15	2	wN1_RxLev（2BYTES）
1004	SEARCHTYPE_LOGSEARCH_SAMPLE_SUMMARY	16	2	wN2_BCCH（2BYTES）
1004	SEARCHTYPE_LOGSEARCH_SAMPLE_SUMMARY	17	1	bN2_BSIC（1BYTE）
1004	SEARCHTYPE_LOGSEARCH_SAMPLE_SUMMARY	18	2	wN2_RxLev（2BYTES）
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	1	8	文件ID4BYTES
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	2	8	采样点id（4BYTES）
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	3	8	采样点时间（4BYTES）
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	4	1	手机序列（1BYTE）
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	5	8	经度（4BYTES）
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	6	8	纬度（4BYTES）
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	7	2	wRxLevSub（2BYTES）
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	8	1	bRxQualSub（1BYTE）
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	9	8	iLAC（4BYTES）
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	10	8	iCI（4BYTES）
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	11	2	wRAC（2BYTES）
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	12	2	wBCCH（2BYTES）
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	13	2	wN1_BCCH（2BYTES）
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	14	1	bN1_BSIC（1BYTE）
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	15	2	wN1_RxLev（2BYTES）
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	16	2	wN2_BCCH（2BYTES）
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	17	1	bN2_BSIC（1BYTE）
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	18	2	wN2_RxLev（2BYTES）
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	19	2	采样点时间毫秒2BYTES
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	20	2	wmode2BYTES
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	21	1	bBSIC1BYTE
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	22	1	bCellGPRSSupport1BYTE
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	23	1	bCellEGPRSSupport1BYTE
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	24	2	wRxLevBCCH2BYTES
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	25	2	wRxLevFull2BYTES
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	26	1	bRxQualFull1BYTE
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	27	2	wSQI2BYTES
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	28	1	bMOSvalue1BYTE
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	29	1	bTA1BYTE
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	30	1	bMsTxPower1BYTE
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	31	8	iN1_LAC4BYTES
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	32	8	iN1_CI4BYTES
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	33	8	iN2_LAC4BYTES
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	34	8	iN2_CI4BYTES
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	35	2	wN3_BCCH2BYTES
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	36	1	bN3_BSIC1BYTE
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	37	2	wN3_RxLev2BYTES
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	38	8	iN3_LAC4BYTES
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	39	8	iN3_CI4BYTES
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	40	2	wN4_BCCH2BYTES
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	41	1	bN4_BSIC1BYTE
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	42	2	wN4_RxLev2BYTES
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	43	8	iN4_LAC4BYTES
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	44	8	iN4_CI4BYTES
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	45	2	wN5_BCCH2BYTES
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	46	1	bN5_BSIC1BYTE
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	47	2	wN5_RxLev2BYTES
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	48	8	iN5_LAC4BYTES
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	49	8	iN5_CI4BYTES
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	50	2	wN6_BCCH2BYTES
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	51	1	bN6_BSIC1BYTE
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	52	2	wN6_RxLev2BYTES
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	53	8	iN6_LAC4BYTES
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	54	8	iN6_CI4BYTES
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	55	1	SpeechCodec1BYTE
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	56	5	PESQ
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	57	8	NumberOfTSUsed4BYTE
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	58	5	BERfloat
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	59	5	bFERFullfloat
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	60	5	bFERSubfloat
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	61	1	bDTX1BYTE
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	62	2	wRLTMax2BYTES
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	63	2	wRLTCur2BYTES
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	64	1	bCodingScheme_DL1BYTE
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	65	1	bCodingScheme_UL1BYTE
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	66	1	bEGPRS_BEP_Mean1BYTE
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	67	1	bModulation_DL1BYTE
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	68	1	bModulation_UL1BYTE
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	69	5	RLC_BLER_Dlfloat
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	70	5	RLC_Throughput_Dlfloat
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	71	5	AppThroughputDLfloat
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	72	8	RLC_Bytes_Received_DL
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	73	8	AppBytesReceived
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	74	2	wTCH2BYTES
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	75	1	bHP1BYTE
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	76	2	H1_C_I_ARFCN2BYTES
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	77	2	H1_C_I_Rxlev2BYTES
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	78	2	H1_C_I2BYTES
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	79	2	H2_C_I_ARFCN2BYTES
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	80	2	H2_C_I_Rxlev2BYTES
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	81	2	H2_C_I2BYTES
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	82	2	H3_C_I_ARFCN2BYTES
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	83	2	H3_C_I_Rxlev2BYTES
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	84	2	H3_C_I2BYTES
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	85	2	H4_C_I_ARFCN2BYTES
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	86	2	H4_C_I_Rxlev2BYTES
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	87	2	H4_C_I2BYTES
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	88	2	H5_C_I_ARFCN2BYTES
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	89	2	H5_C_I_Rxlev2BYTES
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	90	2	H5_C_I2BYTES
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	91	2	H6_C_I_ARFCN2BYTES
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	92	2	H6_C_I_Rxlev2BYTES
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	93	2	H6_C_I2BYTES
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	94	2	H7_C_I_ARFCN2BYTES
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	95	2	H7_C_I_Rxlev2BYTES
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	96	2	H7_C_I2BYTES
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	97	2	H8_C_I_ARFCN2BYTES
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	98	2	H8_C_I_Rxlev2BYTES
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	99	2	H8_C_I2BYTES
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	100	2	H9_C_I_ARFCN2BYTES
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	101	2	H9_C_I_Rxlev2BYTES
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	102	2	H9_C_I2BYTES
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	103	2	H10_C_I_ARFCN2BYTES
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	104	2	H10_C_I_Rxlev2BYTES
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	105	2	H10_C_I2BYTES
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	106	2	H11_C_I_ARFCN2BYTES
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	107	2	H11_C_I_Rxlev2BYTES
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	108	2	H11_C_I2BYTES
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	109	2	H12_C_I_ARFCN2BYTES
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	110	2	H12_C_I_Rxlev2BYTES
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	111	2	H12_C_I2BYTES
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	112	2	H13_C_I_ARFCN2BYTES
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	113	2	H13_C_I_Rxlev2BYTES
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	114	2	H13_C_I2BYTES
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	115	2	H14_C_I_ARFCN2BYTES
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	116	2	H14_C_I_Rxlev2BYTES
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	117	2	H14_C_I2BYTES
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	118	2	H15_C_I_ARFCN2BYTES
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	119	2	H15_C_I_Rxlev2BYTES
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	120	2	H15_C_I2BYTES
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	121	2	H16_C_I_ARFCN2BYTES
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	122	2	H16_C_I_Rxlev2BYTES
1005	SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	123	2	H16_C_I2BYTES
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	1	8	文件ID（4BYTES）
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	2	8	采样点id（4BYTES）
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	3	8	ialtitude(4BYTES):int
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	4	2	wMCC(2BYTES):smallint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	5	1	bMNC(1BYTE):tinyint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	6	1	bACCMIN(1BYTE):tinyint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	7	1	bCRH(1BYTE):tinyint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	8	1	bCRO(1BYTE):tinyint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	9	1	bPT(1BYTE):tinyint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	10	1	bTO(1BYTE):tinyint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	11	1	bDataMode(1BYTE):tinyint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	12	2	wT3212(2BYTES):smallint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	13	2	wT3314(2BYTES):smallint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	14	1	bTLLI(1BYTE):tinyint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	15	2	wC1(2BYTES):smallint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	16	2	wC2(2BYTES):smallint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	17	1	bAttachState(1BYTE):tinyint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	18	1	bGMMState(1BYTE):tinyint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	19	1	bGRRState(1BYTE):tinyint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	20	2	wN1_C1(2BYTES):smallint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	21	2	wN1_C2(2BYTES):smallint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	22	2	wN2_C1(2BYTES):smallint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	23	2	wN2_C2(2BYTES):smallint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	24	2	wN3_C1(2BYTES):smallint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	25	2	wN3_C2(2BYTES):smallint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	26	2	wN4_C1(2BYTES):smallint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	27	2	wN4_C2(2BYTES):smallint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	28	2	wN5_C1(2BYTES):smallint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	29	2	wN5_C2(2BYTES):smallint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	30	2	wN6_C1(2BYTES):smallint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	31	2	wN6_C2(2BYTES):smallint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	32	1	bATTIMSI(1BYTE):tinyint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	33	1	bCCCH_SDCCH_COMB(1BYTE):tinyint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	34	1	bAGBLK(1BYTE):tinyint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	35	1	bMFRMS(1BYTE):tinyint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	36	1	bMAXRET(1BYTE):tinyint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	37	1	bCB(1BYTE):tinyint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	38	1	bCBQ(1BYTE):tinyint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	39	1	bTimeSlot(1BYTE):tinyint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	40	1	bChannelType(1BYTE):tinyint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	41	1	bChannelMode(1BYTE):tinyint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	42	1	bNumberOfSubChannel(1BYTE):tinyint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	43	1	bMAIO(1BYTE):tinyint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	44	1	bHSN(1BYTE):tinyint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	45	1	bTSC(1BYTE):tinyint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	46	1	bTypeOfAllocation(1BYTE):tinyint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	47	1	bTFI_DL(1BYTE):tinyint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	48	1	bTFI_UL(1BYTE):tinyint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	49	1	bTBFOpenClose_DL(1BYTE):tinyint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	50	1	bTBFOpenClose_UL(1BYTE):tinyint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	51	1	bTimeslot_DL(1BYTE):tinyint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	52	1	bTimeslot_UL(1BYTE):tinyint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	53	1	bMAC_Mode_DL(1BYTE):tinyint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	54	1	bMAC_Mode_UL(1BYTE):tinyint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	55	1	bEGPRS_BEP_Variance(1BYTE):tinyint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	56	1	PDPSMState(1BYTE):tinyint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	57	6	PDPAccessPointName
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	58	6	PDPAddress
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	59	8	PDPContextTime(4BYTES):int
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	60	1	PDPContextsActive(1BYTE):tinyint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	61	1	PDPDelayClass(1BYTE):tinyint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	62	1	PDPReliability(1BYTE):tinyint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	63	1	PDPPrecedence(1BYTE):tinyint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	64	1	PDP_LLC_SAPI(1BYTE):tinyint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	65	1	PDPNSAPI(1BYTE):tinyint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	66	5	PDPPeakThroughput：float
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	67	5	PDPMeanThroughput：float
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	68	5	RLC_BLER_UL
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	69	8	RLC_Bytes_Sent_UL(4BYTES):int
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	70	5	RLC_Throughput_UL
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	71	5	RLC_BLK_ReTx_Ratio_UL
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	72	5	RLC_Threshold_UL
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	73	5	LLC_ReTx_Ratio_UL
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	74	5	LLC_Threshold_UL
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	75	8	AppBytesSent(4BYTES):int
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	76	5	AppThroughputUL
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	77	2	wN7_BCCHsmallint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	78	1	bN7_BSICtinyint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	79	2	wN7_RxLevsmallint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	80	2	wN8_BCCHsmallint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	81	1	bN8_BSICtinyint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	82	2	wN8_RxLevsmallint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	83	2	wN9_BCCHsmallint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	84	1	bN9_BSICtinyint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	85	2	wN9_RxLevsmallint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	86	2	wN10_BCCHsmallint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	87	1	bN10_BSICtinyint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	88	2	wN10_RxLevsmallint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	89	2	wN11_BCCHsmallint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	90	1	bN11_BSICtinyint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	91	2	wN11_RxLevsmallint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	92	2	wN12_BCCHsmallint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	93	1	bN12_BSICtinyint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	94	2	wN12_RxLevsmallint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	95	2	wN13_BCCHsmallint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	96	1	bN13_BSICtinyint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	97	2	wN13_RxLevsmallint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	98	2	wN14_BCCHsmallint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	99	1	bN14_BSICtinyint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	100	2	wN14_RxLevsmallint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	101	2	wN15_BCCHsmallint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	102	1	bN15_BSICtinyint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	103	2	wN15_RxLevsmallint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	104	2	wN16_BCCHsmallint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	105	1	bN16_BSICtinyint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	106	2	wN16_RxLevsmallint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	107	2	wN17_BCCHsmallint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	108	1	bN17_BSICtinyint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	109	2	wN17_RxLevsmallint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	110	2	wN18_BCCHsmallint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	111	1	bN18_BSICtinyint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	112	2	wN18_RxLevsmallint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	113	2	wN19_BCCHsmallint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	114	1	bN19_BSICtinyint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	115	2	wN19_RxLevsmallint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	116	2	wN20_BCCHsmallint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	117	1	bN20_BSICtinyint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	118	2	wN20_RxLevsmallint
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	119	5	PDCH_0_U_Own_Data_DLfloat
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	120	5	PDCH_1_U_Own_Data_DLfloat
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	121	5	PDCH_2_U_Own_Data_DLfloat
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	122	5	PDCH_3_U_Own_Data_DLfloat
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	123	5	PDCH_4_U_Own_Data_DLfloat
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	124	5	PDCH_5_U_Own_Data_DLfloat
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	125	5	PDCH_6_U_Own_Data_DLfloat
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	126	5	PDCH_7_U_Own_Data_DLfloat
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	127	5	PDCH_0_U_Other_Data_DLfloat
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	128	5	PDCH_1_U_Other_Data_DLfloat
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	129	5	PDCH_2_U_Other_Data_DLfloat
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	130	5	PDCH_3_U_Other_Data_DLfloat
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	131	5	PDCH_4_U_Other_Data_DLfloat
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	132	5	PDCH_5_U_Other_Data_DLfloat
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	133	5	PDCH_6_U_Other_Data_DLfloat
1006	SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	134	5	PDCH_7_U_Other_Data_DLfloat
1007	SEARCHTYPE_LOGSEARCH_SCAN_SUMMARY	1	8	文件ID4BYTES
1007	SEARCHTYPE_LOGSEARCH_SCAN_SUMMARY	2	8	采样点id4BYTE
1007	SEARCHTYPE_LOGSEARCH_SCAN_SUMMARY	3	8	采样点时间4BYTE
1007	SEARCHTYPE_LOGSEARCH_SCAN_SUMMARY	4	1	手机序列1BYTE
1007	SEARCHTYPE_LOGSEARCH_SCAN_SUMMARY	5	8	经度4BYTE
1007	SEARCHTYPE_LOGSEARCH_SCAN_SUMMARY	6	8	纬度4BYTE
1007	SEARCHTYPE_LOGSEARCH_SCAN_SUMMARY	7	8	后续采样点个数
1007	SEARCHTYPE_LOGSEARCH_SCAN_SUMMARY	8	6	strcellcode
1007	SEARCHTYPE_LOGSEARCH_SCAN_SUMMARY	9	2	wbcch2BYTE
1007	SEARCHTYPE_LOGSEARCH_SCAN_SUMMARY	10	1	bbsic1BYTE
1007	SEARCHTYPE_LOGSEARCH_SCAN_SUMMARY	11	5	berfloat
1007	SEARCHTYPE_LOGSEARCH_SCAN_SUMMARY	12	5	c_Ifloat
1007	SEARCHTYPE_LOGSEARCH_SCAN_SUMMARY	13	5	rxlevfloat
1007	SEARCHTYPE_LOGSEARCH_SCAN_SUMMARY	14	5	rxqualfloat
1007	SEARCHTYPE_LOGSEARCH_SCAN_SUMMARY	15	6	strcellcode
1007	SEARCHTYPE_LOGSEARCH_SCAN_SUMMARY	16	2	wbcch2BYTE
1007	SEARCHTYPE_LOGSEARCH_SCAN_SUMMARY	17	1	bbsic1BYTE
1007	SEARCHTYPE_LOGSEARCH_SCAN_SUMMARY	18	5	berfloat
1007	SEARCHTYPE_LOGSEARCH_SCAN_SUMMARY	19	5	c_Ifloat
1007	SEARCHTYPE_LOGSEARCH_SCAN_SUMMARY	20	5	rxlevfloat
1007	SEARCHTYPE_LOGSEARCH_SCAN_SUMMARY	21	5	rxqualfloat
1007	SEARCHTYPE_LOGSEARCH_SCAN_SUMMARY	22	6	strcellcode
1007	SEARCHTYPE_LOGSEARCH_SCAN_SUMMARY	23	2	wbcch2BYTE
1007	SEARCHTYPE_LOGSEARCH_SCAN_SUMMARY	24	1	bbsic1BYTE
1007	SEARCHTYPE_LOGSEARCH_SCAN_SUMMARY	25	5	berfloat
1007	SEARCHTYPE_LOGSEARCH_SCAN_SUMMARY	26	5	c_Ifloat
1007	SEARCHTYPE_LOGSEARCH_SCAN_SUMMARY	27	5	rxlevfloat
1007	SEARCHTYPE_LOGSEARCH_SCAN_SUMMARY	28	5	rxqualfloat
1008	SEARCHTYPE_LOGSEARCH_SCAN_DETAIL	1	8	文件ID4BYTES
1008	SEARCHTYPE_LOGSEARCH_SCAN_DETAIL	2	8	采样点id4BYTE
1008	SEARCHTYPE_LOGSEARCH_SCAN_DETAIL	3	8	采样点时间4BYTE
1008	SEARCHTYPE_LOGSEARCH_SCAN_DETAIL	4	1	手机序列1BYTE
1008	SEARCHTYPE_LOGSEARCH_SCAN_DETAIL	5	8	经度4BYTE
1008	SEARCHTYPE_LOGSEARCH_SCAN_DETAIL	6	8	纬度4BYTE
1008	SEARCHTYPE_LOGSEARCH_SCAN_DETAIL	7	8	后续采样点个数
1008	SEARCHTYPE_LOGSEARCH_SCAN_DETAIL	8	6	strcellcode
1008	SEARCHTYPE_LOGSEARCH_SCAN_DETAIL	9	2	wbcch2BYTE
1008	SEARCHTYPE_LOGSEARCH_SCAN_DETAIL	10	1	bbsic1BYTE
1008	SEARCHTYPE_LOGSEARCH_SCAN_DETAIL	11	5	berfloat
1008	SEARCHTYPE_LOGSEARCH_SCAN_DETAIL	12	5	c_Ifloat
1008	SEARCHTYPE_LOGSEARCH_SCAN_DETAIL	13	5	rxlevfloat
1008	SEARCHTYPE_LOGSEARCH_SCAN_DETAIL	14	5	rxqualfloat
1008	SEARCHTYPE_LOGSEARCH_SCAN_DETAIL	15	6	strcellcode
1008	SEARCHTYPE_LOGSEARCH_SCAN_DETAIL	16	2	wbcch2BYTE
1008	SEARCHTYPE_LOGSEARCH_SCAN_DETAIL	17	1	bbsic1BYTE
1008	SEARCHTYPE_LOGSEARCH_SCAN_DETAIL	18	5	berfloat
1008	SEARCHTYPE_LOGSEARCH_SCAN_DETAIL	19	5	c_Ifloat
1008	SEARCHTYPE_LOGSEARCH_SCAN_DETAIL	20	5	rxlevfloat
1008	SEARCHTYPE_LOGSEARCH_SCAN_DETAIL	21	5	rxqualfloat
1008	SEARCHTYPE_LOGSEARCH_SCAN_DETAIL	22	6	strcellcode
1008	SEARCHTYPE_LOGSEARCH_SCAN_DETAIL	23	2	wbcch2BYTE
1008	SEARCHTYPE_LOGSEARCH_SCAN_DETAIL	24	1	bbsic1BYTE
1008	SEARCHTYPE_LOGSEARCH_SCAN_DETAIL	25	5	berfloat
1008	SEARCHTYPE_LOGSEARCH_SCAN_DETAIL	26	5	c_Ifloat
1008	SEARCHTYPE_LOGSEARCH_SCAN_DETAIL	27	5	rxlevfloat
1008	SEARCHTYPE_LOGSEARCH_SCAN_DETAIL	28	5	rxqualfloat
1008	SEARCHTYPE_LOGSEARCH_SCAN_DETAIL	29	6	strcellcode
1008	SEARCHTYPE_LOGSEARCH_SCAN_DETAIL	30	2	wbcch2BYTE
1008	SEARCHTYPE_LOGSEARCH_SCAN_DETAIL	31	1	bbsic1BYTE
1008	SEARCHTYPE_LOGSEARCH_SCAN_DETAIL	32	5	berfloat
1008	SEARCHTYPE_LOGSEARCH_SCAN_DETAIL	33	5	c_Ifloat
1008	SEARCHTYPE_LOGSEARCH_SCAN_DETAIL	34	5	rxlevfloat
1008	SEARCHTYPE_LOGSEARCH_SCAN_DETAIL	35	5	rxqualfloat
1008	SEARCHTYPE_LOGSEARCH_SCAN_DETAIL	36	6	strcellcode
1008	SEARCHTYPE_LOGSEARCH_SCAN_DETAIL	37	2	wbcch2BYTE
1008	SEARCHTYPE_LOGSEARCH_SCAN_DETAIL	38	1	bbsic1BYTE
1008	SEARCHTYPE_LOGSEARCH_SCAN_DETAIL	39	5	berfloat
1008	SEARCHTYPE_LOGSEARCH_SCAN_DETAIL	40	5	c_Ifloat
1008	SEARCHTYPE_LOGSEARCH_SCAN_DETAIL	41	5	rxlevfloat
1008	SEARCHTYPE_LOGSEARCH_SCAN_DETAIL	42	5	rxqualfloat
1008	SEARCHTYPE_LOGSEARCH_SCAN_DETAIL	43	6	strcellcode
1008	SEARCHTYPE_LOGSEARCH_SCAN_DETAIL	44	2	wbcch2BYTE
1008	SEARCHTYPE_LOGSEARCH_SCAN_DETAIL	45	1	bbsic1BYTE
1008	SEARCHTYPE_LOGSEARCH_SCAN_DETAIL	46	5	berfloat
1008	SEARCHTYPE_LOGSEARCH_SCAN_DETAIL	47	5	c_Ifloat
1008	SEARCHTYPE_LOGSEARCH_SCAN_DETAIL	48	5	rxlevfloat
1008	SEARCHTYPE_LOGSEARCH_SCAN_DETAIL	49	5	rxqualfloat
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	1	8	文件ID4BYTES
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	2	8	采样点id4BYTE
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	3	8	采样点时间4BYTE
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	4	1	手机序列1BYTE
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	5	8	经度4BYTE
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	6	8	纬度4BYTE
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	7	8	后续采样点个数
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	8	6	strcellcode
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	9	2	wbcch2BYTE
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	10	1	bbsic1BYTE
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	11	5	berfloat
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	12	5	c_Ifloat
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	13	5	rxlevfloat
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	14	5	rxqualfloat
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	15	6	strcellcode
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	16	2	wbcch2BYTE
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	17	1	bbsic1BYTE
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	18	5	berfloat
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	19	5	c_Ifloat
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	20	5	rxlevfloat
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	21	5	rxqualfloat
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	22	6	strcellcode
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	23	2	wbcch2BYTE
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	24	1	bbsic1BYTE
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	25	5	berfloat
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	26	5	c_Ifloat
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	27	5	rxlevfloat
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	28	5	rxqualfloat
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	29	6	strcellcode
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	30	2	wbcch2BYTE
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	31	1	bbsic1BYTE
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	32	5	berfloat
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	33	5	c_Ifloat
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	34	5	rxlevfloat
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	35	5	rxqualfloat
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	36	6	strcellcode
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	37	2	wbcch2BYTE
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	38	1	bbsic1BYTE
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	39	5	berfloat
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	40	5	c_Ifloat
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	41	5	rxlevfloat
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	42	5	rxqualfloat
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	43	6	strcellcode
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	44	2	wbcch2BYTE
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	45	1	bbsic1BYTE
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	46	5	berfloat
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	47	5	c_Ifloat
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	48	5	rxlevfloat
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	49	5	rxqualfloat
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	50	6	strcellcode
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	51	2	wbcch2BYTE
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	52	1	bbsic1BYTE
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	53	5	berfloat
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	54	5	c_Ifloat
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	55	5	rxlevfloat
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	56	5	rxqualfloat
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	57	6	strcellcode
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	58	2	wbcch2BYTE
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	59	1	bbsic1BYTE
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	60	5	berfloat
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	61	5	c_Ifloat
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	62	5	rxlevfloat
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	63	5	rxqualfloat
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	64	6	strcellcode
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	65	2	wbcch2BYTE
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	66	1	bbsic1BYTE
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	67	5	berfloat
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	68	5	c_Ifloat
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	69	5	rxlevfloat
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	70	5	rxqualfloat
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	71	6	strcellcode
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	72	2	wbcch2BYTE
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	73	1	bbsic1BYTE
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	74	5	berfloat
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	75	5	c_Ifloat
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	76	5	rxlevfloat
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	77	5	rxqualfloat
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	78	6	strcellcode
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	79	2	wbcch2BYTE
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	80	1	bbsic1BYTE
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	81	5	berfloat
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	82	5	c_Ifloat
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	83	5	rxlevfloat
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	84	5	rxqualfloat
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	85	6	strcellcode
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	86	2	wbcch2BYTE
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	87	1	bbsic1BYTE
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	88	5	berfloat
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	89	5	c_Ifloat
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	90	5	rxlevfloat
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	91	5	rxqualfloat
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	92	6	strcellcode
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	93	2	wbcch2BYTE
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	94	1	bbsic1BYTE
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	95	5	berfloat
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	96	5	c_Ifloat
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	97	5	rxlevfloat
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	98	5	rxqualfloat
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	99	6	strcellcode
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	100	2	wbcch2BYTE
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	101	1	bbsic1BYTE
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	102	5	berfloat
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	103	5	c_Ifloat
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	104	5	rxlevfloat
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	105	5	rxqualfloat
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	106	6	strcellcode
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	107	2	wbcch2BYTE
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	108	1	bbsic1BYTE
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	109	5	berfloat
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	110	5	c_Ifloat
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	111	5	rxlevfloat
1009	SEARCHTYPE_LOGSEARCH_SCAN_DEPTH	112	5	rxqualfloat
1010	SEARCHTYPE_GRID_IDLE	1	8	ifileid
1010	SEARCHTYPE_GRID_IDLE	2	8	tllongitudeintnull
1010	SEARCHTYPE_GRID_IDLE	3	8	tllatitudeintnull
1010	SEARCHTYPE_GRID_IDLE	4	8	brlongitudeintnull
1010	SEARCHTYPE_GRID_IDLE	5	8	brlatitudeintnull
1010	SEARCHTYPE_GRID_IDLE	6	8	max_rxlev_idleintnull
1010	SEARCHTYPE_GRID_IDLE	7	8	min_rxlev_idleintnull
1010	SEARCHTYPE_GRID_IDLE	8	5	mean_rxlev_idle
1010	SEARCHTYPE_GRID_IDLE	9	8	max_rxlev_dedicatedintnull
1010	SEARCHTYPE_GRID_IDLE	10	8	min_rxlev_dedicatedintnull
1010	SEARCHTYPE_GRID_IDLE	11	5	mean_rxlev_dedicated
1010	SEARCHTYPE_GRID_IDLE	12	5	max_pesqfloatnull
1010	SEARCHTYPE_GRID_IDLE	13	5	min_pesqfloatnull
1010	SEARCHTYPE_GRID_IDLE	14	5	mean_pesq
1010	SEARCHTYPE_GRID_IDLE	15	8	max_tafloatnull
1010	SEARCHTYPE_GRID_IDLE	16	8	min_tafloatnull
1010	SEARCHTYPE_GRID_IDLE	17	5	mean_ta
1010	SEARCHTYPE_GRID_IDLE	18	5	max_sqifloatnull
1010	SEARCHTYPE_GRID_IDLE	19	5	min_sqifloatnull
1010	SEARCHTYPE_GRID_IDLE	20	5	mean_sqi
1010	SEARCHTYPE_GRID_IDLE	21	5	max_c_ibcchfloatnull
1010	SEARCHTYPE_GRID_IDLE	22	5	min_c_ibcchfloatnull
1010	SEARCHTYPE_GRID_IDLE	23	5	mean_c_bcch
1010	SEARCHTYPE_GRID_IDLE	24	5	max_c_itchfloatnull
1010	SEARCHTYPE_GRID_IDLE	25	5	min_c_itchfloatnull
1010	SEARCHTYPE_GRID_IDLE	26	5	mean_c_tch
1010	SEARCHTYPE_GRID_IDLE	27	8	filetimeintnull
1010	SEARCHTYPE_GRID_IDLE	28	8	distanceintnull
1010	SEARCHTYPE_GRID_IDLE	29	8	durationintnull
1010	SEARCHTYPE_GRID_IDLE	30	8	[iLAC1][int]NULL
1010	SEARCHTYPE_GRID_IDLE	31	2	[wRAC1][smallint]NULL
1010	SEARCHTYPE_GRID_IDLE	32	8	[iCI1][int]NULL
1010	SEARCHTYPE_GRID_IDLE	33	8	[isamplenum1][int]NULL
1010	SEARCHTYPE_GRID_IDLE	34	8	[irxlevmean1][int]NULL
1010	SEARCHTYPE_GRID_IDLE	35	8	[iLAC2][int]NULL
1010	SEARCHTYPE_GRID_IDLE	36	2	[wRAC2][smallint]NULL
1010	SEARCHTYPE_GRID_IDLE	37	8	[iCI2][int]NULL
1010	SEARCHTYPE_GRID_IDLE	38	8	[isamplenum2][int]NULL
1010	SEARCHTYPE_GRID_IDLE	39	8	[irxlevmean2][int]NULL
1010	SEARCHTYPE_GRID_IDLE	40	8	[iLAC3][int]NULL
1010	SEARCHTYPE_GRID_IDLE	41	2	[wRAC3][smallint]NULL
1010	SEARCHTYPE_GRID_IDLE	42	8	[iCI3][int]NULL
1010	SEARCHTYPE_GRID_IDLE	43	8	[isamplenum3][int]NULL
1010	SEARCHTYPE_GRID_IDLE	44	8	[irxlevmean3][int]NULL
1010	SEARCHTYPE_GRID_IDLE	45	8	[iLAC4][int]NULL
1010	SEARCHTYPE_GRID_IDLE	46	2	[wRAC4][smallint]NULL
1010	SEARCHTYPE_GRID_IDLE	47	8	[iCI4][int]NULL
1010	SEARCHTYPE_GRID_IDLE	48	8	[isamplenum4][int]NULL
1010	SEARCHTYPE_GRID_IDLE	49	8	[irxlevmean4][int]NULL
1010	SEARCHTYPE_GRID_IDLE	50	8	[isamplenum][int]NULL
1010	SEARCHTYPE_GRID_IDLE	51	8	[Rxlev_10_45][int]NULL
1010	SEARCHTYPE_GRID_IDLE	52	8	[Rxlev_46_50][int]NULL
1010	SEARCHTYPE_GRID_IDLE	53	8	[Rxlev_51_55][int]NULL
1010	SEARCHTYPE_GRID_IDLE	54	8	[Rxlev_56_60][int]NULL
1010	SEARCHTYPE_GRID_IDLE	55	8	[Rxlev_61_65][int]NULL
1010	SEARCHTYPE_GRID_IDLE	56	8	[Rxlev_66_70][int]NULL
1010	SEARCHTYPE_GRID_IDLE	57	8	[Rxlev_71_75][int]NULL
1010	SEARCHTYPE_GRID_IDLE	58	8	[Rxlev_76_80][int]NULL
1010	SEARCHTYPE_GRID_IDLE	59	8	[Rxlev_81][int]NULL
1010	SEARCHTYPE_GRID_IDLE	60	8	[Rxlev_82][int]NULL
1010	SEARCHTYPE_GRID_IDLE	61	8	[Rxlev_83][int]NULL
1010	SEARCHTYPE_GRID_IDLE	62	8	[Rxlev_84][int]NULL
1010	SEARCHTYPE_GRID_IDLE	63	8	[Rxlev_85][int]NULL
1010	SEARCHTYPE_GRID_IDLE	64	8	[Rxlev_86][int]NULL
1010	SEARCHTYPE_GRID_IDLE	65	8	[Rxlev_87][int]NULL
1010	SEARCHTYPE_GRID_IDLE	66	8	[Rxlev_88][int]NULL
1010	SEARCHTYPE_GRID_IDLE	67	8	[Rxlev_89][int]NULL
1010	SEARCHTYPE_GRID_IDLE	68	8	[Rxlev_90][int]NULL
1010	SEARCHTYPE_GRID_IDLE	69	8	[Rxlev_91][int]NULL
1010	SEARCHTYPE_GRID_IDLE	70	8	[Rxlev_92][int]NULL
1010	SEARCHTYPE_GRID_IDLE	71	8	[Rxlev_93][int]NULL
1010	SEARCHTYPE_GRID_IDLE	72	8	[Rxlev_94][int]NULL
1010	SEARCHTYPE_GRID_IDLE	73	8	[Rxlev_95][int]NULL
1010	SEARCHTYPE_GRID_IDLE	74	8	[Rxlev_96][int]NULL
1010	SEARCHTYPE_GRID_IDLE	75	8	[Rxlev_97][int]NULL
1010	SEARCHTYPE_GRID_IDLE	76	8	[Rxlev_98][int]NULL
1010	SEARCHTYPE_GRID_IDLE	77	8	[Rxlev_99_120][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	1	8	ifileid
1011	SEARCHTYPE_GRID_DEDICATED	2	8	tllongitudeintnull
1011	SEARCHTYPE_GRID_DEDICATED	3	8	tllatitudeintnull
1011	SEARCHTYPE_GRID_DEDICATED	4	8	brlongitudeintnull
1011	SEARCHTYPE_GRID_DEDICATED	5	8	brlatitudeintnull
1011	SEARCHTYPE_GRID_DEDICATED	6	8	max_rxlev_idleintnull
1011	SEARCHTYPE_GRID_DEDICATED	7	8	min_rxlev_idleintnull
1011	SEARCHTYPE_GRID_DEDICATED	8	5	mean_rxlev_idle
1011	SEARCHTYPE_GRID_DEDICATED	9	8	max_rxlev_dedicatedintnull
1011	SEARCHTYPE_GRID_DEDICATED	10	8	min_rxlev_dedicatedintnull
1011	SEARCHTYPE_GRID_DEDICATED	11	5	mean_rxlev_dedicated
1011	SEARCHTYPE_GRID_DEDICATED	12	5	max_pesqfloatnull
1011	SEARCHTYPE_GRID_DEDICATED	13	5	min_pesqfloatnull
1011	SEARCHTYPE_GRID_DEDICATED	14	5	mean_pesq
1011	SEARCHTYPE_GRID_DEDICATED	15	8	max_tafloatnull
1011	SEARCHTYPE_GRID_DEDICATED	16	8	min_tafloatnull
1011	SEARCHTYPE_GRID_DEDICATED	17	5	mean_ta
1011	SEARCHTYPE_GRID_DEDICATED	18	5	max_sqifloatnull
1011	SEARCHTYPE_GRID_DEDICATED	19	5	min_sqifloatnull
1011	SEARCHTYPE_GRID_DEDICATED	20	5	mean_sqi
1011	SEARCHTYPE_GRID_DEDICATED	21	5	max_c_ibcchfloatnull
1011	SEARCHTYPE_GRID_DEDICATED	22	5	min_c_ibcchfloatnull
1011	SEARCHTYPE_GRID_DEDICATED	23	5	mean_c_bcch
1011	SEARCHTYPE_GRID_DEDICATED	24	5	max_c_itchfloatnull
1011	SEARCHTYPE_GRID_DEDICATED	25	5	min_c_itchfloatnull
1011	SEARCHTYPE_GRID_DEDICATED	26	5	mean_c_tch
1011	SEARCHTYPE_GRID_DEDICATED	27	8	filetimeintnull
1011	SEARCHTYPE_GRID_DEDICATED	28	8	distanceintnull
1011	SEARCHTYPE_GRID_DEDICATED	29	8	durationintnull
1011	SEARCHTYPE_GRID_DEDICATED	30	8	[iLAC1][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	31	2	[wRAC1][smallint]NULL
1011	SEARCHTYPE_GRID_DEDICATED	32	8	[iCI1][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	33	8	[isamplenum1][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	34	8	[irxlevmean1][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	35	8	[iLAC2][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	36	2	[wRAC2][smallint]NULL
1011	SEARCHTYPE_GRID_DEDICATED	37	8	[iCI2][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	38	8	[isamplenum2][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	39	8	[irxlevmean2][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	40	8	[iLAC3][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	41	2	[wRAC3][smallint]NULL
1011	SEARCHTYPE_GRID_DEDICATED	42	8	[iCI3][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	43	8	[isamplenum3][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	44	8	[irxlevmean3][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	45	8	[iLAC4][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	46	2	[wRAC4][smallint]NULL
1011	SEARCHTYPE_GRID_DEDICATED	47	8	[iCI4][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	48	8	[isamplenum4][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	49	8	[irxlevmean4][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	50	8	[isamplenum][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	51	8	[Rxlev_10_45][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	52	8	[Rxlev_46_50][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	53	8	[Rxlev_51_55][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	54	8	[Rxlev_56_60][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	55	8	[Rxlev_61_65][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	56	8	[Rxlev_66_70][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	57	8	[Rxlev_71_75][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	58	8	[Rxlev_76_80][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	59	8	[Rxlev_81][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	60	8	[Rxlev_82][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	61	8	[Rxlev_83][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	62	8	[Rxlev_84][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	63	8	[Rxlev_85][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	64	8	[Rxlev_86][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	65	8	[Rxlev_87][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	66	8	[Rxlev_88][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	67	8	[Rxlev_89][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	68	8	[Rxlev_90][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	69	8	[Rxlev_91][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	70	8	[Rxlev_92][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	71	8	[Rxlev_93][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	72	8	[Rxlev_94][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	73	8	[Rxlev_95][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	74	8	[Rxlev_96][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	75	8	[Rxlev_97][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	76	8	[Rxlev_98][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	77	8	[Rxlev_99_120][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	78	8	[RxQual0][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	79	8	[RxQual1][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	80	8	[RxQual2][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	81	8	[RxQual3][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	82	8	[RxQual4][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	83	8	[RxQual5][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	84	8	[RxQual6][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	85	8	[RxQual7][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	86	8	[PESQ_0_10][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	87	8	[PESQ_10_20][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	88	8	[PESQ_20_25][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	89	8	[PESQ_25_28][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	90	8	[PESQ_28_30][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	91	8	[PESQ_30_33][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	92	8	[PESQ_33_34][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	93	8	[PESQ_45][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	94	8	[PESQ_Value_0_10][float]NULL
1011	SEARCHTYPE_GRID_DEDICATED	95	8	[PESQ_Value_10_20][float]NULL
1011	SEARCHTYPE_GRID_DEDICATED	96	8	[PESQ_Value_20_25][float]NULL
1011	SEARCHTYPE_GRID_DEDICATED	97	8	[PESQ_Value_25_28][float]NULL
1011	SEARCHTYPE_GRID_DEDICATED	98	8	[PESQ_Value_28_30][float]NULL
1011	SEARCHTYPE_GRID_DEDICATED	99	8	[PESQ_Value_30_33][float]NULL
1011	SEARCHTYPE_GRID_DEDICATED	100	8	[PESQ_Value_30_34][float]NULL
1011	SEARCHTYPE_GRID_DEDICATED	101	8	[PESQ_Value_45][float]NULL
1011	SEARCHTYPE_GRID_DEDICATED	102	8	[TA0][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	103	8	[TA1][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	104	8	[TA2][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	105	8	[TA3][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	106	8	[TA4][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	107	8	[TA5][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	108	8	[TA6][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	109	8	[TA7][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	110	8	[TA8][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	111	8	[TA9][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	112	8	[TA10][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	113	8	[TA_11_15][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	114	8	[TA_16_20][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	115	8	[TA_21_30][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	116	8	[TA_31_40][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	117	8	[TA_41_64][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	118	8	[TA_value_11_15][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	119	8	[TA_value_16_20][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	120	8	[TA_value_21_30][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	121	8	[TA_value_31_40][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	122	8	[TA_value_41_64][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	123	8	[SpeechCodec_FR][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	124	8	[SpeechCodec_HR][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	125	8	[SpeechCodec_EFR][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	126	8	[SpeechCodec_AMR][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	127	8	[SpeechCodecTime_FR][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	128	8	[SpeechCodecTime_HR][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	129	8	[SpeechCodecTime_EFR][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	130	8	[SpeechCodecTime_AMR][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	131	8	[C_Iworst_5_9][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	132	8	[C_Iworst_9_12][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	133	8	[C_Iworst_12_15][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	134	8	[C_Iworst_15_20][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	135	8	[C_Iworst_20_35][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	136	8	[C_Iworst_value_5_9][float]NULL
1011	SEARCHTYPE_GRID_DEDICATED	137	8	[C_Iworst_value_9_12][float]NULL
1011	SEARCHTYPE_GRID_DEDICATED	138	8	[C_Iworst_value_12_15][float]NULL
1011	SEARCHTYPE_GRID_DEDICATED	139	8	[C_Iworst_value_15_20][float]NULL
1011	SEARCHTYPE_GRID_DEDICATED	140	8	[C_Iworst_value_20_35][float]NULL
1011	SEARCHTYPE_GRID_DEDICATED	141	8	[C_Iavg_5_9][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	142	8	[C_Iavg_9_12][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	143	8	[C_Iavg_12_15][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	144	8	[C_Iavg_15_20][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	145	8	[C_Iavg_20_35][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	146	8	[C_Iavg_value_5_9][float]NULL
1011	SEARCHTYPE_GRID_DEDICATED	147	8	[C_Iavg_value_9_12][float]NULL
1011	SEARCHTYPE_GRID_DEDICATED	148	8	[C_Iavg_value_12_15][float]NULL
1011	SEARCHTYPE_GRID_DEDICATED	149	8	[C_Iavg_value_15_20][float]NULL
1011	SEARCHTYPE_GRID_DEDICATED	150	8	[C_Iavg_value_20_35][float]NULL
1011	SEARCHTYPE_GRID_DEDICATED	151	8	[SQI_20_10][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	152	8	[SQI_10_0][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	153	8	[SQI_0_10][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	154	8	[SQI_10_20][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	155	8	[SQI_20_30][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	156	8	[SQIValue_20_10][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	157	8	[SQIValue_10_0][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	158	8	[SQIValue_0_10][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	159	8	[SQIValue_10_20][int]NULL
1011	SEARCHTYPE_GRID_DEDICATED	160	8	[SQIValue_20_30][int]NULL
1012	SEARCHTYPE_GRID_GPRS	1	8	ifileid
1012	SEARCHTYPE_GRID_GPRS	2	8	tllongitudeintnull
1012	SEARCHTYPE_GRID_GPRS	3	8	tllatitudeintnull
1012	SEARCHTYPE_GRID_GPRS	4	8	brlongitudeintnull
1012	SEARCHTYPE_GRID_GPRS	5	8	brlatitudeintnull
1012	SEARCHTYPE_GRID_GPRS	6	8	max_rxlev_packet
1012	SEARCHTYPE_GRID_GPRS	7	8	min_rxlev_packet
1012	SEARCHTYPE_GRID_GPRS	8	5	mean_rxlev_packet
1012	SEARCHTYPE_GRID_GPRS	9	8	max_rxlev_dedicatedintnull
1012	SEARCHTYPE_GRID_GPRS	10	8	min_rxlev_dedicatedintnull
1012	SEARCHTYPE_GRID_GPRS	11	5	mean_rxlev_dedicated
1012	SEARCHTYPE_GRID_GPRS	12	5	max_pesqfloatnull
1012	SEARCHTYPE_GRID_GPRS	13	5	min_pesqfloatnull
1012	SEARCHTYPE_GRID_GPRS	14	5	mean_pesq
1012	SEARCHTYPE_GRID_GPRS	15	8	max_tafloatnull
1012	SEARCHTYPE_GRID_GPRS	16	8	min_tafloatnull
1012	SEARCHTYPE_GRID_GPRS	17	5	mean_ta
1012	SEARCHTYPE_GRID_GPRS	18	5	max_sqifloatnull
1012	SEARCHTYPE_GRID_GPRS	19	5	min_sqifloatnull
1012	SEARCHTYPE_GRID_GPRS	20	5	mean_sqi
1012	SEARCHTYPE_GRID_GPRS	21	5	max_c_ibcchfloatnull
1012	SEARCHTYPE_GRID_GPRS	22	5	min_c_ibcchfloatnull
1012	SEARCHTYPE_GRID_GPRS	23	5	mean_c_bcch
1012	SEARCHTYPE_GRID_GPRS	24	5	max_c_itchfloatnull
1012	SEARCHTYPE_GRID_GPRS	25	5	min_c_itchfloatnull
1012	SEARCHTYPE_GRID_GPRS	26	5	mean_c_tch
1012	SEARCHTYPE_GRID_GPRS	27	8	filetimeintnull
1012	SEARCHTYPE_GRID_GPRS	28	8	distanceintnull
1012	SEARCHTYPE_GRID_GPRS	29	8	durationintnull
1012	SEARCHTYPE_GRID_GPRS	30	8	[iLAC1][int]NULL
1012	SEARCHTYPE_GRID_GPRS	31	2	[wRAC1][smallint]NULL
1012	SEARCHTYPE_GRID_GPRS	32	8	[iCI1][int]NULL
1012	SEARCHTYPE_GRID_GPRS	33	8	[isamplenum1][int]NULL
1012	SEARCHTYPE_GRID_GPRS	34	8	[irxlevmean1][int]NULL
1012	SEARCHTYPE_GRID_GPRS	35	8	[iLAC2][int]NULL
1012	SEARCHTYPE_GRID_GPRS	36	2	[wRAC2][smallint]NULL
1012	SEARCHTYPE_GRID_GPRS	37	8	[iCI2][int]NULL
1012	SEARCHTYPE_GRID_GPRS	38	8	[isamplenum2][int]NULL
1012	SEARCHTYPE_GRID_GPRS	39	8	[irxlevmean2][int]NULL
1012	SEARCHTYPE_GRID_GPRS	40	8	[iLAC3][int]NULL
1012	SEARCHTYPE_GRID_GPRS	41	2	[wRAC3][smallint]NULL
1012	SEARCHTYPE_GRID_GPRS	42	8	[iCI3][int]NULL
1012	SEARCHTYPE_GRID_GPRS	43	8	[isamplenum3][int]NULL
1012	SEARCHTYPE_GRID_GPRS	44	8	[irxlevmean3][int]NULL
1012	SEARCHTYPE_GRID_GPRS	45	8	[iLAC4][int]NULL
1012	SEARCHTYPE_GRID_GPRS	46	2	[wRAC4][smallint]NULL
1012	SEARCHTYPE_GRID_GPRS	47	8	[iCI4][int]NULL
1012	SEARCHTYPE_GRID_GPRS	48	8	[isamplenum4][int]NULL
1012	SEARCHTYPE_GRID_GPRS	49	8	[irxlevmean4][int]NULL
1012	SEARCHTYPE_GRID_GPRS	50	8	[isamplenum][int]NULL
1012	SEARCHTYPE_GRID_GPRS	51	8	[Rxlev_10_45][int]NULL
1012	SEARCHTYPE_GRID_GPRS	52	8	[Rxlev_46_50][int]NULL
1012	SEARCHTYPE_GRID_GPRS	53	8	[Rxlev_51_55][int]NULL
1012	SEARCHTYPE_GRID_GPRS	54	8	[Rxlev_56_60][int]NULL
1012	SEARCHTYPE_GRID_GPRS	55	8	[Rxlev_61_65][int]NULL
1012	SEARCHTYPE_GRID_GPRS	56	8	[Rxlev_66_70][int]NULL
1012	SEARCHTYPE_GRID_GPRS	57	8	[Rxlev_71_75][int]NULL
1012	SEARCHTYPE_GRID_GPRS	58	8	[Rxlev_76_80][int]NULL
1012	SEARCHTYPE_GRID_GPRS	59	8	[Rxlev_81][int]NULL
1012	SEARCHTYPE_GRID_GPRS	60	8	[Rxlev_82][int]NULL
1012	SEARCHTYPE_GRID_GPRS	61	8	[Rxlev_83][int]NULL
1012	SEARCHTYPE_GRID_GPRS	62	8	[Rxlev_84][int]NULL
1012	SEARCHTYPE_GRID_GPRS	63	8	[Rxlev_85][int]NULL
1012	SEARCHTYPE_GRID_GPRS	64	8	[Rxlev_86][int]NULL
1012	SEARCHTYPE_GRID_GPRS	65	8	[Rxlev_87][int]NULL
1012	SEARCHTYPE_GRID_GPRS	66	8	[Rxlev_88][int]NULL
1012	SEARCHTYPE_GRID_GPRS	67	8	[Rxlev_89][int]NULL
1012	SEARCHTYPE_GRID_GPRS	68	8	[Rxlev_90][int]NULL
1012	SEARCHTYPE_GRID_GPRS	69	8	[Rxlev_91][int]NULL
1012	SEARCHTYPE_GRID_GPRS	70	8	[Rxlev_92][int]NULL
1012	SEARCHTYPE_GRID_GPRS	71	8	[Rxlev_93][int]NULL
1012	SEARCHTYPE_GRID_GPRS	72	8	[Rxlev_94][int]NULL
1012	SEARCHTYPE_GRID_GPRS	73	8	[Rxlev_95][int]NULL
1012	SEARCHTYPE_GRID_GPRS	74	8	[Rxlev_96][int]NULL
1012	SEARCHTYPE_GRID_GPRS	75	8	[Rxlev_97][int]NULL
1012	SEARCHTYPE_GRID_GPRS	76	8	[Rxlev_98][int]NULL
1012	SEARCHTYPE_GRID_GPRS	77	8	[Rxlev_99_120][int]NULL
1012	SEARCHTYPE_GRID_GPRS	78	8	[RLC_mean_thr_samplenum][int]NULL
1012	SEARCHTYPE_GRID_GPRS	79	8	[RLC_mean_thr_sum][float]NULL
1012	SEARCHTYPE_GRID_GPRS	80	8	[APP_mean_thr_samplenum][int]NULL
1012	SEARCHTYPE_GRID_GPRS	81	8	[APP_mean_thr_sum][float]NULL
1012	SEARCHTYPE_GRID_GPRS	82	8	[FTP_recv_bytes][int]NULL
1012	SEARCHTYPE_GRID_GPRS	83	8	[FTP_download_duration][int]NULL
1012	SEARCHTYPE_GRID_GPRS	84	8	[RLC_recv_bytes][int]NULL
1012	SEARCHTYPE_GRID_GPRS	85	8	[RLC_download_duration][int]NULL
1012	SEARCHTYPE_GRID_GPRS	86	8	[RLC_BLER_samplenum][int]NULL
1012	SEARCHTYPE_GRID_GPRS	87	8	[RLC_BLER_sum][float]NULL
1012	SEARCHTYPE_GRID_GPRS	88	8	[GPRS_BLER_samplenum][int]NULL
1012	SEARCHTYPE_GRID_GPRS	89	8	[GPRS_BLER_sum][float]NULL
1012	SEARCHTYPE_GRID_GPRS	90	8	[EDGE_cover][int]NULL
1012	SEARCHTYPE_GRID_GPRS	91	8	[GPRS_cover][int]NULL
1012	SEARCHTYPE_GRID_GPRS	92	8	[FTP_slot1_duration][int]NULL
1012	SEARCHTYPE_GRID_GPRS	93	8	[FTP_slot2_duration][int]NULL
1012	SEARCHTYPE_GRID_GPRS	94	8	[FTP_slot3_duration][int]NULL
1012	SEARCHTYPE_GRID_GPRS	95	8	[FTP_slot4_duration][int]NULL
1012	SEARCHTYPE_GRID_GPRS	96	8	[MCS1_package][int]NULL
1012	SEARCHTYPE_GRID_GPRS	97	8	[MCS2_package][int]NULL
1012	SEARCHTYPE_GRID_GPRS	98	8	[MCS3_package][int]NULL
1012	SEARCHTYPE_GRID_GPRS	99	8	[MCS4_package][int]NULL
1012	SEARCHTYPE_GRID_GPRS	100	8	[MCS5_package][int]NULL
1012	SEARCHTYPE_GRID_GPRS	101	8	[MCS6_package][int]NULL
1012	SEARCHTYPE_GRID_GPRS	102	8	[MCS7_package][int]NULL
1012	SEARCHTYPE_GRID_GPRS	103	8	[MCS8_package][int]NULL
1012	SEARCHTYPE_GRID_GPRS	104	8	[MCS9_package][int]NULL
1012	SEARCHTYPE_GRID_GPRS	105	8	[CS1_package][int]NULL
1012	SEARCHTYPE_GRID_GPRS	106	8	[CS2_package][int]NULL
1012	SEARCHTYPE_GRID_GPRS	107	8	[CS3_package][int]NULL
1012	SEARCHTYPE_GRID_GPRS	108	8	[CS4_package][int]NULL
1013	SEARCHTYPE_SCAN_KPI_LOG	1	8	ifileid
1013	SEARCHTYPE_SCAN_KPI_LOG	2	8	[itllongitude][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	3	8	[itllatitude][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	4	8	[ibrlongitude][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	5	8	[ibrlatitude][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	6	8	文件开始时间
1013	SEARCHTYPE_SCAN_KPI_LOG	7	8	测试里程
1013	SEARCHTYPE_SCAN_KPI_LOG	8	8	测试时长
1013	SEARCHTYPE_SCAN_KPI_LOG	9	8	[iRxlevnum1][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	10	8	[Rxlev1_10_45][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	11	8	[Rxlev1_46_50][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	12	8	[Rxlev1_51_55][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	13	8	[Rxlev1_56_60][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	14	8	[Rxlev1_61_65][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	15	8	[Rxlev1_66_70][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	16	8	[Rxlev1_71_75][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	17	8	[Rxlev1_76_80][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	18	8	[Rxlev1_81][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	19	8	[Rxlev1_82][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	20	8	[Rxlev1_83][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	21	8	[Rxlev1_84][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	22	8	[Rxlev1_85][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	23	8	[Rxlev1_86][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	24	8	[Rxlev1_87][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	25	8	[Rxlev1_88][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	26	8	[Rxlev1_89][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	27	8	[Rxlev1_90][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	28	8	[Rxlev1_91][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	29	8	[Rxlev1_92][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	30	8	[Rxlev1_93][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	31	8	[Rxlev1_94][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	32	8	[Rxlev1_95][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	33	8	[Rxlev1_96][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	34	8	[Rxlev1_97][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	35	8	[Rxlev1_98][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	36	8	[Rxlev1_99_120][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	37	5	[Rxlev1_Max][float]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	38	5	[Rxlev1_Min][float]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	39	5	[Rxlev1_Mean][float]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	40	8	[iRxlevnum2][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	41	8	[Rxlev2_10_45][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	42	8	[Rxlev2_46_50][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	43	8	[Rxlev2_51_55][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	44	8	[Rxlev2_56_60][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	45	8	[Rxlev2_61_65][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	46	8	[Rxlev2_66_70][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	47	8	[Rxlev2_71_75][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	48	8	[Rxlev2_76_80][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	49	8	[Rxlev2_81][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	50	8	[Rxlev2_82][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	51	8	[Rxlev2_83][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	52	8	[Rxlev2_84][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	53	8	[Rxlev2_85][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	54	8	[Rxlev2_86][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	55	8	[Rxlev2_87][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	56	8	[Rxlev2_88][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	57	8	[Rxlev2_89][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	58	8	[Rxlev2_90][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	59	8	[Rxlev2_91][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	60	8	[Rxlev2_92][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	61	8	[Rxlev2_93][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	62	8	[Rxlev2_94][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	63	8	[Rxlev2_95][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	64	8	[Rxlev2_96][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	65	8	[Rxlev2_97][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	66	8	[Rxlev2_98][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	67	8	[Rxlev2_99_120][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	68	5	[Rxlev2_Max][float]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	69	5	[Rxlev2_Min][float]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	70	5	[Rxlev2_Mean][float]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	71	8	[iRxlevnum3][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	72	8	[Rxlev3_10_45][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	73	8	[Rxlev3_46_50][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	74	8	[Rxlev3_51_55][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	75	8	[Rxlev3_56_60][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	76	8	[Rxlev3_61_65][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	77	8	[Rxlev3_66_70][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	78	8	[Rxlev3_71_75][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	79	8	[Rxlev3_76_80][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	80	8	[Rxlev3_81][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	81	8	[Rxlev3_82][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	82	8	[Rxlev3_83][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	83	8	[Rxlev3_84][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	84	8	[Rxlev3_85][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	85	8	[Rxlev3_86][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	86	8	[Rxlev3_87][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	87	8	[Rxlev3_88][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	88	8	[Rxlev3_89][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	89	8	[Rxlev3_90][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	90	8	[Rxlev3_91][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	91	8	[Rxlev3_92][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	92	8	[Rxlev3_93][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	93	8	[Rxlev3_94][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	94	8	[Rxlev3_95][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	95	8	[Rxlev3_96][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	96	8	[Rxlev3_97][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	97	8	[Rxlev3_98][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	98	8	[Rxlev3_99_120][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	99	5	[Rxlev3_Max][float]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	100	5	[Rxlev3_Min][float]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	101	5	[Rxlev3_Mean][float]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	102	8	[C_I1_20_9_num][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	103	8	[C_I1_9_12_num][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	104	8	[C_I1_12_15_num][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	105	8	[C_I1_15_20_num][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	106	8	[C_I1_20_40_num][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	107	5	[C_I1_20_9][float]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	108	5	[C_I1_9_12][float]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	109	5	[C_I1_12_15][float]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	110	5	[C_I1_15_20][float]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	111	5	[C_I1_20_40][float]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	112	5	[C_I1_Max][float]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	113	5	[C_I1_Min][float]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	114	8	[C_I2_20_9_num][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	115	8	[C_I2_9_12_num][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	116	8	[C_I2_12_15_num][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	117	8	[C_I2_15_20_num][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	118	8	[C_I2_20_40_num][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	119	5	[C_I2_20_9][float]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	120	5	[C_I2_9_12][float]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	121	5	[C_I2_12_15][float]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	122	5	[C_I2_15_20][float]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	123	5	[C_I2_20_40][float]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	124	5	[C_I2_Max][float]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	125	5	[C_I2_Min][float]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	126	8	[C_I3_20_9_num][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	127	8	[C_I3_9_12_num][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	128	8	[C_I3_12_15_num][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	129	8	[C_I3_15_20_num][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	130	8	[C_I3_20_40_num][int]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	131	5	[C_I3_20_9][float]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	132	5	[C_I3_9_12][float]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	133	5	[C_I3_12_15][float]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	134	5	[C_I3_15_20][float]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	135	5	[C_I3_20_40][float]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	136	5	[C_I3_Max][float]NULL,
1013	SEARCHTYPE_SCAN_KPI_LOG	137	5	[C_I3_Min][float]NULL
1014	SEARCHTYPE_SCAN_KPI_GRID	1	8	ifileid int
1014	SEARCHTYPE_SCAN_KPI_GRID	2	8	[itllongitude][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	3	8	[itllatitude][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	4	8	[ibrlongitude][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	5	8	[ibrlatitude][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	6	8	ifiletime
1014	SEARCHTYPE_SCAN_KPI_GRID	7	8	idistance
1014	SEARCHTYPE_SCAN_KPI_GRID	8	8	iduration
1014	SEARCHTYPE_SCAN_KPI_GRID	9	6	[strcellcode1][varchar]
1014	SEARCHTYPE_SCAN_KPI_GRID	10	8	[icellnum1][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	11	5	[irxlevMean1][float]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	12	5	[irxlevMin1][float]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	13	5	[irxlevMax1][float]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	14	5	[C_IMean1][float]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	15	5	[C_IMax1][float]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	16	5	[C_IMin1][float]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	17	6	[strcellcode2][varchar](255)
1014	SEARCHTYPE_SCAN_KPI_GRID	18	8	[icellnum2]int
1014	SEARCHTYPE_SCAN_KPI_GRID	19	5	[irxlevMean2][float]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	20	5	[irxlevMin2][float]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	21	5	[irxlevMax2][float]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	22	5	[C_IMean2][float]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	23	5	[C_IMax2][float]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	24	5	[C_IMin2][float]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	25	6	[strcellcode3][varchar]
1014	SEARCHTYPE_SCAN_KPI_GRID	26	8	[icellnum3][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	27	5	[irxlevMean3][float]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	28	5	[irxlevMin3][float]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	29	5	[irxlevMax3][float]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	30	5	[C_IMean3][float]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	31	5	[C_IMax3][float]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	32	5	[C_IMin3][float]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	33	8	[iRxlevnum1][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	34	8	[Rxlev1_10_45][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	35	8	[Rxlev1_46_50][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	36	8	[Rxlev1_51_55][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	37	8	[Rxlev1_56_60][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	38	8	[Rxlev1_61_65][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	39	8	[Rxlev1_66_70][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	40	8	[Rxlev1_71_75][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	41	8	[Rxlev1_76_80][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	42	8	[Rxlev1_81][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	43	8	[Rxlev1_82][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	44	8	[Rxlev1_83][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	45	8	[Rxlev1_84][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	46	8	[Rxlev1_85][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	47	8	[Rxlev1_86][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	48	8	[Rxlev1_87][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	49	8	[Rxlev1_88][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	50	8	[Rxlev1_89][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	51	8	[Rxlev1_90][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	52	8	[Rxlev1_91][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	53	8	[Rxlev1_92][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	54	8	[Rxlev1_93][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	55	8	[Rxlev1_94][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	56	8	[Rxlev1_95][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	57	8	[Rxlev1_96][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	58	8	[Rxlev1_97][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	59	8	[Rxlev1_98][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	60	8	[Rxlev1_99_120][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	61	5	[Rxlev1_Max][float]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	62	5	[Rxlev1_Min][float]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	63	5	[Rxlev1_Mean][float]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	64	8	[iRxlevnum2][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	65	8	[Rxlev2_10_45][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	66	8	[Rxlev2_46_50][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	67	8	[Rxlev2_51_55][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	68	8	[Rxlev2_56_60][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	69	8	[Rxlev2_61_65][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	70	8	[Rxlev2_66_70][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	71	8	[Rxlev2_71_75][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	72	8	[Rxlev2_76_80][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	73	8	[Rxlev2_81][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	74	8	[Rxlev2_82][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	75	8	[Rxlev2_83][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	76	8	[Rxlev2_84][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	77	8	[Rxlev2_85][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	78	8	[Rxlev2_86][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	79	8	[Rxlev2_87][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	80	8	[Rxlev2_88][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	81	8	[Rxlev2_89][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	82	8	[Rxlev2_90][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	83	8	[Rxlev2_91][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	84	8	[Rxlev2_92][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	85	8	[Rxlev2_93][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	86	8	[Rxlev2_94][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	87	8	[Rxlev2_95][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	88	8	[Rxlev2_96][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	89	8	[Rxlev2_97][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	90	8	[Rxlev2_98][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	91	8	[Rxlev2_99_120][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	92	5	[Rxlev2_Max][float]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	93	5	[Rxlev2_Min][float]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	94	5	[Rxlev2_Mean][float]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	95	8	[iRxlevnum3][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	96	8	[Rxlev3_10_45][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	97	8	[Rxlev3_46_50][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	98	8	[Rxlev3_51_55][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	99	8	[Rxlev3_56_60][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	100	8	[Rxlev3_61_65][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	101	8	[Rxlev3_66_70][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	102	8	[Rxlev3_71_75][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	103	8	[Rxlev3_76_80][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	104	8	[Rxlev3_81][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	105	8	[Rxlev3_82][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	106	8	[Rxlev3_83][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	107	8	[Rxlev3_84][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	108	8	[Rxlev3_85][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	109	8	[Rxlev3_86][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	110	8	[Rxlev3_87][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	111	8	[Rxlev3_88][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	112	8	[Rxlev3_89][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	113	8	[Rxlev3_90][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	114	8	[Rxlev3_91][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	115	8	[Rxlev3_92][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	116	8	[Rxlev3_93][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	117	8	[Rxlev3_94][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	118	8	[Rxlev3_95][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	119	8	[Rxlev3_96][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	120	8	[Rxlev3_97][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	121	8	[Rxlev3_98][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	122	8	[Rxlev3_99_120][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	123	5	[Rxlev3_Max][float]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	124	5	[Rxlev3_Min][float]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	125	5	[Rxlev3_Mean][float]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	126	8	[C_I1_20_9_num][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	127	8	[C_I1_9_12_num][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	128	8	[C_I1_12_15_num][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	129	8	[C_I1_15_20_num][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	130	8	[C_I1_20_40_num][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	131	5	[C_I1_20_9][float]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	132	5	[C_I1_9_12][float]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	133	5	[C_I1_12_15][float]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	134	5	[C_I1_15_20][float]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	135	5	[C_I1_20_40][float]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	136	5	[C_I1_Max][float]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	137	5	[C_I1_Min][float]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	138	8	[C_I2_20_9_num][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	139	8	[C_I2_9_12_num][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	140	8	[C_I2_12_15_num][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	141	8	[C_I2_15_20_num][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	142	8	[C_I2_20_40_num][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	143	5	[C_I2_20_9][float]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	144	5	[C_I2_9_12][float]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	145	5	[C_I2_12_15][float]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	146	5	[C_I2_15_20][float]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	147	5	[C_I2_20_40][float]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	148	5	[C_I2_Max][float]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	149	5	[C_I2_Min][float]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	150	8	[C_I3_20_9_num][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	151	8	[C_I3_9_12_num][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	152	8	[C_I3_12_15_num][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	153	8	[C_I3_15_20_num][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	154	8	[C_I3_20_40_num][int]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	155	5	[C_I3_20_9][float]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	156	5	[C_I3_9_12][float]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	157	5	[C_I3_12_15][float]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	158	5	[C_I3_15_20][float]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	159	5	[C_I3_20_40][float]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	160	5	[C_I3_Max][float]NULL,
1014	SEARCHTYPE_SCAN_KPI_GRID	161	5	[C_I3_Min][float]NULL
1015	SEARCHTYPE_SCAN_KPI_CELL	1	8	区域类型ID
1015	SEARCHTYPE_SCAN_KPI_CELL	2	8	区域ID
1015	SEARCHTYPE_SCAN_KPI_CELL	3	8	测试类型4BYTE
1015	SEARCHTYPE_SCAN_KPI_CELL	4	8	设备类型4BYTE
1015	SEARCHTYPE_SCAN_KPI_CELL	5	8	文件类型4BYTE
1015	SEARCHTYPE_SCAN_KPI_CELL	6	8	业务类型4BYTE
1015	SEARCHTYPE_SCAN_KPI_CELL	7	8	运营商类型4BYTE
1015	SEARCHTYPE_SCAN_KPI_CELL	8	8	项目类型4BYTE
1015	SEARCHTYPE_SCAN_KPI_CELL	9	8	所属轮次4BYTE
1015	SEARCHTYPE_SCAN_KPI_CELL	10	8	文件ID4BYTES
1015	SEARCHTYPE_SCAN_KPI_CELL	11	6	文件名称string
1015	SEARCHTYPE_SCAN_KPI_CELL	12	8	文件开始时间
1015	SEARCHTYPE_SCAN_KPI_CELL	13	8	测试里程
1015	SEARCHTYPE_SCAN_KPI_CELL	14	8	测试时长
1015	SEARCHTYPE_SCAN_KPI_CELL	15	8	[iRxlevnum1][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	16	8	[Rxlev1_10_45][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	17	8	[Rxlev1_46_50][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	18	8	[Rxlev1_51_55][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	19	8	[Rxlev1_56_60][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	20	8	[Rxlev1_61_65][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	21	8	[Rxlev1_66_70][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	22	8	[Rxlev1_71_75][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	23	8	[Rxlev1_76_80][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	24	8	[Rxlev1_81][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	25	8	[Rxlev1_82][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	26	8	[Rxlev1_83][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	27	8	[Rxlev1_84][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	28	8	[Rxlev1_85][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	29	8	[Rxlev1_86][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	30	8	[Rxlev1_87][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	31	8	[Rxlev1_88][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	32	8	[Rxlev1_89][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	33	8	[Rxlev1_90][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	34	8	[Rxlev1_91][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	35	8	[Rxlev1_92][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	36	8	[Rxlev1_93][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	37	8	[Rxlev1_94][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	38	8	[Rxlev1_95][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	39	8	[Rxlev1_96][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	40	8	[Rxlev1_97][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	41	8	[Rxlev1_98][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	42	8	[Rxlev1_99_120][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	43	5	[Rxlev1_Max][float]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	44	5	[Rxlev1_Min][float]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	45	5	[Rxlev1_Mean][float]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	46	8	[iRxlevnum2][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	47	8	[Rxlev2_10_45][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	48	8	[Rxlev2_46_50][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	49	8	[Rxlev2_51_55][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	50	8	[Rxlev2_56_60][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	51	8	[Rxlev2_61_65][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	52	8	[Rxlev2_66_70][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	53	8	[Rxlev2_71_75][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	54	8	[Rxlev2_76_80][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	55	8	[Rxlev2_81][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	56	8	[Rxlev2_82][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	57	8	[Rxlev2_83][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	58	8	[Rxlev2_84][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	59	8	[Rxlev2_85][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	60	8	[Rxlev2_86][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	61	8	[Rxlev2_87][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	62	8	[Rxlev2_88][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	63	8	[Rxlev2_89][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	64	8	[Rxlev2_90][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	65	8	[Rxlev2_91][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	66	8	[Rxlev2_92][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	67	8	[Rxlev2_93][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	68	8	[Rxlev2_94][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	69	8	[Rxlev2_95][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	70	8	[Rxlev2_96][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	71	8	[Rxlev2_97][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	72	8	[Rxlev2_98][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	73	8	[Rxlev2_99_120][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	74	5	[Rxlev2_Max][float]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	75	5	[Rxlev2_Min][float]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	76	5	[Rxlev2_Mean][float]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	77	8	[iRxlevnum3][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	78	8	[Rxlev3_10_45][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	79	8	[Rxlev3_46_50][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	80	8	[Rxlev3_51_55][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	81	8	[Rxlev3_56_60][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	82	8	[Rxlev3_61_65][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	83	8	[Rxlev3_66_70][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	84	8	[Rxlev3_71_75][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	85	8	[Rxlev3_76_80][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	86	8	[Rxlev3_81][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	87	8	[Rxlev3_82][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	88	8	[Rxlev3_83][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	89	8	[Rxlev3_84][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	90	8	[Rxlev3_85][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	91	8	[Rxlev3_86][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	92	8	[Rxlev3_87][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	93	8	[Rxlev3_88][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	94	8	[Rxlev3_89][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	95	8	[Rxlev3_90][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	96	8	[Rxlev3_91][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	97	8	[Rxlev3_92][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	98	8	[Rxlev3_93][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	99	8	[Rxlev3_94][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	100	8	[Rxlev3_95][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	101	8	[Rxlev3_96][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	102	8	[Rxlev3_97][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	103	8	[Rxlev3_98][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	104	8	[Rxlev3_99_120][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	105	5	[Rxlev3_Max][float]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	106	5	[Rxlev3_Min][float]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	107	5	[Rxlev3_Mean][float]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	108	8	[C_I1_20_9_num][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	109	8	[C_I1_9_12_num][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	110	8	[C_I1_12_15_num][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	111	8	[C_I1_15_20_num][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	112	8	[C_I1_20_40_num][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	113	5	[C_I1_20_9][float]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	114	5	[C_I1_9_12][float]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	115	5	[C_I1_12_15][float]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	116	5	[C_I1_15_20][float]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	117	5	[C_I1_20_40][float]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	118	5	[C_I1_Max][float]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	119	5	[C_I1_Min][float]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	120	8	[C_I2_20_9_num][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	121	8	[C_I2_9_12_num][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	122	8	[C_I2_12_15_num][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	123	8	[C_I2_15_20_num][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	124	8	[C_I2_20_40_num][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	125	5	[C_I2_20_9][float]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	126	5	[C_I2_9_12][float]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	127	5	[C_I2_12_15][float]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	128	5	[C_I2_15_20][float]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	129	5	[C_I2_20_40][float]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	130	5	[C_I2_Max][float]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	131	5	[C_I2_Min][float]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	132	8	[C_I3_20_9_num][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	133	8	[C_I3_9_12_num][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	134	8	[C_I3_12_15_num][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	135	8	[C_I3_15_20_num][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	136	8	[C_I3_20_40_num][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	137	5	[C_I3_20_9][float]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	138	5	[C_I3_9_12][float]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	139	5	[C_I3_12_15][float]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	140	5	[C_I3_15_20][float]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	141	5	[C_I3_20_40][float]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	142	5	[C_I3_Max][float]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	143	5	[C_I3_Min][float]NULL
1015	SEARCHTYPE_SCAN_KPI_CELL	144	8	[MaxDistance1][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	145	8	[MaxDistance2][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	146	8	[MaxDistance3][int]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	147	5	[MaxDistanceDir1][float]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	148	5	[MaxDistanceDir2][float]NULL,
1015	SEARCHTYPE_SCAN_KPI_CELL	149	5	[MaxDistanceDir3][float]NULL
1016	SEARCHTYPE_SCAN_KPI_AREA	1	8	区域类型ID
1016	SEARCHTYPE_SCAN_KPI_AREA	2	8	区域ID
1016	SEARCHTYPE_SCAN_KPI_AREA	3	8	测试类型4BYTE
1016	SEARCHTYPE_SCAN_KPI_AREA	4	8	设备类型4BYTE
1016	SEARCHTYPE_SCAN_KPI_AREA	5	8	文件类型4BYTE
1016	SEARCHTYPE_SCAN_KPI_AREA	6	8	业务类型4BYTE
1016	SEARCHTYPE_SCAN_KPI_AREA	7	8	运营商类型4BYTE
1016	SEARCHTYPE_SCAN_KPI_AREA	8	8	项目类型4BYTE
1016	SEARCHTYPE_SCAN_KPI_AREA	9	8	所属轮次4BYTE
1016	SEARCHTYPE_SCAN_KPI_AREA	10	8	文件ID4BYTES
1016	SEARCHTYPE_SCAN_KPI_AREA	11	6	文件名称string
1016	SEARCHTYPE_SCAN_KPI_AREA	12	8	文件开始时间
1016	SEARCHTYPE_SCAN_KPI_AREA	13	8	测试里程
1016	SEARCHTYPE_SCAN_KPI_AREA	14	8	测试时长
1016	SEARCHTYPE_SCAN_KPI_AREA	15	8	[iRxlevnum1] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	16	8	[Rxlev1_10_45] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	17	8	[Rxlev1_46_50] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	18	8	[Rxlev1_51_55] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	19	8	[Rxlev1_56_60] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	20	8	[Rxlev1_61_65] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	21	8	[Rxlev1_66_70] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	22	8	[Rxlev1_71_75] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	23	8	[Rxlev1_76_80] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	24	8	[Rxlev1_81] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	25	8	[Rxlev1_82] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	26	8	[Rxlev1_83] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	27	8	[Rxlev1_84] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	28	8	[Rxlev1_85] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	29	8	[Rxlev1_86] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	30	8	[Rxlev1_87] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	31	8	[Rxlev1_88] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	32	8	[Rxlev1_89] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	33	8	[Rxlev1_90] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	34	8	[Rxlev1_91] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	35	8	[Rxlev1_92] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	36	8	[Rxlev1_93] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	37	8	[Rxlev1_94] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	38	8	[Rxlev1_95] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	39	8	[Rxlev1_96] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	40	8	[Rxlev1_97] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	41	8	[Rxlev1_98] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	42	8	[Rxlev1_99_120] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	43	5	[Rxlev1_Max] [float] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	44	5	[Rxlev1_Min] [float] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	45	5	[Rxlev1_Mean] [float] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	46	8	[iRxlevnum2] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	47	8	[Rxlev2_10_45] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	48	8	[Rxlev2_46_50] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	49	8	[Rxlev2_51_55] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	50	8	[Rxlev2_56_60] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	51	8	[Rxlev2_61_65] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	52	8	[Rxlev2_66_70] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	53	8	[Rxlev2_71_75] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	54	8	[Rxlev2_76_80] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	55	8	[Rxlev2_81] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	56	8	[Rxlev2_82] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	57	8	[Rxlev2_83] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	58	8	[Rxlev2_84] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	59	8	[Rxlev2_85] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	60	8	[Rxlev2_86] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	61	8	[Rxlev2_87] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	62	8	[Rxlev2_88] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	63	8	[Rxlev2_89] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	64	8	[Rxlev2_90] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	65	8	[Rxlev2_91] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	66	8	[Rxlev2_92] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	67	8	[Rxlev2_93] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	68	8	[Rxlev2_94] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	69	8	[Rxlev2_95] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	70	8	[Rxlev2_96] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	71	8	[Rxlev2_97] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	72	8	[Rxlev2_98] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	73	8	[Rxlev2_99_120] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	74	5	[Rxlev2_Max] [float] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	75	5	[Rxlev2_Min] [float] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	76	5	[Rxlev2_Mean] [float] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	77	8	[iRxlevnum3] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	78	8	[Rxlev3_10_45] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	79	8	[Rxlev3_46_50] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	80	8	[Rxlev3_51_55] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	81	8	[Rxlev3_56_60] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	82	8	[Rxlev3_61_65] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	83	8	[Rxlev3_66_70] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	84	8	[Rxlev3_71_75] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	85	8	[Rxlev3_76_80] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	86	8	[Rxlev3_81] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	87	8	[Rxlev3_82] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	88	8	[Rxlev3_83] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	89	8	[Rxlev3_84] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	90	8	[Rxlev3_85] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	91	8	[Rxlev3_86] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	92	8	[Rxlev3_87] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	93	8	[Rxlev3_88] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	94	8	[Rxlev3_89] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	95	8	[Rxlev3_90] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	96	8	[Rxlev3_91] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	97	8	[Rxlev3_92] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	98	8	[Rxlev3_93] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	99	8	[Rxlev3_94] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	100	8	[Rxlev3_95] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	101	8	[Rxlev3_96] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	102	8	[Rxlev3_97] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	103	8	[Rxlev3_98] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	104	8	[Rxlev3_99_120] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	105	5	[Rxlev3_Max] [float] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	106	5	[Rxlev3_Min] [float] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	107	5	[Rxlev3_Mean] [float] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	108	8	[C_I1_20_9_num] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	109	8	[C_I1_9_12_num] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	110	8	[C_I1_12_15_num] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	111	8	[C_I1_15_20_num] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	112	8	[C_I1_20_40_num] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	113	5	[C_I1_20_9] [float] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	114	5	[C_I1_9_12] [float] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	115	5	[C_I1_12_15] [float] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	116	5	[C_I1_15_20] [float] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	117	5	[C_I1_20_40] [float] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	118	5	[C_I1_Max] [float] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	119	5	[C_I1_Min] [float] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	120	8	[C_I2_20_9_num] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	121	8	[C_I2_9_12_num] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	122	8	[C_I2_12_15_num] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	123	8	[C_I2_15_20_num] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	124	8	[C_I2_20_40_num] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	125	5	[C_I2_20_9] [float] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	126	5	[C_I2_9_12] [float] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	127	5	[C_I2_12_15] [float] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	128	5	[C_I2_15_20] [float] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	129	5	[C_I2_20_40] [float] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	130	5	[C_I2_Max] [float] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	131	5	[C_I2_Min] [float] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	132	8	[C_I3_20_9_num] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	133	8	[C_I3_9_12_num] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	134	8	[C_I3_12_15_num] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	135	8	[C_I3_15_20_num] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	136	8	[C_I3_20_40_num] [int] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	137	5	[C_I3_20_9] [float] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	138	5	[C_I3_9_12] [float] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	139	5	[C_I3_12_15] [float] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	140	5	[C_I3_15_20] [float] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	141	5	[C_I3_20_40] [float] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	142	5	[C_I3_Max] [float] NULL,
1016	SEARCHTYPE_SCAN_KPI_AREA	143	5	[C_I3_Min] [float] NULL
1017	SEARCHTYPE_SCAN_GIS	1	8	[itllongitude][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	2	8	[itllatitude][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	3	8	[ibrlongitude][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	4	8	[ibrlatitude][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	5	8	测试类型4BYTE
1017	SEARCHTYPE_SCAN_GIS	6	8	设备类型4BYTE
1017	SEARCHTYPE_SCAN_GIS	7	8	文件类型4BYTE
1017	SEARCHTYPE_SCAN_GIS	8	8	业务类型4BYTE
1017	SEARCHTYPE_SCAN_GIS	9	8	运营商类型4BYTE
1017	SEARCHTYPE_SCAN_GIS	10	8	项目类型4BYTE
1017	SEARCHTYPE_SCAN_GIS	11	8	所属轮次4BYTE
1017	SEARCHTYPE_SCAN_GIS	12	8	文件ID4BYTES
1017	SEARCHTYPE_SCAN_GIS	13	6	文件名称string
1017	SEARCHTYPE_SCAN_GIS	14	8	文件开始时间
1017	SEARCHTYPE_SCAN_GIS	15	8	测试里程
1017	SEARCHTYPE_SCAN_GIS	16	8	测试时长
1017	SEARCHTYPE_SCAN_GIS	17	8	[iRxlevnum1][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	18	8	[Rxlev1_10_45][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	19	8	[Rxlev1_46_50][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	20	8	[Rxlev1_51_55][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	21	8	[Rxlev1_56_60][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	22	8	[Rxlev1_61_65][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	23	8	[Rxlev1_66_70][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	24	8	[Rxlev1_71_75][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	25	8	[Rxlev1_76_80][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	26	8	[Rxlev1_81][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	27	8	[Rxlev1_82][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	28	8	[Rxlev1_83][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	29	8	[Rxlev1_84][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	30	8	[Rxlev1_85][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	31	8	[Rxlev1_86][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	32	8	[Rxlev1_87][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	33	8	[Rxlev1_88][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	34	8	[Rxlev1_89][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	35	8	[Rxlev1_90][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	36	8	[Rxlev1_91][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	37	8	[Rxlev1_92][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	38	8	[Rxlev1_93][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	39	8	[Rxlev1_94][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	40	8	[Rxlev1_95][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	41	8	[Rxlev1_96][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	42	8	[Rxlev1_97][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	43	8	[Rxlev1_98][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	44	8	[Rxlev1_99_120][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	45	5	[Rxlev1_Max][float]NULL,
1017	SEARCHTYPE_SCAN_GIS	46	5	[Rxlev1_Min][float]NULL,
1017	SEARCHTYPE_SCAN_GIS	47	5	[Rxlev1_Mean][float]NULL,
1017	SEARCHTYPE_SCAN_GIS	48	8	[iRxlevnum2][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	49	8	[Rxlev2_10_45][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	50	8	[Rxlev2_46_50][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	51	8	[Rxlev2_51_55][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	52	8	[Rxlev2_56_60][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	53	8	[Rxlev2_61_65][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	54	8	[Rxlev2_66_70][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	55	8	[Rxlev2_71_75][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	56	8	[Rxlev2_76_80][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	57	8	[Rxlev2_81][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	58	8	[Rxlev2_82][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	59	8	[Rxlev2_83][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	60	8	[Rxlev2_84][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	61	8	[Rxlev2_85][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	62	8	[Rxlev2_86][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	63	8	[Rxlev2_87][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	64	8	[Rxlev2_88][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	65	8	[Rxlev2_89][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	66	8	[Rxlev2_90][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	67	8	[Rxlev2_91][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	68	8	[Rxlev2_92][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	69	8	[Rxlev2_93][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	70	8	[Rxlev2_94][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	71	8	[Rxlev2_95][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	72	8	[Rxlev2_96][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	73	8	[Rxlev2_97][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	74	8	[Rxlev2_98][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	75	8	[Rxlev2_99_120][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	76	5	[Rxlev2_Max][float]NULL,
1017	SEARCHTYPE_SCAN_GIS	77	5	[Rxlev2_Min][float]NULL,
1017	SEARCHTYPE_SCAN_GIS	78	5	[Rxlev2_Mean][float]NULL,
1017	SEARCHTYPE_SCAN_GIS	79	8	[iRxlevnum3][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	80	8	[Rxlev3_10_45][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	81	8	[Rxlev3_46_50][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	82	8	[Rxlev3_51_55][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	83	8	[Rxlev3_56_60][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	84	8	[Rxlev3_61_65][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	85	8	[Rxlev3_66_70][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	86	8	[Rxlev3_71_75][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	87	8	[Rxlev3_76_80][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	88	8	[Rxlev3_81][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	89	8	[Rxlev3_82][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	90	8	[Rxlev3_83][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	91	8	[Rxlev3_84][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	92	8	[Rxlev3_85][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	93	8	[Rxlev3_86][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	94	8	[Rxlev3_87][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	95	8	[Rxlev3_88][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	96	8	[Rxlev3_89][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	97	8	[Rxlev3_90][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	98	8	[Rxlev3_91][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	99	8	[Rxlev3_92][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	100	8	[Rxlev3_93][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	101	8	[Rxlev3_94][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	102	8	[Rxlev3_95][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	103	8	[Rxlev3_96][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	104	8	[Rxlev3_97][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	105	8	[Rxlev3_98][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	106	8	[Rxlev3_99_120][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	107	5	[Rxlev3_Max][float]NULL,
1017	SEARCHTYPE_SCAN_GIS	108	5	[Rxlev3_Min][float]NULL,
1017	SEARCHTYPE_SCAN_GIS	109	5	[Rxlev3_Mean][float]NULL,
1017	SEARCHTYPE_SCAN_GIS	110	8	[C_I1_20_9_num][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	111	8	[C_I1_9_12_num][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	112	8	[C_I1_12_15_num][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	113	8	[C_I1_15_20_num][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	114	8	[C_I1_20_40_num][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	115	5	[C_I1_20_9][float]NULL,
1017	SEARCHTYPE_SCAN_GIS	116	5	[C_I1_9_12][float]NULL,
1017	SEARCHTYPE_SCAN_GIS	117	5	[C_I1_12_15][float]NULL,
1017	SEARCHTYPE_SCAN_GIS	118	5	[C_I1_15_20][float]NULL,
1017	SEARCHTYPE_SCAN_GIS	119	5	[C_I1_20_40][float]NULL,
1017	SEARCHTYPE_SCAN_GIS	120	5	[C_I1_Max][float]NULL,
1017	SEARCHTYPE_SCAN_GIS	121	5	[C_I1_Min][float]NULL,
1017	SEARCHTYPE_SCAN_GIS	122	8	[C_I2_20_9_num][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	123	8	[C_I2_9_12_num][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	124	8	[C_I2_12_15_num][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	125	8	[C_I2_15_20_num][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	126	8	[C_I2_20_40_num][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	127	5	[C_I2_20_9][float]NULL,
1017	SEARCHTYPE_SCAN_GIS	128	5	[C_I2_9_12][float]NULL,
1017	SEARCHTYPE_SCAN_GIS	129	5	[C_I2_12_15][float]NULL,
1017	SEARCHTYPE_SCAN_GIS	130	5	[C_I2_15_20][float]NULL,
1017	SEARCHTYPE_SCAN_GIS	131	5	[C_I2_20_40][float]NULL,
1017	SEARCHTYPE_SCAN_GIS	132	5	[C_I2_Max][float]NULL,
1017	SEARCHTYPE_SCAN_GIS	133	5	[C_I2_Min][float]NULL,
1017	SEARCHTYPE_SCAN_GIS	134	8	[C_I3_20_9_num][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	135	8	[C_I3_9_12_num][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	136	8	[C_I3_12_15_num][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	137	8	[C_I3_15_20_num][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	138	8	[C_I3_20_40_num][int]NULL,
1017	SEARCHTYPE_SCAN_GIS	139	5	[C_I3_20_9][float]NULL,
1017	SEARCHTYPE_SCAN_GIS	140	5	[C_I3_9_12][float]NULL,
1017	SEARCHTYPE_SCAN_GIS	141	5	[C_I3_12_15][float]NULL,
1017	SEARCHTYPE_SCAN_GIS	142	5	[C_I3_15_20][float]NULL,
1017	SEARCHTYPE_SCAN_GIS	143	5	[C_I3_20_40][float]NULL,
1017	SEARCHTYPE_SCAN_GIS	144	5	[C_I3_Max][float]NULL,
1017	SEARCHTYPE_SCAN_GIS	145	5	[C_I3_Min][float]NULL
1018	SEARCHTYPE_SCAN_CELL	1	8	文件ID4BYTES
1018	SEARCHTYPE_SCAN_CELL	2	8	采样点id4BYTE
1018	SEARCHTYPE_SCAN_CELL	3	8	采样点时间4BYTE
1018	SEARCHTYPE_SCAN_CELL	4	1	手机序列1BYTE
1018	SEARCHTYPE_SCAN_CELL	5	8	经度4BYTE
1018	SEARCHTYPE_SCAN_CELL	6	8	纬度4BYTE
1018	SEARCHTYPE_SCAN_CELL	7	8	后续采样点个数
1018	SEARCHTYPE_SCAN_CELL	8	6	strcellcode
1018	SEARCHTYPE_SCAN_CELL	9	2	wbcch2BYTE
1018	SEARCHTYPE_SCAN_CELL	10	1	bbsic1BYTE
1018	SEARCHTYPE_SCAN_CELL	11	5	berfloat
1018	SEARCHTYPE_SCAN_CELL	12	5	c_Ifloat
1018	SEARCHTYPE_SCAN_CELL	13	5	rxlevfloat
1018	SEARCHTYPE_SCAN_CELL	14	5	rxqualfloat
1019	SEARCHTYPE_HEALTH_GSM	1	8	测试类型4BYTE
1019	SEARCHTYPE_HEALTH_GSM	2	8	设备类型4BYTE
1019	SEARCHTYPE_HEALTH_GSM	3	8	文件类型4BYTE
1019	SEARCHTYPE_HEALTH_GSM	4	8	业务类型4BYTE
1019	SEARCHTYPE_HEALTH_GSM	5	8	运营商类型4BYTE
1019	SEARCHTYPE_HEALTH_GSM	6	8	项目类型4BYTE
1019	SEARCHTYPE_HEALTH_GSM	7	8	所属轮次4BYTE
1019	SEARCHTYPE_HEALTH_GSM	8	8	文件ID4BYTES
1019	SEARCHTYPE_HEALTH_GSM	9	6	文件名称string
1019	SEARCHTYPE_HEALTH_GSM	10	8	采样点时间4BYTE
1019	SEARCHTYPE_HEALTH_GSM	11	8	经度4BYTE
1019	SEARCHTYPE_HEALTH_GSM	12	8	纬度4BYTE
1019	SEARCHTYPE_HEALTH_GSM	13	8	主服小区的LAC
1019	SEARCHTYPE_HEALTH_GSM	14	8	主服小区的CI
1019	SEARCHTYPE_HEALTH_GSM	15	2	主服小区的BCCH
1019	SEARCHTYPE_HEALTH_GSM	16	1	主服小区的BSIC
1019	SEARCHTYPE_HEALTH_GSM	17	2	主服小区的Rxlev
1019	SEARCHTYPE_HEALTH_GSM	18	8	N1_LAC
1019	SEARCHTYPE_HEALTH_GSM	19	8	N1_CI
1019	SEARCHTYPE_HEALTH_GSM	20	2	N1_BCCH
1019	SEARCHTYPE_HEALTH_GSM	21	1	N1_BSIC
1019	SEARCHTYPE_HEALTH_GSM	22	2	N1_RxLev
1019	SEARCHTYPE_HEALTH_GSM	23	8	N2_LAC
1019	SEARCHTYPE_HEALTH_GSM	24	8	N2_CI
1019	SEARCHTYPE_HEALTH_GSM	25	2	N2_BCCH
1019	SEARCHTYPE_HEALTH_GSM	26	1	N2_BSIC
1019	SEARCHTYPE_HEALTH_GSM	27	2	N2_RxLev
1019	SEARCHTYPE_HEALTH_GSM	28	8	N3_LAC
1019	SEARCHTYPE_HEALTH_GSM	29	8	N3_CI
1019	SEARCHTYPE_HEALTH_GSM	30	2	N3_BCCH
1019	SEARCHTYPE_HEALTH_GSM	31	1	N3_BSIC
1019	SEARCHTYPE_HEALTH_GSM	32	2	N3_RxLev
1019	SEARCHTYPE_HEALTH_GSM	33	8	N_LAC
1019	SEARCHTYPE_HEALTH_GSM	34	8	N_CI
1019	SEARCHTYPE_HEALTH_GSM	35	2	N_BCCH
1019	SEARCHTYPE_HEALTH_GSM	36	1	N_BSIC
1019	SEARCHTYPE_HEALTH_GSM	37	2	N_RxLev
1019	SEARCHTYPE_HEALTH_GSM	38	8	N_LAC
1019	SEARCHTYPE_HEALTH_GSM	39	8	N_CI
1019	SEARCHTYPE_HEALTH_GSM	40	2	N_BCCH
1019	SEARCHTYPE_HEALTH_GSM	41	1	N_BSIC
1019	SEARCHTYPE_HEALTH_GSM	42	2	N_RxLev
1019	SEARCHTYPE_HEALTH_GSM	43	8	N_LAC
1019	SEARCHTYPE_HEALTH_GSM	44	8	N_CI
1019	SEARCHTYPE_HEALTH_GSM	45	2	N_BCCH
1019	SEARCHTYPE_HEALTH_GSM	46	1	N_BSIC
1019	SEARCHTYPE_HEALTH_GSM	47	2	N_RxLev
1019	SEARCHTYPE_HEALTH_GSM	48	8	N_LAC
1019	SEARCHTYPE_HEALTH_GSM	49	8	N_CI
1019	SEARCHTYPE_HEALTH_GSM	50	2	N_BCCH
1019	SEARCHTYPE_HEALTH_GSM	51	1	N_BSIC
1019	SEARCHTYPE_HEALTH_GSM	52	2	N_RxLev
1019	SEARCHTYPE_HEALTH_GSM	53	8	N_LAC
1019	SEARCHTYPE_HEALTH_GSM	54	8	N_CI
1019	SEARCHTYPE_HEALTH_GSM	55	2	N_BCCH
1019	SEARCHTYPE_HEALTH_GSM	56	1	N_BSIC
1019	SEARCHTYPE_HEALTH_GSM	57	2	N_RxLev
1019	SEARCHTYPE_HEALTH_GSM	58	8	N_LAC
1019	SEARCHTYPE_HEALTH_GSM	59	8	N_CI
1019	SEARCHTYPE_HEALTH_GSM	60	2	N_BCCH
1019	SEARCHTYPE_HEALTH_GSM	61	1	N_BSIC
1019	SEARCHTYPE_HEALTH_GSM	62	2	N_RxLev
1019	SEARCHTYPE_HEALTH_GSM	63	8	N_LAC
1019	SEARCHTYPE_HEALTH_GSM	64	8	N_CI
1019	SEARCHTYPE_HEALTH_GSM	65	2	N_BCCH
1019	SEARCHTYPE_HEALTH_GSM	66	1	N_BSIC
1019	SEARCHTYPE_HEALTH_GSM	67	2	N_RxLev
1019	SEARCHTYPE_HEALTH_GSM	68	8	N_LAC
1019	SEARCHTYPE_HEALTH_GSM	69	8	N_CI
1019	SEARCHTYPE_HEALTH_GSM	70	2	N_BCCH
1019	SEARCHTYPE_HEALTH_GSM	71	1	N_BSIC
1019	SEARCHTYPE_HEALTH_GSM	72	2	N_RxLev
1019	SEARCHTYPE_HEALTH_GSM	73	8	N_LAC
1019	SEARCHTYPE_HEALTH_GSM	74	8	N_CI
1019	SEARCHTYPE_HEALTH_GSM	75	2	N_BCCH
1019	SEARCHTYPE_HEALTH_GSM	76	1	N_BSIC
1019	SEARCHTYPE_HEALTH_GSM	77	2	N_RxLev
1019	SEARCHTYPE_HEALTH_GSM	78	8	N_LAC
1019	SEARCHTYPE_HEALTH_GSM	79	8	N_CI
1019	SEARCHTYPE_HEALTH_GSM	80	2	N_BCCH
1019	SEARCHTYPE_HEALTH_GSM	81	1	N_BSIC
1019	SEARCHTYPE_HEALTH_GSM	82	2	N_RxLev
1019	SEARCHTYPE_HEALTH_GSM	83	8	N_LAC
1019	SEARCHTYPE_HEALTH_GSM	84	8	N_CI
1019	SEARCHTYPE_HEALTH_GSM	85	2	N_BCCH
1019	SEARCHTYPE_HEALTH_GSM	86	1	N_BSIC
1019	SEARCHTYPE_HEALTH_GSM	87	2	N_RxLev
1019	SEARCHTYPE_HEALTH_GSM	88	8	N_LAC
1019	SEARCHTYPE_HEALTH_GSM	89	8	N_CI
1019	SEARCHTYPE_HEALTH_GSM	90	2	N_BCCH
1019	SEARCHTYPE_HEALTH_GSM	91	1	N_BSIC
1019	SEARCHTYPE_HEALTH_GSM	92	2	N_RxLev
1019	SEARCHTYPE_HEALTH_GSM	93	8	N_LAC
1019	SEARCHTYPE_HEALTH_GSM	94	8	N_CI
1019	SEARCHTYPE_HEALTH_GSM	95	2	N_BCCH
1019	SEARCHTYPE_HEALTH_GSM	96	1	N_BSIC
1019	SEARCHTYPE_HEALTH_GSM	97	2	N_RxLev
1019	SEARCHTYPE_HEALTH_GSM	98	8	N_LAC
1019	SEARCHTYPE_HEALTH_GSM	99	8	N_CI
1019	SEARCHTYPE_HEALTH_GSM	100	2	N_BCCH
1019	SEARCHTYPE_HEALTH_GSM	101	1	N_BSIC
1019	SEARCHTYPE_HEALTH_GSM	102	2	N_RxLev
1019	SEARCHTYPE_HEALTH_GSM	103	8	N_LAC
1019	SEARCHTYPE_HEALTH_GSM	104	8	N_CI
1019	SEARCHTYPE_HEALTH_GSM	105	2	N_BCCH
1019	SEARCHTYPE_HEALTH_GSM	106	1	N_BSIC
1019	SEARCHTYPE_HEALTH_GSM	107	2	N_RxLev
1019	SEARCHTYPE_HEALTH_GSM	108	8	N_LAC
1019	SEARCHTYPE_HEALTH_GSM	109	8	N_CI
1019	SEARCHTYPE_HEALTH_GSM	110	2	N_BCCH
1019	SEARCHTYPE_HEALTH_GSM	111	1	N_BSIC
1019	SEARCHTYPE_HEALTH_GSM	112	2	N_RxLev
1019	SEARCHTYPE_HEALTH_GSM	113	8	N_LAC
1019	SEARCHTYPE_HEALTH_GSM	114	8	N_CI
1019	SEARCHTYPE_HEALTH_GSM	115	2	N_BCCH
1019	SEARCHTYPE_HEALTH_GSM	116	1	N_BSIC
1019	SEARCHTYPE_HEALTH_GSM	117	2	N_RxLev
1020	SEARCHTYPE_HEALTH_SCAN	1	8	测试类型4BYTE
1020	SEARCHTYPE_HEALTH_SCAN	2	8	设备类型4BYTE
1020	SEARCHTYPE_HEALTH_SCAN	3	8	文件类型4BYTE
1020	SEARCHTYPE_HEALTH_SCAN	4	8	业务类型4BYTE
1020	SEARCHTYPE_HEALTH_SCAN	5	8	运营商类型4BYTE
1020	SEARCHTYPE_HEALTH_SCAN	6	8	项目类型4BYTE
1020	SEARCHTYPE_HEALTH_SCAN	7	8	所属轮次4BYTE
1020	SEARCHTYPE_HEALTH_SCAN	8	8	文件ID4BYTES
1020	SEARCHTYPE_HEALTH_SCAN	9	6	文件名称string
1020	SEARCHTYPE_HEALTH_SCAN	10	8	采样点时间4BYTE
1020	SEARCHTYPE_HEALTH_SCAN	11	8	经度4BYTE
1020	SEARCHTYPE_HEALTH_SCAN	12	8	纬度4BYTE
1020	SEARCHTYPE_HEALTH_SCAN	13	6	cellname
1020	SEARCHTYPE_HEALTH_SCAN	14	2	BCCH
1020	SEARCHTYPE_HEALTH_SCAN	15	1	BSIC
1020	SEARCHTYPE_HEALTH_SCAN	16	5	Rxlev
1021	SEARCHTYPE_ROAD_RATE	1	8	filetime
1021	SEARCHTYPE_ROAD_RATE	2	8	distance
1021	SEARCHTYPE_ROAD_RATE	3	8	tllongitude
1021	SEARCHTYPE_ROAD_RATE	4	8	tllatitude
1021	SEARCHTYPE_ROAD_RATE	5	8	brlongitude
1021	SEARCHTYPE_ROAD_RATE	6	8	brlatitude
1022	SEARCHTYPE_PROJECT_MAXTIME	1	8	projectid
1022	SEARCHTYPE_PROJECT_MAXTIME	2	8	istime
1023	SEARCHTYPE_TDSCDMA_SAMPLE_SUMMARY	1	8	文件ID4BYTES
1023	SEARCHTYPE_TDSCDMA_SAMPLE_SUMMARY	2	8	采样点id
1023	SEARCHTYPE_TDSCDMA_SAMPLE_SUMMARY	3	8	采样点时间
1023	SEARCHTYPE_TDSCDMA_SAMPLE_SUMMARY	4	2	毫秒
1023	SEARCHTYPE_TDSCDMA_SAMPLE_SUMMARY	5	1	手机序列
1023	SEARCHTYPE_TDSCDMA_SAMPLE_SUMMARY	6	8	经度
1023	SEARCHTYPE_TDSCDMA_SAMPLE_SUMMARY	7	8	纬度
1023	SEARCHTYPE_TDSCDMA_SAMPLE_SUMMARY	8	8	ScellCellID
1023	SEARCHTYPE_TDSCDMA_SAMPLE_SUMMARY	9	8	ScellCPI
1023	SEARCHTYPE_TDSCDMA_SAMPLE_SUMMARY	10	8	ScellUARFCN
1023	SEARCHTYPE_TDSCDMA_SAMPLE_SUMMARY	11	8	LAC
1023	SEARCHTYPE_TDSCDMA_SAMPLE_SUMMARY	12	8	CarrierRSSI
1023	SEARCHTYPE_TDSCDMA_SAMPLE_SUMMARY	13	8	PCCPCH_ISCP
1023	SEARCHTYPE_TDSCDMA_SAMPLE_SUMMARY	14	8	DPCH_ISCP
1023	SEARCHTYPE_TDSCDMA_SAMPLE_SUMMARY	15	8	PCCPCH_RSCP
1023	SEARCHTYPE_TDSCDMA_SAMPLE_SUMMARY	16	8	DPCH_RSCP
1023	SEARCHTYPE_TDSCDMA_SAMPLE_SUMMARY	17	8	TA
1023	SEARCHTYPE_TDSCDMA_SAMPLE_SUMMARY	18	8	PCCPCHSIR
1023	SEARCHTYPE_TDSCDMA_SAMPLE_SUMMARY	19	8	CCTRCHSIR
1023	SEARCHTYPE_TDSCDMA_SAMPLE_SUMMARY	20	8	DownlinkTargetSIR
1023	SEARCHTYPE_TDSCDMA_SAMPLE_SUMMARY	21	8	PCCPCH_C/I(dB)
1023	SEARCHTYPE_TDSCDMA_SAMPLE_SUMMARY	22	8	DPCH_C/I
1023	SEARCHTYPE_TDSCDMA_SAMPLE_SUMMARY	23	8	TxPower
1023	SEARCHTYPE_TDSCDMA_SAMPLE_SUMMARY	24	8	NCell_CPI_1
1023	SEARCHTYPE_TDSCDMA_SAMPLE_SUMMARY	25	8	NCell_CPI_2
1023	SEARCHTYPE_TDSCDMA_SAMPLE_SUMMARY	26	8	NCell_UARFCN_1
1023	SEARCHTYPE_TDSCDMA_SAMPLE_SUMMARY	27	8	NCell_UARFCN_2
1023	SEARCHTYPE_TDSCDMA_SAMPLE_SUMMARY	28	8	NCell_PCCPCH_RSCP_1
1023	SEARCHTYPE_TDSCDMA_SAMPLE_SUMMARY	29	8	NCell_PCCPCH_RSCP_2
1023	SEARCHTYPE_TDSCDMA_SAMPLE_SUMMARY	30	8	NCell_Rn_1
1023	SEARCHTYPE_TDSCDMA_SAMPLE_SUMMARY	31	8	NCell_Rn_2
1023	SEARCHTYPE_TDSCDMA_SAMPLE_SUMMARY	32	8	NCell_RSSI_1
1023	SEARCHTYPE_TDSCDMA_SAMPLE_SUMMARY	33	8	NCell_RSSI_2
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	1	8	文件ID4BYTES
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	2	8	[isampleid][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	3	8	[itime][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	4	2	[wtimems][smallint]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	5	1	[bms][tinyint]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	6	8	[ilongitude][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	7	8	[ilatitude][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	8	8	[Speed][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	9	8	[ScellCellID][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	10	8	[ScellCPI][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	11	8	[ScellUARFCN][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	12	1	[ScellDRXCoefficient][tinyint]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	13	1	[ScellBarred][tinyint]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	14	1	[ScellSIntraSearch][tinyint]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	15	1	[ScellSInterSearch][tinyint]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	16	1	[ScellTreselections][tinyint]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	17	1	[ScellQhysts][tinyint]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	18	8	[AssistantUARFCNSet][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	19	8	[UpPchShift][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	20	8	[AttachAllowed][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	21	8	[ServerHCS][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	22	8	[T3212][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	23	8	[MCC][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	24	8	[MNC][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	25	8	[LAC][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	26	8	[RAC][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	27	8	[Midamble][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	28	1	[RRC_State][tinyint]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	29	1	[MSMode][tinyint]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	30	1	[PS_Status][tinyint]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	31	8	[CarrierRSSI][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	32	8	[PCCPCH_ISCP][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	33	8	[TS_ISCP_VALUE_1][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	34	8	[TS_ISCP_VALUE_2][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	35	8	[TS_ISCP_VALUE_3][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	36	8	[TS_ISCP_VALUE_4][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	37	8	[TS_ISCP_VALUE_5][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	38	8	[TS_ISCP_VALUE_6][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	39	8	[TS_ISCP_VALUE_7][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	40	8	[DPCH_ISCP][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	41	8	[PCCPCH_RSCP][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	42	8	[PCCPCH_C2I][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	43	8	[TS_RSCP_VALUE_1][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	44	8	[TS_RSCP_VALUE_2][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	45	8	[TS_RSCP_VALUE_3][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	46	8	[TS_RSCP_VALUE_4][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	47	8	[TS_RSCP_VALUE_5][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	48	8	[TS_RSCP_VALUE_6][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	49	8	[TS_RSCP_VALUE_7][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	50	8	[DPCH_RSCP][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	51	8	[DPCH_C2I][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	52	8	[TA][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	53	8	[BLER_VALUE][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	54	8	[BLER_TotalBlock][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	55	8	[BLER_ErrBlock][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	56	8	[ScellPathloss][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	57	8	[ScellSrxlev][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	58	8	[ScellRs][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	59	8	[ScellRSSI][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	60	8	[PCCPCH_SIR][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	61	8	[CCTRCHSIR][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	62	8	[DownlinkTargetSIR][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	63	8	[TxPower][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	64	8	[MaxRxPower][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	65	8	[MaxAllowedTxPower][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	66	8	[SyncULTxPower][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	67	8	[RachULTxPower][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	68	8	[ChipWindow][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	69	8	[NCell_CPI_1][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	70	8	[NCell_CPI_2][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	71	8	[NCell_CPI_3][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	72	8	[NCell_CPI_4][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	73	8	[NCell_CPI_5][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	74	8	[NCell_CPI_6][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	75	8	[NCell_UARFCN_1][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	76	8	[NCell_UARFCN_2][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	77	8	[NCell_UARFCN_3][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	78	8	[NCell_UARFCN_4][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	79	8	[NCell_UARFCN_5][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	80	8	[NCell_UARFCN_6][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	81	8	[NCell_PCCPCH_RSCP_1][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	82	8	[NCell_PCCPCH_RSCP_2][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	83	8	[NCell_PCCPCH_RSCP_3][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	84	8	[NCell_PCCPCH_RSCP_4][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	85	8	[NCell_PCCPCH_RSCP_5][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	86	8	[NCell_PCCPCH_RSCP_6][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	87	8	[NCell_Pathloss_1][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	88	8	[NCell_Pathloss_2][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	89	8	[NCell_Pathloss_3][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	90	8	[NCell_Pathloss_4][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	91	8	[NCell_Pathloss_5][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	92	8	[NCell_Pathloss_6][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	93	8	[NCell_ISCPNum_1][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	94	8	[NCell_ISCPNum_2][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	95	8	[NCell_ISCPNum_3][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	96	8	[NCell_ISCPNum_4][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	97	8	[NCell_ISCPNum_5][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	98	8	[NCell_ISCPNum_6][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	99	8	[NCell_Rn_1][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	100	8	[NCell_Rn_2][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	101	8	[NCell_Rn_3][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	102	8	[NCell_Rn_4][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	103	8	[NCell_Rn_5][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	104	8	[NCell_Rn_6][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	105	8	[NCell_RSSI_1][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	106	8	[NCell_RSSI_2][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	107	8	[NCell_RSSI_3][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	108	8	[NCell_RSSI_4][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	109	8	[NCell_RSSI_5][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	110	8	[NCell_RSSI_6][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	111	8	[NCell_State_1][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	112	8	[NCell_State_2][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	113	8	[NCell_State_3][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	114	8	[NCell_State_4][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	115	8	[NCell_State_5][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	116	8	[NCell_State_6][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	117	8	[NGsmCell_arfcn_1][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	118	8	[NGsmCell_arfcn_2][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	119	8	[NGsmCell_arfcn_3][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	120	8	[NGsmCell_arfcn_4][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	121	8	[NGsmCell_arfcn_5][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	122	8	[NGsmCell_arfcn_6][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	123	8	[NGsmCell_bsic_1][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	124	8	[NGsmCell_bsic_2][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	125	8	[NGsmCell_bsic_3][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	126	8	[NGsmCell_bsic_4][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	127	8	[NGsmCell_bsic_5][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	128	8	[NGsmCell_bsic_6][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	129	8	[NGsmCell_RXLev_1][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	130	8	[NGsmCell_RXLev_2][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	131	8	[NGsmCell_RXLev_3][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	132	8	[NGsmCell_RXLev_4][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	133	8	[NGsmCell_RXLev_5][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	134	8	[NGsmCell_RXLev_6][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	135	8	[NGsmCell_c1_1][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	136	8	[NGsmCell_c1_2][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	137	8	[NGsmCell_c1_3][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	138	8	[NGsmCell_c1_4][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	139	8	[NGsmCell_c1_5][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	140	8	[NGsmCell_c1_6][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	141	8	[SCellgsm_bcch_arfcn][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	142	8	[SCellgsm_ci][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	143	8	[SCellgsm_c1][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	144	8	[Servgsm_bcchrxlev][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	145	8	[Servgsm_bsic][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	146	1	[Scellgsm_rxqualfull][tinyint]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	147	1	[Scellgsm_rxqualsub][tinyint]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	148	8	[Scellgsm_rxlevfull][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	149	8	[Scellgsm_rxlevsub][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	150	1	[Scellgsm_speechcode][tinyint]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	151	1	[Scellgsm_RTL_max][tinyint]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	152	1	[Scellgsm_RTL_cur][tinyint]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	153	8	[MOS_Value][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	154	8	[RLC_DL_R4_Rate][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	155	8	[RLC_UL_R4_Rate][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	156	8	[RLC_DL_GPRS_Rate][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	157	8	[RLC_UL_GPRS_Rate][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	158	8	[APP_DL_R4_Rate][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	159	8	[APP_DL_GPRS_Rate][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	160	8	[FTP_DL_Bytes][int]
1024	SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	161	8	[FTP_Status][int]
1025	SEARCHTYPE_TDSCDMA_GRID_AMR	1	8	ifileid
1025	SEARCHTYPE_TDSCDMA_GRID_AMR	2	8	左上经度(4BYTES)
1025	SEARCHTYPE_TDSCDMA_GRID_AMR	3	8	左上纬度(4BYTES)
1025	SEARCHTYPE_TDSCDMA_GRID_AMR	4	8	右下经度(4BYTES)
1025	SEARCHTYPE_TDSCDMA_GRID_AMR	5	8	右下纬度(4BYTES)
1025	SEARCHTYPE_TDSCDMA_GRID_AMR	6	8	[ibler_max][int]
1025	SEARCHTYPE_TDSCDMA_GRID_AMR	7	8	[ibler_min][int]
1025	SEARCHTYPE_TDSCDMA_GRID_AMR	8	8	[iPCCPCH_RSCP_max][int]
1025	SEARCHTYPE_TDSCDMA_GRID_AMR	9	8	[iPCCPCH_RSCP_min][int]
1025	SEARCHTYPE_TDSCDMA_GRID_AMR	10	8	[iPCCPCH_RSCP_mean][int]
1025	SEARCHTYPE_TDSCDMA_GRID_AMR	11	8	[iPCCPCH_C2I_max][int]
1025	SEARCHTYPE_TDSCDMA_GRID_AMR	12	8	[iPCCPCH_C2I_min][int]
1025	SEARCHTYPE_TDSCDMA_GRID_AMR	13	8	[iPCCPCH_C2I_mean][int]
1025	SEARCHTYPE_TDSCDMA_GRID_AMR	14	8	[iDPCH_C2I_max][int]
1025	SEARCHTYPE_TDSCDMA_GRID_AMR	15	8	[iDPCH_C2I_min][int]
1025	SEARCHTYPE_TDSCDMA_GRID_AMR	16	8	[iDPCH_C2I_mean][int]
1025	SEARCHTYPE_TDSCDMA_GRID_AMR	17	8	文件开始时间
1025	SEARCHTYPE_TDSCDMA_GRID_AMR	18	8	[iduration_TD][int]
1025	SEARCHTYPE_TDSCDMA_GRID_AMR	19	8	[iduration_GSM][int]
1025	SEARCHTYPE_TDSCDMA_GRID_AMR	20	8	[idistance_TD][int]
1025	SEARCHTYPE_TDSCDMA_GRID_AMR	21	8	[idistance_GSM][int]
1025	SEARCHTYPE_TDSCDMA_GRID_AMR	22	8	iLAC1
1025	SEARCHTYPE_TDSCDMA_GRID_AMR	23	8	iCI1
1025	SEARCHTYPE_TDSCDMA_GRID_AMR	24	8	isamplenum1
1025	SEARCHTYPE_TDSCDMA_GRID_AMR	25	8	ipccpch_rscpmean1
1025	SEARCHTYPE_TDSCDMA_GRID_AMR	26	8	iLAC2
1025	SEARCHTYPE_TDSCDMA_GRID_AMR	27	8	iCI2
1025	SEARCHTYPE_TDSCDMA_GRID_AMR	28	8	isamplenum2
1025	SEARCHTYPE_TDSCDMA_GRID_AMR	29	8	ipccpch_rscpmean2
1025	SEARCHTYPE_TDSCDMA_GRID_AMR	30	8	iLAC3
1025	SEARCHTYPE_TDSCDMA_GRID_AMR	31	8	iCI3
1025	SEARCHTYPE_TDSCDMA_GRID_AMR	32	8	isamplenum3
1025	SEARCHTYPE_TDSCDMA_GRID_AMR	33	8	ipccpch_rscpmean3
1025	SEARCHTYPE_TDSCDMA_GRID_AMR	34	8	iLAC4
1025	SEARCHTYPE_TDSCDMA_GRID_AMR	35	8	iCI4
1025	SEARCHTYPE_TDSCDMA_GRID_AMR	36	8	isamplenum4
1025	SEARCHTYPE_TDSCDMA_GRID_AMR	37	8	ipccpch_rscpmean4
1025	SEARCHTYPE_TDSCDMA_GRID_AMR	38	8	[ibler_sample][int]
1025	SEARCHTYPE_TDSCDMA_GRID_AMR	39	8	[ibler_total][int]
1025	SEARCHTYPE_TDSCDMA_GRID_AMR	40	8	[ibler_err][int]
1025	SEARCHTYPE_TDSCDMA_GRID_AMR	41	8	[iPCCPCH_sample][int]
1025	SEARCHTYPE_TDSCDMA_GRID_AMR	42	8	[iPCCPCH_RSCP_CI_95_sample][int]
1025	SEARCHTYPE_TDSCDMA_GRID_AMR	43	8	[iPCCPCH_RSCP_CI_90_sample][int]
1025	SEARCHTYPE_TDSCDMA_GRID_AMR	44	8	[iDPCH_C2I_sample][int]
1025	SEARCHTYPE_TDSCDMA_GRID_AMR	45	8	[iDPCH_C2I_F3_sample][int]
1025	SEARCHTYPE_TDSCDMA_GRID_AMR	46	8	[iRxlev_sample][int]
1025	SEARCHTYPE_TDSCDMA_GRID_AMR	47	8	[iRxlev_F75][int]
1025	SEARCHTYPE_TDSCDMA_GRID_AMR	48	8	[iRxlev_F80][int]
1025	SEARCHTYPE_TDSCDMA_GRID_AMR	49	8	[iRxlev_F85][int]
1025	SEARCHTYPE_TDSCDMA_GRID_AMR	50	8	[iRxlev_F90][int]
1025	SEARCHTYPE_TDSCDMA_GRID_AMR	51	8	[iRxlev_F94][int]
1025	SEARCHTYPE_TDSCDMA_GRID_AMR	52	8	[iRxlev_F100][int]
1025	SEARCHTYPE_TDSCDMA_GRID_AMR	53	8	[iMOS_sample][int]
1025	SEARCHTYPE_TDSCDMA_GRID_AMR	54	8	[iMOS_total][int]
1026	SEARCHTYPE_TDSCDMA_GRID_PS	1	8	ifileid
1026	SEARCHTYPE_TDSCDMA_GRID_PS	2	8	左上经度(4BYTES)
1026	SEARCHTYPE_TDSCDMA_GRID_PS	3	8	左上纬度(4BYTES)
1026	SEARCHTYPE_TDSCDMA_GRID_PS	4	8	右下经度(4BYTES)
1026	SEARCHTYPE_TDSCDMA_GRID_PS	5	8	右下纬度(4BYTES)
1026	SEARCHTYPE_TDSCDMA_GRID_PS	6	8	文件开始时间
1026	SEARCHTYPE_TDSCDMA_GRID_PS	7	8	[iduration_TD][int]
1026	SEARCHTYPE_TDSCDMA_GRID_PS	8	8	[iduration_GPRSEDGE][int]
1026	SEARCHTYPE_TDSCDMA_GRID_PS	9	8	[idistance_TD][int]
1026	SEARCHTYPE_TDSCDMA_GRID_PS	10	8	[idistance_GPRSEDGE][int]
1026	SEARCHTYPE_TDSCDMA_GRID_PS	11	8	iLAC1
1026	SEARCHTYPE_TDSCDMA_GRID_PS	12	8	iCI1
1026	SEARCHTYPE_TDSCDMA_GRID_PS	13	8	isamplenum1
1026	SEARCHTYPE_TDSCDMA_GRID_PS	14	8	ipccpch_rscpmean1
1026	SEARCHTYPE_TDSCDMA_GRID_PS	15	8	iLAC2
1026	SEARCHTYPE_TDSCDMA_GRID_PS	16	8	iCI2
1026	SEARCHTYPE_TDSCDMA_GRID_PS	17	8	isamplenum2
1026	SEARCHTYPE_TDSCDMA_GRID_PS	18	8	ipccpch_rscpmean2
1026	SEARCHTYPE_TDSCDMA_GRID_PS	19	8	iLAC3
1026	SEARCHTYPE_TDSCDMA_GRID_PS	20	8	iCI3
1026	SEARCHTYPE_TDSCDMA_GRID_PS	21	8	isamplenum3
1026	SEARCHTYPE_TDSCDMA_GRID_PS	22	8	ipccpch_rscpmean3
1026	SEARCHTYPE_TDSCDMA_GRID_PS	23	8	iLAC4
1026	SEARCHTYPE_TDSCDMA_GRID_PS	24	8	iCI4
1026	SEARCHTYPE_TDSCDMA_GRID_PS	25	8	isamplenum4
1026	SEARCHTYPE_TDSCDMA_GRID_PS	26	8	ipccpch_rscpmean4
1026	SEARCHTYPE_TDSCDMA_GRID_PS	27	8	[iBRU_sample][int]
1026	SEARCHTYPE_TDSCDMA_GRID_PS	28	8	[iBRU_value][int]
1026	SEARCHTYPE_TDSCDMA_GRID_PS	29	8	[iPS384_value][int]
1026	SEARCHTYPE_TDSCDMA_GRID_PS	30	8	[iPS128_value][int]
1026	SEARCHTYPE_TDSCDMA_GRID_PS	31	8	[iPS64_value][int]
1026	SEARCHTYPE_TDSCDMA_GRID_PS	32	8	[iGPRSEDGE_value][int]
1026	SEARCHTYPE_TDSCDMA_GRID_PS	33	8	[iBler_R4_sample][int]
1026	SEARCHTYPE_TDSCDMA_GRID_PS	34	8	[iBler_R4_total][int]
1026	SEARCHTYPE_TDSCDMA_GRID_PS	35	8	[iBler_GPRSEDGE_sample][int]
1026	SEARCHTYPE_TDSCDMA_GRID_PS	36	8	[iBler_GPRSEDGE_total][int]
1026	SEARCHTYPE_TDSCDMA_GRID_PS	37	8	[iRLC_R4_DL_sample][int]
1026	SEARCHTYPE_TDSCDMA_GRID_PS	38	8	[iRLC_R4_DL_value][int]
1026	SEARCHTYPE_TDSCDMA_GRID_PS	39	8	[iRLC_GPRSEDGE_DL_sample][int]
1026	SEARCHTYPE_TDSCDMA_GRID_PS	40	8	[iRLC_GPRSEDGE_DL_value][int]
1026	SEARCHTYPE_TDSCDMA_GRID_PS	41	8	[iAPP_R4_DL_sample][int]
1026	SEARCHTYPE_TDSCDMA_GRID_PS	42	8	[iAPP_R4_DL_value][int]
1026	SEARCHTYPE_TDSCDMA_GRID_PS	43	8	[iAPP_GPRSEDGE_DL_sample][int]
1026	SEARCHTYPE_TDSCDMA_GRID_PS	44	8	[iAPP_GPRSEDGE_DL_value][int]
1026	SEARCHTYPE_TDSCDMA_GRID_PS	45	8	[iFTP_R4_BYTES_DL][int]
1026	SEARCHTYPE_TDSCDMA_GRID_PS	46	8	[iFTP_R4_TIME_DL][int]
1026	SEARCHTYPE_TDSCDMA_GRID_PS	47	8	[iFTP_GPRSEDGE_BYTES_DL][int]
1026	SEARCHTYPE_TDSCDMA_GRID_PS	48	8	[iFTP_GPRSEDGE_TIME_DL][int]
1027	SEARCHTYPE_TDSCDMA_GRID_VP	1	8	ifileid
1027	SEARCHTYPE_TDSCDMA_GRID_VP	2	8	左上经度(4BYTES)
1027	SEARCHTYPE_TDSCDMA_GRID_VP	3	8	左上纬度(4BYTES)
1027	SEARCHTYPE_TDSCDMA_GRID_VP	4	8	右下经度(4BYTES)
1027	SEARCHTYPE_TDSCDMA_GRID_VP	5	8	右下纬度(4BYTES)
1027	SEARCHTYPE_TDSCDMA_GRID_VP	6	8	[iBler_max][int]
1027	SEARCHTYPE_TDSCDMA_GRID_VP	7	8	[iBler_min][int]
1027	SEARCHTYPE_TDSCDMA_GRID_VP	8	8	文件开始时间
1027	SEARCHTYPE_TDSCDMA_GRID_VP	9	8	[iduration][int]
1027	SEARCHTYPE_TDSCDMA_GRID_VP	10	8	[idistance][int]
1027	SEARCHTYPE_TDSCDMA_GRID_VP	11	8	iLAC1
1027	SEARCHTYPE_TDSCDMA_GRID_VP	12	8	iCI1
1027	SEARCHTYPE_TDSCDMA_GRID_VP	13	8	isamplenum1
1027	SEARCHTYPE_TDSCDMA_GRID_VP	14	8	ipccpch_rscpmean1
1027	SEARCHTYPE_TDSCDMA_GRID_VP	15	8	iLAC2
1027	SEARCHTYPE_TDSCDMA_GRID_VP	16	8	iCI2
1027	SEARCHTYPE_TDSCDMA_GRID_VP	17	8	isamplenum2
1027	SEARCHTYPE_TDSCDMA_GRID_VP	18	8	ipccpch_rscpmean2
1027	SEARCHTYPE_TDSCDMA_GRID_VP	19	8	iLAC3
1027	SEARCHTYPE_TDSCDMA_GRID_VP	20	8	iCI3
1027	SEARCHTYPE_TDSCDMA_GRID_VP	21	8	isamplenum3
1027	SEARCHTYPE_TDSCDMA_GRID_VP	22	8	ipccpch_rscpmean3
1027	SEARCHTYPE_TDSCDMA_GRID_VP	23	8	iLAC4
1027	SEARCHTYPE_TDSCDMA_GRID_VP	24	8	iCI4
1027	SEARCHTYPE_TDSCDMA_GRID_VP	25	8	isamplenum4
1027	SEARCHTYPE_TDSCDMA_GRID_VP	26	8	ipccpch_rscpmean4
1027	SEARCHTYPE_TDSCDMA_GRID_VP	27	8	[iBler_sample][int]
1027	SEARCHTYPE_TDSCDMA_GRID_VP	28	8	[iBler_total][int]
1027	SEARCHTYPE_TDSCDMA_GRID_VP	29	8	[iBler_err][int]
1028	SEARCHTYPE_BLACKBLOCK_INFO	1	8	[id] [int],
1028	SEARCHTYPE_BLACKBLOCK_INFO	2	8	[status] [int],
1028	SEARCHTYPE_BLACKBLOCK_INFO	3	8	[created_date] [int],
1028	SEARCHTYPE_BLACKBLOCK_INFO	4	8	[closed_date] [int],
1028	SEARCHTYPE_BLACKBLOCK_INFO	5	8	[first_abnormal_date] [int],
1028	SEARCHTYPE_BLACKBLOCK_INFO	6	8	[last_abnormal_date] [int],
1028	SEARCHTYPE_BLACKBLOCK_INFO	7	8	[last_test_date] [int],
1028	SEARCHTYPE_BLACKBLOCK_INFO	8	8	[abnormal_event_count] [int],
1028	SEARCHTYPE_BLACKBLOCK_INFO	9	8	[abnormal_days] [int],
1028	SEARCHTYPE_BLACKBLOCK_INFO	10	8	[normal_days] [int],
1028	SEARCHTYPE_BLACKBLOCK_INFO	11	6	[area_id] [varchar](255),
1028	SEARCHTYPE_BLACKBLOCK_INFO	12	6	[area_names] [varchar](255),
1028	SEARCHTYPE_BLACKBLOCK_INFO	13	6	[road_names] [varchar](255),
1028	SEARCHTYPE_BLACKBLOCK_INFO	14	6	[cell_names] [varchar](255),
1028	SEARCHTYPE_BLACKBLOCK_INFO	15	6	[name] [varchar](255),
1028	SEARCHTYPE_BLACKBLOCK_INFO	16	6	[reason] [varchar](2000),
1028	SEARCHTYPE_BLACKBLOCK_INFO	17	6	[attfiledesc] [varchar](2000)
1028	SEARCHTYPE_BLACKBLOCK_INFO	18	8	type int
1028	SEARCHTYPE_BLACKBLOCK_INFO	19	8	dwfiledate int
1028	SEARCHTYPE_BLACKBLOCK_INFO	20	8	gooddays_count int,
1028	SEARCHTYPE_BLACKBLOCK_INFO	21	8	last_validate_date int,
1028	SEARCHTYPE_BLACKBLOCK_INFO	22	8	validate_status int
1028	SEARCHTYPE_BLACKBLOCK_INFO	23	6	[griddesc] [varchar](2000)
1028	SEARCHTYPE_BLACKBLOCK_INFO	24	6	[handleuser] [varchar](2000)
1029	SEARCHTYPE_BLACKBLOCK_EVENT	1	8	[black_block_id] [int],
1029	SEARCHTYPE_BLACKBLOCK_EVENT	2	8	[file_id] [int],
1029	SEARCHTYPE_BLACKBLOCK_EVENT	3	8	[sn] [int],
1029	SEARCHTYPE_BLACKBLOCK_EVENT	4	8	[longitude] [int],
1029	SEARCHTYPE_BLACKBLOCK_EVENT	5	8	[latitude] [int],
1029	SEARCHTYPE_BLACKBLOCK_EVENT	6	8	[event_id] [int],
1029	SEARCHTYPE_BLACKBLOCK_EVENT	7	8	[lac] [int],
1029	SEARCHTYPE_BLACKBLOCK_EVENT	8	8	[ci] [int],
1029	SEARCHTYPE_BLACKBLOCK_EVENT	9	8	[project_id] [int],
1029	SEARCHTYPE_BLACKBLOCK_EVENT	10	8	[timevalue] [int],
1029	SEARCHTYPE_BLACKBLOCK_EVENT	11	8	[type] [int]
1029	SEARCHTYPE_BLACKBLOCK_EVENT	12	6	strfilename varchar(255)
1029	SEARCHTYPE_BLACKBLOCK_EVENT	13	8	iservicetype int
1029	SEARCHTYPE_BLACKBLOCK_EVENT	14	6	strsampletbname varchar(255)
1030	SEARCHTYPE_COMMUNITY_INFO	1	8	[communityid] [int] ,
1030	SEARCHTYPE_COMMUNITY_INFO	2	6	cityname
1030	SEARCHTYPE_COMMUNITY_INFO	3	6	districtname 
1030	SEARCHTYPE_COMMUNITY_INFO	4	6	[comname] [varchar](50) ,
1030	SEARCHTYPE_COMMUNITY_INFO	5	6	[comprop] char(50),
1030	SEARCHTYPE_COMMUNITY_INFO	6	8	[longitude] [int] ,
1030	SEARCHTYPE_COMMUNITY_INFO	7	8	[latitude] [int] ,
1030	SEARCHTYPE_COMMUNITY_INFO	8	6	[cmcccoverage] [char(50)] ,
1030	SEARCHTYPE_COMMUNITY_INFO	9	6	[districtrange] [varchar](50) ,
1030	SEARCHTYPE_COMMUNITY_INFO	10	6	[comscale] [varchar](50) ,
1030	SEARCHTYPE_COMMUNITY_INFO	11	8	[area] [int] ,
1030	SEARCHTYPE_COMMUNITY_INFO	12	8	[liftnum] [int] ,
1030	SEARCHTYPE_COMMUNITY_INFO	13	8	[garagearea] [int] ,
1030	SEARCHTYPE_COMMUNITY_INFO	14	8	[garagenum] [int] ,
1030	SEARCHTYPE_COMMUNITY_INFO	15	8	[price] [int] ,
1030	SEARCHTYPE_COMMUNITY_INFO	16	6	[contact] [varchar](50),
1030	SEARCHTYPE_COMMUNITY_INFO	17	6	[picture] [varchar](100),
1030	SEARCHTYPE_COMMUNITY_INFO	18	6	[note] [varchar](500),
1030	SEARCHTYPE_COMMUNITY_INFO	19	6	[description] [varchar](500) ,
1030	SEARCHTYPE_COMMUNITY_INFO	20	6	[advice] [varchar](500),
1030	SEARCHTYPE_COMMUNITY_INFO	21	8	topnum int
1030	SEARCHTYPE_COMMUNITY_INFO	22	8	totalnum int
1030	SEARCHTYPE_COMMUNITY_INFO	23	6	comlevel varchar(50)
1030	SEARCHTYPE_COMMUNITY_INFO	24	8	peoplenum int
1031	SEARCHTYPE_COMMUNITY_BUILIDNG	1	8	buildingid int ,
1031	SEARCHTYPE_COMMUNITY_BUILIDNG	2	6	[cityname] [varchar](50) 
1031	SEARCHTYPE_COMMUNITY_BUILIDNG	3	6	[districtname] [varchar](50)
1031	SEARCHTYPE_COMMUNITY_BUILIDNG	4	6	[comname] [varchar](50) ,
1031	SEARCHTYPE_COMMUNITY_BUILIDNG	5	6	buildingname varchar(50) ,
1031	SEARCHTYPE_COMMUNITY_BUILIDNG	6	8	communityid int ,
1031	SEARCHTYPE_COMMUNITY_BUILIDNG	7	6	[cmccGSM] [varchar](50)
1031	SEARCHTYPE_COMMUNITY_BUILIDNG	8	8	cmccResult int ,
1031	SEARCHTYPE_COMMUNITY_BUILIDNG	9	6	[cuComparecmcc] [varchar](50)
1031	SEARCHTYPE_COMMUNITY_BUILIDNG	10	6	[ctComparecmcc] [varchar](50)
1031	SEARCHTYPE_COMMUNITY_BUILIDNG	11	8	testtime int ,
1031	SEARCHTYPE_COMMUNITY_BUILIDNG	12	6	description varchar(100) ,
1031	SEARCHTYPE_COMMUNITY_BUILIDNG	13	8	cmccCoverPercent int
1031	SEARCHTYPE_COMMUNITY_BUILIDNG	14	8	cuCoverPercent int
1031	SEARCHTYPE_COMMUNITY_BUILIDNG	15	8	dtCoverPercent int
1032	SEARCHTYPE_COMMUNITY_TESTPOINT	1	8	testpointid int ,
1032	SEARCHTYPE_COMMUNITY_TESTPOINT	2	6	[cityname] [varchar](50) 
1032	SEARCHTYPE_COMMUNITY_TESTPOINT	3	6	[districtname] [varchar](50)
1032	SEARCHTYPE_COMMUNITY_TESTPOINT	4	6	[comname] [varchar](50) ,
1032	SEARCHTYPE_COMMUNITY_TESTPOINT	5	6	buildingname varchar(50) ,
1032	SEARCHTYPE_COMMUNITY_TESTPOINT	6	6	testpointname varchar(50),
1032	SEARCHTYPE_COMMUNITY_TESTPOINT	7	8	buildingid int ,
1032	SEARCHTYPE_COMMUNITY_TESTPOINT	8	8	communityid int ,
1032	SEARCHTYPE_COMMUNITY_TESTPOINT	9	6	verticalpos varchar(100),
1032	SEARCHTYPE_COMMUNITY_TESTPOINT	10	6	posprop int ,--[posprop] [varchar](50)
1032	SEARCHTYPE_COMMUNITY_TESTPOINT	11	8	longitude int ,
1032	SEARCHTYPE_COMMUNITY_TESTPOINT	12	8	latitude int ,
1032	SEARCHTYPE_COMMUNITY_TESTPOINT	13	8	cmccRxlev int ,
1032	SEARCHTYPE_COMMUNITY_TESTPOINT	14	6	staticservingincellLAC varchar(50),
1032	SEARCHTYPE_COMMUNITY_TESTPOINT	15	6	staticservingincellCID varchar(50),
1032	SEARCHTYPE_COMMUNITY_TESTPOINT	16	8	ta int ,
1032	SEARCHTYPE_COMMUNITY_TESTPOINT	17	6	voicequality int ,--[voicequality][varchar](50)
1032	SEARCHTYPE_COMMUNITY_TESTPOINT	18	6	activeservingincellLAC varchar(50),
1032	SEARCHTYPE_COMMUNITY_TESTPOINT	19	6	activeservingincellCID varchar(50),
1032	SEARCHTYPE_COMMUNITY_TESTPOINT	20	8	cuRexlev int ,
1032	SEARCHTYPE_COMMUNITY_TESTPOINT	21	8	ctCDMA int ,
1032	SEARCHTYPE_COMMUNITY_TESTPOINT	22	6	testperson varchar(255),
1032	SEARCHTYPE_COMMUNITY_TESTPOINT	23	8	testtime int ,
1032	SEARCHTYPE_COMMUNITY_TESTPOINT	24	6	testphonenum varchar(50),
1032	SEARCHTYPE_COMMUNITY_TESTPOINT	25	6	note varchar(200),
1033	SEARCHTYPE_COMMUNITY_BUILDING_POS	1	8	    ilongitude int NULL,
1033	SEARCHTYPE_COMMUNITY_BUILDING_POS	2	8	    ilatitude  int NULL,
1034	SEARCHTYPE_ROADMONIT_INFO	1	8	[id] [int] 
1034	SEARCHTYPE_ROADMONIT_INFO	2	8	[alert_day] [int] 
1034	SEARCHTYPE_ROADMONIT_INFO	3	6	[area_id] [varchar](255)
1034	SEARCHTYPE_ROADMONIT_INFO	4	6	[area_names] [varchar](255)
1034	SEARCHTYPE_ROADMONIT_INFO	5	6	[road_names] [varchar](255)
1034	SEARCHTYPE_ROADMONIT_INFO	6	6	[cell_names] [varchar](255)
1034	SEARCHTYPE_ROADMONIT_INFO	7	6	[dwdesc] [varchar](255) 
1035	SEARCHTYPE_ROADMONIT_EVENT	1	8	[monit_id] [int]
1035	SEARCHTYPE_ROADMONIT_EVENT	2	8	[file_id] [int]
1035	SEARCHTYPE_ROADMONIT_EVENT	3	8	[sn] [int]
1035	SEARCHTYPE_ROADMONIT_EVENT	4	8	[longitude] [int]
1035	SEARCHTYPE_ROADMONIT_EVENT	5	8	[latitude] [int]
1035	SEARCHTYPE_ROADMONIT_EVENT	6	8	[event_id] [int]
1035	SEARCHTYPE_ROADMONIT_EVENT	7	8	[lac] [int]
1035	SEARCHTYPE_ROADMONIT_EVENT	8	8	[ci] [int]
1035	SEARCHTYPE_ROADMONIT_EVENT	9	8	[project_id] [int]
1035	SEARCHTYPE_ROADMONIT_EVENT	10	8	[timevalue] [int]
1035	SEARCHTYPE_ROADMONIT_EVENT	11	8	[type] [int]
1035	SEARCHTYPE_ROADMONIT_EVENT	12	6	strfilename varchar(255)
1036	SEARCHTYPE_COMPBENCH_DATE	1	8	unit_id int 
1036	SEARCHTYPE_COMPBENCH_DATE	2	8	date int 
1036	SEARCHTYPE_COMPBENCH_DATE	3	6	test_result varchar(255)
1037	SEARCHTYPE_COMPBENCH_UNIT_INFO	1	8	id int
1037	SEARCHTYPE_COMPBENCH_UNIT_INFO	2	8	status int 
1037	SEARCHTYPE_COMPBENCH_UNIT_INFO	3	8	ltlongitude int 
1037	SEARCHTYPE_COMPBENCH_UNIT_INFO	4	8	ltlatitude int 
1037	SEARCHTYPE_COMPBENCH_UNIT_INFO	5	8	brlongitude int 
1037	SEARCHTYPE_COMPBENCH_UNIT_INFO	6	8	brlatitude int 
1037	SEARCHTYPE_COMPBENCH_UNIT_INFO	7	8	better_days int 
1037	SEARCHTYPE_COMPBENCH_UNIT_INFO	8	8	worse_days int
1038	SEARCHTYPE_SCANJAM_TOTAL	1	8	ifileid
1038	SEARCHTYPE_SCANJAM_TOTAL	2	6	orgcellname
1038	SEARCHTYPE_SCANJAM_TOTAL	3	8	ibeam
1038	SEARCHTYPE_SCANJAM_TOTAL	4	8	irxlev
1038	SEARCHTYPE_SCANJAM_TOTAL	5	8	itotal
1039	SEARCHTYPE_SCANJAM_PAIR	1	8	ifileid
1039	SEARCHTYPE_SCANJAM_PAIR	2	6	jamcellname
1039	SEARCHTYPE_SCANJAM_PAIR	3	6	orgcellname
1039	SEARCHTYPE_SCANJAM_PAIR	4	8	ijam_9
1039	SEARCHTYPE_SCANJAM_PAIR	5	8	ijam_8
1039	SEARCHTYPE_SCANJAM_PAIR	6	8	ijam_7
1039	SEARCHTYPE_SCANJAM_PAIR	7	8	ijam_6
1039	SEARCHTYPE_SCANJAM_PAIR	8	8	ijam_5
1039	SEARCHTYPE_SCANJAM_PAIR	9	8	ijam_4
1039	SEARCHTYPE_SCANJAM_PAIR	10	8	ijam_3
1039	SEARCHTYPE_SCANJAM_PAIR	11	8	ijam_2
1039	SEARCHTYPE_SCANJAM_PAIR	12	8	ijam_1
1039	SEARCHTYPE_SCANJAM_PAIR	13	8	ijam0
1039	SEARCHTYPE_SCANJAM_PAIR	14	8	ijam1
1039	SEARCHTYPE_SCANJAM_PAIR	15	8	ijam2
1039	SEARCHTYPE_SCANJAM_PAIR	16	8	ijam3
1039	SEARCHTYPE_SCANJAM_PAIR	17	8	ijam4
1039	SEARCHTYPE_SCANJAM_PAIR	18	8	ijam5
1039	SEARCHTYPE_SCANJAM_PAIR	19	8	ijam6
1039	SEARCHTYPE_SCANJAM_PAIR	20	8	ijam7
1039	SEARCHTYPE_SCANJAM_PAIR	21	8	ijam8
1039	SEARCHTYPE_SCANJAM_PAIR	22	8	ijam9
1040	SEARCHTYPE_COMPLAIN_BLOCK	1	8	id [int]
1040	SEARCHTYPE_COMPLAIN_BLOCK	2	6	addrName [varchar](255)
1040	SEARCHTYPE_COMPLAIN_BLOCK	3	8	longitude [int]
1040	SEARCHTYPE_COMPLAIN_BLOCK	4	8	latitude [int]
1040	SEARCHTYPE_COMPLAIN_BLOCK	5	8	radius [int]
1040	SEARCHTYPE_COMPLAIN_BLOCK	6	8	type int
1040	SEARCHTYPE_COMPLAIN_BLOCK	7	6	placedesc [varchar](2000)
1041	SEARCHTYPE_COMPLAIN_ITEM	1	8	block_id int
1041	SEARCHTYPE_COMPLAIN_ITEM	2	8	date int
1041	SEARCHTYPE_COMPLAIN_ITEM	3	8	longitude int
1041	SEARCHTYPE_COMPLAIN_ITEM	4	8	latitude int
1041	SEARCHTYPE_COMPLAIN_ITEM	5	6	user varchar(20)
1041	SEARCHTYPE_COMPLAIN_ITEM	6	6	detail varchar(255)
1041	SEARCHTYPE_COMPLAIN_ITEM	7	6	info
1042	SEARCHTYPE_ANTENNA_REV	1	8	ifileid int
1042	SEARCHTYPE_ANTENNA_REV	2	6	strcellname VARCHAR(255)
1042	SEARCHTYPE_ANTENNA_REV	3	8	igoodSampleNum INT
1042	SEARCHTYPE_ANTENNA_REV	4	8	ibadSampleNum INT
1042	SEARCHTYPE_ANTENNA_REV	5	8	igoodGridNum INT
1042	SEARCHTYPE_ANTENNA_REV	6	8	ibadGridNum INT
1042	SEARCHTYPE_ANTENNA_REV	7	8	istime INT
1042	SEARCHTYPE_ANTENNA_REV	8	8	ietime INT
1043	SEARCHTYPE_COMPLAIN_REPORT	1	8	[block_id]
1043	SEARCHTYPE_COMPLAIN_REPORT	2	8	[status]
1043	SEARCHTYPE_COMPLAIN_REPORT	3	8	[create_day]
1043	SEARCHTYPE_COMPLAIN_REPORT	4	8	[first_comp_day]
1043	SEARCHTYPE_COMPLAIN_REPORT	5	8	[last_comp_day]
1043	SEARCHTYPE_COMPLAIN_REPORT	6	8	[close_day]
1043	SEARCHTYPE_COMPLAIN_REPORT	7	8	[comp_days]
1043	SEARCHTYPE_COMPLAIN_REPORT	8	8	[normal_days]
1043	SEARCHTYPE_COMPLAIN_REPORT	9	8	[user_count]
1043	SEARCHTYPE_COMPLAIN_REPORT	10	8	[bill_count]
1043	SEARCHTYPE_COMPLAIN_REPORT	11	6	[areaDes]
1043	SEARCHTYPE_COMPLAIN_REPORT	12	6	[dwDes]
1043	SEARCHTYPE_COMPLAIN_REPORT	13	6	[reasonDes]
1043	SEARCHTYPE_COMPLAIN_REPORT	14	6	[solutionDes]
1044	SEARCHTYPE_CDMA_SAMPLE_SUMMARY	1	8	[ifileid] [int]
1044	SEARCHTYPE_CDMA_SAMPLE_SUMMARY	2	8	[isampleid] [int]
1044	SEARCHTYPE_CDMA_SAMPLE_SUMMARY	3	8	[itime] [int] NULL,
1044	SEARCHTYPE_CDMA_SAMPLE_SUMMARY	4	8	[wtimems] [smallint]
1044	SEARCHTYPE_CDMA_SAMPLE_SUMMARY	5	8	[bms] [tinyint]
1044	SEARCHTYPE_CDMA_SAMPLE_SUMMARY	6	8	[iLongitude] [int]
1044	SEARCHTYPE_CDMA_SAMPLE_SUMMARY	7	8	[iLatitude] [int]
1044	SEARCHTYPE_CDMA_SAMPLE_SUMMARY	8	8	[fRX_Power] [int]
1044	SEARCHTYPE_CDMA_SAMPLE_SUMMARY	9	8	[fReferenceEcIo] [int]
1044	SEARCHTYPE_CDMA_SAMPLE_SUMMARY	10	8	[iReferPN] [int]
1044	SEARCHTYPE_CDMA_SAMPLE_SUMMARY	11	8	[iFrequency] [int]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	1	8	[ifileid] [int]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	2	8	[isampleid] [int]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	3	8	[itime] [int]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	4	8	[wtimems] [smallint]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	5	8	[bms] [tinyint]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	6	8	[iLongitude] [int]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	7	8	[iLatitude] [int]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	8	8	[iAltitude] [int]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	9	8	[iSpeed] [int]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	10	8	[iMode] [int]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	11	8	[fRX_Power] [int]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	12	8	[fTX_Power] [int]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	13	8	[fTX_Adj] [int]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	14	8	[fTotalEcIo] [int]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	15	8	[fReferenceEcIo] [int]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	16	8	[fMaxEcIo] [int]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	17	8	[fTotalEc] [int]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	18	8	[fReferenceEc] [int]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	19	8	[fMaxEc] [int]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	20	8	[iReferPN] [int]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	21	8	[iMaxRSSIPN] [int]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	22	8	[fFFER] [int]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	23	8	[iActiveSet] [int]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	24	8	[iFrequency] [int]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	25	8	[Total_SiNR] [int]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	26	8	[Total_C_I] [int]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	27	8	[PER] [int]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	28	8	[DRC_Rate] [int]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	29	9	[CDMA_Markov_Info] [image]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	30	9	CDMA_Markov_Info
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	31	9	CDMA_Markov_Info
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	32	9	CDMA_Markov_Info
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	33	9	CDMA_Markov_Info
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	34	9	CDMA_Markov_Info
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	35	9	CDMA_Markov_Info
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	36	9	CDMA_Markov_Info
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	37	9	[CDMA_Finger_Info] [image]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	38	9	[CDMA_Finger_Info]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	39	9	[CDMA_Finger_Info]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	40	9	[CDMA_Finger_Info]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	41	9	[CDMA_Finger_Info]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	42	9	[CDMA_Finger_Info]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	43	9	[CDMA_Finger_Info]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	44	9	[CDMA_Finger_Info]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	45	9	[CDMA_System_Para_Info] [image]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	46	9	[CDMA_System_Para_Info]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	47	9	[CDMA_System_Para_Info]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	48	9	[CDMA_System_Para_Info]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	49	9	[CDMA_System_Para_Info]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	50	9	[CDMA_System_Para_Info]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	51	9	[CDMA_System_Para_Info]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	52	9	[CDMA_System_Para_Info]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	53	9	[Access_Params_Info] [image]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	54	9	[Access_Params_Info]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	55	9	[Access_Params_Info]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	56	9	[Access_Params_Info]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	57	9	[Access_Params_Info]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	58	9	[Access_Params_Info]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	59	9	[Access_Params_Info]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	60	9	[Access_Params_Info]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	61	9	[Power_Control_Info] [image]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	62	9	[Power_Control_Info]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	63	9	[Power_Control_Info]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	64	9	[Power_Control_Info]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	65	9	[Power_Control_Info]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	66	9	[Power_Control_Info]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	67	9	[Power_Control_Info]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	68	9	[Power_Control_Info]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	69	9	[Serving_Neighbor_Info] [image]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	70	9	[Serving_Neighbor_Info]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	71	9	[Serving_Neighbor_Info]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	72	9	[Serving_Neighbor_Info]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	73	9	[Serving_Neighbor_Info]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	74	9	[Serving_Neighbor_Info]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	75	9	[Serving_Neighbor_Info]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	76	9	[Serving_Neighbor_Info]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	77	9	[CDMA_1X_Radio] [image]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	78	9	[CDMA_1X_Radio]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	79	9	[CDMA_2X_Radio]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	80	9	[CDMA_3X_Radio]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	81	9	[CDMA_4X_Radio]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	82	9	[CDMA_5X_Radio]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	83	9	[CDMA_6X_Radio]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	84	9	[CDMA_7X_Radio]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	85	9	[CDMA_SCH] [image]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	86	9	[CDMA_SCH]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	87	9	[CDMA_SCH]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	88	9	[CDMA_SCH]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	89	9	[CDMA_SCH]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	90	9	[CDMA_SCH]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	91	9	[CDMA_SCH]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	92	9	[CDMA_SCH]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	93	9	[CDMA_1X_Throughput] [image]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	94	9	[CDMA_1X_Throughput]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	95	9	[CDMA_1X_Throughput]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	96	9	[CDMA_1X_Throughput]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	97	9	[CDMA_1X_Throughput]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	98	9	[CDMA_1X_Throughput]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	99	9	[CDMA_1X_Throughput]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	100	9	[CDMA_1X_Throughput]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	101	9	[EVDOState] [image]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	102	9	[EVDOState] 
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	103	9	[EVDOState] 
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	104	9	[EVDOState] 
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	105	9	[EVDOState] 
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	106	9	[EVDOState] 
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	107	9	[EVDOState] 
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	108	9	[EVDOState] 
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	109	9	[EVDOThroughput] [image]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	110	9	[EVDOThroughput]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	111	9	[EVDOThroughput]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	112	9	[EVDOThroughput]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	113	9	[EVDOThroughput]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	114	9	[EVDOThroughput]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	115	9	[EVDOThroughput]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	116	9	[EVDOThroughput]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	117	9	[EVDOActiveSet] [image]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	118	9	[EVDOActiveSet]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	119	9	[EVDOActiveSet]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	120	9	[EVDOActiveSet]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	121	9	[EVDOActiveSet]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	122	9	[EVDOActiveSet]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	123	9	[EVDOActiveSet]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	124	9	[EVDOActiveSet]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	125	9	[EVDOCandidateSet] [image]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	126	9	[EVDOCandidateSet]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	127	9	[EVDOCandidateSet]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	128	9	[EVDOCandidateSet]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	129	9	[EVDOCandidateSet]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	130	9	[EVDOCandidateSet]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	131	9	[EVDOCandidateSet]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	132	9	[EVDOCandidateSet]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	133	9	[EVDONeighborSet] [image]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	134	9	[EVDONeighborSet]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	135	9	[EVDONeighborSet]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	136	9	[EVDONeighborSet]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	137	9	[EVDONeighborSet]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	138	9	[EVDONeighborSet]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	139	9	[EVDONeighborSet]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	140	9	[EVDONeighborSet]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	141	9	[EVDOPacketInfo] [image]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	142	9	[EVDOPacketInfo]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	143	9	[EVDOPacketInfo]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	144	9	[EVDOPacketInfo]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	145	9	[EVDOPacketInfo]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	146	9	[EVDOPacketInfo]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	147	9	[EVDOPacketInfo]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	148	9	[EVDOPacketInfo]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	149	9	[EVDOFingerInfo] [image]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	150	9	[EVDOFingerInfo]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	151	9	[EVDOFingerInfo]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	152	9	[EVDOFingerInfo]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	153	9	[EVDOFingerInfo]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	154	9	[EVDOFingerInfo]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	155	9	[EVDOFingerInfo]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	156	9	[EVDOFingerInfo]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	157	9	[APP_Info] [image]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	158	9	[APP_Info]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	159	9	[APP_Info]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	160	9	[APP_Info]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	161	9	[APP_Info]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	162	9	[APP_Info]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	163	9	[APP_Info]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	164	9	[APP_Info]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	165	9	[PESQ_Info] [image]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	166	9	[PESQ_Info]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	167	9	[PESQ_Info]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	168	9	[PESQ_Info]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	169	9	[PESQ_Info]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	170	9	[PESQ_Info]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	171	9	[PESQ_Info]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	172	9	[PESQ_Info]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	173	9	[Reserved_Info] [image] 
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	174	9	[Reserved_Info]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	175	9	[Reserved_Info]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	176	9	[Reserved_Info]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	177	9	[Reserved_Info]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	178	9	[Reserved_Info]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	179	9	[Reserved_Info]
1045	SEARCHTYPE_CDMA_SAMPLE_NORMAL	180	9	[Reserved_Info]
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	1	8	ifileid
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	2	8	tllongitudeintnull
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	3	8	tllatitudeintnull
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	4	8	brlongitudeintnull
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	5	8	brlatitudeintnull
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	6	8	[ifiletime] [int] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	7	8	[iduration] [int] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	8	8	[idistance] [int] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	9	8	[iTotalSampleNum] [int] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	10	8	[iECIO_f15] [int] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	11	5	[fECIO_f15_Total] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	12	5	[fECIO_f15_Max] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	13	5	[fECIO_f15_Min] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	14	8	[iECIO_f15_f12] [int] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	15	5	[fECIO_f15_f12_Total] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	16	5	[fiECIO_f15_f12_Max] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	17	5	[fiECIO_f15_f12_Min] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	18	8	[iECIO_f12_f9] [int] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	19	5	[fECIO_f12_f9_Total] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	20	5	[fECIO_f12_f9_Max] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	21	5	[fECIO_f12_f9_Min] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	22	8	[iECIO_f9_f6] [int] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	23	5	[fECIO_f9_f6_Total] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	24	5	[fECIO_f9_f6_Max] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	25	5	[fECIO_f9_f6_Min] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	26	8	[iECIO_f6] [int] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	27	5	[fECIO_f6_Total] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	28	5	[fECIO_f6_Max] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	29	5	[fECIO_f6_Min] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	30	8	[iFFER_0_1] [int] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	31	5	[fFFER_0_1_Total] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	32	5	[fFFER_0_1_Max] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	33	5	[fFFER_0_1_Min] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	34	8	[iFFER_1_2] [int] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	35	5	[fFFER_1_2_Total] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	36	5	[fFFER_1_2_Max] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	37	5	[fFFER_1_2_Min] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	38	8	[iFFER_2_3] [int] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	39	5	[fFFER_2_3_Total] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	40	5	[fFFER_2_3_Max] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	41	5	[fFFER_2_3_Min] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	42	8	[iFFER_3_4] [int] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	43	5	[fFFER_3_4_Total] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	44	5	[fFFER_3_4_Max] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	45	5	[fFFER_3_4_Min] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	46	8	[iFFER_4_5] [int] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	47	5	[fFFER_4_5_Total] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	48	5	[fFFER_4_5_Max] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	49	5	[fFFER_4_5_Min] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	50	8	[iFFER_5_6] [int] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	51	5	[fFFER_5_6_Total] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	52	5	[fFFER_5_6_Max] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	53	5	[fFFER_5_6_Min] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	54	8	[iFFER_6_7] [int] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	55	5	[fFFER_6_7_Total] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	56	5	[fFFER_6_7_Max] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	57	5	[fFFER_6_7_Min] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	58	8	[iFFER_7_8] [int] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	59	5	[fFFER_7_8_Total] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	60	5	[fFFER_7_8_Max] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	61	5	[fFFER_7_8_Min] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	62	8	[iFFER_8_9] [int] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	63	5	[fFFER_8_9_Total] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	64	5	[fFFER_8_9_Max] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	65	5	[fFFER_8_9_Min] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	66	8	[iFFER_9_10] [int] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	67	5	[fFFER_9_10_Total] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	68	5	[fFFER_9_10_Max] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	69	5	[fFFER_9_10_Min] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	70	8	[iFFER_10_100] [int] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	71	5	[fFFER_10_100_Total] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	72	5	[fFFER_10_100_Max] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	73	5	[fFFER_10_100_Min] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	74	8	[iTXPower_f20] [int] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	75	5	[fTXPower_f20_Total] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	76	5	[fTXPower_f20_Max] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	77	5	[fTXPower_f20_Min] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	78	8	[iTXPower_f20_f15] [int] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	79	5	[fTXPower_f20_f15_Total] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	80	5	[fTXPower_f20_f15_Max] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	81	5	[fTXPower_f20_f15_Min] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	82	8	[iTXPower_f15_f10] [int] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	83	5	[fTXPower_f15_f10_Total] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	84	5	[fTXPower_f15_f10_Max] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	85	5	[fTXPower_f15_f10_Min] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	86	8	[iTXPower_f10_f5] [int] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	87	5	[fTXPower_f10_f5_Total] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	88	5	[fTXPower_f10_f5_Max] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	89	5	[fTXPower_f10_f5_Min] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	90	8	[iTXPower_f5_0] [int] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	91	5	[fTXPower_f5_0_Total] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	92	5	[fTXPower_f5_0_Max] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	93	5	[fTXPower_f5_0_Min] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	94	8	[iTXPower_0_5] [int] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	95	5	[fTXPower_0_5_Total] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	96	5	[fTXPower_0_5_Max] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	97	5	[fTXPower_0_5_Min] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	98	8	[iTXPower_5_10] [int] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	99	5	[fTXPower_5_10_Total] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	100	5	[fTXPower_5_10_Max] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	101	5	[fTXPower_5_10_Min] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	102	8	[iTXPower_10_15] [int] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	103	5	[fTXPower_10_15_Total] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	104	5	[fTXPower_10_15_Max] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	105	5	[fTXPower_10_15_Min] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	106	8	[iTXPower_15_20] [int] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	107	5	[fTXPower_15_20_Total] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	108	5	[fTXPower_15_20_Max] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	109	5	[fTXPower_15_20_Min] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	110	8	[iTXPower_20] [int] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	111	5	[fTXPower_20_Total] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	112	5	[fTXPower_20_Max] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	113	5	[fTXPower_20_Min] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	114	8	[iRXPower_f94] [int] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	115	5	[fRXPower_f94_Total] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	116	5	[fRXPower_f94_Max] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	117	5	[fRXPower_f94_Min] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	118	8	[iRXPower_f94_f90] [int] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	119	5	[fRXPower_f94_f90_Total] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	120	5	[fRXPower_f94_f90_Max] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	121	5	[fRXPower_f94_f90_Min] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	122	8	[iRXPower_f90_f85] [int] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	123	5	[fRXPower_f90_f85_Total] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	124	5	[fRXPower_f90_f85_Max] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	125	5	[fRXPower_f90_f85_Min] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	126	8	[iRXPower_f85_f80] [int] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	127	5	[fRXPower_f85_f80_Total] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	128	5	[fRXPower_f85_f80_Max] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	129	5	[fRXPower_f85_f80_Min] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	130	8	[iRXPower_f80_f75] [int] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	131	5	[fRXPower_f80_f75_Total] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	132	5	[fRXPower_f80_f75_Max] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	133	5	[fRXPower_f80_f75_Min] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	134	8	[iRXPower_f75_f65] [int] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	135	5	[fRXPower_f75_f65_Total] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	136	5	[fRXPower_f75_f65_Max] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	137	5	[fRXPower_f75_f65_Min] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	138	8	[iRXPower_f65] [int] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	139	5	[fRXPower_f65_Total] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	140	5	[fRXPower_f65_Max] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	141	5	[fRXPower_f65_Min] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	142	8	[iCoverNum12_15_f90] [int] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	143	8	[iCoverNum12_15_f95] [int] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	144	8	[iFTP_Download_samplenum] [int] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	145	8	[iFTP_Download_total] [int] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	146	8	[iPesqLQNum] [int] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	147	5	[fPesqLQ_Total] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	148	5	[fPesqLQ_Max] [float] NULL,
1046	SEARCHTYPE_CDMA_AREA_COVER_GRID	149	5	[fPesqLQ_Min] [float] NULL
1047	SEARCHTYPE_CLUSTER_CLIQUE_INFO	1	8	[itesttype]
1047	SEARCHTYPE_CLUSTER_CLIQUE_INFO	2	8	[ibatch]
1047	SEARCHTYPE_CLUSTER_CLIQUE_INFO	3	8	[iclique_id]
1047	SEARCHTYPE_CLUSTER_CLIQUE_INFO	4	6	[strclique_cell]
1047	SEARCHTYPE_CLUSTER_CLIQUE_INFO	5	8	[iAdjFreqFlag]
1047	SEARCHTYPE_CLUSTER_CLIQUE_INFO	6	6	strarea varchar
1048	SEARCHTYPE_COMPLAIN_BLOCK_GRID	1	8	[grid_id]
1048	SEARCHTYPE_COMPLAIN_BLOCK_GRID	2	8	[block_id]
1049	SEARCHTYPE_COMPLAIN_BLOCK_GRID_LOG	1	8	grid_id int 
1049	SEARCHTYPE_COMPLAIN_BLOCK_GRID_LOG	2	8	ltlongitude int 
1049	SEARCHTYPE_COMPLAIN_BLOCK_GRID_LOG	3	8	ltlatitude int 
1049	SEARCHTYPE_COMPLAIN_BLOCK_GRID_LOG	4	8	brlongitude int 
1049	SEARCHTYPE_COMPLAIN_BLOCK_GRID_LOG	5	8	brlatitude int 
1049	SEARCHTYPE_COMPLAIN_BLOCK_GRID_LOG	6	8	status int 
1049	SEARCHTYPE_COMPLAIN_BLOCK_GRID_LOG	7	8	user_cnt int 
1049	SEARCHTYPE_COMPLAIN_BLOCK_GRID_LOG	8	8	comp_cnt int 
1049	SEARCHTYPE_COMPLAIN_BLOCK_GRID_LOG	9	8	create_day int 
1049	SEARCHTYPE_COMPLAIN_BLOCK_GRID_LOG	10	8	first_day int 
1049	SEARCHTYPE_COMPLAIN_BLOCK_GRID_LOG	11	8	last_day int 
1049	SEARCHTYPE_COMPLAIN_BLOCK_GRID_LOG	12	8	close_day int 
1049	SEARCHTYPE_COMPLAIN_BLOCK_GRID_LOG	13	8	comp_days int 
1049	SEARCHTYPE_COMPLAIN_BLOCK_GRID_LOG	14	8	normal_days int
1049	SEARCHTYPE_COMPLAIN_BLOCK_GRID_LOG	15	6	injArea
1049	SEARCHTYPE_COMPLAIN_BLOCK_GRID_LOG	16	6	dwArea
1049	SEARCHTYPE_COMPLAIN_BLOCK_GRID_LOG	17	6	[name]
1049	SEARCHTYPE_COMPLAIN_BLOCK_GRID_LOG	18	6	reason_des
1049	SEARCHTYPE_COMPLAIN_BLOCK_GRID_LOG	19	6	solution_des
1050	SEARCHTYPE_CLUSTER_LOG	1	8	[itesttype]
1050	SEARCHTYPE_CLUSTER_LOG	2	8	[iyear]
1050	SEARCHTYPE_CLUSTER_LOG	3	8	[ibatch]
1050	SEARCHTYPE_CLUSTER_LOG	4	8	[istime]
1050	SEARCHTYPE_CLUSTER_LOG	5	8	[ietime]
1050	SEARCHTYPE_CLUSTER_LOG	6	6	[strcomment]
1051	SEARCHTYPE_CLUSTER_INFO	1	8	itesttype
1051	SEARCHTYPE_CLUSTER_INFO	2	8	ibatch
1051	SEARCHTYPE_CLUSTER_INFO	3	8	icluster_id
1051	SEARCHTYPE_CLUSTER_INFO	4	6	strcluster_cell
1051	SEARCHTYPE_CLUSTER_INFO	5	6	strarea varchar
1052	SEARCHTYPE_CLUSTER_ADJFREQ_INFO	1	8	itesttype
1052	SEARCHTYPE_CLUSTER_ADJFREQ_INFO	2	8	ibatch
1052	SEARCHTYPE_CLUSTER_ADJFREQ_INFO	3	6	strorgcellname
1052	SEARCHTYPE_CLUSTER_ADJFREQ_INFO	4	6	strjamcellname
1053	SEARCHTYPE_TDSCDMA_NETRATE	1	8	itllongitude
1053	SEARCHTYPE_TDSCDMA_NETRATE	2	8	itllatitude
1053	SEARCHTYPE_TDSCDMA_NETRATE	3	8	ibrlongitude
1053	SEARCHTYPE_TDSCDMA_NETRATE	4	8	ibrlatitude
1053	SEARCHTYPE_TDSCDMA_NETRATE	5	8	iduration_TD
1053	SEARCHTYPE_TDSCDMA_NETRATE	6	8	iduration_GSM
1053	SEARCHTYPE_TDSCDMA_NETRATE	7	8	idistance_TD
1053	SEARCHTYPE_TDSCDMA_NETRATE	8	8	idistance_GSM
1054	SEARCHTYPE_CLUSTER_CSL	1	8	dday int
1055	SEARCHTYPE_CLUSTER_CSL_INFO	1	8	itime int
1055	SEARCHTYPE_CLUSTER_CSL_INFO	2	8	icellnum int
1055	SEARCHTYPE_CLUSTER_CSL_INFO	3	8	ifreqnum int
1055	SEARCHTYPE_CLUSTER_CSL_INFO	4	6	strcelllist varchar
1055	SEARCHTYPE_CLUSTER_CSL_INFO	5	6	strarea varchar
1055	SEARCHTYPE_CLUSTER_CSL_INFO	6	6	stragent varchar
1055	SEARCHTYPE_CLUSTER_CSL_INFO	7	5	fclusterqual float
1056	SEARCHTYPE_DOCUMENT_INFO	1	8	itesttype int 
1056	SEARCHTYPE_DOCUMENT_INFO	2	6	strfilename varchar](255) 
1056	SEARCHTYPE_DOCUMENT_INFO	3	6	strsavepath varchar](255) 
1056	SEARCHTYPE_DOCUMENT_INFO	4	8	iimporttime int 
1056	SEARCHTYPE_DOCUMENT_INFO	5	8	iprojecttype int 
1056	SEARCHTYPE_DOCUMENT_INFO	6	8	iyear int 
1056	SEARCHTYPE_DOCUMENT_INFO	7	8	ibatch int 
1056	SEARCHTYPE_DOCUMENT_INFO	8	8	iareatype int 
1056	SEARCHTYPE_DOCUMENT_INFO	9	8	iareaid int 
1056	SEARCHTYPE_DOCUMENT_INFO	10	8	idevicetype int 
1056	SEARCHTYPE_DOCUMENT_INFO	11	8	ifiletype int 
1056	SEARCHTYPE_DOCUMENT_INFO	12	8	iservicetype int 
1056	SEARCHTYPE_DOCUMENT_INFO	13	8	icarriertype int 
1056	SEARCHTYPE_DOCUMENT_INFO	14	8	iagentid int 
1056	SEARCHTYPE_DOCUMENT_INFO	15	8	istaffid int 
1056	SEARCHTYPE_DOCUMENT_INFO	16	8	strdesc varchar](255)
1056	SEARCHTYPE_DOCUMENT_INFO	17	8	idbvalue int 
1056	SEARCHTYPE_DOCUMENT_INFO	18	8	isubtype1 int 
1056	SEARCHTYPE_DOCUMENT_INFO	19	8	isubtype2 int 
1056	SEARCHTYPE_DOCUMENT_INFO	20	8	statstatus tinyint
1057	SEARCHTYPE_COMPLAIN_BLOCK_GRID_RECENT	1	8	date INT
1057	SEARCHTYPE_COMPLAIN_BLOCK_GRID_RECENT	2	8	cnt_hot INT
1057	SEARCHTYPE_COMPLAIN_BLOCK_GRID_RECENT	3	8	cnt_total INT
1058	SEARCHTYPE_CELL_PROPERTY_INFO	1	8	iid int
1058	SEARCHTYPE_CELL_PROPERTY_INFO	2	6	strbsc varchar(20)
1058	SEARCHTYPE_CELL_PROPERTY_INFO	3	6	strcellcode varchar(20)
1058	SEARCHTYPE_CELL_PROPERTY_INFO	4	8	itime int
1058	SEARCHTYPE_CELL_PROPERTY_INFO	5	8	id231 int
1058	SEARCHTYPE_CELL_PROPERTY_INFO	6	8	id232 int
1058	SEARCHTYPE_CELL_PROPERTY_INFO	7	8	id233 int
1058	SEARCHTYPE_CELL_PROPERTY_INFO	8	8	id234 int
1058	SEARCHTYPE_CELL_PROPERTY_INFO	9	8	id235 int
1058	SEARCHTYPE_CELL_PROPERTY_INFO	10	8	id236 int
1058	SEARCHTYPE_CELL_PROPERTY_INFO	11	5	fd237 float
1058	SEARCHTYPE_CELL_PROPERTY_INFO	12	8	id238 int
1058	SEARCHTYPE_CELL_PROPERTY_INFO	13	8	id239 int
1058	SEARCHTYPE_CELL_PROPERTY_INFO	14	8	id240 int
1058	SEARCHTYPE_CELL_PROPERTY_INFO	15	8	id241 int
1058	SEARCHTYPE_CELL_PROPERTY_INFO	16	8	id242 int
1058	SEARCHTYPE_CELL_PROPERTY_INFO	17	8	id243 int
1058	SEARCHTYPE_CELL_PROPERTY_INFO	18	8	id244 int
1058	SEARCHTYPE_CELL_PROPERTY_INFO	19	8	id245 int
1058	SEARCHTYPE_CELL_PROPERTY_INFO	20	8	id246 int
1058	SEARCHTYPE_CELL_PROPERTY_INFO	21	8	id247 int
1058	SEARCHTYPE_CELL_PROPERTY_INFO	22	8	id248 int
1058	SEARCHTYPE_CELL_PROPERTY_INFO	23	8	id249 int
1058	SEARCHTYPE_CELL_PROPERTY_INFO	24	5	fd250 float
1058	SEARCHTYPE_CELL_PROPERTY_INFO	25	8	id251 int
1058	SEARCHTYPE_CELL_PROPERTY_INFO	26	8	id252 int
1058	SEARCHTYPE_CELL_PROPERTY_INFO	27	5	fd253 float
1058	SEARCHTYPE_CELL_PROPERTY_INFO	28	8	id254 int
1058	SEARCHTYPE_CELL_PROPERTY_INFO	29	5	fd255 float
1059	SEARCHTYPE_CLUSTER_AREA_FREQNUMAVG	1	8	iyear [int]
1059	SEARCHTYPE_CLUSTER_AREA_FREQNUMAVG	2	8	[ibatch] [int]
1059	SEARCHTYPE_CLUSTER_AREA_FREQNUMAVG	3	6	[areaname] [varchar]
1059	SEARCHTYPE_CLUSTER_AREA_FREQNUMAVG	4	5	[freqNumAvg] [float]
1059	SEARCHTYPE_CLUSTER_AREA_FREQNUMAVG	5	8	bandtype int
1060	SEARCHTYPE_CLUSTER_AREA_FREQVARIANCE	1	8	iyear [int]
1060	SEARCHTYPE_CLUSTER_AREA_FREQVARIANCE	2	8	[ibatch] [int]
1060	SEARCHTYPE_CLUSTER_AREA_FREQVARIANCE	3	6	[areaname] [varchar]
1060	SEARCHTYPE_CLUSTER_AREA_FREQVARIANCE	4	5	[freqVariance] [float]
1060	SEARCHTYPE_CLUSTER_AREA_FREQVARIANCE	5	8	bandtype int
1061	SEARCHTYPE_CLUSTER_AREA_WEAKCOVCELLNUM	1	8	iyear [int]
1061	SEARCHTYPE_CLUSTER_AREA_WEAKCOVCELLNUM	2	8	[ibatch] [int]
1061	SEARCHTYPE_CLUSTER_AREA_WEAKCOVCELLNUM	3	6	[areaname] [varchar]
1061	SEARCHTYPE_CLUSTER_AREA_WEAKCOVCELLNUM	4	8	[weakCoverCellNum] [int]
1061	SEARCHTYPE_CLUSTER_AREA_WEAKCOVCELLNUM	5	8	bandtype int
1062	SEARCHTYPE_CLUSTER_AREA_CARRIERUSEAVG	1	8	iyear [int]
1062	SEARCHTYPE_CLUSTER_AREA_CARRIERUSEAVG	2	8	[ibatch] [int]
1062	SEARCHTYPE_CLUSTER_AREA_CARRIERUSEAVG	3	6	[areaname] [varchar]
1062	SEARCHTYPE_CLUSTER_AREA_CARRIERUSEAVG	4	5	[carrierUseAvg] [float]
1062	SEARCHTYPE_CLUSTER_AREA_CARRIERUSEAVG	5	8	bandtype int
1063	SEARCHTYPE_CLUSTER_Cell_WeakCov	1	8	[ibatch] [int]
1063	SEARCHTYPE_CLUSTER_Cell_WeakCov	2	8	[itesttype] [int]
1063	SEARCHTYPE_CLUSTER_Cell_WeakCov	3	6	[strjamcell] [varchar]
1063	SEARCHTYPE_CLUSTER_Cell_WeakCov	4	6	[strorgcells] [varchar]
1063	SEARCHTYPE_CLUSTER_Cell_WeakCov	5	6	strarea varchar
1064	SEARCHTYPE_CLUSTER_Cell_WirelessRate	1	8	[itesttype] [int]
1064	SEARCHTYPE_CLUSTER_Cell_WirelessRate	2	8	[ibatch] [int]
1064	SEARCHTYPE_CLUSTER_Cell_WirelessRate	3	6	[strcellname] [varchar](20)
1064	SEARCHTYPE_CLUSTER_Cell_WirelessRate	4	5	[wirelessRate] [float]
1065	SEARCHTYPE_CLUSTER_AREA_INJECTRATE	1	8	[iyear] [int]
1065	SEARCHTYPE_CLUSTER_AREA_INJECTRATE	2	8	[ibatch] [int]
1065	SEARCHTYPE_CLUSTER_AREA_INJECTRATE	3	6	[areaname] [varchar](20)
1065	SEARCHTYPE_CLUSTER_AREA_INJECTRATE	4	5	[injectRate] [float]
1066	SEARCHTYPE_CLUSTER_CELL_AVAILRATE	1	8	[iyear] [int]
1066	SEARCHTYPE_CLUSTER_CELL_AVAILRATE	2	8	[ibatch] [int]
1066	SEARCHTYPE_CLUSTER_CELL_AVAILRATE	3	5	AvailCellRate flaot
1067	SEARCHTYPE_CLUSTER_COFREQ_INFO	1	8	itesttype
1067	SEARCHTYPE_CLUSTER_COFREQ_INFO	2	8	ibatch
1067	SEARCHTYPE_CLUSTER_COFREQ_INFO	3	6	strorgcellname
1067	SEARCHTYPE_CLUSTER_COFREQ_INFO	4	6	strjamcellname
1068	SEARCHTYPE_CLUSTER_CELL_GRID	1	8	itesttype int 
1068	SEARCHTYPE_CLUSTER_CELL_GRID	2	8	ibatch int 
1068	SEARCHTYPE_CLUSTER_CELL_GRID	3	6	strcellname
1068	SEARCHTYPE_CLUSTER_CELL_GRID	4	8	itllongitude int 
1068	SEARCHTYPE_CLUSTER_CELL_GRID	5	8	itllatitude int 
1068	SEARCHTYPE_CLUSTER_CELL_GRID	6	8	ibrlongitude int 
1068	SEARCHTYPE_CLUSTER_CELL_GRID	7	8	ibrlatitude int
1068	SEARCHTYPE_CLUSTER_CELL_GRID	8	8	igridtype int 
1069	SEARCHTYPE_ES_AREA_EVENT_INFO	1	8	ifileid int
1069	SEARCHTYPE_ES_AREA_EVENT_INFO	2	8	iprojecttype int
1069	SEARCHTYPE_ES_AREA_EVENT_INFO	3	8	iseqid int
1069	SEARCHTYPE_ES_AREA_EVENT_INFO	4	8	itime int
1069	SEARCHTYPE_ES_AREA_EVENT_INFO	5	2	wtimems smallint
1069	SEARCHTYPE_ES_AREA_EVENT_INFO	6	1	bms tinyint
1069	SEARCHTYPE_ES_AREA_EVENT_INFO	7	8	iEventID int
1069	SEARCHTYPE_ES_AREA_EVENT_INFO	8	8	ilongitude int
1069	SEARCHTYPE_ES_AREA_EVENT_INFO	9	8	ilatitude int
1069	SEARCHTYPE_ES_AREA_EVENT_INFO	10	8	icqtposid int
1069	SEARCHTYPE_ES_AREA_EVENT_INFO	11	8	iLAC int
1069	SEARCHTYPE_ES_AREA_EVENT_INFO	12	2	wRAC smallint
1069	SEARCHTYPE_ES_AREA_EVENT_INFO	13	8	iCI int
1069	SEARCHTYPE_ES_AREA_EVENT_INFO	14	8	iTargetLAC int
1069	SEARCHTYPE_ES_AREA_EVENT_INFO	15	2	wTargetRAC smallint
1069	SEARCHTYPE_ES_AREA_EVENT_INFO	16	8	iTargetCI int
1069	SEARCHTYPE_ES_AREA_EVENT_INFO	17	14	ivalue1 int
1069	SEARCHTYPE_ES_AREA_EVENT_INFO	18	14	ivalue2 int
1069	SEARCHTYPE_ES_AREA_EVENT_INFO	19	14	ivalue3 int
1069	SEARCHTYPE_ES_AREA_EVENT_INFO	20	14	ivalue4 int
1069	SEARCHTYPE_ES_AREA_EVENT_INFO	21	14	ivalue5 int
1069	SEARCHTYPE_ES_AREA_EVENT_INFO	22	14	ivalue6 int
1069	SEARCHTYPE_ES_AREA_EVENT_INFO	23	14	ivalue7 int
1069	SEARCHTYPE_ES_AREA_EVENT_INFO	24	14	ivalue8 int
1069	SEARCHTYPE_ES_AREA_EVENT_INFO	25	14	ivalue9 int
1069	SEARCHTYPE_ES_AREA_EVENT_INFO	26	14	ivalue10 int
1069	SEARCHTYPE_ES_AREA_EVENT_INFO	27	8	itaskid int
1069	SEARCHTYPE_ES_AREA_EVENT_INFO	28	8	ianatime int
1069	SEARCHTYPE_ES_AREA_EVENT_INFO	29	8	status int
1069	SEARCHTYPE_ES_AREA_EVENT_INFO	30	6	pretype_desc varchar
1069	SEARCHTYPE_ES_AREA_EVENT_INFO	31	6	pretype_desc varchar
1069	SEARCHTYPE_ES_AREA_EVENT_INFO	32	6	pretype_desc varchar
1069	SEARCHTYPE_ES_AREA_EVENT_INFO	33	6	pretype_desc varchar
1069	SEARCHTYPE_ES_AREA_EVENT_INFO	34	6	pretype_desc varchar
1069	SEARCHTYPE_ES_AREA_EVENT_INFO	35	6	pretype_desc varchar
1069	SEARCHTYPE_ES_AREA_EVENT_INFO	36	6	pretype_desc varchar
1069	SEARCHTYPE_ES_AREA_EVENT_INFO	37	6	pretype_desc varchar
1069	SEARCHTYPE_ES_AREA_EVENT_INFO	38	6	reason_desc varchar
1069	SEARCHTYPE_ES_AREA_EVENT_INFO	39	6	reason_desc varchar
1069	SEARCHTYPE_ES_AREA_EVENT_INFO	40	6	reason_desc varchar
1069	SEARCHTYPE_ES_AREA_EVENT_INFO	41	6	reason_desc varchar
1069	SEARCHTYPE_ES_AREA_EVENT_INFO	42	6	reason_desc varchar
1069	SEARCHTYPE_ES_AREA_EVENT_INFO	43	6	reason_desc varchar
1069	SEARCHTYPE_ES_AREA_EVENT_INFO	44	6	reason_desc varchar
1069	SEARCHTYPE_ES_AREA_EVENT_INFO	45	6	reason_desc varchar
1069	SEARCHTYPE_ES_AREA_EVENT_INFO	46	6	solution_desc varchar
1069	SEARCHTYPE_ES_AREA_EVENT_INFO	47	6	solution_desc varchar
1069	SEARCHTYPE_ES_AREA_EVENT_INFO	48	6	solution_desc varchar
1069	SEARCHTYPE_ES_AREA_EVENT_INFO	49	6	solution_desc varchar
1069	SEARCHTYPE_ES_AREA_EVENT_INFO	50	6	solution_desc varchar
1069	SEARCHTYPE_ES_AREA_EVENT_INFO	51	6	solution_desc varchar
1069	SEARCHTYPE_ES_AREA_EVENT_INFO	52	6	solution_desc varchar
1069	SEARCHTYPE_ES_AREA_EVENT_INFO	53	6	solution_desc varchar
1069	SEARCHTYPE_ES_AREA_EVENT_INFO	54	6	strfilename varchar](255)
1069	SEARCHTYPE_ES_AREA_EVENT_INFO	55	8	iservicetype int
1069	SEARCHTYPE_ES_AREA_EVENT_INFO	56	6	strsampletbname varchar(255)
1070	SEARCHTYPE_OVERCOVER_CELL_GRID	1	8	itesttype int 
1070	SEARCHTYPE_OVERCOVER_CELL_GRID	2	8	ibatch int 
1070	SEARCHTYPE_OVERCOVER_CELL_GRID	3	6	strcellname
1070	SEARCHTYPE_OVERCOVER_CELL_GRID	4	8	itllongitude int 
1070	SEARCHTYPE_OVERCOVER_CELL_GRID	5	8	itllatitude int 
1070	SEARCHTYPE_OVERCOVER_CELL_GRID	6	8	ibrlongitude int 
1070	SEARCHTYPE_OVERCOVER_CELL_GRID	7	8	ibrlatitude int
1070	SEARCHTYPE_OVERCOVER_CELL_GRID	8	8	igridtype int 
1070	SEARCHTYPE_OVERCOVER_CELL_GRID	9	6	strcellname VARCHAR(255)
1071	SEARCHTYPE_LOGSEARCH_INFO	1	8	itesttype int
1071	SEARCHTYPE_LOGSEARCH_INFO	2	8	idevicetype int
1071	SEARCHTYPE_LOGSEARCH_INFO	3	8	ifiletype int
1071	SEARCHTYPE_LOGSEARCH_INFO	4	8	iservicetype int
1071	SEARCHTYPE_LOGSEARCH_INFO	5	8	icarriertype int
1071	SEARCHTYPE_LOGSEARCH_INFO	6	8	iprojecttype int
1071	SEARCHTYPE_LOGSEARCH_INFO	7	8	ibatch int
1071	SEARCHTYPE_LOGSEARCH_INFO	8	8	ifileid int
1071	SEARCHTYPE_LOGSEARCH_INFO	9	6	strfilename varchar(255)
1071	SEARCHTYPE_LOGSEARCH_INFO	10	6	logtbname varchar(255)
1072	SEARCHTYPE_WCDMA_SAMPLE_SUMMARY	1	8	ifileid int
1072	SEARCHTYPE_WCDMA_SAMPLE_SUMMARY	2	8	isampleid
1072	SEARCHTYPE_WCDMA_SAMPLE_SUMMARY	3	8	itime
1072	SEARCHTYPE_WCDMA_SAMPLE_SUMMARY	4	8	wtimems
1072	SEARCHTYPE_WCDMA_SAMPLE_SUMMARY	5	8	MsSeq int
1072	SEARCHTYPE_WCDMA_SAMPLE_SUMMARY	6	8	Longitude int
1072	SEARCHTYPE_WCDMA_SAMPLE_SUMMARY	7	8	Latitude int
1072	SEARCHTYPE_WCDMA_SAMPLE_SUMMARY	8	8	RxPower int
1072	SEARCHTYPE_WCDMA_SAMPLE_SUMMARY	9	8	TxPower int
1072	SEARCHTYPE_WCDMA_SAMPLE_SUMMARY	10	8	BLER int
1072	SEARCHTYPE_WCDMA_SAMPLE_SUMMARY	11	8	TotalRSCP int
1072	SEARCHTYPE_WCDMA_SAMPLE_SUMMARY	12	8	TotalEc_Io int
1072	SEARCHTYPE_WCDMA_SAMPLE_SUMMARY	13	8	MaxEc_Io_PSC int
1072	SEARCHTYPE_WCDMA_SAMPLE_SUMMARY	14	8	frequency int
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	1	8	ifileid int
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	2	8	isampleid
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	3	8	itime
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	4	8	wtimems
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	5	8	MsType int 
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	6	8	MsSeq int
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	7	8	Longitude int
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	8	8	Latitude int
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	9	8	Altitude int
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	10	8	Speed int 
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	11	8	Mode int
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	12	8	RxPower int
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	13	8	TxPower int
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	14	8	BLER int
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	15	8	TotalEc_Io int
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	16	8	MaxEc_Io int 
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	17	8	Refernce_Ec_Io int
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	18	8	TotalRSCP int
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	19	8	MaxRSCP int
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	20	8	Reference_RSCP int
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	21	8	MaxEc_Io_PSC int
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	22	8	Reference_PSC int
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	23	8	SIR int
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	24	8	Target_SIR int
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	25	8	frequency int
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	26	8	PESQScore int
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	27	8	PESQLQ int
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	28	8	PESQMos int
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	29	8	SysCellID int
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	30	8	SysURA_ID int
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	31	8	SysLAI int
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	32	8	SysRAI int
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	33	8	SNeiFreq0
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	34	8	SNeiFreq1
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	35	8	SNeiFreq2
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	36	8	SNeiFreq3
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	37	8	SNeiFreq4
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	38	8	SNeiFreq5
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	39	8	SNeiPSC0
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	40	8	SNeiPSC1
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	41	8	SNeiPSC2
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	42	8	SNeiPSC3
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	43	8	SNeiPSC4
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	44	8	SNeiPSC5
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	45	8	SNeiEcIo0
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	46	8	SNeiEcIo1
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	47	8	SNeiEcIo2
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	48	8	SNeiEcIo3
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	49	8	SNeiEcIo4
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	50	8	SNeiEcIo5
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	51	8	SNeiRSCP0
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	52	8	SNeiRSCP1
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	53	8	SNeiRSCP2
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	54	8	SNeiRSCP3
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	55	8	SNeiRSCP4
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	56	8	SNeiRSCP5
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	57	8	NGsmarfcn0
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	58	8	NGsmarfcn1
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	59	8	NGsmarfcn2
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	60	8	NGsmarfcn3
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	61	8	NGsmarfcn4
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	62	8	NGsmarfcn5
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	63	8	NGsmbsic0
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	64	8	NGsmbsic1
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	65	8	NGsmbsic2
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	66	8	NGsmbsic3
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	67	8	NGsmbsic4
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	68	8	NGsmbsic5
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	69	8	NGsmRXLev0
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	70	8	NGsmRXLev1
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	71	8	NGsmRXLev2
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	72	8	NGsmRXLev3
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	73	8	NGsmRXLev4
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	74	8	NGsmRXLev5
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	75	8	NGsmC10
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	76	8	NGsmC11
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	77	8	NGsmC12
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	78	8	NGsmC13
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	79	8	NGsmC14
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	80	8	NGsmC15
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	81	8	NGsmC20
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	82	8	NGsmC21
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	83	8	NGsmC22
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	84	8	NGsmC23
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	85	8	NGsmC24
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	86	8	NGsmC25
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	87	8	Sgsmbcch_arfcn int
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	88	8	Sgsmci int
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	89	8	Sgsmc1 int
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	90	8	Sgsmc2 int
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	91	8	Sgsmbcchrxlev int
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	92	8	Sgsmbsic int
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	93	8	Sgsmrxqualfull int
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	94	8	Sgsmrxqualsub int
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	95	8	Sgsmrxlevfull int
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	96	8	Sgsmrxlevsub int
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	97	8	Sgsmspeechcode int
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	98	8	SgsmRTLmax int
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	99	8	SgsmRTLcur int
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	100	8	DLPDUThr int
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	101	8	DLSDUThr int
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	102	8	PDUErrRate int
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	103	8	ULPDUThr int
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	104	8	ULSDUThr int
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	105	8	PDURTXRate int
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	106	8	SNeiState0 int
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	107	8	SNeiState1 int
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	108	8	SNeiState2 int
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	109	8	SNeiState3 int
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	110	8	SNeiState4 int
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	111	8	SNeiState5 int
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	112	8	SNeiLockedNum0 int
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	113	8	SNeiLockedNum1 int
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	114	8	SNeiLockedNum2 int
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	115	8	SNeiLockedNum3 int
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	116	8	SNeiLockedNum4 int
1073	SEARCHTYPE_WCDMA_SAMPLE_NORMAL	117	8	SNeiLockedNum5 int
1074	SEARCHTYPE_NEW_LOGSEARCH_EVENT	1	8	文件ID4BYTES
1074	SEARCHTYPE_NEW_LOGSEARCH_EVENT	2	8	事件点id（4BYTES）用于与采样点ID的联动
1074	SEARCHTYPE_NEW_LOGSEARCH_EVENT	3	8	事件点时间（4BYTES）
1074	SEARCHTYPE_NEW_LOGSEARCH_EVENT	4	1	手机序列（1BYTE）
1074	SEARCHTYPE_NEW_LOGSEARCH_EVENT	5	2	毫秒
1074	SEARCHTYPE_NEW_LOGSEARCH_EVENT	6	8	经度（4BYTES）
1074	SEARCHTYPE_NEW_LOGSEARCH_EVENT	7	8	纬度（4BYTES）
1074	SEARCHTYPE_NEW_LOGSEARCH_EVENT	8	8	事件ID（4BYTES）
1074	SEARCHTYPE_NEW_LOGSEARCH_EVENT	9	8	iLAC（4BYTES）主服小区
1074	SEARCHTYPE_NEW_LOGSEARCH_EVENT	10	2	wRAC（2BYTES）
1074	SEARCHTYPE_NEW_LOGSEARCH_EVENT	11	8	iCI（4BYTES）
1074	SEARCHTYPE_NEW_LOGSEARCH_EVENT	12	8	iTargetLAC（4BYTES）目标小区如切换小区重选等
1074	SEARCHTYPE_NEW_LOGSEARCH_EVENT	13	2	wTargetRAC（2BYTES）
1074	SEARCHTYPE_NEW_LOGSEARCH_EVENT	14	8	iTargetCI（4BYTES）
1074	SEARCHTYPE_NEW_LOGSEARCH_EVENT	15	14	事件value1(4BYTES)
1074	SEARCHTYPE_NEW_LOGSEARCH_EVENT	16	14	事件value2(4BYTES)
1074	SEARCHTYPE_NEW_LOGSEARCH_EVENT	17	14	事件value3(4BYTES)
1074	SEARCHTYPE_NEW_LOGSEARCH_EVENT	18	14	事件value4(4BYTES)
1074	SEARCHTYPE_NEW_LOGSEARCH_EVENT	19	14	事件value5(4BYTES)
1074	SEARCHTYPE_NEW_LOGSEARCH_EVENT	20	14	事件value6(4BYTES)
1074	SEARCHTYPE_NEW_LOGSEARCH_EVENT	21	14	事件value7(4BYTES)
1074	SEARCHTYPE_NEW_LOGSEARCH_EVENT	22	14	事件value8(4BYTES)
1074	SEARCHTYPE_NEW_LOGSEARCH_EVENT	23	14	事件value9(4BYTES)
1074	SEARCHTYPE_NEW_LOGSEARCH_EVENT	24	14	事件value10(4BYTES)
1075	SEARCHTYPE_NEW_LOGSEARCH_MSG_DETAIL	1	8	文件ID4BYTES
1075	SEARCHTYPE_NEW_LOGSEARCH_MSG_DETAIL	2	8	消息序列ID（4BYTES）
1075	SEARCHTYPE_NEW_LOGSEARCH_MSG_DETAIL	3	8	消息时间（4BYTES）
1075	SEARCHTYPE_NEW_LOGSEARCH_MSG_DETAIL	4	1	手机序列（1BYTE）
1075	SEARCHTYPE_NEW_LOGSEARCH_MSG_DETAIL	5	2	消息时间毫秒（2BYTES）
1075	SEARCHTYPE_NEW_LOGSEARCH_MSG_DETAIL	6	8	消息ID（4BYTES）
1075	SEARCHTYPE_NEW_LOGSEARCH_MSG_DETAIL	7	1	消息方向（1BYTE）
1076	SEARCHTYPE_NEW_LOGSEARCH_MSG_DEPTH	1	8	文件ID4BYTES
1076	SEARCHTYPE_NEW_LOGSEARCH_MSG_DEPTH	2	8	消息序列ID（4BYTES）
1076	SEARCHTYPE_NEW_LOGSEARCH_MSG_DEPTH	3	8	消息时间（4BYTES）
1076	SEARCHTYPE_NEW_LOGSEARCH_MSG_DEPTH	4	1	手机序列（1BYTE）
1076	SEARCHTYPE_NEW_LOGSEARCH_MSG_DEPTH	5	2	消息时间毫秒（2BYTES）
1076	SEARCHTYPE_NEW_LOGSEARCH_MSG_DEPTH	6	8	消息ID（4BYTES）
1076	SEARCHTYPE_NEW_LOGSEARCH_MSG_DEPTH	7	1	消息方向（1BYTE）
1076	SEARCHTYPE_NEW_LOGSEARCH_MSG_DEPTH	8	9	消息二进制码流（2BYTES码流长度后续为二进制码）
1077	SEARCHTYPE_TABLE_INFO	1	8	ifileid int
1077	SEARCHTYPE_TABLE_INFO	2	8	itesttype
1077	SEARCHTYPE_TABLE_INFO	3	8	iservicetype int
1077	SEARCHTYPE_TABLE_INFO	4	6	strmsgtbname
1077	SEARCHTYPE_TABLE_INFO	5	6	streventtbname
1077	SEARCHTYPE_TABLE_INFO	6	6	strsampletbname
1077	SEARCHTYPE_TABLE_INFO	7	6	strsampletbname2
1077	SEARCHTYPE_TABLE_INFO	8	6	suffix_week
1078	SEARCHTYPE_WCDMA_GRID_AMR	1	8	ifileid int
1078	SEARCHTYPE_WCDMA_GRID_AMR	2	8	[itllongitude] [int]
1078	SEARCHTYPE_WCDMA_GRID_AMR	3	8	[itllatitude] [int]
1078	SEARCHTYPE_WCDMA_GRID_AMR	4	8	[ibrlongitude] [int]
1078	SEARCHTYPE_WCDMA_GRID_AMR	5	8	[ibrlatitude] [int]
1078	SEARCHTYPE_WCDMA_GRID_AMR	6	5	[ftotalEc_Io_Max] [float]
1078	SEARCHTYPE_WCDMA_GRID_AMR	7	5	[ftotalEc_Io_Min] [float]
1078	SEARCHTYPE_WCDMA_GRID_AMR	8	5	[ftotalEc_Io_Mean] [float]
1078	SEARCHTYPE_WCDMA_GRID_AMR	9	5	[fbestEc_Io_Max] [float]
1078	SEARCHTYPE_WCDMA_GRID_AMR	10	5	[fbestEc_Io_Min] [float]
1078	SEARCHTYPE_WCDMA_GRID_AMR	11	5	[fbestEc_Io_Mean] [float]
1078	SEARCHTYPE_WCDMA_GRID_AMR	12	5	[fTotalRSCP_Max] [float]
1078	SEARCHTYPE_WCDMA_GRID_AMR	13	5	[fTotalRSCP_Min] [float]
1078	SEARCHTYPE_WCDMA_GRID_AMR	14	5	[fTotalRSCP_Mean] [float]
1078	SEARCHTYPE_WCDMA_GRID_AMR	15	5	[fBestRSCP_Max] [float]
1078	SEARCHTYPE_WCDMA_GRID_AMR	16	5	[fBestRSCP_Min] [float]
1078	SEARCHTYPE_WCDMA_GRID_AMR	17	5	[fBestRSCP_Mean] [float]
1078	SEARCHTYPE_WCDMA_GRID_AMR	18	5	[fRxPower_Max] [float]
1078	SEARCHTYPE_WCDMA_GRID_AMR	19	5	[fRxPower_Min] [float]
1078	SEARCHTYPE_WCDMA_GRID_AMR	20	5	[fRxPower_Mean] [float]
1078	SEARCHTYPE_WCDMA_GRID_AMR	21	5	[fTxPower_Max] [float]
1078	SEARCHTYPE_WCDMA_GRID_AMR	22	5	[fTxPower_Min] [float]
1078	SEARCHTYPE_WCDMA_GRID_AMR	23	5	[fTxPower_Mean] [float]
1078	SEARCHTYPE_WCDMA_GRID_AMR	24	5	[fSIR_Max] [float]
1078	SEARCHTYPE_WCDMA_GRID_AMR	25	5	[fSIR_Min] [float]
1078	SEARCHTYPE_WCDMA_GRID_AMR	26	5	[fSIR_Mean] [float]
1078	SEARCHTYPE_WCDMA_GRID_AMR	27	5	[fBLER_Max] [float]
1078	SEARCHTYPE_WCDMA_GRID_AMR	28	5	[fBLER_Min] [float]
1078	SEARCHTYPE_WCDMA_GRID_AMR	29	5	[fBLER_Mean] [float]
1078	SEARCHTYPE_WCDMA_GRID_AMR	30	8	[iduration_TD] [int]
1078	SEARCHTYPE_WCDMA_GRID_AMR	31	8	[iduration_GSM] [int]
1078	SEARCHTYPE_WCDMA_GRID_AMR	32	8	[idistance_TD] [int]
1078	SEARCHTYPE_WCDMA_GRID_AMR	33	8	[idistance_GSM] [int]
1078	SEARCHTYPE_WCDMA_GRID_AMR	34	8	[iRSCP_F90_ECIO_F12] [int]
1078	SEARCHTYPE_WCDMA_GRID_AMR	35	8	[iRSCP_F85_ECIO_F10] [int]
1078	SEARCHTYPE_WCDMA_GRID_AMR	36	8	[itotalEc_Io_F14] [int]
1078	SEARCHTYPE_WCDMA_GRID_AMR	37	8	[itotalEc_Io_F14_F12] [int]
1078	SEARCHTYPE_WCDMA_GRID_AMR	38	8	[itotalEc_Io_F12_F11] [int]
1078	SEARCHTYPE_WCDMA_GRID_AMR	39	8	[itotalEc_Io_F11_F10] [int]
1078	SEARCHTYPE_WCDMA_GRID_AMR	40	8	[itotalEc_Io_F10_F8] [int]
1078	SEARCHTYPE_WCDMA_GRID_AMR	41	8	[itotalEc_Io_F8] [int]
1078	SEARCHTYPE_WCDMA_GRID_AMR	42	8	[ibestEc_Io_F14] [int]
1078	SEARCHTYPE_WCDMA_GRID_AMR	43	8	[ibestEc_Io_F14_F12] [int]
1078	SEARCHTYPE_WCDMA_GRID_AMR	44	8	[ibestEc_Io_F12_F11] [int]
1078	SEARCHTYPE_WCDMA_GRID_AMR	45	8	[ibestEc_Io_F11_F10] [int]
1078	SEARCHTYPE_WCDMA_GRID_AMR	46	8	[ibestEc_Io_F10_F8] [int]
1078	SEARCHTYPE_WCDMA_GRID_AMR	47	8	[ibestEc_Io_F8] [int]
1078	SEARCHTYPE_WCDMA_GRID_AMR	48	8	[iTotalRSCP_F105] [int]
1078	SEARCHTYPE_WCDMA_GRID_AMR	49	8	[iTotalRSCP_F105_F100] [int]
1078	SEARCHTYPE_WCDMA_GRID_AMR	50	8	[iTotalRSCP_F100_F90] [int]
1078	SEARCHTYPE_WCDMA_GRID_AMR	51	8	[iTotalRSCP_F90_F85] [int]
1078	SEARCHTYPE_WCDMA_GRID_AMR	52	8	[iTotalRSCP_F85_F80] [int]
1078	SEARCHTYPE_WCDMA_GRID_AMR	53	8	[iTotalRSCP_F80] [int]
1078	SEARCHTYPE_WCDMA_GRID_AMR	54	8	[iBestRSCP_F105] [int]
1078	SEARCHTYPE_WCDMA_GRID_AMR	55	8	[iBestRSCP_F105_F100] [int]
1078	SEARCHTYPE_WCDMA_GRID_AMR	56	8	[iBestRSCP_F100_F90] [int]
1078	SEARCHTYPE_WCDMA_GRID_AMR	57	8	[iBestRSCP_F90_F85] [int]
1078	SEARCHTYPE_WCDMA_GRID_AMR	58	8	[iBestRSCP_F85_F80] [int]
1078	SEARCHTYPE_WCDMA_GRID_AMR	59	8	[iBestRSCP_F80] [int]
1078	SEARCHTYPE_WCDMA_GRID_AMR	60	8	[iRxPower_F90] [int]
1078	SEARCHTYPE_WCDMA_GRID_AMR	61	8	[iRxPower_F90_F85] [int]
1078	SEARCHTYPE_WCDMA_GRID_AMR	62	8	[iRxPower_F85_F80] [int]
1078	SEARCHTYPE_WCDMA_GRID_AMR	63	8	[iRxPower_F80_F75] [int]
1078	SEARCHTYPE_WCDMA_GRID_AMR	64	8	[iRxPower_F75] [int]
1078	SEARCHTYPE_WCDMA_GRID_AMR	65	8	[iTxPower_F15] [int]
1078	SEARCHTYPE_WCDMA_GRID_AMR	66	8	[iTxPower_F15_0] [int]
1078	SEARCHTYPE_WCDMA_GRID_AMR	67	8	[iTxPower_0_10] [int]
1078	SEARCHTYPE_WCDMA_GRID_AMR	68	8	[iTxPower_10_20] [int]
1078	SEARCHTYPE_WCDMA_GRID_AMR	69	8	[iTxPower_20] [int]
1078	SEARCHTYPE_WCDMA_GRID_AMR	70	8	[iTxPower_F10_RSCP_F80] [int]
1078	SEARCHTYPE_WCDMA_GRID_AMR	71	8	[iSIR_0] [int]
1078	SEARCHTYPE_WCDMA_GRID_AMR	72	8	[iSIR_0_6] [int]
1078	SEARCHTYPE_WCDMA_GRID_AMR	73	8	[iSIR_6_9] [int]
1078	SEARCHTYPE_WCDMA_GRID_AMR	74	8	[iSIR_9_12] [int]
1078	SEARCHTYPE_WCDMA_GRID_AMR	75	8	[iSIR_12] [int]
1078	SEARCHTYPE_WCDMA_GRID_AMR	76	8	[iBLER_0_1] [int]
1078	SEARCHTYPE_WCDMA_GRID_AMR	77	8	[iBLER_1_2] [int]
1078	SEARCHTYPE_WCDMA_GRID_AMR	78	8	[iBLER_2_3] [int]
1078	SEARCHTYPE_WCDMA_GRID_AMR	79	8	[iBLER_3_4] [int]
1078	SEARCHTYPE_WCDMA_GRID_AMR	80	8	[iBLER_4_5] [int]
1078	SEARCHTYPE_WCDMA_GRID_AMR	81	8	[iBLER_5_100] [int]
1078	SEARCHTYPE_WCDMA_GRID_AMR	82	8	[iActiveSet_1] [int]
1078	SEARCHTYPE_WCDMA_GRID_AMR	83	8	[iActiveSet_2] [int]
1078	SEARCHTYPE_WCDMA_GRID_AMR	84	8	[iActiveSet_3] [int]
1078	SEARCHTYPE_WCDMA_GRID_AMR	85	8	[iActiveSet_4] [int]
1078	SEARCHTYPE_WCDMA_GRID_AMR	86	8	[iActiveSet_5] [int]
1078	SEARCHTYPE_WCDMA_GRID_AMR	87	8	[iActiveSet_6] [int]
1078	SEARCHTYPE_WCDMA_GRID_AMR	88	8	[iActiveSet_other] [int]
1078	SEARCHTYPE_WCDMA_GRID_AMR	89	8	[iMOS_sample] [int]
1078	SEARCHTYPE_WCDMA_GRID_AMR	90	8	[iMOS_total] [int]
1078	SEARCHTYPE_WCDMA_GRID_AMR	91	8	[iMOS_20] [int]
1078	SEARCHTYPE_WCDMA_GRID_AMR	92	8	[iMOS_20_25] [int]
1078	SEARCHTYPE_WCDMA_GRID_AMR	93	8	[iMOS_25_30] [int]
1078	SEARCHTYPE_WCDMA_GRID_AMR	94	8	[iMOS_30_35] [int]
1078	SEARCHTYPE_WCDMA_GRID_AMR	95	8	[iMOS_35_40] [int]
1078	SEARCHTYPE_WCDMA_GRID_AMR	96	8	[iMOS_40_45] [int]
1078	SEARCHTYPE_WCDMA_GRID_AMR	97	8	[iMOS_45] [int] NULL
1079	SEARCHTYPE_WCDMA_GRID_VP	1	8	ifileid int
1079	SEARCHTYPE_WCDMA_GRID_VP	2	8	[itllongitude] [int]
1079	SEARCHTYPE_WCDMA_GRID_VP	3	8	[itllatitude] [int]
1079	SEARCHTYPE_WCDMA_GRID_VP	4	8	[ibrlongitude] [int]
1079	SEARCHTYPE_WCDMA_GRID_VP	5	8	[ibrlatitude] [int]
1079	SEARCHTYPE_WCDMA_GRID_VP	6	5	[ftotalEc_Io_Max] [float]
1079	SEARCHTYPE_WCDMA_GRID_VP	7	5	[ftotalEc_Io_Min] [float]
1079	SEARCHTYPE_WCDMA_GRID_VP	8	5	[ftotalEc_Io_Mean] [float]
1079	SEARCHTYPE_WCDMA_GRID_VP	9	5	[fbestEc_Io_Max] [float]
1079	SEARCHTYPE_WCDMA_GRID_VP	10	5	[fbestEc_Io_Min] [float]
1079	SEARCHTYPE_WCDMA_GRID_VP	11	5	[fbestEc_Io_Mean] [float]
1079	SEARCHTYPE_WCDMA_GRID_VP	12	5	[fTotalRSCP_Max] [float]
1079	SEARCHTYPE_WCDMA_GRID_VP	13	5	[fTotalRSCP_Min] [float]
1079	SEARCHTYPE_WCDMA_GRID_VP	14	5	[fTotalRSCP_Mean] [float]
1079	SEARCHTYPE_WCDMA_GRID_VP	15	5	[fBestRSCP_Max] [float]
1079	SEARCHTYPE_WCDMA_GRID_VP	16	5	[fBestRSCP_Min] [float]
1079	SEARCHTYPE_WCDMA_GRID_VP	17	5	[fBestRSCP_Mean] [float]
1079	SEARCHTYPE_WCDMA_GRID_VP	18	5	[fRxPower_Max] [float]
1079	SEARCHTYPE_WCDMA_GRID_VP	19	5	[fRxPower_Min] [float]
1079	SEARCHTYPE_WCDMA_GRID_VP	20	5	[fRxPower_Mean] [float]
1079	SEARCHTYPE_WCDMA_GRID_VP	21	5	[fTxPower_Max] [float]
1079	SEARCHTYPE_WCDMA_GRID_VP	22	5	[fTxPower_Min] [float]
1079	SEARCHTYPE_WCDMA_GRID_VP	23	5	[fTxPower_Mean] [float]
1079	SEARCHTYPE_WCDMA_GRID_VP	24	5	[fSIR_Max] [float]
1079	SEARCHTYPE_WCDMA_GRID_VP	25	5	[fSIR_Min] [float]
1079	SEARCHTYPE_WCDMA_GRID_VP	26	5	[fSIR_Mean] [float]
1079	SEARCHTYPE_WCDMA_GRID_VP	27	5	[fBLER_Max] [float]
1079	SEARCHTYPE_WCDMA_GRID_VP	28	5	[fBLER_Min] [float]
1079	SEARCHTYPE_WCDMA_GRID_VP	29	5	[fBLER_Mean] [float]
1079	SEARCHTYPE_WCDMA_GRID_VP	30	8	[iduration_TD] [int]
1079	SEARCHTYPE_WCDMA_GRID_VP	31	8	[iduration_GSM] [int]
1079	SEARCHTYPE_WCDMA_GRID_VP	32	8	[idistance_TD] [int]
1079	SEARCHTYPE_WCDMA_GRID_VP	33	8	[idistance_GSM] [int]
1079	SEARCHTYPE_WCDMA_GRID_VP	34	8	[iRSCP_F90_ECIO_F12] [int]
1079	SEARCHTYPE_WCDMA_GRID_VP	35	8	[iRSCP_F85_ECIO_F10] [int]
1079	SEARCHTYPE_WCDMA_GRID_VP	36	8	[itotalEc_Io_F14] [int]
1079	SEARCHTYPE_WCDMA_GRID_VP	37	8	[itotalEc_Io_F14_F12] [int]
1079	SEARCHTYPE_WCDMA_GRID_VP	38	8	[itotalEc_Io_F12_F11] [int]
1079	SEARCHTYPE_WCDMA_GRID_VP	39	8	[itotalEc_Io_F11_F10] [int]
1079	SEARCHTYPE_WCDMA_GRID_VP	40	8	[itotalEc_Io_F10_F8] [int]
1079	SEARCHTYPE_WCDMA_GRID_VP	41	8	[itotalEc_Io_F8] [int]
1079	SEARCHTYPE_WCDMA_GRID_VP	42	8	[ibestEc_Io_F14] [int]
1079	SEARCHTYPE_WCDMA_GRID_VP	43	8	[ibestEc_Io_F14_F12] [int]
1079	SEARCHTYPE_WCDMA_GRID_VP	44	8	[ibestEc_Io_F12_F11] [int]
1079	SEARCHTYPE_WCDMA_GRID_VP	45	8	[ibestEc_Io_F11_F10] [int]
1079	SEARCHTYPE_WCDMA_GRID_VP	46	8	[ibestEc_Io_F10_F8] [int]
1079	SEARCHTYPE_WCDMA_GRID_VP	47	8	[ibestEc_Io_F8] [int]
1079	SEARCHTYPE_WCDMA_GRID_VP	48	8	[iTotalRSCP_F105] [int]
1079	SEARCHTYPE_WCDMA_GRID_VP	49	8	[iTotalRSCP_F105_F100] [int]
1079	SEARCHTYPE_WCDMA_GRID_VP	50	8	[iTotalRSCP_F100_F90] [int]
1079	SEARCHTYPE_WCDMA_GRID_VP	51	8	[iTotalRSCP_F90_F85] [int]
1079	SEARCHTYPE_WCDMA_GRID_VP	52	8	[iTotalRSCP_F85_F80] [int]
1079	SEARCHTYPE_WCDMA_GRID_VP	53	8	[iTotalRSCP_F80] [int]
1079	SEARCHTYPE_WCDMA_GRID_VP	54	8	[iBestRSCP_F105] [int]
1079	SEARCHTYPE_WCDMA_GRID_VP	55	8	[iBestRSCP_F105_F100] [int]
1079	SEARCHTYPE_WCDMA_GRID_VP	56	8	[iBestRSCP_F100_F90] [int]
1079	SEARCHTYPE_WCDMA_GRID_VP	57	8	[iBestRSCP_F90_F85] [int]
1079	SEARCHTYPE_WCDMA_GRID_VP	58	8	[iBestRSCP_F85_F80] [int]
1079	SEARCHTYPE_WCDMA_GRID_VP	59	8	[iBestRSCP_F80] [int]
1079	SEARCHTYPE_WCDMA_GRID_VP	60	8	[iRxPower_F90] [int]
1079	SEARCHTYPE_WCDMA_GRID_VP	61	8	[iRxPower_F90_F85] [int]
1079	SEARCHTYPE_WCDMA_GRID_VP	62	8	[iRxPower_F85_F80] [int]
1079	SEARCHTYPE_WCDMA_GRID_VP	63	8	[iRxPower_F80_F75] [int]
1079	SEARCHTYPE_WCDMA_GRID_VP	64	8	[iRxPower_F75] [int]
1079	SEARCHTYPE_WCDMA_GRID_VP	65	8	[iTxPower_F15] [int]
1079	SEARCHTYPE_WCDMA_GRID_VP	66	8	[iTxPower_F15_0] [int]
1079	SEARCHTYPE_WCDMA_GRID_VP	67	8	[iTxPower_0_10] [int]
1079	SEARCHTYPE_WCDMA_GRID_VP	68	8	[iTxPower_10_20] [int]
1079	SEARCHTYPE_WCDMA_GRID_VP	69	8	[iTxPower_20] [int]
1079	SEARCHTYPE_WCDMA_GRID_VP	70	8	[iTxPower_F10_RSCP_F80] [int]
1079	SEARCHTYPE_WCDMA_GRID_VP	71	8	[iSIR_0] [int]
1079	SEARCHTYPE_WCDMA_GRID_VP	72	8	[iSIR_0_6] [int]
1079	SEARCHTYPE_WCDMA_GRID_VP	73	8	[iSIR_6_9] [int]
1079	SEARCHTYPE_WCDMA_GRID_VP	74	8	[iSIR_9_12] [int]
1079	SEARCHTYPE_WCDMA_GRID_VP	75	8	[iSIR_12] [int]
1079	SEARCHTYPE_WCDMA_GRID_VP	76	8	[iBLER_0_1] [int]
1079	SEARCHTYPE_WCDMA_GRID_VP	77	8	[iBLER_1_2] [int]
1079	SEARCHTYPE_WCDMA_GRID_VP	78	8	[iBLER_2_3] [int]
1079	SEARCHTYPE_WCDMA_GRID_VP	79	8	[iBLER_3_4] [int]
1079	SEARCHTYPE_WCDMA_GRID_VP	80	8	[iBLER_4_5] [int]
1079	SEARCHTYPE_WCDMA_GRID_VP	81	8	[iBLER_5_100] [int]
1079	SEARCHTYPE_WCDMA_GRID_VP	82	8	[iActiveSet_1] [int]
1079	SEARCHTYPE_WCDMA_GRID_VP	83	8	[iActiveSet_2] [int]
1079	SEARCHTYPE_WCDMA_GRID_VP	84	8	[iActiveSet_3] [int]
1079	SEARCHTYPE_WCDMA_GRID_VP	85	8	[iActiveSet_4] [int]
1079	SEARCHTYPE_WCDMA_GRID_VP	86	8	[iActiveSet_5] [int]
1079	SEARCHTYPE_WCDMA_GRID_VP	87	8	[iActiveSet_6] [int]
1079	SEARCHTYPE_WCDMA_GRID_VP	88	8	[iActiveSet_other] [int]
1079	SEARCHTYPE_WCDMA_GRID_VP	89	8	[iMOS_sample] [int]
1079	SEARCHTYPE_WCDMA_GRID_VP	90	8	[iMOS_total] [int]
1079	SEARCHTYPE_WCDMA_GRID_VP	91	8	[iMOS_20] [int]
1079	SEARCHTYPE_WCDMA_GRID_VP	92	8	[iMOS_20_25] [int]
1079	SEARCHTYPE_WCDMA_GRID_VP	93	8	[iMOS_25_30] [int]
1079	SEARCHTYPE_WCDMA_GRID_VP	94	8	[iMOS_30_35] [int]
1079	SEARCHTYPE_WCDMA_GRID_VP	95	8	[iMOS_35_40] [int]
1079	SEARCHTYPE_WCDMA_GRID_VP	96	8	[iMOS_40_45] [int]
1079	SEARCHTYPE_WCDMA_GRID_VP	97	8	[iMOS_45] [int] NULL
1080	SEARCHTYPE_WCDMA_GRID_PS	1	8	[ifileid] [int]
1080	SEARCHTYPE_WCDMA_GRID_PS	2	8	[itllongitude] [int]
1080	SEARCHTYPE_WCDMA_GRID_PS	3	8	[itllatitude] [int]
1080	SEARCHTYPE_WCDMA_GRID_PS	4	8	[ibrlongitude] [int]
1080	SEARCHTYPE_WCDMA_GRID_PS	5	8	[ibrlatitude] [int]
1080	SEARCHTYPE_WCDMA_GRID_PS	6	5	[ftotalEc_Io_Max] [float]
1080	SEARCHTYPE_WCDMA_GRID_PS	7	5	[ftotalEc_Io_Min] [float]
1080	SEARCHTYPE_WCDMA_GRID_PS	8	5	[ftotalEc_Io_Mean] [float]
1080	SEARCHTYPE_WCDMA_GRID_PS	9	5	[fbestEc_Io_Max] [float]
1080	SEARCHTYPE_WCDMA_GRID_PS	10	5	[fbestEc_Io_Min] [float]
1080	SEARCHTYPE_WCDMA_GRID_PS	11	5	[fbestEc_Io_Mean] [float]
1080	SEARCHTYPE_WCDMA_GRID_PS	12	5	[fTotalRSCP_Max] [float]
1080	SEARCHTYPE_WCDMA_GRID_PS	13	5	[fTotalRSCP_Min] [float]
1080	SEARCHTYPE_WCDMA_GRID_PS	14	5	[fTotalRSCP_Mean] [float]
1080	SEARCHTYPE_WCDMA_GRID_PS	15	5	[fBestRSCP_Max] [float]
1080	SEARCHTYPE_WCDMA_GRID_PS	16	5	[fBestRSCP_Min] [float]
1080	SEARCHTYPE_WCDMA_GRID_PS	17	5	[fBestRSCP_Mean] [float]
1080	SEARCHTYPE_WCDMA_GRID_PS	18	5	[fRxPower_Max] [float]
1080	SEARCHTYPE_WCDMA_GRID_PS	19	5	[fRxPower_Min] [float]
1080	SEARCHTYPE_WCDMA_GRID_PS	20	5	[fRxPower_Mean] [float]
1080	SEARCHTYPE_WCDMA_GRID_PS	21	5	[fTxPower_Max] [float]
1080	SEARCHTYPE_WCDMA_GRID_PS	22	5	[fTxPower_Min] [float]
1080	SEARCHTYPE_WCDMA_GRID_PS	23	5	[fTxPower_Mean] [float]
1080	SEARCHTYPE_WCDMA_GRID_PS	24	5	[fSIR_Max] [float]
1080	SEARCHTYPE_WCDMA_GRID_PS	25	5	[fSIR_Min] [float]
1080	SEARCHTYPE_WCDMA_GRID_PS	26	5	[fSIR_Mean] [float]
1080	SEARCHTYPE_WCDMA_GRID_PS	27	8	[iduration_TD] [int]
1080	SEARCHTYPE_WCDMA_GRID_PS	28	8	[iduration_GSM] [int]
1080	SEARCHTYPE_WCDMA_GRID_PS	29	8	[idistance_TD] [int]
1080	SEARCHTYPE_WCDMA_GRID_PS	30	8	[idistance_GSM] [int]
1080	SEARCHTYPE_WCDMA_GRID_PS	31	8	[iRSCP_F90_ECIO_F12] [int]
1080	SEARCHTYPE_WCDMA_GRID_PS	32	8	[iRSCP_F85_ECIO_F10] [int]
1080	SEARCHTYPE_WCDMA_GRID_PS	33	8	[itotalEc_Io_F14] [int]
1080	SEARCHTYPE_WCDMA_GRID_PS	34	8	[itotalEc_Io_F14_F12] [int]
1080	SEARCHTYPE_WCDMA_GRID_PS	35	8	[itotalEc_Io_F12_F11] [int]
1080	SEARCHTYPE_WCDMA_GRID_PS	36	8	[itotalEc_Io_F11_F10] [int]
1080	SEARCHTYPE_WCDMA_GRID_PS	37	8	[itotalEc_Io_F10_F8] [int]
1080	SEARCHTYPE_WCDMA_GRID_PS	38	8	[itotalEc_Io_F8] [int]
1080	SEARCHTYPE_WCDMA_GRID_PS	39	8	[ibestEc_Io_F14] [int]
1080	SEARCHTYPE_WCDMA_GRID_PS	40	8	[ibestEc_Io_F14_F12] [int]
1080	SEARCHTYPE_WCDMA_GRID_PS	41	8	[ibestEc_Io_F12_F11] [int]
1080	SEARCHTYPE_WCDMA_GRID_PS	42	8	[ibestEc_Io_F11_F10] [int]
1080	SEARCHTYPE_WCDMA_GRID_PS	43	8	[ibestEc_Io_F10_F8] [int]
1080	SEARCHTYPE_WCDMA_GRID_PS	44	8	[ibestEc_Io_F8] [int]
1080	SEARCHTYPE_WCDMA_GRID_PS	45	8	[iTotalRSCP_F105] [int]
1080	SEARCHTYPE_WCDMA_GRID_PS	46	8	[iTotalRSCP_F105_F100] [int]
1080	SEARCHTYPE_WCDMA_GRID_PS	47	8	[iTotalRSCP_F100_F90] [int]
1080	SEARCHTYPE_WCDMA_GRID_PS	48	8	[iTotalRSCP_F90_F85] [int]
1080	SEARCHTYPE_WCDMA_GRID_PS	49	8	[iTotalRSCP_F85_F80] [int]
1080	SEARCHTYPE_WCDMA_GRID_PS	50	8	[iTotalRSCP_F80] [int]
1080	SEARCHTYPE_WCDMA_GRID_PS	51	8	[iBestRSCP_F105] [int]
1080	SEARCHTYPE_WCDMA_GRID_PS	52	8	[iBestRSCP_F105_F100] [int]
1080	SEARCHTYPE_WCDMA_GRID_PS	53	8	[iBestRSCP_F100_F90] [int]
1080	SEARCHTYPE_WCDMA_GRID_PS	54	8	[iBestRSCP_F90_F85] [int]
1080	SEARCHTYPE_WCDMA_GRID_PS	55	8	[iBestRSCP_F85_F80] [int]
1080	SEARCHTYPE_WCDMA_GRID_PS	56	8	[iBestRSCP_F80] [int]
1080	SEARCHTYPE_WCDMA_GRID_PS	57	8	[iRxPower_F90] [int]
1080	SEARCHTYPE_WCDMA_GRID_PS	58	8	[iRxPower_F90_F85] [int]
1080	SEARCHTYPE_WCDMA_GRID_PS	59	8	[iRxPower_F85_F80] [int]
1080	SEARCHTYPE_WCDMA_GRID_PS	60	8	[iRxPower_F80_F75] [int]
1080	SEARCHTYPE_WCDMA_GRID_PS	61	8	[iRxPower_F75] [int]
1080	SEARCHTYPE_WCDMA_GRID_PS	62	8	[iTxPower_F15] [int]
1080	SEARCHTYPE_WCDMA_GRID_PS	63	8	[iTxPower_F15_0] [int]
1080	SEARCHTYPE_WCDMA_GRID_PS	64	8	[iTxPower_0_10] [int]
1080	SEARCHTYPE_WCDMA_GRID_PS	65	8	[iTxPower_10_20] [int]
1080	SEARCHTYPE_WCDMA_GRID_PS	66	8	[iTxPower_20] [int]
1080	SEARCHTYPE_WCDMA_GRID_PS	67	8	[iTxPower_F10_RSCP_F80] [int]
1080	SEARCHTYPE_WCDMA_GRID_PS	68	8	[iSIR_0] [int]
1080	SEARCHTYPE_WCDMA_GRID_PS	69	8	[iSIR_0_6] [int]
1080	SEARCHTYPE_WCDMA_GRID_PS	70	8	[iSIR_6_9] [int]
1080	SEARCHTYPE_WCDMA_GRID_PS	71	8	[iSIR_9_12] [int]
1080	SEARCHTYPE_WCDMA_GRID_PS	72	8	[iSIR_12] [int]
1080	SEARCHTYPE_WCDMA_GRID_PS	73	8	[iActiveSet_1] [int]
1080	SEARCHTYPE_WCDMA_GRID_PS	74	8	[iActiveSet_2] [int]
1080	SEARCHTYPE_WCDMA_GRID_PS	75	8	[iActiveSet_3] [int]
1080	SEARCHTYPE_WCDMA_GRID_PS	76	8	[iActiveSet_4] [int]
1080	SEARCHTYPE_WCDMA_GRID_PS	77	8	[iActiveSet_5] [int]
1080	SEARCHTYPE_WCDMA_GRID_PS	78	8	[iActiveSet_6] [int]
1080	SEARCHTYPE_WCDMA_GRID_PS	79	8	[iActiveSet_other] [int]
1080	SEARCHTYPE_WCDMA_GRID_PS	80	8	[iMOS_sample] [int]
1080	SEARCHTYPE_WCDMA_GRID_PS	81	8	[iMOS_total] [int]
1080	SEARCHTYPE_WCDMA_GRID_PS	82	8	[iMOS_20] [int]
1080	SEARCHTYPE_WCDMA_GRID_PS	83	8	[iMOS_20_25] [int]
1080	SEARCHTYPE_WCDMA_GRID_PS	84	8	[iMOS_25_30] [int]
1080	SEARCHTYPE_WCDMA_GRID_PS	85	8	[iMOS_30_35] [int]
1080	SEARCHTYPE_WCDMA_GRID_PS	86	8	[iMOS_35_40] [int]
1080	SEARCHTYPE_WCDMA_GRID_PS	87	8	[iMOS_40_45] [int]
1080	SEARCHTYPE_WCDMA_GRID_PS	88	8	[iMOS_45] [int]
1080	SEARCHTYPE_WCDMA_GRID_PS	89	8	[iBLERD_samplenum_HSDPA] [int]
1080	SEARCHTYPE_WCDMA_GRID_PS	90	8	[iBLERD_samplenum_R4] [int]
1080	SEARCHTYPE_WCDMA_GRID_PS	91	8	[iBLERD_samplenum_GPRSEDGE] [int]
1080	SEARCHTYPE_WCDMA_GRID_PS	92	5	[fBLERD_total_HSDPA] [float]
1080	SEARCHTYPE_WCDMA_GRID_PS	93	5	[fBLERD_total_R4] [float]
1080	SEARCHTYPE_WCDMA_GRID_PS	94	5	[fBLERD_total_GPRSEDGE] [float]
1080	SEARCHTYPE_WCDMA_GRID_PS	95	8	[iFTP_UL_BYTES] [int]
1080	SEARCHTYPE_WCDMA_GRID_PS	96	8	[iFTP_UL_TIME] [int]
1080	SEARCHTYPE_WCDMA_GRID_PS	97	8	[iFTP_APP_UL_BYTES] [int]
1080	SEARCHTYPE_WCDMA_GRID_PS	98	8	[iFTP_RLC_UL_BYTES] [int]
1080	SEARCHTYPE_WCDMA_GRID_PS	99	8	[iFTP_MAC_UL_BYTES] [int]
1080	SEARCHTYPE_WCDMA_GRID_PS	100	8	[iFTP_UL_TIME_HSUPA] [int]
1080	SEARCHTYPE_WCDMA_GRID_PS	101	8	[iFTP_UL_TIME_R4] [int]
1080	SEARCHTYPE_WCDMA_GRID_PS	102	8	[iFTP_UL_TIME_EDGE] [int]
1080	SEARCHTYPE_WCDMA_GRID_PS	103	8	[iFTP_UL_BYTES_HSUPA] [int]
1080	SEARCHTYPE_WCDMA_GRID_PS	104	8	[iFTP_UL_BYTES_R4] [int]
1080	SEARCHTYPE_WCDMA_GRID_PS	105	8	[iFTP_UL_BYTES_EDGE] [int]
1080	SEARCHTYPE_WCDMA_GRID_PS	106	8	[iFTP_DL_BYTES] [int]
1080	SEARCHTYPE_WCDMA_GRID_PS	107	8	[iFTP_DL_TIME] [int]
1080	SEARCHTYPE_WCDMA_GRID_PS	108	8	[iFTP_APP_DL_BYTES] [int]
1080	SEARCHTYPE_WCDMA_GRID_PS	109	8	[iFTP_RLC_DL_BYTES] [int]
1080	SEARCHTYPE_WCDMA_GRID_PS	110	8	[iFTP_MAC_DL_BYTES] [int]
1080	SEARCHTYPE_WCDMA_GRID_PS	111	8	[iFTP_DL_TIME_R4] [int]
1080	SEARCHTYPE_WCDMA_GRID_PS	112	8	[iFTP_DL_TIME_EDGE] [int]
1080	SEARCHTYPE_WCDMA_GRID_PS	113	8	[iFTP_DL_BYTES_R4] [int]
1080	SEARCHTYPE_WCDMA_GRID_PS	114	8	[iFTP_DL_BYTES_EDGE] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	1	8	[ifileid] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	2	8	[itllongitude] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	3	8	[itllatitude] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	4	8	[ibrlongitude] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	5	8	[ibrlatitude] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	6	5	[ftotalEc_Io_Max] [float]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	7	5	[ftotalEc_Io_Min] [float]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	8	5	[ftotalEc_Io_Mean] [float]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	9	5	[fbestEc_Io_Max] [float]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	10	5	[fbestEc_Io_Min] [float]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	11	5	[fbestEc_Io_Mean] [float]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	12	5	[fTotalRSCP_Max] [float]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	13	5	[fTotalRSCP_Min] [float]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	14	5	[fTotalRSCP_Mean] [float]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	15	5	[fBestRSCP_Max] [float]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	16	5	[fBestRSCP_Min] [float]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	17	5	[fBestRSCP_Mean] [float]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	18	5	[fRxPower_Max] [float]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	19	5	[fRxPower_Min] [float]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	20	5	[fRxPower_Mean] [float]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	21	5	[fTxPower_Max] [float]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	22	5	[fTxPower_Min] [float]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	23	5	[fTxPower_Mean] [float]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	24	5	[fSIR_Max] [float]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	25	5	[fSIR_Min] [float]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	26	5	[fSIR_Mean] [float]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	27	8	[iduration_TD] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	28	8	[iduration_GSM] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	29	8	[idistance_TD] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	30	8	[idistance_GSM] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	31	8	[iRSCP_F90_ECIO_F12] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	32	8	[iRSCP_F85_ECIO_F10] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	33	8	[itotalEc_Io_F14] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	34	8	[itotalEc_Io_F14_F12] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	35	8	[itotalEc_Io_F12_F11] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	36	8	[itotalEc_Io_F11_F10] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	37	8	[itotalEc_Io_F10_F8] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	38	8	[itotalEc_Io_F8] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	39	8	[ibestEc_Io_F14] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	40	8	[ibestEc_Io_F14_F12] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	41	8	[ibestEc_Io_F12_F11] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	42	8	[ibestEc_Io_F11_F10] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	43	8	[ibestEc_Io_F10_F8] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	44	8	[ibestEc_Io_F8] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	45	8	[iTotalRSCP_F105] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	46	8	[iTotalRSCP_F105_F100] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	47	8	[iTotalRSCP_F100_F90] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	48	8	[iTotalRSCP_F90_F85] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	49	8	[iTotalRSCP_F85_F80] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	50	8	[iTotalRSCP_F80] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	51	8	[iBestRSCP_F105] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	52	8	[iBestRSCP_F105_F100] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	53	8	[iBestRSCP_F100_F90] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	54	8	[iBestRSCP_F90_F85] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	55	8	[iBestRSCP_F85_F80] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	56	8	[iBestRSCP_F80] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	57	8	[iRxPower_F90] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	58	8	[iRxPower_F90_F85] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	59	8	[iRxPower_F85_F80] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	60	8	[iRxPower_F80_F75] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	61	8	[iRxPower_F75] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	62	8	[iTxPower_F15] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	63	8	[iTxPower_F15_0] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	64	8	[iTxPower_0_10] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	65	8	[iTxPower_10_20] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	66	8	[iTxPower_20] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	67	8	[iTxPower_F10_RSCP_F80] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	68	8	[iSIR_0] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	69	8	[iSIR_0_6] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	70	8	[iSIR_6_9] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	71	8	[iSIR_9_12] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	72	8	[iSIR_12] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	73	8	[iActiveSet_1] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	74	8	[iActiveSet_2] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	75	8	[iActiveSet_3] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	76	8	[iActiveSet_4] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	77	8	[iActiveSet_5] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	78	8	[iActiveSet_6] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	79	8	[iActiveSet_other] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	80	8	[iMOS_sample] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	81	8	[iMOS_total] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	82	8	[iMOS_20] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	83	8	[iMOS_20_25] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	84	8	[iMOS_25_30] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	85	8	[iMOS_30_35] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	86	8	[iMOS_35_40] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	87	8	[iMOS_40_45] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	88	8	[iMOS_45] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	89	8	[iBLERD_samplenum_HSDPA] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	90	8	[iBLERD_samplenum_R4] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	91	8	[iBLERD_samplenum_GPRSEDGE] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	92	5	[fBLERD_total_HSDPA] [float]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	93	5	[fBLERD_total_R4] [float]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	94	5	[fBLERD_total_GPRSEDGE] [float]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	95	8	[iFTP_UL_BYTES] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	96	8	[iFTP_UL_TIME] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	97	8	[iFTP_APP_UL_BYTES] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	98	8	[iFTP_RLC_UL_BYTES] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	99	8	[iFTP_MAC_UL_BYTES] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	100	8	[iFTP_UL_TIME_HSUPA] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	101	8	[iFTP_UL_TIME_R4] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	102	8	[iFTP_UL_TIME_EDGE] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	103	8	[iFTP_UL_BYTES_HSUPA] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	104	8	[iFTP_UL_BYTES_R4] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	105	8	[iFTP_UL_BYTES_EDGE] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	106	8	[iFTP_DL_BYTES] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	107	8	[iFTP_DL_TIME] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	108	8	[iFTP_APP_DL_BYTES] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	109	8	[iFTP_RLC_DL_BYTES] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	110	8	[iFTP_MAC_DL_BYTES] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	111	8	[iFTP_DL_TIME_HSDPA] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	112	8	[iFTP_DL_TIME_R4] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	113	8	[iFTP_DL_TIME_EDGE] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	114	8	[iFTP_DL_BYTES_HSDPA] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	115	8	[iFTP_DL_BYTES_R4] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	116	8	[iFTP_DL_BYTES_EDGE] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	117	8	[iHS_16QAM_Rate_sample] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	118	8	[iHS_16QAM_Rate_0_5] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	119	8	[iHS_16QAM_Rate_5_10] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	120	8	[iHS_16QAM_Rate_10_20] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	121	8	[iHS_16QAM_Rate_20_50] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	122	8	[iHS_16QAM_Rate_50] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	123	8	[iMean_HSDPA_CQI_sample] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	124	8	[iMean_HSDPA_CQI_10] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	125	8	[iMean_HSDPA_CQI_10_13] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	126	8	[iMean_HSDPA_CQI_13_16] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	127	8	[iMean_HSDPA_CQI_16_19] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	128	8	[iMean_HSDPA_CQI_19_22] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	129	8	[iMean_HSDPA_CQI_22_25] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	130	8	[iMean_HSDPA_CQI_25_28] [int]
1081	SEARCHTYPE_WCDMA_GRID_PSHS	131	8	[iMean_HSDPA_CQI_28] [int]
1082	SEARCHTYPE_LOGSEARCH_INFO_MORE	1	8	itesttype int
1082	SEARCHTYPE_LOGSEARCH_INFO_MORE	2	8	idevicetype int
1082	SEARCHTYPE_LOGSEARCH_INFO_MORE	3	8	ifiletype int
1082	SEARCHTYPE_LOGSEARCH_INFO_MORE	4	8	iservicetype int
1082	SEARCHTYPE_LOGSEARCH_INFO_MORE	5	8	icarriertype int
1082	SEARCHTYPE_LOGSEARCH_INFO_MORE	6	8	iprojecttype int
1082	SEARCHTYPE_LOGSEARCH_INFO_MORE	7	8	ibatch int
1082	SEARCHTYPE_LOGSEARCH_INFO_MORE	8	8	ifileid int
1082	SEARCHTYPE_LOGSEARCH_INFO_MORE	9	6	strfilename varchar(255)
1082	SEARCHTYPE_LOGSEARCH_INFO_MORE	10	6	logtbname varchar(255)
1082	SEARCHTYPE_LOGSEARCH_INFO_MORE	11	8	istime
1082	SEARCHTYPE_LOGSEARCH_INFO_MORE	12	8	ietime
1082	SEARCHTYPE_LOGSEARCH_INFO_MORE	13	8	iduration
1082	SEARCHTYPE_LOGSEARCH_INFO_MORE	14	8	idistance
1082	SEARCHTYPE_LOGSEARCH_INFO_MORE	15	8	itllongitude
1082	SEARCHTYPE_LOGSEARCH_INFO_MORE	16	8	itllatitude
1082	SEARCHTYPE_LOGSEARCH_INFO_MORE	17	8	ibrlongitude
1082	SEARCHTYPE_LOGSEARCH_INFO_MORE	18	8	ibrlatitude
1082	SEARCHTYPE_LOGSEARCH_INFO_MORE	19	8	iareatype
1082	SEARCHTYPE_LOGSEARCH_INFO_MORE	20	8	iareaid
1083	SEARCHTYPE_DOCUMENT_All_INFO	1	8	itesttype int 
1083	SEARCHTYPE_DOCUMENT_All_INFO	2	6	strfilename varchar 
1083	SEARCHTYPE_DOCUMENT_All_INFO	3	6	strsavepath varchar 
1083	SEARCHTYPE_DOCUMENT_All_INFO	4	8	iimporttime int 
1083	SEARCHTYPE_DOCUMENT_All_INFO	5	8	iprojecttype int 
1083	SEARCHTYPE_DOCUMENT_All_INFO	6	8	iyear int 
1083	SEARCHTYPE_DOCUMENT_All_INFO	7	8	ibatch int 
1083	SEARCHTYPE_DOCUMENT_All_INFO	8	8	iareatype int 
1083	SEARCHTYPE_DOCUMENT_All_INFO	9	8	iareaid int 
1083	SEARCHTYPE_DOCUMENT_All_INFO	10	8	idevicetype int 
1083	SEARCHTYPE_DOCUMENT_All_INFO	11	8	ifiletype int 
1083	SEARCHTYPE_DOCUMENT_All_INFO	12	8	iservicetype int 
1083	SEARCHTYPE_DOCUMENT_All_INFO	13	8	icarriertype int 
1083	SEARCHTYPE_DOCUMENT_All_INFO	14	8	iagentid int 
1083	SEARCHTYPE_DOCUMENT_All_INFO	15	8	istaffid int 
1083	SEARCHTYPE_DOCUMENT_All_INFO	16	6	strdesc varchar
1083	SEARCHTYPE_DOCUMENT_All_INFO	17	8	idbvalue int 
1083	SEARCHTYPE_DOCUMENT_All_INFO	18	8	isubtype1 int 
1083	SEARCHTYPE_DOCUMENT_All_INFO	19	8	isubtype2 int 
1083	SEARCHTYPE_DOCUMENT_All_INFO	20	8	statstatus tinyint
1084	SEARCHTYPE_ES_EVENT_TIME	1	8	itime int
1084	SEARCHTYPE_ES_EVENT_TIME	2	8	wtimems int
1085	SEARCHTYPE_CELLSIMU_GRID	1	8	[grid_id] int
1085	SEARCHTYPE_CELLSIMU_GRID	2	8	[ltlongitude] int
1085	SEARCHTYPE_CELLSIMU_GRID	3	8	[ltlatitude] int
1085	SEARCHTYPE_CELLSIMU_GRID	4	8	[brlongitude] int
1085	SEARCHTYPE_CELLSIMU_GRID	5	8	[brlatitude] int
1086	SEARCHTYPE_CELLSIMU_GRID_INFO	1	8	[grid_id] [int]
1086	SEARCHTYPE_CELLSIMU_GRID_INFO	2	8	[cell_id] [int]
1086	SEARCHTYPE_CELLSIMU_GRID_INFO	3	2	[rxlev_min] [smallint]
1086	SEARCHTYPE_CELLSIMU_GRID_INFO	4	2	[rxlev_max] [smallint]
1086	SEARCHTYPE_CELLSIMU_GRID_INFO	5	5	[rxlev_mean] [float]
1086	SEARCHTYPE_CELLSIMU_GRID_INFO	6	8	[sample_count] [int]
1087	SEARCHTYPE_REVIEW_DATA_SAMPLE_SUMARY	1	8	[ifileid] [int]
1087	SEARCHTYPE_REVIEW_DATA_SAMPLE_SUMARY	2	8	isampleid
1087	SEARCHTYPE_REVIEW_DATA_SAMPLE_SUMARY	3	8	itime
1087	SEARCHTYPE_REVIEW_DATA_SAMPLE_SUMARY	4	8	bms
1087	SEARCHTYPE_REVIEW_DATA_SAMPLE_SUMARY	5	8	ilongitude
1087	SEARCHTYPE_REVIEW_DATA_SAMPLE_SUMARY	6	8	ilatitude
1087	SEARCHTYPE_REVIEW_DATA_SAMPLE_SUMARY	7	8	wRxLevSub
1087	SEARCHTYPE_REVIEW_DATA_SAMPLE_SUMARY	8	8	bRxQualSub
1087	SEARCHTYPE_REVIEW_DATA_SAMPLE_SUMARY	9	8	iLAC
1087	SEARCHTYPE_REVIEW_DATA_SAMPLE_SUMARY	10	8	iCI
1087	SEARCHTYPE_REVIEW_DATA_SAMPLE_SUMARY	11	8	wBCCH
1087	SEARCHTYPE_REVIEW_DATA_SAMPLE_SUMARY	12	9	GSM_NeighborInfo
1087	SEARCHTYPE_REVIEW_DATA_SAMPLE_SUMARY	13	9	GSM_NeighborInfo
1087	SEARCHTYPE_REVIEW_DATA_SAMPLE_SUMARY	14	9	GSM_NeighborInfo
1087	SEARCHTYPE_REVIEW_DATA_SAMPLE_SUMARY	15	9	GSM_NeighborInfo
1087	SEARCHTYPE_REVIEW_DATA_SAMPLE_SUMARY	16	9	GSM_NeighborInfo
1087	SEARCHTYPE_REVIEW_DATA_SAMPLE_SUMARY	17	9	GSM_NeighborInfo
1087	SEARCHTYPE_REVIEW_DATA_SAMPLE_SUMARY	18	9	GSM_NeighborInfo
1087	SEARCHTYPE_REVIEW_DATA_SAMPLE_SUMARY	19	9	GSM_NeighborInfo
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	1	8	[ifileid] [int]
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	2	8	isampleid
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	3	8	itime
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	4	8	bms
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	5	8	ilongitude
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	6	8	ilatitude
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	7	8	wRxLevSub
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	8	8	bRxQualSub
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	9	8	iLAC
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	10	8	iCI
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	11	8	wBCCH
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	12	9	GSM_NeighborInfo
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	13	9	GSM_NeighborInfo
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	14	9	GSM_NeighborInfo
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	15	9	GSM_NeighborInfo
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	16	9	GSM_NeighborInfo
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	17	9	GSM_NeighborInfo
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	18	9	GSM_NeighborInfo
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	19	9	GSM_NeighborInfo
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	20	8	wtimems
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	21	8	bMsTxPower
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	22	8	bTA
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	23	8	DSC_Max
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	24	8	DSC_Counter
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	25	8	wC1
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	26	8	wC2
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	27	5	GPRS_BLER_DL
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	28	5	GPRS_RLC_UL_Thr
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	29	5	GPRS_RLC_DL_Thr
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	30	5	GPRS_RLC_UL_RTX_Rate
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	31	5	GPRS_RLC_DL_RTX_Rate
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	32	8	EDGE_UL_MCS
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	33	8	EDGE_DL_MCS
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	34	8	APP_type
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	35	8	APP_Status
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	36	8	APP_DataStatus_DL
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	37	8	APP_DataStatus_UL
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	38	8	APP_TotalSize
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	39	8	APP_TransferedSize
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	40	8	APP_Speed
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	41	8	APP_Average_Speed
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	42	8	APP_TransferedTime
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	43	8	APP_TotalTime
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	44	9	GPRS_TSINFO
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	45	9	GPRS_TSINFO
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	46	9	GPRS_TSINFO
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	47	9	GPRS_TSINFO
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	48	9	GPRS_TSINFO
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	49	9	GPRS_TSINFO
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	50	9	GPRS_TSINFO
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	51	9	GPRS_TSINFO
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	52	9	GPRS_QOS
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	53	9	GPRS_QOS
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	54	9	GPRS_QOS
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	55	9	GPRS_QOS
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	56	9	GPRS_QOS
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	57	9	GPRS_QOS
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	58	9	GPRS_QOS
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	59	9	GPRS_QOS
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	60	9	GPRS_RLCMACStatus
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	61	9	GPRS_RLCMACStatus
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	62	9	GPRS_RLCMACStatus
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	63	9	GPRS_RLCMACStatus
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	64	9	GPRS_RLCMACStatus
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	65	9	GPRS_RLCMACStatus
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	66	9	GPRS_RLCMACStatus
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	67	9	GPRS_RLCMACStatus
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	68	9	PDP_Context
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	69	9	PDP_Context
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	70	9	PDP_Context
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	71	9	PDP_Context
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	72	9	PDP_Context
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	73	9	PDP_Context
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	74	9	PDP_Context
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	75	9	PDP_Context
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	76	9	EDGE_CODEC
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	77	9	EDGE_CODEC
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	78	9	EDGE_CODEC
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	79	9	EDGE_CODEC
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	80	9	EDGE_CODEC
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	81	9	EDGE_CODEC
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	82	9	EDGE_CODEC
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	83	9	EDGE_CODEC
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	84	9	OtherInfoForStat
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	85	9	OtherInfoForStat
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	86	9	OtherInfoForStat
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	87	9	OtherInfoForStat
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	88	9	OtherInfoForStat
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	89	9	OtherInfoForStat
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	90	9	OtherInfoForStat
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	91	9	OtherInfoForStat
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	92	9	reserved
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	93	9	reserved
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	94	9	reserved
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	95	9	reserved
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	96	9	reserved
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	97	9	reserved
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	98	9	reserved
1088	SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL	99	9	reserved
1089	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_SUMMARY	1	8	[ifileid] [int]
1089	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_SUMMARY	2	8	[isampleid] [int]
1089	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_SUMMARY	3	8	[itime] [int]
1089	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_SUMMARY	4	8	[wtimems] [int]
1089	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_SUMMARY	5	8	[bms] [tinyint]
1089	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_SUMMARY	6	8	[ilongitude] [int]
1089	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_SUMMARY	7	8	[ilatitude] [int]
1089	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_SUMMARY	8	8	[SCellgsm_bcch_arfcn] [int]
1089	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_SUMMARY	9	8	[Scellgsm_rxqualsub] [int]
1089	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_SUMMARY	10	8	[Scellgsm_rxlevsub] [int]
1089	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_SUMMARY	11	8	[Scellgsm_lac] [int]
1089	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_SUMMARY	12	8	[SCellgsm_ci] [int]
1089	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_SUMMARY	13	8	[Scellgsm_DSC_Max] [int]
1089	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_SUMMARY	14	8	[PCCPCH_RSCP] [int]
1089	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_SUMMARY	15	8	[PCCPCH_C2I] [int]
1089	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_SUMMARY	16	8	[UARFCN] [int]
1089	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_SUMMARY	17	8	[ScellCPI] [int]
1089	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_SUMMARY	18	8	[LAC] [int]
1089	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_SUMMARY	19	8	[CELLID] [int]
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	1	8	isampleid int 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	2	8	ifileid int 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	3	8	itime int 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	4	8	wtimems int 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	5	8	bms tinyint 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	6	8	ilongitude int 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	7	8	ilatitude int 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	8	8	SCellgsm_bcch_arfcn int 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	9	8	Scellgsm_rxqualfull int 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	10	8	Scellgsm_rxqualsub int 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	11	8	Scellgsm_rxlevfull int 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	12	8	Scellgsm_rxlevsub int 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	13	8	Scellgsm_lac int 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	14	8	SCellgsm_ci int 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	15	8	Scellgsm_txpower int 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	16	8	Scellgsm_ta int 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	17	8	Scellgsm_DSC_Max int 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	18	8	Scellgsm_DSC_Counter int 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	19	8	SCellgsm_c1 int 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	20	8	SCellgsm_c2 int 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	21	8	GPRS_BLER_DL int 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	22	8	GPRS_RLC_UL_Thr int 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	23	8	GPRS_RLC_DL_Thr int 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	24	8	GPRS_RLC_UL_RTX_Rate int 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	25	8	GPRS_RLC_DL_RTX_Rate int 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	26	8	EDGE_UL_MCS int 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	27	8	EDGE_DL_MCS int 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	28	8	PCCPCH_RSCP int 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	29	8	DPCH_RSCP int 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	30	8	DPCH_ISCP int 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	31	8	DPCH_C2I int 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	32	8	PCCPCH_PathLoss int 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	33	8	UTRA_Carrier_RSSI int 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	34	8	TA int 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	35	8	UpPCH_TxPower int 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	36	8	BLER int 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	37	8	UE_TxPower int 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	38	8	PCCPCH_SIR int 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	39	8	PCCPCH_C2I int 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	40	8	UARFCN int 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	41	8	DCH_UARFCN int 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	42	8	FER int 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	43	8	ScellCPI int 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	44	8	UL_RLC_Thr int 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	45	8	DL_RLC_Thr int 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	46	8	UL_PDCP_Thr int 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	47	8	DL_PDCP_Thr int 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	48	8	UL_RLC_RetransmitRate int 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	49	8	DL_RLC_ErrorRate int 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	50	8	TargetDCHSIR int 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	51	8	DCH_SIR int 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	52	8	LAC int 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	53	8	CELLID int 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	54	8	RNC_ID int 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	55	8	APP_type int 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	56	8	APP_Status int 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	57	8	APP_DataStatus_DL int 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	58	8	APP_DataStatus_UL int 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	59	8	APP_TotalSize int 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	60	8	APP_TransferedSize int 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	61	8	APP_Speed int 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	62	8	APP_Average_Speed int 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	63	8	APP_TransferedTime int 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	64	8	APP_TotalTime int 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	65	9	GSM_NeighborInfo image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	66	9	GSM_NeighborInfo image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	67	9	GSM_NeighborInfo image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	68	9	GSM_NeighborInfo image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	69	9	GSM_NeighborInfo image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	70	9	GSM_NeighborInfo image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	71	9	GSM_NeighborInfo image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	72	9	GSM_NeighborInfo image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	73	9	TD_NeighborInfo image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	74	9	TD_NeighborInfo image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	75	9	TD_NeighborInfo image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	76	9	TD_NeighborInfo image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	77	9	TD_NeighborInfo image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	78	9	TD_NeighborInfo image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	79	9	TD_NeighborInfo image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	80	9	TD_NeighborInfo image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	81	9	TSINFO image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	82	9	TSINFO image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	83	9	TSINFO image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	84	9	TSINFO image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	85	9	TSINFO image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	86	9	TSINFO image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	87	9	TSINFO image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	88	9	TSINFO image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	89	9	TD_PhysicalChannelInfo image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	90	9	TD_PhysicalChannelInfo image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	91	9	TD_PhysicalChannelInfo image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	92	9	TD_PhysicalChannelInfo image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	93	9	TD_PhysicalChannelInfo image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	94	9	TD_PhysicalChannelInfo image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	95	9	TD_PhysicalChannelInfo image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	96	9	TD_PhysicalChannelInfo image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	97	9	HSDPA_PhysicalChannelInfo image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	98	9	HSDPA_PhysicalChannelInfo image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	99	9	HSDPA_PhysicalChannelInfo image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	100	9	HSDPA_PhysicalChannelInfo image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	101	9	HSDPA_PhysicalChannelInfo image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	102	9	HSDPA_PhysicalChannelInfo image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	103	9	HSDPA_PhysicalChannelInfo image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	104	9	HSDPA_PhysicalChannelInfo image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	105	9	GPRS_QOS image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	106	9	GPRS_QOS image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	107	9	GPRS_QOS image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	108	9	GPRS_QOS image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	109	9	GPRS_QOS image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	110	9	GPRS_QOS image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	111	9	GPRS_QOS image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	112	9	GPRS_QOS image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	113	9	GPRS_RLCMACStatus image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	114	9	GPRS_RLCMACStatus image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	115	9	GPRS_RLCMACStatus image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	116	9	GPRS_RLCMACStatus image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	117	9	GPRS_RLCMACStatus image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	118	9	GPRS_RLCMACStatus image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	119	9	GPRS_RLCMACStatus image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	120	9	GPRS_RLCMACStatus image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	121	9	PDP_Context image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	122	9	PDP_Context image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	123	9	PDP_Context image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	124	9	PDP_Context image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	125	9	PDP_Context image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	126	9	PDP_Context image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	127	9	PDP_Context image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	128	9	PDP_Context image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	129	9	EDGE_CODEC image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	130	9	EDGE_CODEC image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	131	9	EDGE_CODEC image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	132	9	EDGE_CODEC image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	133	9	EDGE_CODEC image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	134	9	EDGE_CODEC image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	135	9	EDGE_CODEC image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	136	9	EDGE_CODEC image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	137	9	HSDPA_INFO image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	138	9	HSDPA_INFO image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	139	9	HSDPA_INFO image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	140	9	HSDPA_INFO image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	141	9	HSDPA_INFO image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	142	9	HSDPA_INFO image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	143	9	HSDPA_INFO image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	144	9	HSDPA_INFO image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	145	9	HSUPA_INFO image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	146	9	HSUPA_INFO image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	147	9	HSUPA_INFO image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	148	9	HSUPA_INFO image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	149	9	HSUPA_INFO image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	150	9	HSUPA_INFO image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	151	9	HSUPA_INFO image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	152	9	HSUPA_INFO image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	153	9	OtherInfoForStat image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	154	9	OtherInfoForStat image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	155	9	OtherInfoForStat image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	156	9	OtherInfoForStat image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	157	9	OtherInfoForStat image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	158	9	OtherInfoForStat image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	159	9	OtherInfoForStat image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	160	9	OtherInfoForStat image 
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	161	9	reserved image
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	162	9	reserved image
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	163	9	reserved image
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	164	9	reserved image
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	165	9	reserved image
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	166	9	reserved image
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	167	9	reserved image
1090	SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL	168	9	reserved image
1091	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_SUMMARY	1	8	ifileid
1091	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_SUMMARY	2	8	isampleid
1091	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_SUMMARY	3	8	itime
1091	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_SUMMARY	4	8	wtimems
1091	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_SUMMARY	5	8	MsSeq
1091	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_SUMMARY	6	8	Longitude
1091	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_SUMMARY	7	8	Latitude
1091	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_SUMMARY	8	8	Refernce_Ec_Io
1091	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_SUMMARY	9	8	Reference_RSCP
1091	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_SUMMARY	10	8	Reference_PSC
1091	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_SUMMARY	11	8	frequency
1091	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_SUMMARY	12	8	SysCellID
1091	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_SUMMARY	13	8	SysLAI
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	1	8	[ifileid] [int]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	2	8	[isampleid] [int]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	3	8	[itime] [int]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	4	8	[wtimems] [smallint]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	5	8	[MsSeq] [int]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	6	8	[Longitude] [int]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	7	8	[Latitude] [int]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	8	8	[Altitude] [int]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	9	8	[Speed] [int]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	10	8	[Mode] [int]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	11	8	[RxPower] [int]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	12	8	[TxPower] [int]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	13	8	[BLER] [int]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	14	8	[TotalEc_Io] [int]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	15	8	[MaxEc_Io] [int]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	16	8	[Refernce_Ec_Io] [int]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	17	8	[TotalRSCP] [int]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	18	8	[MaxRSCP] [int]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	19	8	[Reference_RSCP] [int]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	20	8	[MaxEc_Io_PSC] [int]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	21	8	[Reference_PSC] [int]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	22	8	[SIR] [int]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	23	8	[Target_SIR] [int]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	24	8	[frequency] [int]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	25	8	[Locked] [int]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	26	8	[SysCellReserved] [int]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	27	8	[SysCellBar] [int]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	28	8	[SysMCC] [int]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	29	8	[SysMNC] [int]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	30	8	[SysCellID] [int]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	31	8	[SysURA_ID] [int]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	32	8	[SysLAI] [int]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	33	8	[SysRAI] [int]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	34	8	[SysSerType] [int]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	35	9	[GSM_Info] [image]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	36	9	[GSM_Info]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	37	9	[GSM_Info]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	38	9	[GSM_Info]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	39	9	[GSM_Info]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	40	9	[GSM_Info]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	41	9	[GSM_Info]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	42	9	[GSM_Info]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	43	9	[GSM_NeighborInfo] [image]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	44	9	[GSM_NeighborInfo]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	45	9	[GSM_NeighborInfo]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	46	9	[GSM_NeighborInfo]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	47	9	[GSM_NeighborInfo]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	48	9	[GSM_NeighborInfo]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	49	9	[GSM_NeighborInfo]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	50	9	[GSM_NeighborInfo]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	51	9	[ServNeighbor_Information] [image]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	52	9	[ServNeighbor_Information]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	53	9	[ServNeighbor_Information]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	54	9	[ServNeighbor_Information]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	55	9	[ServNeighbor_Information]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	56	9	[ServNeighbor_Information]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	57	9	[ServNeighbor_Information]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	58	9	[ServNeighbor_Information]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	59	9	[PESQ_Info] [image]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	60	9	[PESQ_Info]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	61	9	[PESQ_Info]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	62	9	[PESQ_Info]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	63	9	[PESQ_Info]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	64	9	[PESQ_Info]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	65	9	[PESQ_Info]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	66	9	[PESQ_Info]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	67	9	[NeighborSet] [image]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	68	9	[NeighborSet] 
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	69	9	[NeighborSet] 
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	70	9	[NeighborSet] 
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	71	9	[NeighborSet] 
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	72	9	[NeighborSet] 
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	73	9	[NeighborSet] 
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	74	9	[NeighborSet] 
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	75	9	[UMTS_C2I] [image]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	76	9	[UMTS_C2I]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	77	9	[UMTS_C3I]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	78	9	[UMTS_C4I]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	79	9	[UMTS_C5I]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	80	9	[UMTS_C6I]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	81	9	[UMTS_C7I]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	82	9	[UMTS_C8I]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	83	9	[BLER_Information] [image]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	84	9	[BLER_Information]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	85	9	[BLER_Information]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	86	9	[BLER_Information]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	87	9	[BLER_Information]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	88	9	[BLER_Information]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	89	9	[BLER_Information]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	90	9	[BLER_Information]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	91	9	[Finger_Information] [image]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	92	9	[Finger_Information]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	93	9	[Finger_Information]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	94	9	[Finger_Information]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	95	9	[Finger_Information]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	96	9	[Finger_Information]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	97	9	[Finger_Information]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	98	9	[Finger_Information]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	99	9	[ActiveSet] [image]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	100	9	[ActiveSet] 
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	101	9	[ActiveSet] 
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	102	9	[ActiveSet] 
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	103	9	[ActiveSet] 
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	104	9	[ActiveSet] 
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	105	9	[ActiveSet] 
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	106	9	[ActiveSet] 
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	107	9	[ActiveSetLayer1] [image]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	108	9	[ActiveSetLayer1]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	109	9	[ActiveSetLayer2]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	110	9	[ActiveSetLayer3]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	111	9	[ActiveSetLayer4]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	112	9	[ActiveSetLayer5]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	113	9	[ActiveSetLayer6]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	114	9	[ActiveSetLayer7]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	115	9	[PRACH_Information] [image]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	116	9	[PRACH_Information]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	117	9	[PRACH_Information]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	118	9	[PRACH_Information]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	119	9	[PRACH_Information]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	120	9	[PRACH_Information]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	121	9	[PRACH_Information]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	122	9	[PRACH_Information]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	123	9	[RACH_Information] [image]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	124	9	[RACH_Information]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	125	9	[RACH_Information]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	126	9	[RACH_Information]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	127	9	[RACH_Information]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	128	9	[RACH_Information]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	129	9	[RACH_Information]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	130	9	[RACH_Information]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	131	9	[State_Information] [image]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	132	9	[State_Information]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	133	9	[State_Information]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	134	9	[State_Information]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	135	9	[State_Information]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	136	9	[State_Information]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	137	9	[State_Information]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	138	9	[State_Information]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	139	9	[Downlink_PDUSDU] [image]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	140	9	[Downlink_PDUSDU]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	141	9	[Downlink_PDUSDU]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	142	9	[Downlink_PDUSDU]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	143	9	[Downlink_PDUSDU]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	144	9	[Downlink_PDUSDU]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	145	9	[Downlink_PDUSDU]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	146	9	[Downlink_PDUSDU]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	147	9	[Uplink_PDUSDU] [image]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	148	9	[Uplink_PDUSDU]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	149	9	[Uplink_PDUSDU]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	150	9	[Uplink_PDUSDU]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	151	9	[Uplink_PDUSDU]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	152	9	[Uplink_PDUSDU]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	153	9	[Uplink_PDUSDU]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	154	9	[Uplink_PDUSDU]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	155	9	[GPRS_QOS] [image]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	156	9	[GPRS_QOS]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	157	9	[GPRS_QOS]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	158	9	[GPRS_QOS]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	159	9	[GPRS_QOS]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	160	9	[GPRS_QOS]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	161	9	[GPRS_QOS]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	162	9	[GPRS_QOS]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	163	9	[GPRS_RLCMACStatus] [image]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	164	9	[GPRS_RLCMACStatus]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	165	9	[GPRS_RLCMACStatus]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	166	9	[GPRS_RLCMACStatus]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	167	9	[GPRS_RLCMACStatus]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	168	9	[GPRS_RLCMACStatus]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	169	9	[GPRS_RLCMACStatus]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	170	9	[GPRS_RLCMACStatus]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	171	9	[PDP_Context] [image]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	172	9	[PDP_Context]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	173	9	[PDP_Context]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	174	9	[PDP_Context]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	175	9	[PDP_Context]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	176	9	[PDP_Context]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	177	9	[PDP_Context]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	178	9	[PDP_Context]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	179	9	[EDGE_CODEC] [image]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	180	9	[EDGE_CODEC]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	181	9	[EDGE_CODEC]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	182	9	[EDGE_CODEC]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	183	9	[EDGE_CODEC]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	184	9	[EDGE_CODEC]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	185	9	[EDGE_CODEC]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	186	9	[EDGE_CODEC]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	187	9	[APP_Info] [image]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	188	9	[APP_Info]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	189	9	[APP_Info]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	190	9	[APP_Info]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	191	9	[APP_Info]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	192	9	[APP_Info]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	193	9	[APP_Info]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	194	9	[APP_Info]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	195	9	[HSdpaPar] [image]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	196	9	[HSdpaPar]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	197	9	[HSdpaPar]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	198	9	[HSdpaPar]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	199	9	[HSdpaPar]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	200	9	[HSdpaPar]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	201	9	[HSdpaPar]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	202	9	[HSdpaPar]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	203	9	[HSupaPar] [image]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	204	9	[HSupaPar]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	205	9	[HSupaPar]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	206	9	[HSupaPar]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	207	9	[HSupaPar]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	208	9	[HSupaPar]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	209	9	[HSupaPar]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	210	9	[HSupaPar]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	211	9	[Reserved] [image]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	212	9	[Reserved]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	213	9	[Reserved]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	214	9	[Reserved]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	215	9	[Reserved]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	216	9	[Reserved]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	217	9	[Reserved]
1092	SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL	218	9	[Reserved]
1093	SEARCHTYPE_CELL_COVER_DISTANCE	1	8	ifileid int
1093	SEARCHTYPE_CELL_COVER_DISTANCE	2	8	ilac int
1093	SEARCHTYPE_CELL_COVER_DISTANCE	3	8	ici int
1093	SEARCHTYPE_CELL_COVER_DISTANCE	4	8	idistance int
1093	SEARCHTYPE_CELL_COVER_DISTANCE	5	8	iduration int
1093	SEARCHTYPE_CELL_COVER_DISTANCE	6	8	itype int
1094	SEARCHTYPE_ES_EVENT_REPORT	1	8	ifileid int 
1094	SEARCHTYPE_ES_EVENT_REPORT	2	8	iprojecttype int 
1094	SEARCHTYPE_ES_EVENT_REPORT	3	8	iseqid int 
1094	SEARCHTYPE_ES_EVENT_REPORT	4	8	itime int 
1094	SEARCHTYPE_ES_EVENT_REPORT	5	8	wtimems smallint 
1094	SEARCHTYPE_ES_EVENT_REPORT	6	8	bms tinyint 
1094	SEARCHTYPE_ES_EVENT_REPORT	7	8	iEventID int 
1094	SEARCHTYPE_ES_EVENT_REPORT	8	8	ilongitude int
1094	SEARCHTYPE_ES_EVENT_REPORT	9	8	ilatitude int
1094	SEARCHTYPE_ES_EVENT_REPORT	10	8	icqtposid int
1094	SEARCHTYPE_ES_EVENT_REPORT	11	8	iLAC int
1094	SEARCHTYPE_ES_EVENT_REPORT	12	8	wRAC smallint
1094	SEARCHTYPE_ES_EVENT_REPORT	13	8	iCI int
1094	SEARCHTYPE_ES_EVENT_REPORT	14	8	iTargetLAC int
1094	SEARCHTYPE_ES_EVENT_REPORT	15	8	wTargetRAC smallint
1094	SEARCHTYPE_ES_EVENT_REPORT	16	8	iTargetCI int
1094	SEARCHTYPE_ES_EVENT_REPORT	17	8	ianatime int 
1094	SEARCHTYPE_ES_EVENT_REPORT	18	6	pretype_desc varchar(2000)
1094	SEARCHTYPE_ES_EVENT_REPORT	19	6	pretype_desc
1094	SEARCHTYPE_ES_EVENT_REPORT	20	6	pretype_desc
1094	SEARCHTYPE_ES_EVENT_REPORT	21	6	pretype_desc
1094	SEARCHTYPE_ES_EVENT_REPORT	22	6	pretype_desc
1094	SEARCHTYPE_ES_EVENT_REPORT	23	6	pretype_desc
1094	SEARCHTYPE_ES_EVENT_REPORT	24	6	pretype_desc
1094	SEARCHTYPE_ES_EVENT_REPORT	25	6	pretype_desc
1094	SEARCHTYPE_ES_EVENT_REPORT	26	6	reason_desc varchar(2000)
1094	SEARCHTYPE_ES_EVENT_REPORT	27	6	reason_desc
1094	SEARCHTYPE_ES_EVENT_REPORT	28	6	reason_desc
1094	SEARCHTYPE_ES_EVENT_REPORT	29	6	reason_desc
1094	SEARCHTYPE_ES_EVENT_REPORT	30	6	reason_desc
1094	SEARCHTYPE_ES_EVENT_REPORT	31	6	reason_desc
1094	SEARCHTYPE_ES_EVENT_REPORT	32	6	reason_desc
1094	SEARCHTYPE_ES_EVENT_REPORT	33	6	reason_desc
1094	SEARCHTYPE_ES_EVENT_REPORT	34	8	iimporttime int 
1094	SEARCHTYPE_ES_EVENT_REPORT	35	6	cellname varchar(50)
1094	SEARCHTYPE_ES_EVENT_REPORT	36	6	areaname varchar(50)
1094	SEARCHTYPE_ES_EVENT_REPORT	37	6	roadname varchar(200)
1094	SEARCHTYPE_ES_EVENT_REPORT	38	6	strfilename varchar(255)
1094	SEARCHTYPE_ES_EVENT_REPORT	39	6	strsavepath varchar(255)
1094	SEARCHTYPE_ES_EVENT_REPORT	40	6	strmethod varchar(255)
1094	SEARCHTYPE_ES_EVENT_REPORT	41	6	strsolution varchar(2000)
1094	SEARCHTYPE_ES_EVENT_REPORT	42	6	strsolution
1094	SEARCHTYPE_ES_EVENT_REPORT	43	6	strsolution
1094	SEARCHTYPE_ES_EVENT_REPORT	44	6	strsolution
1094	SEARCHTYPE_ES_EVENT_REPORT	45	6	strsolution
1094	SEARCHTYPE_ES_EVENT_REPORT	46	6	strsolution
1094	SEARCHTYPE_ES_EVENT_REPORT	47	6	strsolution
1094	SEARCHTYPE_ES_EVENT_REPORT	48	6	strsolution
1094	SEARCHTYPE_ES_EVENT_REPORT	49	6	strdwusername varchar(50)
1094	SEARCHTYPE_ES_EVENT_REPORT	50	8	itimedw int
1094	SEARCHTYPE_ES_EVENT_REPORT	51	6	strnote varchar(2000)
1094	SEARCHTYPE_ES_EVENT_REPORT	52	6	strnote
1094	SEARCHTYPE_ES_EVENT_REPORT	53	6	strnote
1094	SEARCHTYPE_ES_EVENT_REPORT	54	6	strnote
1094	SEARCHTYPE_ES_EVENT_REPORT	55	6	strnote
1094	SEARCHTYPE_ES_EVENT_REPORT	56	6	strnote
1094	SEARCHTYPE_ES_EVENT_REPORT	57	6	strnote
1094	SEARCHTYPE_ES_EVENT_REPORT	58	6	strnote
1094	SEARCHTYPE_ES_EVENT_REPORT	59	6	strauditor varchar(50)
1094	SEARCHTYPE_ES_EVENT_REPORT	60	8	itimeaudit int
1094	SEARCHTYPE_ES_EVENT_REPORT	61	8	status int 
1094	SEARCHTYPE_ES_EVENT_REPORT	62	6	strbak varchar(2000)
1094	SEARCHTYPE_ES_EVENT_REPORT	63	6	strbak
1094	SEARCHTYPE_ES_EVENT_REPORT	64	6	strbak
1094	SEARCHTYPE_ES_EVENT_REPORT	65	6	strbak
1094	SEARCHTYPE_ES_EVENT_REPORT	66	6	strbak
1094	SEARCHTYPE_ES_EVENT_REPORT	67	6	strbak
1094	SEARCHTYPE_ES_EVENT_REPORT	68	6	strbak
1094	SEARCHTYPE_ES_EVENT_REPORT	69	6	strbak
1094	SEARCHTYPE_ES_EVENT_REPORT	70	6	stropteffect
1094	SEARCHTYPE_ES_EVENT_REPORT	71	8	iservicetype int
1094	SEARCHTYPE_ES_EVENT_REPORT	72	6	strsampletbname varchar(255)
1095	SEARCHTYPE_ES_EVENT_AHEAD_TIME	1	8	itime int
1095	SEARCHTYPE_ES_EVENT_AHEAD_TIME	2	8	wtimems int
1096	SEARCHTYPE_ES_EVENT_MSG	1	8	itime int
1096	SEARCHTYPE_ES_EVENT_MSG	2	8	wtimems int
1096	SEARCHTYPE_ES_EVENT_MSG	3	9	hexcode ox
1097	SEARCHTYPE_CELLSIMU_CELL_GRID	1	5	rxlev_mean float
1097	SEARCHTYPE_CELLSIMU_CELL_GRID	2	8	ltlongitude int
1097	SEARCHTYPE_CELLSIMU_CELL_GRID	3	8	ltlatitude int
1098	SEARCHTYPE_CELL_OUTSERVICE_INFO	1	6	district_name varchar(255),
1098	SEARCHTYPE_CELL_OUTSERVICE_INFO	2	6	ne_name varchar(255),
1098	SEARCHTYPE_CELL_OUTSERVICE_INFO	3	6	object_name varchar(255),
1098	SEARCHTYPE_CELL_OUTSERVICE_INFO	4	6	alarm_name varchar(255),
1098	SEARCHTYPE_CELL_OUTSERVICE_INFO	5	6	alarm_level varchar(255),
1098	SEARCHTYPE_CELL_OUTSERVICE_INFO	6	8	begin_time int not null,
1098	SEARCHTYPE_CELL_OUTSERVICE_INFO	7	8	end_time int,
1098	SEARCHTYPE_CELL_OUTSERVICE_INFO	8	6	description varchar(1000),
1098	SEARCHTYPE_CELL_OUTSERVICE_INFO	9	6	description varchar
1098	SEARCHTYPE_CELL_OUTSERVICE_INFO	10	6	description varchar
1098	SEARCHTYPE_CELL_OUTSERVICE_INFO	11	6	description varchar
1098	SEARCHTYPE_CELL_OUTSERVICE_INFO	12	6	description varchar
1098	SEARCHTYPE_CELL_OUTSERVICE_INFO	13	6	description varchar
1098	SEARCHTYPE_CELL_OUTSERVICE_INFO	14	6	description varchar
1098	SEARCHTYPE_CELL_OUTSERVICE_INFO	15	6	description varchar
1098	SEARCHTYPE_CELL_OUTSERVICE_INFO	16	8	cell_id int not null,
1098	SEARCHTYPE_CELL_OUTSERVICE_INFO	17	8	alarm_id int not null
1098	SEARCHTYPE_CELL_OUTSERVICE_INFO	18	8	type int
1099	SEARCHTYPE_ES_GRID_CELL	1	6	strcellname
1099	SEARCHTYPE_ES_GRID_CELL	2	5	rxlev_mean
1110	STATTYPE_NOGIS_V_GSM_IDLE	1	8	ifileid int
1110	STATTYPE_NOGIS_V_GSM_IDLE	2	8	iareatype int 
1110	STATTYPE_NOGIS_V_GSM_IDLE	3	8	iareaid int 
1110	STATTYPE_NOGIS_V_GSM_IDLE	4	8	文件开始时间
1110	STATTYPE_NOGIS_V_GSM_IDLE	5	8	测试里程
1110	STATTYPE_NOGIS_V_GSM_IDLE	6	8	测试时长
1110	STATTYPE_NOGIS_V_GSM_IDLE	7	8	[iLAC1][int]NULL
1110	STATTYPE_NOGIS_V_GSM_IDLE	8	2	[wRAC1][smallint]NULL
1110	STATTYPE_NOGIS_V_GSM_IDLE	9	8	[iCI1][int]NULL
1110	STATTYPE_NOGIS_V_GSM_IDLE	10	8	[isamplenum1][int]NULL
1110	STATTYPE_NOGIS_V_GSM_IDLE	11	8	[irxlevmean1][int]NULL
1110	STATTYPE_NOGIS_V_GSM_IDLE	12	8	[iLAC2][int]NULL
1110	STATTYPE_NOGIS_V_GSM_IDLE	13	2	[wRAC2][smallint]NULL
1110	STATTYPE_NOGIS_V_GSM_IDLE	14	8	[iCI2][int]NULL
1110	STATTYPE_NOGIS_V_GSM_IDLE	15	8	[isamplenum2][int]NULL
1110	STATTYPE_NOGIS_V_GSM_IDLE	16	8	[irxlevmean2][int]NULL
1110	STATTYPE_NOGIS_V_GSM_IDLE	17	8	[iLAC3][int]NULL
1110	STATTYPE_NOGIS_V_GSM_IDLE	18	2	[wRAC3][smallint]NULL
1110	STATTYPE_NOGIS_V_GSM_IDLE	19	8	[iCI3][int]NULL
1110	STATTYPE_NOGIS_V_GSM_IDLE	20	8	[isamplenum3][int]NULL
1110	STATTYPE_NOGIS_V_GSM_IDLE	21	8	[irxlevmean3][int]NULL
1110	STATTYPE_NOGIS_V_GSM_IDLE	22	8	[iLAC4][int]NULL
1110	STATTYPE_NOGIS_V_GSM_IDLE	23	2	[wRAC4][smallint]NULL
1110	STATTYPE_NOGIS_V_GSM_IDLE	24	8	[iCI4][int]NULL
1110	STATTYPE_NOGIS_V_GSM_IDLE	25	8	[isamplenum4][int]NULL
1110	STATTYPE_NOGIS_V_GSM_IDLE	26	8	[irxlevmean4][int]NULL
1110	STATTYPE_NOGIS_V_GSM_IDLE	27	8	[isamplenum][int]NULL
1110	STATTYPE_NOGIS_V_GSM_IDLE	28	8	[Rxlev_10_45][int]NULL
1110	STATTYPE_NOGIS_V_GSM_IDLE	29	8	[Rxlev_46_50][int]NULL
1110	STATTYPE_NOGIS_V_GSM_IDLE	30	8	[Rxlev_51_55][int]NULL
1110	STATTYPE_NOGIS_V_GSM_IDLE	31	8	[Rxlev_56_60][int]NULL
1110	STATTYPE_NOGIS_V_GSM_IDLE	32	8	[Rxlev_61_65][int]NULL
1110	STATTYPE_NOGIS_V_GSM_IDLE	33	8	[Rxlev_66_70][int]NULL
1110	STATTYPE_NOGIS_V_GSM_IDLE	34	8	[Rxlev_71_75][int]NULL
1110	STATTYPE_NOGIS_V_GSM_IDLE	35	8	[Rxlev_76_80][int]NULL
1110	STATTYPE_NOGIS_V_GSM_IDLE	36	8	[Rxlev_81][int]NULL
1110	STATTYPE_NOGIS_V_GSM_IDLE	37	8	[Rxlev_82][int]NULL
1110	STATTYPE_NOGIS_V_GSM_IDLE	38	8	[Rxlev_83][int]NULL
1110	STATTYPE_NOGIS_V_GSM_IDLE	39	8	[Rxlev_84][int]NULL
1110	STATTYPE_NOGIS_V_GSM_IDLE	40	8	[Rxlev_85][int]NULL
1110	STATTYPE_NOGIS_V_GSM_IDLE	41	8	[Rxlev_86][int]NULL
1110	STATTYPE_NOGIS_V_GSM_IDLE	42	8	[Rxlev_87][int]NULL
1110	STATTYPE_NOGIS_V_GSM_IDLE	43	8	[Rxlev_88][int]NULL
1110	STATTYPE_NOGIS_V_GSM_IDLE	44	8	[Rxlev_89][int]NULL
1110	STATTYPE_NOGIS_V_GSM_IDLE	45	8	[Rxlev_90][int]NULL
1110	STATTYPE_NOGIS_V_GSM_IDLE	46	8	[Rxlev_91][int]NULL
1110	STATTYPE_NOGIS_V_GSM_IDLE	47	8	[Rxlev_92][int]NULL
1110	STATTYPE_NOGIS_V_GSM_IDLE	48	8	[Rxlev_93][int]NULL
1110	STATTYPE_NOGIS_V_GSM_IDLE	49	8	[Rxlev_94][int]NULL
1110	STATTYPE_NOGIS_V_GSM_IDLE	50	8	[Rxlev_95][int]NULL
1110	STATTYPE_NOGIS_V_GSM_IDLE	51	8	[Rxlev_96][int]NULL
1110	STATTYPE_NOGIS_V_GSM_IDLE	52	8	[Rxlev_97][int]NULL
1110	STATTYPE_NOGIS_V_GSM_IDLE	53	8	[Rxlev_98][int]NULL
1110	STATTYPE_NOGIS_V_GSM_IDLE	54	8	[Rxlev_99_120][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	1	8	ifileid int
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	2	8	iareatype int 
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	3	8	iareaid int 
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	4	8	文件开始时间
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	5	8	测试里程
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	6	8	测试时长
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	7	8	[iLAC1][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	8	2	[wRAC1][smallint]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	9	8	[iCI1][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	10	8	[isamplenum1][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	11	8	[irxlevmean1][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	12	8	[iLAC2][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	13	2	[wRAC2][smallint]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	14	8	[iCI2][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	15	8	[isamplenum2][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	16	8	[irxlevmean2][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	17	8	[iLAC3][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	18	2	[wRAC3][smallint]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	19	8	[iCI3][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	20	8	[isamplenum3][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	21	8	[irxlevmean3][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	22	8	[iLAC4][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	23	2	[wRAC4][smallint]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	24	8	[iCI4][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	25	8	[isamplenum4][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	26	8	[irxlevmean4][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	27	8	[isamplenum][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	28	8	[Rxlev_10_45][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	29	8	[Rxlev_46_50][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	30	8	[Rxlev_51_55][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	31	8	[Rxlev_56_60][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	32	8	[Rxlev_61_65][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	33	8	[Rxlev_66_70][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	34	8	[Rxlev_71_75][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	35	8	[Rxlev_76_80][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	36	8	[Rxlev_81][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	37	8	[Rxlev_82][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	38	8	[Rxlev_83][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	39	8	[Rxlev_84][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	40	8	[Rxlev_85][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	41	8	[Rxlev_86][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	42	8	[Rxlev_87][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	43	8	[Rxlev_88][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	44	8	[Rxlev_89][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	45	8	[Rxlev_90][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	46	8	[Rxlev_91][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	47	8	[Rxlev_92][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	48	8	[Rxlev_93][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	49	8	[Rxlev_94][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	50	8	[Rxlev_95][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	51	8	[Rxlev_96][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	52	8	[Rxlev_97][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	53	8	[Rxlev_98][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	54	8	[Rxlev_99_120][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	55	8	[RxQual0][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	56	8	[RxQual1][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	57	8	[RxQual2][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	58	8	[RxQual3][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	59	8	[RxQual4][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	60	8	[RxQual5][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	61	8	[RxQual6][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	62	8	[RxQual7][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	63	8	[PESQ_0_10][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	64	8	[PESQ_10_20][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	65	8	[PESQ_20_25][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	66	8	[PESQ_25_28][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	67	8	[PESQ_28_30][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	68	8	[PESQ_30_33][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	69	8	[PESQ_33_34][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	70	8	[PESQ_45][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	71	5	[PESQ_Value_0_10][float]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	72	5	[PESQ_Value_10_20][float]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	73	5	[PESQ_Value_20_25][float]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	74	5	[PESQ_Value_25_28][float]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	75	5	[PESQ_Value_28_30][float]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	76	5	[PESQ_Value_30_33][float]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	77	5	[PESQ_Value_30_34][float]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	78	5	[PESQ_Value_45][float]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	79	8	[TA0][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	80	8	[TA1][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	81	8	[TA2][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	82	8	[TA3][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	83	8	[TA4][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	84	8	[TA5][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	85	8	[TA6][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	86	8	[TA7][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	87	8	[TA8][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	88	8	[TA9][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	89	8	[TA10][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	90	8	[TA_11_15][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	91	8	[TA_16_20][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	92	8	[TA_21_30][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	93	8	[TA_31_40][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	94	8	[TA_41_64][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	95	8	[TA_value_11_15][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	96	8	[TA_value_16_20][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	97	8	[TA_value_21_30][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	98	8	[TA_value_31_40][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	99	8	[TA_value_41_64][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	100	8	[SpeechCodec_FR][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	101	8	[SpeechCodec_HR][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	102	8	[SpeechCodec_EFR][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	103	8	[SpeechCodec_AMR][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	104	8	[SpeechCodecTime_FR][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	105	8	[SpeechCodecTime_HR][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	106	8	[SpeechCodecTime_EFR][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	107	8	[SpeechCodecTime_AMR][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	108	8	[C_Iworst_5_9][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	109	8	[C_Iworst_9_12][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	110	8	[C_Iworst_12_15][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	111	8	[C_Iworst_15_20][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	112	8	[C_Iworst_20_35][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	113	5	[C_Iworst_value_5_9][float]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	114	5	[C_Iworst_value_9_12][float]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	115	5	[C_Iworst_value_12_15][float]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	116	5	[C_Iworst_value_15_20][float]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	117	5	[C_Iworst_value_20_35][float]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	118	8	[C_Iavg_5_9][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	119	8	[C_Iavg_9_12][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	120	8	[C_Iavg_12_15][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	121	8	[C_Iavg_15_20][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	122	8	[C_Iavg_20_35][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	123	5	[C_Iavg_value_5_9][float]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	124	5	[C_Iavg_value_9_12][float]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	125	5	[C_Iavg_value_12_15][float]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	126	5	[C_Iavg_value_15_20][float]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	127	5	[C_Iavg_value_20_35][float]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	128	8	[SQI_20_10][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	129	8	[SQI_10_0][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	130	8	[SQI_0_10][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	131	8	[SQI_10_20][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	132	8	[SQI_20_30][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	133	8	[SQIValue_20_10][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	134	8	[SQIValue_10_0][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	135	8	[SQIValue_0_10][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	136	8	[SQIValue_10_20][int]NULL
1111	STATTYPE_NOGIS_V_GSM_DEDICATED	137	8	[SQIValue_20_30][int]NULL
1112	STATTYPE_AREA_DATA	1	8	ifileid int
1112	STATTYPE_AREA_DATA	2	8	iareatype int 
1112	STATTYPE_AREA_DATA	3	8	iareaid int 
1112	STATTYPE_AREA_DATA	4	8	文件开始时间
1112	STATTYPE_AREA_DATA	5	8	测试里程
1112	STATTYPE_AREA_DATA	6	8	测试时长
1112	STATTYPE_AREA_DATA	7	8	isamplenum int 
1112	STATTYPE_AREA_DATA	8	8	iLAC1 int
1112	STATTYPE_AREA_DATA	9	8	wRAC1 int
1112	STATTYPE_AREA_DATA	10	8	iCI1 int
1112	STATTYPE_AREA_DATA	11	8	iLAC2 int
1112	STATTYPE_AREA_DATA	12	8	wRAC2 int
1112	STATTYPE_AREA_DATA	13	8	iCI2 int
1112	STATTYPE_AREA_DATA	14	8	iLAC3 int
1112	STATTYPE_AREA_DATA	15	8	wRAC3 int
1112	STATTYPE_AREA_DATA	16	8	iCI3 int
1112	STATTYPE_AREA_DATA	17	8	iLAC4 int
1112	STATTYPE_AREA_DATA	18	8	wRAC4 int
1112	STATTYPE_AREA_DATA	19	8	iCI4 int
1112	STATTYPE_AREA_DATA	20	8	iduration_GPRS int
1112	STATTYPE_AREA_DATA	21	8	iduration_EDGE int
1112	STATTYPE_AREA_DATA	22	8	iduration_3G int
1112	STATTYPE_AREA_DATA	23	8	idistance_GPRS int
1112	STATTYPE_AREA_DATA	24	8	idistance_EDGE int
1112	STATTYPE_AREA_DATA	25	8	idistance_3G int
1112	STATTYPE_AREA_DATA	26	9	CELLSTAT_INFO1 image
1112	STATTYPE_AREA_DATA	27	9	CELLSTAT_INFO2 image
1112	STATTYPE_AREA_DATA	28	9	CELLSTAT_INFO3 image
1112	STATTYPE_AREA_DATA	29	9	CELLSTAT_INFO4 image
1112	STATTYPE_AREA_DATA	30	9	CELLSTAT_INFO5 image
1112	STATTYPE_AREA_DATA	31	9	CELLSTAT_INFO6 image
1112	STATTYPE_AREA_DATA	32	9	CELLSTAT_INFO7 image
1112	STATTYPE_AREA_DATA	33	9	CELLSTAT_INFO8 image
1112	STATTYPE_AREA_DATA	34	9	RSCP_INFO1 image
1112	STATTYPE_AREA_DATA	35	9	RSCP_INFO2 image
1112	STATTYPE_AREA_DATA	36	9	RSCP_INFO3 image
1112	STATTYPE_AREA_DATA	37	9	RSCP_INFO4 image
1112	STATTYPE_AREA_DATA	38	9	RSCP_INFO5 image
1112	STATTYPE_AREA_DATA	39	9	RSCP_INFO6 image
1112	STATTYPE_AREA_DATA	40	9	RSCP_INFO7 image
1112	STATTYPE_AREA_DATA	41	9	RSCP_INFO8 image
1112	STATTYPE_AREA_DATA	42	9	RLC_INFO1 image
1112	STATTYPE_AREA_DATA	43	9	RLC_INFO2 image
1112	STATTYPE_AREA_DATA	44	9	RLC_INFO3 image
1112	STATTYPE_AREA_DATA	45	9	RLC_INFO4 image
1112	STATTYPE_AREA_DATA	46	9	RLC_INFO5 image
1112	STATTYPE_AREA_DATA	47	9	RLC_INFO6 image
1112	STATTYPE_AREA_DATA	48	9	RLC_INFO7 image
1112	STATTYPE_AREA_DATA	49	9	RLC_INFO8 image
1112	STATTYPE_AREA_DATA	50	9	BLER_INFO1 image
1112	STATTYPE_AREA_DATA	51	9	BLER_INFO2 image
1112	STATTYPE_AREA_DATA	52	9	BLER_INFO3 image
1112	STATTYPE_AREA_DATA	53	9	BLER_INFO4 image
1112	STATTYPE_AREA_DATA	54	9	BLER_INFO5 image
1112	STATTYPE_AREA_DATA	55	9	BLER_INFO6 image
1112	STATTYPE_AREA_DATA	56	9	BLER_INFO7 image
1112	STATTYPE_AREA_DATA	57	9	BLER_INFO8 image
1112	STATTYPE_AREA_DATA	58	9	APP_INFO1 image
1112	STATTYPE_AREA_DATA	59	9	APP_INFO2
1112	STATTYPE_AREA_DATA	60	9	APP_INFO3
1112	STATTYPE_AREA_DATA	61	9	APP_INFO4
1112	STATTYPE_AREA_DATA	62	9	APP_INFO5
1112	STATTYPE_AREA_DATA	63	9	APP_INFO6
1112	STATTYPE_AREA_DATA	64	9	APP_INFO7
1112	STATTYPE_AREA_DATA	65	9	APP_INFO8
1112	STATTYPE_AREA_DATA	66	9	FTP_SLOT_INFO1 image
1112	STATTYPE_AREA_DATA	67	9	FTP_SLOT_INFO2
1112	STATTYPE_AREA_DATA	68	9	FTP_SLOT_INFO3
1112	STATTYPE_AREA_DATA	69	9	FTP_SLOT_INFO4
1112	STATTYPE_AREA_DATA	70	9	FTP_SLOT_INFO5
1112	STATTYPE_AREA_DATA	71	9	FTP_SLOT_INFO6
1112	STATTYPE_AREA_DATA	72	9	FTP_SLOT_INFO7
1112	STATTYPE_AREA_DATA	73	9	FTP_SLOT_INFO8
1112	STATTYPE_AREA_DATA	74	9	MCS_INFO1 image
1112	STATTYPE_AREA_DATA	75	9	MCS_INFO2 
1112	STATTYPE_AREA_DATA	76	9	MCS_INFO3 
1112	STATTYPE_AREA_DATA	77	9	MCS_INFO4
1112	STATTYPE_AREA_DATA	78	9	MCS_INFO5
1112	STATTYPE_AREA_DATA	79	9	MCS_INFO6
1112	STATTYPE_AREA_DATA	80	9	MCS_INFO7
1112	STATTYPE_AREA_DATA	81	9	MCS_INFO8
1112	STATTYPE_AREA_DATA	82	9	RESERVED_INFO1 image
1112	STATTYPE_AREA_DATA	83	9	RESERVED_INFO2
1112	STATTYPE_AREA_DATA	84	9	RESERVED_INFO3
1112	STATTYPE_AREA_DATA	85	9	RESERVED_INFO4
1112	STATTYPE_AREA_DATA	86	9	RESERVED_INFO5
1112	STATTYPE_AREA_DATA	87	9	RESERVED_INFO6
1112	STATTYPE_AREA_DATA	88	9	RESERVED_INFO7
1112	STATTYPE_AREA_DATA	89	9	RESERVED_INFO8
1113	STATTYPE_NOGIS_V_GSM_EVENT	1	8	ifileid int
1113	STATTYPE_NOGIS_V_GSM_EVENT	2	8	iareatype int 
1113	STATTYPE_NOGIS_V_GSM_EVENT	3	8	iareaid int 
1113	STATTYPE_NOGIS_V_GSM_EVENT	4	8	弱覆盖事件个数
1113	STATTYPE_NOGIS_V_GSM_EVENT	5	8	试呼事件次数
1113	STATTYPE_NOGIS_V_GSM_EVENT	6	8	主叫接通事件次数
1113	STATTYPE_NOGIS_V_GSM_EVENT	7	8	被叫接通事件次数
1113	STATTYPE_NOGIS_V_GSM_EVENT	8	8	主叫掉话事件次数
1113	STATTYPE_NOGIS_V_GSM_EVENT	9	8	被叫掉话事件次数
1113	STATTYPE_NOGIS_V_GSM_EVENT	10	8	主叫振铃次数
1113	STATTYPE_NOGIS_V_GSM_EVENT	11	8	被叫振铃次数
1113	STATTYPE_NOGIS_V_GSM_EVENT	12	8	主叫接通时长（用户体验）
1113	STATTYPE_NOGIS_V_GSM_EVENT	13	8	被叫接通时长（用户体验）
1113	STATTYPE_NOGIS_V_GSM_EVENT	14	8	主叫接通时长（网络实际）
1113	STATTYPE_NOGIS_V_GSM_EVENT	15	8	被叫接通时长（网络实际）
1113	STATTYPE_NOGIS_V_GSM_EVENT	16	8	切换请求事件个数
1113	STATTYPE_NOGIS_V_GSM_EVENT	17	8	切换成功事件个数
1113	STATTYPE_NOGIS_V_GSM_EVENT	18	8	切换失败事件个数
1113	STATTYPE_NOGIS_V_GSM_EVENT	19	8	主被叫同时掉话事件次数
1113	STATTYPE_NOGIS_V_GSM_EVENT	20	8	弱质量事件次数
1114	STATTYPE_NOGIS_D_GPRS_EVENT	1	8	ifileid int
1114	STATTYPE_NOGIS_D_GPRS_EVENT	2	8	iareatype int 
1114	STATTYPE_NOGIS_D_GPRS_EVENT	3	8	iareaid int 
1114	STATTYPE_NOGIS_D_GPRS_EVENT	4	8	路由区更新请求次数
1114	STATTYPE_NOGIS_D_GPRS_EVENT	5	8	路由区更新成功次数
1114	STATTYPE_NOGIS_D_GPRS_EVENT	6	8	路由区更新失败次数
1114	STATTYPE_NOGIS_D_GPRS_EVENT	7	8	小区重选事件次数
1114	STATTYPE_NOGIS_D_GPRS_EVENT	8	8	PDP激活次数
1114	STATTYPE_NOGIS_D_GPRS_EVENT	9	8	PDP激活成功次数
1114	STATTYPE_NOGIS_D_GPRS_EVENT	10	8	PDP激活失败次数
1114	STATTYPE_NOGIS_D_GPRS_EVENT	11	8	PDP去激活次数
1114	STATTYPE_NOGIS_D_GPRS_EVENT	12	8	PDP去激活成功次数
1114	STATTYPE_NOGIS_D_GPRS_EVENT	13	8	PDP去激活失败次数
1114	STATTYPE_NOGIS_D_GPRS_EVENT	14	8	掉线次数
1114	STATTYPE_NOGIS_D_GPRS_EVENT	15	8	WAP登陆次数
1114	STATTYPE_NOGIS_D_GPRS_EVENT	16	8	WAP登陆成功次数
1114	STATTYPE_NOGIS_D_GPRS_EVENT	17	8	WAP登陆失败次数
1114	STATTYPE_NOGIS_D_GPRS_EVENT	18	8	WAP首页显示时长
1114	STATTYPE_NOGIS_D_GPRS_EVENT	19	8	WAP首页显示成功次数
1114	STATTYPE_NOGIS_D_GPRS_EVENT	20	8	WAP首页登陆时长
1114	STATTYPE_NOGIS_D_GPRS_EVENT	21	8	WAP页面刷新次数
1114	STATTYPE_NOGIS_D_GPRS_EVENT	22	8	WAP页面刷新成功次数
1114	STATTYPE_NOGIS_D_GPRS_EVENT	23	8	GPRSATTACH次数
1114	STATTYPE_NOGIS_D_GPRS_EVENT	24	8	GPRSATTACH成功次数
1114	STATTYPE_NOGIS_D_GPRS_EVENT	25	8	GPRSATTACH失败次数
1114	STATTYPE_NOGIS_D_GPRS_EVENT	26	8	GPRSDETACH次数
1114	STATTYPE_NOGIS_D_GPRS_EVENT	27	8	GPRSDETACH成功次数
1114	STATTYPE_NOGIS_D_GPRS_EVENT	28	8	GPRSDETACH失败次数
1115	STATTYPE_GIS_V_GSM_IDLE	1	8	ifileid int
1115	STATTYPE_GIS_V_GSM_IDLE	2	8	左上经度(4BYTES)
1115	STATTYPE_GIS_V_GSM_IDLE	3	8	左上纬度(4BYTES)
1115	STATTYPE_GIS_V_GSM_IDLE	4	8	右下经度(4BYTES)
1115	STATTYPE_GIS_V_GSM_IDLE	5	8	右下纬度(4BYTES)
1115	STATTYPE_GIS_V_GSM_IDLE	6	8	文件开始时间
1115	STATTYPE_GIS_V_GSM_IDLE	7	8	测试里程
1115	STATTYPE_GIS_V_GSM_IDLE	8	8	测试时长
1115	STATTYPE_GIS_V_GSM_IDLE	9	8	[iLAC1][int]NULL
1115	STATTYPE_GIS_V_GSM_IDLE	10	2	[wRAC1][smallint]NULL
1115	STATTYPE_GIS_V_GSM_IDLE	11	8	[iCI1][int]NULL
1115	STATTYPE_GIS_V_GSM_IDLE	12	8	[isamplenum1][int]NULL
1115	STATTYPE_GIS_V_GSM_IDLE	13	8	[irxlevmean1][int]NULL
1115	STATTYPE_GIS_V_GSM_IDLE	14	8	[iLAC2][int]NULL
1115	STATTYPE_GIS_V_GSM_IDLE	15	2	[wRAC2][smallint]NULL
1115	STATTYPE_GIS_V_GSM_IDLE	16	8	[iCI2][int]NULL
1115	STATTYPE_GIS_V_GSM_IDLE	17	8	[isamplenum2][int]NULL
1115	STATTYPE_GIS_V_GSM_IDLE	18	8	[irxlevmean2][int]NULL
1115	STATTYPE_GIS_V_GSM_IDLE	19	8	[iLAC3][int]NULL
1115	STATTYPE_GIS_V_GSM_IDLE	20	2	[wRAC3][smallint]NULL
1115	STATTYPE_GIS_V_GSM_IDLE	21	8	[iCI3][int]NULL
1115	STATTYPE_GIS_V_GSM_IDLE	22	8	[isamplenum3][int]NULL
1115	STATTYPE_GIS_V_GSM_IDLE	23	8	[irxlevmean3][int]NULL
1115	STATTYPE_GIS_V_GSM_IDLE	24	8	[iLAC4][int]NULL
1115	STATTYPE_GIS_V_GSM_IDLE	25	2	[wRAC4][smallint]NULL
1115	STATTYPE_GIS_V_GSM_IDLE	26	8	[iCI4][int]NULL
1115	STATTYPE_GIS_V_GSM_IDLE	27	8	[isamplenum4][int]NULL
1115	STATTYPE_GIS_V_GSM_IDLE	28	8	[irxlevmean4][int]NULL
1115	STATTYPE_GIS_V_GSM_IDLE	29	8	[isamplenum][int]NULL
1115	STATTYPE_GIS_V_GSM_IDLE	30	8	[Rxlev_10_45][int]NULL
1115	STATTYPE_GIS_V_GSM_IDLE	31	8	[Rxlev_46_50][int]NULL
1115	STATTYPE_GIS_V_GSM_IDLE	32	8	[Rxlev_51_55][int]NULL
1115	STATTYPE_GIS_V_GSM_IDLE	33	8	[Rxlev_56_60][int]NULL
1115	STATTYPE_GIS_V_GSM_IDLE	34	8	[Rxlev_61_65][int]NULL
1115	STATTYPE_GIS_V_GSM_IDLE	35	8	[Rxlev_66_70][int]NULL
1115	STATTYPE_GIS_V_GSM_IDLE	36	8	[Rxlev_71_75][int]NULL
1115	STATTYPE_GIS_V_GSM_IDLE	37	8	[Rxlev_76_80][int]NULL
1115	STATTYPE_GIS_V_GSM_IDLE	38	8	[Rxlev_81][int]NULL
1115	STATTYPE_GIS_V_GSM_IDLE	39	8	[Rxlev_82][int]NULL
1115	STATTYPE_GIS_V_GSM_IDLE	40	8	[Rxlev_83][int]NULL
1115	STATTYPE_GIS_V_GSM_IDLE	41	8	[Rxlev_84][int]NULL
1115	STATTYPE_GIS_V_GSM_IDLE	42	8	[Rxlev_85][int]NULL
1115	STATTYPE_GIS_V_GSM_IDLE	43	8	[Rxlev_86][int]NULL
1115	STATTYPE_GIS_V_GSM_IDLE	44	8	[Rxlev_87][int]NULL
1115	STATTYPE_GIS_V_GSM_IDLE	45	8	[Rxlev_88][int]NULL
1115	STATTYPE_GIS_V_GSM_IDLE	46	8	[Rxlev_89][int]NULL
1115	STATTYPE_GIS_V_GSM_IDLE	47	8	[Rxlev_90][int]NULL
1115	STATTYPE_GIS_V_GSM_IDLE	48	8	[Rxlev_91][int]NULL
1115	STATTYPE_GIS_V_GSM_IDLE	49	8	[Rxlev_92][int]NULL
1115	STATTYPE_GIS_V_GSM_IDLE	50	8	[Rxlev_93][int]NULL
1115	STATTYPE_GIS_V_GSM_IDLE	51	8	[Rxlev_94][int]NULL
1115	STATTYPE_GIS_V_GSM_IDLE	52	8	[Rxlev_95][int]NULL
1115	STATTYPE_GIS_V_GSM_IDLE	53	8	[Rxlev_96][int]NULL
1115	STATTYPE_GIS_V_GSM_IDLE	54	8	[Rxlev_97][int]NULL
1115	STATTYPE_GIS_V_GSM_IDLE	55	8	[Rxlev_98][int]NULL
1115	STATTYPE_GIS_V_GSM_IDLE	56	8	[Rxlev_99_120][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	1	8	ifileid int
1116	STATTYPE_GIS_V_GSM_DEDICATED	2	8	左上经度(4BYTES)
1116	STATTYPE_GIS_V_GSM_DEDICATED	3	8	左上纬度(4BYTES)
1116	STATTYPE_GIS_V_GSM_DEDICATED	4	8	右下经度(4BYTES)
1116	STATTYPE_GIS_V_GSM_DEDICATED	5	8	右下纬度(4BYTES)
1116	STATTYPE_GIS_V_GSM_DEDICATED	6	8	文件开始时间
1116	STATTYPE_GIS_V_GSM_DEDICATED	7	8	测试里程
1116	STATTYPE_GIS_V_GSM_DEDICATED	8	8	测试时长
1116	STATTYPE_GIS_V_GSM_DEDICATED	9	8	[iLAC1][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	10	2	[wRAC1][smallint]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	11	8	[iCI1][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	12	8	[isamplenum1][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	13	8	[irxlevmean1][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	14	8	[iLAC2][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	15	2	[wRAC2][smallint]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	16	8	[iCI2][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	17	8	[isamplenum2][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	18	8	[irxlevmean2][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	19	8	[iLAC3][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	20	2	[wRAC3][smallint]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	21	8	[iCI3][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	22	8	[isamplenum3][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	23	8	[irxlevmean3][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	24	8	[iLAC4][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	25	2	[wRAC4][smallint]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	26	8	[iCI4][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	27	8	[isamplenum4][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	28	8	[irxlevmean4][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	29	8	[isamplenum][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	30	8	[Rxlev_10_45][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	31	8	[Rxlev_46_50][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	32	8	[Rxlev_51_55][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	33	8	[Rxlev_56_60][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	34	8	[Rxlev_61_65][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	35	8	[Rxlev_66_70][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	36	8	[Rxlev_71_75][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	37	8	[Rxlev_76_80][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	38	8	[Rxlev_81][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	39	8	[Rxlev_82][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	40	8	[Rxlev_83][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	41	8	[Rxlev_84][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	42	8	[Rxlev_85][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	43	8	[Rxlev_86][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	44	8	[Rxlev_87][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	45	8	[Rxlev_88][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	46	8	[Rxlev_89][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	47	8	[Rxlev_90][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	48	8	[Rxlev_91][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	49	8	[Rxlev_92][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	50	8	[Rxlev_93][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	51	8	[Rxlev_94][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	52	8	[Rxlev_95][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	53	8	[Rxlev_96][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	54	8	[Rxlev_97][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	55	8	[Rxlev_98][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	56	8	[Rxlev_99_120][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	57	8	[RxQual0][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	58	8	[RxQual1][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	59	8	[RxQual2][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	60	8	[RxQual3][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	61	8	[RxQual4][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	62	8	[RxQual5][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	63	8	[RxQual6][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	64	8	[RxQual7][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	65	8	[PESQ_0_10][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	66	8	[PESQ_10_20][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	67	8	[PESQ_20_25][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	68	8	[PESQ_25_28][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	69	8	[PESQ_28_30][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	70	8	[PESQ_30_33][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	71	8	[PESQ_33_34][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	72	8	[PESQ_45][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	73	5	[PESQ_Value_0_10][float]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	74	5	[PESQ_Value_10_20][float]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	75	5	[PESQ_Value_20_25][float]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	76	5	[PESQ_Value_25_28][float]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	77	5	[PESQ_Value_28_30][float]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	78	5	[PESQ_Value_30_33][float]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	79	5	[PESQ_Value_30_34][float]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	80	5	[PESQ_Value_45][float]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	81	8	[TA0][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	82	8	[TA1][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	83	8	[TA2][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	84	8	[TA3][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	85	8	[TA4][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	86	8	[TA5][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	87	8	[TA6][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	88	8	[TA7][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	89	8	[TA8][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	90	8	[TA9][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	91	8	[TA10][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	92	8	[TA_11_15][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	93	8	[TA_16_20][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	94	8	[TA_21_30][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	95	8	[TA_31_40][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	96	8	[TA_41_64][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	97	8	[TA_value_11_15][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	98	8	[TA_value_16_20][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	99	8	[TA_value_21_30][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	100	8	[TA_value_31_40][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	101	8	[TA_value_41_64][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	102	8	[SpeechCodec_FR][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	103	8	[SpeechCodec_HR][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	104	8	[SpeechCodec_EFR][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	105	8	[SpeechCodec_AMR][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	106	8	[SpeechCodecTime_FR][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	107	8	[SpeechCodecTime_HR][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	108	8	[SpeechCodecTime_EFR][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	109	8	[SpeechCodecTime_AMR][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	110	8	[C_Iworst_5_9][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	111	8	[C_Iworst_9_12][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	112	8	[C_Iworst_12_15][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	113	8	[C_Iworst_15_20][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	114	8	[C_Iworst_20_35][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	115	5	[C_Iworst_value_5_9][float]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	116	5	[C_Iworst_value_9_12][float]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	117	5	[C_Iworst_value_12_15][float]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	118	5	[C_Iworst_value_15_20][float]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	119	5	[C_Iworst_value_20_35][float]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	120	8	[C_Iavg_5_9][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	121	8	[C_Iavg_9_12][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	122	8	[C_Iavg_12_15][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	123	8	[C_Iavg_15_20][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	124	8	[C_Iavg_20_35][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	125	5	[C_Iavg_value_5_9][float]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	126	5	[C_Iavg_value_9_12][float]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	127	5	[C_Iavg_value_12_15][float]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	128	5	[C_Iavg_value_15_20][float]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	129	5	[C_Iavg_value_20_35][float]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	130	8	[SQI_20_10][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	131	8	[SQI_10_0][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	132	8	[SQI_0_10][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	133	8	[SQI_10_20][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	134	8	[SQI_20_30][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	135	8	[SQIValue_20_10][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	136	8	[SQIValue_10_0][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	137	8	[SQIValue_0_10][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	138	8	[SQIValue_10_20][int]NULL
1116	STATTYPE_GIS_V_GSM_DEDICATED	139	8	[SQIValue_20_30][int]NULL
1117	STATTYPE_GIS_D_GPRS	1	8	ifileid int
1117	STATTYPE_GIS_D_GPRS	2	8	左上经度(4BYTES)
1117	STATTYPE_GIS_D_GPRS	3	8	左上纬度(4BYTES)
1117	STATTYPE_GIS_D_GPRS	4	8	右下经度(4BYTES)
1117	STATTYPE_GIS_D_GPRS	5	8	右下纬度(4BYTES)
1117	STATTYPE_GIS_D_GPRS	6	8	文件开始时间
1117	STATTYPE_GIS_D_GPRS	7	8	测试里程
1117	STATTYPE_GIS_D_GPRS	8	8	测试时长
1117	STATTYPE_GIS_D_GPRS	9	8	[iLAC1][int]NULL
1117	STATTYPE_GIS_D_GPRS	10	2	[wRAC1][smallint]NULL
1117	STATTYPE_GIS_D_GPRS	11	8	[iCI1][int]NULL
1117	STATTYPE_GIS_D_GPRS	12	8	[isamplenum1][int]NULL
1117	STATTYPE_GIS_D_GPRS	13	8	[irxlevmean1][int]NULL
1117	STATTYPE_GIS_D_GPRS	14	8	[iLAC2][int]NULL
1117	STATTYPE_GIS_D_GPRS	15	2	[wRAC2][smallint]NULL
1117	STATTYPE_GIS_D_GPRS	16	8	[iCI2][int]NULL
1117	STATTYPE_GIS_D_GPRS	17	8	[isamplenum2][int]NULL
1117	STATTYPE_GIS_D_GPRS	18	8	[irxlevmean2][int]NULL
1117	STATTYPE_GIS_D_GPRS	19	8	[iLAC3][int]NULL
1117	STATTYPE_GIS_D_GPRS	20	2	[wRAC3][smallint]NULL
1117	STATTYPE_GIS_D_GPRS	21	8	[iCI3][int]NULL
1117	STATTYPE_GIS_D_GPRS	22	8	[isamplenum3][int]NULL
1117	STATTYPE_GIS_D_GPRS	23	8	[irxlevmean3][int]NULL
1117	STATTYPE_GIS_D_GPRS	24	8	[iLAC4][int]NULL
1117	STATTYPE_GIS_D_GPRS	25	2	[wRAC4][smallint]NULL
1117	STATTYPE_GIS_D_GPRS	26	8	[iCI4][int]NULL
1117	STATTYPE_GIS_D_GPRS	27	8	[isamplenum4][int]NULL
1117	STATTYPE_GIS_D_GPRS	28	8	[irxlevmean4][int]NULL
1117	STATTYPE_GIS_D_GPRS	29	8	[isamplenum][int]NULL
1117	STATTYPE_GIS_D_GPRS	30	8	[Rxlev_10_45][int]NULL
1117	STATTYPE_GIS_D_GPRS	31	8	[Rxlev_46_50][int]NULL
1117	STATTYPE_GIS_D_GPRS	32	8	[Rxlev_51_55][int]NULL
1117	STATTYPE_GIS_D_GPRS	33	8	[Rxlev_56_60][int]NULL
1117	STATTYPE_GIS_D_GPRS	34	8	[Rxlev_61_65][int]NULL
1117	STATTYPE_GIS_D_GPRS	35	8	[Rxlev_66_70][int]NULL
1117	STATTYPE_GIS_D_GPRS	36	8	[Rxlev_71_75][int]NULL
1117	STATTYPE_GIS_D_GPRS	37	8	[Rxlev_76_80][int]NULL
1117	STATTYPE_GIS_D_GPRS	38	8	[Rxlev_81][int]NULL
1117	STATTYPE_GIS_D_GPRS	39	8	[Rxlev_82][int]NULL
1117	STATTYPE_GIS_D_GPRS	40	8	[Rxlev_83][int]NULL
1117	STATTYPE_GIS_D_GPRS	41	8	[Rxlev_84][int]NULL
1117	STATTYPE_GIS_D_GPRS	42	8	[Rxlev_85][int]NULL
1117	STATTYPE_GIS_D_GPRS	43	8	[Rxlev_86][int]NULL
1117	STATTYPE_GIS_D_GPRS	44	8	[Rxlev_87][int]NULL
1117	STATTYPE_GIS_D_GPRS	45	8	[Rxlev_88][int]NULL
1117	STATTYPE_GIS_D_GPRS	46	8	[Rxlev_89][int]NULL
1117	STATTYPE_GIS_D_GPRS	47	8	[Rxlev_90][int]NULL
1117	STATTYPE_GIS_D_GPRS	48	8	[Rxlev_91][int]NULL
1117	STATTYPE_GIS_D_GPRS	49	8	[Rxlev_92][int]NULL
1117	STATTYPE_GIS_D_GPRS	50	8	[Rxlev_93][int]NULL
1117	STATTYPE_GIS_D_GPRS	51	8	[Rxlev_94][int]NULL
1117	STATTYPE_GIS_D_GPRS	52	8	[Rxlev_95][int]NULL
1117	STATTYPE_GIS_D_GPRS	53	8	[Rxlev_96][int]NULL
1117	STATTYPE_GIS_D_GPRS	54	8	[Rxlev_97][int]NULL
1117	STATTYPE_GIS_D_GPRS	55	8	[Rxlev_98][int]NULL
1117	STATTYPE_GIS_D_GPRS	56	8	[Rxlev_99_120][int]NULL
1117	STATTYPE_GIS_D_GPRS	57	8	[RLC_mean_thr_samplenum][int]NULL
1117	STATTYPE_GIS_D_GPRS	58	5	[RLC_mean_thr_sum][float]NULL
1117	STATTYPE_GIS_D_GPRS	59	8	[APP_mean_thr_samplenum][int]NULL
1117	STATTYPE_GIS_D_GPRS	60	5	[APP_mean_thr_sum][float]NULL
1117	STATTYPE_GIS_D_GPRS	61	8	[FTP_recv_bytes][int]NULL
1117	STATTYPE_GIS_D_GPRS	62	8	[FTP_download_duration][int]NULL
1117	STATTYPE_GIS_D_GPRS	63	8	[RLC_recv_bytes][int]NULL
1117	STATTYPE_GIS_D_GPRS	64	8	[RLC_download_duration][int]NULL
1117	STATTYPE_GIS_D_GPRS	65	8	[RLC_BLER_samplenum][int]NULL
1117	STATTYPE_GIS_D_GPRS	66	5	[RLC_BLER_sum][float]NULL
1117	STATTYPE_GIS_D_GPRS	67	8	[GPRS_BLER_samplenum][int]NULL
1117	STATTYPE_GIS_D_GPRS	68	5	[GPRS_BLER_sum][float]NULL
1117	STATTYPE_GIS_D_GPRS	69	8	[EDGE_cover][int]NULL
1117	STATTYPE_GIS_D_GPRS	70	8	[GPRS_cover][int]NULL
1117	STATTYPE_GIS_D_GPRS	71	8	[FTP_slot1_duration][int]NULL
1117	STATTYPE_GIS_D_GPRS	72	8	[FTP_slot2_duration][int]NULL
1117	STATTYPE_GIS_D_GPRS	73	8	[FTP_slot3_duration][int]NULL
1117	STATTYPE_GIS_D_GPRS	74	8	[FTP_slot4_duration][int]NULL
1117	STATTYPE_GIS_D_GPRS	75	8	[MCS1_package][int]NULL
1117	STATTYPE_GIS_D_GPRS	76	8	[MCS2_package][int]NULL
1117	STATTYPE_GIS_D_GPRS	77	8	[MCS3_package][int]NULL
1117	STATTYPE_GIS_D_GPRS	78	8	[MCS4_package][int]NULL
1117	STATTYPE_GIS_D_GPRS	79	8	[MCS5_package][int]NULL
1117	STATTYPE_GIS_D_GPRS	80	8	[MCS6_package][int]NULL
1117	STATTYPE_GIS_D_GPRS	81	8	[MCS7_package][int]NULL
1117	STATTYPE_GIS_D_GPRS	82	8	[MCS8_package][int]NULL
1117	STATTYPE_GIS_D_GPRS	83	8	[MCS9_package][int]NULL
1117	STATTYPE_GIS_D_GPRS	84	8	[CS1_package][int]NULL
1117	STATTYPE_GIS_D_GPRS	85	8	[CS2_package][int]NULL
1117	STATTYPE_GIS_D_GPRS	86	8	[CS3_package][int]NULL
1117	STATTYPE_GIS_D_GPRS	87	8	[CS4_package][int]NULL
1118	STATTYPE_GIS_V_GSM_EVENT	1	8	ifileid int
1118	STATTYPE_GIS_V_GSM_EVENT	2	8	左上经度(4BYTES)
1118	STATTYPE_GIS_V_GSM_EVENT	3	8	左上纬度(4BYTES)
1118	STATTYPE_GIS_V_GSM_EVENT	4	8	右下经度(4BYTES)
1118	STATTYPE_GIS_V_GSM_EVENT	5	8	右下纬度(4BYTES)
1118	STATTYPE_GIS_V_GSM_EVENT	6	8	弱覆盖事件个数
1118	STATTYPE_GIS_V_GSM_EVENT	7	8	试呼事件次数
1118	STATTYPE_GIS_V_GSM_EVENT	8	8	主叫接通事件次数
1118	STATTYPE_GIS_V_GSM_EVENT	9	8	被叫接通事件次数
1118	STATTYPE_GIS_V_GSM_EVENT	10	8	主叫掉话事件次数
1118	STATTYPE_GIS_V_GSM_EVENT	11	8	被叫掉话事件次数
1118	STATTYPE_GIS_V_GSM_EVENT	12	8	主叫振铃次数
1118	STATTYPE_GIS_V_GSM_EVENT	13	8	被叫振铃次数
1118	STATTYPE_GIS_V_GSM_EVENT	14	8	主叫接通时长（用户体验）
1118	STATTYPE_GIS_V_GSM_EVENT	15	8	被叫接通时长（用户体验）
1118	STATTYPE_GIS_V_GSM_EVENT	16	8	主叫接通时长（网络实际）
1118	STATTYPE_GIS_V_GSM_EVENT	17	8	被叫接通时长（网络实际）
1118	STATTYPE_GIS_V_GSM_EVENT	18	8	切换请求事件个数
1118	STATTYPE_GIS_V_GSM_EVENT	19	8	切换成功事件个数
1118	STATTYPE_GIS_V_GSM_EVENT	20	8	切换失败事件个数
1118	STATTYPE_GIS_V_GSM_EVENT	21	8	主被叫同时掉话事件次数
1118	STATTYPE_GIS_V_GSM_EVENT	22	8	弱质量事件次数
1119	STATTYPE_GIS_D_GPRS_EVENT	1	8	ifileid int
1119	STATTYPE_GIS_D_GPRS_EVENT	2	8	左上经度(4BYTES)
1119	STATTYPE_GIS_D_GPRS_EVENT	3	8	左上纬度(4BYTES)
1119	STATTYPE_GIS_D_GPRS_EVENT	4	8	右下经度(4BYTES)
1119	STATTYPE_GIS_D_GPRS_EVENT	5	8	右下纬度(4BYTES)
1119	STATTYPE_GIS_D_GPRS_EVENT	6	8	路由区更新请求次数
1119	STATTYPE_GIS_D_GPRS_EVENT	7	8	路由区更新成功次数
1119	STATTYPE_GIS_D_GPRS_EVENT	8	8	路由区更新失败次数
1119	STATTYPE_GIS_D_GPRS_EVENT	9	8	小区重选事件次数
1119	STATTYPE_GIS_D_GPRS_EVENT	10	8	PDP激活次数
1119	STATTYPE_GIS_D_GPRS_EVENT	11	8	PDP激活成功次数
1119	STATTYPE_GIS_D_GPRS_EVENT	12	8	PDP激活失败次数
1119	STATTYPE_GIS_D_GPRS_EVENT	13	8	PDP去激活次数
1119	STATTYPE_GIS_D_GPRS_EVENT	14	8	PDP去激活成功次数
1119	STATTYPE_GIS_D_GPRS_EVENT	15	8	PDP去激活失败次数
1119	STATTYPE_GIS_D_GPRS_EVENT	16	8	掉线次数
1119	STATTYPE_GIS_D_GPRS_EVENT	17	8	WAP登陆次数
1119	STATTYPE_GIS_D_GPRS_EVENT	18	8	WAP登陆成功次数
1119	STATTYPE_GIS_D_GPRS_EVENT	19	8	WAP登陆失败次数
1119	STATTYPE_GIS_D_GPRS_EVENT	20	8	WAP首页显示时长
1119	STATTYPE_GIS_D_GPRS_EVENT	21	8	WAP首页显示成功次数
1119	STATTYPE_GIS_D_GPRS_EVENT	22	8	WAP首页登陆时长
1119	STATTYPE_GIS_D_GPRS_EVENT	23	8	WAP页面刷新次数
1119	STATTYPE_GIS_D_GPRS_EVENT	24	8	WAP页面刷新成功次数
1119	STATTYPE_GIS_D_GPRS_EVENT	25	8	GPRSATTACH次数
1119	STATTYPE_GIS_D_GPRS_EVENT	26	8	GPRSATTACH成功次数
1119	STATTYPE_GIS_D_GPRS_EVENT	27	8	GPRSATTACH失败次数
1119	STATTYPE_GIS_D_GPRS_EVENT	28	8	GPRSDETACH次数
1119	STATTYPE_GIS_D_GPRS_EVENT	29	8	GPRSDETACH成功次数
1119	STATTYPE_GIS_D_GPRS_EVENT	30	8	GPRSDETACH失败次数
1120	STATTYPE_CDMA_KPI_PARA	1	8	ifileid int
1120	STATTYPE_CDMA_KPI_PARA	2	8	左上经度(4BYTES)
1120	STATTYPE_CDMA_KPI_PARA	3	8	左上纬度(4BYTES)
1120	STATTYPE_CDMA_KPI_PARA	4	8	右下经度(4BYTES)
1120	STATTYPE_CDMA_KPI_PARA	5	8	右下纬度(4BYTES)
1120	STATTYPE_CDMA_KPI_PARA	6	8	文件开始时间
1120	STATTYPE_CDMA_KPI_PARA	7	8	测试里程
1120	STATTYPE_CDMA_KPI_PARA	8	8	测试时长
1120	STATTYPE_CDMA_KPI_PARA	9	8	[iTotalSampleNum] [int] NULL,
1120	STATTYPE_CDMA_KPI_PARA	10	8	[iECIO_f15] [int] NULL,
1120	STATTYPE_CDMA_KPI_PARA	11	5	[fECIO_f15_Total] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	12	5	[fECIO_f15_Max] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	13	5	[fECIO_f15_Min] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	14	8	[iECIO_f15_f12] [int] NULL,
1120	STATTYPE_CDMA_KPI_PARA	15	5	[fECIO_f15_f12_Total] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	16	5	[fiECIO_f15_f12_Max] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	17	5	[fiECIO_f15_f12_Min] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	18	8	[iECIO_f12_f9] [int] NULL,
1120	STATTYPE_CDMA_KPI_PARA	19	5	[fECIO_f12_f9_Total] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	20	5	[fECIO_f12_f9_Max] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	21	5	[fECIO_f12_f9_Min] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	22	8	[iECIO_f9_f6] [int] NULL,
1120	STATTYPE_CDMA_KPI_PARA	23	5	[fECIO_f9_f6_Total] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	24	5	[fECIO_f9_f6_Max] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	25	5	[fECIO_f9_f6_Min] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	26	8	[iECIO_f6] [int] NULL,
1120	STATTYPE_CDMA_KPI_PARA	27	5	[fECIO_f6_Total] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	28	5	[fECIO_f6_Max] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	29	5	[fECIO_f6_Min] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	30	8	[iFFER_0_1] [int] NULL,
1120	STATTYPE_CDMA_KPI_PARA	31	5	[fFFER_0_1_Total] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	32	5	[fFFER_0_1_Max] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	33	5	[fFFER_0_1_Min] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	34	8	[iFFER_1_2] [int] NULL,
1120	STATTYPE_CDMA_KPI_PARA	35	5	[fFFER_1_2_Total] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	36	5	[fFFER_1_2_Max] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	37	5	[fFFER_1_2_Min] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	38	8	[iFFER_2_3] [int] NULL,
1120	STATTYPE_CDMA_KPI_PARA	39	5	[fFFER_2_3_Total] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	40	5	[fFFER_2_3_Max] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	41	5	[fFFER_2_3_Min] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	42	8	[iFFER_3_4] [int] NULL,
1120	STATTYPE_CDMA_KPI_PARA	43	5	[fFFER_3_4_Total] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	44	5	[fFFER_3_4_Max] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	45	5	[fFFER_3_4_Min] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	46	8	[iFFER_4_5] [int] NULL,
1120	STATTYPE_CDMA_KPI_PARA	47	5	[fFFER_4_5_Total] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	48	5	[fFFER_4_5_Max] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	49	5	[fFFER_4_5_Min] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	50	8	[iFFER_5_6] [int] NULL,
1120	STATTYPE_CDMA_KPI_PARA	51	5	[fFFER_5_6_Total] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	52	5	[fFFER_5_6_Max] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	53	5	[fFFER_5_6_Min] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	54	8	[iFFER_6_7] [int] NULL,
1120	STATTYPE_CDMA_KPI_PARA	55	5	[fFFER_6_7_Total] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	56	5	[fFFER_6_7_Max] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	57	5	[fFFER_6_7_Min] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	58	8	[iFFER_7_8] [int] NULL,
1120	STATTYPE_CDMA_KPI_PARA	59	5	[fFFER_7_8_Total] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	60	5	[fFFER_7_8_Max] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	61	5	[fFFER_7_8_Min] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	62	8	[iFFER_8_9] [int] NULL,
1120	STATTYPE_CDMA_KPI_PARA	63	5	[fFFER_8_9_Total] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	64	5	[fFFER_8_9_Max] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	65	5	[fFFER_8_9_Min] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	66	8	[iFFER_9_10] [int] NULL,
1120	STATTYPE_CDMA_KPI_PARA	67	5	[fFFER_9_10_Total] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	68	5	[fFFER_9_10_Max] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	69	5	[fFFER_9_10_Min] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	70	8	[iFFER_10_100] [int] NULL,
1120	STATTYPE_CDMA_KPI_PARA	71	5	[fFFER_10_100_Total] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	72	5	[fFFER_10_100_Max] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	73	5	[fFFER_10_100_Min] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	74	8	[iTXPower_f20] [int] NULL,
1120	STATTYPE_CDMA_KPI_PARA	75	5	[fTXPower_f20_Total] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	76	5	[fTXPower_f20_Max] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	77	5	[fTXPower_f20_Min] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	78	8	[iTXPower_f20_f15] [int] NULL,
1120	STATTYPE_CDMA_KPI_PARA	79	5	[fTXPower_f20_f15_Total] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	80	5	[fTXPower_f20_f15_Max] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	81	5	[fTXPower_f20_f15_Min] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	82	8	[iTXPower_f15_f10] [int] NULL,
1120	STATTYPE_CDMA_KPI_PARA	83	5	[fTXPower_f15_f10_Total] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	84	5	[fTXPower_f15_f10_Max] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	85	5	[fTXPower_f15_f10_Min] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	86	8	[iTXPower_f10_f5] [int] NULL,
1120	STATTYPE_CDMA_KPI_PARA	87	5	[fTXPower_f10_f5_Total] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	88	5	[fTXPower_f10_f5_Max] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	89	5	[fTXPower_f10_f5_Min] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	90	8	[iTXPower_f5_0] [int] NULL,
1120	STATTYPE_CDMA_KPI_PARA	91	5	[fTXPower_f5_0_Total] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	92	5	[fTXPower_f5_0_Max] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	93	5	[fTXPower_f5_0_Min] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	94	8	[iTXPower_0_5] [int] NULL,
1120	STATTYPE_CDMA_KPI_PARA	95	5	[fTXPower_0_5_Total] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	96	5	[fTXPower_0_5_Max] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	97	5	[fTXPower_0_5_Min] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	98	8	[iTXPower_5_10] [int] NULL,
1120	STATTYPE_CDMA_KPI_PARA	99	5	[fTXPower_5_10_Total] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	100	5	[fTXPower_5_10_Max] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	101	5	[fTXPower_5_10_Min] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	102	8	[iTXPower_10_15] [int] NULL,
1120	STATTYPE_CDMA_KPI_PARA	103	5	[fTXPower_10_15_Total] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	104	5	[fTXPower_10_15_Max] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	105	5	[fTXPower_10_15_Min] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	106	8	[iTXPower_15_20] [int] NULL,
1120	STATTYPE_CDMA_KPI_PARA	107	5	[fTXPower_15_20_Total] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	108	5	[fTXPower_15_20_Max] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	109	5	[fTXPower_15_20_Min] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	110	8	[iTXPower_20] [int] NULL,
1120	STATTYPE_CDMA_KPI_PARA	111	5	[fTXPower_20_Total] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	112	5	[fTXPower_20_Max] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	113	5	[fTXPower_20_Min] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	114	8	[iRXPower_f94] [int] NULL,
1120	STATTYPE_CDMA_KPI_PARA	115	5	[fRXPower_f94_Total] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	116	5	[fRXPower_f94_Max] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	117	5	[fRXPower_f94_Min] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	118	8	[iRXPower_f94_f90] [int] NULL,
1120	STATTYPE_CDMA_KPI_PARA	119	5	[fRXPower_f94_f90_Total] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	120	5	[fRXPower_f94_f90_Max] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	121	5	[fRXPower_f94_f90_Min] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	122	8	[iRXPower_f90_f85] [int] NULL,
1120	STATTYPE_CDMA_KPI_PARA	123	5	[fRXPower_f90_f85_Total] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	124	5	[fRXPower_f90_f85_Max] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	125	5	[fRXPower_f90_f85_Min] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	126	8	[iRXPower_f85_f80] [int] NULL,
1120	STATTYPE_CDMA_KPI_PARA	127	5	[fRXPower_f85_f80_Total] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	128	5	[fRXPower_f85_f80_Max] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	129	5	[fRXPower_f85_f80_Min] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	130	8	[iRXPower_f80_f75] [int] NULL,
1120	STATTYPE_CDMA_KPI_PARA	131	5	[fRXPower_f80_f75_Total] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	132	5	[fRXPower_f80_f75_Max] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	133	5	[fRXPower_f80_f75_Min] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	134	8	[iRXPower_f75_f65] [int] NULL,
1120	STATTYPE_CDMA_KPI_PARA	135	5	[fRXPower_f75_f65_Total] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	136	5	[fRXPower_f75_f65_Max] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	137	5	[fRXPower_f75_f65_Min] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	138	8	[iRXPower_f65] [int] NULL,
1120	STATTYPE_CDMA_KPI_PARA	139	5	[fRXPower_f65_Total] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	140	5	[fRXPower_f65_Max] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	141	5	[fRXPower_f65_Min] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	142	8	[iCoverNum12_15_f90] [int] NULL,
1120	STATTYPE_CDMA_KPI_PARA	143	8	[iCoverNum12_15_f95] [int] NULL,
1120	STATTYPE_CDMA_KPI_PARA	144	8	[iFTP_Download_samplenum] [int] NULL,
1120	STATTYPE_CDMA_KPI_PARA	145	8	[iFTP_Download_total] [int] NULL,
1120	STATTYPE_CDMA_KPI_PARA	146	8	[iPesqLQNum] [int] NULL,
1120	STATTYPE_CDMA_KPI_PARA	147	5	[fPesqLQ_Total] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	148	5	[fPesqLQ_Max] [float] NULL,
1120	STATTYPE_CDMA_KPI_PARA	149	5	[fPesqLQ_Min] [float] NULL
1121	STATTYPE_KPI_LOG_INFO	1	8	左上经度(4BYTES)
1121	STATTYPE_KPI_LOG_INFO	2	8	左上纬度(4BYTES)
1121	STATTYPE_KPI_LOG_INFO	3	8	右下经度(4BYTES)
1121	STATTYPE_KPI_LOG_INFO	4	8	右下纬度(4BYTES)
1121	STATTYPE_KPI_LOG_INFO	5	8	测试类型4BYTE
1121	STATTYPE_KPI_LOG_INFO	6	8	设备类型4BYTE
1121	STATTYPE_KPI_LOG_INFO	7	8	文件类型4BYTE
1121	STATTYPE_KPI_LOG_INFO	8	8	业务类型4BYTE
1121	STATTYPE_KPI_LOG_INFO	9	8	运营商类型4BYTE
1121	STATTYPE_KPI_LOG_INFO	10	8	项目类型4BYTE
1121	STATTYPE_KPI_LOG_INFO	11	8	所属轮次4BYTE
1121	STATTYPE_KPI_LOG_INFO	12	8	文件ID4BYTES
1121	STATTYPE_KPI_LOG_INFO	13	6	文件名称string
1121	STATTYPE_KPI_LOG_INFO	14	8	所属地域类型ID
1121	STATTYPE_KPI_LOG_INFO	15	8	所属地域ID
1121	STATTYPE_KPI_LOG_INFO	16	8	所属代维公司ID
1121	STATTYPE_KPI_LOG_INFO	17	8	衰减值
1121	STATTYPE_KPI_LOG_INFO	18	8	项目子类型ID1
1121	STATTYPE_KPI_LOG_INFO	19	8	项目子类型ID2
1121	STATTYPE_KPI_LOG_INFO	20	8	文件大小
1122	STATTYPE_NOGIS_TDSCDMA_AMR	1	8	ifileid int
1122	STATTYPE_NOGIS_TDSCDMA_AMR	2	8	iLAC1
1122	STATTYPE_NOGIS_TDSCDMA_AMR	3	8	iCI1
1122	STATTYPE_NOGIS_TDSCDMA_AMR	4	8	isamplenum1
1122	STATTYPE_NOGIS_TDSCDMA_AMR	5	8	ipccpch_rscpmean1
1122	STATTYPE_NOGIS_TDSCDMA_AMR	6	8	iLAC2
1122	STATTYPE_NOGIS_TDSCDMA_AMR	7	8	iCI2
1122	STATTYPE_NOGIS_TDSCDMA_AMR	8	8	isamplenum2
1122	STATTYPE_NOGIS_TDSCDMA_AMR	9	8	ipccpch_rscpmean2
1122	STATTYPE_NOGIS_TDSCDMA_AMR	10	8	iLAC3
1122	STATTYPE_NOGIS_TDSCDMA_AMR	11	8	iCI3
1122	STATTYPE_NOGIS_TDSCDMA_AMR	12	8	isamplenum3
1122	STATTYPE_NOGIS_TDSCDMA_AMR	13	8	ipccpch_rscpmean3
1122	STATTYPE_NOGIS_TDSCDMA_AMR	14	8	iLAC4
1122	STATTYPE_NOGIS_TDSCDMA_AMR	15	8	iCI4
1122	STATTYPE_NOGIS_TDSCDMA_AMR	16	8	isamplenum4
1122	STATTYPE_NOGIS_TDSCDMA_AMR	17	8	ipccpch_rscpmean4
1122	STATTYPE_NOGIS_TDSCDMA_AMR	18	8	[iduration_TD][int]
1122	STATTYPE_NOGIS_TDSCDMA_AMR	19	8	[iduration_GSM][int]
1122	STATTYPE_NOGIS_TDSCDMA_AMR	20	8	[idistance_TD][int]
1122	STATTYPE_NOGIS_TDSCDMA_AMR	21	8	[idistance_GSM][int]
1122	STATTYPE_NOGIS_TDSCDMA_AMR	22	8	[ibler_sample][int]
1122	STATTYPE_NOGIS_TDSCDMA_AMR	23	8	[ibler_total][int]
1122	STATTYPE_NOGIS_TDSCDMA_AMR	24	8	[ibler_err][int]
1122	STATTYPE_NOGIS_TDSCDMA_AMR	25	8	[ibler_max][int]
1122	STATTYPE_NOGIS_TDSCDMA_AMR	26	8	[ibler_min][int]
1122	STATTYPE_NOGIS_TDSCDMA_AMR	27	8	[iPCCPCH_sample][int]
1122	STATTYPE_NOGIS_TDSCDMA_AMR	28	8	[iPCCPCH_RSCP_CI_95_sample][int]
1122	STATTYPE_NOGIS_TDSCDMA_AMR	29	8	[iPCCPCH_RSCP_CI_90_sample][int]
1122	STATTYPE_NOGIS_TDSCDMA_AMR	30	8	[iPCCPCH_RSCP_max][int]
1122	STATTYPE_NOGIS_TDSCDMA_AMR	31	8	[iPCCPCH_RSCP_min][int]
1122	STATTYPE_NOGIS_TDSCDMA_AMR	32	8	[iPCCPCH_RSCP_mean][int]
1122	STATTYPE_NOGIS_TDSCDMA_AMR	33	8	[iPCCPCH_C2I_max][int]
1122	STATTYPE_NOGIS_TDSCDMA_AMR	34	8	[iPCCPCH_C2I_min][int]
1122	STATTYPE_NOGIS_TDSCDMA_AMR	35	8	[iPCCPCH_C2I_mean][int]
1122	STATTYPE_NOGIS_TDSCDMA_AMR	36	8	[iDPCH_C2I_sample][int]
1122	STATTYPE_NOGIS_TDSCDMA_AMR	37	8	[iDPCH_C2I_F3_sample][int]
1122	STATTYPE_NOGIS_TDSCDMA_AMR	38	8	[iDPCH_C2I_max][int]
1122	STATTYPE_NOGIS_TDSCDMA_AMR	39	8	[iDPCH_C2I_min][int]
1122	STATTYPE_NOGIS_TDSCDMA_AMR	40	8	[iDPCH_C2I_mean][int]
1122	STATTYPE_NOGIS_TDSCDMA_AMR	41	8	[iRxlev_sample][int]
1122	STATTYPE_NOGIS_TDSCDMA_AMR	42	8	[iRxlev_F75][int]
1122	STATTYPE_NOGIS_TDSCDMA_AMR	43	8	[iRxlev_F80][int]
1122	STATTYPE_NOGIS_TDSCDMA_AMR	44	8	[iRxlev_F85][int]
1122	STATTYPE_NOGIS_TDSCDMA_AMR	45	8	[iRxlev_F90][int]
1122	STATTYPE_NOGIS_TDSCDMA_AMR	46	8	[iRxlev_F94][int]
1122	STATTYPE_NOGIS_TDSCDMA_AMR	47	8	[iRxlev_F100][int]
1122	STATTYPE_NOGIS_TDSCDMA_AMR	48	8	[iMOS_sample][int]
1122	STATTYPE_NOGIS_TDSCDMA_AMR	49	8	[iMOS_total][int]
1123	STATTYPE_NOGIS_TDSCDMA_PS	1	8	ifileid int
1123	STATTYPE_NOGIS_TDSCDMA_PS	2	8	iLAC1
1123	STATTYPE_NOGIS_TDSCDMA_PS	3	8	iCI1
1123	STATTYPE_NOGIS_TDSCDMA_PS	4	8	isamplenum1
1123	STATTYPE_NOGIS_TDSCDMA_PS	5	8	ipccpch_rscpmean1
1123	STATTYPE_NOGIS_TDSCDMA_PS	6	8	iLAC2
1123	STATTYPE_NOGIS_TDSCDMA_PS	7	8	iCI2
1123	STATTYPE_NOGIS_TDSCDMA_PS	8	8	isamplenum2
1123	STATTYPE_NOGIS_TDSCDMA_PS	9	8	ipccpch_rscpmean2
1123	STATTYPE_NOGIS_TDSCDMA_PS	10	8	iLAC3
1123	STATTYPE_NOGIS_TDSCDMA_PS	11	8	iCI3
1123	STATTYPE_NOGIS_TDSCDMA_PS	12	8	isamplenum3
1123	STATTYPE_NOGIS_TDSCDMA_PS	13	8	ipccpch_rscpmean3
1123	STATTYPE_NOGIS_TDSCDMA_PS	14	8	iLAC4
1123	STATTYPE_NOGIS_TDSCDMA_PS	15	8	iCI4
1123	STATTYPE_NOGIS_TDSCDMA_PS	16	8	isamplenum4
1123	STATTYPE_NOGIS_TDSCDMA_PS	17	8	ipccpch_rscpmean4
1123	STATTYPE_NOGIS_TDSCDMA_PS	18	8	[iduration_TD][int]
1123	STATTYPE_NOGIS_TDSCDMA_PS	19	8	[iduration_GPRSEDGE][int]
1123	STATTYPE_NOGIS_TDSCDMA_PS	20	8	[idistance_TD][int]
1123	STATTYPE_NOGIS_TDSCDMA_PS	21	8	[idistance_GPRSEDGE][int]
1123	STATTYPE_NOGIS_TDSCDMA_PS	22	8	[iBRU_sample][int]
1123	STATTYPE_NOGIS_TDSCDMA_PS	23	8	[iBRU_value][int]
1123	STATTYPE_NOGIS_TDSCDMA_PS	24	8	[iPS384_value][int]
1123	STATTYPE_NOGIS_TDSCDMA_PS	25	8	[iPS128_value][int]
1123	STATTYPE_NOGIS_TDSCDMA_PS	26	8	[iPS64_value][int]
1123	STATTYPE_NOGIS_TDSCDMA_PS	27	8	[iGPRSEDGE_value][int]
1123	STATTYPE_NOGIS_TDSCDMA_PS	28	8	[iBler_R4_sample][int]
1123	STATTYPE_NOGIS_TDSCDMA_PS	29	8	[iBler_R4_total][int]
1123	STATTYPE_NOGIS_TDSCDMA_PS	30	8	[iBler_GPRSEDGE_sample][int]
1123	STATTYPE_NOGIS_TDSCDMA_PS	31	8	[iBler_GPRSEDGE_total][int]
1123	STATTYPE_NOGIS_TDSCDMA_PS	32	8	[iRLC_R4_DL_sample][int]
1123	STATTYPE_NOGIS_TDSCDMA_PS	33	8	[iRLC_R4_DL_value][int]
1123	STATTYPE_NOGIS_TDSCDMA_PS	34	8	[iRLC_GPRSEDGE_DL_sample][int]
1123	STATTYPE_NOGIS_TDSCDMA_PS	35	8	[iRLC_GPRSEDGE_DL_value][int]
1123	STATTYPE_NOGIS_TDSCDMA_PS	36	8	[iAPP_R4_DL_sample][int]
1123	STATTYPE_NOGIS_TDSCDMA_PS	37	8	[iAPP_R4_DL_value][int]
1123	STATTYPE_NOGIS_TDSCDMA_PS	38	8	[iAPP_GPRSEDGE_DL_sample][int]
1123	STATTYPE_NOGIS_TDSCDMA_PS	39	8	[iAPP_GPRSEDGE_DL_value][int]
1123	STATTYPE_NOGIS_TDSCDMA_PS	40	8	[iFTP_R4_BYTES_DL][int]
1123	STATTYPE_NOGIS_TDSCDMA_PS	41	8	[iFTP_R4_TIME_DL][int]
1123	STATTYPE_NOGIS_TDSCDMA_PS	42	8	[iFTP_GPRSEDGE_BYTES_DL][int]
1123	STATTYPE_NOGIS_TDSCDMA_PS	43	8	[iFTP_GPRSEDGE_TIME_DL][int]
1124	STATTYPE_NOGIS_TDSCDMA_VP	1	8	ifileid int
1124	STATTYPE_NOGIS_TDSCDMA_VP	2	8	iLAC1
1124	STATTYPE_NOGIS_TDSCDMA_VP	3	8	iCI1
1124	STATTYPE_NOGIS_TDSCDMA_VP	4	8	isamplenum1
1124	STATTYPE_NOGIS_TDSCDMA_VP	5	8	ipccpch_rscpmean1
1124	STATTYPE_NOGIS_TDSCDMA_VP	6	8	iLAC2
1124	STATTYPE_NOGIS_TDSCDMA_VP	7	8	iCI2
1124	STATTYPE_NOGIS_TDSCDMA_VP	8	8	isamplenum2
1124	STATTYPE_NOGIS_TDSCDMA_VP	9	8	ipccpch_rscpmean2
1124	STATTYPE_NOGIS_TDSCDMA_VP	10	8	iLAC3
1124	STATTYPE_NOGIS_TDSCDMA_VP	11	8	iCI3
1124	STATTYPE_NOGIS_TDSCDMA_VP	12	8	isamplenum3
1124	STATTYPE_NOGIS_TDSCDMA_VP	13	8	ipccpch_rscpmean3
1124	STATTYPE_NOGIS_TDSCDMA_VP	14	8	iLAC4
1124	STATTYPE_NOGIS_TDSCDMA_VP	15	8	iCI4
1124	STATTYPE_NOGIS_TDSCDMA_VP	16	8	isamplenum4
1124	STATTYPE_NOGIS_TDSCDMA_VP	17	8	ipccpch_rscpmean4
1124	STATTYPE_NOGIS_TDSCDMA_VP	18	8	[iduration][int]
1124	STATTYPE_NOGIS_TDSCDMA_VP	19	8	[idistance][int]
1124	STATTYPE_NOGIS_TDSCDMA_VP	20	8	[iBler_sample][int]
1124	STATTYPE_NOGIS_TDSCDMA_VP	21	8	[iBler_total][int]
1124	STATTYPE_NOGIS_TDSCDMA_VP	22	8	[iBler_err][int]
1124	STATTYPE_NOGIS_TDSCDMA_VP	23	8	[iBler_max][int]
1124	STATTYPE_NOGIS_TDSCDMA_VP	24	8	[iBler_min][int]
1125	STATTYPE_GIS_TDSCDMA_AMR	1	8	ifileid int
1125	STATTYPE_GIS_TDSCDMA_AMR	2	8	左上经度(4BYTES)
1125	STATTYPE_GIS_TDSCDMA_AMR	3	8	左上纬度(4BYTES)
1125	STATTYPE_GIS_TDSCDMA_AMR	4	8	右下经度(4BYTES)
1125	STATTYPE_GIS_TDSCDMA_AMR	5	8	右下纬度(4BYTES)
1125	STATTYPE_GIS_TDSCDMA_AMR	6	8	文件开始时间
1125	STATTYPE_GIS_TDSCDMA_AMR	7	8	[iduration_TD][int]
1125	STATTYPE_GIS_TDSCDMA_AMR	8	8	[iduration_GSM][int]
1125	STATTYPE_GIS_TDSCDMA_AMR	9	8	[idistance_TD][int]
1125	STATTYPE_GIS_TDSCDMA_AMR	10	8	[idistance_GSM][int]
1125	STATTYPE_GIS_TDSCDMA_AMR	11	8	iLAC1
1125	STATTYPE_GIS_TDSCDMA_AMR	12	8	iCI1
1125	STATTYPE_GIS_TDSCDMA_AMR	13	8	isamplenum1
1125	STATTYPE_GIS_TDSCDMA_AMR	14	8	ipccpch_rscpmean1
1125	STATTYPE_GIS_TDSCDMA_AMR	15	8	iLAC2
1125	STATTYPE_GIS_TDSCDMA_AMR	16	8	iCI2
1125	STATTYPE_GIS_TDSCDMA_AMR	17	8	isamplenum2
1125	STATTYPE_GIS_TDSCDMA_AMR	18	8	ipccpch_rscpmean2
1125	STATTYPE_GIS_TDSCDMA_AMR	19	8	iLAC3
1125	STATTYPE_GIS_TDSCDMA_AMR	20	8	iCI3
1125	STATTYPE_GIS_TDSCDMA_AMR	21	8	isamplenum3
1125	STATTYPE_GIS_TDSCDMA_AMR	22	8	ipccpch_rscpmean3
1125	STATTYPE_GIS_TDSCDMA_AMR	23	8	iLAC4
1125	STATTYPE_GIS_TDSCDMA_AMR	24	8	iCI4
1125	STATTYPE_GIS_TDSCDMA_AMR	25	8	isamplenum4
1125	STATTYPE_GIS_TDSCDMA_AMR	26	8	ipccpch_rscpmean4
1125	STATTYPE_GIS_TDSCDMA_AMR	27	8	[ibler_sample][int]
1125	STATTYPE_GIS_TDSCDMA_AMR	28	8	[ibler_total][int]
1125	STATTYPE_GIS_TDSCDMA_AMR	29	8	[ibler_err][int]
1125	STATTYPE_GIS_TDSCDMA_AMR	30	8	[iPCCPCH_sample][int]
1125	STATTYPE_GIS_TDSCDMA_AMR	31	8	[iPCCPCH_RSCP_CI_95_sample][int]
1125	STATTYPE_GIS_TDSCDMA_AMR	32	8	[iPCCPCH_RSCP_CI_90_sample][int]
1125	STATTYPE_GIS_TDSCDMA_AMR	33	8	[iDPCH_C2I_sample][int]
1125	STATTYPE_GIS_TDSCDMA_AMR	34	8	[iDPCH_C2I_F3_sample][int]
1125	STATTYPE_GIS_TDSCDMA_AMR	35	8	[iRxlev_sample][int]
1125	STATTYPE_GIS_TDSCDMA_AMR	36	8	[iRxlev_F75][int]
1125	STATTYPE_GIS_TDSCDMA_AMR	37	8	[iRxlev_F80][int]
1125	STATTYPE_GIS_TDSCDMA_AMR	38	8	[iRxlev_F85][int]
1125	STATTYPE_GIS_TDSCDMA_AMR	39	8	[iRxlev_F90][int]
1125	STATTYPE_GIS_TDSCDMA_AMR	40	8	[iRxlev_F94][int]
1125	STATTYPE_GIS_TDSCDMA_AMR	41	8	[iRxlev_F100][int]
1125	STATTYPE_GIS_TDSCDMA_AMR	42	8	[iMOS_sample][int]
1125	STATTYPE_GIS_TDSCDMA_AMR	43	8	[iMOS_total][int]
1126	STATTYPE_GIS_TDSCDMA_PS	1	8	ifileid int
1126	STATTYPE_GIS_TDSCDMA_PS	2	8	左上经度(4BYTES)
1126	STATTYPE_GIS_TDSCDMA_PS	3	8	左上纬度(4BYTES)
1126	STATTYPE_GIS_TDSCDMA_PS	4	8	右下经度(4BYTES)
1126	STATTYPE_GIS_TDSCDMA_PS	5	8	右下纬度(4BYTES)
1126	STATTYPE_GIS_TDSCDMA_PS	6	8	文件开始时间
1126	STATTYPE_GIS_TDSCDMA_PS	7	8	[iduration_TD][int]
1126	STATTYPE_GIS_TDSCDMA_PS	8	8	[iduration_GPRSEDGE][int]
1126	STATTYPE_GIS_TDSCDMA_PS	9	8	[idistance_TD][int]
1126	STATTYPE_GIS_TDSCDMA_PS	10	8	[idistance_GPRSEDGE][int]
1126	STATTYPE_GIS_TDSCDMA_PS	11	8	iLAC1
1126	STATTYPE_GIS_TDSCDMA_PS	12	8	iCI1
1126	STATTYPE_GIS_TDSCDMA_PS	13	8	isamplenum1
1126	STATTYPE_GIS_TDSCDMA_PS	14	8	ipccpch_rscpmean1
1126	STATTYPE_GIS_TDSCDMA_PS	15	8	iLAC2
1126	STATTYPE_GIS_TDSCDMA_PS	16	8	iCI2
1126	STATTYPE_GIS_TDSCDMA_PS	17	8	isamplenum2
1126	STATTYPE_GIS_TDSCDMA_PS	18	8	ipccpch_rscpmean2
1126	STATTYPE_GIS_TDSCDMA_PS	19	8	iLAC3
1126	STATTYPE_GIS_TDSCDMA_PS	20	8	iCI3
1126	STATTYPE_GIS_TDSCDMA_PS	21	8	isamplenum3
1126	STATTYPE_GIS_TDSCDMA_PS	22	8	ipccpch_rscpmean3
1126	STATTYPE_GIS_TDSCDMA_PS	23	8	iLAC4
1126	STATTYPE_GIS_TDSCDMA_PS	24	8	iCI4
1126	STATTYPE_GIS_TDSCDMA_PS	25	8	isamplenum4
1126	STATTYPE_GIS_TDSCDMA_PS	26	8	ipccpch_rscpmean4
1126	STATTYPE_GIS_TDSCDMA_PS	27	8	[iBRU_sample][int]
1126	STATTYPE_GIS_TDSCDMA_PS	28	8	[iBRU_value][int]
1126	STATTYPE_GIS_TDSCDMA_PS	29	8	[iPS384_value][int]
1126	STATTYPE_GIS_TDSCDMA_PS	30	8	[iPS128_value][int]
1126	STATTYPE_GIS_TDSCDMA_PS	31	8	[iPS64_value][int]
1126	STATTYPE_GIS_TDSCDMA_PS	32	8	[iGPRSEDGE_value][int]
1126	STATTYPE_GIS_TDSCDMA_PS	33	8	[iBler_R4_sample][int]
1126	STATTYPE_GIS_TDSCDMA_PS	34	8	[iBler_R4_total][int]
1126	STATTYPE_GIS_TDSCDMA_PS	35	8	[iBler_GPRSEDGE_sample][int]
1126	STATTYPE_GIS_TDSCDMA_PS	36	8	[iBler_GPRSEDGE_total][int]
1126	STATTYPE_GIS_TDSCDMA_PS	37	8	[iRLC_R4_DL_sample][int]
1126	STATTYPE_GIS_TDSCDMA_PS	38	8	[iRLC_R4_DL_value][int]
1126	STATTYPE_GIS_TDSCDMA_PS	39	8	[iRLC_GPRSEDGE_DL_sample][int]
1126	STATTYPE_GIS_TDSCDMA_PS	40	8	[iRLC_GPRSEDGE_DL_value][int]
1126	STATTYPE_GIS_TDSCDMA_PS	41	8	[iAPP_R4_DL_sample][int]
1126	STATTYPE_GIS_TDSCDMA_PS	42	8	[iAPP_R4_DL_value][int]
1126	STATTYPE_GIS_TDSCDMA_PS	43	8	[iAPP_GPRSEDGE_DL_sample][int]
1126	STATTYPE_GIS_TDSCDMA_PS	44	8	[iAPP_GPRSEDGE_DL_value][int]
1126	STATTYPE_GIS_TDSCDMA_PS	45	8	[iFTP_R4_BYTES_DL][int]
1126	STATTYPE_GIS_TDSCDMA_PS	46	8	[iFTP_R4_TIME_DL][int]
1126	STATTYPE_GIS_TDSCDMA_PS	47	8	[iFTP_GPRSEDGE_BYTES_DL][int]
1126	STATTYPE_GIS_TDSCDMA_PS	48	8	[iFTP_GPRSEDGE_TIME_DL][int]
1127	STATTYPE_GIS_TDSCDMA_VP	1	8	ifileid int
1127	STATTYPE_GIS_TDSCDMA_VP	2	8	左上经度(4BYTES)
1127	STATTYPE_GIS_TDSCDMA_VP	3	8	左上纬度(4BYTES)
1127	STATTYPE_GIS_TDSCDMA_VP	4	8	右下经度(4BYTES)
1127	STATTYPE_GIS_TDSCDMA_VP	5	8	右下纬度(4BYTES)
1127	STATTYPE_GIS_TDSCDMA_VP	6	8	文件开始时间
1127	STATTYPE_GIS_TDSCDMA_VP	7	8	[iduration][int]
1127	STATTYPE_GIS_TDSCDMA_VP	8	8	[idistance][int]
1127	STATTYPE_GIS_TDSCDMA_VP	9	8	iLAC1
1127	STATTYPE_GIS_TDSCDMA_VP	10	8	iCI1
1127	STATTYPE_GIS_TDSCDMA_VP	11	8	isamplenum1
1127	STATTYPE_GIS_TDSCDMA_VP	12	8	ipccpch_rscpmean1
1127	STATTYPE_GIS_TDSCDMA_VP	13	8	iLAC2
1127	STATTYPE_GIS_TDSCDMA_VP	14	8	iCI2
1127	STATTYPE_GIS_TDSCDMA_VP	15	8	isamplenum2
1127	STATTYPE_GIS_TDSCDMA_VP	16	8	ipccpch_rscpmean2
1127	STATTYPE_GIS_TDSCDMA_VP	17	8	iLAC3
1127	STATTYPE_GIS_TDSCDMA_VP	18	8	iCI3
1127	STATTYPE_GIS_TDSCDMA_VP	19	8	isamplenum3
1127	STATTYPE_GIS_TDSCDMA_VP	20	8	ipccpch_rscpmean3
1127	STATTYPE_GIS_TDSCDMA_VP	21	8	iLAC4
1127	STATTYPE_GIS_TDSCDMA_VP	22	8	iCI4
1127	STATTYPE_GIS_TDSCDMA_VP	23	8	isamplenum4
1127	STATTYPE_GIS_TDSCDMA_VP	24	8	ipccpch_rscpmean4
1127	STATTYPE_GIS_TDSCDMA_VP	25	8	[iBler_sample][int]
1127	STATTYPE_GIS_TDSCDMA_VP	26	8	[iBler_total][int]
1127	STATTYPE_GIS_TDSCDMA_VP	27	8	[iBler_err][int]
1128	STATTYPE_TDSCDMA_EVENT	1	8	ifileid int
1128	STATTYPE_TDSCDMA_EVENT	2	8	左上经度(4BYTES)
1128	STATTYPE_TDSCDMA_EVENT	3	8	左上纬度(4BYTES)
1128	STATTYPE_TDSCDMA_EVENT	4	8	右下经度(4BYTES)
1128	STATTYPE_TDSCDMA_EVENT	5	8	右下纬度(4BYTES)
1128	STATTYPE_TDSCDMA_EVENT	6	8	[TD_MO_CallAttempt][int],
1128	STATTYPE_TDSCDMA_EVENT	7	8	[TD_MT_CallAttempt][int],
1128	STATTYPE_TDSCDMA_EVENT	8	8	[TD_GSM_MO_CallAttempt][int],
1128	STATTYPE_TDSCDMA_EVENT	9	8	[TD_GSM_MT_CallAttempt][int],
1128	STATTYPE_TDSCDMA_EVENT	10	8	[TD_MO_Established][int],
1128	STATTYPE_TDSCDMA_EVENT	11	8	[TD_MT_Established][int],
1128	STATTYPE_TDSCDMA_EVENT	12	8	[TD_GSM_MO_Established][int],
1128	STATTYPE_TDSCDMA_EVENT	13	8	[TD_GSM_MT_Established][int],
1128	STATTYPE_TDSCDMA_EVENT	14	8	[TD_MO_Drop][int],
1128	STATTYPE_TDSCDMA_EVENT	15	8	[TD_MT_Drop][int],
1128	STATTYPE_TDSCDMA_EVENT	16	8	[TD_GSM_MO_Drop][int],
1128	STATTYPE_TDSCDMA_EVENT	17	8	[TD_GSM_MT_Drop][int],
1128	STATTYPE_TDSCDMA_EVENT	18	8	[TD_MO_CallFail][int],
1128	STATTYPE_TDSCDMA_EVENT	19	8	[TD_MT_CallFail][int],
1128	STATTYPE_TDSCDMA_EVENT	20	8	[TD_GSM_MO_CallFail][int],
1128	STATTYPE_TDSCDMA_EVENT	21	8	[TD_GSM_MT_CallFail][int],
1128	STATTYPE_TDSCDMA_EVENT	22	8	[TD_MO_Disconnect][int],
1128	STATTYPE_TDSCDMA_EVENT	23	8	[TD_MT_Disconnect][int],
1128	STATTYPE_TDSCDMA_EVENT	24	8	[TD_GSM_MO_Disconnect][int],
1128	STATTYPE_TDSCDMA_EVENT	25	8	[TD_GSM_MT_Disconnect][int],
1128	STATTYPE_TDSCDMA_EVENT	26	8	[TD_MO_Setup][int],
1128	STATTYPE_TDSCDMA_EVENT	27	8	[TD_MO_Setup_Duration][int],
1128	STATTYPE_TDSCDMA_EVENT	28	8	[TD_MT_Setup][int],
1128	STATTYPE_TDSCDMA_EVENT	29	8	[TD_MT_Setup_Duration][int],
1128	STATTYPE_TDSCDMA_EVENT	30	8	[TD_GSM_MO_Setup][int],
1128	STATTYPE_TDSCDMA_EVENT	31	8	[TD_GSM_MO_Setup_Duration][int],
1128	STATTYPE_TDSCDMA_EVENT	32	8	[TD_GSM_MT_Setup][int],
1128	STATTYPE_TDSCDMA_EVENT	33	8	[TD_GSM_MT_Setup_Duration][int],
1128	STATTYPE_TDSCDMA_EVENT	34	8	[TD_LocationUpdate_Request][int],
1128	STATTYPE_TDSCDMA_EVENT	35	8	[TD_GSM_LocationUpdate_Request][int],
1128	STATTYPE_TDSCDMA_EVENT	36	8	[TD_CellReselection_T2G][int],
1128	STATTYPE_TDSCDMA_EVENT	37	8	[TD_CellReselection_G2T][int],
1128	STATTYPE_TDSCDMA_EVENT	38	8	[TD_HandoverRequest_T2G][int],
1128	STATTYPE_TDSCDMA_EVENT	39	8	[TD_HandoverSuccess_T2G][int],
1128	STATTYPE_TDSCDMA_EVENT	40	8	[TD_HandoverFail_T2G][int],
1128	STATTYPE_TDSCDMA_EVENT	41	8	[TD_Handover_T2G_MeanRxlev_Before][int],
1128	STATTYPE_TDSCDMA_EVENT	42	8	[TD_Handover_T2G_MeanRxlev_After][int],
1128	STATTYPE_TDSCDMA_EVENT	43	8	[TD_HandoverRequest_IntraT][int],
1128	STATTYPE_TDSCDMA_EVENT	44	8	[TD_HandoverSuccess_IntraT][int],
1128	STATTYPE_TDSCDMA_EVENT	45	8	[TD_HandoverFail_IntraT][int],
1128	STATTYPE_TDSCDMA_EVENT	46	8	[TD_Handover_IntraT_MeanRxlev_Before][int],
1128	STATTYPE_TDSCDMA_EVENT	47	8	[TD_Handover_IntraT_MeanRxlev_After][int],
1128	STATTYPE_TDSCDMA_EVENT	48	8	[TD_HandoverRequest_Baton][int],
1128	STATTYPE_TDSCDMA_EVENT	49	8	[TD_HandoverSuccess_Baton][int],
1128	STATTYPE_TDSCDMA_EVENT	50	8	[TD_HandoverFail_Baton][int],
1128	STATTYPE_TDSCDMA_EVENT	51	8	[TD_Handover_Baton_MeanRxlev_Before][int],
1128	STATTYPE_TDSCDMA_EVENT	52	8	[TD_Handover_Baton_MeanRxlev_After][int],
1128	STATTYPE_TDSCDMA_EVENT	53	8	[TD_HandoverRequest_IntraG][int],
1128	STATTYPE_TDSCDMA_EVENT	54	8	[TD_HandoverSuccess_IntraG][int],
1128	STATTYPE_TDSCDMA_EVENT	55	8	[TD_HandoverFail_IntraG][int],
1128	STATTYPE_TDSCDMA_EVENT	56	8	[TD_Handover_IntraG_MeanRxlev_Before][int],
1128	STATTYPE_TDSCDMA_EVENT	57	8	[TD_Handover_IntraG_MeanRxlev_After][int],
1128	STATTYPE_TDSCDMA_EVENT	58	8	[TD_CellReselection_T2T][int],
1128	STATTYPE_TDSCDMA_EVENT	59	8	[TD_CellReselection_T2T_MeanRxlve_Before][int],
1128	STATTYPE_TDSCDMA_EVENT	60	8	[TD_CellReselection_T2T_MeanRxlve_After][int]
1129	STATTYPE_GIS_WCDMA_AMR	1	8	ifileid int
1129	STATTYPE_GIS_WCDMA_AMR	2	8	[itllongitude] [int]
1129	STATTYPE_GIS_WCDMA_AMR	3	8	[itllatitude] [int]
1129	STATTYPE_GIS_WCDMA_AMR	4	8	[ibrlongitude] [int]
1129	STATTYPE_GIS_WCDMA_AMR	5	8	[ibrlatitude] [int]
1129	STATTYPE_GIS_WCDMA_AMR	6	8	[iduration_TD] [int]
1129	STATTYPE_GIS_WCDMA_AMR	7	8	[iduration_GSM] [int]
1129	STATTYPE_GIS_WCDMA_AMR	8	8	[idistance_TD] [int]
1129	STATTYPE_GIS_WCDMA_AMR	9	8	[idistance_GSM] [int]
1129	STATTYPE_GIS_WCDMA_AMR	10	8	[iRSCP_F90_ECIO_F12] [int]
1129	STATTYPE_GIS_WCDMA_AMR	11	8	[iRSCP_F85_ECIO_F10] [int]
1129	STATTYPE_GIS_WCDMA_AMR	12	8	[itotalEc_Io_F14] [int]
1129	STATTYPE_GIS_WCDMA_AMR	13	8	[itotalEc_Io_F14_F12] [int]
1129	STATTYPE_GIS_WCDMA_AMR	14	8	[itotalEc_Io_F12_F11] [int]
1129	STATTYPE_GIS_WCDMA_AMR	15	8	[itotalEc_Io_F11_F10] [int]
1129	STATTYPE_GIS_WCDMA_AMR	16	8	[itotalEc_Io_F10_F8] [int]
1129	STATTYPE_GIS_WCDMA_AMR	17	8	[itotalEc_Io_F8] [int]
1129	STATTYPE_GIS_WCDMA_AMR	18	8	[ibestEc_Io_F14] [int]
1129	STATTYPE_GIS_WCDMA_AMR	19	8	[ibestEc_Io_F14_F12] [int]
1129	STATTYPE_GIS_WCDMA_AMR	20	8	[ibestEc_Io_F12_F11] [int]
1129	STATTYPE_GIS_WCDMA_AMR	21	8	[ibestEc_Io_F11_F10] [int]
1129	STATTYPE_GIS_WCDMA_AMR	22	8	[ibestEc_Io_F10_F8] [int]
1129	STATTYPE_GIS_WCDMA_AMR	23	8	[ibestEc_Io_F8] [int]
1129	STATTYPE_GIS_WCDMA_AMR	24	8	[iTotalRSCP_F105] [int]
1129	STATTYPE_GIS_WCDMA_AMR	25	8	[iTotalRSCP_F105_F100] [int]
1129	STATTYPE_GIS_WCDMA_AMR	26	8	[iTotalRSCP_F100_F90] [int]
1129	STATTYPE_GIS_WCDMA_AMR	27	8	[iTotalRSCP_F90_F85] [int]
1129	STATTYPE_GIS_WCDMA_AMR	28	8	[iTotalRSCP_F85_F80] [int]
1129	STATTYPE_GIS_WCDMA_AMR	29	8	[iTotalRSCP_F80] [int]
1129	STATTYPE_GIS_WCDMA_AMR	30	8	[iBestRSCP_F105] [int]
1129	STATTYPE_GIS_WCDMA_AMR	31	8	[iBestRSCP_F105_F100] [int]
1129	STATTYPE_GIS_WCDMA_AMR	32	8	[iBestRSCP_F100_F90] [int]
1129	STATTYPE_GIS_WCDMA_AMR	33	8	[iBestRSCP_F90_F85] [int]
1129	STATTYPE_GIS_WCDMA_AMR	34	8	[iBestRSCP_F85_F80] [int]
1129	STATTYPE_GIS_WCDMA_AMR	35	8	[iBestRSCP_F80] [int]
1129	STATTYPE_GIS_WCDMA_AMR	36	8	[iRxPower_F90] [int]
1129	STATTYPE_GIS_WCDMA_AMR	37	8	[iRxPower_F90_F85] [int]
1129	STATTYPE_GIS_WCDMA_AMR	38	8	[iRxPower_F85_F80] [int]
1129	STATTYPE_GIS_WCDMA_AMR	39	8	[iRxPower_F80_F75] [int]
1129	STATTYPE_GIS_WCDMA_AMR	40	8	[iRxPower_F75] [int]
1129	STATTYPE_GIS_WCDMA_AMR	41	8	[iTxPower_F15] [int]
1129	STATTYPE_GIS_WCDMA_AMR	42	8	[iTxPower_F15_0] [int]
1129	STATTYPE_GIS_WCDMA_AMR	43	8	[iTxPower_0_10] [int]
1129	STATTYPE_GIS_WCDMA_AMR	44	8	[iTxPower_10_20] [int]
1129	STATTYPE_GIS_WCDMA_AMR	45	8	[iTxPower_20] [int]
1129	STATTYPE_GIS_WCDMA_AMR	46	8	[iTxPower_F10_RSCP_F80] [int]
1129	STATTYPE_GIS_WCDMA_AMR	47	8	[iSIR_0] [int]
1129	STATTYPE_GIS_WCDMA_AMR	48	8	[iSIR_0_6] [int]
1129	STATTYPE_GIS_WCDMA_AMR	49	8	[iSIR_6_9] [int]
1129	STATTYPE_GIS_WCDMA_AMR	50	8	[iSIR_9_12] [int]
1129	STATTYPE_GIS_WCDMA_AMR	51	8	[iSIR_12] [int]
1129	STATTYPE_GIS_WCDMA_AMR	52	8	[iBLER_0_1] [int]
1129	STATTYPE_GIS_WCDMA_AMR	53	8	[iBLER_1_2] [int]
1129	STATTYPE_GIS_WCDMA_AMR	54	8	[iBLER_2_3] [int]
1129	STATTYPE_GIS_WCDMA_AMR	55	8	[iBLER_3_4] [int]
1129	STATTYPE_GIS_WCDMA_AMR	56	8	[iBLER_4_5] [int]
1129	STATTYPE_GIS_WCDMA_AMR	57	8	[iBLER_5_100] [int]
1129	STATTYPE_GIS_WCDMA_AMR	58	8	[iActiveSet_1] [int]
1129	STATTYPE_GIS_WCDMA_AMR	59	8	[iActiveSet_2] [int]
1129	STATTYPE_GIS_WCDMA_AMR	60	8	[iActiveSet_3] [int]
1129	STATTYPE_GIS_WCDMA_AMR	61	8	[iActiveSet_4] [int]
1129	STATTYPE_GIS_WCDMA_AMR	62	8	[iActiveSet_5] [int]
1129	STATTYPE_GIS_WCDMA_AMR	63	8	[iActiveSet_6] [int]
1129	STATTYPE_GIS_WCDMA_AMR	64	8	[iActiveSet_other] [int]
1129	STATTYPE_GIS_WCDMA_AMR	65	8	[iMOS_sample] [int]
1129	STATTYPE_GIS_WCDMA_AMR	66	8	[iMOS_total] [int]
1129	STATTYPE_GIS_WCDMA_AMR	67	8	[iMOS_20] [int]
1129	STATTYPE_GIS_WCDMA_AMR	68	8	[iMOS_20_25] [int]
1129	STATTYPE_GIS_WCDMA_AMR	69	8	[iMOS_25_30] [int]
1129	STATTYPE_GIS_WCDMA_AMR	70	8	[iMOS_30_35] [int]
1129	STATTYPE_GIS_WCDMA_AMR	71	8	[iMOS_35_40] [int]
1129	STATTYPE_GIS_WCDMA_AMR	72	8	[iMOS_40_45] [int]
1129	STATTYPE_GIS_WCDMA_AMR	73	8	[iMOS_45] [int] NULL
1129	STATTYPE_GIS_WCDMA_AMR	74	8	[iRscpTxNum_80_10] [int]
1129	STATTYPE_GIS_WCDMA_AMR	75	8	[iRscpNum_80] [int]
1130	STATTYPE_GIS_WCDMA_VP	1	8	ifileid int
1130	STATTYPE_GIS_WCDMA_VP	2	8	[itllongitude] [int]
1130	STATTYPE_GIS_WCDMA_VP	3	8	[itllatitude] [int]
1130	STATTYPE_GIS_WCDMA_VP	4	8	[ibrlongitude] [int]
1130	STATTYPE_GIS_WCDMA_VP	5	8	[ibrlatitude] [int]
1130	STATTYPE_GIS_WCDMA_VP	6	8	[iduration_TD] [int]
1130	STATTYPE_GIS_WCDMA_VP	7	8	[iduration_GSM] [int]
1130	STATTYPE_GIS_WCDMA_VP	8	8	[idistance_TD] [int]
1130	STATTYPE_GIS_WCDMA_VP	9	8	[idistance_GSM] [int]
1130	STATTYPE_GIS_WCDMA_VP	10	8	[iRSCP_F90_ECIO_F12] [int]
1130	STATTYPE_GIS_WCDMA_VP	11	8	[iRSCP_F85_ECIO_F10] [int]
1130	STATTYPE_GIS_WCDMA_VP	12	8	[itotalEc_Io_F14] [int]
1130	STATTYPE_GIS_WCDMA_VP	13	8	[itotalEc_Io_F14_F12] [int]
1130	STATTYPE_GIS_WCDMA_VP	14	8	[itotalEc_Io_F12_F11] [int]
1130	STATTYPE_GIS_WCDMA_VP	15	8	[itotalEc_Io_F11_F10] [int]
1130	STATTYPE_GIS_WCDMA_VP	16	8	[itotalEc_Io_F10_F8] [int]
1130	STATTYPE_GIS_WCDMA_VP	17	8	[itotalEc_Io_F8] [int]
1130	STATTYPE_GIS_WCDMA_VP	18	8	[ibestEc_Io_F14] [int]
1130	STATTYPE_GIS_WCDMA_VP	19	8	[ibestEc_Io_F14_F12] [int]
1130	STATTYPE_GIS_WCDMA_VP	20	8	[ibestEc_Io_F12_F11] [int]
1130	STATTYPE_GIS_WCDMA_VP	21	8	[ibestEc_Io_F11_F10] [int]
1130	STATTYPE_GIS_WCDMA_VP	22	8	[ibestEc_Io_F10_F8] [int]
1130	STATTYPE_GIS_WCDMA_VP	23	8	[ibestEc_Io_F8] [int]
1130	STATTYPE_GIS_WCDMA_VP	24	8	[iTotalRSCP_F105] [int]
1130	STATTYPE_GIS_WCDMA_VP	25	8	[iTotalRSCP_F105_F100] [int]
1130	STATTYPE_GIS_WCDMA_VP	26	8	[iTotalRSCP_F100_F90] [int]
1130	STATTYPE_GIS_WCDMA_VP	27	8	[iTotalRSCP_F90_F85] [int]
1130	STATTYPE_GIS_WCDMA_VP	28	8	[iTotalRSCP_F85_F80] [int]
1130	STATTYPE_GIS_WCDMA_VP	29	8	[iTotalRSCP_F80] [int]
1130	STATTYPE_GIS_WCDMA_VP	30	8	[iBestRSCP_F105] [int]
1130	STATTYPE_GIS_WCDMA_VP	31	8	[iBestRSCP_F105_F100] [int]
1130	STATTYPE_GIS_WCDMA_VP	32	8	[iBestRSCP_F100_F90] [int]
1130	STATTYPE_GIS_WCDMA_VP	33	8	[iBestRSCP_F90_F85] [int]
1130	STATTYPE_GIS_WCDMA_VP	34	8	[iBestRSCP_F85_F80] [int]
1130	STATTYPE_GIS_WCDMA_VP	35	8	[iBestRSCP_F80] [int]
1130	STATTYPE_GIS_WCDMA_VP	36	8	[iRxPower_F90] [int]
1130	STATTYPE_GIS_WCDMA_VP	37	8	[iRxPower_F90_F85] [int]
1130	STATTYPE_GIS_WCDMA_VP	38	8	[iRxPower_F85_F80] [int]
1130	STATTYPE_GIS_WCDMA_VP	39	8	[iRxPower_F80_F75] [int]
1130	STATTYPE_GIS_WCDMA_VP	40	8	[iRxPower_F75] [int]
1130	STATTYPE_GIS_WCDMA_VP	41	8	[iTxPower_F15] [int]
1130	STATTYPE_GIS_WCDMA_VP	42	8	[iTxPower_F15_0] [int]
1130	STATTYPE_GIS_WCDMA_VP	43	8	[iTxPower_0_10] [int]
1130	STATTYPE_GIS_WCDMA_VP	44	8	[iTxPower_10_20] [int]
1130	STATTYPE_GIS_WCDMA_VP	45	8	[iTxPower_20] [int]
1130	STATTYPE_GIS_WCDMA_VP	46	8	[iTxPower_F10_RSCP_F80] [int]
1130	STATTYPE_GIS_WCDMA_VP	47	8	[iSIR_0] [int]
1130	STATTYPE_GIS_WCDMA_VP	48	8	[iSIR_0_6] [int]
1130	STATTYPE_GIS_WCDMA_VP	49	8	[iSIR_6_9] [int]
1130	STATTYPE_GIS_WCDMA_VP	50	8	[iSIR_9_12] [int]
1130	STATTYPE_GIS_WCDMA_VP	51	8	[iSIR_12] [int]
1130	STATTYPE_GIS_WCDMA_VP	52	8	[iBLER_0_1] [int]
1130	STATTYPE_GIS_WCDMA_VP	53	8	[iBLER_1_2] [int]
1130	STATTYPE_GIS_WCDMA_VP	54	8	[iBLER_2_3] [int]
1130	STATTYPE_GIS_WCDMA_VP	55	8	[iBLER_3_4] [int]
1130	STATTYPE_GIS_WCDMA_VP	56	8	[iBLER_4_5] [int]
1130	STATTYPE_GIS_WCDMA_VP	57	8	[iBLER_5_100] [int]
1130	STATTYPE_GIS_WCDMA_VP	58	8	[iActiveSet_1] [int]
1130	STATTYPE_GIS_WCDMA_VP	59	8	[iActiveSet_2] [int]
1130	STATTYPE_GIS_WCDMA_VP	60	8	[iActiveSet_3] [int]
1130	STATTYPE_GIS_WCDMA_VP	61	8	[iActiveSet_4] [int]
1130	STATTYPE_GIS_WCDMA_VP	62	8	[iActiveSet_5] [int]
1130	STATTYPE_GIS_WCDMA_VP	63	8	[iActiveSet_6] [int]
1130	STATTYPE_GIS_WCDMA_VP	64	8	[iActiveSet_other] [int]
1130	STATTYPE_GIS_WCDMA_VP	65	8	[iMOS_sample] [int]
1130	STATTYPE_GIS_WCDMA_VP	66	8	[iMOS_total] [int]
1130	STATTYPE_GIS_WCDMA_VP	67	8	[iMOS_20] [int]
1130	STATTYPE_GIS_WCDMA_VP	68	8	[iMOS_20_25] [int]
1130	STATTYPE_GIS_WCDMA_VP	69	8	[iMOS_25_30] [int]
1130	STATTYPE_GIS_WCDMA_VP	70	8	[iMOS_30_35] [int]
1130	STATTYPE_GIS_WCDMA_VP	71	8	[iMOS_35_40] [int]
1130	STATTYPE_GIS_WCDMA_VP	72	8	[iMOS_40_45] [int]
1130	STATTYPE_GIS_WCDMA_VP	73	8	[iMOS_45] [int] NULL
1130	STATTYPE_GIS_WCDMA_VP	74	8	[iRscpTxNum_80_10] [int]
1130	STATTYPE_GIS_WCDMA_VP	75	8	[iRscpNum_80] [int]
1131	STATTYPE_GIS_WCDMA_PS	1	8	[ifileid] [int]
1131	STATTYPE_GIS_WCDMA_PS	2	8	[itllongitude] [int]
1131	STATTYPE_GIS_WCDMA_PS	3	8	[itllatitude] [int]
1131	STATTYPE_GIS_WCDMA_PS	4	8	[ibrlongitude] [int]
1131	STATTYPE_GIS_WCDMA_PS	5	8	[ibrlatitude] [int]
1131	STATTYPE_GIS_WCDMA_PS	6	8	[iduration_TD] [int]
1131	STATTYPE_GIS_WCDMA_PS	7	8	[iduration_GSM] [int]
1131	STATTYPE_GIS_WCDMA_PS	8	8	[idistance_TD] [int]
1131	STATTYPE_GIS_WCDMA_PS	9	8	[idistance_GSM] [int]
1131	STATTYPE_GIS_WCDMA_PS	10	8	[iRSCP_F90_ECIO_F12] [int]
1131	STATTYPE_GIS_WCDMA_PS	11	8	[iRSCP_F85_ECIO_F10] [int]
1131	STATTYPE_GIS_WCDMA_PS	12	8	[itotalEc_Io_F14] [int]
1131	STATTYPE_GIS_WCDMA_PS	13	8	[itotalEc_Io_F14_F12] [int]
1131	STATTYPE_GIS_WCDMA_PS	14	8	[itotalEc_Io_F12_F11] [int]
1131	STATTYPE_GIS_WCDMA_PS	15	8	[itotalEc_Io_F11_F10] [int]
1131	STATTYPE_GIS_WCDMA_PS	16	8	[itotalEc_Io_F10_F8] [int]
1131	STATTYPE_GIS_WCDMA_PS	17	8	[itotalEc_Io_F8] [int]
1131	STATTYPE_GIS_WCDMA_PS	18	8	[ibestEc_Io_F14] [int]
1131	STATTYPE_GIS_WCDMA_PS	19	8	[ibestEc_Io_F14_F12] [int]
1131	STATTYPE_GIS_WCDMA_PS	20	8	[ibestEc_Io_F12_F11] [int]
1131	STATTYPE_GIS_WCDMA_PS	21	8	[ibestEc_Io_F11_F10] [int]
1131	STATTYPE_GIS_WCDMA_PS	22	8	[ibestEc_Io_F10_F8] [int]
1131	STATTYPE_GIS_WCDMA_PS	23	8	[ibestEc_Io_F8] [int]
1131	STATTYPE_GIS_WCDMA_PS	24	8	[iTotalRSCP_F105] [int]
1131	STATTYPE_GIS_WCDMA_PS	25	8	[iTotalRSCP_F105_F100] [int]
1131	STATTYPE_GIS_WCDMA_PS	26	8	[iTotalRSCP_F100_F90] [int]
1131	STATTYPE_GIS_WCDMA_PS	27	8	[iTotalRSCP_F90_F85] [int]
1131	STATTYPE_GIS_WCDMA_PS	28	8	[iTotalRSCP_F85_F80] [int]
1131	STATTYPE_GIS_WCDMA_PS	29	8	[iTotalRSCP_F80] [int]
1131	STATTYPE_GIS_WCDMA_PS	30	8	[iBestRSCP_F105] [int]
1131	STATTYPE_GIS_WCDMA_PS	31	8	[iBestRSCP_F105_F100] [int]
1131	STATTYPE_GIS_WCDMA_PS	32	8	[iBestRSCP_F100_F90] [int]
1131	STATTYPE_GIS_WCDMA_PS	33	8	[iBestRSCP_F90_F85] [int]
1131	STATTYPE_GIS_WCDMA_PS	34	8	[iBestRSCP_F85_F80] [int]
1131	STATTYPE_GIS_WCDMA_PS	35	8	[iBestRSCP_F80] [int]
1131	STATTYPE_GIS_WCDMA_PS	36	8	[iRxPower_F90] [int]
1131	STATTYPE_GIS_WCDMA_PS	37	8	[iRxPower_F90_F85] [int]
1131	STATTYPE_GIS_WCDMA_PS	38	8	[iRxPower_F85_F80] [int]
1131	STATTYPE_GIS_WCDMA_PS	39	8	[iRxPower_F80_F75] [int]
1131	STATTYPE_GIS_WCDMA_PS	40	8	[iRxPower_F75] [int]
1131	STATTYPE_GIS_WCDMA_PS	41	8	[iTxPower_F15] [int]
1131	STATTYPE_GIS_WCDMA_PS	42	8	[iTxPower_F15_0] [int]
1131	STATTYPE_GIS_WCDMA_PS	43	8	[iTxPower_0_10] [int]
1131	STATTYPE_GIS_WCDMA_PS	44	8	[iTxPower_10_20] [int]
1131	STATTYPE_GIS_WCDMA_PS	45	8	[iTxPower_20] [int]
1131	STATTYPE_GIS_WCDMA_PS	46	8	[iTxPower_F10_RSCP_F80] [int]
1131	STATTYPE_GIS_WCDMA_PS	47	8	[iSIR_0] [int]
1131	STATTYPE_GIS_WCDMA_PS	48	8	[iSIR_0_6] [int]
1131	STATTYPE_GIS_WCDMA_PS	49	8	[iSIR_6_9] [int]
1131	STATTYPE_GIS_WCDMA_PS	50	8	[iSIR_9_12] [int]
1131	STATTYPE_GIS_WCDMA_PS	51	8	[iSIR_12] [int]
1131	STATTYPE_GIS_WCDMA_PS	52	8	[iActiveSet_1] [int]
1131	STATTYPE_GIS_WCDMA_PS	53	8	[iActiveSet_2] [int]
1131	STATTYPE_GIS_WCDMA_PS	54	8	[iActiveSet_3] [int]
1131	STATTYPE_GIS_WCDMA_PS	55	8	[iActiveSet_4] [int]
1131	STATTYPE_GIS_WCDMA_PS	56	8	[iActiveSet_5] [int]
1131	STATTYPE_GIS_WCDMA_PS	57	8	[iActiveSet_6] [int]
1131	STATTYPE_GIS_WCDMA_PS	58	8	[iActiveSet_other] [int]
1131	STATTYPE_GIS_WCDMA_PS	59	8	[iMOS_sample] [int]
1131	STATTYPE_GIS_WCDMA_PS	60	8	[iMOS_total] [int]
1131	STATTYPE_GIS_WCDMA_PS	61	8	[iMOS_20] [int]
1131	STATTYPE_GIS_WCDMA_PS	62	8	[iMOS_20_25] [int]
1131	STATTYPE_GIS_WCDMA_PS	63	8	[iMOS_25_30] [int]
1131	STATTYPE_GIS_WCDMA_PS	64	8	[iMOS_30_35] [int]
1131	STATTYPE_GIS_WCDMA_PS	65	8	[iMOS_35_40] [int]
1131	STATTYPE_GIS_WCDMA_PS	66	8	[iMOS_40_45] [int]
1131	STATTYPE_GIS_WCDMA_PS	67	8	[iMOS_45] [int]
1131	STATTYPE_GIS_WCDMA_PS	68	8	[iBLERD_samplenum_HSDPA] [int]
1131	STATTYPE_GIS_WCDMA_PS	69	8	[iBLERD_samplenum_R4] [int]
1131	STATTYPE_GIS_WCDMA_PS	70	8	[iBLERD_samplenum_GPRSEDGE] [int]
1131	STATTYPE_GIS_WCDMA_PS	71	5	[fBLERD_total_HSDPA] [float]
1131	STATTYPE_GIS_WCDMA_PS	72	5	[fBLERD_total_R4] [float]
1131	STATTYPE_GIS_WCDMA_PS	73	5	[fBLERD_total_GPRSEDGE] [float]
1131	STATTYPE_GIS_WCDMA_PS	74	8	[iFTP_UL_BYTES] [int]
1131	STATTYPE_GIS_WCDMA_PS	75	8	[iFTP_UL_TIME] [int]
1131	STATTYPE_GIS_WCDMA_PS	76	8	[iFTP_APP_UL_BYTES] [int]
1131	STATTYPE_GIS_WCDMA_PS	77	8	[iFTP_RLC_UL_BYTES] [int]
1131	STATTYPE_GIS_WCDMA_PS	78	8	[iFTP_MAC_UL_BYTES] [int]
1131	STATTYPE_GIS_WCDMA_PS	79	8	[iFTP_UL_TIME_HSUPA] [int]
1131	STATTYPE_GIS_WCDMA_PS	80	8	[iFTP_UL_TIME_R4] [int]
1131	STATTYPE_GIS_WCDMA_PS	81	8	[iFTP_UL_TIME_EDGE] [int]
1131	STATTYPE_GIS_WCDMA_PS	82	8	[iFTP_UL_BYTES_HSUPA] [int]
1131	STATTYPE_GIS_WCDMA_PS	83	8	[iFTP_UL_BYTES_R4] [int]
1131	STATTYPE_GIS_WCDMA_PS	84	8	[iFTP_UL_BYTES_EDGE] [int]
1131	STATTYPE_GIS_WCDMA_PS	85	8	[iFTP_DL_BYTES] [int]
1131	STATTYPE_GIS_WCDMA_PS	86	8	[iFTP_DL_TIME] [int]
1131	STATTYPE_GIS_WCDMA_PS	87	8	[iFTP_APP_DL_BYTES] [int]
1131	STATTYPE_GIS_WCDMA_PS	88	8	[iFTP_RLC_DL_BYTES] [int]
1131	STATTYPE_GIS_WCDMA_PS	89	8	[iFTP_MAC_DL_BYTES] [int]
1131	STATTYPE_GIS_WCDMA_PS	90	8	[iFTP_DL_TIME_R4] [int]
1131	STATTYPE_GIS_WCDMA_PS	91	8	[iFTP_DL_TIME_EDGE] [int]
1131	STATTYPE_GIS_WCDMA_PS	92	8	[iFTP_DL_BYTES_R4] [int]
1131	STATTYPE_GIS_WCDMA_PS	93	8	[iFTP_DL_BYTES_EDGE] [int]
1131	STATTYPE_GIS_WCDMA_PS	94	8	[iRscpTxNum_80_10] [int]
1131	STATTYPE_GIS_WCDMA_PS	95	8	[iRscpNum_80] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	1	8	[ifileid] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	2	8	[itllongitude] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	3	8	[itllatitude] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	4	8	[ibrlongitude] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	5	8	[ibrlatitude] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	6	8	[iduration_TD] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	7	8	[iduration_GSM] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	8	8	[idistance_TD] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	9	8	[idistance_GSM] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	10	8	[iRSCP_F90_ECIO_F12] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	11	8	[iRSCP_F85_ECIO_F10] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	12	8	[itotalEc_Io_F14] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	13	8	[itotalEc_Io_F14_F12] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	14	8	[itotalEc_Io_F12_F11] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	15	8	[itotalEc_Io_F11_F10] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	16	8	[itotalEc_Io_F10_F8] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	17	8	[itotalEc_Io_F8] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	18	8	[ibestEc_Io_F14] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	19	8	[ibestEc_Io_F14_F12] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	20	8	[ibestEc_Io_F12_F11] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	21	8	[ibestEc_Io_F11_F10] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	22	8	[ibestEc_Io_F10_F8] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	23	8	[ibestEc_Io_F8] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	24	8	[iTotalRSCP_F105] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	25	8	[iTotalRSCP_F105_F100] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	26	8	[iTotalRSCP_F100_F90] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	27	8	[iTotalRSCP_F90_F85] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	28	8	[iTotalRSCP_F85_F80] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	29	8	[iTotalRSCP_F80] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	30	8	[iBestRSCP_F105] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	31	8	[iBestRSCP_F105_F100] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	32	8	[iBestRSCP_F100_F90] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	33	8	[iBestRSCP_F90_F85] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	34	8	[iBestRSCP_F85_F80] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	35	8	[iBestRSCP_F80] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	36	8	[iRxPower_F90] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	37	8	[iRxPower_F90_F85] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	38	8	[iRxPower_F85_F80] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	39	8	[iRxPower_F80_F75] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	40	8	[iRxPower_F75] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	41	8	[iTxPower_F15] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	42	8	[iTxPower_F15_0] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	43	8	[iTxPower_0_10] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	44	8	[iTxPower_10_20] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	45	8	[iTxPower_20] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	46	8	[iTxPower_F10_RSCP_F80] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	47	8	[iSIR_0] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	48	8	[iSIR_0_6] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	49	8	[iSIR_6_9] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	50	8	[iSIR_9_12] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	51	8	[iSIR_12] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	52	8	[iActiveSet_1] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	53	8	[iActiveSet_2] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	54	8	[iActiveSet_3] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	55	8	[iActiveSet_4] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	56	8	[iActiveSet_5] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	57	8	[iActiveSet_6] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	58	8	[iActiveSet_other] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	59	8	[iMOS_sample] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	60	8	[iMOS_total] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	61	8	[iMOS_20] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	62	8	[iMOS_20_25] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	63	8	[iMOS_25_30] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	64	8	[iMOS_30_35] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	65	8	[iMOS_35_40] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	66	8	[iMOS_40_45] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	67	8	[iMOS_45] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	68	8	[iBLERD_samplenum_HSDPA] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	69	8	[iBLERD_samplenum_R4] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	70	8	[iBLERD_samplenum_GPRSEDGE] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	71	5	[fBLERD_total_HSDPA] [float]
1132	STATTYPE_GIS_WCDMA_PSHS	72	5	[fBLERD_total_R4] [float]
1132	STATTYPE_GIS_WCDMA_PSHS	73	5	[fBLERD_total_GPRSEDGE] [float]
1132	STATTYPE_GIS_WCDMA_PSHS	74	8	[iFTP_UL_BYTES] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	75	8	[iFTP_UL_TIME] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	76	8	[iFTP_APP_UL_BYTES] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	77	8	[iFTP_RLC_UL_BYTES] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	78	8	[iFTP_MAC_UL_BYTES] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	79	8	[iFTP_UL_TIME_HSUPA] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	80	8	[iFTP_UL_TIME_R4] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	81	8	[iFTP_UL_TIME_EDGE] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	82	8	[iFTP_UL_BYTES_HSUPA] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	83	8	[iFTP_UL_BYTES_R4] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	84	8	[iFTP_UL_BYTES_EDGE] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	85	8	[iFTP_DL_BYTES] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	86	8	[iFTP_DL_TIME] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	87	8	[iFTP_APP_DL_BYTES] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	88	8	[iFTP_RLC_DL_BYTES] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	89	8	[iFTP_MAC_DL_BYTES] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	90	8	[iFTP_DL_TIME_HSDPA] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	91	8	[iFTP_DL_TIME_R4] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	92	8	[iFTP_DL_TIME_EDGE] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	93	8	[iFTP_DL_BYTES_HSDPA] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	94	8	[iFTP_DL_BYTES_R4] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	95	8	[iFTP_DL_BYTES_EDGE] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	96	8	[iHS_16QAM_Rate_sample] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	97	8	[iHS_16QAM_Rate_0_5] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	98	8	[iHS_16QAM_Rate_5_10] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	99	8	[iHS_16QAM_Rate_10_20] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	100	8	[iHS_16QAM_Rate_20_50] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	101	8	[iHS_16QAM_Rate_50] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	102	8	[iMean_HSDPA_CQI_sample] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	103	8	[iMean_HSDPA_CQI_10] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	104	8	[iMean_HSDPA_CQI_10_13] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	105	8	[iMean_HSDPA_CQI_13_16] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	106	8	[iMean_HSDPA_CQI_16_19] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	107	8	[iMean_HSDPA_CQI_19_22] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	108	8	[iMean_HSDPA_CQI_22_25] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	109	8	[iMean_HSDPA_CQI_25_28] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	110	8	[iMean_HSDPA_CQI_28] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	111	8	[fHsupa_mean] [float]
1132	STATTYPE_GIS_WCDMA_PSHS	112	8	[iHsupa_sample] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	113	8	[fHsdpa_mean] [float]
1132	STATTYPE_GIS_WCDMA_PSHS	114	8	[iHsdpa_sample]
1132	STATTYPE_GIS_WCDMA_PSHS	115	8	[iRscpTxNum_80_10] [int]
1132	STATTYPE_GIS_WCDMA_PSHS	116	8	[iRscpNum_80] [int]
1133	STATTYPE_GIS_D_GPRS_NEW	1	8	ifileid int
1133	STATTYPE_GIS_D_GPRS_NEW	2	8	itllongitude int
1133	STATTYPE_GIS_D_GPRS_NEW	3	8	itllatitude int
1133	STATTYPE_GIS_D_GPRS_NEW	4	8	ibrlongitude int
1133	STATTYPE_GIS_D_GPRS_NEW	5	8	ibrlatitude int
1133	STATTYPE_GIS_D_GPRS_NEW	6	8	ifiletime int
1133	STATTYPE_GIS_D_GPRS_NEW	7	8	idistance int
1133	STATTYPE_GIS_D_GPRS_NEW	8	8	iduration int
1133	STATTYPE_GIS_D_GPRS_NEW	9	8	isamplenum int 
1133	STATTYPE_GIS_D_GPRS_NEW	10	8	iLAC1 int
1133	STATTYPE_GIS_D_GPRS_NEW	11	8	wRAC1 int
1133	STATTYPE_GIS_D_GPRS_NEW	12	8	iCI1 int
1133	STATTYPE_GIS_D_GPRS_NEW	13	8	iLAC2 int
1133	STATTYPE_GIS_D_GPRS_NEW	14	8	wRAC2 int
1133	STATTYPE_GIS_D_GPRS_NEW	15	8	iCI2 int
1133	STATTYPE_GIS_D_GPRS_NEW	16	8	iLAC3 int
1133	STATTYPE_GIS_D_GPRS_NEW	17	8	wRAC3 int
1133	STATTYPE_GIS_D_GPRS_NEW	18	8	iCI3 int
1133	STATTYPE_GIS_D_GPRS_NEW	19	8	iLAC4 int
1133	STATTYPE_GIS_D_GPRS_NEW	20	8	wRAC4 int
1133	STATTYPE_GIS_D_GPRS_NEW	21	8	iCI4 int
1133	STATTYPE_GIS_D_GPRS_NEW	22	8	iduration_GPRS int
1133	STATTYPE_GIS_D_GPRS_NEW	23	8	iduration_EDGE int
1133	STATTYPE_GIS_D_GPRS_NEW	24	8	iduration_3G int
1133	STATTYPE_GIS_D_GPRS_NEW	25	8	idistance_GPRS int
1133	STATTYPE_GIS_D_GPRS_NEW	26	8	idistance_EDGE int
1133	STATTYPE_GIS_D_GPRS_NEW	27	8	idistance_3G int
1133	STATTYPE_GIS_D_GPRS_NEW	28	9	CELLSTAT_INFO1 image
1133	STATTYPE_GIS_D_GPRS_NEW	29	9	CELLSTAT_INFO2 image
1133	STATTYPE_GIS_D_GPRS_NEW	30	9	CELLSTAT_INFO3 image
1133	STATTYPE_GIS_D_GPRS_NEW	31	9	CELLSTAT_INFO4 image
1133	STATTYPE_GIS_D_GPRS_NEW	32	9	CELLSTAT_INFO5 image
1133	STATTYPE_GIS_D_GPRS_NEW	33	9	CELLSTAT_INFO6 image
1133	STATTYPE_GIS_D_GPRS_NEW	34	9	CELLSTAT_INFO7 image
1133	STATTYPE_GIS_D_GPRS_NEW	35	9	CELLSTAT_INFO8 image
1133	STATTYPE_GIS_D_GPRS_NEW	36	9	RSCP_INFO1 image
1133	STATTYPE_GIS_D_GPRS_NEW	37	9	RSCP_INFO2 image
1133	STATTYPE_GIS_D_GPRS_NEW	38	9	RSCP_INFO3 image
1133	STATTYPE_GIS_D_GPRS_NEW	39	9	RSCP_INFO4 image
1133	STATTYPE_GIS_D_GPRS_NEW	40	9	RSCP_INFO5 image
1133	STATTYPE_GIS_D_GPRS_NEW	41	9	RSCP_INFO6 image
1133	STATTYPE_GIS_D_GPRS_NEW	42	9	RSCP_INFO7 image
1133	STATTYPE_GIS_D_GPRS_NEW	43	9	RSCP_INFO8 image
1133	STATTYPE_GIS_D_GPRS_NEW	44	9	RLC_INFO1 image
1133	STATTYPE_GIS_D_GPRS_NEW	45	9	RLC_INFO2 image
1133	STATTYPE_GIS_D_GPRS_NEW	46	9	RLC_INFO3 image
1133	STATTYPE_GIS_D_GPRS_NEW	47	9	RLC_INFO4 image
1133	STATTYPE_GIS_D_GPRS_NEW	48	9	RLC_INFO5 image
1133	STATTYPE_GIS_D_GPRS_NEW	49	9	RLC_INFO6 image
1133	STATTYPE_GIS_D_GPRS_NEW	50	9	RLC_INFO7 image
1133	STATTYPE_GIS_D_GPRS_NEW	51	9	RLC_INFO8 image
1133	STATTYPE_GIS_D_GPRS_NEW	52	9	BLER_INFO1 image
1133	STATTYPE_GIS_D_GPRS_NEW	53	9	BLER_INFO2 image
1133	STATTYPE_GIS_D_GPRS_NEW	54	9	BLER_INFO3 image
1133	STATTYPE_GIS_D_GPRS_NEW	55	9	BLER_INFO4 image
1133	STATTYPE_GIS_D_GPRS_NEW	56	9	BLER_INFO5 image
1133	STATTYPE_GIS_D_GPRS_NEW	57	9	BLER_INFO6 image
1133	STATTYPE_GIS_D_GPRS_NEW	58	9	BLER_INFO7 image
1133	STATTYPE_GIS_D_GPRS_NEW	59	9	BLER_INFO8 image
1133	STATTYPE_GIS_D_GPRS_NEW	60	9	APP_INFO1 image
1133	STATTYPE_GIS_D_GPRS_NEW	61	9	APP_INFO2
1133	STATTYPE_GIS_D_GPRS_NEW	62	9	APP_INFO3
1133	STATTYPE_GIS_D_GPRS_NEW	63	9	APP_INFO4
1133	STATTYPE_GIS_D_GPRS_NEW	64	9	APP_INFO5
1133	STATTYPE_GIS_D_GPRS_NEW	65	9	APP_INFO6
1133	STATTYPE_GIS_D_GPRS_NEW	66	9	APP_INFO7
1133	STATTYPE_GIS_D_GPRS_NEW	67	9	APP_INFO8
1133	STATTYPE_GIS_D_GPRS_NEW	68	9	FTP_SLOT_INFO1 image
1133	STATTYPE_GIS_D_GPRS_NEW	69	9	FTP_SLOT_INFO2
1133	STATTYPE_GIS_D_GPRS_NEW	70	9	FTP_SLOT_INFO3
1133	STATTYPE_GIS_D_GPRS_NEW	71	9	FTP_SLOT_INFO4
1133	STATTYPE_GIS_D_GPRS_NEW	72	9	FTP_SLOT_INFO5
1133	STATTYPE_GIS_D_GPRS_NEW	73	9	FTP_SLOT_INFO6
1133	STATTYPE_GIS_D_GPRS_NEW	74	9	FTP_SLOT_INFO7
1133	STATTYPE_GIS_D_GPRS_NEW	75	9	FTP_SLOT_INFO8
1133	STATTYPE_GIS_D_GPRS_NEW	76	9	MCS_INFO1 image
1133	STATTYPE_GIS_D_GPRS_NEW	77	9	MCS_INFO2 
1133	STATTYPE_GIS_D_GPRS_NEW	78	9	MCS_INFO3 
1133	STATTYPE_GIS_D_GPRS_NEW	79	9	MCS_INFO4
1133	STATTYPE_GIS_D_GPRS_NEW	80	9	MCS_INFO5
1133	STATTYPE_GIS_D_GPRS_NEW	81	9	MCS_INFO6
1133	STATTYPE_GIS_D_GPRS_NEW	82	9	MCS_INFO7
1133	STATTYPE_GIS_D_GPRS_NEW	83	9	MCS_INFO8
1133	STATTYPE_GIS_D_GPRS_NEW	84	9	RESERVED_INFO1 image
1133	STATTYPE_GIS_D_GPRS_NEW	85	9	RESERVED_INFO2
1133	STATTYPE_GIS_D_GPRS_NEW	86	9	RESERVED_INFO3
1133	STATTYPE_GIS_D_GPRS_NEW	87	9	RESERVED_INFO4
1133	STATTYPE_GIS_D_GPRS_NEW	88	9	RESERVED_INFO5
1133	STATTYPE_GIS_D_GPRS_NEW	89	9	RESERVED_INFO6
1133	STATTYPE_GIS_D_GPRS_NEW	90	9	RESERVED_INFO7
1133	STATTYPE_GIS_D_GPRS_NEW	91	9	RESERVED_INFO8
1134	STATTYPE_NOGIS_EVENT	1	8	[ifileid][int]
1134	STATTYPE_NOGIS_EVENT	2	8	[iareatype][int]
1134	STATTYPE_NOGIS_EVENT	3	8	[iareaid][int]
1134	STATTYPE_NOGIS_EVENT	4	8	[itesttype][int]
1134	STATTYPE_NOGIS_EVENT	5	8	[isamplenum][int]
1134	STATTYPE_NOGIS_EVENT	6	8	[iEventID][int]
1134	STATTYPE_NOGIS_EVENT	7	14	[ivalue1][int]
1134	STATTYPE_NOGIS_EVENT	8	14	[ivalue2][int]
1134	STATTYPE_NOGIS_EVENT	9	14	[ivalue3][int]
1134	STATTYPE_NOGIS_EVENT	10	14	[ivalue4][int]
1134	STATTYPE_NOGIS_EVENT	11	14	[ivalue5][int]
1134	STATTYPE_NOGIS_EVENT	12	14	[ivalue6][int]
1134	STATTYPE_NOGIS_EVENT	13	14	[ivalue7][int]
1134	STATTYPE_NOGIS_EVENT	14	14	[ivalue8][int]
1134	STATTYPE_NOGIS_EVENT	15	14	[ivalue9][int]
1134	STATTYPE_NOGIS_EVENT	16	14	[ivalue10][int]
1135	STATTYPE_KPI_LOG_LN	1	8	[ifileid] [int]
1135	STATTYPE_KPI_LOG_LN	2	8	[itesttype] [int]
1135	STATTYPE_KPI_LOG_LN	3	8	[bms] [tinyint]
1135	STATTYPE_KPI_LOG_LN	4	8	[ifiletime] [int]
1135	STATTYPE_KPI_LOG_LN	5	8	[iduration] [int]
1135	STATTYPE_KPI_LOG_LN	6	8	[idistance] [int]
1135	STATTYPE_KPI_LOG_LN	7	8	[isamplenum] [int]
1135	STATTYPE_KPI_LOG_LN	8	8	[itllongitude] [int]
1135	STATTYPE_KPI_LOG_LN	9	8	[itllatitude] [int]
1135	STATTYPE_KPI_LOG_LN	10	8	[ibrlongitude] [int]
1135	STATTYPE_KPI_LOG_LN	11	8	[ibrlatitude] [int]
1135	STATTYPE_KPI_LOG_LN	12	5	[rxlev_total] [float]
1135	STATTYPE_KPI_LOG_LN	13	8	[rxlev_count] [int]
1135	STATTYPE_KPI_LOG_LN	14	8	[rxqual_total] [int]
1135	STATTYPE_KPI_LOG_LN	15	8	[rxqual_count] [int]
1135	STATTYPE_KPI_LOG_LN	16	5	[rxagc_total] [float]
1135	STATTYPE_KPI_LOG_LN	17	8	[rxagc_count] [int]
1135	STATTYPE_KPI_LOG_LN	18	5	[ffer_total] [float]
1135	STATTYPE_KPI_LOG_LN	19	8	[ffer_count] [int]
1136	STATTYPE_STAT_DATA_IMAGE	1	9	image1
1136	STATTYPE_STAT_DATA_IMAGE	2	9	image1
1136	STATTYPE_STAT_DATA_IMAGE	3	9	image1
1136	STATTYPE_STAT_DATA_IMAGE	4	9	image1
1136	STATTYPE_STAT_DATA_IMAGE	5	9	image1
1136	STATTYPE_STAT_DATA_IMAGE	6	9	image1
1136	STATTYPE_STAT_DATA_IMAGE	7	9	image1
1136	STATTYPE_STAT_DATA_IMAGE	8	9	image1
1136	STATTYPE_STAT_DATA_IMAGE	9	9	image2
1136	STATTYPE_STAT_DATA_IMAGE	10	9	image2
1136	STATTYPE_STAT_DATA_IMAGE	11	9	image2
1136	STATTYPE_STAT_DATA_IMAGE	12	9	image2
1136	STATTYPE_STAT_DATA_IMAGE	13	9	image2
1136	STATTYPE_STAT_DATA_IMAGE	14	9	image2
1136	STATTYPE_STAT_DATA_IMAGE	15	9	image2
1136	STATTYPE_STAT_DATA_IMAGE	16	9	image2
1136	STATTYPE_STAT_DATA_IMAGE	17	9	image3
1136	STATTYPE_STAT_DATA_IMAGE	18	9	image3
1136	STATTYPE_STAT_DATA_IMAGE	19	9	image3
1136	STATTYPE_STAT_DATA_IMAGE	20	9	image3
1136	STATTYPE_STAT_DATA_IMAGE	21	9	image3
1136	STATTYPE_STAT_DATA_IMAGE	22	9	image3
1136	STATTYPE_STAT_DATA_IMAGE	23	9	image3
1136	STATTYPE_STAT_DATA_IMAGE	24	9	image3
1136	STATTYPE_STAT_DATA_IMAGE	25	9	image4
1136	STATTYPE_STAT_DATA_IMAGE	26	9	image4
1136	STATTYPE_STAT_DATA_IMAGE	27	9	image4
1136	STATTYPE_STAT_DATA_IMAGE	28	9	image4
1136	STATTYPE_STAT_DATA_IMAGE	29	9	image4
1136	STATTYPE_STAT_DATA_IMAGE	30	9	image4
1136	STATTYPE_STAT_DATA_IMAGE	31	9	image4
1136	STATTYPE_STAT_DATA_IMAGE	32	9	image4
1136	STATTYPE_STAT_DATA_IMAGE	33	9	image5
1136	STATTYPE_STAT_DATA_IMAGE	34	9	image5
1136	STATTYPE_STAT_DATA_IMAGE	35	9	image5
1136	STATTYPE_STAT_DATA_IMAGE	36	9	image5
1136	STATTYPE_STAT_DATA_IMAGE	37	9	image5
1136	STATTYPE_STAT_DATA_IMAGE	38	9	image5
1136	STATTYPE_STAT_DATA_IMAGE	39	9	image5
1136	STATTYPE_STAT_DATA_IMAGE	40	9	image5
1136	STATTYPE_STAT_DATA_IMAGE	41	9	image6
1136	STATTYPE_STAT_DATA_IMAGE	42	9	image6
1136	STATTYPE_STAT_DATA_IMAGE	43	9	image6
1136	STATTYPE_STAT_DATA_IMAGE	44	9	image6
1136	STATTYPE_STAT_DATA_IMAGE	45	9	image6
1136	STATTYPE_STAT_DATA_IMAGE	46	9	image6
1136	STATTYPE_STAT_DATA_IMAGE	47	9	image6
1136	STATTYPE_STAT_DATA_IMAGE	48	9	image6
1136	STATTYPE_STAT_DATA_IMAGE	49	9	image7
1136	STATTYPE_STAT_DATA_IMAGE	50	9	image7
1136	STATTYPE_STAT_DATA_IMAGE	51	9	image7
1136	STATTYPE_STAT_DATA_IMAGE	52	9	image7
1136	STATTYPE_STAT_DATA_IMAGE	53	9	image7
1136	STATTYPE_STAT_DATA_IMAGE	54	9	image7
1136	STATTYPE_STAT_DATA_IMAGE	55	9	image7
1136	STATTYPE_STAT_DATA_IMAGE	56	9	image7
1136	STATTYPE_STAT_DATA_IMAGE	57	9	image8
1136	STATTYPE_STAT_DATA_IMAGE	58	9	image8
1136	STATTYPE_STAT_DATA_IMAGE	59	9	image8
1136	STATTYPE_STAT_DATA_IMAGE	60	9	image8
1136	STATTYPE_STAT_DATA_IMAGE	61	9	image8
1136	STATTYPE_STAT_DATA_IMAGE	62	9	image8
1136	STATTYPE_STAT_DATA_IMAGE	63	9	image8
1136	STATTYPE_STAT_DATA_IMAGE	64	9	image8
1136	STATTYPE_STAT_DATA_IMAGE	65	9	image9
1136	STATTYPE_STAT_DATA_IMAGE	66	9	image9
1136	STATTYPE_STAT_DATA_IMAGE	67	9	image9
1136	STATTYPE_STAT_DATA_IMAGE	68	9	image9
1136	STATTYPE_STAT_DATA_IMAGE	69	9	image9
1136	STATTYPE_STAT_DATA_IMAGE	70	9	image9
1136	STATTYPE_STAT_DATA_IMAGE	71	9	image9
1136	STATTYPE_STAT_DATA_IMAGE	72	9	image9
1136	STATTYPE_STAT_DATA_IMAGE	73	9	image10
1136	STATTYPE_STAT_DATA_IMAGE	74	9	image10
1136	STATTYPE_STAT_DATA_IMAGE	75	9	image10
1136	STATTYPE_STAT_DATA_IMAGE	76	9	image10
1136	STATTYPE_STAT_DATA_IMAGE	77	9	image10
1136	STATTYPE_STAT_DATA_IMAGE	78	9	image10
1136	STATTYPE_STAT_DATA_IMAGE	79	9	image10
1136	STATTYPE_STAT_DATA_IMAGE	80	9	image10
1136	STATTYPE_STAT_DATA_IMAGE	81	9	image11
1136	STATTYPE_STAT_DATA_IMAGE	82	9	image11
1136	STATTYPE_STAT_DATA_IMAGE	83	9	image11
1136	STATTYPE_STAT_DATA_IMAGE	84	9	image11
1136	STATTYPE_STAT_DATA_IMAGE	85	9	image11
1136	STATTYPE_STAT_DATA_IMAGE	86	9	image11
1136	STATTYPE_STAT_DATA_IMAGE	87	9	image11
1136	STATTYPE_STAT_DATA_IMAGE	88	9	image11
1136	STATTYPE_STAT_DATA_IMAGE	89	9	image12
1136	STATTYPE_STAT_DATA_IMAGE	90	9	image12
1136	STATTYPE_STAT_DATA_IMAGE	91	9	image12
1136	STATTYPE_STAT_DATA_IMAGE	92	9	image12
1136	STATTYPE_STAT_DATA_IMAGE	93	9	image12
1136	STATTYPE_STAT_DATA_IMAGE	94	9	image12
1136	STATTYPE_STAT_DATA_IMAGE	95	9	image12
1136	STATTYPE_STAT_DATA_IMAGE	96	9	image12
1136	STATTYPE_STAT_DATA_IMAGE	97	9	image13
1136	STATTYPE_STAT_DATA_IMAGE	98	9	image13
1136	STATTYPE_STAT_DATA_IMAGE	99	9	image13
1136	STATTYPE_STAT_DATA_IMAGE	100	9	image13
1136	STATTYPE_STAT_DATA_IMAGE	101	9	image13
1136	STATTYPE_STAT_DATA_IMAGE	102	9	image13
1136	STATTYPE_STAT_DATA_IMAGE	103	9	image13
1136	STATTYPE_STAT_DATA_IMAGE	104	9	image13
1136	STATTYPE_STAT_DATA_IMAGE	105	9	image14
1136	STATTYPE_STAT_DATA_IMAGE	106	9	image14
1136	STATTYPE_STAT_DATA_IMAGE	107	9	image14
1136	STATTYPE_STAT_DATA_IMAGE	108	9	image14
1136	STATTYPE_STAT_DATA_IMAGE	109	9	image14
1136	STATTYPE_STAT_DATA_IMAGE	110	9	image14
1136	STATTYPE_STAT_DATA_IMAGE	111	9	image14
1136	STATTYPE_STAT_DATA_IMAGE	112	9	image14
1136	STATTYPE_STAT_DATA_IMAGE	113	9	image15
1136	STATTYPE_STAT_DATA_IMAGE	114	9	image15
1136	STATTYPE_STAT_DATA_IMAGE	115	9	image15
1136	STATTYPE_STAT_DATA_IMAGE	116	9	image15
1136	STATTYPE_STAT_DATA_IMAGE	117	9	image15
1136	STATTYPE_STAT_DATA_IMAGE	118	9	image15
1136	STATTYPE_STAT_DATA_IMAGE	119	9	image15
1136	STATTYPE_STAT_DATA_IMAGE	120	9	image15
1136	STATTYPE_STAT_DATA_IMAGE	121	9	image16
1136	STATTYPE_STAT_DATA_IMAGE	122	9	image16
1136	STATTYPE_STAT_DATA_IMAGE	123	9	image16
1136	STATTYPE_STAT_DATA_IMAGE	124	9	image16
1136	STATTYPE_STAT_DATA_IMAGE	125	9	image16
1136	STATTYPE_STAT_DATA_IMAGE	126	9	image16
1136	STATTYPE_STAT_DATA_IMAGE	127	9	image16
1136	STATTYPE_STAT_DATA_IMAGE	128	9	image16
1137	STATTYPE_KPI_LOG_LN_TD_Amr	1	8	ifileid
1137	STATTYPE_KPI_LOG_LN_TD_Amr	2	8	itesttype
1137	STATTYPE_KPI_LOG_LN_TD_Amr	3	8	bms
1137	STATTYPE_KPI_LOG_LN_TD_Amr	4	8	ifiletime
1137	STATTYPE_KPI_LOG_LN_TD_Amr	5	8	iduration
1137	STATTYPE_KPI_LOG_LN_TD_Amr	6	8	idistance
1137	STATTYPE_KPI_LOG_LN_TD_Amr	7	8	isample
1137	STATTYPE_KPI_LOG_LN_TD_Amr	8	8	itllongitude
1137	STATTYPE_KPI_LOG_LN_TD_Amr	9	8	itllatitude
1137	STATTYPE_KPI_LOG_LN_TD_Amr	10	8	ibrlongitude
1137	STATTYPE_KPI_LOG_LN_TD_Amr	11	8	ibrlatitude
1137	STATTYPE_KPI_LOG_LN_TD_Amr	12	8	iPCCPCH_sample
1137	STATTYPE_KPI_LOG_LN_TD_Amr	13	8	iPCCPCH_RSCP_mean
1137	STATTYPE_KPI_LOG_LN_TD_Amr	14	8	iDPCH_C2I_sample
1137	STATTYPE_KPI_LOG_LN_TD_Amr	15	8	iDPCH_C2I_mean
1138	STATTYPE_KPI_LOG_LN_TD_Ps	1	8	ifileid
1138	STATTYPE_KPI_LOG_LN_TD_Ps	2	8	itesttype
1138	STATTYPE_KPI_LOG_LN_TD_Ps	3	8	bms
1138	STATTYPE_KPI_LOG_LN_TD_Ps	4	8	ifiletime
1138	STATTYPE_KPI_LOG_LN_TD_Ps	5	8	iduration
1138	STATTYPE_KPI_LOG_LN_TD_Ps	6	8	idistance
1138	STATTYPE_KPI_LOG_LN_TD_Ps	7	8	[isamplenum]
1138	STATTYPE_KPI_LOG_LN_TD_Ps	8	8	itllongitude
1138	STATTYPE_KPI_LOG_LN_TD_Ps	9	8	itllatitude
1138	STATTYPE_KPI_LOG_LN_TD_Ps	10	8	ibrlongitude
1138	STATTYPE_KPI_LOG_LN_TD_Ps	11	8	ibrlatitude
1138	STATTYPE_KPI_LOG_LN_TD_Ps	12	9	RSCP_INFO
1138	STATTYPE_KPI_LOG_LN_TD_Ps	13	9	RSCP_INFO
1138	STATTYPE_KPI_LOG_LN_TD_Ps	14	9	RSCP_INFO
1138	STATTYPE_KPI_LOG_LN_TD_Ps	15	9	RSCP_INFO
1138	STATTYPE_KPI_LOG_LN_TD_Ps	16	9	RSCP_INFO
1138	STATTYPE_KPI_LOG_LN_TD_Ps	17	9	RSCP_INFO
1138	STATTYPE_KPI_LOG_LN_TD_Ps	18	9	RSCP_INFO
1138	STATTYPE_KPI_LOG_LN_TD_Ps	19	9	RSCP_INFO
1138	STATTYPE_KPI_LOG_LN_TD_Ps	20	9	APP_INFO
1138	STATTYPE_KPI_LOG_LN_TD_Ps	21	9	APP_INFO
1138	STATTYPE_KPI_LOG_LN_TD_Ps	22	9	APP_INFO
1138	STATTYPE_KPI_LOG_LN_TD_Ps	23	9	APP_INFO
1138	STATTYPE_KPI_LOG_LN_TD_Ps	24	9	APP_INFO
1138	STATTYPE_KPI_LOG_LN_TD_Ps	25	9	APP_INFO
1138	STATTYPE_KPI_LOG_LN_TD_Ps	26	9	APP_INFO
1138	STATTYPE_KPI_LOG_LN_TD_Ps	27	9	APP_INFO
1139	STATTYPE_KPI_LOG_LN_GSMV	1	8	ifileid
1139	STATTYPE_KPI_LOG_LN_GSMV	2	8	itesttype
1139	STATTYPE_KPI_LOG_LN_GSMV	3	8	bms
1139	STATTYPE_KPI_LOG_LN_GSMV	4	8	ifiletime
1139	STATTYPE_KPI_LOG_LN_GSMV	5	8	iduration
1139	STATTYPE_KPI_LOG_LN_GSMV	6	8	idistance
1139	STATTYPE_KPI_LOG_LN_GSMV	7	8	isamplenum
1139	STATTYPE_KPI_LOG_LN_GSMV	8	8	itllongitude
1139	STATTYPE_KPI_LOG_LN_GSMV	9	8	itllatitude
1139	STATTYPE_KPI_LOG_LN_GSMV	10	8	ibrlongitude
1139	STATTYPE_KPI_LOG_LN_GSMV	11	8	ibrlatitude
1139	STATTYPE_KPI_LOG_LN_GSMV	12	8	PESQ_0_10
1139	STATTYPE_KPI_LOG_LN_GSMV	13	8	PESQ_10_20
1139	STATTYPE_KPI_LOG_LN_GSMV	14	8	PESQ_20_25
1139	STATTYPE_KPI_LOG_LN_GSMV	15	8	PESQ_25_28
1139	STATTYPE_KPI_LOG_LN_GSMV	16	8	PESQ_28_30
1139	STATTYPE_KPI_LOG_LN_GSMV	17	8	PESQ_30_33
1139	STATTYPE_KPI_LOG_LN_GSMV	18	8	PESQ_33_34
1139	STATTYPE_KPI_LOG_LN_GSMV	19	8	PESQ_45
1139	STATTYPE_KPI_LOG_LN_GSMV	20	5	PESQ_Mean float
1140	STATTYPE_CELL_PROPERTY_CELLINFO	1	6	strcellname varchar
1140	STATTYPE_CELL_PROPERTY_CELLINFO	2	8	idate int
1140	STATTYPE_CELL_PROPERTY_CELLINFO	3	8	iomcserver int
1140	STATTYPE_CELL_PROPERTY_CELLINFO	4	6	strcellref varchar
1140	STATTYPE_CELL_PROPERTY_CELLINFO	5	6	strcellid varchar
1140	STATTYPE_CELL_PROPERTY_CELLINFO	6	8	ilac int
1140	STATTYPE_CELL_PROPERTY_CELLINFO	7	8	ici int
1140	STATTYPE_CELL_PROPERTY_CELLINFO	8	8	ibscid int
1140	STATTYPE_CELL_PROPERTY_CELLINFO	9	8	ibcch int
1140	STATTYPE_CELL_PROPERTY_CELLINFO	10	8	ibsic int
1140	STATTYPE_CELL_PROPERTY_CELLINFO	11	8	ibandtype int
1140	STATTYPE_CELL_PROPERTY_CELLINFO	12	6	strbtstype varchar
1140	STATTYPE_CELL_PROPERTY_CELLINFO	13	6	strsumtype varchar
1140	STATTYPE_CELL_PROPERTY_CELLINFO	14	6	strcouplertype varchar
1140	STATTYPE_CELL_PROPERTY_CELLINFO	15	6	strcarriertype varchar
1140	STATTYPE_CELL_PROPERTY_CELLINFO	16	8	icarriertnum int
1140	STATTYPE_CELL_PROPERTY_CELLINFO	17	8	ivchannelnum int
1140	STATTYPE_CELL_PROPERTY_CELLINFO	18	8	isdcchnum int
1140	STATTYPE_CELL_PROPERTY_CELLINFO	19	8	igprsopened int
1140	STATTYPE_CELL_PROPERTY_CELLINFO	20	8	iedgeopened int
1140	STATTYPE_CELL_PROPERTY_CELLINFO	21	8	ihalfrateopened int
1140	STATTYPE_CELL_PROPERTY_CELLINFO	22	8	igprsstaticpdchnum int
1140	STATTYPE_CELL_PROPERTY_CELLINFO	23	8	igprsdynamicpdchnum int
1140	STATTYPE_CELL_PROPERTY_CELLINFO	24	8	iegprsstaticpdchnum int
1140	STATTYPE_CELL_PROPERTY_CELLINFO	25	8	iegprsdynamicpdchnum int
1140	STATTYPE_CELL_PROPERTY_CELLINFO	26	8	iacmbnextraabistsmain int
1140	STATTYPE_CELL_PROPERTY_CELLINFO	27	8	iacmbracode int
1140	STATTYPE_CELL_PROPERTY_CELLINFO	28	8	iagprsmaxdltbfSpdch int
1140	STATTYPE_CELL_PROPERTY_CELLINFO	29	8	iagprsmaxegprsmcs int
1140	STATTYPE_CELL_PROPERTY_CELLINFO	30	8	iagprsmaxpdch int
1140	STATTYPE_CELL_PROPERTY_CELLINFO	31	8	iagprsmaxpdchhighload int
1140	STATTYPE_CELL_PROPERTY_CELLINFO	32	8	iagprsmaxpdchpertbf int
1140	STATTYPE_CELL_PROPERTY_CELLINFO	33	8	iagprsmaxultbfspdch int
1140	STATTYPE_CELL_PROPERTY_CELLINFO	34	8	iagprsminpdch int
1140	STATTYPE_CELL_PROPERTY_CELLINFO	35	8	iagprstbfdlinitcs int
1140	STATTYPE_CELL_PROPERTY_CELLINFO	36	8	iagprstbfdlinitmcs int
1140	STATTYPE_CELL_PROPERTY_CELLINFO	37	8	iagprstbfulinitcs int
1140	STATTYPE_CELL_PROPERTY_CELLINFO	38	8	iagprstbfulinitmcs int
1140	STATTYPE_CELL_PROPERTY_CELLINFO	39	8	ibcchfrequency int
1140	STATTYPE_CELL_PROPERTY_CELLINFO	40	6	strccchconf varchar
1140	STATTYPE_CELL_PROPERTY_CELLINFO	41	8	icellreselectoffset int
1140	STATTYPE_CELL_PROPERTY_CELLINFO	42	8	icellreselectparamind int
1140	STATTYPE_CELL_PROPERTY_CELLINFO	43	8	icellbarred int
1140	STATTYPE_CELL_PROPERTY_CELLINFO	44	8	icellreselecthysteresis int
1140	STATTYPE_CELL_PROPERTY_CELLINFO	45	6	strfrequencyrange varchar
1140	STATTYPE_CELL_PROPERTY_CELLINFO	46	6	strhoppingtype varchar
1140	STATTYPE_CELL_PROPERTY_CELLINFO	47	6	strlocationname varchar
1140	STATTYPE_CELL_PROPERTY_CELLINFO	48	8	imaxgprscs int
1140	STATTYPE_CELL_PROPERTY_CELLINFO	49	8	inbrsdcchavailable int
1140	STATTYPE_CELL_PROPERTY_CELLINFO	50	8	inbrrchavailable int
1140	STATTYPE_CELL_PROPERTY_CELLINFO	51	8	inoofblocksforaccessgrant int
1140	STATTYPE_CELL_PROPERTY_CELLINFO	52	8	ipenaltytime int
1140	STATTYPE_CELL_PROPERTY_CELLINFO	53	8	iracolor int
1140	STATTYPE_CELL_PROPERTY_CELLINFO	54	8	irxLevaccessmin int
1140	STATTYPE_CELL_PROPERTY_CELLINFO	55	8	itempoffset int
1140	STATTYPE_CELL_PROPERTY_CELLINFO	56	8	ithrfrloadlsv1 int
1140	STATTYPE_CELL_PROPERTY_CELLINFO	57	8	ithrfrloadusv1 int
1140	STATTYPE_CELL_PROPERTY_CELLINFO	58	8	itimeadvancefilter int
1140	STATTYPE_CELL_PROPERTY_CELLINFO	59	8	inbrdrgsm18001900tre int
1140	STATTYPE_CELL_PROPERTY_CELLINFO	60	8	inbrdrgsm900tre int
1140	STATTYPE_CELL_PROPERTY_CELLINFO	61	8	ienbspc int
1140	STATTYPE_CELL_PROPERTY_CELLINFO	62	8	ienmspc int
1140	STATTYPE_CELL_PROPERTY_CELLINFO	63	8	ibstxpwrmax int
1140	STATTYPE_CELL_PROPERTY_CELLINFO	64	8	ibstxpwrmin int
1140	STATTYPE_CELL_PROPERTY_CELLINFO	65	8	imstxpwrmax int
1140	STATTYPE_CELL_PROPERTY_CELLINFO	66	8	imstxpwrmin int
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	1	6	strmanufacturer varchar
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	2	8	itime int
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	3	6	strcellcode varchar
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	4	6	strcellname varchar
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	5	8	ilac int
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	6	8	ici int
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	7	6	strbsc varchar
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	8	8	igprsdata1 int
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	9	8	igprsdata2 int
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	10	8	igprsdata3 int
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	11	8	igprsdata4 int
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	12	5	fgprsdata5 float
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	13	5	fgprsdata6 float
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	14	5	fgprsdata7 float
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	15	5	fgprsdata8 float
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	16	5	fgprsdata9 float
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	17	5	fgprsdata10 float
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	18	5	fgprsdata11 float
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	19	5	fgprsdata12 float
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	20	5	fgprsdata13 float
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	21	5	fgprsdata14 float
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	22	5	fgprsdata15 float
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	23	5	fgprsdata16 float
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	24	5	fgprsdata17 float
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	25	5	fgprsdata18 float
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	26	5	fgprsdata19 float
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	27	5	fgprsdata20 float
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	28	5	fgprsdata21 float
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	29	5	fgprsdata22 float
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	30	5	fgprsdata23 float
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	31	5	fgprsdata24 float
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	32	5	fgprsdata25 float
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	33	5	fgprsdata26 float
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	34	8	igprsdata27 int
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	35	8	igprsdata28 int
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	36	5	fgprsdata29 float
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	37	8	igprsdata30 int
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	38	5	fgprsdata31 float
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	39	5	fgprsdata32 float
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	40	8	igprsdata33 int
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	41	8	igprsdata34 int
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	42	5	fgprsdata35 float
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	43	5	fgprsdata36 float
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	44	5	fgprsdata37 float
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	45	5	fgprsdata38 float
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	46	5	fgprsdata39 float
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	47	5	fgprsdata40 float
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	48	5	fgprsdata41 float
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	49	5	fgprsdata42 float
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	50	5	fgprsdata43 float
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	51	5	fgprsdata44 float
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	52	5	fgprsdata45 float
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	53	5	fgprsdata46 float
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	54	5	fgprsdata47 float
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	55	5	fgprsdata48 float
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	56	5	fgprsdata49 float
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	57	5	fgprsdata50 float
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	58	5	fgprsdata51 float
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	59	5	fgprsdata52 float
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	60	5	fgprsdata53 float
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	61	5	fgprsdata54 float
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	62	5	fgprsdata55 float
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	63	5	fgprsdata56 float
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	64	5	fgprsdata57 float
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	65	5	fgprsdata58 float
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	66	5	fgprsdata59 float
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	67	5	fgprsdata60 float
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	68	5	fgprsdata61 float
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	69	5	fgprsdata62 float
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	70	5	fgprsdata63 float
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	71	5	fgprsdata64 float
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	72	5	fgprsdata65 float
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	73	8	igprsdata66 int
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	74	8	igprsdata67 int
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	75	8	igprsdata68 int
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	76	8	igprsdata69 int
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	77	5	fgprsdata70 float
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	78	5	fgprsdata71 float
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	79	5	fgprsdata72 float
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	80	5	fgprsdata73 float
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	81	5	fgprsdata74 float
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	82	5	fgprsdata75 float
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	83	5	fgprsdata76 float
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	84	5	fgprsdata77 float
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	85	5	fgprsdata78 float
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	86	5	fgprsdata79 float
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	87	5	fgprsdata80 float
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	88	5	fgprsdata81 float
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	89	5	fgprsdata82 float
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	90	5	fgprsdata83 float
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	91	5	fgprsdata84 float
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	92	5	fgprsdata85 float
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	93	5	fgprsdata86 float
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	94	5	fgprsdata87 float
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	95	5	fgprsdata88 float
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	96	5	fgprsdata89 float
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	97	8	igprsdata90 int
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	98	8	igprsdata91 int
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	99	8	igprsdata92 int
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	100	8	igprsdata93 int
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	101	8	igprsdata94 int
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	102	8	igprsdata95 int
1141	STATTYPE_CELL_PROPERTY_GPRSINFO	103	5	fgprsdata96 float
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	1	6	strmaintenance varchar
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	2	6	strbsc varchar
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	3	6	strcellcode varchar
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	4	6	strcellname varchar
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	5	8	itime int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	6	5	ftraffic1 float
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	7	8	itraffic2 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	8	8	itraffic3 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	9	8	itraffic4 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	10	5	ftraffic5 float
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	11	5	ftraffic6 float
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	12	8	itraffic7 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	13	8	itraffic8 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	14	8	itraffic9 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	15	8	itraffic10 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	16	8	itraffic11 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	17	8	itraffic12 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	18	5	ftraffic13 float
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	19	5	ftraffic14 float
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	20	5	ftraffic15 float
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	21	5	ftraffic16 float
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	22	5	ftraffic17 float
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	23	8	itraffic18 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	24	8	itraffic19 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	25	5	ftraffic20 float
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	26	5	ftraffic21 float
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	27	8	itraffic22 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	28	8	itraffic23 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	29	8	itraffic24 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	30	8	itraffic25 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	31	5	ftraffic26 float
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	32	5	ftraffic27 float
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	33	5	ftraffic28 float
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	34	5	ftraffic29 float
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	35	5	ftraffic30 float
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	36	5	ftraffic31 float
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	37	5	ftraffic32 float
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	38	5	ftraffic33 float
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	39	5	ftraffic34 float
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	40	5	ftraffic35 float
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	41	8	itraffic36 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	42	8	itraffic37 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	43	5	ftraffic38 float
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	44	5	ftraffic39 float
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	45	8	itraffic40 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	46	5	ftraffic41 float
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	47	8	itraffic42 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	48	5	ftraffic43 float
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	49	5	ftraffic44 float
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	50	5	ftraffic45 float
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	51	5	ftraffic46 float
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	52	5	ftraffic47 float
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	53	5	ftraffic48 float
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	54	8	itraffic49 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	55	5	ftraffic50 float
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	56	5	ftraffic51 float
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	57	8	itraffic52 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	58	8	itraffic53 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	59	8	itraffic54 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	60	8	itraffic55 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	61	8	itraffic56 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	62	8	itraffic57 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	63	8	itraffic58 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	64	8	itraffic59 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	65	8	itraffic60 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	66	8	itraffic61 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	67	8	itraffic62 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	68	8	itraffic63 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	69	8	itraffic64 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	70	8	itraffic65 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	71	8	itraffic66 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	72	8	itraffic67 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	73	8	itraffic68 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	74	8	itraffic69 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	75	8	itraffic70 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	76	8	itraffic71 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	77	8	itraffic72 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	78	8	itraffic73 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	79	8	itraffic74 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	80	5	ftraffic75 float
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	81	5	ftraffic76 float
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	82	5	ftraffic77 float
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	83	5	ftraffic78 float
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	84	8	itraffic79 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	85	8	itraffic80 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	86	8	itraffic81 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	87	8	itraffic82 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	88	8	itraffic83 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	89	8	itraffic84 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	90	8	itraffic85 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	91	8	itraffic86 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	92	8	itraffic87 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	93	8	itraffic88 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	94	8	itraffic89 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	95	8	itraffic90 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	96	8	itraffic91 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	97	8	itraffic92 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	98	8	itraffic93 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	99	8	itraffic94 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	100	8	itraffic95 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	101	8	itraffic96 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	102	8	itraffic97 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	103	8	itraffic98 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	104	8	itraffic99 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	105	8	itraffic100 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	106	8	itraffic101 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	107	8	itraffic102 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	108	5	ftraffic103 float
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	109	5	ftraffic104 float
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	110	5	ftraffic105 float
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	111	5	ftraffic106 float
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	112	8	itraffic107 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	113	8	itraffic108 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	114	8	itraffic109 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	115	5	ftraffic110 float
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	116	8	itraffic111 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	117	8	itraffic112 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	118	5	ftraffic113 float
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	119	5	ftraffic114 float
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	120	5	ftraffic115 float
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	121	8	itraffic116 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	122	8	itraffic117 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	123	8	itraffic118 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	124	8	itraffic119 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	125	8	itraffic120 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	126	8	itraffic121 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	127	8	itraffic122 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	128	8	itraffic123 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	129	8	itraffic124 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	130	8	itraffic125 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	131	8	itraffic126 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	132	8	itraffic127 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	133	8	itraffic128 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	134	8	itraffic129 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	135	8	itraffic130 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	136	8	itraffic131 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	137	8	itraffic132 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	138	8	itraffic133 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	139	8	itraffic134 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	140	8	itraffic135 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	141	8	itraffic136 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	142	8	itraffic137 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	143	8	itraffic138 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	144	8	itraffic139 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	145	8	itraffic140 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	146	8	itraffic141 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	147	8	itraffic142 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	148	8	itraffic143 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	149	8	itraffic144 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	150	8	itraffic145 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	151	8	itraffic146 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	152	8	itraffic147 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	153	8	itraffic148 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	154	8	itraffic149 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	155	8	itraffic150 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	156	8	itraffic151 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	157	5	ftraffic152 float
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	158	5	ftraffic153 float
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	159	5	ftraffic154 float
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	160	8	itraffic155 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	161	8	itraffic156 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	162	8	itraffic157 int
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	163	5	ftraffic158 float
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	164	5	ftraffic159 float
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	165	5	ftraffic160 float
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	166	5	ftraffic161 float
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	167	5	ftraffic162 float
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	168	5	ftraffic163 float
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	169	5	ftraffic164 float
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	170	5	ftraffic165 float
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	171	5	ftraffic166 float
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	172	5	ftraffic167 float
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	173	5	ftraffic168 float
1142	STATTYPE_CELL_PROPERTY_TRAFFICINFO	174	5	ftraffic169 float
1200	CONFIGTYPE_BTS	1	8	ID（4BYTES）
1200	CONFIGTYPE_BTS	2	8	起始时间；（4BYTES）
1200	CONFIGTYPE_BTS	3	8	结束时间；（4BYTES）
1200	CONFIGTYPE_BTS	4	6	strbtsname
1200	CONFIGTYPE_BTS	5	6	strmscname
1200	CONFIGTYPE_BTS	6	6	strbscname
1200	CONFIGTYPE_BTS	7	8	ilongitude
1200	CONFIGTYPE_BTS	8	8	ilatitude
1200	CONFIGTYPE_BTS	9	6	strtype
1200	CONFIGTYPE_BTS	10	1	bangtype
1200	CONFIGTYPE_BTS	11	6	strcomment
1201	CONFIGTYPE_CELL	1	8	iidint
1201	CONFIGTYPE_CELL	2	8	istimeint
1201	CONFIGTYPE_CELL	3	8	ietimeint
1201	CONFIGTYPE_CELL	4	6	strcellnamestring
1201	CONFIGTYPE_CELL	5	6	strcellcodestring
1201	CONFIGTYPE_CELL	6	8	ibtsidint
1201	CONFIGTYPE_CELL	7	8	lacint
1201	CONFIGTYPE_CELL	8	8	ciint
1201	CONFIGTYPE_CELL	9	8	ibcchint
1201	CONFIGTYPE_CELL	10	8	ibsicint
1201	CONFIGTYPE_CELL	11	6	tchstring
1201	CONFIGTYPE_CELL	12	1	hoptinyint(1表示开启0表示关闭)
1202	CONFIGTYPE_PD	1	8	iidint
1202	CONFIGTYPE_PD	2	8	istimeint
1202	CONFIGTYPE_PD	3	8	ietimeint
1202	CONFIGTYPE_PD	4	8	icellidint
1202	CONFIGTYPE_PD	5	8	igrpidint
1202	CONFIGTYPE_PD	6	8	ipdnoint
1202	CONFIGTYPE_PD	7	8	ilongitude
1202	CONFIGTYPE_PD	8	8	ilatitude
1202	CONFIGTYPE_PD	9	8	itype
1202	CONFIGTYPE_PD	10	8	iangle_dirint
1202	CONFIGTYPE_PD	11	8	iangle_obint
1202	CONFIGTYPE_PD	12	8	ialtitudeint
1202	CONFIGTYPE_PD	13	5	fgainfloat
1202	CONFIGTYPE_PD	14	8	iEiRPint
1202	CONFIGTYPE_PD	15	6	strcommentstring
1203	CONFIGTYPE_NBCELL	1	8	小区ID（4BYTES1＋）
1203	CONFIGTYPE_NBCELL	2	8	起始时间；（4BYTES）
1203	CONFIGTYPE_NBCELL	3	8	结束时间；（4BYTES）
1203	CONFIGTYPE_NBCELL	4	8	邻区ID
1203	CONFIGTYPE_NBCELL	5	1	邻区类型
1204	CONFIGTYPE_DISTRICT_COMMUNITY	1	8	行政区ID
1204	CONFIGTYPE_DISTRICT_COMMUNITY	2	6	行政区名称
1204	CONFIGTYPE_DISTRICT_COMMUNITY	3	8	街道办ID
1204	CONFIGTYPE_DISTRICT_COMMUNITY	4	6	街道办名称
1205	CONFIGTYPE_REPEATER	1	8	[iid][int]
1205	CONFIGTYPE_REPEATER	2	8	[istime][int]
1205	CONFIGTYPE_REPEATER	3	8	[ietime][int]
1205	CONFIGTYPE_REPEATER	4	6	[strname][varchar](50)
1205	CONFIGTYPE_REPEATER	5	8	[longitude][int]
1205	CONFIGTYPE_REPEATER	6	8	[latitude][int]
1205	CONFIGTYPE_REPEATER	7	8	[band_type]
1205	CONFIGTYPE_REPEATER	8	6	[address][varchar](200)
1205	CONFIGTYPE_REPEATER	9	8	[dedicated_cell_id]
1206	CONFIGTYPE_SCAN	1	8	areatype
1206	CONFIGTYPE_SCAN	2	6	areatypename
1206	CONFIGTYPE_SCAN	3	8	areaid
1206	CONFIGTYPE_SCAN	4	6	areaname
1206	CONFIGTYPE_SCAN	5	6	comment
1206	CONFIGTYPE_SCAN	6	8	tllongitude
1206	CONFIGTYPE_SCAN	7	8	tllatitude
1206	CONFIGTYPE_SCAN	8	8	brlongitude
1206	CONFIGTYPE_SCAN	9	8	brlatitude
1207	CONFIGTYPE_AREA	1	8	areatypeid
1207	CONFIGTYPE_AREA	2	8	areaid
1207	CONFIGTYPE_AREA	3	6	areaname
1207	CONFIGTYPE_AREA	4	6	areacomment
1208	CONFIGTYPE_VILLAGE_Nature	1	8	iid
1208	CONFIGTYPE_VILLAGE_Nature	2	8	istime
1208	CONFIGTYPE_VILLAGE_Nature	3	8	ietime
1208	CONFIGTYPE_VILLAGE_Nature	4	6	name
1208	CONFIGTYPE_VILLAGE_Nature	5	6	village_administration_name
1208	CONFIGTYPE_VILLAGE_Nature	6	1	is_village_administration_locus
1208	CONFIGTYPE_VILLAGE_Nature	7	6	prefecture_name
1208	CONFIGTYPE_VILLAGE_Nature	8	6	town_name
1208	CONFIGTYPE_VILLAGE_Nature	9	8	longitude
1208	CONFIGTYPE_VILLAGE_Nature	10	8	latitude
1208	CONFIGTYPE_VILLAGE_Nature	11	8	door_count
1208	CONFIGTYPE_VILLAGE_Nature	12	8	person_count
1208	CONFIGTYPE_VILLAGE_Nature	13	6	landform_info
1208	CONFIGTYPE_VILLAGE_Nature	14	6	road_info
1208	CONFIGTYPE_VILLAGE_Nature	15	6	power_info
1208	CONFIGTYPE_VILLAGE_Nature	16	6	economy_info
1208	CONFIGTYPE_VILLAGE_Nature	17	8	broad_band_connect_count
1208	CONFIGTYPE_VILLAGE_Nature	18	6	industry_info
1208	CONFIGTYPE_VILLAGE_Nature	19	6	other_info
1208	CONFIGTYPE_VILLAGE_Nature	20	8	cmcc_lac
1208	CONFIGTYPE_VILLAGE_Nature	21	8	cmcc_ci
1208	CONFIGTYPE_VILLAGE_Nature	22	6	cmcc_cell_name
1208	CONFIGTYPE_VILLAGE_Nature	23	6	cmcc_repeater_name
1208	CONFIGTYPE_VILLAGE_Nature	24	8	cmcc_distance
1208	CONFIGTYPE_VILLAGE_Nature	25	5	cmcc_coverage
1208	CONFIGTYPE_VILLAGE_Nature	26	5	cmcc_ratio_drop
1208	CONFIGTYPE_VILLAGE_Nature	27	5	cmcc_ratio_connect
1208	CONFIGTYPE_VILLAGE_Nature	28	5	cmcc_mos_value
1208	CONFIGTYPE_VILLAGE_Nature	29	5	cmcc_setup_relay_average
1208	CONFIGTYPE_VILLAGE_Nature	30	5	unicom_coverage
1208	CONFIGTYPE_VILLAGE_Nature	31	5	unicom_ratio_drop
1208	CONFIGTYPE_VILLAGE_Nature	32	5	unicom_ratio_connect
1208	CONFIGTYPE_VILLAGE_Nature	33	5	unicom_mos_value
1208	CONFIGTYPE_VILLAGE_Nature	34	5	telcom_coverage
1208	CONFIGTYPE_VILLAGE_Nature	35	5	telcom_ratio_drop
1208	CONFIGTYPE_VILLAGE_Nature	36	5	telcom_ratio_connect
1208	CONFIGTYPE_VILLAGE_Nature	37	5	telcom_mos_value
1209	CONFIGTYPE_VILLAGE_Admin	1	8	iid
1209	CONFIGTYPE_VILLAGE_Admin	2	8	istime
1209	CONFIGTYPE_VILLAGE_Admin	3	8	ietime
1209	CONFIGTYPE_VILLAGE_Admin	4	6	name
1209	CONFIGTYPE_VILLAGE_Admin	5	6	prefecture_name
1209	CONFIGTYPE_VILLAGE_Admin	6	6	town_name
1209	CONFIGTYPE_VILLAGE_Admin	7	8	longitude
1209	CONFIGTYPE_VILLAGE_Admin	8	8	latitude
1209	CONFIGTYPE_VILLAGE_Admin	9	8	door_count
1209	CONFIGTYPE_VILLAGE_Admin	10	8	person_count
1209	CONFIGTYPE_VILLAGE_Admin	11	6	landform_info
1209	CONFIGTYPE_VILLAGE_Admin	12	6	road_info
1209	CONFIGTYPE_VILLAGE_Admin	13	6	power_info
1209	CONFIGTYPE_VILLAGE_Admin	14	6	economy_info
1209	CONFIGTYPE_VILLAGE_Admin	15	8	broad_band_connect_count
1209	CONFIGTYPE_VILLAGE_Admin	16	6	industry_info
1209	CONFIGTYPE_VILLAGE_Admin	17	6	other_info
1209	CONFIGTYPE_VILLAGE_Admin	18	8	cmcc_lac
1209	CONFIGTYPE_VILLAGE_Admin	19	8	cmcc_ci
1209	CONFIGTYPE_VILLAGE_Admin	20	6	cmcc_cell_name
1209	CONFIGTYPE_VILLAGE_Admin	21	6	cmcc_repeater_name
1209	CONFIGTYPE_VILLAGE_Admin	22	8	cmcc_distance
1209	CONFIGTYPE_VILLAGE_Admin	23	5	cmcc_coverage
1209	CONFIGTYPE_VILLAGE_Admin	24	5	cmcc_ratio_drop
1209	CONFIGTYPE_VILLAGE_Admin	25	5	cmcc_ratio_connect
1209	CONFIGTYPE_VILLAGE_Admin	26	5	cmcc_mos_value
1209	CONFIGTYPE_VILLAGE_Admin	27	5	cmcc_setup_relay_average
1209	CONFIGTYPE_VILLAGE_Admin	28	5	unicom_coverage
1209	CONFIGTYPE_VILLAGE_Admin	29	5	unicom_ratio_drop
1209	CONFIGTYPE_VILLAGE_Admin	30	5	unicom_ratio_connect
1209	CONFIGTYPE_VILLAGE_Admin	31	5	unicom_mos_value
1209	CONFIGTYPE_VILLAGE_Admin	32	5	telcom_coverage
1209	CONFIGTYPE_VILLAGE_Admin	33	5	telcom_ratio_drop
1209	CONFIGTYPE_VILLAGE_Admin	34	5	telcom_ratio_connect
1209	CONFIGTYPE_VILLAGE_Admin	35	5	telcom_mos_value
1210	CONFIGTYPE_AREA_LIST	1	8	iareaid
1210	CONFIGTYPE_AREA_LIST	2	6	strareaname
1210	CONFIGTYPE_AREA_LIST	3	6	strcomment
1210	CONFIGTYPE_AREA_LIST	4	8	longitude
1210	CONFIGTYPE_AREA_LIST	5	8	latitude
1210	CONFIGTYPE_AREA_LIST	6	8	areatypeid
1210	CONFIGTYPE_AREA_LIST	7	8	pictureCount
1211	CONFIGTYPE_AREA_ADD	1	8	areaid
1212	CONFIGTYPE_TDSCDMA_BTS	1	8	[iid][int],
1212	CONFIGTYPE_TDSCDMA_BTS	2	8	[istime][int],
1212	CONFIGTYPE_TDSCDMA_BTS	3	8	[ietime][int],
1212	CONFIGTYPE_TDSCDMA_BTS	4	6	[strbtsname][varchar](255),
1212	CONFIGTYPE_TDSCDMA_BTS	5	6	[strrncname][varchar](255),
1212	CONFIGTYPE_TDSCDMA_BTS	6	6	[strrnccode][varchar](255),
1212	CONFIGTYPE_TDSCDMA_BTS	7	8	[ilongitude][int],
1212	CONFIGTYPE_TDSCDMA_BTS	8	8	[ilatitude][int],
1212	CONFIGTYPE_TDSCDMA_BTS	9	6	[strsupplier][varchar](255),
1212	CONFIGTYPE_TDSCDMA_BTS	10	6	[strdoor][varchar](20),
1212	CONFIGTYPE_TDSCDMA_BTS	11	6	[strcomment][varchar](255)
1213	CONFIGTYPE_TDSCDMA_CELL	1	8	[iid][int]
1213	CONFIGTYPE_TDSCDMA_CELL	2	8	[istime][int]
1213	CONFIGTYPE_TDSCDMA_CELL	3	8	[ietime][int]
1213	CONFIGTYPE_TDSCDMA_CELL	4	6	[strcellname][varchar](50)
1213	CONFIGTYPE_TDSCDMA_CELL	5	6	[strcellcode][varchar](50)
1213	CONFIGTYPE_TDSCDMA_CELL	6	8	[ibtsid][int]
1213	CONFIGTYPE_TDSCDMA_CELL	7	8	[strlac][varchar](50)
1213	CONFIGTYPE_TDSCDMA_CELL	8	8	[strci][varchar](50)
1213	CONFIGTYPE_TDSCDMA_CELL	9	8	[icpi][varchar](50)
1213	CONFIGTYPE_TDSCDMA_CELL	10	8	[ifreq][varchar](50)
1213	CONFIGTYPE_TDSCDMA_CELL	11	6	[strfreqlist][varchar](255)
1213	CONFIGTYPE_TDSCDMA_CELL	12	6	[strcomment][varchar](255)
1214	CONFIGTYPE_TDSCDMA_PD	1	8	[iid][int]
1214	CONFIGTYPE_TDSCDMA_PD	2	8	[istime][int]
1214	CONFIGTYPE_TDSCDMA_PD	3	8	[ietime][int]
1214	CONFIGTYPE_TDSCDMA_PD	4	8	[icellid][int]
1214	CONFIGTYPE_TDSCDMA_PD	5	8	[ilongitude][int]
1214	CONFIGTYPE_TDSCDMA_PD	6	8	[ilatitude][int]
1214	CONFIGTYPE_TDSCDMA_PD	7	8	[iangle_dir][int]
1214	CONFIGTYPE_TDSCDMA_PD	8	8	[iangle_ob][int]
1214	CONFIGTYPE_TDSCDMA_PD	9	8	[ialtitude][int]
1214	CONFIGTYPE_TDSCDMA_PD	10	6	[strcomment][varchar](255)
1215	CONFIGTYPE_CITY	1	8	id int
1215	CONFIGTYPE_CITY	2	6	cityname varhcar(255)
1216	CONFIGTYPE_TDSCDMA_NBCELL	1	8	inbcellid int
1216	CONFIGTYPE_TDSCDMA_NBCELL	2	8	itype int
1216	CONFIGTYPE_TDSCDMA_NBCELL	3	8	istime int
1216	CONFIGTYPE_TDSCDMA_NBCELL	4	8	ietime int
1217	CONFIGTYPE_WCDMA_NODEB	1	8	[iid]
1217	CONFIGTYPE_WCDMA_NODEB	2	8	[istime]
1217	CONFIGTYPE_WCDMA_NODEB	3	8	[ietime]
1217	CONFIGTYPE_WCDMA_NODEB	4	6	[strnodebname]
1217	CONFIGTYPE_WCDMA_NODEB	5	6	[strnodebcode]
1217	CONFIGTYPE_WCDMA_NODEB	6	6	[strmgwname]
1217	CONFIGTYPE_WCDMA_NODEB	7	6	[strrncname]
1217	CONFIGTYPE_WCDMA_NODEB	8	8	[ilongitude]
1217	CONFIGTYPE_WCDMA_NODEB	9	8	[ilatitude]
1217	CONFIGTYPE_WCDMA_NODEB	10	8	[itype]
1217	CONFIGTYPE_WCDMA_NODEB	11	6	[strcomment]
1218	CONFIGTYPE_WCDMA_CELL	1	8	[iid]
1218	CONFIGTYPE_WCDMA_CELL	2	8	[istime]
1218	CONFIGTYPE_WCDMA_CELL	3	8	[ietime]
1218	CONFIGTYPE_WCDMA_CELL	4	6	[strcellname]
1218	CONFIGTYPE_WCDMA_CELL	5	6	[strcellcode]
1218	CONFIGTYPE_WCDMA_CELL	6	8	[inodebid]
1218	CONFIGTYPE_WCDMA_CELL	7	8	[ilac]
1218	CONFIGTYPE_WCDMA_CELL	8	8	[ici]
1218	CONFIGTYPE_WCDMA_CELL	9	8	[iuarfcn]
1218	CONFIGTYPE_WCDMA_CELL	10	8	[ipsc]
1218	CONFIGTYPE_WCDMA_CELL	11	6	[uarfcnlist]
1218	CONFIGTYPE_WCDMA_CELL	12	6	[strcomment]
1219	CONFIGTYPE_WCDMA_PD	1	8	[iid]
1219	CONFIGTYPE_WCDMA_PD	2	8	[istime]
1219	CONFIGTYPE_WCDMA_PD	3	8	[ietime]
1219	CONFIGTYPE_WCDMA_PD	4	8	[icellid]
1219	CONFIGTYPE_WCDMA_PD	5	8	[ilongitude]
1219	CONFIGTYPE_WCDMA_PD	6	8	[ilatitude]
1219	CONFIGTYPE_WCDMA_PD	7	8	[direction_type]
1219	CONFIGTYPE_WCDMA_PD	8	5	[direction]
1219	CONFIGTYPE_WCDMA_PD	9	5	[downward_m]
1219	CONFIGTYPE_WCDMA_PD	10	5	[downward_e]
1219	CONFIGTYPE_WCDMA_PD	11	5	[altitude]
1219	CONFIGTYPE_WCDMA_PD	12	6	[strcomment]
1220	CONFIGTYPE_WCDMA_NBCELL	1	8	inbcellid int
1220	CONFIGTYPE_WCDMA_NBCELL	2	8	itype int
1220	CONFIGTYPE_WCDMA_NBCELL	3	8	istime int
1220	CONFIGTYPE_WCDMA_NBCELL	4	8	ietime int
1221	CONFIGTYPE_PROJECT_EXTEND	1	8	iid int
1221	CONFIGTYPE_PROJECT_EXTEND	2	8	itypeid int
1221	CONFIGTYPE_PROJECT_EXTEND	3	8	isubid int
1221	CONFIGTYPE_PROJECT_EXTEND	4	6	strcomment varchar
1222	CONFIGTYPE_KPI_TABLE	1	8	iidx int
1222	CONFIGTYPE_KPI_TABLE	2	6	strcolname varchar
1222	CONFIGTYPE_KPI_TABLE	3	6	strcoldesc varchar
1222	CONFIGTYPE_KPI_TABLE	4	8	icoltype int
1222	CONFIGTYPE_KPI_TABLE	5	6	strtablename varchar
1222	CONFIGTYPE_KPI_TABLE	6	6	strrptname varchar
1300	CONFIGTYPE_STATIC_INFO	1	8	静态数据ID
1300	CONFIGTYPE_STATIC_INFO	2	6	静态数据名称
1300	CONFIGTYPE_STATIC_INFO	3	6	静态数据备注
1301	TEMPTYPE_INT	1	8	公共类型返回整型
1302	CONFIGTYPE_COVER_REGION_INFO	1	8	[id] [int]
1302	CONFIGTYPE_COVER_REGION_INFO	2	8	[type] [int]
1302	CONFIGTYPE_COVER_REGION_INFO	3	6	[name] [varchar](50) 
1302	CONFIGTYPE_COVER_REGION_INFO	4	6	[description] [varchar](255) 
1302	CONFIGTYPE_COVER_REGION_INFO	5	8	[status] [int] NULL
1303	CONFIGTYPE_COVER_REGION_POINT	1	8	[id] [int]
1303	CONFIGTYPE_COVER_REGION_POINT	2	8	[longitude] [int]
1303	CONFIGTYPE_COVER_REGION_POINT	3	8	[latitude] [int]
1304	CONFIGTYPE_LTE_BTS	1	8	ID（4BYTES）
1304	CONFIGTYPE_LTE_BTS	2	8	起始时间；（4BYTES）
1304	CONFIGTYPE_LTE_BTS	3	8	结束时间；（4BYTES）
1304	CONFIGTYPE_LTE_BTS	4	6	strmscname
1304	CONFIGTYPE_LTE_BTS	5	6	strenodebname
1304	CONFIGTYPE_LTE_BTS	6	8	ienodebid
1304	CONFIGTYPE_LTE_BTS	7	8	ilongitude
1304	CONFIGTYPE_LTE_BTS	8	8	ilatitude
1304	CONFIGTYPE_LTE_BTS	9	6	strdoor
1304	CONFIGTYPE_LTE_BTS	10	6	strcomment
1305	CONFIGTYPE_LTE_CELL	1	8	iidint
1305	CONFIGTYPE_LTE_CELL	2	8	istimeint
1305	CONFIGTYPE_LTE_CELL	3	8	ietimeint
1305	CONFIGTYPE_LTE_CELL	4	6	strcellcode
1305	CONFIGTYPE_LTE_CELL	5	6	strcellname
1305	CONFIGTYPE_LTE_CELL	6	8	ibtsid
1305	CONFIGTYPE_LTE_CELL	7	8	itac
1305	CONFIGTYPE_LTE_CELL	8	8	ieci
1305	CONFIGTYPE_LTE_CELL	9	8	icellid
1305	CONFIGTYPE_LTE_CELL	10	8	isectorid
1305	CONFIGTYPE_LTE_CELL	11	8	ipci
1305	CONFIGTYPE_LTE_CELL	12	8	iearfcn
1305	CONFIGTYPE_LTE_CELL	13	6	strfreqlist
1305	CONFIGTYPE_LTE_CELL	14	6	strcomment
1305	CONFIGTYPE_LTE_CELL	15	6	strgsmfreqlist
1305	CONFIGTYPE_LTE_CELL	16	6	strtdfreqlist
1306	CONFIGTYPE_LTE_PD	1	8	iid
1306	CONFIGTYPE_LTE_PD	2	8	istime
1306	CONFIGTYPE_LTE_PD	3	8	ietime
1306	CONFIGTYPE_LTE_PD	4	8	icellid
1306	CONFIGTYPE_LTE_PD	5	8	iflongitude
1306	CONFIGTYPE_LTE_PD	6	8	iflatitude
1306	CONFIGTYPE_LTE_PD	7	8	iangle_dir
1306	CONFIGTYPE_LTE_PD	8	8	iangle_ob
1306	CONFIGTYPE_LTE_PD	9	8	ialtitude
1306	CONFIGTYPE_LTE_PD	10	6	strcomment
1307	CONFIGTYPE_LTE_NBCELL	1	8	icellid
1307	CONFIGTYPE_LTE_NBCELL	2	8	inbcellid
1307	CONFIGTYPE_LTE_NBCELL	3	8	itype
1307	CONFIGTYPE_LTE_NBCELL	4	8	istime
1307	CONFIGTYPE_LTE_NBCELL	5	8	ietime
1308	CONFIGTYPE_NR_BTS	1	8	iid
1308	CONFIGTYPE_NR_BTS	2	8	istime
1308	CONFIGTYPE_NR_BTS	3	8	ietime
1308	CONFIGTYPE_NR_BTS	4	6	strenodebname
1308	CONFIGTYPE_NR_BTS	5	8	ienodebid
1308	CONFIGTYPE_NR_BTS	6	8	ilongitude
1308	CONFIGTYPE_NR_BTS	7	8	ilatitude
1308	CONFIGTYPE_NR_BTS	8	6	strdoor
1308	CONFIGTYPE_NR_BTS	9	6	strcomment
1309	CONFIGTYPE_NR_CELL	1	8	iid
1309	CONFIGTYPE_NR_CELL	2	8	istime
1309	CONFIGTYPE_NR_CELL	3	8	ietime
1309	CONFIGTYPE_NR_CELL	4	6	strcellcode
1309	CONFIGTYPE_NR_CELL	5	6	strcellname
1309	CONFIGTYPE_NR_CELL	6	8	igNodeBid
1309	CONFIGTYPE_NR_CELL	7	8	itac
1309	CONFIGTYPE_NR_CELL	8	14	inci
1309	CONFIGTYPE_NR_CELL	9	8	icellid
1309	CONFIGTYPE_NR_CELL	10	8	isectorid
1309	CONFIGTYPE_NR_CELL	11	8	ipci
1309	CONFIGTYPE_NR_CELL	12	8	iarfcn
1309	CONFIGTYPE_NR_CELL	13	8	issb_gscn
1309	CONFIGTYPE_NR_CELL	14	6	cgi
1309	CONFIGTYPE_NR_CELL	15	6	strcomment
1310	CONFIGTYPE_NR_PD	1	8	iid
1310	CONFIGTYPE_NR_PD	2	8	istime
1310	CONFIGTYPE_NR_PD	3	8	ietime
1310	CONFIGTYPE_NR_PD	4	8	icellid
1310	CONFIGTYPE_NR_PD	5	8	ilongitude
1310	CONFIGTYPE_NR_PD	6	8	ilatitude
1310	CONFIGTYPE_NR_PD	7	8	iangle_dir
1310	CONFIGTYPE_NR_PD	8	8	iangle_ob
1310	CONFIGTYPE_NR_PD	9	8	ialtitude
1310	CONFIGTYPE_NR_PD	10	6	strcomment
1311	CONFIGTYPE_NR_NBCELL	1	8	icellid
1311	CONFIGTYPE_NR_NBCELL	2	8	inbcellid
1311	CONFIGTYPE_NR_NBCELL	3	8	itype
1311	CONFIGTYPE_NR_NBCELL	4	8	istime
1311	CONFIGTYPE_NR_NBCELL	5	8	ietime
1400	SEARCH_RESULTTYPE_FILEPATHNAME	1	6	返回文件路径及名称
1400	SEARCH_RESULTTYPE_FILEPATHNAME	2	8	ifiletype
1600	SEARCHTYPE_CQTMNG_PLACE_INFO	1	8	[id] [int]
1600	SEARCHTYPE_CQTMNG_PLACE_INFO	2	6	[name] [varchar]
1600	SEARCHTYPE_CQTMNG_PLACE_INFO	3	8	[longitude] [int]
1600	SEARCHTYPE_CQTMNG_PLACE_INFO	4	8	[latitude] [int]
1600	SEARCHTYPE_CQTMNG_PLACE_INFO	5	8	[vipType] [int]
1600	SEARCHTYPE_CQTMNG_PLACE_INFO	6	8	[areasize] [int]
1600	SEARCHTYPE_CQTMNG_PLACE_INFO	7	8	[floor] [int]
1600	SEARCHTYPE_CQTMNG_PLACE_INFO	8	8	[type] [int]
1600	SEARCHTYPE_CQTMNG_PLACE_INFO	9	8	[status] [int]
1600	SEARCHTYPE_CQTMNG_PLACE_INFO	10	8	[last_date] [int]
1600	SEARCHTYPE_CQTMNG_PLACE_INFO	11	6	area_name
1600	SEARCHTYPE_CQTMNG_PLACE_INFO	12	6	area_name2
1600	SEARCHTYPE_CQTMNG_PLACE_INFO	13	6	[bak] [varchar]
1601	SEARCHTYPE_CQTMNG_PLACESUB_INFO	1	8	[sub_id] [int]
1601	SEARCHTYPE_CQTMNG_PLACESUB_INFO	2	8	[place_id] [int]
1601	SEARCHTYPE_CQTMNG_PLACESUB_INFO	3	6	[name] [varchar]
1601	SEARCHTYPE_CQTMNG_PLACESUB_INFO	4	8	[longitude] [int]
1601	SEARCHTYPE_CQTMNG_PLACESUB_INFO	5	8	[latitude] [int]
1601	SEARCHTYPE_CQTMNG_PLACESUB_INFO	6	8	[status] [int]
1601	SEARCHTYPE_CQTMNG_PLACESUB_INFO	7	8	[last_date] [int]
1601	SEARCHTYPE_CQTMNG_PLACESUB_INFO	8	6	[bak] [varchar]
1602	SEARCHTYPE_CQTMNG_CDMA_INFO	1	8	[place_id] [int]
1602	SEARCHTYPE_CQTMNG_CDMA_INFO	2	8	[sub_id] [int]
1602	SEARCHTYPE_CQTMNG_CDMA_INFO	3	8	[carrier] [int]
1602	SEARCHTYPE_CQTMNG_CDMA_INFO	4	5	[rxLev90pct] [float]
1602	SEARCHTYPE_CQTMNG_CDMA_INFO	5	5	[rxLev94pct] [float]
1602	SEARCHTYPE_CQTMNG_CDMA_INFO	6	5	[droppct] [float]
1602	SEARCHTYPE_CQTMNG_CDMA_INFO	7	5	[connectpct] [float]
1602	SEARCHTYPE_CQTMNG_CDMA_INFO	8	5	[rxqualpct] [float]
1602	SEARCHTYPE_CQTMNG_CDMA_INFO	9	5	[setupdur] [float]
1602	SEARCHTYPE_CQTMNG_CDMA_INFO	10	5	[reserv1] [float]
1602	SEARCHTYPE_CQTMNG_CDMA_INFO	11	5	[reserv2] [float]
1602	SEARCHTYPE_CQTMNG_CDMA_INFO	12	5	[reserv3] [float]
1602	SEARCHTYPE_CQTMNG_CDMA_INFO	13	5	[reserv4] [float]
1602	SEARCHTYPE_CQTMNG_CDMA_INFO	14	5	[reserv5] [float]
1602	SEARCHTYPE_CQTMNG_CDMA_INFO	15	8	[last_date] [int]
1602	SEARCHTYPE_CQTMNG_CDMA_INFO	16	8	[iprojecttype] [int]
1603	SEARCHTYPE_CQTMNG_GSM_INFO	1	8	[place_id] [int]
1603	SEARCHTYPE_CQTMNG_GSM_INFO	2	8	[sub_id] [int]
1603	SEARCHTYPE_CQTMNG_GSM_INFO	3	8	[carrier] [int]
1603	SEARCHTYPE_CQTMNG_GSM_INFO	4	5	[rxLev90pct] [float]
1603	SEARCHTYPE_CQTMNG_GSM_INFO	5	5	[rxLev94pct] [float]
1603	SEARCHTYPE_CQTMNG_GSM_INFO	6	5	[droppct] [float]
1603	SEARCHTYPE_CQTMNG_GSM_INFO	7	5	[connectpct] [float]
1603	SEARCHTYPE_CQTMNG_GSM_INFO	8	5	[rxqualpct] [float]
1603	SEARCHTYPE_CQTMNG_GSM_INFO	9	5	[setupdur] [float]
1603	SEARCHTYPE_CQTMNG_GSM_INFO	10	5	[reserv1] [float]
1603	SEARCHTYPE_CQTMNG_GSM_INFO	11	5	[reserv2] [float]
1603	SEARCHTYPE_CQTMNG_GSM_INFO	12	5	[reserv3] [float]
1603	SEARCHTYPE_CQTMNG_GSM_INFO	13	5	[reserv4] [float]
1603	SEARCHTYPE_CQTMNG_GSM_INFO	14	5	[reserv5] [float]
1603	SEARCHTYPE_CQTMNG_GSM_INFO	15	8	[last_date] [int]
1603	SEARCHTYPE_CQTMNG_GSM_INFO	16	8	[iprojecttype] [int]
1604	SEARCHTYPE_CQTMNG_LOG_INFO	1	8	[ifileid] [int]
1604	SEARCHTYPE_CQTMNG_LOG_INFO	2	6	[logtbname] [varchar]
1604	SEARCHTYPE_CQTMNG_LOG_INFO	3	8	[istime] [int]
1604	SEARCHTYPE_CQTMNG_LOG_INFO	4	8	[place_id] [int]
1604	SEARCHTYPE_CQTMNG_LOG_INFO	5	8	[place_sub_id] [int]
1604	SEARCHTYPE_CQTMNG_LOG_INFO	6	5	[reserv1] [float]
1604	SEARCHTYPE_CQTMNG_LOG_INFO	7	5	[reserv2] [float]
1604	SEARCHTYPE_CQTMNG_LOG_INFO	8	5	[reserv3] [float]
1604	SEARCHTYPE_CQTMNG_LOG_INFO	9	5	[reserv4] [float]
1604	SEARCHTYPE_CQTMNG_LOG_INFO	10	5	[reserv5] [float]
1604	SEARCHTYPE_CQTMNG_LOG_INFO	11	5	[reserv6] [float]
1604	SEARCHTYPE_CQTMNG_LOG_INFO	12	5	[reserv7] [float]
1604	SEARCHTYPE_CQTMNG_LOG_INFO	13	5	[reserv8] [float]
1604	SEARCHTYPE_CQTMNG_LOG_INFO	14	5	[reserv9] [float]
1604	SEARCHTYPE_CQTMNG_LOG_INFO	15	5	[reserv10] [float]
1604	SEARCHTYPE_CQTMNG_LOG_INFO	16	6	strfilename
1604	SEARCHTYPE_CQTMNG_LOG_INFO	17	8	iprojecttype
1604	SEARCHTYPE_CQTMNG_LOG_INFO	18	8	iservicetype int
1604	SEARCHTYPE_CQTMNG_LOG_INFO	19	8	icarriertype int
1604	SEARCHTYPE_CQTMNG_LOG_INFO	20	8	Status int
1604	SEARCHTYPE_CQTMNG_LOG_INFO	21	8	istime int
1605	SEARCHTYPE_CELL_ALARM_INFO	1	8	begin_time int
1605	SEARCHTYPE_CELL_ALARM_INFO	2	8	end_time int
1605	SEARCHTYPE_CELL_ALARM_INFO	3	5	valid float
1605	SEARCHTYPE_CELL_ALARM_INFO	4	6	cell_name varhcar
1605	SEARCHTYPE_CELL_ALARM_INFO	5	6	vendor_name varchar
1605	SEARCHTYPE_CELL_ALARM_INFO	6	6	alarm_type varchar
1605	SEARCHTYPE_CELL_ALARM_INFO	7	6	alarm_level varchar
1605	SEARCHTYPE_CELL_ALARM_INFO	8	6	name varcahr
1605	SEARCHTYPE_CELL_ALARM_INFO	9	6	description varchar
1605	SEARCHTYPE_CELL_ALARM_INFO	10	6	description
1605	SEARCHTYPE_CELL_ALARM_INFO	11	6	description
1605	SEARCHTYPE_CELL_ALARM_INFO	12	6	description
1605	SEARCHTYPE_CELL_ALARM_INFO	13	6	description
1605	SEARCHTYPE_CELL_ALARM_INFO	14	6	description
1605	SEARCHTYPE_CELL_ALARM_INFO	15	6	description
1605	SEARCHTYPE_CELL_ALARM_INFO	16	6	description
1606	SEARCHTYPE_CELL_PROPERTY_LN_INFO	1	6	type nvarchar
1606	SEARCHTYPE_CELL_PROPERTY_LN_INFO	2	6	CITYNAME VARCHAR2(30)
1606	SEARCHTYPE_CELL_PROPERTY_LN_INFO	3	6	DEVICENAME VARCHAR2(30)
1606	SEARCHTYPE_CELL_PROPERTY_LN_INFO	4	6	BSCNAME VARCHAR2(30)
1606	SEARCHTYPE_CELL_PROPERTY_LN_INFO	5	6	CELLNAME VARCHAR2(50)
1606	SEARCHTYPE_CELL_PROPERTY_LN_INFO	6	5	CELL_LAC NUMBER
1606	SEARCHTYPE_CELL_PROPERTY_LN_INFO	7	5	CELL_CI NUMBER
1606	SEARCHTYPE_CELL_PROPERTY_LN_INFO	8	8	LOGTIME int
1606	SEARCHTYPE_CELL_PROPERTY_LN_INFO	9	5	VIEW001 NUMBER
1606	SEARCHTYPE_CELL_PROPERTY_LN_INFO	10	5	VIEW002 NUMBER
1606	SEARCHTYPE_CELL_PROPERTY_LN_INFO	11	5	VIEW003 NUMBER
1606	SEARCHTYPE_CELL_PROPERTY_LN_INFO	12	5	VIEW004 NUMBER
1606	SEARCHTYPE_CELL_PROPERTY_LN_INFO	13	5	VIEW005 NUMBER
1606	SEARCHTYPE_CELL_PROPERTY_LN_INFO	14	5	VIEW006 NUMBER
1606	SEARCHTYPE_CELL_PROPERTY_LN_INFO	15	5	VIEW007 NUMBER
1606	SEARCHTYPE_CELL_PROPERTY_LN_INFO	16	5	VIEW008 NUMBER
1606	SEARCHTYPE_CELL_PROPERTY_LN_INFO	17	5	VIEW009 NUMBER
1606	SEARCHTYPE_CELL_PROPERTY_LN_INFO	18	5	VIEW010 NUMBER
1606	SEARCHTYPE_CELL_PROPERTY_LN_INFO	19	5	VIEW011 NUMBER
1606	SEARCHTYPE_CELL_PROPERTY_LN_INFO	20	5	VIEW012 NUMBER
1606	SEARCHTYPE_CELL_PROPERTY_LN_INFO	21	5	VIEW013 NUMBER
1606	SEARCHTYPE_CELL_PROPERTY_LN_INFO	22	5	VIEW014 NUMBER
1606	SEARCHTYPE_CELL_PROPERTY_LN_INFO	23	5	VIEW015 NUMBER
1606	SEARCHTYPE_CELL_PROPERTY_LN_INFO	24	5	VIEW016 NUMBER
1606	SEARCHTYPE_CELL_PROPERTY_LN_INFO	25	5	VIEW017 NUMBER
1606	SEARCHTYPE_CELL_PROPERTY_LN_INFO	26	5	VIEW018 NUMBER
1606	SEARCHTYPE_CELL_PROPERTY_LN_INFO	27	5	VIEW019 NUMBER
1606	SEARCHTYPE_CELL_PROPERTY_LN_INFO	28	5	VIEW020 NUMBER
1607	SEARCHTYPE_ExcelBlock_INFO	1	8	id int 
1607	SEARCHTYPE_ExcelBlock_INFO	2	6	peers varchar
1607	SEARCHTYPE_ExcelBlock_INFO	3	8	status int 
1607	SEARCHTYPE_ExcelBlock_INFO	4	8	created_date int 
1607	SEARCHTYPE_ExcelBlock_INFO	5	8	closed_date int 
1607	SEARCHTYPE_ExcelBlock_INFO	6	8	first_abnormal_date int 
1607	SEARCHTYPE_ExcelBlock_INFO	7	8	last_abnormal_date int 
1607	SEARCHTYPE_ExcelBlock_INFO	8	8	abnormal_event_count int 
1607	SEARCHTYPE_ExcelBlock_INFO	9	6	area_names varchar 
1607	SEARCHTYPE_ExcelBlock_INFO	10	6	road_names varchar 
1607	SEARCHTYPE_ExcelBlock_INFO	11	8	resv1 int 
1607	SEARCHTYPE_ExcelBlock_INFO	12	8	resv2 int 
1607	SEARCHTYPE_ExcelBlock_INFO	13	8	resv3 int 
1607	SEARCHTYPE_ExcelBlock_INFO	14	8	resv4 int 
1607	SEARCHTYPE_ExcelBlock_INFO	15	8	resv5 int
1608	SEARCHTYPE_ExcelBlock_Item_INFO	1	8	blockid int
1608	SEARCHTYPE_ExcelBlock_Item_INFO	2	6	codeno varchar
1608	SEARCHTYPE_ExcelBlock_Item_INFO	3	6	road varchar
1608	SEARCHTYPE_ExcelBlock_Item_INFO	4	6	area varchar
1608	SEARCHTYPE_ExcelBlock_Item_INFO	5	8	longitude int
1608	SEARCHTYPE_ExcelBlock_Item_INFO	6	8	latitude int
1608	SEARCHTYPE_ExcelBlock_Item_INFO	7	6	probtype varchar
1608	SEARCHTYPE_ExcelBlock_Item_INFO	8	6	anadesc varchar
1608	SEARCHTYPE_ExcelBlock_Item_INFO	9	8	timevalue int
1608	SEARCHTYPE_ExcelBlock_Item_INFO	10	6	logfilestr varchar
1608	SEARCHTYPE_ExcelBlock_Item_INFO	11	6	process varchar
1608	SEARCHTYPE_ExcelBlock_Item_INFO	12	6	company varchar
1608	SEARCHTYPE_ExcelBlock_Item_INFO	13	6	groupstr varchar
1608	SEARCHTYPE_ExcelBlock_Item_INFO	14	6	sectionstr varchar
1608	SEARCHTYPE_ExcelBlock_Item_INFO	15	6	tunetype varchar
1608	SEARCHTYPE_ExcelBlock_Item_INFO	16	6	resvstr1 varchar
1608	SEARCHTYPE_ExcelBlock_Item_INFO	17	6	resvstr2 varchar
1608	SEARCHTYPE_ExcelBlock_Item_INFO	18	8	resv1 int
1608	SEARCHTYPE_ExcelBlock_Item_INFO	19	8	resv2 int
1608	SEARCHTYPE_ExcelBlock_Item_INFO	20	8	resv3 int
1608	SEARCHTYPE_ExcelBlock_Item_INFO	21	8	resv4 int
1608	SEARCHTYPE_ExcelBlock_Item_INFO	22	8	resv5 int
1609	SEARCHTYPE_ExcelBlock_Local_Item_INFO	1	8	blockid int
1609	SEARCHTYPE_ExcelBlock_Local_Item_INFO	2	6	codeno varchar
1609	SEARCHTYPE_ExcelBlock_Local_Item_INFO	3	6	road varchar
1609	SEARCHTYPE_ExcelBlock_Local_Item_INFO	4	6	area varchar
1609	SEARCHTYPE_ExcelBlock_Local_Item_INFO	5	8	longitude int
1609	SEARCHTYPE_ExcelBlock_Local_Item_INFO	6	8	latitude int
1609	SEARCHTYPE_ExcelBlock_Local_Item_INFO	7	6	probtype varchar
1609	SEARCHTYPE_ExcelBlock_Local_Item_INFO	8	6	anadesc varchar
1609	SEARCHTYPE_ExcelBlock_Local_Item_INFO	9	8	timevalue int
1609	SEARCHTYPE_ExcelBlock_Local_Item_INFO	10	6	logfilestr varchar
1609	SEARCHTYPE_ExcelBlock_Local_Item_INFO	11	6	process varchar
1609	SEARCHTYPE_ExcelBlock_Local_Item_INFO	12	6	company varchar
1609	SEARCHTYPE_ExcelBlock_Local_Item_INFO	13	6	groupstr varchar
1609	SEARCHTYPE_ExcelBlock_Local_Item_INFO	14	6	sectionstr varchar
1609	SEARCHTYPE_ExcelBlock_Local_Item_INFO	15	6	tunetype varchar
1609	SEARCHTYPE_ExcelBlock_Local_Item_INFO	16	8	rpttimevalue int
1609	SEARCHTYPE_ExcelBlock_Local_Item_INFO	17	6	typeofstr varchar
1609	SEARCHTYPE_ExcelBlock_Local_Item_INFO	18	6	cellstr varchar
1609	SEARCHTYPE_ExcelBlock_Local_Item_INFO	19	6	tuneoption varchar
1609	SEARCHTYPE_ExcelBlock_Local_Item_INFO	20	6	indutyunit varchar
1609	SEARCHTYPE_ExcelBlock_Local_Item_INFO	21	6	doperiod varchar
1609	SEARCHTYPE_ExcelBlock_Local_Item_INFO	22	6	resvstr1 varchar
1609	SEARCHTYPE_ExcelBlock_Local_Item_INFO	23	6	resvstr2 varchar
1609	SEARCHTYPE_ExcelBlock_Local_Item_INFO	24	8	resv1 int
1609	SEARCHTYPE_ExcelBlock_Local_Item_INFO	25	8	resv2 int
1609	SEARCHTYPE_ExcelBlock_Local_Item_INFO	26	8	resv3 int
1609	SEARCHTYPE_ExcelBlock_Local_Item_INFO	27	8	resv4 int
1609	SEARCHTYPE_ExcelBlock_Local_Item_INFO	28	8	resv5 int
1610	SEARCHTYPE_REVIEW_CELLTRACE	1	8	isampleid int 
1610	SEARCHTYPE_REVIEW_CELLTRACE	2	8	ifileid int 
1610	SEARCHTYPE_REVIEW_CELLTRACE	3	8	itime int 
1610	SEARCHTYPE_REVIEW_CELLTRACE	4	2	wtimems smallint 
1610	SEARCHTYPE_REVIEW_CELLTRACE	5	8	irncid int 
1610	SEARCHTYPE_REVIEW_CELLTRACE	6	6	imsi varchar 
1610	SEARCHTYPE_REVIEW_CELLTRACE	7	6	tmsi varchar 
1610	SEARCHTYPE_REVIEW_CELLTRACE	8	8	icellid int 
1610	SEARCHTYPE_REVIEW_CELLTRACE	9	6	iinterfaceswitch int 
1610	SEARCHTYPE_REVIEW_CELLTRACE	10	6	ifreq int 
1610	SEARCHTYPE_REVIEW_CELLTRACE	11	2	wmoduleid smallint 
1610	SEARCHTYPE_REVIEW_CELLTRACE	12	2	wprocid smallint 
1610	SEARCHTYPE_REVIEW_CELLTRACE	13	8	idwid int 
1610	SEARCHTYPE_REVIEW_CELLTRACE	14	6	specialinfo varchar
1610	SEARCHTYPE_REVIEW_CELLTRACE	15	1	bdirection tinyint 
1610	SEARCHTYPE_REVIEW_CELLTRACE	16	8	imsgid int 
1610	SEARCHTYPE_REVIEW_CELLTRACE	17	6	msgname varchar
1610	SEARCHTYPE_REVIEW_CELLTRACE	18	6	msgl3name varchar
1610	SEARCHTYPE_REVIEW_CELLTRACE	19	2	wmsglenth smallint 
1610	SEARCHTYPE_REVIEW_CELLTRACE	20	9	hexcode image 
1610	SEARCHTYPE_REVIEW_CELLTRACE	21	9	hexcode image 
1610	SEARCHTYPE_REVIEW_CELLTRACE	22	9	hexcode image 
1610	SEARCHTYPE_REVIEW_CELLTRACE	23	9	hexcode image 
1610	SEARCHTYPE_REVIEW_CELLTRACE	24	9	hexcode image 
1610	SEARCHTYPE_REVIEW_CELLTRACE	25	9	hexcode image 
1610	SEARCHTYPE_REVIEW_CELLTRACE	26	9	hexcode image 
1610	SEARCHTYPE_REVIEW_CELLTRACE	27	9	hexcode image 
1610	SEARCHTYPE_REVIEW_CELLTRACE	28	9	msghead image
1610	SEARCHTYPE_REVIEW_CELLTRACE	29	9	msghead image
1610	SEARCHTYPE_REVIEW_CELLTRACE	30	9	msghead image
1610	SEARCHTYPE_REVIEW_CELLTRACE	31	9	msghead image
1610	SEARCHTYPE_REVIEW_CELLTRACE	32	9	msghead image
1610	SEARCHTYPE_REVIEW_CELLTRACE	33	9	msghead image
1610	SEARCHTYPE_REVIEW_CELLTRACE	34	9	msghead image
1610	SEARCHTYPE_REVIEW_CELLTRACE	35	9	msghead image
1611	SEARCHTYPE_REVIEW_SCAN_WCDMA	1	8	ifileid int
1611	SEARCHTYPE_REVIEW_SCAN_WCDMA	2	8	isampleid int
1611	SEARCHTYPE_REVIEW_SCAN_WCDMA	3	8	ilongitude int
1611	SEARCHTYPE_REVIEW_SCAN_WCDMA	4	8	ilatitude int
1611	SEARCHTYPE_REVIEW_SCAN_WCDMA	5	8	itime int
1611	SEARCHTYPE_REVIEW_SCAN_WCDMA	6	2	wtimems smallint
1611	SEARCHTYPE_REVIEW_SCAN_WCDMA	7	1	bms tinyint
1611	SEARCHTYPE_REVIEW_SCAN_WCDMA	8	9	CPICH image
1611	SEARCHTYPE_REVIEW_SCAN_WCDMA	9	9	CPICH image
1611	SEARCHTYPE_REVIEW_SCAN_WCDMA	10	9	CPICH image
1611	SEARCHTYPE_REVIEW_SCAN_WCDMA	11	9	CPICH image
1611	SEARCHTYPE_REVIEW_SCAN_WCDMA	12	9	CPICH image
1611	SEARCHTYPE_REVIEW_SCAN_WCDMA	13	9	CPICH image
1611	SEARCHTYPE_REVIEW_SCAN_WCDMA	14	9	CPICH image
1611	SEARCHTYPE_REVIEW_SCAN_WCDMA	15	9	CPICH image
1611	SEARCHTYPE_REVIEW_SCAN_WCDMA	16	9	FINGER image
1611	SEARCHTYPE_REVIEW_SCAN_WCDMA	17	9	FINGER image
1611	SEARCHTYPE_REVIEW_SCAN_WCDMA	18	9	FINGER image
1611	SEARCHTYPE_REVIEW_SCAN_WCDMA	19	9	FINGER image
1611	SEARCHTYPE_REVIEW_SCAN_WCDMA	20	9	FINGER image
1611	SEARCHTYPE_REVIEW_SCAN_WCDMA	21	9	FINGER image
1611	SEARCHTYPE_REVIEW_SCAN_WCDMA	22	9	FINGER image
1611	SEARCHTYPE_REVIEW_SCAN_WCDMA	23	9	FINGER image
1611	SEARCHTYPE_REVIEW_SCAN_WCDMA	24	9	SCH image
1611	SEARCHTYPE_REVIEW_SCAN_WCDMA	25	9	SCH image
1611	SEARCHTYPE_REVIEW_SCAN_WCDMA	26	9	SCH image
1611	SEARCHTYPE_REVIEW_SCAN_WCDMA	27	9	SCH image
1611	SEARCHTYPE_REVIEW_SCAN_WCDMA	28	9	SCH image
1611	SEARCHTYPE_REVIEW_SCAN_WCDMA	29	9	SCH image
1611	SEARCHTYPE_REVIEW_SCAN_WCDMA	30	9	SCH image
1611	SEARCHTYPE_REVIEW_SCAN_WCDMA	31	9	SCH image
1611	SEARCHTYPE_REVIEW_SCAN_WCDMA	32	9	OtherInfo image
1611	SEARCHTYPE_REVIEW_SCAN_WCDMA	33	9	OtherInfo image
1611	SEARCHTYPE_REVIEW_SCAN_WCDMA	34	9	OtherInfo image
1611	SEARCHTYPE_REVIEW_SCAN_WCDMA	35	9	OtherInfo image
1611	SEARCHTYPE_REVIEW_SCAN_WCDMA	36	9	OtherInfo image
1611	SEARCHTYPE_REVIEW_SCAN_WCDMA	37	9	OtherInfo image
1611	SEARCHTYPE_REVIEW_SCAN_WCDMA	38	9	OtherInfo image
1611	SEARCHTYPE_REVIEW_SCAN_WCDMA	39	9	OtherInfo image
1611	SEARCHTYPE_REVIEW_SCAN_WCDMA	40	9	reserved image
1611	SEARCHTYPE_REVIEW_SCAN_WCDMA	41	9	reserved image
1611	SEARCHTYPE_REVIEW_SCAN_WCDMA	42	9	reserved image
1611	SEARCHTYPE_REVIEW_SCAN_WCDMA	43	9	reserved image
1611	SEARCHTYPE_REVIEW_SCAN_WCDMA	44	9	reserved image
1611	SEARCHTYPE_REVIEW_SCAN_WCDMA	45	9	reserved image
1611	SEARCHTYPE_REVIEW_SCAN_WCDMA	46	9	reserved image
1611	SEARCHTYPE_REVIEW_SCAN_WCDMA	47	9	reserved image
1612	SEARCHTYPE_SITESIMU_SAMPLE	1	8	longitude
1612	SEARCHTYPE_SITESIMU_SAMPLE	2	8	latitude
1612	SEARCHTYPE_SITESIMU_SAMPLE	3	5	rxlev
1612	SEARCHTYPE_SITESIMU_SAMPLE	4	6	orgcellname
1613	SEARCHTYPE_CELL_TRAFFIC	1	8	cellid
1613	SEARCHTYPE_CELL_TRAFFIC	2	6	cellcode
1613	SEARCHTYPE_CELL_TRAFFIC	3	5	traffic
1613	SEARCHTYPE_CELL_TRAFFIC	4	8	channel_num
1614	SEARCHTYPE_REVIEW_ULGSM_NORMAL	1	8	isampleid int
1614	SEARCHTYPE_REVIEW_ULGSM_NORMAL	2	8	ifileid int
1614	SEARCHTYPE_REVIEW_ULGSM_NORMAL	3	8	itime int] NULL,
1614	SEARCHTYPE_REVIEW_ULGSM_NORMAL	4	2	wtimems smallint
1614	SEARCHTYPE_REVIEW_ULGSM_NORMAL	5	1	bms tinyint
1614	SEARCHTYPE_REVIEW_ULGSM_NORMAL	6	8	ilongitude int
1614	SEARCHTYPE_REVIEW_ULGSM_NORMAL	7	8	ilatitude int
1614	SEARCHTYPE_REVIEW_ULGSM_NORMAL	8	2	wmode smallint
1614	SEARCHTYPE_REVIEW_ULGSM_NORMAL	9	2	wMCC smallint
1614	SEARCHTYPE_REVIEW_ULGSM_NORMAL	10	1	bMNC tinyint
1614	SEARCHTYPE_REVIEW_ULGSM_NORMAL	11	8	iLAC int
1614	SEARCHTYPE_REVIEW_ULGSM_NORMAL	12	2	wRAC smallint
1614	SEARCHTYPE_REVIEW_ULGSM_NORMAL	13	8	iCI int
1614	SEARCHTYPE_REVIEW_ULGSM_NORMAL	14	2	wBCCH smallint
1614	SEARCHTYPE_REVIEW_ULGSM_NORMAL	15	1	bBSIC tinyint
1614	SEARCHTYPE_REVIEW_ULGSM_NORMAL	16	2	wRxLevFull_DL smallint
1614	SEARCHTYPE_REVIEW_ULGSM_NORMAL	17	2	wRxLevFull_UL smallint
1614	SEARCHTYPE_REVIEW_ULGSM_NORMAL	18	2	wRxLevSub_DL smallint
1614	SEARCHTYPE_REVIEW_ULGSM_NORMAL	19	2	wRxLevSub_UL smallint
1614	SEARCHTYPE_REVIEW_ULGSM_NORMAL	20	1	bRxQualFull_DL tinyint
1614	SEARCHTYPE_REVIEW_ULGSM_NORMAL	21	1	bRxQualFull_UL tinyint
1614	SEARCHTYPE_REVIEW_ULGSM_NORMAL	22	1	bRxQualSub_DL tinyint
1614	SEARCHTYPE_REVIEW_ULGSM_NORMAL	23	1	bRxQualSub_UL tinyint
1614	SEARCHTYPE_REVIEW_ULGSM_NORMAL	24	1	bDTX_DL tinyint
1614	SEARCHTYPE_REVIEW_ULGSM_NORMAL	25	1	bDTX_UL tinyint
1614	SEARCHTYPE_REVIEW_ULGSM_NORMAL	26	1	bPathLoss_Full tinyint
1614	SEARCHTYPE_REVIEW_ULGSM_NORMAL	27	1	bPathLoss_Sub tinyint
1614	SEARCHTYPE_REVIEW_ULGSM_NORMAL	28	1	bPower_Level_BS tinyint
1614	SEARCHTYPE_REVIEW_ULGSM_NORMAL	29	1	bPower_Level_MS tinyint
1614	SEARCHTYPE_REVIEW_ULGSM_NORMAL	30	2	wSQI smallint
1614	SEARCHTYPE_REVIEW_ULGSM_NORMAL	31	1	bTA tinyint
1614	SEARCHTYPE_REVIEW_ULGSM_NORMAL	32	1	bHP tinyint
1614	SEARCHTYPE_REVIEW_ULGSM_NORMAL	33	1	bChannelType tinyint
1614	SEARCHTYPE_REVIEW_ULGSM_NORMAL	34	1	bMAIO tinyint
1614	SEARCHTYPE_REVIEW_ULGSM_NORMAL	35	1	bHSN tinyint
1614	SEARCHTYPE_REVIEW_ULGSM_NORMAL	36	1	bTimesolt tinyint
1614	SEARCHTYPE_REVIEW_ULGSM_NORMAL	37	9	mNBCell image
1614	SEARCHTYPE_REVIEW_ULGSM_NORMAL	38	9	mNBCell image
1614	SEARCHTYPE_REVIEW_ULGSM_NORMAL	39	9	mNBCell image
1614	SEARCHTYPE_REVIEW_ULGSM_NORMAL	40	9	mNBCell image
1614	SEARCHTYPE_REVIEW_ULGSM_NORMAL	41	9	mNBCell image
1614	SEARCHTYPE_REVIEW_ULGSM_NORMAL	42	9	mNBCell image
1614	SEARCHTYPE_REVIEW_ULGSM_NORMAL	43	9	mNBCell image
1614	SEARCHTYPE_REVIEW_ULGSM_NORMAL	44	9	mNBCell image
1614	SEARCHTYPE_REVIEW_ULGSM_NORMAL	45	9	mReserved image
1614	SEARCHTYPE_REVIEW_ULGSM_NORMAL	46	9	mReserved image
1614	SEARCHTYPE_REVIEW_ULGSM_NORMAL	47	9	mReserved image
1614	SEARCHTYPE_REVIEW_ULGSM_NORMAL	48	9	mReserved image
1614	SEARCHTYPE_REVIEW_ULGSM_NORMAL	49	9	mReserved image
1614	SEARCHTYPE_REVIEW_ULGSM_NORMAL	50	9	mReserved image
1614	SEARCHTYPE_REVIEW_ULGSM_NORMAL	51	9	mReserved image
1614	SEARCHTYPE_REVIEW_ULGSM_NORMAL	52	9	mReserved image
1615	SEARCHTYPE_NEWBLOCK_ID	1	8	block_id
1615	SEARCHTYPE_NEWBLOCK_ID	2	8	new_block_id
1616	SEARCHTYPE_NEWBLOCK_INFO	1	8	new_block_id int 
1616	SEARCHTYPE_NEWBLOCK_INFO	2	8	week_monday int 
1616	SEARCHTYPE_NEWBLOCK_INFO	3	8	hot_status int 
1616	SEARCHTYPE_NEWBLOCK_INFO	4	8	black_status int 
1616	SEARCHTYPE_NEWBLOCK_INFO	5	8	hot_intending int 
1616	SEARCHTYPE_NEWBLOCK_INFO	6	8	black_intending int 
1616	SEARCHTYPE_NEWBLOCK_INFO	7	8	user_count int 
1616	SEARCHTYPE_NEWBLOCK_INFO	8	8	bill_count int 
1616	SEARCHTYPE_NEWBLOCK_INFO	9	6	areaDes varchar
1616	SEARCHTYPE_NEWBLOCK_INFO	10	6	jdDes varchar
1616	SEARCHTYPE_NEWBLOCK_INFO	11	6	reasonDes varchar
1616	SEARCHTYPE_NEWBLOCK_INFO	12	6	solutionDes varchar
1616	SEARCHTYPE_NEWBLOCK_INFO	13	5	HotWeekScale float
1616	SEARCHTYPE_NEWBLOCK_INFO	14	6	MainOldBlock varchar
1617	SEARCHTYPE_TDSCDMA_NETRATE_IMAGE	1	8	itllongitude
1617	SEARCHTYPE_TDSCDMA_NETRATE_IMAGE	2	8	itllatitude
1617	SEARCHTYPE_TDSCDMA_NETRATE_IMAGE	3	10	image1
2000	SEARCHTYPE_FILE_LIST	1	8	fileid
2000	SEARCHTYPE_FILE_LIST	2	8	testtype
2000	SEARCHTYPE_FILE_LIST	3	6	logname
2000	SEARCHTYPE_FILE_LIST	4	6	tbname
2001	SEARCHTYPE_TB_LIST	1	8	fileid
2001	SEARCHTYPE_TB_LIST	2	8	testtype
2001	SEARCHTYPE_TB_LIST	3	6	logname
2001	SEARCHTYPE_TB_LIST	4	6	msgname
2001	SEARCHTYPE_TB_LIST	5	6	eventname
2001	SEARCHTYPE_TB_LIST	6	6	samplename
2001	SEARCHTYPE_TB_LIST	7	6	sample2name
2001	SEARCHTYPE_TB_LIST	8	6	suffix
2002	SEARCHTYPE_FILE_LIST_SAMPLE	1	8	fileid
2002	SEARCHTYPE_FILE_LIST_SAMPLE	2	8	testtype
2002	SEARCHTYPE_FILE_LIST_SAMPLE	3	6	logname
2002	SEARCHTYPE_FILE_LIST_SAMPLE	4	6	tbname
2002	SEARCHTYPE_FILE_LIST_SAMPLE	5	8	itllongitude
2002	SEARCHTYPE_FILE_LIST_SAMPLE	6	8	itllatitude
2002	SEARCHTYPE_FILE_LIST_SAMPLE	7	8	ibrlongitude
2002	SEARCHTYPE_FILE_LIST_SAMPLE	8	8	ibrlatitude
2002	SEARCHTYPE_FILE_LIST_SAMPLE	9	8	samplenum
2003	DIYSEARCHTYPE_FILE_LIST	1	8	fileid
2003	DIYSEARCHTYPE_FILE_LIST	2	8	testtype
2003	DIYSEARCHTYPE_FILE_LIST	3	6	logname
2003	DIYSEARCHTYPE_FILE_LIST	4	6	tbname
2003	DIYSEARCHTYPE_FILE_LIST	5	8	itesttype
2003	DIYSEARCHTYPE_FILE_LIST	6	8	idevicetype
2003	DIYSEARCHTYPE_FILE_LIST	7	8	ifiletype
2003	DIYSEARCHTYPE_FILE_LIST	8	8	iservicetype
2003	DIYSEARCHTYPE_FILE_LIST	9	8	icarriertype
2003	DIYSEARCHTYPE_FILE_LIST	10	8	iprojecttype
2003	DIYSEARCHTYPE_FILE_LIST	11	8	ibatch
2003	DIYSEARCHTYPE_FILE_LIST	12	8	ifileid
2003	DIYSEARCHTYPE_FILE_LIST	13	6	strfilename
2004	SEARCHTYPE_FILE_LIST_SAMPLE2	1	8	fileid
2004	SEARCHTYPE_FILE_LIST_SAMPLE2	2	8	testtype
2004	SEARCHTYPE_FILE_LIST_SAMPLE2	3	8	iservicetype
2004	SEARCHTYPE_FILE_LIST_SAMPLE2	4	6	logname
2004	SEARCHTYPE_FILE_LIST_SAMPLE2	5	6	tbname
2004	SEARCHTYPE_FILE_LIST_SAMPLE2	6	8	itllongitude
2004	SEARCHTYPE_FILE_LIST_SAMPLE2	7	8	itllatitude
2004	SEARCHTYPE_FILE_LIST_SAMPLE2	8	8	ibrlongitude
2004	SEARCHTYPE_FILE_LIST_SAMPLE2	9	8	ibrlatitude
2004	SEARCHTYPE_FILE_LIST_SAMPLE2	10	8	samplenum
2005	SEARCHTYPE_SCAN_TD_CELL	1	8	ifileid int
2005	SEARCHTYPE_SCAN_TD_CELL	2	8	isampleid int
2005	SEARCHTYPE_SCAN_TD_CELL	3	8	ilongitude int
2005	SEARCHTYPE_SCAN_TD_CELL	4	8	ilatitude int
2005	SEARCHTYPE_SCAN_TD_CELL	5	8	itime int
2005	SEARCHTYPE_SCAN_TD_CELL	6	2	wtimems smallint
2005	SEARCHTYPE_SCAN_TD_CELL	7	1	bms tinyint
2005	SEARCHTYPE_SCAN_TD_CELL	8	8	icellid int 
2005	SEARCHTYPE_SCAN_TD_CELL	9	2	channel smallint
2005	SEARCHTYPE_SCAN_TD_CELL	10	1	cpi tinyint
2005	SEARCHTYPE_SCAN_TD_CELL	11	5	rscp float
2005	SEARCHTYPE_SCAN_TD_CELL	12	5	c_i float
2005	SEARCHTYPE_SCAN_TD_CELL	13	5	sir float
2005	SEARCHTYPE_SCAN_TD_CELL	14	5	ecio float
2005	SEARCHTYPE_SCAN_TD_CELL	15	5	bler float
2006	REQTYPE_CONFIG_SERVER_LIST	1	8	id_int
2006	REQTYPE_CONFIG_SERVER_LIST	2	6	ip_varchar
2006	REQTYPE_CONFIG_SERVER_LIST	3	8	port_int
2006	REQTYPE_CONFIG_SERVER_LIST	4	8	nettype_int
2006	REQTYPE_CONFIG_SERVER_LIST	5	8	maxConnCnt int
2006	REQTYPE_CONFIG_SERVER_LIST	6	6	comment varchar
