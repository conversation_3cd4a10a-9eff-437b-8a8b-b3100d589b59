#!/bin/bash
echo 开始部署
app_name="DtDrvApp"
cur_path=`pwd`
log_dirname="logs"

app_dir="$cur_path/$app_name"
logs_dir="$cur_path/$log_dirname"

# echo $app_dir
# echo $logs_dir

if [ -f $app_dir ]
then
	pid=`ps -ef|grep $app_name|grep -v grep|grep -v kill|awk '{print $2}'`
	echo 进程id:$pid
	if [ ${pid} ] 
	then
		kill $pid
		echo 杀死进程:$pid
	fi

	echo 启动项目
	
	if [ ! -d $logs_dir ]
	then
		echo 创建目录：$logs_dir
		mkdir -p $logs_dir
	fi

	nohup ./DtDrvApp > logs/output.txt 2>&1 & 
	echo 完成部署
else
	echo 当前目录下没有 $app_name 文件，请放到与 $app_name 同级目录下运行部署
fi
