#!/bin/bash

# DtDrvApp.Core 手动启动脚本 (无需.NET SDK检查)

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 函数定义
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示欢迎信息
echo -e "${BLUE}"
echo "=================================================="
echo "    DtDrvApp.Core 手动启动脚本"
echo "=================================================="
echo -e "${NC}"

# 检查是否在正确的目录
if [ ! -f "DtDrvApp.Core.sln" ]; then
    log_error "请确保在DtDrvApp.Core项目根目录下运行此脚本"
    exit 1
fi

log_info "当前目录: $(pwd)"

# 选择启动方式
echo ""
log_info "请选择启动方式:"
echo "1) 使用Docker启动 (推荐 - 包含完整环境)"
echo "2) 手动安装.NET SDK后启动"
echo "3) 仅构建Docker镜像"
echo "4) 显示.NET SDK安装指南"
echo ""

read -p "请输入选择 (1-4): " choice

case $choice in
    1)
        start_with_docker
        ;;
    2)
        start_with_dotnet
        ;;
    3)
        build_docker_only
        ;;
    4)
        show_dotnet_install_guide
        ;;
    *)
        log_error "无效选择"
        exit 1
        ;;
esac

# 使用Docker启动
start_with_docker() {
    log_info "使用Docker启动完整环境..."
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装！"
        echo "请安装Docker: https://docs.docker.com/get-docker/"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "docker-compose未安装！"
        echo "请安装docker-compose"
        exit 1
    fi
    
    # 检查docker-compose文件
    if [ ! -f "docker-compose.yml" ]; then
        log_error "docker-compose.yml文件不存在"
        exit 1
    fi
    
    log_info "停止可能存在的旧容器..."
    docker-compose down 2>/dev/null || true
    
    log_info "构建Docker镜像..."
    docker-compose build
    
    if [ $? -ne 0 ]; then
        log_error "Docker镜像构建失败"
        exit 1
    fi
    
    log_info "启动服务..."
    docker-compose up -d
    
    if [ $? -ne 0 ]; then
        log_error "服务启动失败"
        exit 1
    fi
    
    log_info "等待服务启动..."
    sleep 15
    
    log_info "检查服务状态..."
    docker-compose ps
    
    log_success "服务启动成功！"
    echo ""
    echo "访问地址:"
    echo "  - HTTP API: http://localhost:5000"
    echo "  - HTTPS API: https://localhost:5001"
    echo "  - TCP服务: localhost:23456"
    echo "  - 健康检查: http://localhost:5000/health"
    echo "  - API文档: http://localhost:5000/swagger"
    echo "  - 数据库: localhost:1433 (SQL Server)"
    echo ""
    echo "常用命令:"
    echo "  - 查看日志: docker-compose logs -f"
    echo "  - 停止服务: docker-compose down"
    echo "  - 重启服务: docker-compose restart"
    
    # 测试健康检查
    log_info "测试服务健康状态..."
    sleep 5
    
    if command -v curl &> /dev/null; then
        if curl -f http://localhost:5000/health >/dev/null 2>&1; then
            log_success "健康检查通过！服务运行正常"
        else
            log_warning "健康检查失败，服务可能还在启动中"
            echo "请稍等片刻后访问 http://localhost:5000/health"
        fi
    else
        log_info "请访问 http://localhost:5000/health 检查服务状态"
    fi
}

# 使用.NET启动
start_with_dotnet() {
    log_info "尝试使用.NET SDK启动..."
    
    # 尝试不同的dotnet路径
    DOTNET_PATHS=(
        "/home/<USER>/dotnet"
        "/usr/bin/dotnet"
        "/usr/local/bin/dotnet"
        "dotnet"
    )
    
    DOTNET_CMD=""
    for path in "${DOTNET_PATHS[@]}"; do
        if command -v "$path" &> /dev/null; then
            # 测试是否能正常工作
            if $path --version &> /dev/null; then
                DOTNET_CMD="$path"
                log_success "找到可用的.NET SDK: $path"
                break
            fi
        fi
    done
    
    if [ -z "$DOTNET_CMD" ]; then
        log_error ".NET SDK未正确安装或配置"
        show_dotnet_install_guide
        exit 1
    fi
    
    # 显示版本
    VERSION=$($DOTNET_CMD --version)
    log_info ".NET SDK版本: $VERSION"
    
    # 还原依赖
    log_info "还原NuGet包..."
    $DOTNET_CMD restore
    
    if [ $? -ne 0 ]; then
        log_error "依赖还原失败"
        exit 1
    fi
    
    # 构建项目
    log_info "构建项目..."
    $DOTNET_CMD build --configuration Debug
    
    if [ $? -ne 0 ]; then
        log_error "项目构建失败"
        exit 1
    fi
    
    log_warning "注意：需要配置数据库连接"
    echo "请编辑 src/DtDrvApp.Api/appsettings.json 配置数据库连接字符串"
    echo ""
    
    read -p "数据库已配置好了吗？(y/n): " db_ready
    if [ "$db_ready" != "y" ] && [ "$db_ready" != "Y" ]; then
        show_database_config_help
        exit 0
    fi
    
    # 启动应用
    log_info "启动应用程序..."
    cd src/DtDrvApp.Api
    $DOTNET_CMD run
}

# 仅构建Docker镜像
build_docker_only() {
    log_info "仅构建Docker镜像..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装！"
        exit 1
    fi
    
    log_info "构建镜像..."
    docker build -t dtdrvapp-core:latest .
    
    if [ $? -eq 0 ]; then
        log_success "Docker镜像构建成功！"
        echo "镜像名称: dtdrvapp-core:latest"
        echo ""
        echo "运行镜像:"
        echo "docker run -p 23456:23456 -p 5000:5000 dtdrvapp-core:latest"
    else
        log_error "Docker镜像构建失败"
        exit 1
    fi
}

# 显示.NET SDK安装指南
show_dotnet_install_guide() {
    echo ""
    log_info ".NET 8.0 SDK 安装指南:"
    echo ""
    echo "Ubuntu/Debian:"
    echo "  wget https://packages.microsoft.com/config/ubuntu/22.04/packages-microsoft-prod.deb -O packages-microsoft-prod.deb"
    echo "  sudo dpkg -i packages-microsoft-prod.deb"
    echo "  sudo apt-get update"
    echo "  sudo apt-get install -y dotnet-sdk-8.0"
    echo ""
    echo "CentOS/RHEL/Fedora:"
    echo "  sudo dnf install dotnet-sdk-8.0"
    echo ""
    echo "手动安装:"
    echo "  1. 访问: https://dotnet.microsoft.com/download/dotnet/8.0"
    echo "  2. 下载适合您系统的SDK"
    echo "  3. 按照安装说明进行安装"
    echo ""
    echo "验证安装:"
    echo "  dotnet --version"
    echo ""
}

# 显示数据库配置帮助
show_database_config_help() {
    echo ""
    log_info "数据库配置帮助:"
    echo ""
    echo "1. 使用Docker启动SQL Server:"
    echo "   docker run -e \"ACCEPT_EULA=Y\" -e \"SA_PASSWORD=YourStrong@Passw0rd\" \\"
    echo "     -p 1433:1433 --name sqlserver -d mcr.microsoft.com/mssql/server:2022-latest"
    echo ""
    echo "2. 编辑配置文件 src/DtDrvApp.Api/appsettings.json:"
    echo "   \"ConnectionStrings\": {"
    echo "     \"DefaultConnection\": \"Server=localhost;Database=DtDrvApp;User Id=sa;Password=YourStrong@Passw0rd;TrustServerCertificate=true;\""
    echo "   }"
    echo ""
    echo "3. 初始化数据库:"
    echo "   运行 scripts/init-database.sql 脚本"
    echo ""
}

echo ""
log_info "脚本执行完成"
