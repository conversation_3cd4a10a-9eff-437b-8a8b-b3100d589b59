#!/bin/bash

# DtDrvApp.Core 简化启动脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}=================================================="
echo "    DtDrvApp.Core 简化启动脚本"
echo "==================================================${NC}"
echo ""

# 检查是否在正确目录
if [ ! -f "DtDrvApp.Core.sln" ]; then
    echo -e "${RED}[ERROR]${NC} 请在DtDrvApp.Core项目根目录下运行此脚本"
    exit 1
fi

echo -e "${BLUE}[INFO]${NC} 当前目录: $(pwd)"
echo ""

# 提供启动选项
echo -e "${BLUE}[INFO]${NC} 选择启动方式:"
echo "1) Docker启动 (推荐 - 无需配置.NET)"
echo "2) 手动配置.NET后启动"
echo "3) 显示.NET安装指南"
echo ""

read -p "请输入选择 (1-3): " choice

case $choice in
    1)
        echo -e "${BLUE}[INFO]${NC} 使用Docker启动..."
        
        # 检查Docker
        if ! command -v docker &> /dev/null; then
            echo -e "${RED}[ERROR]${NC} Docker未安装！"
            echo "请安装Docker: https://docs.docker.com/get-docker/"
            exit 1
        fi
        
        if ! command -v docker-compose &> /dev/null; then
            echo -e "${RED}[ERROR]${NC} docker-compose未安装！"
            exit 1
        fi
        
        echo -e "${BLUE}[INFO]${NC} 停止旧容器..."
        docker-compose down 2>/dev/null || true
        
        echo -e "${BLUE}[INFO]${NC} 构建并启动服务..."
        docker-compose up --build -d
        
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}[SUCCESS]${NC} 服务启动成功！"
            echo ""
            echo "访问地址:"
            echo "  - HTTP: http://localhost:5000"
            echo "  - HTTPS: https://localhost:5001"
            echo "  - TCP服务: localhost:23456"
            echo "  - 健康检查: http://localhost:5000/health"
            echo ""
            echo "管理命令:"
            echo "  - 查看日志: docker-compose logs -f"
            echo "  - 停止服务: docker-compose down"
            
            # 等待服务启动并测试
            echo -e "${BLUE}[INFO]${NC} 等待服务启动..."
            sleep 10
            
            if command -v curl &> /dev/null; then
                echo -e "${BLUE}[INFO]${NC} 测试服务状态..."
                if curl -f http://localhost:5000/health >/dev/null 2>&1; then
                    echo -e "${GREEN}[SUCCESS]${NC} 服务运行正常！"
                else
                    echo -e "${YELLOW}[WARNING]${NC} 服务可能还在启动中，请稍后访问"
                fi
            fi
        else
            echo -e "${RED}[ERROR]${NC} 服务启动失败"
            exit 1
        fi
        ;;
        
    2)
        echo -e "${BLUE}[INFO]${NC} 尝试手动启动..."
        
        # 尝试找到dotnet
        DOTNET_CMD=""
        for cmd in "dotnet" "/usr/bin/dotnet" "/usr/local/bin/dotnet" "/home/<USER>/dotnet"; do
            if command -v "$cmd" &> /dev/null && $cmd --version &> /dev/null; then
                DOTNET_CMD="$cmd"
                break
            fi
        done
        
        if [ -z "$DOTNET_CMD" ]; then
            echo -e "${RED}[ERROR]${NC} 未找到可用的.NET SDK"
            echo "请选择选项3查看安装指南"
            exit 1
        fi
        
        echo -e "${GREEN}[SUCCESS]${NC} 找到.NET SDK: $DOTNET_CMD"
        VERSION=$($DOTNET_CMD --version)
        echo -e "${BLUE}[INFO]${NC} 版本: $VERSION"
        
        echo -e "${BLUE}[INFO]${NC} 还原依赖..."
        $DOTNET_CMD restore
        
        echo -e "${BLUE}[INFO]${NC} 构建项目..."
        $DOTNET_CMD build
        
        echo -e "${YELLOW}[WARNING]${NC} 请确保已配置数据库连接"
        echo "配置文件: src/DtDrvApp.Api/appsettings.json"
        echo ""
        
        read -p "是否继续启动? (y/n): " continue_start
        if [ "$continue_start" = "y" ] || [ "$continue_start" = "Y" ]; then
            echo -e "${BLUE}[INFO]${NC} 启动应用..."
            cd src/DtDrvApp.Api
            $DOTNET_CMD run
        fi
        ;;
        
    3)
        echo ""
        echo -e "${BLUE}[INFO]${NC} .NET 8.0 SDK 安装指南:"
        echo ""
        echo "Ubuntu/Debian:"
        echo "  wget https://packages.microsoft.com/config/ubuntu/22.04/packages-microsoft-prod.deb"
        echo "  sudo dpkg -i packages-microsoft-prod.deb"
        echo "  sudo apt-get update"
        echo "  sudo apt-get install -y dotnet-sdk-8.0"
        echo ""
        echo "CentOS/RHEL:"
        echo "  sudo dnf install dotnet-sdk-8.0"
        echo ""
        echo "手动安装:"
        echo "  1. 访问: https://dotnet.microsoft.com/download/dotnet/8.0"
        echo "  2. 下载Linux x64版本"
        echo "  3. 解压到 /usr/local/dotnet"
        echo "  4. 添加到PATH: export PATH=\$PATH:/usr/local/dotnet"
        echo ""
        echo "验证安装:"
        echo "  dotnet --version"
        ;;
        
    *)
        echo -e "${RED}[ERROR]${NC} 无效选择"
        exit 1
        ;;
esac

echo ""
echo -e "${BLUE}[INFO]${NC} 脚本执行完成"
