// ColumnDef.cpp: implementation of the CColumnDef class.
//
//////////////////////////////////////////////////////////////////////

#include "./ColumnDef.h"
#include "./DataBaseDeal.h"
// #include "./SearchDIY.h"
#include "./DataBaseInfo.h"
#include "./DtdrvApp_Public_Var.h"
#include "ParaResult.h"


//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////


/************************************************************************/
/*CColumnItem                                                     
************************************************************************/
//talbecolumn
CColumnItem::CColumnItem(Stru_CommonPara stru_CP, int maxGroup,
			int dataType, std::string abnormalData, int fixImage, 
			int subStart,int subEnd)
{
    InitFunc();

	this->imageID = stru_CP.imageID;
	this->paraID = stru_CP.paraID;
	this->tbID = stru_CP.tbID;
	this->tbName = CStdCommond::StringToUpOrLow(stru_CP.tbName, 1);
	this->maxGroup = maxGroup;

	this->dataType = dataType;
	this->abnormalData = abnormalData;
	this->dbColumnName = CStdCommond::StringToUpOrLow(stru_CP.dbColumnName, 1);
	this->FixImage = fixImage;
	this->subStart = subStart;

	this->subEnd = subEnd;
	this->clientColumnName = stru_CP.clientColumnName;

	this->imageDataType = "";
	this->imageDataTypeID = "";
	this->imageDataColumnID = "";

	this->imageDataColumnDealType = -1;
	this->imageDataColumnDealDisc = "";

	this->columnType = CColumnItem::TableColumnType;

	bColumnProerty = 0x00;
	if (this->imageID > 0)
	{
		bColumnProerty |= CColumnItem::ColumnProperty_IsImageType;
	}

	if (this->FixImage == 1)
	{
		bColumnProerty |= CColumnItem::ColumnProperty_IsFixType;
	}

	//初始化异常值
	dataBufLenth = 0;
	PPM_OS::memset(abnormalDataBuffer, 0, sizeof(abnormalDataBuffer));
    (this->*pfunc_CsData[dataType])();
}	
void CColumnItem::InitFunc(void)
{
	for (int i = 0; i < 256; i++)
	{
		pfunc_CsData[i] = &CColumnItem::DealCsDefaultValue;
	}

	pfunc_CsData[TYPE_CS_TINYINT] = &CColumnItem::DealCsTinyIntValue;

	pfunc_CsData[TYPE_CS_SMALLINT] = &CColumnItem::DealCsSmallIntValue;

	pfunc_CsData[TYPE_CS_USMALLINT] = &CColumnItem::DealCsUSmallIntValue;

	pfunc_CsData[TYPE_CS_INT_FLOAT] = &CColumnItem::DealCsIntFloatValue;

	pfunc_CsData[TYPE_CS_INT] = &CColumnItem::DealCsIntValue;

	pfunc_CsData[TYPE_CS_UINT64] = &CColumnItem::DealCsInt64Value;
	pfunc_CsData[TYPE_CS_INT64] = &CColumnItem::DealCsInt64Value;

	pfunc_CsData[TYPE_CS_TEXT] = &CColumnItem::DealCsTextValue;
	pfunc_CsData[TYPE_CS_VARYBIN_MERGE] = &CColumnItem::DealCsTextValue;
	pfunc_CsData[TYPE_CS_TEXT_MERGE] = &CColumnItem::DealCsTextValue;
	pfunc_CsData[TYPE_CS_VARTEXT] = &CColumnItem::DealCsTextValue;

}

void CColumnItem::DealCsDefaultValue(void)
{

}
void CColumnItem::DealCsTinyIntValue(void)
{
	abnormalDataBuffer[0]  = CStdCommond::strtotinyint(abnormalData);
	dataBufLenth = 1;
}
void CColumnItem::DealCsSmallIntValue(void)
{
	short temp = CStdCommond::strtosmallint(abnormalData);
	PPM_OS::memcpy(abnormalDataBuffer, &temp, 2);
	dataBufLenth = 2;
}
void CColumnItem::DealCsUSmallIntValue(void)
{
	WORD temp = CStdCommond::strtosmallint(abnormalData);
	PPM_OS::memcpy(abnormalDataBuffer, &temp, 2);
	dataBufLenth = 2;
}
void CColumnItem::DealCsTextValue(void)
{
	this->abnormalData = "NullText";

	PPM_OS::memcpy(abnormalDataBuffer, abnormalData.c_str(), 
		abnormalData.length() < sizeof(abnormalDataBuffer)?abnormalData.length():sizeof(abnormalDataBuffer));
	dataBufLenth = abnormalData.length() < sizeof(abnormalDataBuffer)?abnormalData.length():sizeof(abnormalDataBuffer);
}
void CColumnItem::DealCsIntFloatValue(void)
{
	int temp = atoi(abnormalData.c_str());
	PPM_OS::memcpy(abnormalDataBuffer, &temp, 4);
	dataBufLenth = 4;
}
void CColumnItem::DealCsIntValue(void)
{
	int temp = atoi(abnormalData.c_str());
	PPM_OS::memcpy(abnormalDataBuffer, &temp, 4);
	dataBufLenth = 4;
}
void CColumnItem::DealCsInt64Value(void)
{
	INT64 temp = atoll(abnormalData.c_str());
	PPM_OS::memcpy(abnormalDataBuffer, &temp, 8);
	dataBufLenth = 8;
}

//imagecolumn
CColumnItem::CColumnItem(Stru_CommonPara stru_CP, std::string servicetype, std::string imageDataType, std::string imageDataTypeID, 
						 std::string imageDataColumnID,  int imageDataColumnDealType, std::string imageDataColumnDealDisc)
{
	this->imageID = stru_CP.imageID;
	this->paraID = stru_CP.paraID;
	this->tbID = stru_CP.tbID;
	this->tbName = CStdCommond::StringToUpOrLow(stru_CP.tbName, 1);
	this->maxGroup = 0;

	this->servicetype = servicetype;
	this->dataType = 10;
	this->abnormalData = "00000000";
	this->dbColumnName = CStdCommond::StringToUpOrLow(stru_CP.dbColumnName, 1);
	this->FixImage = CColumnItem::NoImageFixType;
	this->subStart = -1;
	this->subEnd = -1;
	this->clientColumnName = stru_CP.clientColumnName;
	this->imageDataType = imageDataType;
	this->imageDataTypeID = imageDataTypeID;
	this->imageDataColumnID = imageDataColumnID;

	this->imageDataColumnDealType = imageDataColumnDealType;

	if (imageDataColumnDealDisc.find("NULL") != std::string::npos)
	{
		this->imageDataColumnDealDisc = "";
	}
	else 
	{
		this->imageDataColumnDealDisc = imageDataColumnDealDisc;
	}
	this->columnType = CColumnItem::StatImageType;

	bColumnProerty = 0x00 | CColumnItem::ColumnProperty_IsStatImageType | CColumnItem::ColumnProperty_IsImageType;

}

CColumnItem* 
CColumnItem::GetTableColumnItem(const std::string strData)
{
	std::vector<std::string> vec_data;
	vec_data.clear();
	if (!GetSliptColumnItem(strData, vec_data))
	{
		return NULL;
	}

	Stru_CommonPara stru_CP;
	stru_CP.imageID = atoi((vec_data[0]).c_str());
	stru_CP.paraID = atoi((vec_data[1]).c_str());
	stru_CP.tbID = atoi((vec_data[2]).c_str());
	stru_CP.tbName = vec_data[3];
	stru_CP.dbColumnName = vec_data[7];
	stru_CP.clientColumnName = vec_data[11];

	CColumnItem* p_CColumnItem = NULL;
	p_CColumnItem = new CColumnItem(stru_CP, atoi((vec_data[4]).c_str()), atoi((vec_data[5]).c_str()), 
		vec_data[6], atoi((vec_data[8]).c_str()), atoi((vec_data[9]).c_str()), atoi((vec_data[10]).c_str()));


	p_CColumnItem->isDBColumnExist = FALSE;

	return p_CColumnItem;
}

bool CColumnItem::GetSliptColumnItem(const std::string strData, std::vector<std::string>& vec_data)
{
	if (strData.length() <= 0)
	{
		return false;
	}

	vec_data = CStdCommond::str_slipt(strData, "\t");
	if (vec_data.size() < 12)
	{
		return false;
	}

	for (std::vector<std::string>::size_type i = 0; i < vec_data.size(); i++)
	{
		vec_data[i] = CStdCommond::str_trim(vec_data[i], ' ');
	}
   
	return true;
}

CColumnItem* 
CColumnItem::GetTableColumnItem(std::vector<std::string> vec_data)
{
	CColumnItem* p_CColumnItem = NULL;
	if (0 == vec_data.size())
	{
		return p_CColumnItem;
	}

	Stru_CommonPara stru_CP;
    stru_CP.imageID = atoi((vec_data[0]).c_str());
    stru_CP.paraID = atoi((vec_data[1]).c_str());
	stru_CP.tbID = atoi((vec_data[2]).c_str());
	stru_CP.tbName = vec_data[3];
	stru_CP.dbColumnName = vec_data[7];
	stru_CP.clientColumnName = vec_data[11];

	p_CColumnItem = new CColumnItem(stru_CP, atoi((vec_data[4]).c_str()), atoi((vec_data[5]).c_str()), 
		vec_data[6], atoi((vec_data[8]).c_str()), atoi((vec_data[9]).c_str()), atoi((vec_data[10]).c_str()));

	p_CColumnItem->isDBColumnExist = FALSE;

	return p_CColumnItem;
}

CColumnItem* 
CColumnItem::GetImageColumnItem(const std::string strData)
{
	std::vector<std::string> vec_data;
	vec_data.clear();
	if (!GetSliptColumnItem(strData, vec_data))
	{
		return NULL;
	}

	CColumnItem* p_CColumnItem = NULL;

	Stru_CommonPara stru_CP;
	stru_CP.imageID = atoi((vec_data[0]).c_str());
	stru_CP.paraID = atoi((vec_data[1]).c_str());
	stru_CP.tbID = atoi((vec_data[2]).c_str());
	stru_CP.tbName = vec_data[4];
	stru_CP.dbColumnName = vec_data[6];
	stru_CP.clientColumnName = vec_data[9];

	p_CColumnItem = new CColumnItem(stru_CP, vec_data[3], vec_data[5], vec_data[7], 
		vec_data[8], atoi((vec_data[10]).c_str()), vec_data[11]);

	p_CColumnItem->isDBColumnExist = FALSE;

	return p_CColumnItem;
}

CColumnItem* 
CColumnItem::GetImageColumnItem(std::vector<std::string> vec_data)
{
	CColumnItem* p_CColumnItem = NULL;
	if (0 == vec_data.size())
	{
		return p_CColumnItem;
	}

	Stru_CommonPara stru_CP;
    BuildCommonPara(stru_CP, vec_data);
	p_CColumnItem = new CColumnItem(stru_CP, vec_data[3], vec_data[5], vec_data[7], 
		vec_data[8], atoi((vec_data[10]).c_str()), vec_data[11]);

	p_CColumnItem->isDBColumnExist = FALSE;

	return p_CColumnItem;
}

void CColumnItem::BuildCommonPara(Stru_CommonPara& stru_CP, std::vector<std::string> vec_data)
{
	stru_CP.imageID = atoi((vec_data[0]).c_str());
	stru_CP.paraID = atoi((vec_data[1]).c_str());
	stru_CP.tbID = atoi((vec_data[2]).c_str());
	stru_CP.tbName = vec_data[4];
	stru_CP.dbColumnName = vec_data[6];
	stru_CP.clientColumnName = vec_data[9];
}

/************************************************************************/
/*CColumnDef                                                     
************************************************************************/
//用于构建查询表结构，不在内的表格将无法通过自定义接口查询数据。
CColumnDef::CColumnDef()
{
}

CColumnDef *
CColumnDef::Instance (void)
{
	static CColumnDef *m_pColumnDef = NULL;
	
	if (m_pColumnDef == 0)
    {
		m_pColumnDef = new CColumnDef;
		if(NULL == m_pColumnDef){
			return NULL;
		}
    }
	return m_pColumnDef;
}

CColumnDef::~CColumnDef()
{
	for(std::map<STRU_COLUMNID, std::map<std::string, CColumnItem*>>::iterator p_mapColumnItem = m_pMapClomnsStatItem.begin();
		p_mapColumnItem != m_pMapClomnsStatItem.end(); ++p_mapColumnItem)
	{
		for (std::map<std::string, CColumnItem*>::iterator p_CColumnItem = m_pMapClomnsStatItem[p_mapColumnItem->first].begin();
			p_CColumnItem != m_pMapClomnsStatItem[p_mapColumnItem->first].end(); ++p_CColumnItem)
		{
			delete(p_CColumnItem->second);
			p_CColumnItem->second = NULL;
		}
	}
	m_pMapClomnsStatItem.clear();
}



void
CColumnDef::PushItemIntoMap(CColumnItem* item)
{
   if (-1 == item->paraID)
   {
	   //对于无效的Item清除
	   delete item;
	   item = NULL;
	   return;
   }

   STRU_COLUMNID  stru_columnid;
   stru_columnid.imageID  = item->imageID;
   stru_columnid.paraID = item->paraID;
   stru_columnid.tbID = item->tbID;

   m_pMapClomns[stru_columnid] = item;

   m_pMapDBTables[item->tbID][stru_columnid] = item;

   m_pMapDBTablesColumnName[item->tbID][item->dbColumnName] = item;

#ifdef _DEBUG

   if (m_pMapClomnsStatItem.find(stru_columnid) != m_pMapClomnsStatItem.end() && item->imageDataColumnID == "" )
   {
	   PPM_DEBUG((LM_ERROR, "存在键值重复： %d, %d, %d \n", stru_columnid.imageID, stru_columnid.paraID, stru_columnid.tbID));
	   PPM_APPLOG((LM_ERROR, "存在键值重复： %d, %d, %d \n", stru_columnid.imageID, stru_columnid.paraID, stru_columnid.tbID));
   }

   else if (m_pMapClomnsStatItem.find(stru_columnid) != m_pMapClomnsStatItem.end() 
	   && m_pMapClomnsStatItem[stru_columnid].find(item->imageDataColumnID) != m_pMapClomnsStatItem[stru_columnid].end())
   {
	   PPM_DEBUG((LM_ERROR, "存在键值重复： %d, %d, %d, %s \n", stru_columnid.imageID, stru_columnid.paraID,
		   stru_columnid.tbID, item->imageDataColumnID.c_str()));
	   PPM_APPLOG((LM_ERROR, "存在键值重复： %d, %d, %d, %s \n", stru_columnid.imageID, stru_columnid.paraID, 
		   stru_columnid.tbID, item->imageDataColumnID.c_str()));
   }

#endif

    m_pMapClomnsStatItem[stru_columnid][item->imageDataColumnID] = item;
   
}

std::vector<STRU_COLUMNID> 
CColumnDef::GetColumnIDbyIDStr(int tableid, std::string columnNames)
{
	std::vector<STRU_COLUMNID> vec_STRU_COLUMNID;
	std::vector<std::string> vec_columnName = CStdCommond::str_slipt(columnNames, ",");

	if (vec_columnName.size()%3 != 0)
	{
		return vec_STRU_COLUMNID;
	}

    for (std::vector<std::string>::iterator p_vecColumnName = vec_columnName.begin(); p_vecColumnName != vec_columnName.end(); ++p_vecColumnName)
	{
        if (m_pMapDBTablesColumnName[tableid].find(*p_vecColumnName) != m_pMapDBTablesColumnName[tableid].end())
        {
           STRU_COLUMNID temp;
		   temp.imageID = m_pMapDBTablesColumnName[tableid][*p_vecColumnName]->imageID;
		   temp.paraID = m_pMapDBTablesColumnName[tableid][*p_vecColumnName]->paraID;
		   temp.tbID = m_pMapDBTablesColumnName[tableid][*p_vecColumnName]->tbID;
           vec_STRU_COLUMNID.push_back(temp);
        }
	}

	return vec_STRU_COLUMNID;
}

std::vector<STRU_COLUMNID> 
CColumnDef::ConvertIDStrToColumnID(std::string columnNames)
{
	std::vector<STRU_COLUMNID> vec_STRU_COLUMNID;
	std::vector<std::string> vec_columnName = CStdCommond::str_slipt(columnNames, ",");

	if (vec_columnName.size()%3 != 0)
	{
		return vec_STRU_COLUMNID;
	}

	std::vector<STRU_COLUMNID> vec_STRU_COLUMNID_Temp;
	for (std::vector<STRU_COLUMNID>::size_type i=0; i<vec_columnName.size(); ++i)
    {
		STRU_COLUMNID temp;
		temp.imageID = atoi(vec_columnName[i].c_str());
		temp.paraID = atoi(vec_columnName[++i].c_str());
		temp.tbID = atoi(vec_columnName[++i].c_str());   
        vec_STRU_COLUMNID_Temp.push_back(temp);
    }

	for (std::vector<STRU_COLUMNID>::iterator p_STRU_COLUMNID = vec_STRU_COLUMNID_Temp.begin();
		p_STRU_COLUMNID != vec_STRU_COLUMNID_Temp.end(); ++p_STRU_COLUMNID)
	{
		if (CColumnDef::Instance()->m_pMapClomns.find(*p_STRU_COLUMNID) != CColumnDef::Instance()->m_pMapClomns.end())
		{
			vec_STRU_COLUMNID.push_back(*p_STRU_COLUMNID);
		}
	}

	return vec_STRU_COLUMNID;
}

BOOL
CColumnDef::MarkColumnExists(STRU_COLUMNID columnid)
{
   if (m_pMapClomnsStatItem.find(columnid) != m_pMapClomnsStatItem.end())
   {
	   for (std::map<std::string, CColumnItem*>::iterator p_MM = m_pMapClomnsStatItem[columnid].begin(); 
		   p_MM != m_pMapClomnsStatItem[columnid].end(); ++p_MM)
	   {
           p_MM->second->isDBColumnExist = TRUE;
	   }
   }
   return TRUE;
}

BOOL
CColumnDef::MarkColumnNoExists(STRU_COLUMNID columnid)
{
	if (m_pMapClomnsStatItem.find(columnid) != m_pMapClomnsStatItem.end())
	{
		for (std::map<std::string, CColumnItem*>::iterator p_MM = m_pMapClomnsStatItem[columnid].begin();
			p_MM != m_pMapClomnsStatItem[columnid].end(); ++p_MM)
		{
			p_MM->second->isDBColumnExist = FALSE;
		}
	}
	return TRUE;
}

std::map<STRU_COLUMNID, CColumnItem*>
CColumnDef::GetTableColumn(const int tableID)
{
    std::map<STRU_COLUMNID, CColumnItem*> columnItems;

	if (CColumnDef::Instance()->m_pMapDBTables.find(tableID) == CColumnDef::Instance()->m_pMapDBTables.end())
	{
		
	}
	else 
	{
       columnItems = m_pMapDBTables[tableID];
	}
    return columnItems;
}

BOOL
CColumnDef::IsColumnExist(const STRU_COLUMNID columnID)
{
	if (m_pMapClomns.find(columnID) != m_pMapClomns.end()
		&& m_pMapClomns[columnID]->isDBColumnExist == TRUE)
	{
		return TRUE;
	}
	else 
	{
		return FALSE;
	}
	
}

BOOL
CColumnDef::IsTableExist(const int tableid)
{ 
	return m_pMapDBTables.find(tableid) != m_pMapDBTables.end()?TRUE:FALSE;
}

CColumnItem*
CColumnDef::GetColumnItem(const STRU_COLUMNID columnID)
{
	if (m_pMapClomns.find(columnID) == m_pMapClomns.end())
	{
		return NULL;
	}
	return m_pMapClomns[columnID];
}


BOOL
CColumnDef::IsColumnDefCanUsed(void)
{
	//检查配置的可用性，不可用就退出程序
	if (0 == CColumnDef::Instance()->m_pMapDBTables.size())
	{
		return FALSE;
	}

    BOOL checkColumnRight = TRUE;


#ifdef _DEBUG
	for (map<STRU_COLUMNID, CColumnItem*>::iterator p_mapCColumnItem = m_pMapClomns.begin(); 
		p_mapCColumnItem != m_pMapClomns.end(); ++p_mapCColumnItem)
    {
		CColumnItem* p_CColumnItem = p_mapCColumnItem->second;

		//用于预留的空结构，不判断
		if (p_CColumnItem->paraID == -1)
		{
			continue;
		}

		//暂不用检查stat image
        if ((p_CColumnItem->bColumnProerty & CColumnItem::ColumnProperty_IsStatImageType) == CColumnItem::ColumnProperty_IsStatImageType)
		{
			continue;
		}

		//检查数据类型
        if (p_CColumnItem->dataType <= 0)
        {
			string error_str = "TableColmn配置存在错误，问题：数据类型错误，请检查：ID为 " +
				CStdCommond::itostr(p_CColumnItem->imageID) + "," + CStdCommond::itostr(p_CColumnItem->paraID) +"," +
				CStdCommond::itostr(p_CColumnItem->tbID) + "\n";
			PPM_APPLOG((LM_ERROR, error_str.c_str()));
			checkColumnRight = FALSE;	   
        }

		//检查异常值
		if (p_CColumnItem->abnormalData == "")
		{
			string error_str = "TableColmn配置存在错误，问题：数据异常值错误，请检查：ID为 " +
				CStdCommond::itostr(p_CColumnItem->imageID) + "," + CStdCommond::itostr(p_CColumnItem->paraID) + "," +
				CStdCommond::itostr(p_CColumnItem->tbID) + "\n";
			PPM_APPLOG((LM_ERROR, error_str.c_str()));
			checkColumnRight = FALSE;	  
		}

        if (p_CColumnItem->dbColumnName == "")
        {
			string error_str = "TableColmn配置存在错误，问题：ColumnName没赋值，请检查：ID为 " + 
				CStdCommond::itostr(p_CColumnItem->imageID) + "," + CStdCommond::itostr(p_CColumnItem->paraID) + "," + 
				CStdCommond::itostr(p_CColumnItem->tbID) + "\n";
			PPM_APPLOG((LM_ERROR, error_str.c_str()));
			checkColumnRight = FALSE;
        }


		/*
		判断image的切割是否正确
		*/
        if ((p_CColumnItem->bColumnProerty & (CColumnItem::ColumnProperty_IsImageType|CColumnItem::ColumnProperty_IsStatImageType)) 
			== (CColumnItem::ColumnProperty_IsImageType))
       {
		   //确定是tablecolumn的image格式
		   int len = p_CColumnItem->subEnd - p_CColumnItem->subStart + 1;
           if (len < 0)
           {
			   string error_str = "TableColmn配置存在错误，问题：切取位置错误，请检查：ID为 " + 
				   CStdCommond::itostr(p_CColumnItem->imageID) + "," + CStdCommond::itostr(p_CColumnItem->paraID) + "," +
				   CStdCommond::itostr(p_CColumnItem->tbID) + "\n";
			   PPM_APPLOG((LM_ERROR, error_str.c_str()));
			   checkColumnRight = FALSE; 
		   }

		   switch(p_CColumnItem->dataType)
		   {
		   case TYPE_CS_TINYINT:
			   {
				   if (1 != len)
				   {
					   string error_str = "TableColmn配置存在错误，问题：切取位置错误，请检查：ID为 " + 
                           CStdCommond::itostr(p_CColumnItem->imageID) + "," + CStdCommond::itostr(p_CColumnItem->paraID) + "," +
                           CStdCommond::itostr(p_CColumnItem->tbID) + "\n";
                       PPM_APPLOG((LM_ERROR, error_str.c_str()));
					   checkColumnRight = FALSE;	 
				   }
			   }
			   break;
		   case TYPE_CS_SMALLINT:
		   case TYPE_CS_USMALLINT:
			   {
				   if (2 != len)
				   {
					   string error_str = "TableColmn配置存在错误，问题：切取位置错误，请检查：ID为 " + 
                           CStdCommond::itostr(p_CColumnItem->imageID) + "," + CStdCommond::itostr(p_CColumnItem->paraID) + "," + 
                           CStdCommond::itostr(p_CColumnItem->tbID) + "\n";
					   PPM_APPLOG((LM_ERROR, error_str.c_str()));
					   checkColumnRight = FALSE;	 
				   }
			   }
			   break;
		   case TYPE_CS_FLOAT:
		   case TYPE_CS_INT_FLOAT:
		   case TYPE_CS_INT:
			   {
				   if (4 != len)
				   {
					   string error_str = "TableColmn配置存在错误，问题：切取位置错误，请检查：ID为 " +
                           CStdCommond::itostr(p_CColumnItem->imageID) + "," + CStdCommond::itostr(p_CColumnItem->paraID) + "," +
                           CStdCommond::itostr(p_CColumnItem->tbID) + "\n";
					   PPM_APPLOG((LM_ERROR, error_str.c_str()));
					   checkColumnRight = FALSE; 
				   }
			   }
			   break;
		   case TYPE_CS_TEXT:
		   case TYPE_CS_VARYBIN:
		   case TYPE_CS_VARYBIN_MERGE:
		   case TYPE_CS_TEXT_MERGE:
		   case TYPE_CS_VARTEXT:
			   {

			   }
			   break;

		   case TYPE_CS_UINT64:
		   case TYPE_CS_INT64:
			   {
				   if (8 != len)
				   {
					   string error_str = "TableColmn配置存在错误，问题：切取位置错误，请检查：ID为 " +
                           CStdCommond::itostr(p_CColumnItem->imageID) + "," + CStdCommond::itostr(p_CColumnItem->paraID) + "," +
                           CStdCommond::itostr(p_CColumnItem->tbID) + "\n";
					   PPM_APPLOG((LM_ERROR, error_str.c_str()));
					   checkColumnRight = FALSE; 
				   }
			   }
			   break;

		   default:
			   {
				   string error_str = "TableColmn配置存在错误，问题：无效类型，请检查：ID为 " + 
                       CStdCommond::itostr(p_CColumnItem->imageID) + "," + CStdCommond::itostr(p_CColumnItem->paraID) + "," +
                       CStdCommond::itostr(p_CColumnItem->tbID) + "\n";
				   PPM_APPLOG((LM_ERROR, error_str.c_str()));
				   checkColumnRight = FALSE;
			   }
		     }
         }
    }

#endif

	return checkColumnRight;
}

std::map<STRU_COLUMNID, std::map<std::string, CColumnItem*>>&
CColumnDef::GetClomnsStatItem(void)
{
	return m_pMapClomnsStatItem;
}

CColumnItem*
CColumnDef::GetColumn(const int tableid, const std::string columnName)
{
   if (m_pMapDBTablesColumnName.find(tableid) != m_pMapDBTablesColumnName.end())
   {
	   if (m_pMapDBTablesColumnName[tableid].find(columnName) == m_pMapDBTablesColumnName[tableid].end())
	   {
          return NULL;
	   }

	   return m_pMapDBTablesColumnName[tableid][columnName];
   }

   return NULL;
}









