// ColumnDef.h: interface for the CColumnDef class.
//
//////////////////////////////////////////////////////////////////////

#ifndef __COLUMNDEF_H__
#define __COLUMNDEF_H__

#include "./StdHeader.h"
#include "./DataItem.h"
#include "./StdCommond.h"


#define TABLECOLUMNID_NUM = 3


struct STRU_COLUMNID
{
public:
	int imageID;
	int paraID;
	int tbID;

	bool operator < (STRU_COLUMNID const& _A) const
	{
		//这个函数指定排序策略
		if(tbID < _A.tbID) 
		{
			return true;
		}
		else if(tbID == _A.tbID) 
		{
			if (paraID < _A.paraID)
			{
				return true;
			}
			else if (paraID == _A.paraID)
			{
				if(imageID < _A.imageID)
				{
					return true;
				}
				return false;
			}
			return false;
		}
		return false;
	}
};

struct Stru_CommonPara
{
	int imageID;
	int paraID;
	int tbID;
	std::string tbName;
	std::string dbColumnName;
	std::string clientColumnName;
};

class CColumnItem
{
public:
	std::string clientColumnName;

	int imageID;
	int paraID;
	int tbID;
	std::string tbName;
	int maxGroup;//
	int dataType;
	std::string abnormalData;
	std::string dbColumnName;
	int FixImage;//
	int subStart;//
	int subEnd;//

	std::string servicetype;
  std::string imageDataType;
	std::string imageDataTypeID;
	std::string imageDataColumnID;

	//标识字段是否存在于数据库
  BOOL isDBColumnExist;

	//服务端数据绑定结构
	/**
    *重大问题修复，由于多线程作用，数据集类不应该具有对具体数据操作的功能，
	*此处不应该添加数据绑定的指针
	*在多线程内处理，会产生野指针的作用
	*此数据绑定应该放到ColumnResult控制
	*/

	//image 统计单元操作模式
	int imageDataColumnDealType;
	std::string imageDataColumnDealDisc;

	//Column类型
  int columnType;

	//column属性
	// ()()()()()(column类型：0非统计image类型，1统计image类型)(是否固定)(是否是image)
	BYTE bColumnProerty;

	//异常值内存
	BYTE abnormalDataBuffer[10];

	int dataBufLenth;

public:
	enum FixType
	{
		NoImageFixType = 0,
		ImageFixType = 1
	};

	enum ColumnType
	{
		TableColumnType = 1,
		StatImageType = 2
	};

	enum ColumnProperty
	{
		ColumnProperty_IsImageType = 0x01,
		ColumnProperty_IsFixType = 0x02,
		ColumnProperty_IsStatImageType = 0x04
        
	};

public: 

	//talbecolumn
	CColumnItem(Stru_CommonPara stru_CP, int maxGroup, 
		        int dataType, std::string abnormalData, int fixImage, 
				int subStart, int subEnd);

	//imagecolumn
	CColumnItem(Stru_CommonPara stru_CP, std::string servicetype, std::string imageDataType, std::string imageDataTypeID, 
		std::string imageDataColumnID, int imageDataColumnDealType, std::string imageDataColumnDealDisc);


  BOOL IsColumnImage()
	{
      if ((bColumnProerty & ColumnProperty_IsImageType) == ColumnProperty_IsImageType)
		 	{
			 	return TRUE;
		 	}
		 	return FALSE;
	}

	BOOL IsColumnFix()
	{
		if ((bColumnProerty & ColumnProperty_IsFixType) == ColumnProperty_IsFixType)
		{
			return TRUE;
		}
		return FALSE;
	}

	virtual ~CColumnItem()
	{

	}; 

public:
	static CColumnItem* GetTableColumnItem(const std::string strData);
	static CColumnItem* GetTableColumnItem(std::vector<std::string> vec_data);
	static CColumnItem* GetImageColumnItem(const std::string strData);
	static CColumnItem* GetImageColumnItem(std::vector<std::string> vec_data);

public:
	 STRU_COLUMNID GetTableColumnID(void)
	 {
		 STRU_COLUMNID columnid;
		 columnid.imageID = imageID;
		 columnid.paraID = paraID;
		 columnid.tbID = tbID;

		 return columnid;
	 }

private:
	void (CColumnItem::*pfunc_CsData[256])(void);
  void DealCsDefaultValue(void);
  void DealCsTinyIntValue(void);
	void DealCsSmallIntValue(void);
	void DealCsUSmallIntValue(void);
	void DealCsTextValue(void);
	void DealCsIntFloatValue(void);
	void DealCsIntValue(void);
	void DealCsInt64Value(void);
	void InitFunc(void);

	static bool GetSliptColumnItem(const std::string strData, std::vector<std::string>& vec_data);
	static void BuildCommonPara(Stru_CommonPara& stru_CP, std::vector<std::string> vec_data);
};

class CColumnDef  
{
public:
	enum DataBaseTable
	{
		/************************************************************************/
		/* normal table ID                                                      */
		/************************************************************************/
    tb_log_file = 1,
		tb_log_file_time = 2,
		tb_scan_w_sample = 3,
    tb_scan_td_sample = 4,
		tb_gsmctr_sample = 5,
		tb_tdctr_sample = 6,
		tb_ulgsm_sample = 7,
		tb_scan_sample = 8,
		tb_cqtwcdma_sample = 9,
		tb_cqtwcdma_sample5 = 10,
		tb_dtcdma_sample5 = 11,
		tb_dtcdma_sample = 12,
		tb_cqtcdma_sample = 13,
		tb_cqtcdma_sample5 = 14,
		tb_cqtevdo_sample5 = 15,
		tb_dtevdo_sample5 = 16,
		tb_dtevdo_sample = 17,
		tb_cqtevdo_sample = 18,
    tb_dtgsm_sample5 = 19,
		tb_cqtgsm_sample5 = 20,
		tb_autodtgsm_sample5 = 21,
		tb_autocqtgsm_sample5 = 22,
		tb_dtgsm_sample6 = 23,
		tb_cqtgsm_sample6 = 24,
		tb_autocqtgsm_sample6 = 25,
		tb_autodtgsm_sample6 = 26,
		tb_cqtlte_fdd_sample = 27,
		tb_dtlte_fdd_sample = 28,
		tb_dtsignal_sample = 29,
		tb_cqtsignal_sample = 30,
		tb_scan_nbiot_topn_sample = 31,
		tb_cqtgsm_sample2 = 32,
		tb_autocqtgsm_sample2 = 33,
		tb_autodtgsm_sample2 = 34,
		tb_dttdscdma_sample = 35,
		tb_cqttdscdma_sample = 36,
		tb_dttdscdma_sample6 = 37,    //tdscdma语音
		tb_cqttdscdma_sample6 = 38,
		tb_dttdscdma_sample5 = 39,
		tb_cqttdscdma_sample5 = 40,
		tb_dtwcdma_sample = 41,
		tb_dtwcdma_sample5 = 42,
		tb_model_event = 43,
		tb_stat_scan_top = 44,
		tb_model_msg = 45,
		tb_scan_sample2 = 46,
		tb_model_stat_area_event = 47,
		tb_dtwlan_sample = 48,
		tb_cqtwlan_sample = 49,
		tb_es_event_result = 50,
		tb_scan_lte_sample = 51,
		tb_cqtlte_sample = 52,
		tb_dtlte_sample = 53,
		tb_scan_cw_measure_sample = 54,
		tb_scan_FreqSpectrum_sample = 55,
		tb_lte_uep_sample = 56,
		tb_cqtnr_sample = 57,
		tb_dtnr_sample = 58,
		tb_scan_nr_sample = 59,
		tb_gsmr_event = 60, // 这张表为虚拟表，字段配置在 表字段配置_其他.mc 文件中，实际没有这张表，用于客户端 GSMR 数据关联公里标返回格式

		/************************************************************************/
		/* image stati table ID                                                  */
		/************************************************************************/
		tb_model_wcdma_stati_amr_log = 1000,
		tb_model_wcdma_stati_amr_cell,
		tb_model_wcdma_stati_amr_grid,
		tb_model_wcdma_stati_amr_log_idle,
		tb_model_wcdma_stati_amr_cell_idle,
		tb_model_wcdma_stati_amr_grid_idle,
		tb_model_wcdma_stati_vp_log,
		tb_model_wcdma_stati_vp_cell,
		tb_model_wcdma_stati_vp_grid,
		tb_model_wcdma_stati_ps_log,
		tb_model_wcdma_stati_ps_cell,
		tb_model_wcdma_stati_ps_grid,
		tb_model_wcdma_stati_pshs_log_psdpa,
		tb_model_wcdma_stati_pshs_cell_psdpa,
		tb_model_wcdma_stati_pshs_grid_psdpa,
		tb_model_wcdma_stati_pshs_log_psupa,
		tb_model_wcdma_stati_pshs_cell_psupa,
		tb_model_wcdma_stati_pshs_grid_psupa,
		tb_model_cdma_stati_voice_grid,
		tb_model_cdma_stati_voice_log,
		tb_model_tdscdma_stati_amr_grid,
		tb_model_tdscdma_stati_amr_cell,
		tb_model_tdscdma_stati_amr_log,
		tb_model_tdscdma_stati_amr_grid_idle,
		tb_model_tdscdma_stati_amr_cell_idle,
		tb_model_tdscdma_stati_amr_log_idle,
		tb_model_tdscdma_stati_vp_grid,
		tb_model_tdscdma_stati_vp_cell,
		tb_model_tdscdma_stati_vp_log,
		tb_model_tdscdma_stati_ps_grid,
		tb_model_tdscdma_stati_ps_cell,
		tb_model_tdscdma_stati_ps_log,
		tb_model_tdscdma_stati_ps_grid_hsdpa,
		tb_model_tdscdma_stati_ps_cell_hsdpa,
		tb_model_tdscdma_stati_ps_log_hsdpa,
		tb_model_gsm_stati_voice_log,
		tb_model_gsm_stati_voice_cell,
		tb_model_gsm_stati_voice_grid,
		tb_model_gsm_stati_data_log,
		tb_model_gsm_stati_data_cell,
		tb_model_gsm_stati_data_grid,
		tb_model_gsm_stati_mtr_grid = 1041,
		tb_model_gsm_stati_mtr_log,
		tb_model_gsm_stati_mtr_cell,

		tb_model_gsm_stati_voice_cell_grid = 1044,
		tb_model_gsm_stati_data_cell_grid,
		tb_model_wcdma_stati_amr_cell_grid,
		tb_model_wcdma_stati_vp_cell_grid,
		tb_model_wcdma_stati_ps_cell_grid,
		tb_model_wcdma_stati_pshs_cell_grid,
		tb_model_tdscdma_stati_amr_cell_grid,
		tb_model_tdscdma_stati_vp_cell_grid,
		tb_model_tdscdma_stati_ps_cell_grid,

    tb_model_wcdma_stati_amr_area = 1053,
		tb_model_wcdma_stati_amr_area_idle,
		tb_model_wcdma_stati_vp_area,
		tb_model_wcdma_stati_ps_area,
    tb_model_wcdma_stati_pshs_area_psdpa,
		tb_model_wcdma_stati_pshs_area_psupa,
		tb_model_cdma_stati_voice_area,
		tb_model_tdscdma_stati_amr_area,
		tb_model_tdscdma_stati_amr_area_idle,
		tb_model_tdscdma_stati_vp_area,
		tb_model_tdscdma_stati_ps_area,
		tb_model_tdscdma_stati_ps_area_psdpa,
		tb_model_gsm_stati_voice_area,
		tb_model_gsm_stati_data_area,

		tb_model_cdma2000_stati_data_log = 1067,
		tb_model_cdma2000_stati_data_grid = 1068,
		tb_model_cdma2000_stati_data_area = 1069,

		tb_scan_gsm_stati_cell_grid = 1070,
		tb_scan_gsm_stati_log = 1071,

		tb_model_cdma_stati_d_grid = 1072,
		tb_model_cdma_stati_d_log = 1073,
		tb_model_cdma_stati_d_area = 1074,

		tb_scan_tdscdma_stati_cell_grid = 1075,
		tb_scan_tdscdma_stati_log = 1076,
		tb_scan_tdscdma_stati_grid = 1077,

    tb_scan_gsm_stati_grid = 1078,
		tb_scan_wcdma_stati_cell_grid = 1079,
    tb_wlan_stati_log = 1080,

		tb_model_lte_stati_amr_grid = 1082,
		tb_model_lte_stati_amr_cell = 1083,
		tb_model_lte_stati_amr_cell_grid = 1084,
		tb_model_lte_stati_amr_log = 1085,
		tb_scan_ltetopn_stati_grid = 1086,
		tb_scan_ltetopn_stati_cell_grid = 1087,
		tb_scan_ltetopn_stati_log = 1088,
		tb_model_lte_stati_amr_area = 1089,
		tb_model_lte_fdd_stati_amr_grid = 1090,
		tb_model_lte_fdd_stati_amr_cell = 1091,
		tb_model_lte_fdd_stati_amr_cell_grid = 1092,
		tb_model_lte_fdd_stati_amr_log = 1093,
		tb_model_lte_fdd_stati_amr_area = 1094,

		tb_model_lte_signal_stati_grid = 1095,
		tb_model_lte_signal_stati_cell = 1096,
		tb_model_lte_signal_stati_cell_grid = 1097,
		tb_model_lte_signal_stati_log = 1098,
		tb_model_lte_signal_stati_area = 1099,
		tb_scan_gsm_stati_area = 1100,
		tb_scan_tdscdma_stati_area = 1101,
		tb_scan_ltetopn_stati_area = 1102,

		tb_scan_lte_freqspectrum_stati_log = 1103,
		tb_scan_lte_freqspectrum_stati_grid = 1104,


		tb_scan_nbiot_topn_stati_log = 1107,
		tb_scan_nbiot_topn_stati_grid = 1108,
		tb_scan_nbiot_topn_stati_rams_grid = 1109,
		tb_scan_nbiot_topn_stati_cell_grid = 100000,
		tb_scan_nbiot_topn_stati_area = 100001,

    tb_nr_stati_amr_log = 1110,
    tb_nr_stati_amr_cell = 1111,
    tb_nr_stati_amr_grid = 1112,
		tb_nr_stati_amr_cell_grid = 1113,
		tb_nr_stati_amr_area = 1114,

		tb_scan_nr_stati_log = 1115,
		tb_scan_nr_stati_grid = 1116,
		tb_scan_nr_stati_cell_grid = 1117,
		tb_scan_nr_stati_area = 1118,

		tb_scan_nr_freqspectrum_stati_log = 1119,
		tb_scan_nr_freqspectrum_stati_grid = 1120
	};




public:

	void PushItemIntoMap(CColumnItem* item);
	std::vector<STRU_COLUMNID> GetColumnIDbyIDStr(int tableid, std::string columnNames);
	std::vector<STRU_COLUMNID> ConvertIDStrToColumnID(std::string columnNames);
  BOOL MarkColumnExists(STRU_COLUMNID columnid);
	BOOL MarkColumnNoExists(STRU_COLUMNID columnid);
	std::map<STRU_COLUMNID, CColumnItem*> GetTableColumn(const int tableID);
	BOOL IsColumnExist(const STRU_COLUMNID columnID);
	CColumnItem* GetColumnItem(const STRU_COLUMNID columnID);
	BOOL IsColumnDefCanUsed(void);
  std::map<STRU_COLUMNID, std::map<std::string, CColumnItem*>>& GetClomnsStatItem(void);
	BOOL IsTableExist(const int tableid);
	CColumnItem* GetColumn(const int tableid, const std::string columnName);

public:
	static CColumnDef* Instance (void);

private:
	CColumnDef();

public:
	~CColumnDef();

private:
	std::map<STRU_COLUMNID, CColumnItem*> m_pMapClomns;
	std::map<int, std::map<STRU_COLUMNID, CColumnItem*>> m_pMapDBTables;
  std::map<int, std::map<std::string, CColumnItem*>> m_pMapDBTablesColumnName;
	std::map<STRU_COLUMNID, std::map<std::string, CColumnItem*>> m_pMapClomnsStatItem;
};







#endif
