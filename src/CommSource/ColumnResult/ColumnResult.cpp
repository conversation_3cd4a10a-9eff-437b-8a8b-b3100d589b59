#include "ColumnResult.h"

#include "./DtSockServer.h"
#include "./DtdrvApp_Public_Var.h"
#include "./StdNetBuffer.h"

/************************************************************************/
/*CColumnResult                                                         */
/************************************************************************/

CColumnResult::CColumnResult()
	:m_mapAddValuePos(),
	m_TbSqlTypeMap()
{
	m_pDataBaseDeal_ = NULL;
	m_pServerDeal_ = NULL;
	m_pSendBuf_ = NULL;
	m_pParamParser = NULL;

	InitAddValueMap();
	InitSqlTypeMap();
};	

CColumnResult::~CColumnResult()
{
	if(NULL != m_pParamParser)
	{
    delete m_pParamParser;
    m_pParamParser = NULL;
	}


	m_pDataBaseDeal_ = NULL;
	m_pServerDeal_ = NULL;
	m_pSendBuf_ = NULL;

	m_mapAddValuePos.clear();

	for (auto pt = m_TbSqlTypeMap.begin(); pt != m_TbSqlTypeMap.end(); pt++)
	{
		pt->second.clear();
	}
	
	m_TbSqlTypeMap.clear();
};


BOOL 
CColumnResult::InitColumnRusult()
{

	m_pDataBaseDeal_ = NULL;
	m_pServerDeal_ = NULL;
	m_pSendBuf_ = NULL;

	return TRUE;
}

//接收数据,直接发送到客户端
	BOOL 
	CColumnResult::Init(const std::string strColumnID, const std::string strTableID, const std::string strTableName, 
		CDataBaseDeal* const pDataBaseDeal_, PPM_Server_Deal_Base* const pServerDeal_, BYTE* const pSendBuf_)
	{
		Init(pDataBaseDeal_, pServerDeal_, pSendBuf_);

		if(NULL != m_pParamParser)
		{
			delete m_pParamParser;
		}
		m_pParamParser = new CParamParser(pDataBaseDeal_); 
		
		if(FALSE == m_pParamParser->InitColumnResult(strColumnID, strTableID, strTableName))
		{
			return FALSE;
		}

    if(FALSE == InitSendColumns())
		{
			return FALSE;
		}

		return TRUE;
	}

BOOL 
CColumnResult::Init(const std::string strColumnID, const std::vector<int> vec_TableID, std::vector<std::string> vec_TableName,
		CDataBaseDeal* const pDataBaseDeal_, PPM_Server_Deal_Base* const pServerDeal_, BYTE* const pSendBuf_)
{
	Init(pDataBaseDeal_, pServerDeal_, pSendBuf_);

	if(NULL != m_pParamParser)
	{
		delete m_pParamParser;
	}
	m_pParamParser = new CParamParser(pDataBaseDeal_); 

	if(FALSE == m_pParamParser->InitColumnResult(strColumnID, vec_TableID, vec_TableName))
	{
		return FALSE;
	}

	if(FALSE == InitSendColumns())
	{
		return FALSE;
	}

	return TRUE;

}

BOOL 
CColumnResult::Init(const std::vector<STRU_COLUMNID> vec_id, const std::vector<int> vec_table, const std::vector<std::string> vec_tbname,
						CDataBaseDeal* const pDataBaseDeal_, PPM_Server_Deal_Base* const pServerDeal_, BYTE* const pSendBuf_)
{
	Init(pDataBaseDeal_, pServerDeal_, pSendBuf_);

	if(NULL != m_pParamParser)
	{
		delete m_pParamParser;
	}
	m_pParamParser = new CParamParser(pDataBaseDeal_); 

	if(FALSE == m_pParamParser->InitColumnResult(vec_id, vec_table, vec_tbname))
	{
		return FALSE;
	}

	if(FALSE == InitSendColumns())
	{
		return FALSE;
	}

	return TRUE;
}

BOOL 
CColumnResult::Init(const int table, const std::string tbname,
	CDataBaseDeal* const pDataBaseDeal_, PPM_Server_Deal_Base* const pServerDeal_, BYTE* const pSendBuf_)
{
	Init(pDataBaseDeal_, pServerDeal_, pSendBuf_);

	std::vector<int> vec_table;
	std::vector<std::string> vec_tbname;

	vec_table.push_back(table);
	vec_tbname.push_back(tbname);

	if(NULL != m_pParamParser)
	{
		delete m_pParamParser;
	}
	m_pParamParser = new CParamParser(pDataBaseDeal_); 

	if(FALSE == m_pParamParser->InitColumnResult(vec_table, vec_tbname))
	{
		return FALSE;
	}

	if(FALSE == InitSendColumns())
	{
		return FALSE;
	}

	return TRUE;
}

BOOL 
CColumnResult::Init(CDataBaseDeal* const pDataBaseDeal_, PPM_Server_Deal_Base* const pServerDeal_, BYTE* const pSendBuf_)
{
	InitColumnRusult();

	ISearchDeal::Init(pDataBaseDeal_, pServerDeal_, pSendBuf_);

	return TRUE;
}

std::string
CColumnResult::GetReturnColumnID(const std::map<int, std::vector<CColumnItem*>> vec_ColumnItem)
{
	std::string str_result = "";
	BOOL once = TRUE;
	for (auto p_map_ColumnItem = vec_ColumnItem.begin(); 
		p_map_ColumnItem != vec_ColumnItem.end(); ++p_map_ColumnItem)
	{
		for (auto p_vec_ColumnItem = (p_map_ColumnItem->second).begin();
			p_vec_ColumnItem != (p_map_ColumnItem->second).end(); ++p_vec_ColumnItem)
		{
			if (TRUE == once)
			{
				str_result += CStdCommond::itostr((*p_vec_ColumnItem)->imageID) + "," +
					CStdCommond::itostr((*p_vec_ColumnItem)->paraID) + "," + CStdCommond::itostr((*p_vec_ColumnItem)->tbID);
				once = FALSE;
			}
			else 
			{
				str_result += "," + CStdCommond::itostr((*p_vec_ColumnItem)->imageID) + "," + 
					CStdCommond::itostr((*p_vec_ColumnItem)->paraID) + "," + CStdCommond::itostr((*p_vec_ColumnItem)->tbID);
			}	
		}
	}

	return str_result;
}


/************************************************************************
1.生成用于返回结构的ID串（m_ColumnSQL）
2.生成用于返回结构的数据结构（m_vec_Result_）
************************************************************************/
BOOL
CColumnResult::InitSendColumns(void)
{
    std::set<std::string> set_id;

	std::map<int,std::vector<CColumnItem*>>& srcColumn = m_pParamParser->GetSearchColumn();  //源数据结构

	for (auto p_vec_pColumnItem = srcColumn.begin(); 
		p_vec_pColumnItem != srcColumn.end(); ++p_vec_pColumnItem)
	{ 
		for (auto p_vecCColumnItem = p_vec_pColumnItem->second.begin();
			p_vecCColumnItem != p_vec_pColumnItem->second.end(); ++p_vecCColumnItem)
		{
      std::string temp = CStdCommond::itostr((*p_vecCColumnItem)->imageID) + "," + CStdCommond::itostr((*p_vecCColumnItem)->tbID);

      if (set_id.find(temp) != set_id.end() && ((*p_vecCColumnItem)->bColumnProerty & 0x03) == CColumnItem::ColumnProperty_IsImageType)
			{
				continue;
			}

			m_AppColumnResult.push_back(*p_vecCColumnItem);

      set_id.insert(temp);
		}
	}

	if (m_AppColumnResult.size() == 0)
	{
		return FALSE;
	}

	return TRUE;
}

std::string
CColumnResult::GetDBSearchSql(void)
{
   std::string DBSearchSql = "";
   std::vector<CColumnItem*> dbColumnResult = m_pParamParser->GetDBColumn();

   for (auto p_pColumnItem = dbColumnResult.begin(); 
	   p_pColumnItem != dbColumnResult.end(); ++p_pColumnItem)
   {
	   DBSearchSql += (*p_pColumnItem)->dbColumnName + ",";
   }

   return DBSearchSql.substr(0, DBSearchSql.length()-1);
}

std::string
CColumnResult::GetDBSearchSql(std::string logfileTbName)
{
	std::string DBSearchSql = "";
  std::vector<CColumnItem*> dbColumnResult = m_pParamParser->GetDBColumn();

  for (auto p_pColumnItem = dbColumnResult.begin(); p_pColumnItem != dbColumnResult.end(); ++p_pColumnItem)
	{
		if ((*p_pColumnItem)->imageID == 0 && (*p_pColumnItem)->paraID == 66 && (*p_pColumnItem)->tbID == 1)
		{
			#ifdef USE_MYSQL
			DBSearchSql += std::string("IFNULL(b." + (*p_pColumnItem)->dbColumnName + ", 255) as istatus,");
			#else
			DBSearchSql += std::string("ISNULL(b." + (*p_pColumnItem)->dbColumnName + ", 255) as istatus,");
			#endif
		}
		else
		{
			DBSearchSql += std::string(logfileTbName + "." + (*p_pColumnItem)->dbColumnName + ",");
		}
	}

	return DBSearchSql.substr(0, DBSearchSql.length()-1);
}

std::string
CColumnResult::GetSendDataColumnID(void)
{
	std::string SendDataColumnID = "";
  for (auto p_pColumnItem = m_AppColumnResult.begin(); p_pColumnItem != m_AppColumnResult.end(); ++p_pColumnItem)
	{
		SendDataColumnID += CStdCommond::itostr((*p_pColumnItem)->imageID) + "," + 
            CStdCommond::itostr((*p_pColumnItem)->paraID) + "," + CStdCommond::itostr((*p_pColumnItem)->tbID) + ",";
	}

	return SendDataColumnID.substr(0, SendDataColumnID.length()-1);
}



BOOL
CColumnResult::SendColumnsIDToClient(BYTE cmd1, BYTE cmd2, BYTE cmd3 )
{
	if(NULL != m_pServerDeal_)
	{
		PPM_OS::memset(m_pSendBuf_,0,SEND_BUF_MAXSIZE);

		int tm_ioffset = 0;
		CStdNetBufferWriter::PushByte(m_pSendBuf_, tm_ioffset, cmd1, SEND_BUF_MAXSIZE);
		CStdNetBufferWriter::PushByte(m_pSendBuf_, tm_ioffset, cmd2, SEND_BUF_MAXSIZE);
		CStdNetBufferWriter::PushByte(m_pSendBuf_, tm_ioffset, cmd3, SEND_BUF_MAXSIZE);

		std::string strID = GetSendDataColumnID();
		if (0 == strID.length())
		{
			return FALSE;
		}

		CStdNetBufferWriter::PushStr(m_pSendBuf_, tm_ioffset, strID, SEND_BUF_MAXSIZE);

		if(0 > m_pServerDeal_->SendData(m_pSendBuf_,tm_ioffset)){
			return FALSE;
		} 
	}	
	return TRUE;
}

BOOL
CColumnResult::SendGsmrEventColumnsIDToClient(BYTE cmd1, BYTE cmd2, BYTE cmd3)
{
	if (NULL != m_pServerDeal_)
	{
		PPM_OS::memset(m_pSendBuf_, 0, SEND_BUF_MAXSIZE);

		int tm_ioffset = 0;
		CStdNetBufferWriter::PushByte(m_pSendBuf_, tm_ioffset, cmd1, SEND_BUF_MAXSIZE);
		CStdNetBufferWriter::PushByte(m_pSendBuf_, tm_ioffset, cmd2, SEND_BUF_MAXSIZE);
		CStdNetBufferWriter::PushByte(m_pSendBuf_, tm_ioffset, cmd3, SEND_BUF_MAXSIZE);

		std::string strID = "0,1,60,0,2,60,0,3,60,0,4,60,0,5,60,0,6,60,0,7,60,0,8,60,0,9,60,0,10,60,0,11,60,0,12,60,0,13,60,0,14,60,0,15,60,0,16,60,0,17,60,0,18,60,0,19,60,0,20,60,0,21,60,0,22,60,0,23,60,0,24,60,0,25,60,0,26,60,0,27,60,0,28,60,0,29,60,0,30,60,0,31,60,0,32,60,0,33,60,0,34,60,0,35,60,0,36,60";

		if (0 == strID.length())
		{
			return FALSE;
		}

		CStdNetBufferWriter::PushStr(m_pSendBuf_, tm_ioffset, strID, SEND_BUF_MAXSIZE);

		if (0 > m_pServerDeal_->SendData(m_pSendBuf_, tm_ioffset)) {
			return FALSE;
		}
	}
	return TRUE;
}


BOOL
CColumnResult::SearchSqlGetData(const std::string sql, CDataItem* fetchDataItem, std::vector<CDataItem*>* pCDataItemRows)
{
	if (NULL == fetchDataItem || NULL == pCDataItemRows)
	{
		return FALSE;
	}

    CStdSqlOpHelper sqlOpHelper(&m_dbConnInfo);

	if(!sqlOpHelper.ExecuteReader(sql.c_str()))
	{
       return FALSE;
	}

	while(sqlOpHelper.Read())
	{
    pCDataItemRows->push_back(fetchDataItem->FillData(sqlOpHelper));
	}

    return TRUE;
}

BOOL
CColumnResult::SearchSqlGetDataAndSendData(const BYTE cmd1, const BYTE cmd2, const BYTE cmd3,
                                           const std::string sql, CDataItem* fetchDataItem, std::vector<CDataItem*>* pCDataItemRows)
{
	if (NULL == fetchDataItem || NULL == pCDataItemRows)
	{
		return FALSE;
	}

	if(NULL == m_pServerDeal_)
	{
       return FALSE;
	}

	CStdSqlOpHelper sqlOpHelper(&m_dbConnInfo);

	if(!sqlOpHelper.ExecuteReader(sql.c_str()))
	{
		return FALSE;
	}

	while(sqlOpHelper.Read())
	{
		//解析数据，放入结构体
		pCDataItemRows->push_back(fetchDataItem->FillData(sqlOpHelper));


        //发送数据
		int tm_ioffset = 0;
		PPM_OS::memset(m_pSendBuf_,0,SEND_BUF_MAXSIZE);

		BuildSendCmdBuf(cmd1, cmd2, cmd3, tm_ioffset);
 
		BOOL bSuccess = TRUE;
		for (auto p_pCColumnItem = m_AppColumnResult.begin(); 
			p_pCColumnItem != m_AppColumnResult.end(); ++p_pCColumnItem)
		{
      BYTE* buffer = sqlOpHelper[(*p_pCColumnItem)->dbColumnName];
			int dataLenth = sqlOpHelper.GetDataLen((*p_pCColumnItem)->dbColumnName);

			CorrectBigintFieldDatalen((*p_pCColumnItem)->tbName, (*p_pCColumnItem)->dbColumnName, dataLenth);

			if (sqlOpHelper.GetDataType((*p_pCColumnItem)->dbColumnName) == CDataObject::E_DataType_Text)
			{
				std::string unix_text = CUtility::Utf8ToGBK((char*)buffer);
				sqlOpHelper.UpdateBuffer((*p_pCColumnItem)->dbColumnName, (BYTE*)unix_text.c_str(), unix_text.length());
				buffer = sqlOpHelper[(*p_pCColumnItem)->dbColumnName];
				dataLenth = sqlOpHelper.GetUpdateBufferDataLen((*p_pCColumnItem)->dbColumnName);
			}

			if(!AddValue(*p_pCColumnItem,m_pSendBuf_, tm_ioffset, buffer, dataLenth))
			{
				bSuccess = FALSE;
				break;
			}
		}

		if(!bSuccess)
		{
			continue;
		}

		if(0 > m_pServerDeal_->SendData(m_pSendBuf_,tm_ioffset)){
			return FALSE;
		}

	}

	return TRUE;
}

void CColumnResult::BuildSendCmdBuf(const BYTE cmd1, const BYTE cmd2, const BYTE cmd3, int& tm_ioffset)
{
	CStdNetBufferWriter::PushByte(m_pSendBuf_, tm_ioffset, cmd1, SEND_BUF_MAXSIZE);
	CStdNetBufferWriter::PushByte(m_pSendBuf_, tm_ioffset, cmd2, SEND_BUF_MAXSIZE);
	CStdNetBufferWriter::PushByte(m_pSendBuf_, tm_ioffset, cmd3, SEND_BUF_MAXSIZE);
}

BOOL
CColumnResult::SearchSqlSendData(const BYTE cmd1, const BYTE cmd2, const BYTE cmd3,
								 const std::string sql)
{
	if(NULL == m_pServerDeal_)
	{
		return FALSE;
	}

	CStdSqlOpHelper sqlOpHelper(&m_dbConnInfo);

	if(!sqlOpHelper.ExecuteReader(sql.c_str()))
	{
		return FALSE;
	}

	while(sqlOpHelper.Read())
	{
		//发送数据
		PPM_OS::memset(m_pSendBuf_,0,SEND_BUF_MAXSIZE);

		int tm_ioffset = 0;

		BuildSendCmdBuf(cmd1, cmd2, cmd3, tm_ioffset);

    BOOL bSuccess = TRUE;
		for (auto p_pCColumnItem = m_AppColumnResult.begin(); p_pCColumnItem != m_AppColumnResult.end(); ++p_pCColumnItem)
		{
			BYTE* buffer = sqlOpHelper[(*p_pCColumnItem)->dbColumnName];
			int dataLenth = sqlOpHelper.GetDataLen((*p_pCColumnItem)->dbColumnName);

			// 针对freetds 7.0版本不支持处理bigint的情况做处理
			CorrectBigintFieldDatalen((*p_pCColumnItem)->tbName, (*p_pCColumnItem)->dbColumnName, dataLenth);
			// 针对OceanBase数据库执行 IFNULL(b.istatus, 255) 后，结果由字段的 tinyint 转换为了longlong 类型了的情况做处理
			CorrectTinyintFieldDatalen((*p_pCColumnItem), dataLenth);

			if (sqlOpHelper.GetDataType((*p_pCColumnItem)->dbColumnName) == CDataObject::E_DataType_Text)
			{
				std::string unix_text = CUtility::Utf8ToGBK((char*)buffer);
				sqlOpHelper.UpdateBuffer((*p_pCColumnItem)->dbColumnName, (BYTE*)unix_text.c_str(), unix_text.length());
				buffer = sqlOpHelper[(*p_pCColumnItem)->dbColumnName];
				dataLenth = sqlOpHelper.GetUpdateBufferDataLen((*p_pCColumnItem)->dbColumnName);
			}

			if (dataLenth < 0)
			{
				dataLenth = 0;
			}

			if(!AddValue(*p_pCColumnItem,m_pSendBuf_, tm_ioffset, buffer, dataLenth))
			{
				bSuccess = FALSE;
				break;
			}
		}

		if(FALSE == bSuccess)
		{
      continue;
		}

		if(0 > m_pServerDeal_->SendData(m_pSendBuf_,tm_ioffset)){
			return FALSE;
		}

	}

	return TRUE;

}

BOOL
CColumnResult::SearchSqlSendDataWithMyData(const BYTE cmd1, const BYTE cmd2, const BYTE cmd3,
                                           const std::string sql, std::vector<STRU_ColumnAppendData>& mapColumnAppendData)
{
	if(NULL == m_pServerDeal_)
	{
		return FALSE;
	}

	CStdSqlOpHelper sqlOpHelper(&m_dbConnInfo);

	if(!sqlOpHelper.ExecuteReader(sql.c_str()))
	{
		return FALSE;
	}

	while(sqlOpHelper.Read())
	{
		//发送数据
		PPM_OS::memset(m_pSendBuf_,0,SEND_BUF_MAXSIZE);

		int tm_ioffset = 0;
		CStdNetBufferWriter::PushByte(m_pSendBuf_, tm_ioffset, cmd1, SEND_BUF_MAXSIZE);
    CStdNetBufferWriter::PushByte(m_pSendBuf_, tm_ioffset, cmd2, SEND_BUF_MAXSIZE);
    CStdNetBufferWriter::PushByte(m_pSendBuf_, tm_ioffset, cmd3, SEND_BUF_MAXSIZE);

		BOOL bSuccess = TRUE;

		if (!WriteData(sqlOpHelper, mapColumnAppendData, tm_ioffset, bSuccess))
		{
			return FALSE;
		}

		AddData(sqlOpHelper, tm_ioffset, bSuccess);

		if(!bSuccess)
		{
      continue;
		}

		if(0 > m_pServerDeal_->SendData(m_pSendBuf_,tm_ioffset)){
			return FALSE;
		}

	}

	return TRUE;
}

BOOL CColumnResult::WriteData(CStdSqlOpHelper& sqlOpHelper, std::vector<STRU_ColumnAppendData>& mapColumnAppendData, int& tm_ioffset, BOOL& bSuccess)
{
	for(auto it = mapColumnAppendData.begin(); it != mapColumnAppendData.end(); ++it)
	{
		BYTE* buffer = sqlOpHelper[it->columnName];
		int dataLenth = sqlOpHelper.GetDataLen(it->columnName);

		if (TYPE_CS_INT != it->dataType)
		{
			return FALSE;
		}

		//直接往内存写入用户的数据
		if (dataLenth == 8) //NR NCI在表中为bigint类型，此处特殊处理
		{
			if (!CStdNetBufferWriter::PushInt64(m_pSendBuf_, tm_ioffset, *(INT64*)buffer, SEND_BUF_MAXSIZE))
			{
				bSuccess = FALSE;
				break;
			}
		}
		else
		{
			if (!CStdNetBufferWriter::PushInt(m_pSendBuf_, tm_ioffset, *(int*)buffer, SEND_BUF_MAXSIZE))
			{
				bSuccess = FALSE;
				break;
			}
		}
	}

	return TRUE;
}

void CColumnResult::AddData(CStdSqlOpHelper& sqlOpHelper, int& tm_ioffset, BOOL& bSuccess)
{
	for (auto p_pCColumnItem = m_AppColumnResult.begin();
		p_pCColumnItem != m_AppColumnResult.end(); ++p_pCColumnItem)
	{
		BYTE* buffer = sqlOpHelper[(*p_pCColumnItem)->dbColumnName];
		int dataLenth = sqlOpHelper.GetDataLen((*p_pCColumnItem)->dbColumnName);

		CorrectBigintFieldDatalen((*p_pCColumnItem)->tbName, (*p_pCColumnItem)->dbColumnName, dataLenth);

		if (sqlOpHelper.GetDataType((*p_pCColumnItem)->dbColumnName) == CDataObject::E_DataType_Text)
		{
			std::string unix_text = CUtility::Utf8ToGBK((char*)buffer);
			sqlOpHelper.UpdateBuffer((*p_pCColumnItem)->dbColumnName, (BYTE*)unix_text.c_str(), unix_text.length());
			buffer = sqlOpHelper[(*p_pCColumnItem)->dbColumnName];
			dataLenth = sqlOpHelper.GetUpdateBufferDataLen((*p_pCColumnItem)->dbColumnName);
		}

		if(!AddValue(*p_pCColumnItem,m_pSendBuf_, tm_ioffset, buffer, dataLenth))
		{
			bSuccess = FALSE;
			break;
		}
	}
}

BOOL
CColumnResult::AddValue( CColumnItem* p_CColumnItem, BYTE* const pBuf, int& ioffset, BYTE* dbDataBuf, int dbDataLenth)
{
	if (NULL == p_CColumnItem
	|| NULL == dbDataBuf
	|| dbDataLenth < 0)
	{
			return FALSE;
	}

	if(p_CColumnItem->IsColumnImage()
	&& p_CColumnItem->IsColumnFix())
	{
		//如果是Image，结构确认的结构，就需要挖出来填写
		if (!WriteImagStructValue(p_CColumnItem, dbDataLenth, pBuf, ioffset, dbDataBuf))
		{
			return FALSE;
		}		
	}
	else if(p_CColumnItem->IsColumnImage()
		&& !p_CColumnItem->IsColumnFix())
	{
		CStdNetBufferWriter::PushSmallInt(pBuf, ioffset, (WORD)dbDataLenth, SEND_BUF_MAXSIZE);
		CStdNetBufferWriter::PushBuffer(pBuf, ioffset, dbDataLenth, dbDataBuf, SEND_BUF_MAXSIZE);
	}
	else 
	{
		if(!AddValue(p_CColumnItem->dataType, pBuf, ioffset, dbDataBuf, dbDataLenth))
		{
			return FALSE;
		}  
	}

	return TRUE;
}

BOOL CColumnResult::WriteImagStructValue(CColumnItem* p_CColumnItem, int dbDataLenth, BYTE* const pBuf, int& ioffset, BYTE* dbDataBuf)
{
	int dataStartPos =  p_CColumnItem->subStart;
	int dataEndPos = p_CColumnItem->subEnd;

	if(dataStartPos < 0 || dataStartPos > dbDataLenth
		|| dataEndPos < 0 || dataEndPos > dbDataLenth)
	{
		if(!AddValue(p_CColumnItem->dataType, pBuf, ioffset, p_CColumnItem->abnormalDataBuffer, p_CColumnItem->dataBufLenth))
		{
			return FALSE;
		}  
	}
	else 
	{
		if(!CStdNetBufferWriter::PushBuffer(pBuf, ioffset, dbDataLenth, dbDataBuf, dataStartPos, dataEndPos, SEND_BUF_MAXSIZE))
		{
			return FALSE;
		}
	}

	return TRUE;
}

BOOL 
CColumnResult::AddValue(int dataType, BYTE* const pBuf, int& ioffset, BYTE* dbDataBuf, int dbDataLenth)
{
	std::map<int, DealAddValueData>::iterator it;

	it = m_mapAddValuePos.find(dataType);

	if (it != m_mapAddValuePos.end())
	{
		return (this->*(it->second))(pBuf, ioffset, dbDataBuf, dbDataLenth);
	}
	else
	{
		return FALSE;
	}
}


BOOL CColumnResult::Deal_CS_Tinyint(BYTE* const pBuf, int& ioffset, BYTE* dbDataBuf, int dbDataLenth)
{
	CHECLFALSE(CStdNetBufferWriter::PushByte(pBuf, ioffset, *(byte*)dbDataBuf, SEND_BUF_MAXSIZE))
	return TRUE;
}

BOOL CColumnResult::Deal_CS_Smallint(BYTE* const pBuf, int& ioffset, BYTE* dbDataBuf, int dbDataLenth)
{
	CHECLFALSE(CStdNetBufferWriter::PushSmallInt(pBuf, ioffset, *(WORD*)dbDataBuf, SEND_BUF_MAXSIZE))
	return TRUE;
}

BOOL CColumnResult::Deal_CS_Float(BYTE* const pBuf, int& ioffset, BYTE* dbDataBuf, int dbDataLenth)
{
	CHECLFALSE(CStdNetBufferWriter::PushFloat(pBuf, ioffset, *(float*)dbDataBuf, SEND_BUF_MAXSIZE))
	return TRUE;
}

BOOL CColumnResult::Deal_CS_Int_Float(BYTE* const pBuf, int& ioffset, BYTE* dbDataBuf, int dbDataLenth)
{
	CHECLFALSE(CStdNetBufferWriter::PushInt(pBuf, ioffset, *(int*)dbDataBuf, SEND_BUF_MAXSIZE))
	return TRUE;
}

BOOL CColumnResult::Deal_CS_Int(BYTE* const pBuf, int& ioffset, BYTE* dbDataBuf, int dbDataLenth)
{
	if (dbDataLenth == 1)
	{
		int data = *(BYTE*)dbDataBuf & 0x000000ff;
		CHECLFALSE(CStdNetBufferWriter::PushInt(pBuf, ioffset, data, SEND_BUF_MAXSIZE))
	}
	else if (dbDataLenth == 8)
	{
		CHECLFALSE(CStdNetBufferWriter::PushInt64(pBuf, ioffset, *(LONGLONG*)dbDataBuf, SEND_BUF_MAXSIZE))
	}
	else
	{
		CHECLFALSE(CStdNetBufferWriter::PushInt(pBuf, ioffset, *(int*)dbDataBuf, SEND_BUF_MAXSIZE))
	}

	return TRUE;
}

BOOL CColumnResult::Deal_CS_Int64(BYTE* const pBuf, int& ioffset, BYTE* dbDataBuf, int dbDataLenth)
{
	if (dbDataLenth == 4)
	{
		LONGLONG data = *(int*)dbDataBuf & 0x00000000ffffffff;
		CHECLFALSE(CStdNetBufferWriter::PushInt64(pBuf, ioffset, (LONGLONG)data, SEND_BUF_MAXSIZE))
	}
	else
	{
		CHECLFALSE(CStdNetBufferWriter::PushInt64(pBuf, ioffset, *(LONGLONG*)dbDataBuf, SEND_BUF_MAXSIZE))
	}

	return TRUE;
}

BOOL CColumnResult::Deal_CS_Text(BYTE* const pBuf, int& ioffset, BYTE* dbDataBuf, int dbDataLenth)
{
	CHECLFALSE(CStdNetBufferWriter::PushSmallInt(pBuf, ioffset, (WORD)dbDataLenth, SEND_BUF_MAXSIZE))
	CHECLFALSE(CStdNetBufferWriter::PushBuffer(pBuf, ioffset, dbDataLenth, dbDataBuf, SEND_BUF_MAXSIZE))
	return TRUE;
}

void CColumnResult::InitAddValueMap(void)
{
	m_mapAddValuePos.insert(std::make_pair(TYPE_CS_TINYINT, &CColumnResult::Deal_CS_Tinyint));

	m_mapAddValuePos.insert(std::make_pair(TYPE_CS_SMALLINT, &CColumnResult::Deal_CS_Smallint));
	m_mapAddValuePos.insert(std::make_pair(TYPE_CS_USMALLINT, &CColumnResult::Deal_CS_Smallint));

	m_mapAddValuePos.insert(std::make_pair(TYPE_CS_FLOAT, &CColumnResult::Deal_CS_Float));

	m_mapAddValuePos.insert(std::make_pair(TYPE_CS_INT_FLOAT, &CColumnResult::Deal_CS_Int_Float));

	m_mapAddValuePos.insert(std::make_pair(TYPE_CS_INT, &CColumnResult::Deal_CS_Int));

	m_mapAddValuePos.insert(std::make_pair(TYPE_CS_UINT64, &CColumnResult::Deal_CS_Int64));
	m_mapAddValuePos.insert(std::make_pair(TYPE_CS_INT64, &CColumnResult::Deal_CS_Int64));

	m_mapAddValuePos.insert(std::make_pair(TYPE_CS_VARYBIN_MERGE, &CColumnResult::Deal_CS_Text));
	m_mapAddValuePos.insert(std::make_pair(TYPE_CS_VARYBIN, &CColumnResult::Deal_CS_Text));
	m_mapAddValuePos.insert(std::make_pair(TYPE_CS_TEXT, &CColumnResult::Deal_CS_Text));
	m_mapAddValuePos.insert(std::make_pair(TYPE_CS_TEXT_MERGE, &CColumnResult::Deal_CS_Text));
	m_mapAddValuePos.insert(std::make_pair(TYPE_CS_VARTEXT, &CColumnResult::Deal_CS_Text));

}

void CColumnResult::InitSqlTypeMap(void)
{
	std::vector<STRU_TbSqlTypeInfo> tmpvec;
	STRU_TbSqlTypeInfo tbsqltype;
	tbsqltype.SetInfo("ici", TYPE_CS_INT64, 8);
	tmpvec.clear();
	tmpvec.push_back(tbsqltype);
	m_TbSqlTypeMap.insert(std::make_pair(std::string("tb_dtnr_sample"), tmpvec));

	tmpvec.clear();
	tbsqltype.SetInfo("ici", TYPE_CS_INT64, 8);
	tmpvec.push_back(tbsqltype);
	tbsqltype.SetInfo("itargetci", TYPE_CS_INT64, 8);
	tmpvec.push_back(tbsqltype);
	tbsqltype.SetInfo("ivalue1", TYPE_CS_INT64, 8);
	tmpvec.push_back(tbsqltype);
	tbsqltype.SetInfo("ivalue2", TYPE_CS_INT64, 8);
	tmpvec.push_back(tbsqltype);
	tbsqltype.SetInfo("ivalue3", TYPE_CS_INT64, 8);
	tmpvec.push_back(tbsqltype);
	tbsqltype.SetInfo("ivalue4", TYPE_CS_INT64, 8);
	tmpvec.push_back(tbsqltype);
	tbsqltype.SetInfo("ivalue5", TYPE_CS_INT64, 8);
	tmpvec.push_back(tbsqltype);
	tbsqltype.SetInfo("ivalue6", TYPE_CS_INT64, 8);
	tmpvec.push_back(tbsqltype);
	tbsqltype.SetInfo("ivalue7", TYPE_CS_INT64, 8);
	tmpvec.push_back(tbsqltype);
	tbsqltype.SetInfo("ivalue8", TYPE_CS_INT64, 8);
	tmpvec.push_back(tbsqltype);
	tbsqltype.SetInfo("ivalue9", TYPE_CS_INT64, 8);
	tmpvec.push_back(tbsqltype);
	tbsqltype.SetInfo("ivalue10", TYPE_CS_INT64, 8);
	tmpvec.push_back(tbsqltype);
	m_TbSqlTypeMap.insert(std::make_pair(std::string("tb_model_event"), tmpvec));
	
	tmpvec.clear();
	tbsqltype.SetInfo("ici", TYPE_CS_INT64, 8);
	tmpvec.push_back(tbsqltype);
	m_TbSqlTypeMap.insert(std::make_pair(std::string("tb_nr_stati_amr_cell"), tmpvec));

	tmpvec.clear();
	tbsqltype.SetInfo("ici", TYPE_CS_INT64, 8);
	tmpvec.push_back(tbsqltype);
	m_TbSqlTypeMap.insert(std::make_pair(std::string("tb_nr_stati_amr_cell_grid"), tmpvec));
	
}

void CColumnResult::CorrectBigintFieldDatalen(std::string tbname, std::string fieldname, int& datalen)
{
#ifdef FREETDS_7_0
	auto pMaptor = m_TbSqlTypeMap.find(tbname);
	if (pMaptor == m_TbSqlTypeMap.end())
	{
		return;
	}

	for (std::size_t i = 0; i < pMaptor->second.size(); i++)
	{
		if (pMaptor->second[i].columnName == fieldname)
		{
			datalen = pMaptor->second[i].datalen;
			break;
		}
	}
#endif
}

void CColumnResult::CorrectTinyintFieldDatalen(CColumnItem* pColumnItem, int& datalen)
{
	#ifdef USE_MYSQL
	if (pColumnItem->imageID == 0 && pColumnItem->paraID == 66 && pColumnItem->tbID == 1)
	{
		if (datalen != 1)
		{
			datalen = 1;
		}
	}
	#endif
}