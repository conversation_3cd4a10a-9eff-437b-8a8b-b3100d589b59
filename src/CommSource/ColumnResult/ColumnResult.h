#pragma once

#include "./ColumnDef.h"
#include "./DBColumnDef.h"
#include "./ParamParser.h"
#include "./StdSqlOpHelper.h"

struct STRU_ColumnAppendData
{
   std::string columnName;
   int dataType;
};

struct STRU_TbSqlTypeInfo
{
	void SetInfo(std::string colname, int datatype, int len)
	{
		columnName = colname;
		dataType = datatype;
		datalen = len;
	}
   std::string columnName;
   int dataType;
	 int datalen;
};

class CColumnResult : public ISearchDeal
{
public:
	enum SearchType
	{
		SearchType_SendData = 1,
		SearchType_FetchData,
		SearchType_FetchAndSendData,
		SearchType_SendDataWithMyData
	};

public:
	CColumnResult();
	~CColumnResult();

	//接收数据,直接发送到客户端
	BOOL Init(const std::string strColumnID, const std::string strTableID, const std::string strTableName, 
		CDataBaseDeal* const pDataBaseDeal_, PPM_Server_Deal_Base* const pServerDeal_, BYTE* const pSendBuf_);

	BOOL Init(const std::string strColumnID, const std::vector<int> vec_TableID, std::vector<std::string> vec_TableName,
		CDataBaseDeal* const pDataBaseDeal_, PPM_Server_Deal_Base* const pServerDeal_, BYTE* const pSendBuf_);

	BOOL Init(const std::vector<STRU_COLUMNID> vec_id, const std::vector<int> vec_table, const std::vector<std::string> vec_tbname,
		          CDataBaseDeal* const pDataBaseDeal_, PPM_Server_Deal_Base* const pServerDeal_, BYTE* const pSendBuf_);

  //接收数据并保存
	BOOL Init(const int table, const std::string tbname,
		CDataBaseDeal* const pDataBaseDeal_, PPM_Server_Deal_Base* const pServerDeal_, BYTE* const pSendBuf_);

	virtual BOOL Init(CDataBaseDeal* const pDataBaseDeal_, PPM_Server_Deal_Base* const pServerDeal_, BYTE* const pSendBuf_);

public:
	BOOL SendColumnsIDToClient(BYTE cmd1, BYTE cmd2, BYTE cmd3 );
	BOOL SendGsmrEventColumnsIDToClient(BYTE cmd1, BYTE cmd2, BYTE cmd3);

	BOOL SearchSqlGetData(const std::string sql, CDataItem* fetchDataItem = NULL, std::vector<CDataItem*>* pCDataItemRows = NULL);
	BOOL SearchSqlGetDataAndSendData(const BYTE cmd1, const BYTE cmd2, const BYTE cmd3, const std::string sql,
	CDataItem* fetchDataItem = NULL, std::vector<CDataItem*>* pCDataItemRows = NULL);
	BOOL SearchSqlSendData(const BYTE cmd1, const BYTE cmd2, const BYTE cmd3,const std::string sql);
	BOOL SearchSqlSendDataWithMyData(const BYTE cmd1, const BYTE cmd2, const BYTE cmd3,const std::string sql, std::vector<STRU_ColumnAppendData>& mapMyData);
	std::string GetDBSearchSql(void);
	std::string GetDBSearchSql(std::string logfileTbName);

private:
	BOOL  InitColumnRusult();
	std::string GetReturnColumnID(const std::map<int, std::vector<CColumnItem*>> vec_ColumnItem);
	BOOL InitSendColumns(void);
	std::string GetSendDataColumnID(void);
	BOOL AddValue( CColumnItem* p_CColumnItem, BYTE* const pBuf, int& ioffset, BYTE* dbDataBuf, int dbDataLenth);
	BOOL AddValue(int dataType, BYTE* const pBuf, int& ioffset, BYTE* dbDataBuf, int dbDataLenth);

	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset)
	{
		return TRUE;
	}

	virtual BOOL RowResultDeal(int nQType)
	{
    return TRUE;
	}

private:
  BOOL WriteData(CStdSqlOpHelper& sqlOpHelper, std::vector<STRU_ColumnAppendData>& mapColumnAppendData, int& tm_ioffset, BOOL& bSuccess);
  void AddData(CStdSqlOpHelper& sqlOpHelper, int& tm_ioffset, BOOL& bSuccess);
	BOOL WriteImagStructValue(CColumnItem* p_CColumnItem, int dbDataLenth, BYTE* const pBuf, int& ioffset, BYTE* dbDataBuf);

private:
	typedef BOOL (CColumnResult::*DealAddValueData)(BYTE* const pBuf, int& ioffset, BYTE* dbDataBuf, int dbDataLenth);
	std::map<int, DealAddValueData> m_mapAddValuePos;
	BOOL Deal_CS_Tinyint(BYTE* const pBuf, int& ioffset, BYTE* dbDataBuf, int dbDataLenth);
	BOOL Deal_CS_Smallint(BYTE* const pBuf, int& ioffset, BYTE* dbDataBuf, int dbDataLenth);
	BOOL Deal_CS_Float(BYTE* const pBuf, int& ioffset, BYTE* dbDataBuf, int dbDataLenth);
	BOOL Deal_CS_Int_Float(BYTE* const pBuf, int& ioffset, BYTE* dbDataBuf, int dbDataLenth);
	BOOL Deal_CS_Int(BYTE* const pBuf, int& ioffset, BYTE* dbDataBuf, int dbDataLenth);
	BOOL Deal_CS_Int64(BYTE* const pBuf, int& ioffset, BYTE* dbDataBuf, int dbDataLenth);
	BOOL Deal_CS_Text(BYTE* const pBuf, int& ioffset, BYTE* dbDataBuf, int dbDataLenth);
	void InitAddValueMap(void);
	void BuildSendCmdBuf(const BYTE cmd1, const BYTE cmd2, const BYTE cmd3, int& tm_ioffset);

	void InitSqlTypeMap(void);
	void CorrectBigintFieldDatalen(std::string tbname, std::string fieldname, int& datalen);
	void CorrectTinyintFieldDatalen(CColumnItem* pColumnItem, int& datalen);

private:
	CParamParser* m_pParamParser;

	std::vector<CColumnItem*> m_AppColumnResult;       //服务端数据发送数据结构
	// 用于存储一些表中的一些字段对应的数据库类型，针对Freetds 7.0 无法处理 bigint、和 varbanary 类型的处理方式
	std::map<std::string, std::vector<STRU_TbSqlTypeInfo>> m_TbSqlTypeMap; 

};
