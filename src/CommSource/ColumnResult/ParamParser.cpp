#include "ParamParser.h"
#include "./ColumnDef.h"
#include "./DataBaseDeal.h"
#include "./SearchDIY.h"
#include "./DataBaseInfo.h"
#include "./DtdrvApp_Public_Var.h"


CParamParser::CParamParser(CDataBaseDeal* const pDataBaseDeal_)
{
	this->m_pDataBaseDeal_ = pDataBaseDeal_;

}

CParamParser::~CParamParser(void)
{
   Clear();
}

int
CParamParser::Clear()
{
	m_SrcColumn.clear();

	return 0;
}




BOOL
CParamParser::InitColumnResult(const std::string strColumnID, const std::string strTableID, const std::string strTableName)
{
	if (0 == strTableID.size())
	{
		return FALSE;
	}

	std::vector<std::string> vec_str = CStdCommond::str_slipt(strTableID, ",");
	std::vector<std::string> vec_TableName = CStdCommond::str_slipt(strTableName, ",");

	std::vector<int> vec_TableID;
	for (auto p_vecStr = vec_str.begin(); p_vecStr != vec_str.end(); p_vecStr++)
	{
		vec_TableID.push_back(atoi((*p_vecStr).c_str()));
	}

	return InitColumnResult(strColumnID, vec_TableID, vec_TableName);
}


BOOL
CParamParser::InitColumnResult(const std::string strColumnID, const std::vector<int> vec_TableID, std::vector<std::string> vec_TableName)
{
	if (0 == strColumnID.size())
	{
		return FALSE;
	}

	std::vector<std::string> vec_str = CStdCommond::str_slipt(strColumnID, ",");

	if (vec_str.size()%3 != 0)
	{
		return FALSE;
	}

	std::vector<STRU_COLUMNID> vec_ColumnID;

	for (auto p_vecStr = vec_str.begin(); p_vecStr != vec_str.end(); p_vecStr += 3)
	{
		STRU_COLUMNID temp;
		temp.imageID = atoi((*p_vecStr).c_str());
		temp.paraID = atoi((*(p_vecStr + 1)).c_str());
		temp.tbID = atoi((*(p_vecStr + 2)).c_str());
		vec_ColumnID.push_back(temp);
	}

	return InitColumnResult(vec_ColumnID, vec_TableID, vec_TableName);
}


BOOL
CParamParser::InitColumnResult(const std::vector<int> vec_TableID, std::vector<std::string> vec_TableName)
{
	if (0 == vec_TableID.size() || 0 == vec_TableName.size() || vec_TableID.size() != vec_TableName.size())
	{
		return FALSE;
	}

	//检测服务端配置表字段与数据库现有的表字段差别
	for (size_t i=0; i<vec_TableID.size(); i++)
	{
		CheckDBColumnExitsColumnDef(vec_TableID[i], vec_TableName[i]);
	}

	//客户端发送的查询数据结构，与服务端处理数据结构校对。获得需要处理的数据结构
	m_SrcColumn = CheckSearchColumn(vec_TableID);
	if (m_SrcColumn.size() == 0)
	{
		return FALSE;
	}

	InitDBSearchColumns();

	return TRUE;
}


BOOL
CParamParser::InitColumnResult(const std::vector<STRU_COLUMNID> vec_ColumnID, const std::vector<int> vec_TableID, std::vector<std::string> vec_TableName)
{
	if (0 == vec_ColumnID.size() || 0 == vec_TableID.size() || 0 == vec_TableName.size() || vec_TableID.size() != vec_TableName.size())
	{
		return FALSE;
	}

	//检测服务端配置表字段与数据库现有的表字段差别
	for (size_t i=0; i<vec_TableID.size(); i++)
	{
		CheckDBColumnExitsColumnDef(vec_TableID[i], vec_TableName[i]);
	}

	//客户端发送的查询数据结构，与服务端处理数据结构校对。获得需要处理的数据结构
	m_SrcColumn = CheckSearchColumn(vec_ColumnID, vec_TableID);

    InitDBSearchColumns();

	if (m_SrcColumn.size() == 0
		|| m_DBColumnResult.size() == 0)
	{
		return FALSE;
	}	

	return TRUE;
}



void 
CParamParser::CheckDBColumnExitsColumnDef(int tbid, std::string tbname)
{
	CDBColumnDef::Instance()->GetDBColumns(tbname, m_pDataBaseDeal_, NULL, NULL);
	std::map<STRU_COLUMNID, CColumnItem*> columnItems = CColumnDef::Instance()->GetTableColumn(tbid);

	for (auto p_ColumnItem = columnItems.begin(); p_ColumnItem != columnItems.end(); ++p_ColumnItem)
	{
		STRU_COLUMNID columnid = p_ColumnItem->first;
		if ((tbid == 1 && columnid.imageID == 0 && columnid.paraID == 66 && columnid.tbID == 1) ||
			CDBColumnDef::Instance()->IsColumnExistsInDB(tbname, columnItems[columnid]->dbColumnName))
		{
			CColumnDef::Instance()->MarkColumnExists(columnid);
		}
		else 
		{
			CColumnDef::Instance()->MarkColumnNoExists(columnid);
		}
	}
}

std::map<int, std::vector< CColumnItem*>>
CParamParser::CheckSearchColumn(const std::vector<STRU_COLUMNID> vec_id, const std::vector<int> vec_table)
{
	std::map<int, std::vector<CColumnItem*>> vec_result;

	for (auto p_vec_table = vec_table.begin(); p_vec_table != vec_table.end(); ++p_vec_table)
	{
		vec_result[*p_vec_table] = CheckSearchColumn(vec_id, *p_vec_table);
	}
	return vec_result;
}

std::vector< CColumnItem*>
CParamParser::CheckSearchColumn(const std::vector<STRU_COLUMNID> vec_id, const int tableID)
{
	std::vector<CColumnItem*> vec_result;

	if (0 == vec_id.size())
	{
		return vec_result;
	}

	//如果具有三元组-1，-1，-1就默认为全字段查询
	if (IsSearchAllColumn(vec_id))
	{
		std::map<STRU_COLUMNID, CColumnItem*> columnItems = CColumnDef::Instance()->GetTableColumn(tableID);

		for (auto p_mappCColumnItem = columnItems.begin(); 
			p_mappCColumnItem != columnItems.end(); ++p_mappCColumnItem)
		{
			if (CColumnDef::Instance()->IsColumnExist(p_mappCColumnItem->first))
			{
				vec_result.push_back(p_mappCColumnItem->second);
			}      
		}
	}
	else 
	{
		std::map<STRU_COLUMNID, CColumnItem*> columnItems = CColumnDef::Instance()->GetTableColumn(tableID);
		std::set<STRU_COLUMNID> columnItemSet;

		for (auto p_vec_id = vec_id.begin(); p_vec_id != vec_id.end(); ++p_vec_id)
		{
			if (CColumnDef::Instance()->IsColumnExist(*p_vec_id) && columnItems.find(*p_vec_id) != columnItems.end() 
				&& columnItemSet.find(*p_vec_id) == columnItemSet.end())
			{
				vec_result.push_back(CColumnDef::Instance()->GetColumnItem(*p_vec_id));
				columnItemSet.insert(*p_vec_id);
			}
		}
	}

	return vec_result;
}

BOOL CParamParser::IsSearchAllColumn(const std::vector<STRU_COLUMNID> vec_id)
{
	BOOL SearchAllColumn = FALSE;
	for (size_t i=0; i<vec_id.size(); ++i)
	{
		if (vec_id[i].imageID == -1 && vec_id[i].paraID == -1 && vec_id[i].tbID == -1)
		{
			SearchAllColumn = TRUE;
			break;
		}
	}

	return SearchAllColumn;
}

std::map<int, std::vector< CColumnItem*>>
CParamParser::CheckSearchColumn(const std::vector<int> vec_table)
{
	std::map<int, std::vector<CColumnItem*>> vec_result;

	for (auto p_vec_table = vec_table.begin(); p_vec_table != vec_table.end(); ++p_vec_table)
	{
		std::map<STRU_COLUMNID, CColumnItem*> columnItems = CColumnDef::Instance()->GetTableColumn(*p_vec_table);

    for (auto p_vec_id = columnItems.begin(); p_vec_id != columnItems.end(); ++p_vec_id)
		{
			if (CColumnDef::Instance()->IsColumnExist(p_vec_id->first))
			{
				vec_result[*p_vec_table].push_back(columnItems[p_vec_id->first]);
			}
		}
	}
	return vec_result;
}


/************************************************************************
1.生成用于数据库查询的查询语句（m_ColumnSQL）
2.生成用于获取数据返回的绑定结构（m_vec_Result_）
************************************************************************/
BOOL
CParamParser::InitDBSearchColumns(void)
{
  m_DBColumnResult.clear();

	std::map<std::string, int> map_ParaResult;

  for (auto p_vec_pColumnItem = m_SrcColumn.begin(); p_vec_pColumnItem != m_SrcColumn.end(); ++p_vec_pColumnItem)
	{ 
		for (auto p_vecCColumnItem = p_vec_pColumnItem->second.begin(); 
			p_vecCColumnItem != p_vec_pColumnItem->second.end(); ++p_vecCColumnItem)
		{
			std::string temp = CStdCommond::itostr((*p_vecCColumnItem)->imageID) + "," + CStdCommond::itostr((*p_vecCColumnItem)->tbID);

			if (((*p_vecCColumnItem)->bColumnProerty & CColumnItem::ColumnProperty_IsImageType) == CColumnItem::ColumnProperty_IsImageType
				&& map_ParaResult.find(std::string(temp)) != map_ParaResult.end())
			{
				continue;
			}

			m_DBColumnResult.push_back(*p_vecCColumnItem);

			map_ParaResult[temp] = 0;
		}
	}

	if (m_DBColumnResult.size() == 0)
	{
		return FALSE;
	}

	return TRUE;
}
