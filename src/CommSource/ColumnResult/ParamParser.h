#pragma once

#include "./StdHeader.h"
#include "./PPM_OS.h"
#include "./DataItem.h"
#include "./ColumnDef.h"



class CParamParser
{
public:
	CParamParser(CDataBaseDeal* const pDataBaseDeal_);

	~CParamParser(void);

	BOOL InitColumnResult(const std::string strColumnID, const std::string strTableID, const std::string strTableName);
	BOOL InitColumnResult(const std::string strColumnID, const std::vector<int> vec_TableID, std::vector<std::string> vec_TableName);
	BOOL InitColumnResult(const std::vector<int> vec_TableID, std::vector<std::string> vec_TableName);
	BOOL InitColumnResult(const std::vector<STRU_COLUMNID> vec_ColumnID, const std::vector<int> vec_TableID, std::vector<std::string> vec_TableName);

    std::map<int,std::vector<CColumnItem*>>& GetSearchColumn()
	{
		return m_SrcColumn;
	}

  std::vector<CColumnItem*>& GetDBColumn()
	{
    return m_DBColumnResult;
	}

private:
	void CheckDBColumnExitsColumnDef(int tbid, std::string tbname);

	std::map<int, std::vector< CColumnItem*>> CheckSearchColumn(const std::vector<STRU_COLUMNID> vec_id, const std::vector<int> vec_table);

	std::vector< CColumnItem*> CheckSearchColumn(const std::vector<STRU_COLUMNID> vec_id, const int tableID);

	std::map<int, std::vector< CColumnItem*>> CheckSearchColumn(const std::vector<int> vec_table);

	BOOL InitDBSearchColumns(void);
  BOOL IsSearchAllColumn(const std::vector<STRU_COLUMNID> vec_id);
	int Clear();

private:
  CDataBaseDeal* m_pDataBaseDeal_;

	std::vector<CColumnItem*> m_DBColumnResult;    //数据库数据获取结构
	std::map<int,std::vector<CColumnItem*>> m_SrcColumn;  //源数据结构

};
