#include "DBColumnDef.h"

CDBColumnDef::CDBColumnDef(void)
{
	m_FetchDataItem = (CDataItem*)CDataItem_DBColumn::Instance();
}

CDBColumnDef::~CDBColumnDef(void)
{
	for (auto p_mapCDataItem_DBColumn = m_mapDBColumn.begin(); 
		p_mapCDataItem_DBColumn != m_mapDBColumn.end(); ++p_mapCDataItem_DBColumn)
  {
    for (auto p_DBColumn = m_mapDBColumn[p_mapCDataItem_DBColumn->first].begin();
      p_DBColumn != m_mapDBColumn[p_mapCDataItem_DBColumn->first].end(); ++p_DBColumn)
    {
      CDataItem_DBColumn* pTemp = p_DBColumn->second;
      delete pTemp;
      p_DBColumn->second = nullptr;
    }
  }
  m_mapDBColumn.clear();
}

BOOL
CDBColumnDef::RowResultDeal(int nQType)
{
   return TRUE;
}


std::map<std::string, CDataItem_DBColumn*>
CDBColumnDef::GetDBColumns(std::string tbname,CDataBaseDeal* const m_pDataBaseDeal_, PPM_Server_Deal_Base* const m_pServerDeal_, BYTE* const m_pSendBuf_)
{
	std::map<std::string, CDataItem_DBColumn*> map_CDataItem_DBColumn;

  if (0 == tbname.length() )
  {
    return map_CDataItem_DBColumn;
  }

  //对于多线程操作，会存在同时查表结构的情况，导致内存泄漏。
  CStdAutoLock tm_autolock(&m_lock_);

  if (m_mapDBColumn.find(tbname) == m_mapDBColumn.end() || m_mapDBColumn[tbname].size() == 0)
  {
    m_tbname = tbname;
    Init(m_pDataBaseDeal_, m_pServerDeal_, m_pSendBuf_);

    if(NULL != m_pDataBaseDeal_)
    {
      #ifdef USE_MYSQL
      std::string sql = "select " + m_FetchDataItem->GetColumnSelSql() +
          " from INFORMATION_SCHEMA.COLUMNS where TABLE_NAME = '" + tbname + "' AND TABLE_SCHEMA = DATABASE();";
      #else
      std::string sql = "select " + m_FetchDataItem->GetColumnSelSql() +
          " from syscolumns  where id = (select id from sysobjects where name = '" + tbname + "')";
      #endif

      tDBConnInfo *conninfo = m_pDataBaseDeal_->GetConnectInfo();

		  memcpy(m_dbConnInfo.pchDBName, conninfo->pchDBName, sizeof(m_dbConnInfo.pchDBName)-1);
		  memcpy(m_dbConnInfo.pchDBServer, conninfo->pchDBServer, sizeof(m_dbConnInfo.pchDBServer)-1);
		  memcpy(m_dbConnInfo.pchUserName, conninfo->pchUserName, sizeof(m_dbConnInfo.pchUserName)-1);
		  memcpy(m_dbConnInfo.pchUserPassword, conninfo->pchPassword, sizeof(m_dbConnInfo.pchUserPassword)-1);
      m_dbConnInfo.nPort = conninfo->nPort;
			m_dbConnInfo.DisplaySql = 1;

      CStdSqlOpHelper sqlOpHelper(&m_dbConnInfo);

      int res = sqlOpHelper.ExecuteReader(sql.c_str());
      if (res == FALSE){
        return map_CDataItem_DBColumn;
      }

      while(sqlOpHelper.Read())
      {
        CDataItem_DBColumn* temp = (CDataItem_DBColumn*)m_FetchDataItem->FillData(sqlOpHelper);
        temp->tbname = m_tbname;

        m_mapDBColumn[m_tbname][temp->columnName] = temp;
      }

      map_CDataItem_DBColumn = m_mapDBColumn[m_tbname];
    }

  }
  return map_CDataItem_DBColumn;
}

BOOL
CDBColumnDef::IsColumnExistsInDB(const std::string tbname, const std::string columnName)
{
  if (m_mapDBColumn.find(tbname) == m_mapDBColumn.end())
  {
    return FALSE;
  }

  if (m_mapDBColumn[tbname].find(columnName) == m_mapDBColumn[tbname].end())
  {
    return FALSE;
  }

  return TRUE;
}

BOOL
CDBColumnDef::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
  return TRUE;
}
