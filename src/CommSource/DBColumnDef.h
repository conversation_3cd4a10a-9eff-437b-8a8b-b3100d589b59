#pragma once

#include "./InterfaceDef.h"
#include "SearchSock.h"

class CDBColumnDef : public ISearchDeal
{
public:
	static CDBColumnDef* Instance(void)
	{
		static CDBColumnDef *m_pCDBColumnDef = nullptr;

		if (m_pCDBColumnDef == nullptr)
		{
			m_pCDBColumnDef = new CDBColumnDef();
			if(nullptr == m_pCDBColumnDef){
				return nullptr;
			}
		}
		return m_pCDBColumnDef;
	}

private:
	CDBColumnDef();
public:
	~CDBColumnDef();


public:

	CDataItem* m_FetchDataItem;

  std::map<std::string, CDataItem_DBColumn*> GetDBColumns(std::string tbname, CDataBaseDeal* const m_pDataBaseDeal_,
                                                PPM_Server_Deal_Base* const m_pServerDeal_, BYTE* const m_pSendBuf_);
    
	BOOL IsColumnExistsInDB(const std::string tbname, const std::string columnName);
    
  virtual BOOL RowResultDeal(int nQType);

	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);

private:
  CStdLock m_lock_;

	std::string m_tbname;

	std::map<std::string, std::map<std::string, CDataItem_DBColumn*>> m_mapDBColumn;


};
