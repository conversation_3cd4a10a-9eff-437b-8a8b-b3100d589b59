#pragma once

#include "./PPM_Log_Msg.h"
#include "./StdHeader.h"


#define PPM_APPLOG(X) \
	do { \
	int __PPM_error = PPM_Log_Msg::last_error_adapter (); \
	PPM_Log_Msg *PPM___ = CDtdrvApp_Public_Var::Instance()->Instance_AppLog(); \
	PPM___->conditional_set (__FILE__, __LINE__, 0, __PPM_error); \
	PPM___->log_date X ; \
	} while (0)


class CDtdrvApp_Public_Var
{
private:
    CDtdrvApp_Public_Var()
		:m_applogfile_("App_log.log")
	{
		m_applogfile_.Initialize("App_log.log");
		CDtdrvApp_Public_Var::Instance_AppLog()->msg_ostream(m_applogfile_.GetFile(),&m_applogfile_);
	}

public:
	static CDtdrvApp_Public_Var *Instance (void){
		static CDtdrvApp_Public_Var *theInstance = 0;
		if (theInstance == 0)
		{
			theInstance = new CDtdrvApp_Public_Var;
		}
		return theInstance;
	};	

	~CDtdrvApp_Public_Var()
	{
		delete CDtdrvApp_Public_Var::Instance_AppLog();
	}

public:
	 PPM_Log_Msg *Instance_AppLog (void){
		static PPM_Log_Msg *app_log = 0;
		if (app_log == 0)
		{
			app_log = PPM_Log_Msg::instance_new();
		}
		return app_log;
	};	

private:
	///程序文件
	static PPM_Log_Msg* m_pAppLog;
	CStdLog m_applogfile_;

};
