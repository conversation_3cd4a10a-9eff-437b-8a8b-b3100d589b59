// EnumDef.h: interface for the CEnumDef class.
//
//////////////////////////////////////////////////////////////////////

#ifndef __ENUMDEF_H__
#define __ENUMDEF_H__

class CEnumDef  
{
public :
  enum TestType 
	{
		T_GSM_DT = 1,
		T_GSM_CQT = 2,
		T_AUTODT = 3,
		T_AUTODT_TA = 4,
		T_SCANTEST = 5,
		T_AUTOCQT = 6,
		T_CDMA_DT = 7,
		T_CDMA_CQT = 8,
		T_TDSCDMA_DT = 9,
		T_TDSCDMA_CQT = 10,
		T_WCDMA_DT = 11,
		T_WCDMA_CQT = 12,
		T_CDMA2000_DT = 13,
		T_CDMA2000_CQT = 14,
		T_CALLTRACE = 15,
		T_WLAN_DT = 16,
		T_WLAN_CQT = 17,
		T_ATU_CQT = 18,   //集团ATU陀螺仪打点测试
		T_LTE_DT = 19,
		T_LTE_CQT = 20,
		T_LTE_UEP = 21,
		T_LTE_FDD_DT = 22,
		T_LTE_FDD_CQT = 23,
		T_Signal_DT = 24,
		T_Signal_CQT = 25,
		T_NBIOT_DT = 26, //NBIOT数据测试类型
		T_NBIOT_CQT = 27,//NBIOT数据测试类型
		T_NR_TDD_DT = 28,//NR 数据测试类型
		T_NR_TDD_CQT = 29,//NR 数据测试类型
	};
	
  enum ServiceType
	{
		Ser_GSM_VOICE = 1,
		Ser_GPRS_DATA = 2,
		Ser_EDGE_DATA = 3,
		Ser_TDSCDMA_VOICE = 4,
		Ser_TDSCDMA_DATA = 5,
		Ser_CDMA_VOICE = 6,
		Ser_CDMA1X_DATA = 7,
		Ser_CDMA2000_VOICE = 8,
		Ser_CDMA2000_DATA = 9,
		Ser_WCDMA_VOICE = 10,
		Ser_WCDMA_DATA = 11,
		Ser_SCAN = 12,
		Ser_TDSCDMA_VIDEO = 13,
		Ser_WCDMA_VIDEO = 14,
		Ser_WCDMA_HSDPA = 15,
		Ser_CDMA2000_VIDEO = 16,
		Ser_TDSCDMA_IDLE = 17,
		Ser_TDSCDMA_HSDPA = 18,
		Ser_SCAN_TD = 19,
		Ser_SCAN_CDMA = 20,
		Ser_SCAN_WCDMA = 21,
		Ser_GSM_IDLE = 22,
		Ser_GSM_MOS = 23,
		Ser_GSM_UPLINK = 24,
		Ser_WCDMA_IDLE = 25,
		Ser_CDMA_IDLE = 26,
		Ser_TDSCDMA_HSUPA = 27,
		Ser_WCDMA_HSUPA = 28,
		Ser_GSM_CALLTRACE = 29,
		Ser_TD_CALLTRACE = 30,
		Ser_WLAN = 31,
		Ser_CDMA_MOS = 32,
		Ser_LTE_VOICE = 33,
		Ser_LTE_DATA = 34,
		Ser_LTE_SCAN_TOPN = 35,
		Ser_LTE_SCAN_CW = 36,
		Ser_LTE_SCAN_FREQSPECTRUM = 37,
		Ser_TD_SCAN_FREQSPECTRUM = 38,
		Ser_GSM_SCAN_FREQSPECTRUM = 39,
		Ser_CDMA2000_IDLE = 40,
		Ser_TDD_LTE_IDLE = 41,
		Ser_TDD_LTE_MULTI = 42,
		Ser_TDD_LTE_VOLTE = 43,
	  Ser_LTE_TDD_UEP = 44,
		Ser_LTE_FDD_VOICE = 45,
		Ser_LTE_FDD_DATA = 46,
		Ser_LTE_FDD_IDLE = 47,
		Ser_LTE_FDD_MULTI = 48,
		Ser_LTE_FDD_VOLTE = 49,
		SER_LTE_SIGNAL = 50,
		Ser_TDD_LTE_VIDEO_VOLTE = 51,
		Ser_LTE_FDD_VIDEO_VOLTE = 52,
		Ser_NBIOT_SCAN_TOPN = 55, //NB-IOT TOPN扫描模式
		Ser_NBIOT_DATA = 56, //NB-IOT数据
		Ser_NR_NSA_TDD_IDLE = 57, //NR非独立组网空闲模式
		Ser_NR_NSA_TDD_DATA = 58, //NR非独立组网数据业务模式
		Ser_NR_NSA_TDD_VOLTE = 59, //NR非独立组网4G VOLTE业务模式(5G文件中有进行lte volte业务)
		Ser_NR_SA_TDD_IDLE = 60, //NR独立组网空闲模式
		Ser_NR_SA_TDD_DATA = 61, //NR独立组网数据业务模式
		Ser_NR_SA_TDD_VOLTE = 62, //NR独立组网4G VOLTE业务模式(5G文件中有进行lte volte业务)
		Ser_NR_DM_TDD_IDLE = 63, //NR双模空闲模式 (DM（Dual Mode）双模：即工作在两个网络模式下)
		Ser_NR_DM_TDD_DATA = 64, //NR双模数据业务模式
		Ser_NR_DM_TDD_VOLTE = 65, //NR双模4G VOLTE业务模式
		Ser_NR_SA_TDD_EPSFB = 66, //NR独立组网语音(EPSFB)
		Ser_NR_DM_TDD_EPSFB = 67, //NR双模语音(EPSFB)
		Ser_NR_NSA_TDD_MULTI = 68, //NR非独立组网并发模式
		Ser_NR_SA_TDD_MULTI = 69, //NR独立组网并发模式
		Ser_NR_DM_TDD_MULTI = 70, //NR双模并发模式
		Ser_SCAN_NR = 71, //NR 扫频模式
		SER_SCAN_NRFREQ = 72, //NR 频谱分析模式
		Ser_NR_DM_TDD_VONR = 73, //NR独立组网语音(VONR)
	};

public:
	static bool IsNrServiceType(int servicetype)
	{
		int szNrServicetype[] = {
			Ser_NR_NSA_TDD_IDLE,
			Ser_NR_NSA_TDD_DATA,
			Ser_NR_NSA_TDD_VOLTE,
			Ser_NR_SA_TDD_IDLE,
			Ser_NR_SA_TDD_DATA,
			Ser_NR_SA_TDD_VOLTE,
			Ser_NR_DM_TDD_IDLE,
			Ser_NR_DM_TDD_DATA,
			Ser_NR_DM_TDD_VOLTE,
			Ser_NR_SA_TDD_EPSFB,
			Ser_NR_DM_TDD_EPSFB,
			Ser_NR_NSA_TDD_MULTI,
			Ser_NR_SA_TDD_MULTI,
			Ser_NR_DM_TDD_MULTI,
			Ser_NR_DM_TDD_VONR
		};

		int nsize = sizeof(szNrServicetype) / sizeof(szNrServicetype[0]);

		bool bIsFind = false;
		for (int i = 0; i < nsize; i++)
		{
			if (servicetype == szNrServicetype[i])
			{
				bIsFind = true;
				break;
			}
		}

		return bIsFind;
	};

	static bool IsLteFddServiceType(int servicetype)
	{
		int szLFServicetype[] = {
			Ser_LTE_FDD_VOICE,
			Ser_LTE_FDD_DATA,
			Ser_LTE_FDD_IDLE,
			Ser_LTE_FDD_MULTI,
			Ser_LTE_FDD_VOLTE,
			Ser_LTE_FDD_VIDEO_VOLTE
		};

		int nsize = sizeof(szLFServicetype) / sizeof(szLFServicetype[0]);

		bool bIsFind = false;
		for (int i = 0; i < nsize; i++)
		{
			if (servicetype == szLFServicetype[i])
			{
				bIsFind = true;
				break;
			}
		}

		return bIsFind;
	};

	static bool IsLteTddServiceType(int servicetype)
	{
		int szLTServicetype[] = {
			Ser_LTE_VOICE,
			Ser_LTE_DATA,
			Ser_TDD_LTE_IDLE,
			Ser_TDD_LTE_MULTI,
			Ser_TDD_LTE_VOLTE,
			Ser_TDD_LTE_VIDEO_VOLTE,
			Ser_NBIOT_DATA
		};

		int nsize = sizeof(szLTServicetype) / sizeof(szLTServicetype[0]);

		bool bIsFind = false;
		for (int i = 0; i < nsize; i++)
		{
			if (servicetype == szLTServicetype[i])
			{
				bIsFind = true;
				break;
			}
		}

		return bIsFind;
	};

};

#endif
