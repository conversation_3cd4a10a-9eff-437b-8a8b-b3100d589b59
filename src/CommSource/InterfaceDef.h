// InterfaceDef.h: interface for the CInterfaceDef class.
//
//////////////////////////////////////////////////////////////////////
#pragma once

#include "StdHeader.h"
#include "PPM_CONN_Actor.h"
#include "DataBaseDeal.h"


//#define SendBufMaxSize 8100 * 2
//保持跟PPM发送的包大小差不多
#define SEND_BUF_MAXSIZE NODE_BUF_SIZE - 100



//_interface ISearchDeal
class ISearchDeal : public CQueryRecord
{
public:
	virtual ~ISearchDeal(){};

public:
	PPM_Server_Deal_Base* m_pServerDeal_;
	BYTE* m_pSendBuf_;  //共用10000 Byte，作为发送给客户端的Buffer
	STRU_DBConnInfo m_dbConnInfo;

public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset) = 0;

	BOOL Init(CDataBaseDeal* const pDataBaseDeal_, PPM_Server_Deal_Base* const pServerDeal_, BYTE* const pSendBuf_)
	{
		if (pDataBaseDeal_ == NULL || pServerDeal_ == NULL || pSendBuf_ == NULL)
		{
			return FALSE;
		}

		this->m_pDataBaseDeal_ = pDataBaseDeal_;
		this->m_pServerDeal_ = pServerDeal_;
		this->m_pSendBuf_ = pSendBuf_;

		//初始化查询连接结构
		
		tDBConnInfo *conninfo = pDataBaseDeal_->GetConnectInfo();

		memcpy(m_dbConnInfo.pchDBName, conninfo->pchDBName, sizeof(m_dbConnInfo.pchDBName)-1);
		memcpy(m_dbConnInfo.pchDBServer, conninfo->pchDBServer, sizeof(m_dbConnInfo.pchDBServer)-1);
		memcpy(m_dbConnInfo.pchUserName, conninfo->pchUserName, sizeof(m_dbConnInfo.pchUserName)-1);
		memcpy(m_dbConnInfo.pchUserPassword, conninfo->pchPassword, sizeof(m_dbConnInfo.pchUserPassword)-1);
		m_dbConnInfo.nPort = conninfo->nPort;
		m_dbConnInfo.DisplaySql = 1;
		
		memset(this->m_pSendBuf_, 0, SEND_BUF_MAXSIZE);

		return TRUE;
	};
};


template < class T >
class Singleton
{	
public:
	static T* Instance ()
	{
		static T* m_pInstance = NULL;
		
		if (m_pInstance == 0)
		{
			m_pInstance = new T;
			if(NULL == m_pInstance){
				return NULL;
			}
		}
		return m_pInstance;
	}
	
};

/************************************************************************
类池，主要用于多线程时，将实例赋给各个线程                                                                   
************************************************************************/
template <class T>
class ClassPool
{
public:
	ClassPool(){};
	 ~ClassPool()
	 {
		 for (auto p_T = m_ClassPool.begin(); p_T != m_ClassPool.end(); ++p_T)
		 {
			 delete *p_T;
		 }
         m_ClassPool.clear();
	 }

private:
	std::list<const T*> m_ClassPool;
	STDLOCK m_ClassPoolLock;

public:
	 T* PopInstance(void)
	{
		m_ClassPoolLock.Lock();

		if (m_ClassPool.size() == 0)
		{
           m_ClassPool.push_back(new T);
		}
        
		T* classTemp =  (T*)(*m_ClassPool.begin());
		m_ClassPool.pop_front();

		m_ClassPoolLock.Unlock();

		return classTemp;
	}

	 void PushInstance(const T* const p_class)
	{
       m_ClassPoolLock.Lock();

	   m_ClassPool.push_back(p_class);
        
	   m_ClassPoolLock.Unlock();
	}

	void ClearPool(void)
	{
       m_ClassPoolLock.Lock();

	   for (auto p_iter = m_ClassPool.begin(); p_iter != m_ClassPool.end(); ++p_iter)
	   {
		   delete(*p_iter);
	   }
       m_ClassPool.clear();

	   m_ClassPoolLock.Unlock();
	}
 
};


