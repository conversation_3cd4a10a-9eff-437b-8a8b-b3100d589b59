#include "ParaResult.h"

STRU_ParaResult::STRU_ParaResult()
{
	iParaType = 0;
	pParaResult = nullptr;
	lSize = 0;
	buffSize = 0;
}

STRU_ParaResult::~STRU_ParaResult()
{
	delete pParaResult;
	pParaResult = nullptr;
}

STRU_ParaResult*
STRU_ParaResult::SwitchParaResult(int paraType)
{
	STRU_ParaResult* p_STRU_ParaResult = nullptr;

	Get_STRU_ParaResult_Part1(paraType, &p_STRU_ParaResult);
	Get_STRU_ParaResult_Part2(paraType, &p_STRU_ParaResult);

	if (!p_STRU_ParaResult)
	{
		return NULL;
	}
	return p_STRU_ParaResult;
}


void STRU_ParaResult::Get_STRU_ParaResult_Part1(int paraType, STRU_ParaResult** pResult)
{
	if (paraType == TYPE_CS_TINYINT)
	{
		*pResult = new CParaResult_TINYINT;
	}
	else if (paraType == TYPE_CS_SMALLINT)
	{
		*pResult = new CParaResult_SMALLINT;
	}
	else if (paraType == TYPE_CS_USMALLINT)
	{
		*pResult = new CParaResult_USMALLINT;
	}
	else if (paraType == TYPE_CS_FLOAT)
	{
		*pResult = new CParaResult_FLOAT;
	}
	else if (paraType == TYPE_CS_INT_FLOAT)
	{
		*pResult = new CParaResult_INT_FLOAT;
	}
	else if (paraType == TYPE_CS_TEXT)
	{
		*pResult = new CParaResult_TEXT;
	}
}

void STRU_ParaResult::Get_STRU_ParaResult_Part2(int paraType, STRU_ParaResult** pResult)
{
	if (paraType == TYPE_CS_INT)
	{
		*pResult = new CParaResult_INT;
	}
	else if (paraType == TYPE_CS_VARYBIN)
	{
		*pResult = new CParaResult_VARYBIN;
	}
	else if (paraType == TYPE_CS_VARYBIN_MERGE)
	{
		*pResult = new CParaResult_VARYBIN_MERGE;
	}
	else if (paraType == TYPE_CS_TEXT_MERGE)
	{
		*pResult = new CParaResult_TEXT_MERGE;
	}
	else if (paraType == TYPE_CS_UINT64)
	{
		*pResult = new CParaResult_UINT64;
	}
	else if (paraType == TYPE_CS_INT64)
	{
		*pResult = new CParaResult_INT64;
	}
}

CParaResult_TINYINT::CParaResult_TINYINT()
{
	iParaType = TYPE_CS_TINYINT;

	pParaResult = new BYTE[10];
	buffSize = 1;

	memset(pParaResult, 0, 10);
}

CParaResult_SMALLINT::CParaResult_SMALLINT()
{
	iParaType = TYPE_CS_SMALLINT;
	pParaResult = new BYTE[10];
	buffSize = 4;

	memset(pParaResult, 0, 10);
}

CParaResult_USMALLINT::CParaResult_USMALLINT()
{
	iParaType = TYPE_CS_USMALLINT;
	pParaResult = new BYTE[10];
	buffSize = 4;

	memset(pParaResult, 0, 10);
}


CParaResult_FLOAT::CParaResult_FLOAT()
{
	iParaType = TYPE_CS_FLOAT;
	pParaResult = new BYTE[10];
	buffSize = 8;

	memset(pParaResult, 0, 10);
}

CParaResult_INT_FLOAT::CParaResult_INT_FLOAT()
{
	iParaType = TYPE_CS_INT_FLOAT;
	pParaResult = new BYTE[10];
	buffSize = 4;

	memset(pParaResult, 0, 10);
}

CParaResult_TEXT::CParaResult_TEXT()
{
	iParaType = TYPE_CS_TEXT;
	pParaResult = new BYTE[MAX_RECORD_BUFFER_SIZE];   //最大获取8000
	buffSize = MAX_RECORD_BUFFER_SIZE;

	memset(pParaResult, 0, MAX_RECORD_BUFFER_SIZE);
}

CParaResult_INT::CParaResult_INT()
{
	iParaType = TYPE_CS_INT;
	pParaResult = new BYTE[10];
	buffSize = 4;

	memset(pParaResult, 0, 10);
}

CParaResult_VARYBIN::CParaResult_VARYBIN()
{
	iParaType = TYPE_CS_VARYBIN;
	pParaResult = new BYTE[MAX_RECORD_BUFFER_SIZE];   //最大获取8000
	buffSize = MAX_RECORD_BUFFER_SIZE;

	memset(pParaResult, 0, MAX_RECORD_BUFFER_SIZE);
}

CParaResult_VARYBIN_MERGE::CParaResult_VARYBIN_MERGE()
{
	iParaType = TYPE_CS_VARYBIN_MERGE;
	pParaResult = new BYTE[MAX_RECORD_BUFFER_SIZE];   //最大获取8000
	buffSize = MAX_RECORD_BUFFER_SIZE;

	memset(pParaResult, 0, MAX_RECORD_BUFFER_SIZE);
}

CParaResult_TEXT_MERGE::CParaResult_TEXT_MERGE()
{
	iParaType = TYPE_CS_TEXT_MERGE;
	pParaResult = new BYTE[MAX_RECORD_BUFFER_SIZE];   //最大获取8000
	buffSize = MAX_RECORD_BUFFER_SIZE;

	memset(pParaResult, 0, MAX_RECORD_BUFFER_SIZE);
}

CParaResult_UINT64::CParaResult_UINT64()
{
	iParaType = TYPE_CS_UINT64;
	pParaResult = new BYTE[10];
	buffSize = 8;

	memset(pParaResult, 0, 10);
}

CParaResult_INT64::CParaResult_INT64()
{
	iParaType = TYPE_CS_INT64;
	pParaResult = new BYTE[10];
	buffSize = 8;

	memset(pParaResult, 0, 10);
}




