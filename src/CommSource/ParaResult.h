#pragma once

#include "StdHeader.h"
#include "StdDataObject.h"

#define MAX_RECORD_BUFFER_SIZE   (16*1024)

class STRU_ParaResult
{
public:
	STRU_ParaResult();
	virtual ~STRU_ParaResult();

	static STRU_ParaResult* SwitchParaResult(int iParaType);
	virtual STRU_ParaResult* Copy() = 0;
	virtual void ClearBuffer() = 0;

public:
	int iParaType;
	BYTE* pParaResult;
	long lSize;
	int buffSize;

private:
	static void Get_STRU_ParaResult_Part1(int paraType, STRU_ParaResult** pResult);
	static void Get_STRU_ParaResult_Part2(int paraType, STRU_ParaResult** pResult);
};

class CParaResult_TINYINT : public STRU_ParaResult
{
public:
	CParaResult_TINYINT();

	virtual STRU_ParaResult* Copy()
	{
		STRU_ParaResult* paraResult = new CParaResult_TINYINT();
		paraResult->iParaType = this->iParaType;
		paraResult->buffSize = 1;
		memcpy(paraResult->pParaResult, this->pParaResult, paraResult->buffSize);
		return paraResult;
	}

	virtual void ClearBuffer()
	{
       memset(pParaResult, 0, 10);
	}

};

class CParaResult_SMALLINT : public STRU_ParaResult
{
public:
	CParaResult_SMALLINT();

	virtual STRU_ParaResult* Copy()
	{
		STRU_ParaResult* paraResult = new CParaResult_SMALLINT();
		paraResult->iParaType = this->iParaType;	 
		paraResult->buffSize = 2;
		memcpy(paraResult->pParaResult, this->pParaResult, paraResult->buffSize);
		return paraResult;
	}

	virtual void ClearBuffer()
	{
		memset(pParaResult, 0, 10);
	}
};

class CParaResult_USMALLINT : public STRU_ParaResult
{
public:
	CParaResult_USMALLINT();

	virtual STRU_ParaResult* Copy()
	{
		STRU_ParaResult* paraResult = new CParaResult_USMALLINT();
		paraResult->iParaType = this->iParaType;	 
		paraResult->buffSize = 2;
		memcpy(paraResult->pParaResult, this->pParaResult, paraResult->buffSize);
		return paraResult;
	}

	virtual void ClearBuffer()
	{
		memset(pParaResult, 0, 10);
	}
};

class CParaResult_FLOAT : public STRU_ParaResult
{
public:
	CParaResult_FLOAT();

	virtual STRU_ParaResult* Copy()
	{
		STRU_ParaResult* paraResult = new CParaResult_FLOAT();
		paraResult->iParaType = this->iParaType;
		paraResult->buffSize = 8;
		memcpy(paraResult->pParaResult, this->pParaResult, paraResult->buffSize);
		return paraResult;
	}

	virtual void ClearBuffer()
	{
		memset(pParaResult, 0, 10);
	}
};

class CParaResult_INT_FLOAT : public STRU_ParaResult
{
public:
	CParaResult_INT_FLOAT();

	virtual STRU_ParaResult* Copy()
	{
		STRU_ParaResult* paraResult = new CParaResult_INT_FLOAT();
		paraResult->iParaType = this->iParaType;
		paraResult->buffSize = 4;
		memcpy(paraResult->pParaResult, this->pParaResult, paraResult->buffSize);
		return paraResult;
	}

	virtual void ClearBuffer()
	{
		memset(pParaResult, 0, 10);
	}
};

class CParaResult_TEXT : public STRU_ParaResult
{
public:
	CParaResult_TEXT();

	virtual STRU_ParaResult* Copy()
	{
		STRU_ParaResult* paraResult = new CParaResult_TEXT();
		paraResult->iParaType = this->iParaType;
		paraResult->buffSize = MAX_RECORD_BUFFER_SIZE;
		memcpy(paraResult->pParaResult, this->pParaResult, paraResult->buffSize);
		return paraResult;
	}

	virtual void ClearBuffer()
	{
		memset(pParaResult, 0, MAX_RECORD_BUFFER_SIZE);
	}

};

class CParaResult_INT : public STRU_ParaResult
{
public:
	CParaResult_INT();

	virtual STRU_ParaResult* Copy()
	{
		STRU_ParaResult* paraResult = new CParaResult_INT();
		paraResult->iParaType = this->iParaType;
		paraResult->buffSize = 4;
		memcpy(paraResult->pParaResult, this->pParaResult, paraResult->buffSize);
		return paraResult;
	}

	virtual void ClearBuffer()
	{
		memset(pParaResult, 0, 10);
	}
};

class CParaResult_VARYBIN : public STRU_ParaResult
{
public:
	CParaResult_VARYBIN();

	virtual STRU_ParaResult* Copy()
	{
		STRU_ParaResult* paraResult = new CParaResult_VARYBIN();
		paraResult->iParaType = this->iParaType;
		paraResult->buffSize = MAX_RECORD_BUFFER_SIZE;
		memcpy(paraResult->pParaResult, this->pParaResult, paraResult->buffSize);
		return paraResult;
	}

	virtual void ClearBuffer()
	{
		memset(pParaResult, 0, MAX_RECORD_BUFFER_SIZE);
	}

};

class CParaResult_VARYBIN_MERGE : public STRU_ParaResult
{
public:
	CParaResult_VARYBIN_MERGE();

	virtual STRU_ParaResult* Copy()
	{
		STRU_ParaResult* paraResult = new CParaResult_VARYBIN_MERGE();
		paraResult->iParaType = this->iParaType;
		paraResult->buffSize = MAX_RECORD_BUFFER_SIZE;
		memcpy(paraResult->pParaResult, this->pParaResult, paraResult->buffSize);
		return paraResult;
	}

	virtual void ClearBuffer()
	{
		memset(pParaResult, 0, MAX_RECORD_BUFFER_SIZE);
	}
};

class CParaResult_TEXT_MERGE : public STRU_ParaResult
{
public:
	CParaResult_TEXT_MERGE();

	virtual STRU_ParaResult* Copy()
	{
		STRU_ParaResult* paraResult = new CParaResult_TEXT_MERGE();
		paraResult->iParaType = this->iParaType;
		paraResult->buffSize = MAX_RECORD_BUFFER_SIZE;
		memcpy(paraResult->pParaResult, this->pParaResult, paraResult->buffSize);
		return paraResult;
	}

	virtual void ClearBuffer()
	{
		memset(pParaResult, 0, MAX_RECORD_BUFFER_SIZE);
	}
};

class CParaResult_UINT64 : public STRU_ParaResult
{
public:
	CParaResult_UINT64();

	virtual STRU_ParaResult* Copy()
	{
		STRU_ParaResult* paraResult = new CParaResult_UINT64();
		paraResult->iParaType = this->iParaType;
		paraResult->buffSize = 8;
		memcpy(paraResult->pParaResult, this->pParaResult, paraResult->buffSize);
		return paraResult;
	}

	virtual void ClearBuffer()
	{
		memset(pParaResult, 0, 10);
	}
};

class CParaResult_INT64 : public STRU_ParaResult
{
public:
	CParaResult_INT64();

	virtual STRU_ParaResult* Copy()
	{
		STRU_ParaResult* paraResult = new CParaResult_INT64();
		paraResult->iParaType = this->iParaType;
		paraResult->buffSize = 8;
		memcpy(paraResult->pParaResult, this->pParaResult, paraResult->buffSize);
		return paraResult;
	}

	virtual void ClearBuffer()
	{
		memset(pParaResult, 0, 10);
	}
};




