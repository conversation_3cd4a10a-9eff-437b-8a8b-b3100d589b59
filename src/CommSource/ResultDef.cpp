#include "ResultDef.h"
#include "StdCommond.h"

CResultDef::CResultDef(void)
{
}

CResultDef::~CResultDef(void)
{
}

CResultDef *
CResultDef::Instance (void)
{
	static CResultDef *m_pResultDef = nullptr;

	if (m_pResultDef == 0)
	{
		m_pResultDef = new CResultDef;
		if(nullptr == m_pResultDef){
			return nullptr;
		}
	}
	return m_pResultDef;
}


BOOL
CResultDef::AddResultDef(int type, int datatype)
{
	m_mapResultDef[type].push_back(datatype);
	return TRUE;
}

std::vector<STRU_ParaResult*> 
CResultDef::GetVecResult(const int& resulttype)
{
   std::vector<STRU_ParaResult*> vec_ParaResult;

   if (m_mapResultDef.find(resulttype) == m_mapResultDef.end())
   {
	   return  vec_ParaResult;
   }

   std::vector<int>::const_iterator p_vec = m_mapResultDef[resulttype].begin();
   for (; p_vec != m_mapResultDef[resulttype].end(); ++p_vec)
   {
      STRU_ParaResult *paraResult;
			paraResult = STRU_ParaResult::SwitchParaResult(*p_vec);
			if (nullptr == paraResult)
			{
				vec_ParaResult.clear();
				return vec_ParaResult;
			}
			vec_ParaResult.push_back(paraResult);   
   }
   return vec_ParaResult;
}

std::vector<STRU_ParaResult*> 
CResultDef::GetVecResult(const std::string strResultType)
{
	std::vector<STRU_ParaResult*> vec_ParaResult;

	if (0 == strResultType.length())
	{
		return  vec_ParaResult;
	}

	std::vector<std::string> vec_ResultType = CStdCommond::str_slipt(strResultType, ",");
	for (std::vector<std::string>::size_type i = 0; i < vec_ResultType.size(); ++i)
	{
		STRU_ParaResult* p_STRU_ParaResult = STRU_ParaResult::SwitchParaResult(atoi((vec_ResultType[i]).c_str()));
		if (nullptr == p_STRU_ParaResult)
		{
		   ClearVecResult(vec_ParaResult);
		   return vec_ParaResult;
		}
    vec_ParaResult.push_back(p_STRU_ParaResult);
	}

	return vec_ParaResult;
}

BOOL
CResultDef::ClearVecResult(std::vector<STRU_ParaResult*>& vec_ParaResult)
{
	if (0 == vec_ParaResult.size())
	{
		return  TRUE;
	}

	std::vector<STRU_ParaResult*>::iterator p_vec = vec_ParaResult.begin();
	for (; p_vec != vec_ParaResult.end(); ++p_vec)
	{
      delete(*p_vec);
	}
	vec_ParaResult.clear();

	return TRUE;
}

std::size_t
CResultDef::GetVecResultSize(const int& resulttype) 
{
	if (m_mapResultDef.find(resulttype) == m_mapResultDef.end())
	{
		return  -1;
	}
	return  m_mapResultDef[resulttype].size();
}

BOOL
CResultDef::AddResultDef(const std::string strLine)
{
	if (strLine.length() <= 0)
	{
		return FALSE;
	}

	std::vector<std::string> vec_data = CStdCommond::str_slipt(strLine, "\t");
	if (vec_data.size() != 5)
	{
		return FALSE;
	}

	for (size_t i = 0; i < vec_data.size(); i++)
	{
		vec_data[i] = CStdCommond::str_trim(vec_data[i], ' ');
	}

	m_mapResultDef[atoi((vec_data[0]).c_str())].push_back(atoi((vec_data[3]).c_str()));

	return TRUE;
}

BOOL
CResultDef::AddResultDef(std::vector<std::string> vec_data)
{
	m_mapResultDef[atoi((vec_data[0]).c_str())].push_back(atoi((vec_data[3]).c_str()));

	return TRUE;
}

BOOL
CResultDef::IsResultDefCanUsed(void)
{
	if (0 == m_mapResultDef.size())
	{
		return FALSE;
	}
	return TRUE;
}





