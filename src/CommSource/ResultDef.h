#pragma once

#include "StdHeader.h"
#include "DataBaseDeal.h"
#include "ParaResult.h"

class CResultDef 
{
public:
	enum ResultDef_ID
	{
    SEARCHTYPE_AUTHUSERCHEK       =    900,	//authuserchek
    SEARCHTYPE_AUTHUSERFUNCGET    =    901, //authuserfuncget
		SEARCHTYPE_AUTHUSERCHEK2      =    902, //authuserfuncget

    USERMNGTYPE_FUNC              =    910,
    USERMNGTYPE_SUBFUNC           =    911,
    USERMNGTYPE_ROLE              =    912,
    USERMNGTYPE_ROLE_FUNC         =    913,
    USERMNGTYPE_ROLE_USER         =    914,

		/////////////////////////////////////////////////////////////////////////////////////////

    RESULT_SINGLE					=		800,

    CONFIGTYPE_VERSION				=		1500,
    CONFIGTYPE_UPDATE_DESC			=		1501,
    CONFIGTYPE_ROLE					=		1502,
    CONFIGTYPE_ROLE_FUNC		=    		1503,

		SEARCHTYPE_LOGSEARCH		=		1000,
		SEARCHTYPE_LOGSEARCH_MSG_DETAIL		=	1001,
		SEARCHTYPE_LOGSEARCH_MSG_DEPTH	=		1002,
		SEARCHTYPE_LOGSEARCH_EVENT		=		1003,
		SEARCHTYPE_LOGSEARCH_SAMPLE_SUMMARY	=	1004,
		SEARCHTYPE_LOGSEARCH_SAMPLE_NORMAL	=	1005,
		SEARCHTYPE_LOGSEARCH_SAMPLE_ABNORMAL	=1006,
		SEARCHTYPE_LOGSEARCH_SCAN_SUMMARY	=	1007,
		SEARCHTYPE_LOGSEARCH_SCAN_DETAIL	=	1008,
		SEARCHTYPE_LOGSEARCH_SCAN_DEPTH		=	1009,
		SEARCHTYPE_GRID_IDLE			=		1010,
		SEARCHTYPE_GRID_DEDICATED	=			1011,
		SEARCHTYPE_GRID_GPRS 			=		1012,
		SEARCHTYPE_SCAN_KPI_LOG			=		1013,
		SEARCHTYPE_SCAN_KPI_GRID		=		1014,
		SEARCHTYPE_SCAN_KPI_CELL		=		1015,
		SEARCHTYPE_SCAN_KPI_AREA		=		1016,
		SEARCHTYPE_SCAN_GIS				=		1017,
		SEARCHTYPE_SCAN_CELL		=			1018,

		SEARCHTYPE_HEALTH_GSM		=			1019,
		SEARCHTYPE_HEALTH_SCAN		=			1020	,
		SEARCHTYPE_ROAD_RATE			=		1021,	
		SEARCHTYPE_PROJECT_MAXTIME		=		1022,	

		SEARCHTYPE_TDSCDMA_SAMPLE_SUMMARY	=	1023,
		SEARCHTYPE_TDSCDMA_SAMPLE_NORMAL	=	1024,

		SEARCHTYPE_TDSCDMA_GRID_AMR		 =       1025,
		SEARCHTYPE_TDSCDMA_GRID_PS		 =       1026,
		SEARCHTYPE_TDSCDMA_GRID_VP		 =       1027,
		SEARCHTYPE_BLACKBLOCK_INFO      =        1028,
		SEARCHTYPE_BLACKBLOCK_EVENT      =       1029,
		SEARCHTYPE_COMMUNITY_INFO        =       1030,
		SEARCHTYPE_COMMUNITY_BUILIDNG      =     1031,
		SEARCHTYPE_COMMUNITY_TESTPOINT     =     1032,
		SEARCHTYPE_COMMUNITY_BUILDING_POS   =    1033,
		SEARCHTYPE_ROADMONIT_INFO        =       1034,
		SEARCHTYPE_ROADMONIT_EVENT      =        1035,
		SEARCHTYPE_COMPBENCH_DATE        =       1036,
		SEARCHTYPE_COMPBENCH_UNIT_INFO   =       1037,

		SEARCHTYPE_SCANJAM_TOTAL		=		1038 ,     //resultdef.xml 缺省 需要检查
		SEARCHTYPE_SCANJAM_PAIR			=		1039  ,    //resultdef.xml 缺省 需要检查

		SEARCHTYPE_COMPLAIN_BLOCK      =         1040,
		SEARCHTYPE_COMPLAIN_ITEM      =          1041,
		SEARCHTYPE_ANTENNA_REV        =          1042,
		SEARCHTYPE_COMPLAIN_REPORT		=		1043,

		SEARCHTYPE_CDMA_SAMPLE_SUMMARY		=    1044,
		SEARCHTYPE_CDMA_SAMPLE_NORMAL     =      1045,
		SEARCHTYPE_CDMA_AREA_COVER_GRID    =     1046,

		SEARCHTYPE_COMPLAIN_BLOCK_GRID       =   1048,
		SEARCHTYPE_COMPLAIN_BLOCK_GRID_LOG   =   1049,

		SEARCHTYPE_CLUSTER_CLIQUE_INFO		=	1047,
		SEARCHTYPE_CLUSTER_LOG            =      1050,
		SEARCHTYPE_CLUSTER_INFO            =     1051,
		SEARCHTYPE_CLUSTER_ADJFREQ_INFO    =     1052,

		SEARCHTYPE_TDSCDMA_NETRATE     =         1053,

		SEARCHTYPE_CLUSTER_CSL        =          1054,
		SEARCHTYPE_CLUSTER_CSL_INFO    =         1055,

		SEARCHTYPE_DOCUMENT_INFO       =         1056,

		SEARCHTYPE_COMPLAIN_BLOCK_GRID_RECENT =  1057,

		SEARCHTYPE_CELL_PROPERTY_INFO   =        1058,

		SEARCHTYPE_CLUSTER_AREA_FREQNUMAVG    =  1059,
		SEARCHTYPE_CLUSTER_AREA_FREQVARIANCE  =  1060,
		SEARCHTYPE_CLUSTER_AREA_WEAKCOVCELLNUM = 1061,
		SEARCHTYPE_CLUSTER_AREA_CARRIERUSEAVG  = 1062,
		SEARCHTYPE_CLUSTER_Cell_WeakCov      =   1063,
		SEARCHTYPE_CLUSTER_Cell_WirelessRate  =  1064,
		SEARCHTYPE_CLUSTER_AREA_INJECTRATE    =  1065,
		SEARCHTYPE_CLUSTER_CELL_AVAILRATE     =  1066,
		SEARCHTYPE_CLUSTER_COFREQ_INFO       =   1067,
		SEARCHTYPE_CLUSTER_CELL_GRID         =   1068,
		SEARCHTYPE_ES_AREA_EVENT_INFO        =   1069,
		SEARCHTYPE_OVERCOVER_GRID_INFO       =   1070,

		SEARCHTYPE_LOGSEARCH_INFO            =   1071,

		SEARCHTYPE_WCDMA_SAMPLE_SUMMARY      =   1072,
		SEARCHTYPE_WCDMA_SAMPLE_NORMAL       =   1073,

		//====================NEW============================
		SEARCHTYPE_NEW_LOGSEARCH_EVENT		=	1074,
		SEARCHTYPE_NEW_LOGSEARCH_MSG_DETAIL	=	1075,
		SEARCHTYPE_NEW_LOGSEARCH_MSG_DEPTH	=	1076,
		//====================NEW============================

		SEARCHTYPE_TABLE_INFO              =     1077,

		SEARCHTYPE_WCDMA_GRID_AMR          =     1078,
		SEARCHTYPE_WCDMA_GRID_VP            =    1079,
		SEARCHTYPE_WCDMA_GRID_PS            =    1080,
		SEARCHTYPE_WCDMA_GRID_PSHS         =     1081,

		SEARCHTYPE_LOGSEARCH_INFO_MORE       =   1082,

		SEARCHTYPE_DOCUMENT_All_INFO        =    1083,

		SEARCHTYPE_ES_EVENT_TIME          =      1084,

		SEARCHTYPE_CELLSIMU_GRID           =     1085,
		SEARCHTYPE_CELLSIMU_GRID_INFO      =     1086,

		SEARCHTYPE_REVIEW_DATA_SAMPLE_SUMMARY =   1087,
		SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL   =  1088,

		SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_SUMMARY =  1089,
		SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL  =  1090,

		SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_SUMMARY  = 1091,
		SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL  =  1092,

		SEARCHTYPE_CELL_COVER_DISTANCE     =     1093,

		SEARCHTYPE_ES_EVENT_REPORT         =     1094,
		SEARCHTYPE_ES_EVENT_AHEAD_TIME     =     1095,
		SEARCHTYPE_ES_EVENT_MSG             =    1096,

		SEARCHTYPE_CELLSIMU_CELL_GRID       =    1097,
		SEARCHTYPE_CELL_OUTSERVICE_INFO      =   1098,

		SEARCHTYPE_ES_GRID_CELL             =    1099,//

		/////////////////////////////////////////////////////////////////////////////////////////////////////
		SEARCHTYPE_FILE_LIST		=			2000,
		SEARCHTYPE_TB_LIST			=			2001,
		SEARCHTYPE_FILE_LIST_SAMPLE		=		2002	,
		DIYSEARCHTYPE_FILE_LIST          =       2003,
		SEARCHTYPE_FILE_LIST_SAMPLE2		=	2004,
		SEARCHTYPE_SCAN_TD_CELL             =   2005,

		//statistic/////////////////////////////////////////////////////////////////////////////////////////
		STATTYPE_NOGIS_V_GSM_IDLE			=	1110,
		STATTYPE_NOGIS_V_GSM_DEDICATED	=		1111,
		STATTYPE_AREA_DATA				   = 	1112,
		STATTYPE_NOGIS_V_GSM_EVENT		=		1113,
		STATTYPE_NOGIS_D_GPRS_EVENT		=		1114  ,

		STATTYPE_GIS_V_GSM_IDLE			=		1115,
		STATTYPE_GIS_V_GSM_DEDICATED	=		1116,
		STATTYPE_GIS_D_GPRS				=		1117,
		STATTYPE_GIS_V_GSM_EVENT		=		1118,
		STATTYPE_GIS_D_GPRS_EVENT		=		1119,

		STATTYPE_CDMA_KPI_PARA			=		1120,
		STATTYPE_KPI_LOG_INFO				=	1121,

		STATTYPE_NOGIS_TDSCDMA_AMR     =         1122,
		STATTYPE_NOGIS_TDSCDMA_PS     =          1123   , 
		STATTYPE_NOGIS_TDSCDMA_VP     =          1124,
		STATTYPE_GIS_TDSCDMA_AMR       =         1125,
		STATTYPE_GIS_TDSCDMA_PS        =         1126  ,  
		STATTYPE_GIS_TDSCDMA_VP        =         1127,
		STATTYPE_TDSCDMA_EVENT         =         1128,

		STATTYPE_GIS_WCDMA_AMR		=			1129,
		STATTYPE_GIS_WCDMA_VP			=		1130,
		STATTYPE_GIS_WCDMA_PS			=		1131    ,
		STATTYPE_GIS_WCDMA_PSHS		=			1132,

		STATTYPE_GIS_D_GPRS_NEW		=			1133,

		STATTYPE_NOGIS_EVENT			=		1134,

		STATTYPE_KPI_LOG_LN       =              1135,//

		STATTYPE_STAT_DATA_IMAGE     =           1136,//

		STATTYPE_KPI_LOG_LN_TD_Amr     =         1137,//
		STATTYPE_KPI_LOG_LN_TD_Ps       =        1138,//
		STATTYPE_KPI_LOG_LN_GSMV          =      1139,//

		STATTYPE_CELL_PROPERTY_CELLINFO     =    1140,//
		STATTYPE_CELL_PROPERTY_GPRSINFO     =    1141,//
		STATTYPE_CELL_PROPERTY_TRAFFICINFO   =   1142,//

		//config////////////////////////////////////////////////////////////////////////////////////////////
		CONFIGTYPE_BTS				=			1200,
		CONFIGTYPE_CELL				=			1201,
		CONFIGTYPE_PD					=		1202,
		CONFIGTYPE_NBCELL			=			1203,
		CONFIGTYPE_REPEATER				=		1205,

		CONFIGTYPE_DISTRICT_COMMUNITY		=	1204,
		CONFIGTYPE_SCAN				=			1206,
		CONFIGTYPE_AREA			=				1207,

		CONFIGTYPE_VILLAGE_Nature        =       1208,
		CONFIGTYPE_VILLAGE_Admin         =       1209,
		CONFIGTYPE_AREA_LIST             =       1210,
		CONFIGTYPE_AREA_ADD              =       1211,

		CONFIGTYPE_TDSCDMA_BTS            =      1212,
		CONFIGTYPE_TDSCDMA_CELL          =       1213,
		CONFIGTYPE_TDSCDMA_PD           =        1214,
		CONFIGTYPE_TDSCDMA_NBCELL      =         1216  ,

		CONFIGTYPE_CITY                =         1215,

		CONFIGTYPE_WCDMA_NODEB			=		1217,
		CONFIGTYPE_WCDMA_CELL			=		1218,
		CONFIGTYPE_WCDMA_PD				=		1219,
		CONFIGTYPE_WCDMA_NBCELL			=		1220,
		CONFIGTYPE_PROJECT_EXTEND    =           1221,

		CONFIGTYPE_KPI_TABLE      =              1222,

		CONFIGTYPE_STATIC_INFO		=			1300,
		TEMPTYPE_INT              =              1301,

		CONFIGTYPE_COVER_REGION_INFO     =       1302,
		CONFIGTYPE_COVER_REGION_POINT    =       1303,
		CONFIGTYPE_LTE_BTS               =       1304,
		CONFIGTYPE_LTE_CELL              =       1305,
		CONFIGTYPE_LTE_PD                =       1306,
		CONFIGTYPE_LTE_NBCELL            =       1307,

		CONFIGTYPE_NR_BTS               =       1308,
		CONFIGTYPE_NR_CELL              =       1309,
		CONFIGTYPE_NR_PD                =       1310,
		CONFIGTYPE_NR_NBCELL            =       1311,

		CONFIG_SERVER_LIST				 =       2006,

		//add by yht for filedownload////////////////////////////////////////////////////////////

		CONFIGTYPE_LOG_DOWNLOAD     =            1400,

		//Search expend//////////////////////////////////////////////////////////////////////////
		SEARCHTYPE_CQTMNG_PLACE_INFO      =      1600,//
		SEARCHTYPE_CQTMNG_PLACESUB_INFO   =      1601,//
		SEARCHTYPE_CQTMNG_CDMA_INFO      =       1602,//
		SEARCHTYPE_CQTMNG_GSM_INFO       =       1603,//
		SEARCHTYPE_CQTMNG_LOG_INFO       =       1604,//
		SEARCHTYPE_CELL_ALARM_INFO       =       1605,//
		SEARCHTYPE_CELL_PROPERTY_LN_INFO   =     1606,//
		SEARCHTYPE_ExcelBlock_INFO         =     1607,//
		SEARCHTYPE_ExcelBlock_Item_INFO    =     1608,//
		SEARCHTYPE_ExcelBlock_Local_Item_INFO =  1609,//

//NEW ADD
		SEARCHTYPE_REVIEW_CELLTRACE       =      1610,
		SEARCHTYPE_REVIEW_SCAN_WCDMA      =      1611,
		SEARCHTYPE_SITESIMU_SAMPLE		=		 1612,
		SEARCHTYPE_CELL_TRAFFIC			=	     1613,
		SEARCHTYPE_REVIEW_ULGSM_NORMAL    =  	 1614,
    SEARCHTYPE_NEWBLOCK_ID             =     1615,
    SEARCHTYPE_NEWBLOCK_INFO        =        1616,
    SEARCHTYPE_TDSCDMA_NETRATE_IMAGE  =      1617,
		SEARCHTYPE_CQTMNG_TDAMR_INFO      =       1618,//

		SEARCHTYPE_DBINFO =  14,  //dbinfo
		SEARCHTYPE_CFGFRAME  = 19,  //cfgframe
		SEARCHTYPE_REGION_RECT =  31 , //region_rect
		SEARCHTYPE_REGION_POLY = 32,  //region_rect
		SEARCHTYPE_REGION_STREET = 33  //region_rect
	};



public:
	std::vector<STRU_ParaResult*> GetVecResult(const int& resulttype);
	std::vector<STRU_ParaResult*> GetVecResult(const std::string strResultType);
	BOOL ClearVecResult(std::vector<STRU_ParaResult*>& vec_ParaResult);
	std::size_t GetVecResultSize(const int& resulttype);
	BOOL AddResultDef(int type, int datatype);
  BOOL AddResultDef(const std::string strLine);
	BOOL AddResultDef(std::vector<std::string> vec_data);
	BOOL IsResultDefCanUsed(void);

public:	
	~CResultDef(void);
   
private:
	CResultDef(void);
	std::map<const int, std::vector<int>> m_mapResultDef;

public:
	static CResultDef* Instance (void);



};
