// SearchHead.h: interface for the CSearchHead class.
//
//////////////////////////////////////////////////////////////////////

#ifndef __SEARCHHEAD_H__
#define __SEARCHHEAD_H__

#pragma once

#if _Debug
#define CHECLFALSE(X)  if(FALSE == X) {PPM_DEBUG((LM_ERROR, "ReturnFalse : %s \n", #X));return FALSE;}
#define CHECLNULL(X) if(NULL == X) {PPM_DEBUG((LM_ERROR, "ReturnNull : %s \n", #X));return FALSE;}
#else
#define CHECLFALSE(X)  if(FALSE == X) \
{\
	return FALSE;\
}
#define CHECLNULL(X) if(NULL == X) \
{\
	return FALSE;\
}
#endif

/************************************************************************
Mark:
    对于struct，class，不能强制进行清零操作。
	如：memset(&STRU_TB_LIST, 0, sizeof(STRU_TB_LIST));
	清零操作执行后会破坏内存的结构，到时析构存在问题。如string类型会析构出现问题
************************************************************************/

struct STRU_TB_LIST
{
	int fileid;
	int testtype;
	std::string strlog;
	std::string strmsg;
	std::string strevent;
	std::string strsample;
	std::string strsample2;
	std::string strsuffix;

	STRU_TB_LIST()
	{
		fileid = 0;
		testtype = 0;
		strlog = "";
		strmsg = "";
		strevent = "";
		strsample= "";
		strsample2 = "";
		strsuffix = "";
	}
};

struct STRU_TB_INFO
{
	int fileid;
	int nettype;
	int servicetype;
	std::string strmsg;
	std::string strevent;
	std::string strsample;
	std::string strsample2;
	std::string strsuffix;

	STRU_TB_INFO()
	{
		fileid = 0;
		nettype = 0;
		servicetype = 0;
		strmsg = "";
		strevent = "";
		strsample = "";
		strsample2 = "";
		strsuffix = "";
	}
};

struct STRU_FILE_LIST
{
	int testtype;
	std::string strlog;
	std::string strtb;

  STRU_FILE_LIST()
	{
		testtype = 0;
		strlog = "";
		strtb = "";
	}

	bool operator < (STRU_FILE_LIST const& _A) const
	{
		//这个函数指定排序策略
		if(testtype < _A.testtype) 
		{
			return true;
		}
		if(testtype == _A.testtype) 
		{
			if(strtb.compare(_A.strtb) < 0)
			{
				return true;
			}
			if(strtb.compare(_A.strtb) == 0)
			{
				if(strlog.compare(_A.strlog) < 0)
				{
					return true;
				}
				else
				{
					return false;
				}
			}
			return false;
		}
		return false;
	}

};

struct STRU_FILE_LIST2
{
	int testtype;
	int servicetype;
	std::string strlog;
	std::string strtb;

	STRU_FILE_LIST2()
	{
		testtype = 0;
		servicetype = 0;
		strlog = "";
		strtb = "";
	}

	bool operator < (STRU_FILE_LIST2 const& _A) const
	{
		//这个函数指定排序策略
		//testtype
		if (testtype > _A.testtype)
		{
			return false;
		}
		if (testtype < _A.testtype) 
		{
			return true;
		}
        //servicetype
		if (servicetype > _A.servicetype)
		{
			return false;
		}
		if (servicetype < _A.servicetype)
		{
			return true;
		}
        //strtb
		if(strtb.compare(_A.strtb) > 0)
		{
			return false;
		}
		if(strtb.compare(_A.strtb) < 0)
		{
			return true;
		}
        //strlog
		if(strlog.compare(_A.strlog) < 0)
		{
			return true;
		}
		else
		{
			return false;
		}
	}

};

struct STRU_TBNAME{
	int testtype;
	std::string tbname;
	
	bool operator < (STRU_TBNAME const& _A) const
	{
		//这个函数指定排序策略
		if(testtype < _A.testtype) 
		{
			return true;
		}
		if(testtype == _A.testtype) 
		{
			if(tbname.compare(_A.tbname) < 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		return false;
	}
};

struct STRU_SEARCH_TIMESPAN{
	void ConvertToNetSeq()
	{
		istime = MAKEINT_NETSEQ1(istime);
		ietime = MAKEINT_NETSEQ1(ietime);
	}
    STRU_SEARCH_TIMESPAN()
	{
       istime = -1;
       ietime = -1;
	}

	int istime;
	int ietime;
};
struct STRU_SEARCH_BASIC{
	void ConvertToNetSeq()
	{
		istime = MAKEINT_NETSEQ1(istime);
		ietime = MAKEINT_NETSEQ1(ietime);
	}
	int istime;
	int ietime;
	int iprojecttype;
	char szprojecttype[255];
	int idevicetype;
	char szdevicetype[255];
	int iservicetype;
	char szservicetype[255];
	int itesttype;
	char sztesttype[255];
	int icarriertype;
	char szcarriertype[255];
	char szfilename[255];
	int ieventlist;
	char szeventlist[255];
};

struct STRU_SEARCH_AREA{
	void ConvertToNetSeq()
	{
		itllongitude = MAKEINT_NETSEQ1(itllongitude);
		itllatitude = MAKEINT_NETSEQ1(itllatitude);
		ibrlongitude = MAKEINT_NETSEQ1(ibrlongitude);
		ibrlatitude = MAKEINT_NETSEQ1(ibrlatitude);
	}
	STRU_SEARCH_AREA()
	{
       itllongitude = 0;
	   itllatitude = 0;
       ibrlongitude = 0;
	   ibrlatitude = 0;
	}

	int itllongitude;
	int itllatitude;
	int ibrlongitude;
	int ibrlatitude;
};

struct STRU_LOG_REVIEW{

	void ConvertToNetSeq()
	{
		ifileID = MAKEINT_NETSEQ1(ifileID);
		istime = MAKEINT_NETSEQ1(istime);
		ietime = MAKEINT_NETSEQ1(ietime);
	}

    STRU_LOG_REVIEW()
	{
       iprojecttype = 0;
       itesttype = 0;
       ifileID = 0;
       ireviewtype = 0;
       istime = 0;
       ietime = 0;
	}

	int iprojecttype;
	int itesttype;
	int ifileID;
	int ireviewtype;
	int istime;
	int ietime;



};

struct STRU_SEARCH_CELL{
	void ConvertToNetSeq()
	{
		iLAC = MAKEINT_NETSEQ1(iLAC);
		wRAC = MAKEWORD_NETSEQ1(wRAC);
		iCI = MAKEINT_NETSEQ1(iCI);
	}

    STRU_SEARCH_CELL()
	{
        iLAC = 0;
        wRAC = 0;
        iCI = 0;
	}

	int iLAC;
	WORD wRAC;
	int iCI;
};

struct STRU_SEARCH_LOG_KPI{
	void ConvertToNetSeq()
	{
		iprojecttype = MAKEINT_NETSEQ1(iprojecttype);
		itesttype = MAKEINT_NETSEQ1(itesttype);
		ifileID = MAKEINT_NETSEQ1(ifileID);
	}

	STRU_SEARCH_LOG_KPI()
	{
        iprojecttype = 0;
        itesttype = 0;
        ifileID = 0;
	}

	int iprojecttype;
	int itesttype;
	int ifileID;
};


struct STRU_TIME_SPAN{
	void ConvertToNetSeq()
	{
		istime = MAKEINT_NETSEQ1(istime);
		ietime = MAKEINT_NETSEQ1(ietime);
	}

	STRU_TIME_SPAN()
	{
       istime = 0;
       ietime = 0;
	}

	int istime;
	int ietime;
};

struct STRU_STAT_GIS{
	void ConvertToNetSeq()
	{
		itllongitude = MAKEINT_NETSEQ1(itllongitude);
		itllatitude = MAKEINT_NETSEQ1(itllatitude);
		ibrlongitude = MAKEINT_NETSEQ1(ibrlongitude);
		ibrlatitude = MAKEINT_NETSEQ1(ibrlatitude);
		iprojecttype = MAKEINT_NETSEQ1(iprojecttype);
		itimetype = MAKEINT_NETSEQ1(itimetype);
		iyear = MAKEINT_NETSEQ1(iyear);
		ibatch = MAKEINT_NETSEQ1(ibatch); 
	}
	int itllongitude;
	int itllatitude;
	int ibrlongitude;
	int ibrlatitude;
	int iprojecttype;
	int itimetype;
	int iyear;
	int ibatch;
	char sztimetype[225];
};

struct STRU_STAT_NOGIS{
	void ConvertToNetSeq()
	{
		iareatypeid = MAKEINT_NETSEQ1(iareatypeid);
		iprojecttype = MAKEINT_NETSEQ1(iprojecttype);
	}
	int iareatypeid;
	int iprojecttype;
	char szarea[512];
	char sztimetype[255];
};

struct STRU_USER_INFO{
	void ConvertToNetSeq()
	{
		iuserid = MAKEINT_NETSEQ1(iuserid);
	}
	int iuserid;
	char szusername[255];
	char szlogon_code[255];
	char szlogon_pwd[255];
	char szphone[255];
	char szcomment[255];
};


///鉴权信息
struct STRU_AUTHINFO
{
	BYTE bAuthType;//鉴权类型
	char pchLogonName[255]; //鉴权登陆名
	char pchPassword[1024]; //鉴权登陆密码
	int iUserID;  //用户ID
	char pchRandomStr[255]; //随机数
	char pchResult[255]; //MD5转换后的字符串
	int iDBID; //所属数据库ID
	int iDBIDSub;//省用户子ID
	int iCityID;//对应的图层ID
};

//规则矩形
struct STRU_Region_Rect
{
	int itllong;
	int itllat;
	int ibrlong;
	int ibrlai;
};
//点
struct STRU_Region_Point
{
	int ilong;
	int ilat;
};

//不规则区域
struct STRU_RegionPloy
{
	STRU_RegionPloy(){
		id = 0;
		strname = "";
		vec_point.clear();
	}

	int id;
	std::string strname;
	std::vector<STRU_Region_Point> vec_point;
};

//街道区域，包括多条街道
struct STRU_RegionStreat
{
	STRU_RegionStreat(){
		id = 0;
		strname = "";
		igrpid = 0;
		vec_poly.clear();
	}

	int id;
	std::string strname;
	int igrpid;
	std::vector<STRU_RegionPloy> vec_poly;
};




#endif
