// SearchSock.cpp: implementation of the CSearchSock class.
//
//////////////////////////////////////////////////////////////////////

#include "SearchSock.h"
#include "DtdrvApp_Public_Var.h"
#include "StdCommond.h"
#include "ParaResult.h"
#include "ResultDef.h"
#include "StdNetBuffer.h"

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////


//////////////////////////////////////////////////////////////////////////
//CSearchSock
//////////////////////////////////////////////////////////////////////////

CSearchSock::CSearchSock()
{
	m_vec_tb_list.clear();
	m_map_filelist.clear();

	m_pServerDeal_ = NULL;

	for(int i=0;i<256;i++)
	{
		pfunc_RowResultDeal[i] = &CSearchSock::RowResultDeal_Default;
		pfunc_AddSendValue[i] = &CSearchSock::AddDefaultValue;
		pfunc_AddValueByType[i] = &CSearchSock::AddValueByDefault;
	}
 
	/************************************************************************/
	/*   RowResultDeal                                                      */
	/************************************************************************/
	pfunc_RowResultDeal[TYPE_VEC_FILE_LIST] = &CSearchSock::RowResultDeal_File_List;
  pfunc_RowResultDeal[TYPE_VEC_FILE_LIST_SAMPLE] = &CSearchSock::RowResultDeal_File_List_Sample;
	pfunc_RowResultDeal[TYPE_VEC_FILE_LIST_SAMPLE2] = &CSearchSock::RowResultDeal_File_List_Sample2;	
	pfunc_RowResultDeal[TYPE_VEC_TB_LIST] = &CSearchSock::RowResultDeal_TB_List;
	pfunc_RowResultDeal[TYPE_VEC_TB_INFO] = &CSearchSock::RowResultDeal_TB_Info;
	
	pfunc_RowResultDeal[TYPE_VEC_ES_AREA_EVENT] = &CSearchSock::RowResultDeal_ES_AREA_EVENT_INFO;
		
	pfunc_RowResultDeal[TYPE_VEC_COMPLAIN_GRID] = &CSearchSock::RowResultDeal_COMPLAIN_GRID;	
  pfunc_RowResultDeal[TYPE_VEC_GIS_DATA] = &CSearchSock::RowResultDeal_STAT_GIS_DATA;
	pfunc_RowResultDeal[TYPE_VEC_STAT_AREA_DATA] = &CSearchSock::RowResultDeal_STAT_AREA_DATA;
	
	pfunc_RowResultDeal[TYPE_VEC_REVIEW_DATA_SUMMARY] = &CSearchSock::RowResultDeal_LogReview_Data_Summary;
	pfunc_RowResultDeal[TYPE_VEC_REVIEW_DATA_NORMAL] = &CSearchSock::RowResultDeal_LogReview_Data_Normal;	
	pfunc_RowResultDeal[TYPE_VEC_REVIEW_TDSCDMA_DATA_SUMMARY] = &CSearchSock::RowResultDeal_Normal;
	pfunc_RowResultDeal[TYPE_VEC_REVIEW_TDSCDMA_DATA_NORMAL] = &CSearchSock::RowResultDeal_LogReview_Tdscdma_Data_Normal;
	pfunc_RowResultDeal[TYPE_VEC_REVIEW_WCDMA_DATA_SUMMARY] = &CSearchSock::RowResultDeal_Normal;
	pfunc_RowResultDeal[TYPE_VEC_REVIEW_WCDMA_DATA_NORMAL] = &CSearchSock::RowResultDeal_LogReview_Wcdma_Data_Normal;
	
	pfunc_RowResultDeal[TYPE_VEC_REVIEW_CDMA_NORMAL] = &CSearchSock::RowResultDeal_LogReview_Cdma_Sample_Normal;
	
	pfunc_RowResultDeal[TYPE_VEC_SEARCH_DATA_IMAGE] = &CSearchSock::RowResultDeal_SEARCH_DATA_IMAGE;

	pfunc_RowResultDeal[TYPE_VEC_REVIEW_ULGSM_NORMAL] = &CSearchSock::RowResultDeal_LogReview_ULGSM_Normal;
	pfunc_RowResultDeal[TYPE_VEC_REVIEW_SCAN_WCDMA] = &CSearchSock::RowResultDeal_LogReview_SCAN_WCDMA;
		
	pfunc_RowResultDeal[TYPE_VEC_NORMAL] = &CSearchSock::RowResultDeal_Normal;

	pfunc_RowResultDeal[TYPE_VEC_TALBE_COLUMN] = &CSearchSock::RowResultDeal_TB_Column;
	
	//////////////////////////////////////////////////////////////////////////
	//Buffer处理
	pfunc_AddSendValue[TYPE_CS_TINYINT] = &CSearchSock::AddTinyIntValue;
	pfunc_AddSendValue[TYPE_CS_SMALLINT] = &CSearchSock::AddSmallIntValue;
	pfunc_AddSendValue[TYPE_CS_FLOAT] = &CSearchSock::AddFloatValue;
	pfunc_AddSendValue[TYPE_CS_TEXT] = &CSearchSock::AddTextValue;
	pfunc_AddSendValue[TYPE_CS_INT] = &CSearchSock::AddIntValue;
	pfunc_AddSendValue[TYPE_CS_INT64] = &CSearchSock::AddInt64Value;
  pfunc_AddSendValue[TYPE_CS_VARYBIN] = &CSearchSock::AddImageValue;
  pfunc_AddSendValue[TYPE_CS_VARYBIN_MERGE] = &CSearchSock::AddImageValue;
	pfunc_AddSendValue[TYPE_CS_TEXT_MERGE] = &CSearchSock::AddTextMergeValue;

	pfunc_AddValueByType[TYPE_CS_TINYINT] = &CSearchSock::AddValueByTinyInt;
	pfunc_AddValueByType[TYPE_CS_SMALLINT] = &CSearchSock::AddValueBySmallInt;
	pfunc_AddValueByType[TYPE_CS_FLOAT] = &CSearchSock::AddValueByFloat;
	pfunc_AddValueByType[TYPE_CS_TEXT] = &CSearchSock::AddValueByText;
	pfunc_AddValueByType[TYPE_CS_TEXT_MERGE] = &CSearchSock::AddValueByText;
	pfunc_AddValueByType[TYPE_CS_INT] = &CSearchSock::AddValueByInt;

}

CSearchSock::~CSearchSock()
{
	m_pServerDeal_ = NULL;
	m_vec_tb_list.clear();
	m_map_filelist.clear();
}

BOOL 
CSearchSock::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	return true;
}

/************************************************************************
添加数据至发送内存
************************************************************************/
void 
CSearchSock::AddDefaultValue(int& ibufoffset,int vecoffset)
{
	PPM_DEBUG((LM_ERROR,"known value type !%l\n"));
}
void  
CSearchSock::AddTinyIntValue(int& ibufoffset,int vecoffset)
{
	m_pSendBuf_[ibufoffset++] = m_vecColumn[vecoffset].pColValue[0];
}
void  
CSearchSock::AddSmallIntValue(int& ibufoffset,int vecoffset)
{
	WORD tm_word = *(WORD*)(m_vecColumn[vecoffset].pColValue);
	CStdNetBufferWriter::PushSmallInt(m_pSendBuf_, ibufoffset, tm_word, SEND_BUF_MAXSIZE);
}
void  
CSearchSock::AddFloatValue(int& ibufoffset,int vecoffset)
{
	float tm_double = *(float*)(m_vecColumn[vecoffset].pColValue);
	int tm_int = (int)(tm_double * 1000);
	CStdNetBufferWriter::PushInt(m_pSendBuf_, ibufoffset, tm_int, SEND_BUF_MAXSIZE);
	
}
void
CSearchSock::AddInt64Value(int& ibufoffset, int vecoffset)
{
	INT64 tm_int64 = *(INT64*)(m_vecColumn[vecoffset].pColValue);
	tm_int64 = MAKEINT64_NETSEQ1(tm_int64);
	CStdNetBufferWriter::PushInt64(m_pSendBuf_, ibufoffset, tm_int64, SEND_BUF_MAXSIZE);
}
void  
CSearchSock::AddTextValue(int& ibufoffset,int vecoffset)
{
	std::string unix_str = CUtility::Utf8ToGBK((char*)m_vecColumn[vecoffset].pColValue);
	CStdNetBufferWriter::PushStr(m_pSendBuf_, ibufoffset, unix_str, SEND_BUF_MAXSIZE);
}
void  
CSearchSock::AddIntValue(int& ibufoffset,int vecoffset)
{
	int tm_int = *(int*)(m_vecColumn[vecoffset].pColValue);
	CStdNetBufferWriter::PushInt(m_pSendBuf_, ibufoffset, tm_int, SEND_BUF_MAXSIZE);
}

void  
CSearchSock::AddImageValue(int& ibufoffset,int vecoffset)
{
  WORD tm_total = m_vecColumn[vecoffset].iDataLen;
	CStdNetBufferWriter::PushSmallInt(m_pSendBuf_, ibufoffset, (WORD)tm_total, SEND_BUF_MAXSIZE);
	CStdNetBufferWriter::PushBuffer(m_pSendBuf_, ibufoffset, tm_total, m_vecColumn[vecoffset].pColValue, SEND_BUF_MAXSIZE );
}

void  
CSearchSock::AddTextMergeValue(int& tm_ioffset,int vecoffset)
{
  BYTE szDesc[8000];
	memset(&szDesc, 0, 8000);
	int tm_len = 0;
	int offset = 0;

	std::string unix_text = CUtility::Utf8ToGBK((char*)m_vecColumn[vecoffset].pColValue);
	BYTE unix_value[8000]{};
	memcpy(unix_value, unix_text.c_str(), unix_text.length());

	for (int i=0; i<8; i++)
	{
    tm_len = PPM_OS::strlen(((char*)unix_value + 255*i));

		BYTE* pData = (unix_value + 255*i);
		CStdNetBufferWriter::PushBuffer(szDesc, offset, tm_len, pData, sizeof(szDesc));
	}

  WORD tm_total = offset;
	CStdNetBufferWriter::PushSmallInt(m_pSendBuf_, tm_ioffset, (WORD)tm_total, SEND_BUF_MAXSIZE);
	CStdNetBufferWriter::PushBuffer(m_pSendBuf_, tm_ioffset, tm_total, szDesc, SEND_BUF_MAXSIZE);  

}


void 
CSearchSock::AddValueByType(int& ibufoffset, int type, byte* p_Data)
{
   (this->*pfunc_AddValueByType[type])(p_Data, ibufoffset);
}

void CSearchSock::AddValueByDefault(const BYTE* const pData, int& ioffset)
{
	
}

void CSearchSock::AddValueByTinyInt(const BYTE* const pData, int& ioffset)
{
	m_pSendBuf_[ioffset++] = *pData;
}

void CSearchSock::AddValueBySmallInt(const BYTE* const pData, int& ioffset)
{
	WORD tm_word;
	tm_word = (*(WORD*)pData);
	tm_word = MAKEWORD_NETSEQ1(tm_word);
	PPM_OS::memcpy(m_pSendBuf_+ioffset,&tm_word,2);
	ioffset += 2;
}

void CSearchSock::AddValueByFloat(const BYTE* const pData, int& ioffset)
{
	double tm_double = (*(double*)pData);
	int tm_int = (int) (tm_double * (1000));
	tm_int = MAKEINT_NETSEQ1(tm_int);
	PPM_OS::memcpy(m_pSendBuf_+ioffset, &tm_int, 4);
	ioffset += 4;
}

void CSearchSock::AddValueByText(const BYTE* const pData, int& ioffset)
{
	std::string unix_data = CUtility::Utf8ToGBK(((char*)pData));
	WORD tm_len = unix_data.length();
	WORD tm_lensave = MAKEWORD_NETSEQ1(tm_len);
	PPM_OS::memcpy(m_pSendBuf_ + ioffset, &tm_lensave, 2);
	PPM_OS::memcpy(m_pSendBuf_ + ioffset + 2, unix_data.c_str(), tm_len);
	ioffset += (tm_len + 2);
}

void CSearchSock::AddValueByInt(const BYTE* const pData, int& ioffset)
{
	int tm_int;
	tm_int = (*(int*)pData);
	tm_int = MAKEINT_NETSEQ1(tm_int);
	PPM_OS::memcpy(m_pSendBuf_+ioffset, &tm_int, 4);
	ioffset += 4;
}
/************************************************************************
回馈操作
************************************************************************/
void 
CSearchSock::FeedBackOver(BYTE bCmd1,BYTE bresValue)
{
	PPM_DEBUG((LM_INFO,"deal over!\n"));
	if(NULL != m_pServerDeal_){
		PPM_OS::memset(m_pSendBuf_,0,SEND_BUF_MAXSIZE);
		
		//构造信息发送
		int tm_ioffset = 0;
		m_pSendBuf_[tm_ioffset++] = bCmd1;//CMD1_SEARCH;//命令字
		m_pSendBuf_[tm_ioffset++] = CMD2_RESPONSE;//子命令字
		m_pSendBuf_[tm_ioffset++] = bresValue;//RESTYPE_SEARCHEND;//响应类型查询结束
		m_pServerDeal_->SendData(m_pSendBuf_,tm_ioffset);
	}	
}
void 
CSearchSock::FeedBackOverInt(BYTE bCmd1,int iresValue)
{
	PPM_DEBUG((LM_INFO,"deal over!\n"));
	if(NULL != m_pServerDeal_){
		PPM_OS::memset(m_pSendBuf_,0,SEND_BUF_MAXSIZE);
		
		iresValue = MAKEINT_NETSEQ1(iresValue);
		//构造信息发送
		int tm_ioffset = 0;
		m_pSendBuf_[tm_ioffset++] = bCmd1;//CMD1_SEARCH;//命令字
		m_pSendBuf_[tm_ioffset++] = CMD2_RESPONSE;//子命令字
		PPM_OS::memcpy(m_pSendBuf_ + tm_ioffset,&iresValue,4);
		tm_ioffset += 4;
		m_pServerDeal_->SendData(m_pSendBuf_,tm_ioffset);
	}	
}
void 
CSearchSock::FeedBackOverIntWithThirdCmd(BYTE bCmd1,int iresValue)
{
	PPM_DEBUG((LM_INFO,"deal over!\n"));
	if(NULL != m_pServerDeal_){
		PPM_OS::memset(m_pSendBuf_,0,SEND_BUF_MAXSIZE);
		
		iresValue = MAKEINT_NETSEQ1(iresValue);
		//构造信息发送
		int tm_ioffset = 0;
		m_pSendBuf_[tm_ioffset++] = bCmd1;//CMD1_SEARCH;//命令字
		m_pSendBuf_[tm_ioffset++] = CMD2_RESPONSE;//子命令字
		tm_ioffset += 1;
		PPM_OS::memcpy(m_pSendBuf_ + tm_ioffset,&iresValue,4);
		tm_ioffset += 4;
		m_pServerDeal_->SendData(m_pSendBuf_,tm_ioffset);
	}	
}

void 
CSearchSock::FeedBackOverIntWithThirdCmd(BYTE bCmd1, BYTE bCmd3, int iresValue)
{
	PPM_DEBUG((LM_INFO,"deal over!\n"));
	if(NULL != m_pServerDeal_){
		PPM_OS::memset(m_pSendBuf_,0,SEND_BUF_MAXSIZE);
		
		iresValue = MAKEINT_NETSEQ1(iresValue);
		//构造信息发送
		int tm_ioffset = 0;
		m_pSendBuf_[tm_ioffset++] = bCmd1;//CMD1_SEARCH;//命令字
		m_pSendBuf_[tm_ioffset++] = CMD2_RESPONSE;//子命令字
		m_pSendBuf_[tm_ioffset++] = bCmd3;
		
		PPM_OS::memcpy(m_pSendBuf_ + tm_ioffset,&iresValue,4);
		tm_ioffset += 4;
		m_pServerDeal_->SendData(m_pSendBuf_,tm_ioffset);
	}	
}

/************************************************************************
处理输出结果
************************************************************************/
BOOL 
CSearchSock::RowResultDeal(int nQType)
{
	return (this->*pfunc_RowResultDeal[nQType % 0x100])();
}

BOOL 
CSearchSock::RowResultDeal_Default()
{
	return FALSE;
}

BOOL 
CSearchSock::RowResultDeal_Normal()
{
	if(NULL != m_pServerDeal_)
	{
		PPM_OS::memset(m_pSendBuf_,0,SEND_BUF_MAXSIZE);
		
		//构造信息发送
		int tm_ioffset = 0;

		BuildSendBufHeadCmdCode(tm_ioffset);

		for(std::size_t i = 0; i < m_vecColumn.size(); i++){
      (this->*pfunc_AddSendValue[m_vecColumn[i].iColType])(tm_ioffset,i);
		}
		if(0 > m_pServerDeal_->SendData(m_pSendBuf_,tm_ioffset)){
			return -1;
		}
	}
	return TRUE;
}


BOOL
CSearchSock::RowResultDeal_STAT_GIS_DATA()
{
	if(m_vecColumn.size() != CResultDef::Instance()->GetVecResultSize(CResultDef::STATTYPE_GIS_D_GPRS_NEW)){
		return FALSE;
	} 
	if(NULL != m_pServerDeal_){
		PPM_OS::memset(m_pSendBuf_,0,SEND_BUF_MAXSIZE);
		
		int tm_ioffset = 0;
		m_pSendBuf_[tm_ioffset++] = CMD1_SEARCH;
		m_pSendBuf_[tm_ioffset++] = CMD2_RESPONSE;
		m_pSendBuf_[tm_ioffset++] = m_bResponseType_;
		//
		
		byte szDesc[2000];
		PPM_OS::memset(&szDesc, 0, sizeof(szDesc));
		
        WORD szDesc_ioffset = 0;
		WORD tm_total = 0;
		
		for(std::size_t i = 0; i < m_vecColumn.size(); i++)
		{
			if((i >= 27) && (i <= 90))
			{
				DealStatGisBetween27To90(i, szDesc, szDesc_ioffset, tm_total, tm_ioffset);
			}
			else
			{
				(this->*pfunc_AddSendValue[m_vecColumn[i].iColType])(tm_ioffset,i);
			}
		}
		if(0 > m_pServerDeal_->SendData(m_pSendBuf_,tm_ioffset)){
			return -1;
		}
	}
	return TRUE;
}

bool CSearchSock::IsStatGisNeedNum(unsigned int num)
{
	unsigned int vecnum[] = {34, 42, 50, 58, 66, 74, 82, 90};

	bool IsNeedNum = false;
	for (std::size_t count = 0; count < sizeof(vecnum); count++)
	{
		if (num == vecnum[count])
		{
			IsNeedNum = true;
			break;
		}
	}

	return IsNeedNum;
}

void CSearchSock::DealStatGisBetween27To90(unsigned int i, BYTE* szDesc, WORD& szDesc_ioffset, WORD& tm_total, int& tm_ioffset)
{
	WORD tm_len = 0;

	PPM_OS::memcpy(&tm_len,m_vecColumn[i].pColValue,2);
	tm_len = REVERTWORD_NETSEQ1(tm_len);

	if (tm_len > 1)
	{
		byte sztmp[260];
		PPM_OS::memset(&sztmp, 0, sizeof(sztmp));
		PPM_OS::memcpy(sztmp,m_vecColumn[i].pColValue + 2,tm_len);
		PPM_OS::memcpy(szDesc + szDesc_ioffset, sztmp, tm_len);
		szDesc_ioffset += tm_len;
		tm_total += tm_len;
	}

	if(IsStatGisNeedNum(i))
	{
		WORD tm_lensave = MAKEWORD_NETSEQ1(tm_total);
		PPM_OS::memcpy(m_pSendBuf_ + tm_ioffset,&tm_lensave,2);
		PPM_OS::memcpy(m_pSendBuf_ + tm_ioffset + 2,szDesc,tm_total);	
		tm_ioffset += tm_total+2;

		PPM_OS::memset(&szDesc, 0, sizeof(szDesc));
		szDesc_ioffset = 0;
		tm_total = 0;
	}
}

BOOL
CSearchSock::RowResultDeal_LogReview_Data_Summary()
{
	if(m_vecColumn.size() != CResultDef::Instance()->GetVecResultSize(CResultDef::SEARCHTYPE_REVIEW_DATA_SAMPLE_SUMMARY)){
		return FALSE;
	} 
	if(NULL != m_pServerDeal_)
	{
		PPM_OS::memset(m_pSendBuf_,0,SEND_BUF_MAXSIZE);
		int tm_ioffset = 0;
        
		BuildSendBufHeadCmdCode(tm_ioffset);
		
		byte szDesc[2000];
		PPM_OS::memset(&szDesc, 0, sizeof(szDesc));

        WORD szDesc_ioffset = 0;
		WORD tm_total = 0;				
		
		for(std::size_t i = 0; i < m_vecColumn.size(); i++)
		{
			if((i >= 11) && (i <= 18))
			{
				DealStatGisBetween11To18(i, szDesc, szDesc_ioffset, tm_total, tm_ioffset);
			}
			else
			{
				(this->*pfunc_AddSendValue[m_vecColumn[i].iColType])(tm_ioffset,i);
			}
		}
		if(0 > m_pServerDeal_->SendData(m_pSendBuf_,tm_ioffset)){
			return -1;
		}
	}
	return TRUE;
}

void CSearchSock::BuildSendBufHeadCmdCode(int& tm_ioffset)
{
	m_pSendBuf_[tm_ioffset++] = CMD1_SEARCH;
	m_pSendBuf_[tm_ioffset++] = CMD2_RESPONSE;
	m_pSendBuf_[tm_ioffset++] = m_bResponseType_;
}

void CSearchSock::DealStatGisBetween11To18(unsigned int i, BYTE* szDesc, WORD& szDesc_ioffset, WORD& tm_total, int& tm_ioffset)
{
	WORD tm_len = 0;
	PPM_OS::memcpy(&tm_len,m_vecColumn[i].pColValue,2);
	tm_len = REVERTWORD_NETSEQ1(tm_len);

	if (tm_len > 1)
	{
		byte sztmp[260];
		PPM_OS::memset(&sztmp, 0, sizeof(sztmp));
		PPM_OS::memcpy(sztmp,m_vecColumn[i].pColValue + 2,tm_len);
		PPM_OS::memcpy(szDesc + szDesc_ioffset, sztmp, tm_len);
		szDesc_ioffset += tm_len;
		tm_total += tm_len;
	}

	if(i == 18)
	{
		WORD tm_lensave = MAKEWORD_NETSEQ1(tm_total);
		PPM_OS::memcpy(m_pSendBuf_ + tm_ioffset,&tm_lensave,2);
		PPM_OS::memcpy(m_pSendBuf_ + tm_ioffset + 2,szDesc,tm_total);	
		tm_ioffset += tm_total+2;

		PPM_OS::memset(&szDesc, 0, sizeof(szDesc));
		szDesc_ioffset = 0;
		tm_total = 0;
	}
}

BOOL
CSearchSock::RowResultDeal_LogReview_Data_Normal()
{
	if(m_vecColumn.size() != CResultDef::Instance()->GetVecResultSize(CResultDef::SEARCHTYPE_REVIEW_DATA_SAMPLE_NORMAL)){
		return FALSE;
	} 
	if(NULL != m_pServerDeal_){
		PPM_OS::memset(m_pSendBuf_,0,SEND_BUF_MAXSIZE);
		
		int tm_ioffset = 0;

		BuildSendBufHeadCmdCode(tm_ioffset);
		
		byte szDesc[2000];
		PPM_OS::memset(&szDesc, 0, sizeof(szDesc));

        WORD szDesc_ioffset = 0;
		WORD tm_total = 0;
		
		for(std::size_t i = 0; i < m_vecColumn.size(); i++)
		{
			if(((i >= 11) && (i <= 18)) || ((i >= 43) && (i <= 98)) )
			{
				DealStatGisBetween43To98(i, szDesc, szDesc_ioffset, tm_total, tm_ioffset);
			}
			else
			{
				(this->*pfunc_AddSendValue[m_vecColumn[i].iColType])(tm_ioffset,i);
			}
		}
		if(0 > m_pServerDeal_->SendData(m_pSendBuf_,tm_ioffset)){
			return -1;
		}
	}
	return TRUE;
}

bool CSearchSock::IsLogReviewNeedNum(unsigned int num)
{
	unsigned int vecnum[] = {18, 50, 58, 66, 74, 82, 90, 98};

	bool IsNeedNum = false;
	for (std::size_t count = 0; count < sizeof(vecnum); count++)
	{
		if (num == vecnum[count])
		{
			IsNeedNum = true;
			break;
		}
	}

	return IsNeedNum;
}

void CSearchSock::DealStatGisBetween43To98(unsigned int i, BYTE* szDesc, WORD& szDesc_ioffset, WORD& tm_total, int& tm_ioffset)
{
    WORD tm_len = 0;
	PPM_OS::memcpy(&tm_len,m_vecColumn[i].pColValue,2);
	tm_len = REVERTWORD_NETSEQ1(tm_len);

	if (tm_len > 1)
	{
		byte sztmp[260];
		PPM_OS::memset(&sztmp, 0, sizeof(sztmp));
		PPM_OS::memcpy(sztmp,m_vecColumn[i].pColValue + 2,tm_len);
		PPM_OS::memcpy(szDesc + szDesc_ioffset, sztmp, tm_len);
		szDesc_ioffset += tm_len;
		tm_total += tm_len;
	}

	if(IsLogReviewNeedNum(i))
	{
		WORD tm_lensave = MAKEWORD_NETSEQ1(tm_total);
		PPM_OS::memcpy(m_pSendBuf_ + tm_ioffset,&tm_lensave,2);
		PPM_OS::memcpy(m_pSendBuf_ + tm_ioffset + 2,szDesc,tm_total);	
		tm_ioffset += tm_total+2;

		PPM_OS::memset(&szDesc, 0, sizeof(szDesc));
		szDesc_ioffset = 0;
		tm_total = 0;
	}
}

BOOL
CSearchSock::RowResultDeal_LogReview_Tdscdma_Data_Normal()
{
	if(m_vecColumn.size() != CResultDef::Instance()->GetVecResultSize(CResultDef::SEARCHTYPE_REVIEW_TDSCDMA_DATA_SAMPLE_NORMAL)){
		return FALSE;
	} 
	if(NULL != m_pServerDeal_){
		PPM_OS::memset(m_pSendBuf_,0,SEND_BUF_MAXSIZE);
		
		int tm_ioffset = 0;
		BuildSendBufHeadCmdCode(tm_ioffset);
		
		byte szDesc[2000];
		PPM_OS::memset(&szDesc, 0, sizeof(szDesc));
		
        WORD szDesc_ioffset = 0;
		WORD tm_total = 0;		
		
		for(std::size_t i = 0; i < m_vecColumn.size(); i++)
		{
			if((i >= 64) && (i <= 167))
			{
				DealStatGisBetween64To167(i, szDesc, szDesc_ioffset, tm_total, tm_ioffset);
			}
			else
			{
				(this->*pfunc_AddSendValue[m_vecColumn[i].iColType])(tm_ioffset,i);
			}
		}
		if(0 > m_pServerDeal_->SendData(m_pSendBuf_,tm_ioffset)){
			return -1;
		}
	}
	return TRUE;
}

bool CSearchSock::IsLogReviewTdscdmaNeedNum(unsigned int num)
{
	unsigned int vecnum[] = {71, 79, 87, 95, 103, 111, 119, 127, 135, 143, 151, 159, 167};

	bool IsNeedNum = false;
	for (std::size_t count = 0; count < sizeof(vecnum); count++)
	{
		if (num == vecnum[count])
		{
			IsNeedNum = true;
			break;
		}
	}

	return IsNeedNum;
}

void CSearchSock::DealStatGisBetween64To167(unsigned int i, BYTE* szDesc, WORD& szDesc_ioffset, WORD& tm_total, int& tm_ioffset)
{
  WORD tm_len = 0;
	PPM_OS::memcpy(&tm_len,m_vecColumn[i].pColValue,2);
	tm_len = REVERTWORD_NETSEQ1(tm_len);

	if (tm_len > 1)
	{
		byte sztmp[260];
		PPM_OS::memset(&sztmp, 0, sizeof(sztmp));
		PPM_OS::memcpy(sztmp,m_vecColumn[i].pColValue + 2,tm_len);
		PPM_OS::memcpy(szDesc + szDesc_ioffset, sztmp, tm_len);
		szDesc_ioffset += tm_len;
		tm_total += tm_len;
	}

	if(IsLogReviewTdscdmaNeedNum(i))
	{
		WORD tm_lensave = MAKEWORD_NETSEQ1(tm_total);
		PPM_OS::memcpy(m_pSendBuf_ + tm_ioffset,&tm_lensave,2);
		PPM_OS::memcpy(m_pSendBuf_ + tm_ioffset + 2,szDesc,tm_total);	
		tm_ioffset += tm_total+2;

		PPM_OS::memset(&szDesc, 0, sizeof(szDesc));
		szDesc_ioffset = 0;
		tm_total = 0;
	}
}

BOOL
CSearchSock::RowResultDeal_LogReview_Wcdma_Data_Normal()
{
	if(m_vecColumn.size() != CResultDef::Instance()->GetVecResultSize(CResultDef::SEARCHTYPE_REVIEW_WCDMA_DATA_SAMPLE_NORMAL)){
		return FALSE;
	} 
	if(NULL != m_pServerDeal_){
		PPM_OS::memset(m_pSendBuf_,0,SEND_BUF_MAXSIZE);
		
		int tm_ioffset = 0;
		BuildSendBufHeadCmdCode(tm_ioffset);
		
		byte szDesc[2000];
		PPM_OS::memset(&szDesc, 0, sizeof(szDesc));
		
    WORD szDesc_ioffset = 0;
		WORD tm_total = 0;
		
		for(std::size_t i = 0; i < m_vecColumn.size(); i++)
		{
			if((i >= 34) && (i <= 225))
			{
				DealStatGisBetween34To225(i, szDesc, szDesc_ioffset, tm_total, tm_ioffset);
			}
			else
			{
				(this->*pfunc_AddSendValue[m_vecColumn[i].iColType])(tm_ioffset,i);
			}
		}
		if(0 > m_pServerDeal_->SendData(m_pSendBuf_,tm_ioffset)){
			return -1;
		}
	}
	return TRUE;
}

bool CSearchSock::IsLogReviewWcdmaNeedNum(unsigned int num)
{
    unsigned int vecnum[] = {41, 49, 57, 65, 73, 81, 89, 97, 105, 113, 121, 129, 137, 
		145, 153, 161, 169, 177, 185, 193, 201, 209, 217, 225};

	bool IsNeedNum = false;
	for (std::size_t count = 0; count < sizeof(vecnum); count++)
	{
		if (num == vecnum[count])
		{
			IsNeedNum = true;
			break;
		}
	}

	return IsNeedNum;
}

void CSearchSock::DealStatGisBetween34To225(unsigned int i, BYTE* szDesc, WORD& szDesc_ioffset, WORD& tm_total, int& tm_ioffset)
{
	WORD tm_len = 0;
	PPM_OS::memcpy(&tm_len,m_vecColumn[i].pColValue,2);
	tm_len = REVERTWORD_NETSEQ1(tm_len);

	byte byteTemp[2000];
	PPM_OS::memset(byteTemp, 0, sizeof(byteTemp));
	PPM_OS::memcpy(&byteTemp,m_vecColumn[i].pColValue,sizeof(byteTemp));

	if (tm_len > 1)
	{
		byte sztmp[260];
		PPM_OS::memset(&sztmp, 0, sizeof(sztmp));
		PPM_OS::memcpy(sztmp,m_vecColumn[i].pColValue+ 2,tm_len);
		PPM_OS::memcpy(szDesc + szDesc_ioffset, sztmp, tm_len);
		szDesc_ioffset += tm_len;
		tm_total += tm_len;
	}

	if(IsLogReviewWcdmaNeedNum(i))
	{
		WORD tm_lensave = MAKEWORD_NETSEQ1(tm_total);
		PPM_OS::memcpy(m_pSendBuf_ + tm_ioffset,&tm_lensave,2);
		PPM_OS::memcpy(m_pSendBuf_ + tm_ioffset + 2,szDesc,tm_total);	
		tm_ioffset += tm_total+2;

		PPM_OS::memset(&szDesc, 0, sizeof(szDesc));
		szDesc_ioffset = 0;
		tm_total = 0;
	}
}

BOOL
CSearchSock::RowResultDeal_LogReview_Cdma_Sample_Normal()
{
	if(m_vecColumn.size() != CResultDef::Instance()->GetVecResultSize(CResultDef::SEARCHTYPE_CDMA_SAMPLE_NORMAL)){
		return FALSE;
	} 
	if(NULL != m_pServerDeal_){
		PPM_OS::memset(m_pSendBuf_,0,SEND_BUF_MAXSIZE);
		
		int tm_ioffset = 0;
    BuildSendBufHeadCmdCode(tm_ioffset);
		
		byte szDesc[2000];
		PPM_OS::memset(&szDesc, 0, sizeof(szDesc));
		
    WORD szDesc_ioffset = 0;
		WORD tm_total = 0;		
		
		for(std::size_t i = 0; i < m_vecColumn.size(); i++)
		{
			if((i >= 28) && (i <= 179))
			{
				DealStatGisBetween28To179(i, szDesc, szDesc_ioffset, tm_total, tm_ioffset);
			}
			else
			{
				(this->*pfunc_AddSendValue[m_vecColumn[i].iColType])(tm_ioffset,i);
			}
		}
		if(0 > m_pServerDeal_->SendData(m_pSendBuf_,tm_ioffset)){
			return -1;
		}
	}
	return TRUE;
}

bool CSearchSock::IsLogReviewCdmaNeedNum(unsigned int num)
{
	unsigned int vecnum[] = {35, 43, 51, 59, 67, 75, 83, 91, 99, 107, 115, 123, 131, 139, 147, 155, 163, 171, 179};

	bool IsNeedNum = false;
	for (std::size_t count = 0; count < sizeof(vecnum); count++)
	{
		if (num == vecnum[count])
		{
			IsNeedNum = true;
			break;
		}
	}

	return IsNeedNum;
}

void CSearchSock::DealStatGisBetween28To179(unsigned int i, BYTE* szDesc, WORD& szDesc_ioffset, WORD& tm_total, int& tm_ioffset)
{
  WORD tm_len = 0;
	PPM_OS::memcpy(&tm_len,m_vecColumn[i].pColValue,2);
	tm_len = REVERTWORD_NETSEQ1(tm_len);

	if (tm_len > 1)
	{
		byte sztmp[260];
		PPM_OS::memset(&sztmp, 0, sizeof(sztmp));
		PPM_OS::memcpy(sztmp,m_vecColumn[i].pColValue + 2,tm_len);
		PPM_OS::memcpy(szDesc + szDesc_ioffset, sztmp, tm_len);
		szDesc_ioffset += tm_len;
		tm_total += tm_len;
	}

	if(IsLogReviewCdmaNeedNum(i))
	{
		WORD tm_lensave = MAKEWORD_NETSEQ1(tm_total);
		PPM_OS::memcpy(m_pSendBuf_ + tm_ioffset,&tm_lensave,2);
		PPM_OS::memcpy(m_pSendBuf_ + tm_ioffset + 2,szDesc,tm_total);	
		tm_ioffset += tm_total+2;

		PPM_OS::memset(&szDesc, 0, sizeof(szDesc));
		szDesc_ioffset = 0;
		tm_total = 0;
	}
}

BOOL
CSearchSock::RowResultDeal_LogReview_ULGSM_Normal()
{
	if(m_vecColumn.size() != CResultDef::Instance()->GetVecResultSize(CResultDef::SEARCHTYPE_REVIEW_ULGSM_NORMAL)){
		return FALSE;
	}  
	if(NULL != m_pServerDeal_){
		PPM_OS::memset(m_pSendBuf_,0,SEND_BUF_MAXSIZE);

		int tm_ioffset = 0;
		BuildSendBufHeadCmdCode(tm_ioffset);

		byte szDesc[2000];
		PPM_OS::memset(&szDesc, 0, sizeof(szDesc));

		WORD szDesc_ioffset = 0;
		WORD tm_total = 0;
		
		for(std::size_t i = 0; i < m_vecColumn.size(); i++)
		{
			if((i >= 36) && (i <= 51))
			{
				DealStatGisBetween36To51(i, szDesc, szDesc_ioffset, tm_total, tm_ioffset);
			}
			else
			{
				(this->*pfunc_AddSendValue[m_vecColumn[i].iColType])(tm_ioffset,i);
			}
		}
		if(0 > m_pServerDeal_->SendData(m_pSendBuf_,tm_ioffset)){
			return -1;
		}
	}
	return TRUE;
}

void CSearchSock::DealStatGisBetween36To51(unsigned int i, BYTE* szDesc, WORD& szDesc_ioffset, WORD& tm_total, int& tm_ioffset)
{
	WORD tm_len = 0;
	PPM_OS::memcpy(&tm_len,m_vecColumn[i].pColValue,2);
	tm_len = REVERTWORD_NETSEQ1(tm_len);

	if (tm_len > 1)
	{
		byte sztmp[260];
		PPM_OS::memset(&sztmp, 0, sizeof(sztmp));
		PPM_OS::memcpy(sztmp,m_vecColumn[i].pColValue + 2,tm_len);
		PPM_OS::memcpy(szDesc + szDesc_ioffset, sztmp, tm_len);
		szDesc_ioffset += tm_len;
		tm_total += tm_len;
	}

	if(i == 43 || i == 51 )
	{
		WORD tm_lensave = MAKEWORD_NETSEQ1(tm_total);
		PPM_OS::memcpy(m_pSendBuf_ + tm_ioffset,&tm_lensave,2);
		PPM_OS::memcpy(m_pSendBuf_ + tm_ioffset + 2,szDesc,tm_total);	
		tm_ioffset += tm_total+2;

		PPM_OS::memset(&szDesc, 0, sizeof(szDesc));
		szDesc_ioffset = 0;
		tm_total = 0;
	}
}

BOOL
CSearchSock::RowResultDeal_LogReview_SCAN_WCDMA()
{
	if(m_vecColumn.size() != CResultDef::Instance()->GetVecResultSize(CResultDef::SEARCHTYPE_REVIEW_SCAN_WCDMA)){
		return FALSE;
	} 
 
	if(NULL != m_pServerDeal_){
		PPM_OS::memset(m_pSendBuf_,0,SEND_BUF_MAXSIZE);

		int tm_ioffset = 0;

		BuildSendBufHeadCmdCode(tm_ioffset);

		byte szDesc[2000];
		PPM_OS::memset(&szDesc, 0, sizeof(szDesc));

		WORD szDesc_ioffset = 0;
		WORD tm_total = 0;

		for(std::size_t i = 0; i < m_vecColumn.size(); i++)
		{
			if((i >= 7) && (i <= 46))
			{
				DealStatGisBetween7To46(i, szDesc, szDesc_ioffset, tm_total, tm_ioffset);
			}
			else
			{
				(this->*pfunc_AddSendValue[m_vecColumn[i].iColType])(tm_ioffset,i);
			}
		}
		if(0 > m_pServerDeal_->SendData(m_pSendBuf_,tm_ioffset)){
			return -1;
		}
	}
	return TRUE;
}

bool CSearchSock::IsLogReviewScanNeedNum(unsigned int num)
{
	unsigned int vecnum[] = {14, 22, 30, 38, 46};

	bool IsNeedNum = false;
	for (std::size_t count = 0; count < sizeof(vecnum); count++)
	{
		if (num == vecnum[count])
		{
			IsNeedNum = true;
			break;
		}
	}

	return IsNeedNum;
}

void CSearchSock::DealStatGisBetween7To46(unsigned int i, BYTE* szDesc, WORD& szDesc_ioffset, WORD& tm_total, int& tm_ioffset)
{
  WORD tm_len = 0;
	PPM_OS::memcpy(&tm_len,m_vecColumn[i].pColValue,2);
	tm_len = REVERTWORD_NETSEQ1(tm_len);

	if (tm_len > 1)
	{
		byte sztmp[260];
		PPM_OS::memset(&sztmp, 0, sizeof(sztmp));
		PPM_OS::memcpy(sztmp,m_vecColumn[i].pColValue + 2,tm_len);
		PPM_OS::memcpy(szDesc + szDesc_ioffset, sztmp, tm_len);
		szDesc_ioffset += tm_len;
		tm_total += tm_len;
	}

	if(IsLogReviewScanNeedNum(i))
	{
		WORD tm_lensave = MAKEWORD_NETSEQ1(tm_total);
		PPM_OS::memcpy(m_pSendBuf_ + tm_ioffset,&tm_lensave,2);
		PPM_OS::memcpy(m_pSendBuf_ + tm_ioffset + 2,szDesc,tm_total);	
		tm_ioffset += tm_total+2;

		PPM_OS::memset(&szDesc, 0, sizeof(szDesc));
		szDesc_ioffset = 0;
		tm_total = 0;
	}
}

BOOL
CSearchSock::RowResultDeal_SEARCH_DATA_IMAGE()
{
	if(m_vecColumn.size() != CResultDef::Instance()->GetVecResultSize(CResultDef::STATTYPE_STAT_DATA_IMAGE)){
		return FALSE;
	} 
	if(NULL != m_pServerDeal_){
		PPM_OS::memset(m_pSendBuf_,0,SEND_BUF_MAXSIZE);

		int tm_ioffset = 0;
		m_pSendBuf_[tm_ioffset++] = CMD1_SEARCH;
		m_pSendBuf_[tm_ioffset++] = CMD2_RESPONSE;
		m_pSendBuf_[tm_ioffset++] = m_bResponseType_;
		//

		byte szDesc[2000];
		PPM_OS::memset(&szDesc, 0, sizeof(szDesc));
		byte sztmp[260];

		WORD szDesc_ioffset = 0;
		WORD tm_total = 0;
		WORD tm_len = 0;

		for(std::size_t i = 0; i < m_vecColumn.size(); i++)
		{
			PPM_OS::memcpy(&tm_len,m_vecColumn[i].pColValue,2);
			tm_len = REVERTWORD_NETSEQ1(tm_len);

			if (tm_len > 1)
			{
				PPM_OS::memset(&sztmp, 0, sizeof(sztmp));
				PPM_OS::memcpy(sztmp,m_vecColumn[i].pColValue + 2,tm_len);
				PPM_OS::memcpy(szDesc + szDesc_ioffset, sztmp, tm_len);
				szDesc_ioffset += tm_len;
				tm_total += tm_len;
			}

			if(IsLogReviewSearchNeedNum(i))
			{
				WORD tm_lensave = MAKEWORD_NETSEQ1(tm_total);
				PPM_OS::memcpy(m_pSendBuf_ + tm_ioffset,&tm_lensave,2);
				PPM_OS::memcpy(m_pSendBuf_ + tm_ioffset + 2,szDesc,tm_total);	
				tm_ioffset += tm_total+2;

				PPM_OS::memset(&szDesc, 0, sizeof(szDesc));
				szDesc_ioffset = 0;
				tm_total = 0;
			}
		}
		if(0 > m_pServerDeal_->SendData(m_pSendBuf_,tm_ioffset)){
			return -1;
		}
	}
	return TRUE;
}

bool CSearchSock::IsLogReviewSearchNeedNum(unsigned int num)
{
	unsigned int vecnum[] = {7, 15, 23, 31, 39, 47, 55, 63, 71, 79, 87, 95, 103, 111, 119, 127};

	bool IsNeedNum = false;
	for (std::size_t count = 0; count < sizeof(vecnum); count++)
	{
		if (num == vecnum[count])
		{
			IsNeedNum = true;
			break;
		}
	}

	return IsNeedNum;
}

BOOL 
CSearchSock::RowResultDeal_File_List()
{
	STRU_FILE_LIST tm1;

	tm1.testtype = *((int*)m_vecColumn[1].pColValue);
	tm1.strlog = (char *)m_vecColumn[2].pColValue;
	tm1.strtb = (char *)m_vecColumn[3].pColValue;
	
	int fileid = *((int*)m_vecColumn[0].pColValue);
	
	m_map_filelist[tm1].push_back(fileid);
	
	return TRUE;
}

BOOL 
CSearchSock::RowResultDeal_File_List_Sample()
{	
	STRU_FILE_LIST tm1;

	tm1.testtype = *((int*)m_vecColumn[1].pColValue);
	tm1.strlog = (char *)m_vecColumn[2].pColValue;
	tm1.strtb = (char *)m_vecColumn[3].pColValue;	
	
	int fileid = *((int*)m_vecColumn[0].pColValue);
	
	m_map_filelist[tm1].push_back(fileid);
	
	return TRUE;
}


BOOL 
CSearchSock::RowResultDeal_File_List_Sample2()
{	
	STRU_FILE_LIST2 tm1;

	tm1.testtype = *((int*)m_vecColumn[1].pColValue);
	tm1.servicetype = *((int*)m_vecColumn[2].pColValue);
	tm1.strlog = (char *)m_vecColumn[3].pColValue;
	tm1.strtb = (char *)m_vecColumn[4].pColValue;	
	
	int fileid = *((int*)m_vecColumn[0].pColValue);
	
	m_map_filelist2[tm1].push_back(fileid);
    	
	return TRUE;
}


BOOL 
CSearchSock::RowResultDeal_TB_List()
{
	//set the file map list
	STRU_TB_LIST tm1;
	
	tm1.fileid = *((int*)m_vecColumn[0].pColValue);
	tm1.testtype = *((int*)m_vecColumn[1].pColValue);
	tm1.strlog = (char *)m_vecColumn[2].pColValue;
	tm1.strmsg = (char *)m_vecColumn[3].pColValue;
	tm1.strevent = (char *)m_vecColumn[4].pColValue;
	tm1.strsample = (char *)m_vecColumn[5].pColValue;
	tm1.strsample2 = (char *)m_vecColumn[6].pColValue;
	tm1.strsuffix = (char *)m_vecColumn[7].pColValue;
	
	m_vec_tb_list.push_back(tm1);
	
	return TRUE;
}

BOOL
CSearchSock::RowResultDeal_ES_AREA_EVENT_INFO()
{
	if(m_vecColumn.size() != CResultDef::Instance()->GetVecResultSize(CResultDef::SEARCHTYPE_ES_AREA_EVENT_INFO)){
		return FALSE;
	} 
	if(NULL != m_pServerDeal_){
		PPM_OS::memset(m_pSendBuf_,0,SEND_BUF_MAXSIZE);
		
		int tm_ioffset = 0;

		m_pSendBuf_[tm_ioffset++] = CMD1_SEARCH;
		m_pSendBuf_[tm_ioffset++] = CMD2_RESPONSE;
		m_pSendBuf_[tm_ioffset++] = m_bResponseType_;
		//
		
		char szDesc[1024];
		PPM_OS::memset(&szDesc, 0, sizeof(szDesc));
		
		for(std::size_t i = 0; i < m_vecColumn.size(); i++)
		{
			if((i >= 29) && (i <= 52))
			{
				DealStatGisBetween29To52(i, szDesc, tm_ioffset);
			}
			else
			{
				(this->*pfunc_AddSendValue[m_vecColumn[i].iColType])(tm_ioffset,i);
			}
		}
		if(0 > m_pServerDeal_->SendData(m_pSendBuf_,tm_ioffset)){
			return -1;
		}
	}
	return TRUE;
}

void CSearchSock::DealStatGisBetween29To52(unsigned int i, char* szDesc, int& tm_ioffset)
{
	int tm_len = 0;
	char sztmp[255];
	tm_len = PPM_OS::strlen(((char*)m_vecColumn[i].pColValue));
	PPM_OS::memset(&sztmp, 0, sizeof(sztmp));
	PPM_OS::memcpy(sztmp,m_vecColumn[i].pColValue,tm_len);
	PPM_OS::snprintf(szDesc,1024,"%s%s",szDesc,sztmp);

	if(i == 36 || i == 44 || i == 52)
	{
		WORD tm_total = PPM_OS::strlen(szDesc);
		WORD tm_lensave = MAKEWORD_NETSEQ1(tm_total);
		PPM_OS::memcpy(m_pSendBuf_ + tm_ioffset,&tm_lensave,2);
		PPM_OS::memcpy(m_pSendBuf_ + tm_ioffset + 2,szDesc,tm_total);

		tm_ioffset += tm_total+2;
		PPM_OS::memset(&szDesc, 0, sizeof(szDesc));
	}
}


BOOL 
CSearchSock::RowResultDeal_COMPLAIN_GRID()
{
	if(NULL != m_pServerDeal_)
	{
		PPM_OS::memset(m_pSendBuf_,0,SEND_BUF_MAXSIZE);
		
		//构造信息发送
		int tm_ioffset = 0;
		
		m_pSendBuf_[tm_ioffset++] = CMD1_SEARCH;//命令字
		m_pSendBuf_[tm_ioffset++] = CMD2_RESPONSE;//子命令字
		m_pSendBuf_[tm_ioffset++] = m_bResponseType_;//响应类型
		//
    int intTemp;
		for(std::size_t i = 0; i < m_vecColumn.size(); i++){       
			if (1 == i)
			{
			   intTemp = *((int*)m_vecColumn[i].pColValue);
			   m_vec_temp_int.push_back(intTemp);
			}          

      (this->*pfunc_AddSendValue[m_vecColumn[i].iColType])(tm_ioffset,i);
		}
		if(0 > m_pServerDeal_->SendData(m_pSendBuf_,tm_ioffset)){
			return -1;
		}
	}
	return TRUE;
}

BOOL 
CSearchSock::RowResultDeal_TB_Info()
{
	m_tb_info.fileid = *((int*)m_vecColumn[0].pColValue);
	m_tb_info.nettype = *((int*)m_vecColumn[1].pColValue);
	m_tb_info.servicetype = *((int*)m_vecColumn[2].pColValue);
	m_tb_info.strmsg = (char *)m_vecColumn[3].pColValue;
	m_tb_info.strevent = (char *)m_vecColumn[4].pColValue;
	m_tb_info.strsample = (char *)m_vecColumn[5].pColValue;
	m_tb_info.strsample2 = (char *)m_vecColumn[6].pColValue;
	m_tb_info.strsuffix = (char *)m_vecColumn[7].pColValue;
	
	return TRUE;
}


BOOL
CSearchSock::RowResultDeal_STAT_AREA_DATA()
{
	if(m_vecColumn.size() != CResultDef::Instance()->GetVecResultSize(CResultDef::STATTYPE_AREA_DATA)){
		return FALSE;
	} 
	if(NULL != m_pServerDeal_){
		PPM_OS::memset(m_pSendBuf_,0,SEND_BUF_MAXSIZE);
		
		int tm_ioffset = 0;
        BuildSendBufHeadCmdCode(tm_ioffset);
		
		byte szDesc[2000];
		PPM_OS::memset(&szDesc, 0, sizeof(szDesc));
		
    WORD szDesc_ioffset = 0;
		WORD tm_total = 0;

		for(std::size_t i = 0; i < m_vecColumn.size(); i++)
		{
			if((i >= 25) && (i <= 88))
			{
				DealStatGisBetween28To88(i, szDesc, szDesc_ioffset, tm_total, tm_ioffset);
			}
			else
			{
				(this->*pfunc_AddSendValue[m_vecColumn[i].iColType])(tm_ioffset,i);
			}
		}
		if(0 > m_pServerDeal_->SendData(m_pSendBuf_,tm_ioffset)){
			return -1;
		}
	}
	return TRUE;
}

bool CSearchSock::IsStatAreaNeedNum(unsigned int num)
{
	unsigned int vecnum[] = {32, 40, 48, 56, 64, 72, 80, 88};

	bool IsNeedNum = false;
	for (std::size_t count = 0; count < sizeof(vecnum); count++)
	{
		if (num == vecnum[count])
		{
			IsNeedNum = true;
			break;
		}
	}

	return IsNeedNum;
}

void CSearchSock::DealStatGisBetween28To88(unsigned int i, BYTE* szDesc, WORD& szDesc_ioffset, WORD& tm_total, int& tm_ioffset)
{
  WORD tm_len = 0;
	PPM_OS::memcpy(&tm_len,m_vecColumn[i].pColValue,2);
	tm_len = REVERTWORD_NETSEQ1(tm_len);

	if (tm_len > 1)
	{
		byte sztmp[260];
		PPM_OS::memset(&sztmp, 0, sizeof(sztmp));
		PPM_OS::memcpy(sztmp,m_vecColumn[i].pColValue + 2,tm_len);
		PPM_OS::memcpy(szDesc + szDesc_ioffset, sztmp, tm_len);
		szDesc_ioffset += tm_len;
		tm_total += tm_len;
	}

	if(IsStatAreaNeedNum(i))
	{
		WORD tm_lensave = MAKEWORD_NETSEQ1(tm_total);
		PPM_OS::memcpy(m_pSendBuf_ + tm_ioffset,&tm_lensave,2);
		PPM_OS::memcpy(m_pSendBuf_ + tm_ioffset + 2,szDesc,tm_total);	
		tm_ioffset += tm_total+2;

		PPM_OS::memset(&szDesc, 0, sizeof(szDesc));
		szDesc_ioffset = 0;
		tm_total = 0;
	}
}
/************************************************************************
文件头处理                                                                 
************************************************************************/
BOOL
CSearchSock::SearchLogFileInfo(char* logtbname, int iprojecttype, int ifileid, int itemp)
{	
	char tm_sql[8000];		
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_review_file_info_get '%s', %d,%d,%d",
		logtbname,
		ifileid,
		iprojecttype,
		itemp);
	
	if (0 == itemp)
	{
		m_iRowType_ = TYPE_VEC_NORMAL;
		m_bResponseType_ = RESTYPE_FILE_INFO;
		SEARCHSQL(m_iRowType_, tm_sql);
	}
	else if (1 == itemp)
	{
		m_iRowType_ = TYPE_VEC_NORMAL;
		m_bResponseType_ = RESTYPE_FILE_INFO_MORE;
		SEARCHSQL(m_iRowType_, tm_sql);
	}
	
	return TRUE;
}

BOOL
CSearchSock::SearchLogFileSInfo(const char* logname, int itesttype, char* ifileid, int itemp)
{	
	char tm_sql[8000];		
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_review_files_info_get '%s', '%s', %d, %d",
		logname,
		ifileid,
		itesttype,
		itemp
		);
	
	if (0 == itemp)
	{
		m_iRowType_ = TYPE_VEC_NORMAL;
		m_bResponseType_ = RESTYPE_FILE_INFO;
		if(NULL != m_pDataBaseDeal_){
			m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
			//处理结束
		}	
		Clear();
	}
	else if (1 == itemp)
	{
		m_iRowType_ = TYPE_VEC_NORMAL;
		m_bResponseType_ = RESTYPE_FILE_INFO_MORE;
		if(NULL != m_pDataBaseDeal_){
			m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
			//处理结束
		}
		Clear();
	}	
	return TRUE;
}
/************************************************************************
文件头处理                                                                 
************************************************************************/

BOOL 
CSearchSock::RowResultDeal_TB_Column()
{
	m_vec_temp_int.push_back(PPM_OS::atoi((char*)m_vecColumn[0].pColValue));
	return TRUE;
}





