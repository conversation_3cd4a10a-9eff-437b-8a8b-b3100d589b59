// SearchSock.h: interface for the CSearchSock class.
//
//////////////////////////////////////////////////////////////////////
#pragma once

#include "./CommandDefine.h"
#include "./Search_PI.h"
#include "./EnumDef.h"
#include "InterfaceDef.h"
#include "ConfigSetting.h"
#include "DataItem.h"

#define SEARCHSQL(X,Y) if(nullptr != m_pDataBaseDeal_){\
						m_pDataBaseDeal_->SearchSql(Y,this,X);} \
						Clear()


//////////////////////////////////////////////////////////////////////////
//查询接口父类
//////////////////////////////////////////////////////////////////////////


/************************************************************************
   TYPE OF RowResultDeal
   公用的设置为 0x01 ~ 0x5F 继承子类 0x60 ~ 0xFF 
************************************************************************/

#define TYPE_VEC_TB_LIST					0x01	//获取文件列表
#define TYPE_VEC_FILE_LIST					0x02	//获取文件列表
#define TYPE_VEC_FILE_LIST_SAMPLE			0x03	//获取文件列表
#define TYPE_VEC_FILE_LIST_SAMPLE2          0x04	//获取文件列表
#define TYPE_VEC_DIY_FILE_LIST              0x05    //diy 文件列表
#define TYPE_VEC_TB_INFO		            0x06	//获取文件列表

#define TYPE_VEC_NORMAL     			    0x10
#define TYPE_VEC_SEARCH_DATA_IMAGE          0x11

#define TYPE_VEC_REVIEW_DATA_SUMMARY                0x20  
#define TYPE_VEC_REVIEW_DATA_NORMAL                 0x21    
#define TYPE_VEC_REVIEW_TDSCDMA_DATA_SUMMARY        0x22  
#define TYPE_VEC_REVIEW_TDSCDMA_DATA_NORMAL         0x23  
#define TYPE_VEC_REVIEW_WCDMA_DATA_SUMMARY          0x24  
#define TYPE_VEC_REVIEW_WCDMA_DATA_NORMAL           0x25  
#define TYPE_VEC_REVIEW_CDMA_NORMAL                 0x26
#define TYPE_VEC_LOGREVIEW_COUNT                    0x27
#define TYPE_VEC_GRID_IDLE_RECENT                   0x28
#define TYPE_VEC_GRID_DEDICATED_RECENT              0x29
#define TYPE_VEC_GRID_GPRS_RECENT                   0x2a
#define TYPE_VEC_ES_AREA_EVENT                      0x2c
#define TYPE_VEC_COMPLAIN_GRID                      0x2d	
#define TYPE_VEC_GIS_DATA                           0x2e
#define TYPE_VEC_REVIEW_ULGSM_NORMAL                0x2f  
#define TYPE_VEC_REVIEW_SCAN_WCDMA                  0x30

#define TYPE_VEC_STAT_AREA_DATA                     0x31
#define TYPE_VEC_TALBE_COLUMN                       0x32	



//////////////////////////////////////////////////////////////////////////

class CSearchSock : public ISearchDeal
{

public: 
	CSearchSock();	
	virtual ~CSearchSock();
	
public:

	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);

	
protected:
	/************************************************************************/
	/*参数                                                                  */
	/************************************************************************/

	BYTE m_bResponseType_;	
	int m_iRowType_; 
		
	std::vector<int> m_vec_temp_int;
	
	STRU_TB_INFO m_tb_info;
	std::vector<STRU_TB_LIST> m_vec_tb_list;	
	std::map<STRU_FILE_LIST, std::vector<int> > m_map_filelist; 
	std::map<STRU_FILE_LIST2, std::vector<int> > m_map_filelist2; 

public:
	virtual BOOL RowResultDeal(int nQType);
	virtual BOOL RowResultDeal_Default();
	
protected: //RowResultDeal 
	
	//数据处理函数集
	BOOL (CSearchSock::*pfunc_RowResultDeal[256])();
	void (CSearchSock::*pfunc_AddSendValue[256])(int& ibufoffset,int vecoffset);

	BOOL RowResultDeal_STAT_GIS_DATA();
	BOOL RowResultDeal_STAT_AREA_DATA();
	
	BOOL RowResultDeal_LogReview_Data_Summary();
	BOOL RowResultDeal_LogReview_Data_Normal();
	BOOL RowResultDeal_LogReview_Tdscdma_Data_Normal();
	BOOL RowResultDeal_LogReview_Wcdma_Data_Normal();
	BOOL RowResultDeal_LogReview_ULGSM_Normal();
  BOOL RowResultDeal_LogReview_SCAN_WCDMA();
	
	BOOL RowResultDeal_LogReview_Cdma_Sample_Normal();
	BOOL RowResultDeal_SEARCH_DATA_IMAGE();
	
	
	
	BOOL RowResultDeal_File_List();
	BOOL RowResultDeal_File_List_Sample();
	BOOL RowResultDeal_File_List_Sample2();
	BOOL RowResultDeal_TB_List();
	
	BOOL RowResultDeal_ES_AREA_EVENT_INFO();
	
	BOOL RowResultDeal_COMPLAIN_GRID();

	BOOL RowResultDeal_TB_Column();

	//diy search
	BOOL RowResultDeal_DIY_File_List();

	BOOL RowResultDeal_Normal();
	BOOL RowResultDeal_TB_Info();

public:
	/************************************************************************/
	/* 函数                                                                 */
	/************************************************************************/
	//add value
	void AddTinyIntValue(int& ibufoffset,int vecoffset);
	void AddSmallIntValue(int& ibufoffset,int vecoffset);
	void AddFloatValue(int& ibufoffset,int vecoffset);
	void AddTextValue(int& ibufoffset,int vecoffset);
	void AddIntValue(int& ibufoffset,int vecoffset);
	void AddInt64Value(int& ibufoffset, int vecoffset);
	void AddImageValue(int& ibufoffset,int vecoffset);
	void AddTextMergeValue(int& ibufoffset,int vecoffset);

	void AddDefaultValue(int& ibufoffset,int vecoffset);


	//add value 
	void AddValueByType(int& ibufoffset, int type, byte* p_Data);
		
	//feekback
	void FeedBackOver(BYTE bCmd1,BYTE bresValue);
	void FeedBackOverInt(BYTE bCmd1,int iresValue);
  void FeedBackOverIntWithThirdCmd(BYTE bCmd1,int iresValue);
  void FeedBackOverIntWithThirdCmd(BYTE bCmd1,BYTE bCmd3, int iresValue);

protected:
	BOOL SearchLogFileInfo(char* logtbname, int iprojecttype, int ifileid, int itemp);
	BOOL SearchLogFileSInfo(const char* logtbname, int itesttype, char* ifileid, int itemp);
	
private:
	void (CSearchSock::*pfunc_AddValueByType[256])(const BYTE* const pData, int& ioffset);
	void AddValueByDefault(const BYTE* const pData, int& ioffset);
	void AddValueByTinyInt(const BYTE* const pData, int& ioffset);
	void AddValueBySmallInt(const BYTE* const pData, int& ioffset);
	void AddValueByFloat(const BYTE* const pData, int& ioffset);
	void AddValueByText(const BYTE* const pData, int& ioffset);
	void AddValueByInt(const BYTE* const pData, int& ioffset);

	bool IsStatGisNeedNum(unsigned int num);
	bool IsLogReviewNeedNum(unsigned int num);
	bool IsLogReviewTdscdmaNeedNum(unsigned int num);
	bool IsLogReviewWcdmaNeedNum(unsigned int num);
	bool IsLogReviewCdmaNeedNum(unsigned int num);
	bool IsLogReviewScanNeedNum(unsigned int num);
	bool IsLogReviewSearchNeedNum(unsigned int num);
	bool IsStatAreaNeedNum(unsigned int num);
	void DealStatGisBetween27To90(unsigned int i, BYTE* szDesc, WORD& szDesc_ioffset, WORD& tm_total, int& tm_ioffset);
	void DealStatGisBetween11To18(unsigned int i, BYTE* szDesc, WORD& szDesc_ioffset, WORD& tm_total, int& tm_ioffset);
	void DealStatGisBetween43To98(unsigned int i, BYTE* szDesc, WORD& szDesc_ioffset, WORD& tm_total, int& tm_ioffset);
	void DealStatGisBetween64To167(unsigned int i, BYTE* szDesc, WORD& szDesc_ioffset, WORD& tm_total, int& tm_ioffset);
	void DealStatGisBetween34To225(unsigned int i, BYTE* szDesc, WORD& szDesc_ioffset, WORD& tm_total, int& tm_ioffset);
	void DealStatGisBetween28To179(unsigned int i, BYTE* szDesc, WORD& szDesc_ioffset, WORD& tm_total, int& tm_ioffset);
	void DealStatGisBetween36To51(unsigned int i, BYTE* szDesc, WORD& szDesc_ioffset, WORD& tm_total, int& tm_ioffset);
	void DealStatGisBetween7To46(unsigned int i, BYTE* szDesc, WORD& szDesc_ioffset, WORD& tm_total, int& tm_ioffset);
	void DealStatGisBetween29To52(unsigned int i, char* szDesc, int& tm_ioffset);
	void DealStatGisBetween28To88(unsigned int i, BYTE* szDesc, WORD& szDesc_ioffset, WORD& tm_total, int& tm_ioffset);
	void BuildSendBufHeadCmdCode(int& tm_ioffset);
};



