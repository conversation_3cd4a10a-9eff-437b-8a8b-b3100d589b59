// Search_PI.cpp: implementation of the CSearch_PI class.
//
//////////////////////////////////////////////////////////////////////

#include "Search_PI.h"
#include "DataBaseDeal.h"
#include "CommandDefine.h"
#include "DtdrvApp_Public_Var.h"

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CSearch_PI::CSearch_PI()
{

}

CSearch_PI::~CSearch_PI()
{

}

void 
CSearch_PI::FeedBackOver(BYTE bCmd1,BY<PERSON> bresValue,PPM_Server_Deal_Base* const pServerDeal_)
{
    BYTE pSendBuf_[10000];

	PPM_DEBUG((LM_INFO,"deal over!\n"));
	if(NULL != pServerDeal_){
		PPM_OS::memset(pSendBuf_,0,sizeof(pSendBuf_));
		
		//构造信息发送
		int tm_ioffset = 0;
		pSendBuf_[tm_ioffset++] = bCmd1;//CMD1_SEARCH;//命令字
		pSendBuf_[tm_ioffset++] = CMD2_RESPONSE;//子命令字
		pSendBuf_[tm_ioffset++] = bresValue;//RESTYPE_SEARCHEND;//响应类型查询结束
		pServerDeal_->SendData(pSendBuf_,tm_ioffset);
	}	
}

void 
CSearch_PI::FeedBackOver(BYTE bCmd1,BYTE bresValue,BYTE* const pSendBuf_,PPM_Server_Deal_Base* const pServerDeal_)
{
	PPM_DEBUG((LM_INFO,"deal over!\n"));
	if(NULL != pServerDeal_){
		PPM_OS::memset(pSendBuf_,0,sizeof(pSendBuf_));

		//构造信息发送
		int tm_ioffset = 0;
		pSendBuf_[tm_ioffset++] = bCmd1;//CMD1_SEARCH;//命令字
		pSendBuf_[tm_ioffset++] = CMD2_RESPONSE;//子命令字
		pSendBuf_[tm_ioffset++] = bresValue;//RESTYPE_SEARCHEND;//响应类型查询结束
		pServerDeal_->SendData(pSendBuf_,tm_ioffset);
	}	
}

void 
CSearch_PI::FeedBackOverInt(BYTE bCmd1,int iresValue,BYTE* const pSendBuf_,PPM_Server_Deal_Base* const pServerDeal_)
{
	PPM_DEBUG((LM_INFO,"deal over!\n"));
	if(NULL != pServerDeal_){
		PPM_OS::memset(pSendBuf_,0,sizeof(pSendBuf_));

		iresValue = MAKEINT_NETSEQ1(iresValue);
		//构造信息发送
		int tm_ioffset = 0;
		pSendBuf_[tm_ioffset++] = bCmd1;//CMD1_SEARCH;//命令字
		pSendBuf_[tm_ioffset++] = CMD2_RESPONSE;//子命令字
		PPM_OS::memcpy(pSendBuf_ + tm_ioffset,&iresValue,4);
		tm_ioffset += 4;
		pServerDeal_->SendData(pSendBuf_,tm_ioffset);
	}	
}

void 
CSearch_PI::FeedBackOverIntWithThirdCmd(BYTE bCmd1,int iresValue,BYTE* const pSendBuf_,PPM_Server_Deal_Base* const pServerDeal_)
{
	PPM_DEBUG((LM_INFO,"deal over!\n"));
	if(NULL != pServerDeal_){
		PPM_OS::memset(pSendBuf_,0,sizeof(pSendBuf_));
		
		iresValue = MAKEINT_NETSEQ1(iresValue);
		//构造信息发送
		int tm_ioffset = 0;
		pSendBuf_[tm_ioffset++] = bCmd1;//CMD1_SEARCH;//命令字
		pSendBuf_[tm_ioffset++] = CMD2_RESPONSE;//子命令字
		tm_ioffset += 1;
		PPM_OS::memcpy(pSendBuf_ + tm_ioffset,&iresValue,4);
		tm_ioffset += 4;
		pServerDeal_->SendData(pSendBuf_,tm_ioffset);
	}	
}


BOOL  
CSearch_PI::AddIntToBuf(BYTE* pBuf,int& ioffset,int ivalue, int MaxLenth)
{
	int tm_int = ivalue;
	tm_int = MAKEINT_NETSEQ1(tm_int);
	MOVENEXTTEST(ioffset, 4, MaxLenth);
	PPM_OS::memcpy(pBuf+ioffset,&tm_int,4);
	ioffset += 4;
	return TRUE;
}
BOOL  
CSearch_PI::AddWORDToBuf(BYTE* pBuf,int& ioffset,int ivalue, int MaxLenth)
{
	WORD tm_word = ivalue;
	tm_word = MAKEWORD_NETSEQ1(tm_word);
	MOVENEXTTEST(ioffset, 2, MaxLenth);
	PPM_OS::memcpy(pBuf+ioffset,&tm_word,2);
	ioffset += 2;
	return TRUE;
}

BOOL  
CSearch_PI::AddStringToBuf(BYTE* pBuf,int& ioffset,std::string strtarget, int MaxLenth)
{
	WORD tm_word = strtarget.size();
	char tm_pstr[255];
	PPM_OS::snprintf(tm_pstr,sizeof(tm_pstr),"%s",strtarget.c_str());
	WORD tm_wordsend = MAKEWORD_NETSEQ1(tm_word);
	MOVENEXTTEST(ioffset, tm_word+2, MaxLenth);
	PPM_OS::memcpy(pBuf + ioffset,&tm_wordsend,2);
	ioffset += 2;
	PPM_OS::memcpy(pBuf + ioffset,tm_pstr,tm_word);
	ioffset += tm_word;
	return TRUE;
}

BOOL
CSearch_PI::GetStringFromBuf(const BYTE* const pBuf,int& ioffset,int nCount,std::string& strtarget)
{
	WORD tm_word = 0;
	MOVENEXTTEST(ioffset,2,nCount);
	PPM_OS::memcpy(&tm_word,pBuf+ioffset,2);
	tm_word = MAKEWORD_NETSEQ1(tm_word);
	MOVENEXT(ioffset,2,nCount);
	char tm_pstr[255];
	PPM_OS::memset(tm_pstr,0,sizeof(tm_pstr));
	MOVENEXTTEST(ioffset,tm_word,nCount);
	PPM_OS::memcpy(tm_pstr,pBuf+ioffset,tm_word);
	strtarget = tm_pstr;
	MOVENEXT(ioffset,tm_word,nCount);
	return TRUE;
}


BOOL
CSearch_PI::GetWordFromBuf(const BYTE* const pBuf,int& ioffset,int nCount,WORD& tm_word)
{
	MOVENEXTTEST(ioffset,2,nCount);
	PPM_OS::memcpy(&tm_word,pBuf+ioffset,2);
	tm_word = MAKEWORD_NETSEQ1(tm_word);
	MOVENEXT(ioffset,2,nCount);

	return TRUE;
}


BOOL  
CSearch_PI::GetIntFromBuf(const BYTE* const pBuf,int& ioffset,int nCount,int& ivalue)
{
	MOVENEXTTEST(ioffset,4,nCount);
	PPM_OS::memcpy(&ivalue,pBuf+ioffset,4);
	ivalue = MAKEINT_NETSEQ1(ivalue);
	MOVENEXT(ioffset,4,nCount);
	return TRUE;
}


BOOL 
CSearch_PI::GetEventIdList(const BYTE* const pData, const int nCount,
						   int& ioffset,char* targetstr)
{
	int itemp;
	
	char tm_sql_part[512];
	memset(&tm_sql_part, 0, sizeof(tm_sql_part));
	
	PPM_OS::memcpy(&itemp,pData + ioffset,4);
	itemp = MAKEINT_NETSEQ1(itemp);		
	if(-1 == itemp)
	{
		MOVENEXT(ioffset,4,nCount)
	}
	else
	{
		memset(&tm_sql_part, 0, sizeof(tm_sql_part));
		if(FALSE == GetTextValue_int(pData,nCount,ioffset,tm_sql_part)){
			return FALSE;
		}
		PPM_OS::snprintf(targetstr,1024,"%s %s (%s)", targetstr, " and iEventID in ", tm_sql_part);
	}
	return true;
}

BOOL 
CSearch_PI::GetTypeSql(const BYTE* const pData, const int nCount,
						   int& ioffset,char* targetstr)
{
	if (!MakeTargetStr(pData, nCount, ioffset, targetstr, (char*)" and iprojecttype in "))
	{
		return FALSE;
	}
	
	if (!MakeTargetStr(pData, nCount, ioffset, targetstr, (char*)" and idevicetype in "))
	{
		return FALSE;
	}

	if (!MakeTargetStr(pData, nCount, ioffset, targetstr, (char*)" and iservicetype in "))
	{
		return FALSE;
	}
	
	if (!MakeTargetStr(pData, nCount, ioffset, targetstr, (char*)" and iagentid in "))
	{
		return FALSE;
	}
	
	if (!MakeTargetStr(pData, nCount, ioffset, targetstr, (char*)" and icarriertype in "))
	{
		return FALSE;
	}
		
	return TRUE;
}

BOOL CSearch_PI::MakeTargetStr(const BYTE* const pData, const int nCount, int& ioffset,char* targetstr, char* c_type)
{
	BYTE btemp = 0xff;
	PPM_OS::memcpy(&btemp,pData + ioffset,1);
	if(0xff == btemp || 0 == btemp)
	{
		MOVENEXT(ioffset,1,nCount)
	}
	else
	{
		char tm_sql_part[512];
		memset(tm_sql_part, 0, sizeof(tm_sql_part));
		if(FALSE == GetTextValue_char(pData,nCount,ioffset,tm_sql_part)){
			return FALSE;
		}
		PPM_OS::snprintf(targetstr,1024,"%s %s (%s)", targetstr, c_type, tm_sql_part);
	}
    return TRUE;
}

BOOL 
CSearch_PI::GetTypeSql_LN(const BYTE* const pData, const int nCount,
						   int& ioffset,char* targetstr, std::map<int, std::vector<int> >* p_map_area_list)
{
	if (!MakeTargetStr_Ln(pData, nCount, ioffset, targetstr, (char*)" and iprojecttype in "))
	{
		return FALSE;
	}
	
	if (!MakeTargetStr_Ln(pData, nCount, ioffset, targetstr, (char*)" and idevicetype in "))
	{
		return FALSE;
	}

	if (!MakeTargetStr_Ln(pData, nCount, ioffset, targetstr, (char*)" and iservicetype in "))
	{
		return FALSE;
	}
	
	if (!MakeTargetStr_Ln(pData, nCount, ioffset, targetstr, (char*)" and iagentid in "))
	{
		return FALSE;
	}	

	if (!MakeTargetStr_Ln(pData, nCount, ioffset, targetstr, (char*)" and icarriertype in "))
	{
		return FALSE;
	}
	
/**************************************deal area search***************************************/
	int itemp = 0;
	PPM_OS::memcpy(&itemp,pData + ioffset,4);
	
	itemp = MAKEINT_NETSEQ1(itemp);

	if(-1 == itemp)
	{
		MOVENEXT(ioffset,4,nCount)
	}
	else
	{
		MOVENEXT(ioffset,4,nCount)

		int iareatype = 0;
		int iareaid = 0;

		for(int i=0; i<itemp; i++)
		{
			PPM_OS::memcpy(&iareatype,pData + ioffset,4);
			MOVENEXT(ioffset,4,nCount)
			
			PPM_OS::memcpy(&iareaid,pData + ioffset,4);
			MOVENEXT(ioffset,4,nCount)

			iareatype = MAKEINT_NETSEQ1(iareatype);
			iareaid = MAKEINT_NETSEQ1(iareaid);
			
			(*p_map_area_list)[iareatype].push_back(iareaid);
		}
	}
	
/**************************************deal area search***************************************/
	
	return TRUE;
}

BOOL CSearch_PI::MakeTargetStr_Ln(const BYTE* const pData, const int nCount, int& ioffset,char* targetstr, char* c_type)
{
	BYTE btemp = 0xff;
	PPM_OS::memcpy(&btemp,pData + ioffset,1);
	if(0xff == btemp)
	{
		MOVENEXT(ioffset,1,nCount)
	}
	else
	{
		char tm_sql_part[1024];
		memset(tm_sql_part, 0, sizeof(tm_sql_part));
		if(FALSE == GetTextValue_char(pData,nCount,ioffset,tm_sql_part)){
			return FALSE;
		}
		PPM_OS::snprintf(targetstr,1024,"%s %s (%s)", targetstr, c_type, tm_sql_part);
	}
    return TRUE;
}

BOOL 
CSearch_PI::GetTypeSql4(const BYTE* const pData, const int nCount,
						   int& ioffset,char* targetstr)
{
    if (!MakeTargetStr4(pData, nCount, ioffset, targetstr, (char*)" and iprojecttype in "))
    {
		return FALSE;
    }
	
	if (!MakeTargetStr4(pData, nCount, ioffset, targetstr, (char*)" and idevicetype in "))
	{
		return FALSE;
	}	
	
	if (!MakeTargetStr4(pData, nCount, ioffset, targetstr, (char*)" and iservicetype in "))
	{
		return FALSE;
	}
	
	if (!MakeTargetStr4(pData, nCount, ioffset, targetstr, (char*)" and itesttype in "))
	{
		return FALSE;
	}
	
	if (!MakeTargetStr4(pData, nCount, ioffset, targetstr, (char*)" and icarriertype in "))
	{
		return FALSE;
	}
	
	return TRUE;
}

BOOL CSearch_PI::MakeTargetStr4(const BYTE* const pData, const int nCount, int& ioffset,char* targetstr, char* c_type)
{
	int itemp;
	
	PPM_OS::memcpy(&itemp,pData + ioffset,4);
	itemp = MAKEINT_NETSEQ1(itemp);		
	if(-1 == itemp)
	{
		MOVENEXT(ioffset,4,nCount)
	}
	else
	{
		char tm_sql_part[512];
		memset(tm_sql_part, 0, sizeof(tm_sql_part));
		if(FALSE == GetTextValue_int(pData,nCount,ioffset,tm_sql_part)){
			return FALSE;
		}
		PPM_OS::snprintf(targetstr,1024,"%s %s (%s)", targetstr, c_type, tm_sql_part);
	}
	return TRUE;
}

BOOL 
CSearch_PI::GetTextValue_word(const BYTE* const pData, const int nCount,
							 int& ioffset,char* targetstr,int targetlen)
{
	MOVENEXTTEST(ioffset,2,nCount);
	WORD tm_len;
	PPM_OS::memcpy(&tm_len,pData + ioffset,2);
	tm_len = MAKEWORD_NETSEQ1(tm_len);

	MOVENEXT(ioffset,2,nCount);
	MOVENEXTTEST(ioffset,tm_len,nCount);
	if(tm_len > targetlen){
		return FALSE;
	}

	PPM_OS::memcpy(targetstr,pData + ioffset,tm_len);
	MOVENEXT(ioffset,tm_len,nCount);
	return TRUE;
}


BOOL 
CSearch_PI::GetTextValue_char(const BYTE* const pData, const int nCount, int& ioffset,char* targetstr)
{
	BYTE btemp ;
	PPM_OS::memcpy(&btemp,pData + ioffset,1);
	
	int tm_len = btemp & 0x000000ff;
	MOVENEXT(ioffset,1,nCount);
	MOVENEXTTEST(ioffset,tm_len,nCount);
	for(int tm_i = 0 ;tm_i < tm_len ;tm_i++)
	{
		if(0 == tm_i)
		{
			PPM_OS::memset(targetstr,0,PPM_OS::strlen(targetstr));
			PPM_OS::snprintf(targetstr,255,"%d",pData[ioffset+tm_i]);
		}
		else
		{
			PPM_OS::snprintf(targetstr,255,"%s,%d",targetstr,pData[ioffset+tm_i]);
		}
	}
	MOVENEXT(ioffset,tm_len,nCount);
	
	return TRUE;
}

BOOL 
CSearch_PI::GetTextValue_int(const BYTE* const pData, const int nCount,
							  int& ioffset,char* targetstr)
{
	int itemp ;
	PPM_OS::memcpy(&itemp,pData + ioffset,4);
	itemp = MAKEINT_NETSEQ1(itemp);
	
	int tm_len = itemp;
	MOVENEXT(ioffset,4,nCount);
	
	int tm_i = 0;
	MOVENEXTTEST(ioffset,tm_len*4,nCount);
	
	for(tm_i = 0 ;tm_i < tm_len ;tm_i++)
	{
		if(0 == tm_i)
		{
			PPM_OS::memset(targetstr,0,PPM_OS::strlen(targetstr));
			
			PPM_OS::memcpy(&itemp,pData + ioffset + tm_i*4,4);
			itemp = MAKEINT_NETSEQ1(itemp);
			PPM_OS::snprintf(targetstr,255,"%d",itemp);
		}
		else
		{
			PPM_OS::memcpy(&itemp,pData + ioffset + tm_i*4,4);
			itemp = MAKEINT_NETSEQ1(itemp);
			PPM_OS::snprintf(targetstr,255,"%s,%d",targetstr,itemp);
		}
	}
	MOVENEXT(ioffset,tm_len*4,nCount);
	
	return TRUE;
}

BOOL 
CSearch_PI::GetPointList(const BYTE* const pData, const int nCount,
							  int& ioffset,char* targetstr)
{
	int itemp ;
	PPM_OS::memcpy(&itemp,pData + ioffset,4);
	itemp = MAKEINT_NETSEQ1(itemp);

	int tm_len = itemp;
	MOVENEXT(ioffset,4,nCount);

	int tm_i = 0;
	MOVENEXTTEST(ioffset,tm_len*8,nCount);


	for ( tm_i=0; tm_i<tm_len; tm_i++)
	{
		STRU_SEARCH_AREA tm_Search_Area;
		//longitude and latitude
		PPM_OS::memcpy(&tm_Search_Area.itllongitude,pData + ioffset,4);
		PPM_OS::memcpy(&tm_Search_Area.itllongitude,pData + ioffset + tm_i*8,4);

		PPM_OS::memcpy(&tm_Search_Area.itllatitude,pData + ioffset,4);
		PPM_OS::memcpy(&tm_Search_Area.itllongitude,pData + ioffset + tm_i*8+4,4);

		tm_Search_Area.ConvertToNetSeq();
        
		if (0 == tm_i)
		{	
			PPM_OS::sprintf(targetstr, "%d,%d", tm_Search_Area.itllongitude, tm_Search_Area.itllatitude);
		}
		else 
		{
			PPM_OS::sprintf(targetstr, "%s,%d,%d", &targetstr, tm_Search_Area.itllongitude, tm_Search_Area.itllatitude);
		}	
	}
	MOVENEXTTEST(ioffset,tm_len*8,nCount);
    return true;
};


BOOL 
CSearch_PI::AddTinyIntToBuf( BYTE* const pBuf, int& ibufoffset, const BYTE* const pData)
{
	pBuf[ibufoffset++] = pData[0];
	return TRUE;
}

BOOL 
CSearch_PI::AddSmallIntToBuf(BYTE* const pBuf, int& ibufoffset, const BYTE* const pData)
{
	WORD tm_word;
	tm_word = (*(WORD*)pData);
	tm_word = MAKEWORD_NETSEQ1(tm_word);
	PPM_OS::memcpy(pBuf + ibufoffset,&tm_word,2);
	ibufoffset += 2;
	return TRUE;
}

BOOL 
CSearch_PI::AddUSmallIntToBuf(BYTE* const pBuf, int& ibufoffset, const BYTE* const pData)
{
	unsigned short tm_word;
	tm_word = (*(unsigned short*)pData);
	tm_word = MAKEWORD_NETSEQ1(tm_word);
	PPM_OS::memcpy(pBuf + ibufoffset,&tm_word,2);
	ibufoffset += 2;
	return TRUE;
}

BOOL 
CSearch_PI::AddFloatToBuf(BYTE* const pBuf, int& ibufoffset, const BYTE* const pData)
{
	float tm_double = (*(float*)pData);
	int tm_int = (int)(tm_double * 1000);
	tm_int = MAKEINT_NETSEQ1(tm_int);
	PPM_OS::memcpy(pBuf+ibufoffset,&tm_int,4);
	ibufoffset += 4;
	return TRUE;
}

BOOL 
CSearch_PI::AddIntFloatToBuf(BYTE* const pBuf, int& ibufoffset, const BYTE* const pData)
{
	int tm_int;
	tm_int = (*(int*)pData);
	tm_int = MAKEINT_NETSEQ1(tm_int);
	PPM_OS::memcpy(pBuf+ibufoffset,&tm_int,4);
	ibufoffset += 4;
	return TRUE;
}

BOOL  
CSearch_PI::AddUInt64ToBuf(BYTE* const pBuf, int& ibufoffset, const BYTE* const pData)
{
	UINT64 tm_int;
	tm_int = (*(UINT64*)pData);
	tm_int = MAKEINT64_NETSEQ1(tm_int);
	PPM_OS::memcpy(pBuf+ibufoffset,&tm_int,8);
	ibufoffset += 8;
	return TRUE;
}

BOOL  
CSearch_PI::AddInt64ToBuf(BYTE* const pBuf, int& ibufoffset, const BYTE* const pData)
{
	INT64 tm_int;
	tm_int = (*(INT64*)pData);
	tm_int = MAKEINT64_NETSEQ1(tm_int);
	PPM_OS::memcpy(pBuf+ibufoffset,&tm_int,8);
	ibufoffset += 8;
	return TRUE;
}

BOOL  
CSearch_PI::AddTextToBuf(BYTE* const pBuf, int& ibufoffset, const BYTE* const pData)
{
	WORD tm_len = PPM_OS::strlen(((char*)pData));
	WORD tm_lensave = MAKEWORD_NETSEQ1(tm_len);
	PPM_OS::memcpy(pBuf + ibufoffset,&tm_lensave,2);
	PPM_OS::memcpy(pBuf + ibufoffset + 2,pData,tm_len);
	ibufoffset += (tm_len + 2);	
	return TRUE;
}

BOOL  
CSearch_PI::AddIntToBuf(BYTE* const pBuf, int& ibufoffset, const BYTE* const pData)
{
	int tm_int;
	tm_int = (*(int*)pData);
	tm_int = MAKEINT_NETSEQ1(tm_int);
	PPM_OS::memcpy(pBuf+ibufoffset,&tm_int,4);
	ibufoffset += 4;
	return TRUE;
}

BOOL  
CSearch_PI::AddImageToBuf(BYTE* const pBuf, int& ibufoffset, const BYTE* const pData)
{
	WORD tm_total;
	PPM_OS::memcpy(&tm_total,pData, 2);
	tm_total = REVERTWORD_NETSEQ1(tm_total);
	WORD tm_lensave = MAKEWORD_NETSEQ1(tm_total);
	PPM_OS::memcpy(pBuf + ibufoffset,&tm_lensave,2);
	PPM_OS::memcpy(pBuf + ibufoffset + 2,pData + 2,tm_total);	
	ibufoffset += tm_total+2;
	return TRUE;
}

BOOL  
CSearch_PI::AddTextMergeToBuf(BYTE* const pBuf, int& ibufoffset, const BYTE* const pData)
{
	char szDesc[1024];
	PPM_OS::memset(&szDesc, 0, sizeof(szDesc));
	char sztmp[300];
	int tm_len = 0;

	for (int i=0; i<8; i++)
	{
		tm_len = PPM_OS::strlen(((char*)pData + i * 300));
		if (0 == tm_len)
		{
			break;
		}
		PPM_OS::memset(&sztmp, 0, sizeof(sztmp));
		PPM_OS::memcpy(sztmp, pData + i * 300, tm_len);
		PPM_OS::snprintf(szDesc,1024,"%s%s",szDesc,sztmp);
	}

	WORD tm_total = PPM_OS::strlen(szDesc);
	WORD tm_lensave = MAKEWORD_NETSEQ1(tm_total);
	PPM_OS::memcpy(pBuf + ibufoffset,&tm_lensave,2);
	PPM_OS::memcpy(pBuf + ibufoffset + 2,szDesc,tm_total);
	ibufoffset += tm_total+2;
	return TRUE;
}

// BOOL
// CSearch_PI::SearchBySqlCmd(const char* p_Sql, const char* pchUserName, const char* pchUserPassword, const char* pchDBServer)
// {
// 	/*****************************************************************************/	
// 	char sztmp[512] = "";
// 	LPSTR lpstr;
// 	PROCESS_INFORMATION   pidInfo;
// 	STARTUPINFO           startInfo;

// 	GetStartInfo(startInfo);

// 	PPM_OS::memset(sztmp, 0, sizeof(sztmp));
// 	PPM_OS::snprintf(sztmp,512,"sqlcmd -q\"%s\" -U%s -P%s -S%s",
// 		p_Sql,
// 		pchUserName,
// 		pchUserPassword,
// 		pchDBServer
// 		);	

// 	lpstr = (LPSTR)sztmp;
// 	BOOL bRes = CreateProcess(NULL,lpstr,NULL,NULL,TRUE,CREATE_NO_WINDOW,NULL,NULL,&startInfo,&pidInfo);
// 	WaitForSingleObject(pidInfo.hProcess,INFINITE);

// 	CloseHandle(pidInfo.hProcess);
// 	CloseHandle(pidInfo.hThread);	
// 	/*****************************************************************************/

// 	return TRUE;
// }

// void CSearch_PI::GetStartInfo(STARTUPINFO& startInfo)
// {
// 	startInfo.cb = sizeof(STARTUPINFO);
// 	startInfo.lpReserved = NULL;
// 	startInfo.lpTitle = NULL;
// 	startInfo.lpDesktop = NULL;
// 	startInfo.dwFlags = STARTF_USESTDHANDLES;
// 	startInfo.dwX = 0;
// 	startInfo.dwY = 0;
// 	startInfo.dwXSize = 0;
// 	startInfo.dwYSize = 0;
// 	startInfo.dwXCountChars = 0;
// 	startInfo.dwYCountChars = 0;
// 	startInfo.wShowWindow = 0;
// 	startInfo.lpReserved2 = NULL;
// 	startInfo.cbReserved2 = 0;
// 	startInfo.hStdError = GetStdHandle(STD_ERROR_HANDLE);  
// 	startInfo.hStdInput = GetStdHandle(STD_INPUT_HANDLE);
// 	startInfo.hStdOutput = GetStdHandle(STD_OUTPUT_HANDLE);
// }


