// Search_PI.h: interface for the CSearch_PI class.
//
//////////////////////////////////////////////////////////////////////

#ifndef __SEARCH_PI_H__
#define __SEARCH_PI_H__

#include "StdHeader.h"
#include "PPM_CONN_Actor.h"
#include "SearchHead.h"
#include "Macro.h"
// #include "../src/ParaResult.h"

class CSearch_PI  
{
public:
	CSearch_PI();
	virtual ~CSearch_PI();

	static void FeedBackOver(BYTE bCmd1,BYTE bresValue,PPM_Server_Deal_Base* const pServerDeal_);
	static void FeedBackOver(BYTE bCmd1,BYTE bresValue,BYTE* const pSendBuf,PPM_Server_Deal_Base* const pServerDeal);
	static void FeedBackOverInt(BYTE bCmd1,int iresValue,BYTE* const pSendBuf,PPM_Server_Deal_Base* const pServerDeal);
	static void FeedBackOverIntWithThirdCmd(BYTE bCmd1,int iresValue,BYTE* const pSendBuf,PPM_Server_Deal_Base* const pServerDeal);

	static BOOL AddIntToBuf(BYTE* pBuf,int& ioffset,int ivalue, int MaxLenth);
	static BOOL AddWORDToBuf(BYTE* pBuf,int& ioffset,int ivalue, int MaxLenth);	
	static BOOL AddStringToBuf(BYTE* pBuf,int& ioffset,std::string strtarget, int MaxLenth);

	static BOOL AddDataToBuf(const int dataType, BYTE* const pBuf, int& ioffset, const BYTE* const pData, const int MAXlENTH);
	// static BOOL AddDataToBuf(const int dataType, BYTE* const pBuf, int& ioffset, STRU_ParaResult* m_Result_, const int MAXlENTH);
  static BOOL AddTinyIntToBuf( BYTE* const pBuf, int& ioffset, const BYTE* const pData);
	static BOOL AddSmallIntToBuf( BYTE* const pBuf, int& ioffset, const BYTE* const pData); 
	static BOOL AddUSmallIntToBuf( BYTE* const pBuf, int& ioffset, const BYTE* const pData); 
  static BOOL AddFloatToBuf( BYTE* const pBuf, int& ioffset, const BYTE* const pData);
	static BOOL AddIntFloatToBuf( BYTE* const pBuf, int& ioffset, const BYTE* const pData);
	static BOOL AddUInt64ToBuf( BYTE* const pBuf, int& ioffset, const BYTE* const pData);
	static BOOL AddInt64ToBuf( BYTE* const pBuf, int& ioffset, const BYTE* const pData);
	static BOOL AddTextToBuf( BYTE* const pBuf, int& ioffset, const BYTE* const pData);
	static BOOL AddIntToBuf( BYTE* const pBuf, int& ioffset, const BYTE* const pData);
	static BOOL AddImageToBuf( BYTE* const pBuf, int& ioffset, const BYTE* const pData);
	static BOOL AddTextMergeToBuf( BYTE* const pBuf, int& ioffset, const BYTE* const pData);
	
	static BOOL GetTypeSql(const BYTE* const pData, const int nCount,int& ioffset,char* targetstr);
	static BOOL GetEventIdList(const BYTE* const pData, const int nCount,int& ioffset,char* targetstr);
	static BOOL GetTypeSql_LN(const BYTE* const pData, const int nCount,int& ioffset,char* targetstr, std::map<int, std::vector<int> >* p_map_area_list);
	static BOOL GetTypeSql4(const BYTE* const pData, const int nCount,int& ioffset,char* targetstr);
  static BOOL GetPointList(const BYTE* const pData, const int nCount,int& ioffset,char* targetstr);


	static BOOL GetTextValue_word(const BYTE* const pData, const int nCount,int& ioffset,char* targetstr,int targetlen);
	static BOOL GetTextValue_char(const BYTE* const pData, const int nCount,int& ioffset,char* targetstr);
	static BOOL GetTextValue_int(const BYTE* const pData, const int nCount,int& ioffset,char* targetstr);
	static BOOL GetIntFromBuf(const BYTE* const pBuf,int& ioffset,int nCount,int& ivalue);
	static BOOL GetStringFromBuf(const BYTE* const pBuf,int& ioffset,int nCount,std::string& strtarget);
	static BOOL GetWordFromBuf(const BYTE* const pBuf,int& ioffset,int nCount,WORD& strtarget);

	// static BOOL SearchBySqlCmd(const char* p_Sql, const char* pchUserName, const char* pchUserPassword, const char* pchDBServer);

private:
	static BOOL MakeTargetStr(const BYTE* const pData, const int nCount, int& ioffset,char* targetstr, char* c_type);
	static BOOL MakeTargetStr4(const BYTE* const pData, const int nCount, int& ioffset,char* targetstr, char* c_type);
	static BOOL MakeTargetStr_Ln(const BYTE* const pData, const int nCount, int& ioffset,char* targetstr, char* c_type);
	// static void GetStartInfo(STARTUPINFO& startInfo);

};

#endif 
