// SockFactory.cpp: implementation of the CSockFactory class.
//
//////////////////////////////////////////////////////////////////////

#include "./SockFactory.h"

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CSockFactory::CSockFactory()
{

}

CSockFactory::~CSockFactory()
{
	for (auto p_ClassPool = m_mapSockDealTotalProxy.begin();
		p_ClassPool != m_mapSockDealTotalProxy.end(); ++p_ClassPool)
     {
		 delete p_ClassPool->second;
     }
	m_mapSockDealTotalProxy.clear();
}











