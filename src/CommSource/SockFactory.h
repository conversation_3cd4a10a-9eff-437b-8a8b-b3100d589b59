// SockFactory.h: interface for the CSockFactory class.
//
//////////////////////////////////////////////////////////////////////



#ifndef __SOCKFACTORY_H__
#define __SOCKFACTORY_H__

#include "./InterfaceDef.h"
#include "./ConfigSetting.h"


#define DEALSOCK(X)  string print_info = "SearchType: "; \
                     print_info +=  #X; \
                     PPM_DEBUG((LM_INFO, "%s \n", print_info.c_str()));\
					 ##X* ClassTemp = Pool_##X.PopInstance();\
                     if( TRUE == ClassTemp->Init(sockInfo.pDataBaseDeal_, sockInfo.pServerDeal_, sockInfo.pSendBuf_))\
					 {ClassTemp->DealData(pData, nCount,ioffset);}\
					 Pool_##X.PushInstance(ClassTemp)

#define DEALDATA(X)  string print_info = "SearchType: ";print_info +=  #X; \
					PPM_DEBUG((LM_INFO, "%s \n", print_info.c_str()));\
					if(m_mapSockDealTotalProxy.find(#X) == m_mapSockDealTotalProxy.end()){\
					m_mapSockDealTotalProxy[#X] = (ClassPool<ISearchDeal>*)(new ClassPool<##X>());}\
                    ##X* ClassTemp = ((ClassPool<##X>*)m_mapSockDealTotalProxy[#X])->PopInstance();\
                    if( TRUE == ClassTemp->Init(sockInfo.pDataBaseDeal_, sockInfo.pServerDeal_, sockInfo.pSendBuf_)){\
					ClassTemp->DealData(pData, nCount,ioffset);}\
					m_mapSockDealTotalProxy[#X]->PushInstance(ClassTemp)						

#define DEALDATA2(X, Y)  std::string print_info = "";\
						print_info += "[";\
						print_info += Y ;\
						print_info += "]";\
						print_info += "SearchType: ";\
						print_info +=  #X;\
						PPM_DEBUG((LM_INFO, "%s \n", print_info.c_str()));\
						X ClassTemp;\
            if( TRUE == ClassTemp.Init(sockInfo.pDataBaseDeal_, sockInfo.pServerDeal_, sockInfo.pSendBuf_)){\
						ClassTemp.DealData(pData, nCount,ioffset);}\
						print_info = "";\
						print_info += "[";\
						print_info += Y ;\
						print_info += "]";\
						print_info += "End Search: ";\
						print_info +=  #X;\
						PPM_DEBUG((LM_INFO, "%s \n", print_info.c_str()))


//////////////////////////////////////////////////////////////////////////
//查询接口工厂父类
//////////////////////////////////////////////////////////////////////////

//多线程单例不应该具有属性
class CSockFactory
{     
public:
	CSockFactory();
	virtual ~CSockFactory();

protected:
	 std::map<std::string, ClassPool<ISearchDeal>*> m_mapSockDealTotalProxy;

public:

	//处理查询分类
    virtual BOOL DealSock(const BYTE* const pData, const int nCount,int& ioffset, const STRU_SockInfo& sockInfo) = 0;

};

#endif
