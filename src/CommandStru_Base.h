#ifndef __DT_COMMANDSTRU_BASE_H__
#define __DT_COMMANDSTRU_BASE_H__

#include "StdHeader.h"
#include "Strutils.h"
#include "Utility.h"

#define DIR_UNKNOW 0
#define DIR_DOWN   1
#define DIR_UP     2
#define LINE_RECORD_LEN 8192
#define SQL_LEN (1024 * 9) //要比信令的buffer大

#define READ_FILE_OK(nCount) if (nCount > (LINE_RECORD_LEN * 8)){strcpy(m_pWorkBox->m_tFileInfo.tPath.szcomment, "巨包数据");\
    return STATSTATUS_HUGE_PACKET;}\
    if (m_rFile.Read(m_pWorkBox->m_pData, nCount) != nCount) {strcpy(m_pWorkBox->m_tFileInfo.tPath.szcomment, "read file error");\
    return STATSTATUS_UNKNOWN_VERSION;}
#define READ_FILE_OKBOOL(nCount) if (nCount > (LINE_RECORD_LEN * 8)){strcpy(m_pWorkBox->m_tFileInfo.tPath.szcomment, "巨包数据");\
	return false;}\
	if (m_rFile.Read(m_pWorkBox->m_pData, nCount) != nCount) {strcpy(m_pWorkBox->m_tFileInfo.tPath.szcomment, "read file error");\
	return false;}
	
#define INT_IS_VALIDE(iValue) if ((iValue) != (-9999)) //0xFFFFD8F1 
#define SHORTS_IS_VALIDE(wValue) if ((wValue) != (-9999)) //0xD8F1
#define CLEAR_DOT(dotExt) if(dotExt != nullptr) {delete dotExt; dotExt = nullptr;}
#define OCX_BSIC(nValue) ((((BYTE)(nValue)) & 0x07) + (((((BYTE)(nValue)) & 0x38) >> 3) * 10))
#define PRINT_LOG(szlog) CStdConsole::OutPut(szlog);	CStdConsole::OutPut("\n");
#define MAKE_APPMSGID(apptype, appstatus) (0x7FFF0000 + (apptype << 8) + appstatus)
/**==============================
#define COMPARE_TIME(nSec, nMSec) (((nSec) == m_pWorkBox->m_pUnion->tCommInfo.Time) && ((nMSec) == m_pWorkBox->m_pUnion->tCommInfo.MSec))
================================**/
/* Useful when you have an array whose size you can tell at compile-time */
#define array_length(x)	(sizeof x / sizeof x[0])
#define  MsgID_End_of_file_identifier (999999)

/* 作用：检测偏移量是否合法。a:累计的偏移量，b:偏移量，c:长度 */
// #define MOVENEXTTEST(a,b,c) {if((a+b)>c) {return -1;}}
#define MOVENEXTCHECK(a,b,c) {if((a+b)>c) {return false;}}

/* 作用：偏移指定的偏移量。a:累计的偏移量，b:偏移量，c:长度 */
// #define MOVENEXT(a,b,c) {a+=b;if(a>c) {return -1;}}
#define MOVEDNEXT(a,b,c) {a+=b;if(a>c) {return false;}}
#define MOVENNEXT(a,b,c) {a+=b;if(a>c) {return;}}

/* 数组+数组大小 */
typedef struct Stru_Array_Pointer
{
	// 如果使用此构造函数，需要用户自行调用 initial(x)
	Stru_Array_Pointer()
	:pPointer(nullptr),nSize(0),nMaxSize(0)
	{
	};
	Stru_Array_Pointer(size_t sz)
	:pPointer(nullptr),nSize(0),nMaxSize(0)
	{
		Initial(sz);
	};
	~Stru_Array_Pointer()
	{
		Release();
	};
	//初始化
	void Initial(size_t sz)
	{
		if (pPointer == nullptr)
		{
			nSize = 0;
			nMaxSize = sz;
			pPointer = new BYTE[sz]{};
		}

		Clear();
	};
	//重新申请
	void Realloc(size_t sz)
	{
		if (sz > nMaxSize)
		{
			if (pPointer != nullptr)
			{
				delete[] pPointer;
			}
			pPointer = new BYTE[sz+1]{};
			nMaxSize = sz+1;
			Clear();
		}	
	};
	//用完释放
	void Release(void)
	{
		if (pPointer != nullptr)
		{
			delete[] pPointer;
			pPointer = nullptr;
		}
		nSize = 0;
		nMaxSize = 0;
	};
	void Clear(void)
	{
		if (pPointer != nullptr)
		{
			memset(pPointer, 0, nMaxSize);
		}
		nSize = 0;
	};
	Stru_Array_Pointer& operator= (const Stru_Array_Pointer& Array)
	{
		if (this != (&Array))
		{
			memcpy(pPointer, Array.pPointer, Array.nSize);
			nSize = Array.nSize;
			nMaxSize = Array.nMaxSize;
		}
		
		return *this;
	};
	
	BYTE* pPointer;
	size_t nSize;   //字节数
	size_t nMaxSize;//申请的最大空间
}tStru_Array_Pointer;

/* 参数变量初始值定义 */
#define INITIALVALUE_BYTE   255
#define INITIALVALUE_SHORT  -255
#define INITIALVALUE_FLOAT  -10000000
#define INITIALVALUE_INT    -10000000
#define INITIALVALUE_INT64  -10000000
#define INITIALVALUE_MAXUINT 4294967295

//////////////////////////////////////////// 公共信息, 解析源码用到///////////////////////////////////////
#define FIELD_LENGTH 64 // 应该定义为8的倍数
#define FIELD_COPY_LENGTH 63 // FIELD_LENGTH - 1
#define MSG_FIELD_LENGTH (1024 * 8)

// 运营商
enum eCARRIER
{
	eCARRIER_CHINAMOBILE = 1,	//中国移动
	eCARRIER_CHINAUNICOM = 2,	//中国联通
	eCARRIER_CHINATELECOM= 3,	//中国电信
	eCARRIER_CHINACBN = 4,	//中国广电
	eCARRIER_NONE
};

// PLMN 运营商
enum ePLMN
{
	ePLMN_CHINAMOBILE = 1,	//中国移动
	ePLMN_CHINAUNICOM = 2,	//中国联通
	ePLMN_CHINATELECOM = 3,	//中国电信
	ePLMN_CHINACBN = 4,	//中国广电
	ePLMN_NONE
};

//测试数据手机当前网络状态
enum eCurrNetType
{
	eCurrNetType_GSM = 0x01,
	eCurrNetType_WCDMA = 0x02,
	eCurrNetType_TD = 0x04,
	eCurrNetType_CDMA_EVDO = 0x08,
	eCurrNetType_LTE = 0x10,
	eCurrNetType_UnKnow = 0x20,
	eCurrNetType_NR = 0x40,
	eCurrNetType_NoService = 0x80,
	
	eCurrNetType_Invalid = INITIALVALUE_BYTE
};

// 测试数据手机当前模式
enum EMODE
{
	//GSM Mode
	MODE_NOSERVICE = 1,
	MODE_IDLE,
	MODE_DEDICATED,
	MODE_LIMITED_SERVICE,
	MODE_SCAN,
	MODE_PACKET,
	MODE_PACKETIDLE,
	
	// WCDMA Mode
	MODE_WCDMA_IDLE = 0x10,
	MODE_WCDMA_DEDICATED,
	MODE_WCDMA_PACKET,
	
	// TD Mode
	MODE_TDSCDMA_IDLE = 0x20,
	MODE_TDSCDMA_DEDICATED,
	MODE_TDSCDMA_PACKET,
	
	// CDMA Mode
	MODE_CDMA_IDLE = 0x30,
	MODE_CDMA_DEDICATED,
	MODE_EVDO_IDLE,
	MODE_EVDO_DEDICATE,
	
	// LTE Mode
	MODE_LTE_IDLE = 0x40,
	MODE_LTE_DEDICATED,

	// NR Mode
	MODE_NR_IDEL = 0x50,      //空闲态(MODE_NR_ENDC_INACTIVE 去激活态)
	MODE_NR_NSA_CONNECTED,    //非独立组网连接态
	MODE_NR_SA_CONNECTED,     //独立组网连接态
};

// 终端上报当前小区的双工模式
enum eDuplexMode
{
	eLteDuplexMode_FDD = 0,
	eLteDuplexMode_TDD,
	eLteDuplexMode_NBIOT,

	eNrDuplexMode_FDD,
	eNrDuplexMode_TDD,
	
	eDuplexMode_NONE
};

enum EGPSMODE
{
	ENUM_GPS_GPGGA = 0x00000001,
	ENUM_GPS_GPGSA = 0x00000002,
	ENUM_GPS_GPGSV = 0x00000004,
	ENUM_GPS_GPRMC = 0x00000008,
	ENUM_GPS_GPVTG = 0x00000010,
	ENUM_GPS_GPGLL = 0x00000020,
	ENUM_GPS_MARK  = 0x00000040,
	ENUM_GPS_BACKFILL_BYLAC_POINT = 0x00000080,
};

enum eUserCfg
{
	USERCFG_NORMAL,      //正常解码
	USERCFG_DIRDEAL,     //[已清楚业务类型]深圳鼎利网络评估专用
	USERCFG_FileList,    //[未清楚业务类型]用作标记文件使用用户的配置
	USERCFG_SKETCHY,     //粗略指定业务类和网络类型的[如:某设备均测试2G的语音或3G的语音]
};

// 手机芯片厂家
enum eMSCHIPTYPE
{
	MS_Chip_Qualcomm = 1, //高通
	MS_Chip_Sagem, //萨基姆
	MS_Chip_Samsung, //三星
	MS_Chip_Huawei, //华为
	MS_Chip_ZTE, //中兴
};

// 手机具体型号
enum eMSSUBTYPE
{
	MS_NMEA0183 = 1,
	MS_SAGEM_OT1, 	
	MS_SAGEM_OT2,
	MS_SAGEM_OT3, 
	MS_SAGEM_OT4, 
	MS_SAGEM_OT5, 
	MS_SAGEM_OT6, 
	MS_SAMSUNG_X199,
	MS_SAMSUNG_Z500, // wcdma ms
	MS_mc8755_s1, 
	MS_mc8755_gsm,
	MS_qualcommms,
	MS_qualcommwcdmams, // wcdma ms
	MS_Huawei_EC360,
	MS_LG_KG90c,
	MS_SAMSUNG_F400,
	MS_SAMSUNG_F408,
	MS_NOKIA_N85,
	MS_HUAWEI_E270,
	MS_LG_KX206,
	MS_SAGEM_OT35,
	MS_LG_KX236,
	MS_MOTO,
	MS_mc8755,
	MS_ZTEAC8710,      // EVDO上网卡,
	MS_HaiGaoTD2010,   // 扫频数据
	MS_ComarcoTD2010,  // 扫频数据
	MS_JingXinTD2010,  // 扫频数据
	MS_NOKIA_6720,
	MS_RSGSMDualBand, // 扫频数据
	MS_JingXin3301AB, // 扫频数据
	MS_HUAWEI_E180,
	MS_qualcommms_evdo,
	MS_HUAWEI_EC226,
	MS_DTIGSMDualBand,
	MS_RSGSM900,
	MS_RSGSM1800,
	MS_MAIWEI_DX,
	MS_NOKIA_6120,
	MS_RSUMTS2110,
	MS_LG_KX218,
	MS_MSM6800_Compatible,
	MS_SAMSUNG_X799,
	MS_SonyEricsson,
	MS_HUAWEI_E620,
	MS_HUAWEI_C810E,
	MS_HUAWEI_EC510,
	MS_Ericsson,
	MS_LG_KX256,
	MS_SAMSUNG_Z720,
	MS_LG_KX266,
	MS_ANYDATA_ADU520,
	MS_AIRCARD_880U,
	MS_SAMSUNG_Z728,
	MS_SAGEM_OT8,
	MS_NOKIA_C5,
	MS_NOKIA_6720C,
	MS_SAMSUNG_Z105,
	MS_HUAWEI_U120E,
	MS_FENGHUOTD2010,
	MS_TM7200_COMPATIBLE,
	MS_HUAWEI_E1820,
	MS_CNU_680, //CDMA, EVDO
	MS_NOKIA_6121, //WCDMA
	MS_IPHONE4S_WCDMA, //GSM/WCDMA
	MS_SAMSUNG_Z560,
	MS_DingLi_WIFI, //鼎利WiFi
	MS_HISILICON_LTE_TDD, //LTE
	MS_INNOFIDEI_RAPID_MD2100, //LTE
	MS_QUALCOMM_LTE_TDD, //LTE
	MS_DX_188U,
	MS_IPHONE4_WCDMA, //GSM/WCDMA
	MS_HISILICON_MIFI_E5776S860, //LTE
	MS_DATANG_LC5160T, //LTE
	MS_HUAWEI_E367, //WCDMA
	MS_FENGHUO_RFSS_V2_LTE, //LTE SCAN
	MS_ZTE_MF668, //WCDMA
	MS_HISILICON_CPE_B593, //LTE
	MS_ZTE_MicroChip, //ZTE (*.APT)
	MS_HISILICON_CPE, //LTE
	MS_ZTE_MF820T, //LTE
	MS_SAMSUNG_SIII_I9305, //WCDMA
	MS_HTC_Vivid, //WCDMA
	MS_SIERRA_WIRELESS_AC850, //WCDMA HSDPA+
	MS_SAMSUNG_S4_I9505, //WCDMA HSDPA+
	MS_SAMSUNG_NOTE_II_N7108D, //LTE
	MS_SW_313U, //WCDMA HSDPA+
	MS_HISILICON_E3276S_861, //LTE
	MS_MERLIN_630, //WCDMA
	MS_HTC_ONE_802T, //TD
	MS_SAMSUNG_S4_I9508, //TD
	MS_DATANG_LC5161, //LTE
	MS_ZCAT_LTETBZ, //ATU LTE
	MS_HISILICON_E5375S_860, //LTE
	MS_SAMSUNG_SIII_I9308C,  //LTE(3网)
	MS_SAMSUNG_SIII_I9308D, //LTE CSFB
	MS_QUANTA_QSPOT_D2, //LTE
	MS_SAMSUNG_NOTE_II_N719_LTE, //LTE
	MS_IPHONE5_GSM_A1429, //LTE CSFB
	MS_QUANTA_QSPOT_D1, //LTE
	MS_HUAWEI_D2_5000, //LTE
	MS_HUAWEI_D2_6070, //LTE
	MS_HISILICON_E3276S_150, //LTE
	MS_ATU_TBK, //ATU TD
	MS_ZTE_MF91S, //LTE QUALCOMM
	MS_SONY_M35T, //LTE QUALCOMM CSFB
	MS_HUAWEI_E5776S_860, //LTE
	MS_HUAWEI_E5375S_860, // LTE
	MS_HUAWEI_E3276S_861,
	MS_HUAWEI_E392U_12,
	MS_IPHONE5S,
	MS_IPHONE5S_GSM,
	MS_IPHONE5C_GSM,
	MS_SIM929,
	MS_HUAWEI_E353, //WCDMA
	MS_SAMSUNG_NOTE_III_N9008V, //LTE CSFB
	MS_HTC_ONE_802D, //CDMA
	MS_HUAWEI_EC177, //CDMA
	MS_HUAWEI_E398U_18, // LTE
	MS_ZTE_MF820, // WCDMA
	MS_HUAWEI_EC1261, // EVDO
	MS_VODAFONE_K5005, //LTE
	MS_HUAWEI_C8650, //CDMA
	MS_SAMSUNG_NOTE_III_N7506V, //LTE FDD CSFB
	MS_HUAWEI_8800, //CDMA
	MS_QUALCOMM_LTE_FDD, //LTE FDD CSFB
	MS_HUAWEI_E392U_21, //LTE
	MS_SAMSUNG_NOTE_III_N7509V, //LTE
	MS_IPHONE6, //LTE CSFB
	MS_HTC_M8, //LTE Volte
	MS_HTC_M8T, //LTE CSFB
	MS_IPHONE6_PLUS, //LTE CSFB
	MS_DINGLI_GPS, //dingli GPS
	MS_ZTE_LTE_TDD,
	MS_ZTE_Q801L, // FDD LTE
	MS_HUAWEI_MATE7, 
	MS_SAMSUNG_S5_G9008V, // TDD LTE&VOLTE
	MS_SAMSUNG_S5_G9006V, // FDD LTE
	MS_HUAWEI_E398U_15, // FDD LTE
	MS_SAMSUNG_S6_G9200, // FDD LTE
	MS_IPHONE6S, // LTE(FDD/TDD)
	MS_HTC_M9U, // LTE
	MS_CHINAMOBILE_N1_MAX_M823, // VoLTE
	MS_SAMSUNG_S5_G900F, // WCDMA
	MS_SAMSUNG_G3608, // WCDMA
	MS_SAMSUNG_NOTE_III_N9005, // WCDMA HSPA+
	MS_SAMSUNG_S4_I9507V, // LTE FDD CSFB
	MS_HUAWEI_MATE8, // LTE VoLTE
	MS_SONY_Z2, // LTE CSFB
	MS_SAMSUNG_S4_M919, // 联通 WCDMA
	MS_CHINAMOBILE_N1_M821, // VoLTE视频
	MS_SAMSUNG_S6_G9208, // TDD LTE
	MS_ZTE_NUBIA_Z9_MAX_NX510J, // VoLTE
	MS_SAMSUNG_S7_G9300, // LTE FDD CSFB
	MS_IPHONE6S_PLUS, // LTE
	MS_SAMSUNG_S7_G9308, //LTE
	MS_ZTE_N5L, //TDD LTE
	MS_ZTE_Blade_V8, //TDD LTE
	MS_SAMSUNG_S5_G900I, // TDD LTE 联通
	MS_REMO_1526, // 移动物联网
	MS_SAMSUNG_S5_G9009W,
	MS_HUAWEI_E3276S_150, // TDD LTE
	MS_ZTE_NUBIA_Z11_MINI_NX529J, // GSM
	MS_RS_TSME_V2, 
	MS_SAMSUNG_S7_EDGE_G9350,
	MS_SAMSUNG_SIII_I747,
	MS_TD_EP680V2,
	MS_ZTE_NUBIA_Z11_NX531J,//LTE TDD
	MS_ZTE_NUBIA_Z9_NX508J,//WCDMA
	MS_MI_5,//LTE TDD(小米5)
	MS_SAMSUNG_S6_EDGE_G9250,//LTE TDD
	MS_CHINAMOBILE_N2_M836, // VoLTE
	MS_SAMSUNG_S8_G9500, //LTE FDD
	MS_ZTE_Blade_Q_Lux,
	MS_ZTE_NUBIA_Z11_MINIS_NX549J, //LTE FDD
	MS_QUALCOMM_MSM_8974,//LTE TDD
	MS_HUAWEI_C8817L,//LTE TDD
	MS_VIVO_Y79A,//LTE TDD
	MS_REDMI_NOTE5,// LTE TDD(红米Note5)
	MS_CHINAMOBILE_A2_M636, //LTE FDD
	MS_ZTE_NUBIA_Z17_MINI_NX569J, //LTE FDD
	MS_LIERDA_USB_DONGLE, //NB-IOT
	MS_HUAWEI_MATE9, //LTE FDD
	MS_SAMSUNG_S9_G9600,//LTE TDD
	MS_OPPO_R11S,//GSM
	MS_SAMSUNG_NOTE5_N9200,//LTE
	MS_MI_NOTE_PRO, //LTE TDD
	MS_VIVO_Y85A, //LTE TDD
	MS_ZTE_ZM8300, //LTE TDD
	MS_VIVO_X23, //LTE TDD
	MS_VIVO_Y66I_A, // LTE FDD
	MS_HUAWEI_MATE20, //LTE TDD
	MS_HUAWEI_MATE10, //LTE TDD
	MS_VIVO_IQOO, // LTE TDD
	MS_ZTE_NUBIA_Z9_MAX_NX518J,
	MS_QUALCOMM_MTP_9206,
	MS_RS_TSME6_V2,
	MS_UBlox_GPS, //GPS
	MS_HTC_10,
	MS_REDMI_K30_4G, //高通
	MS_HTC_U_3W, //高通
	MS_SIM7000, //NB-IOT
	MS_MI_8, //高通
	MS_LONGSUNG_A9500, //NB-IOT

	//5G
	MS_ZTE_Axon10s_5G_Pro,//NR NSA
	MS_ZTE_Axon10_5G_Pro,//NR NSA
	MS_HiSilicon_Balong_5000,//NR NSA
	MS_HUAWEI_Mate20X, //NR SA
	MS_HUAWEI_Mate30,//NR NSA
	MS_HUAWEI_MATE30_PRO,//NR NSA
	MS_HUAWEI_P40, //NR NSA
	MS_HUAWEI_P40_PRO,
	MS_VIVO_NEX_5G,
	MS_HUAWEI_MATE40_PRO,
	MS_SAMSUNG_S20_G9810,
	MS_HUAWEI_MATE40,
	MS_QUALCOMM_SDMX60, //NR_SA
	MS_REDMI_K30_5G,
	MS_OPPO_RENO4_5G,
	MS_QUALCOMM_SDMX55, //NR_SA
	MS_MI_10, //5G手机
	MS_MI_MIX3_5G, //
	MS_ZTE_Axon30_Ultra_5G,
};

enum eDEVTYPE
{
	LOG2FMT = 0,
	DEV_TEMS = 1,
	DEV_NEMO,
	DEV_ANT,
	DEV_PREMIER,
	DEV_CLARIFY,
	DEV_RCU,
	DEV_MTU,
	DEV_HUAXING,
	DEV_RIXUN,
	DEV_FEELER,
	DEV_CDS,
	DEV_PIONEER,
	DEV_PANORAMA,
	DEV_DATANG,
	DEV_MICROSOFT,
	DEV_MAIWEI,
	DEV_DINGXING,
	DEV_WANHE,
	DEV_TDSCDMA = 19,
	DEV_RandS,
	DEV_ZTE,
	DEV_FENGLIAN, // 烽联
	DEV_HUAWEI, //华为
	DEV_JINGXIN,
	DEV_JINGWEI,
	DEV_AGILENT, // 安捷伦设备
	DEV_KnowYou, // 诺优设备
	DEV_FengHuo, // 烽火设备
	DEV_HONGXIN, // 虹信设备
	DEV_STUMBLER, // WLan
	DEV_AIRMAGNET, // 艾尔麦
	DEV_TESCOMM,   // 泰合佳通
	DEV_ZHUOXIN,   //卓信(现有数据TD扫频)
	DEV_OPTI,  //欧佩泰斯 
	DEV_MASTERCOM, //名通科技
	DEV_QUALCOMM,
	DEV_GSMR, // 铁科院GSMR
	DEV_N1Max, //神州泰岳优化宝
	DEV_NBIOT, //高通数据
	DEV_MINGRUN,// 铭润
	DEV_PCTEL,
	DEV_HUGELAND,
	DEV_DINGLI,
	DEV_XKSC,  //珠海新科思创
	DEV_SPIDER, //上海小蜘蛛
	DEV_NOKIA_IHANDLE, //上海 诺基亚自己的单验工具 ihandle
	DEV_WSW, //北京 万思维 公司的路测设备
	DEV_MOBILE //此设备为通用设备 代表一切移动端
};

enum eNETTYPE
{
	NET_TRY = -2,
	NET_OTHER = -1,
	NET_ZIP = 0,
	NET_GPS,
	NET_GSM,
	NET_CDMA,
	NET_TDSCDMA,
	NET_CDMA2000,
	NET_WCDMA,
	NET_SCAN_GSM,
	NET_SCAN_CDMA,
	NET_SCAN_TDSCDMA,
	NET_SCAN_WCDMA,
	NET_GSM_UPLINK,
	NET_MOS,
	NET_GSM_CALLTRACE,
	NET_TD_CALLTRACE,
	NET_WLAN,
	NET_SCAN_LTE,
	NET_LTE_TDD,
	NET_LTE_FDD,
	NET_LTE_NBIOT,
	NET_SCAN_NBIOT,
	NET_NR_NSA_TDD,        // 中国运营商使用的NR频段为:n41、n78、n79，使用的双工模式都是TDD.   [8/28/2019 hhj]
	NET_NR_SA_TDD,         // 目前中国运营商腾出低频段用于NR网络.                             [10/8/2019 hhj]
	NET_NR_DM_TDD,		   // SA | NSA
	NET_SCAN_NR,

	NET_2G = 100,
	NET_3G,
	NET_SCAN_GENERAL, //扫频个网络表结构通用的扫频模式（CW测量模式、频谱分析模式）
	NET_UEP, //感知测试
};

enum eSERTYPE
{
	SER_GSM_VOICE = 1,
	SER_GPRS_DATA,
	SER_EDGE_DATA,
	SER_TDSCDMA_VOICE,
	SER_TDSCDMA_DATA,
	SER_CDMA_VOICE,
	SER_CDMA1X_DATA,
	SER_CDMA2000_VOICE,
	SER_CDMA2000_DATA,
	SER_WCDMA_VOICE,
	SER_WCDMA_DATA,
	SER_SCAN,
	SER_TDSCDMA_VIDEO,
	SER_WCDMA_VIDEO,
	SER_WCDMA_HSDPA,
	SER_CDMA2000_VIDEO,
	SER_TDSCDMA_IDLE,
	SER_TDSCDMA_HSDPA,
	SER_SCAN_TD,
	SER_SCAN_CDMA,
	SER_SCAN_WCDMA,
	SER_GSM_IDLE,
	SER_GSM_MOS,
	SER_GSM_UPLINK,
	SER_WCDMA_IDLE,
	SER_CDMA_IDLE,
	SER_TDSCDMA_HSUPA,
	SER_WCDMA_HSUPA,
	SER_GSM_CALLTRACE,
	SER_TD_CALLTRACE,
	SER_WLAN,
	SER_CDMA_MOS,
	SER_LTE_TDD_VOICE, //LTE_TDD_语音(CSFB)
	SER_LTE_TDD_DATA, //LTE_TDD_数据
	SER_SCAN_LTETOPN, //TD-LTE TopN扫描模式
	SER_SCAN_LTECW,   //TD-LTE CW测量模式
	SER_SCAN_LTEFREQ, //TD-LTE 频谱分析模式
	SER_SCAN_TDFREQ,  //TD-SCDMA频谱分析模式
	SER_SCAN_GSMFREQ, //GSM频谱分析模式
	SER_CDMA2000_IDLE,
	SER_LTE_TDD_IDLE, //LTE_TDD_空闲
	SER_LTE_TDD_MULTI, //LTE_TDD_并发测试
	SER_LTE_TDD_VOLTE, //LTE_TDD_语音(VOLTE)
	SER_LTE_TDD_UEP, //LTE_TDD_感知

	SER_LTE_FDD_VOICE, //LTE_FDD_语音(CSFB)
	SER_LTE_FDD_DATA, //LTE_FDD_数据
	SER_LTE_FDD_IDLE, //LTE_FDD_空闲
	SER_LTE_FDD_MULTI, //LTE_FDD_并发测试
	SER_LTE_FDD_VOLTE, //LTE_FDD_语音(VOLTE)

	SER_LTE_SIGNAL,

	SER_LTE_TDD_VIDEO_VOLTE, //LTE_TDD_视频(VOLTE)
	SER_LTE_FDD_VIDEO_VOLTE, //LTE_FDD_视频(VOLTE)

	SER_LTE_TDD_CA, //LTE_TDD_载波聚合(CA)
	SER_LTE_FDD_CA, //LTE_FDD_载波聚合(CA)

	SER_SCAN_NBIOT_TOPN, //NB-IOT TOPN扫频模式
	SER_NBIOT_DATA, //NB-IoT数据

	SER_NR_NSA_TDD_IDLE, //NR非独立组网空闲模式
	SER_NR_NSA_TDD_DATA, //NR非独立组网数据业务模式
	SER_NR_NSA_TDD_VOLTE, //NR非独立组网4G VOLTE业务模式(5G文件中有进行lte volte业务)
	SER_NR_SA_TDD_IDLE, //NR独立组网空闲模式
	SER_NR_SA_TDD_DATA, //NR独立组网数据业务模式
	SER_NR_SA_TDD_VOLTE, //NR独立组网4G VOLTE业务模式(5G文件中有进行lte volte业务)
	SER_NR_DM_TDD_IDLE, //NR双模空闲模式 (DM（Dual Mode）双模：即工作在两个网络模式下)
	SER_NR_DM_TDD_DATA, //NR双模数据业务模式
	SER_NR_DM_TDD_VOLTE, //NR双模4G VOLTE业务模式
	SER_NR_SA_TDD_EPSFB, //NR独立组网EPSFB语音业务
	SER_NR_DM_TDD_EPSFB, //NR双模 EPSFB语音业务
	SER_NR_NSA_TDD_MULTI, //NR非独立组网并发业务模式
	SER_NR_SA_TDD_MULTI, //NR独立组网并发业务模式
	SER_NR_DM_TDD_MULTI, //NR双模并发业务模式
	SER_SCAN_NR, //NR 扫频模式
	SER_SCAN_NRFREQ, //NR 频谱分析模式
	SER_NR_SA_TDD_VONR, // NR VONR业务

	SER_DIRPARSER_VOICE = 100,
	SER_DIRPARSER_DATA,
	SER_DIRPARSER_VIDEO,
	SER_DIRPARSER_HSDPA,
	SER_DIRPARSER_IDLE,
	SER_DIRPARSER_SCAN,
	SER_DIRPARSER_MULTI,
	SER_DIRPARSER_VOLTE,
	SER_DIRPARSER_EPSFB,
	SER_DIRPARSER_VONR,
};

enum eTESTTYPE
{
	TEST_DOC = 0,
	TEST_DT = 1,
	TEST_CQT,
	TEST_AUTODT,
	TEST_TA,
	TEST_SC,
	TEST_AUTOCQT,
	TEST_CDMADT,
	TEST_CDMACQT,
	TEST_TDSCDMADT,
	TEST_TDSCDMACQT,
	TEST_WCDMADT,
	TEST_WCDMACQT,
	TEST_CDMA2000DT,
	TEST_CDMA2000CQT,
	TEST_CALLTRACE,
	TEST_WLANDT,
	TEST_WLANCQT,
	TEST_ATUCQT, //ATU 陀螺仪(GYRO)CQT测试
	TEST_LTE_TDD_DT,
	TEST_LTE_TDD_CQT,
	TEST_LTE_UEP,
	TEST_LTE_FDD_DT,
	TEST_LTE_FDD_CQT,
	TEST_LTE_SIGNAL_DT,
	TEST_LTE_SIGNAL_CQT,
	TEST_NBIOT_DT,
	TEST_NBIOT_CQT,
	TEST_NR_TDD_DT,
	TEST_NR_TDD_CQT
};

enum eFILETYPE
{
	FILE_TEST_DOC = 0,
	FILE_TEMS_LOG = 1,   
	FILE_TEMS_FMT,
	FILE_DINGLI_PAF,
	FILE_DINGLI_GHL,
	FILE_DINGLI_WHL,
	FILE_DINGLI_THL,
	FILE_CLARIFY_MDB,
	FILE_NEMO_DT1,
	FILE_ANT_ANT,
	FILE_HUAXING_OPT,
	FILE_HUAXING_CDM,
	FILE_CDS_LOG,
	FILE_RIXUN_SGZ,
	FILE_RIXUN_QGZ,
	FILE_FEELER_BIN,
	FILE_MTU_TA,
	FILE_PREMIER_MS,
	FILE_PANO_CDM,
	FILE_DINGLI_CHL,
	FILE_HX_OP4, // 相当于FILE_HUAXING_OP4
	FILE_HX_GPS,
	FILE_HX_PCM,
	FILE_DINGLI_RCU,
	FILE_DINGLI_WTO,
	FILE_TEMS_REDE, //TEMS Visualization(GSM)
 	FILE_TESCOMM_COOLTEST,
	FILE_FENGLIAN_LCF,  //丰联lcf(解密后的文件)
	FILE_NEMO_NMF,
	FILE_RIXUN_RXG,
	FILE_RIXUN_RXC,
	FILE_RIXUN_LG4,
	FILE_DATANG_LOG,
	FILE_HUAXING_OP4,
	FILE_HUAXING_GPS,
	FILE_HUAXING_PCM,
	FILE_JDSU_ISD,     //JDSU(捷迪讯)lte扫频数据
	FILE_STD_LTE,     //集团ATU-LTE
	FILE_ASPSFMT_TXT, //集团ASPS格式的扫频数据
	FILE_ZHUOXIN_LOG, //卓信TD扫频数据		
	FILE_TEMS_TRP,    //TEMS 14.0
	FILE_HUAXING_WLL, //华星wlan(*.wll)
	FILE_STD_LOL, //集团ATU-WLAN
	FILE_SKYWARE_LOG, //TD-U-Touch S50 log
	FILE_ZHUOXIN_MVS, //卓信TD/GSM扫频数据
	FILE_OPTI_CSV,  //欧佩泰斯 LTE扫频数据
	FILE_NEMO_DT2,
	FILE_CELL_CFG,
	FILE_DATANG_LGL,  //大唐LTE
	FILE_ASPSFMT_LTE, //集团ASPS格式的LTE扫频数据(*.csv)
	FILE_HUAXING_FPF,  //华星FPF
	FILE_STD_LOG,
	FILE_ATU_CSV,  // 集团ATU导出的文件
	FILE_WANHE_WHD,
	FILE_UEP_CSV,  //感知测试接口数据
	FILE_OPTI_OTLOG,  // 欧佩泰斯原始log
	FILE_STD_DGZ,  // 鼎利ATU压缩文件
 	FILE_ZTE_APT_CSV,  // 废弃
	FILE_RS_ASC,
	FILE_DINGXIN_DX,
	FILE_MAIWEI_MW3,
	FILE_ZTE_APTX,
	FILE_FENGLIAN_FLD,
	FILE_ZTE_APT,
	FILE_DATANG_MUT,
	FILE_TEMS_FMC,
	FILE_TEMS_FMG,
	FILE_TEMS_FMS,
	FILE_RAINBOW_RB, // 科虹Rainbow&PCTEL扫频
	FILE_CLARRB_GSM,
	FILE_CLARRB_TD,
	FILE_CLARIFY_MID,
	FILE_HXMOS_TXT,
	FILE_TEMS_MTR,
	FILE_COMBA_RTZ,
	FILE_COMBA_RTX,
	FILE_MT_CDG,
	FILE_STD_LOC,
	FILE_CLARRB_LTE,
	FILE_QM_QMDL,
	FILE_QM_DLF,
	FILE_TRIORAIL_GSMR,
 	FILE_TRIORAIL_FES,
	FILE_HUAWEI_GEN,
	FILE_ZTE_ZRX,
	FILE_JWTX_GSX,   // 经纬G网
	FILE_JWTX_CMX,   // 经纬C网
	FILE_AIRMAGNET_AMC,   // 艾尔麦wlan数据
	FILE_ZTE_APM2CSV,   //中兴APM转换的CSV 4G测试数据解码
	FILE_HUAWEI_GEW, // 华为中间文件W网
	FILE_AGILEN_AOD, // 安捷伦aod扫频数据
	FILE_KNOWYOU_TLG, // 诺优*.tfg
	FILE_STUMBLER_NS1,
	FILE_TEMS_FMW,
	FILE_FENGHUO_DAT, // 烽火扫频数据
	FILE_ASPSFMT_SF,  // 集团ASPS格式的扫频数据
	FILE_FENGHUO_TOP, // 烽火T网扫频数据(*.top)
	FILE_TEMS_POCKET,
	FILE_ZTE_STD,
	FILE_TEMS_LZZ,
	FILE_TEMS_LZM,
	FILE_TEMS_LZ1,
	FILE_TEMS_LZ2,
	FILE_HUAWEI_TMF,
	FILE_HUAXING_FYF, // 华星*.fyf
	FILE_HONGXIN_ADF, // 虹信*.adf
	FILE_HUAXING_FGS, // 华星*.fgs
	FILE_STD_LOT,     // 标准格式*.lot
	FILE_HXMOS_CSV,
	FILE_HUAXING_WR,  // 华星Wlan*.wr
	FILE_CDS_CDF,
	FILE_TEMS_CTR,
	FILE_NEMO_NBL,
	FILE_STD_LOW,
	FILE_TEMS_QMD,
	FILE_TEMS_QMZ,
	FILE_STD_LOE,
	FILE_DINGLI_DCF,  //鼎力DCF数据
	FILE_HUAWEI_GEN2CSV,//华为Probe导出的csv文件
	FILE_RS_ASC_NB, // 罗德斯瓦次asc NB扫频
	FILE_MINGRUN_MRM,	// 铭润mrm文件
	FILE_KNOWYOU_CU,// 诺优*.cu
	FILE_HUGELAND_CU,// 惠捷朗CDS软件生成的*.cu文件
	FILE_FENGHUO_CSV,// 黑龙江烽火设备导出的CSV文件
	FILE_PCTEL2CSV, //PCTEL设备转换出的CSV文件
	FilE_ZTE_APS2CSV,//中兴aps文件转换出的CSV文件
	FILE_DINGLI_CU, //鼎利软件测试出来的cu文件，可回放
	FILE_SPARK_RAF, //新科思创Spark软件测试的.raf文件
	FILE_STD_L5G, //L5G文件
	FILE_KNOWYOU_NRSCAN2CSV, //诺优 NR 扫频数据类型
	FILE_SPIDER_HL5G, //上海 小蜘蛛 .hl5g文件
	FILE_SPIDER_HLNR, //上海 小蜘蛛 .hlnr文件
	FILE_SPIDER_HLTE, //上海 小蜘蛛 .hlte文件
	FILE_SPIDER_HLOG, //上海 小蜘蛛 .hlog文件
	FILE_KNOWYOU_LTLG, // 诺优*.ltlg
	FILE_RS_SPECTRUM_CSV, //上海罗德斯瓦茨生成的用于地段计费的CSV文件，只解析经纬度
	FILE_ZTE_APM, //新疆中兴设备 T-Phone Analyzer 测试的APM文件
	FILE_DATANG_DTM, //大唐 ETG 软件测试生成的 .dtm文件
	FILE_NOKIA_IHANDLE_HLD, //Nokia 单验工具 ihandle 生成的 .hlg文件
	FILE_SPARK_CU, //万思维的 spark 路测软件 生成的 .cu文件
	FILE_SPARK_DTLOG,//万思维的 spark 路测软件 转换生成的 .dtlog文件
	FILE_CHUANGYUAN_4G_SCAN_CSV,//创远扫频仪生成的 .csv 文件
	FILE_CHUANGYUAN_5G_SCAN_CSV,//创远扫频仪生成的 .csv 文件
	FILE_HUAWEI_CU, //华为软件测试出来的cu文件，.cu文件
	FILE_APP_JSON2CSV, //手机APP软件测试生成的json文件，转换出的CSV文件
	FILE_GENERIC_CSV, // 通用的简单采样点CSV文件

	FILE_TYPE_NONE, //数据类型总数
	/*************************************************************************
	拆分文件的中间文件统一按*.mtf处理
	(*.mtf)
	(256BYTE)File Header: sizeof(tSTRU_MiddleFile_Info) + reserve(256-sizeof(tSTRU_MiddleFile_Info))
	*************************************************************************/
	FILE_MT_MTF = 10000,
	//解码过程使用的文件类型
	FILE_MT_FH_LJF750531, //FILE_FENGHUO_DAT
	FILE_MT_FH_R3001, //FILE_FENGHUO_DAT
	FILE_LBE,
	FILE_ISF,
	FILE_SPARK_RAF2MTNR, //新科思创Spark软件用.raf文件导出的.mtnr文件
	FILE_FENGHUO_NR_CSV //烽火5Gcsv
};

enum FLD_TYPE
{
	TYPE_INT = 1,
	TYPE_SHORT = 2,
	TYPE_BYTE = 3,
	TYPE_FLOAT = 4,
	TYPE_STR = 5,
	TYPE_FLOAT2 = 6, // float * 1000
	TYPE_INT64 = 7
};

enum eFILE_STATSTATUS
{
	STATSTATUS_Initializtion = 0,		// 初始化
	STATSTATUS_DECODE_SUCCESS = 1,		// 解码成功
	STATSTATUS_TRY_SUCCESS,				// 预分析成功
	STATSTATUS_UNZIP_SUCCESS,			// 拆包成功
	STATSTATUS_DIR_PARSED_SUCCESS,		// 路径解析成功

	STATSTATUS_DECODE_FAILED,			// 解码失败
	STATSTATUS_UNZIP_FAILED,			// 拆包失败
	STATSTATUS_TRY_FAILED,				// 预分析失败
	STATSTATUS_DIR_PARSED_FAILED,		// 路径解析失败

	STATSTATUS_BASE_SUCCESS,			// 基本分析成功
	STATSTATUS_TIME_ERROR,				// 文件头时间信息解析错误
	STATSTATUS_UNKNOWN_VERSION,			// 未知版本数据
	STATSTATUS_INSERT_LOG_FAILES,		// 插入log失败
	STATSTATUS_NO_MS,					// 未解析出有效测试端口
	STATSTATUS_UNKNOWN_NETTYPE,			// 未知网络类型
	STATSTATUS_FILE_OPENFAILURE,		// 打开文件失败
	STATSTATUS_ABNORMAL,				// 解析异常终止
	STATSTATUS_UNKNOWN_FILETYPE,		// 未知文件类型
	STATSTATUS_NAME_TOOSHORT,			// 文件名太短
	STATSTATUS_LOG2FMT,					// Log文件转换
	STATSTATUS_START_DECODE,			// 开始解码
	STATSTATUS_HUGE_PACKET,				// 巨包数据
	STATSTATUS_READFILE_ERROR,          // 读取文件错误
	STATSTATUS_UNKNOWN_MS,              // 新手机
	STATSTATUS_UNKNOWN_TESTTYPE,        // 无效的测试类型
	STATSTATUS_PREPARE_DECODE,          // 准备解码
	STATSTATUS_OVERDUE_FILE,            // 过期文件限制入库 （广东省移动需求）[12/7/2016 zhs]
	STATSTATUS_NOT_ORIGINAL_FILE,       // 非原始文件 （四川省移动需求）[1/2/2020 fhx]
};

const char FILE_STATSTATUS_INFO[][35] = 
{
	"",
	"解码成功",
	"预分析成功",	
	"拆包成功",
	"路径解析成功",
	"解码失败",
	"拆包失败",
	"预分析失败",
	"路径解析失败",
	"基本分析成功",
	"文件头时间信息解析错误",
	"未知版本数据",
	"插入log失败",
	"未解析出有效测试端口",
	"未知网络类型",
	"打开文件失败",
	"解析异常终止",
	"未知文件类型",
	"文件名太短",
	"Log文件转换",
	"开始解码",
	"巨包数据",
	"read file error",
	"未适配的手机类型",
	"无效的测试类型",
	"预备解码",
	"过期文件限制入库",
	"非原始文件"
};

enum eCommentKey
{
	eCommentKey_DecodeProc, //解码过程
	eCommentKey_TestVersion,//ATU FileInfo
	eCommentKey_TestPlan,//ATU TestScheme Name
	eCommentKey_DeviceInfo, //RCU 设备信息
	eCommentKey_RRCUnknownChannel, //RRC未知信道号
	eCommentKey_MODigit, //主叫号码
	eCommentKey_MTDigit, //被叫号码
	eCommentKey_IMSI,//国际移动用户识别号
	eCommentKey_ElapsedTime, //解码处理消耗时间
	eCommentKey_LoadFileList, //加载解码队列
};

const char szCommentKey[][32] = 
{
	"DecodeProcess",
	"TestVersion", 
	"TestPlan",
	"DeviceInfo",
	"RRC Unknown Channel",
	"MODigit",
	"MTDigit",
	"Mobile IMSI",
	"ElapsedTime",
	"LoadFileList"
};

// 与tb_cfg_dirattribute对应
enum ENUM_DIRPARSER_STYLE
{
	ENUM_PRO_TYPE = 1,  
	ENUM_BIZ_TYPE,
	ENUM_TEST_TYPE,
	ENUM_FILE_TYPE,
	ENUM_DIST,
	ENUM_YEAR,
	ENUM_MON,
	ENUM_AGENT,
	ENUM_AREATYPE,
	ENUM_AREAID,
	ENUM_CARRIER,
	ENUM_DEV_TYPE,
	ENUM_DATE,
	ENUM_DB,
	ENUM_FILE_MODEL,
	ENUM_SCETURY,
	ENUM_SOURCE,
	ENUM_EVENT,
	ENUM_PERSONID
};

// 参数保留信息存储
typedef struct STRU_KEEP_PARA
{
#define TakeOff_NetWork_Interval 1000 // 单位毫秒

	/*采样点时间说明：
	 *lastsampleTime,lastsampleMSec:为填写真正采样点的最后一个时间点
	 *tlastsampleTime:为填写每一个采样点的最后一个的时间点（包括异常的采样点，如只有GPS信息）
	 *生成只有GPS的采样点的条件：a、当前时间比tlastsampleTime大3分钟及以上，b、tlastsampleTime比lastsampleTime大1分钟及以上。
	*/
	int	lastsampleTime;		// int	时间（包含日期）
	int	lastsampleMSec;		// int	毫秒数
	STRU_TIME_VAL tlastsampleTime; // 时间（包含日期）主要用于填写GPS信息到回放表
	STRU_TIME_VAL tlastsample3Time; // 时间（包含日期）主要用于填写GPS信息到统计表
	int	lastlongitude;		// int	经度
	int	lastlatitude;		// int	纬度
	BYTE b2g_3gstatusflag;   //用于标识是2G还是3G 1为3G，2为2G
	
	BYTE bMode;
	int bMCS;
	short sBcch;
	int iLac;
	int iCi;
	int tHandcmdtime;
	short hfBcch;
	BYTE hfBsic;
	BYTE IsSetUped;
	BYTE IsConnected;
	int FTP_StatCount;//统计FTPFirstData数量<作用:有些文件被分割过，一上来就是Ftp Continue，故也把这些信息入库>

	void Clear(void)
	{
		lastsampleTime = 0;
		lastsampleMSec = 0;
		tlastsampleTime.Clear();
		tlastsample3Time.Clear();
		lastlongitude = 0;
		lastlatitude = 0;
		b2g_3gstatusflag = 2;

		bMode = 0;
		bMCS = 0;
		sBcch = -255;
		iLac = -255;
		iCi = -255;
		tHandcmdtime = -1;
		hfBcch = -255;
		hfBsic = 255;
		IsSetUped = 0;
		IsConnected = 0;

		FTP_StatCount = 0;
	};
} tSTRU_KEEP_PARA;

typedef struct STRU_CFGPATH_INFO// 与STRU_PATH_INFO保持一致
{
	char strpath[256];    // 原始文件路径
	char strname[256];    // 原始文件名称
	char strcurname[256]; // 当前文件名称
	int curfiletype;	  // 当前文件类型
	int fileid;
	unsigned int filesize;
	unsigned int Unzipfilesize; //解压后的文件大小
	int ndiredeal;  // 是否直接处理
	int trytype;    // 高16位网络，低16位业务按位
	int nettype;
	int dbid;	
	time_t  importTime;
	int statstatus;
	int project_ID; // 项目类型ID		
	int testtype;   // 测试类型----		
	int year;       // 所属年份		
	int testdate;   // 测试日期	
	int batch;	    // 所属轮次（没有轮次填-1）		
	int areatype;   // 所属地域类型ID		
	int areaid;     // 所属地域ID		
	int agentid;    // 所属代维公司ID		
	int devtypeid;  // 所属测试设备类型ID		
	int orgfiletypeid; // 原始文件类型ID		
	int sertypeid;  // 业务类型ID		
	int staffid;	// 所属员工ID（没有填-1）		
	int attenuation; // 衰减值		
	int pjsubid1;	// 项目子类型ID1		
	int pjsubid2;	// 项目子类型ID2
	int carrierid;  // 运营商ID
	int areapointLng; //CQT地域值的itllongitude
	int areapointLat; //CQT地域值的itllatitude
	short wMsChipType;    //手机芯片厂商（enum eMSCHIPTYPE）
	short wMsSubType; //手机具体型号，取值为（enum eMSTYPE）
	int nExeId;  // exeid
	//使用宏AppendComment追加字符串描述
	char szcomment[256];
	char szDecodeProc[256]; //解析过程的状态描述信息
	char szMsName[256]; // 测试手机信息
	char szTestSoftName[256]; // 测试软件名称
	char szTestSoftVersion[256]; // 测试软件版本号

	//修改tb_log_file中的staffid值，用于统计上下行
	void AlterStaffid(int MsgID)
	{
		//1:DownLoad, 2:UpLoad, 3:混合
		if ((MsgID & 0xFFFFFF00) == 0x7fff0200) //DownLoad
		{
			if (this->staffid == -1)
			{
				this->staffid = 0;
			}

			this->staffid |= 0x00000001;
		}
		else if ((MsgID & 0xFFFFFF00) == 0x7fff0300) //UpLoad
		{
			if (this->staffid == -1)
			{
				this->staffid = 0;
			}
			
			this->staffid |= 0x00000002;
		}
	};
} tSTRU_CFGPATH_INFO;

struct MSG_HEAD 
{
	UINT msgId;
	BYTE direction;
	BYTE channelType;
	WORD length;
	UINT second;
	UINT usecond;
};

typedef struct MT_PKG_HEADER
{
	BYTE flag;
	BYTE port;
	WORD len;
	int sec;
	UINT usec;
} tMT_PKG_HEADER;

enum eMsgType
{
	eMsgType_L3 = 0,
	eMsgType_RTP,
	
	eMsgType_NONE
};

typedef struct STRU_BASE_INFO
{
	time_t  nStartTime;
	time_t  nMSec;
	time_t  nEndTime;
	time_t  nEndMSec;
	time_t  nDataDate;
	double fDistance; //单位：米
	double fDuration; //单位：秒
	int nTopLong;
	int nTopLat;
	int nBottomLong;
	int nBottomLat;
	int nLastLong;
	int nLastLat;
	int nTestSchemeVer;
	int nCarrierType[eCARRIER_NONE];
	int nNrCarrierType[eCARRIER_NONE];//5G频点统计的运营商信息
	int nPlmnCarrier[ePLMN_NONE];
	int nNrPlmnCarrier[ePLMN_NONE];//5G移动网络代码统计的运营商信息
	int nDuplexMode[eDuplexMode_NONE];
	char szSam1Tbname[64];
	char szSam2Tbname[64];
	char szMsgTbname[eMsgType_NONE][64];
	char szEvtTbname[64];
	char szBackFillGpsTbname[64];
} tSTRU_BASE_INFO;

typedef struct STRU_FILE_INFO
{
	tSTRU_BASE_INFO tBInfo;
	tSTRU_CFGPATH_INFO tPath;
} tSTRU_FILE_INFO;

// 该结构长度是 256
typedef struct STRU_MiddleFile_Info
{
	int nFileid;
	int nFiletype;
	int nMsPort;
	int nMsType;
	int nGpsPort;
	int nGpsType;
	int nNetType;

	int nDataDate;   // 日期
	int nStartTime;  // 开始时间

	WORD nVersion;  // LG4
	WORD bCompress; // LG4

	int nOrigiFileSize; //原始文件大小
	
	BYTE UserCfg;
	BYTE carrierid; //运营商
	short sertypeid; //测试类型

	char szMsName[64]; // 测试手机信息
	char szTestSoftName[64]; // 测试软件名称
	char szTestSoftVersion[64]; // 测试软件版本号

	BYTE nReserve[256 - 4 * 12 - 64 * 3];

	void Clear(void)
	{
		memset(this, -1, sizeof(STRU_MiddleFile_Info));
		memset(szMsName, 0, sizeof(szMsName));
		memset(szTestSoftName, 0, sizeof(szTestSoftName));
		memset(szTestSoftVersion, 0, sizeof(szTestSoftVersion));
		memset(nReserve, 0, sizeof(nReserve));
	};
} tSTRU_MiddleFile_Info;	

//////////////////////////////////////////// GSM/GPRS EDGEDT BEGIN///////////////////////////////////////
typedef struct STRU_CommonInformation
{
	int SamSeq;		// int  采样点ID
	int	Time;		// int	时间（包含日期）
	int	MSec;		// int	毫秒数
	int	MsType;		// int	测试设备类型和型号
	int	MsSeq;		// int	测试手机号
	int	Longitude;	// int	采样点经度
	int	Latitude;	// int	采样点纬度
	int	Altitude;	// int	采样点高度
	int	Speed;		// int	采样点速度
	int	Mode; 		// int	手机当前模式
	int GpsMode;    // Gps打点模式
	int Current_Network_Type;
	int CurrNetType_MsgId; // 仅通过消息识别的网络类型
	int AlarmType;  // 告警类型 0-非告警, 1-测试模块重启
	__int64 QualcommTimeStamp; // 高通芯片的手机时间戳（毫秒）
	__int64 MarkpointLng; // 鼎利打点测试坐标
	__int64 MarkpointLat; // 鼎利打点测试坐标

	void Clear(void)
	{
		SamSeq = 0;		
		Time = 0;		
		MSec = 0;		
		MsType = 0;		
		MsSeq = 0;		
		Longitude = 0;	
		Latitude = 0;	
		Altitude = 0;	
		Speed = 0;		
		Mode = 255; 
		GpsMode = 0;
		Current_Network_Type = eCurrNetType_Invalid;
		CurrNetType_MsgId = 255;
		AlarmType = 0;
		QualcommTimeStamp = 0;

		MarkpointLng = 0;
		MarkpointLat = 0;
	};
} tSTRU_CommonInformation;

typedef struct STRU_HXMOS
{
	int LAC;
	int CI;
	int BCCH;
	int PESQ_LQ;
	int PESQ_SCORE;
	int PESQ_DELAY;

	void Clear(void)
	{
		LAC = 0;
		CI = 0;
		BCCH = 0;
		PESQ_LQ = -10000000;
		PESQ_SCORE = -10000000;
		PESQ_DELAY = -10000000;
	};
} tSTRU_HXMOS;

typedef struct STRU_MESSAGE
{
	int MsgID;
	int Direction;           // 0:未知    1:下行    2:上行
	char Message[MSG_FIELD_LENGTH];
	char Block1Code[MSG_FIELD_LENGTH];
	char Block2Code[MSG_FIELD_LENGTH];
	void Clear(void)
	{
		MsgID = 0;
		Direction = 0;
		memset(Message, 0, sizeof(Message));
		memset(Block1Code, 0, sizeof(Block1Code));
		memset(Block2Code, 0, sizeof(Block2Code));
	};
} tSTRU_MESSAGE;

// 数据业务应用层性能, 解析源码
typedef struct STRU_Application
{
	int PESQScore;
	int PESQLQ;
	int PESQMos;
	int POLQA_Score_SWB;

	int	AppBytesReceived;	// int	应用层接收字节数
	int	AppBytesSent;		// int	应用层发送字节数
	int	AppThroughputDL; 	// int	下行应用层吞吐量
	int	AppThroughputUL;	// int	上行应用层吞吐量

	int	FTP_Download_Bytes;
	int	FTP_Upload_Bytes;
	int	FTP_Download_Rate;	// int	FTP下载速率(bps)
	int	FTP_Upload_Rate;	// int	FTP上传速率(bps)
	int	Http_Download_Rate;	// int	HTTP下行速率(bps)
	int	Http_Upload_Rate;	// int	HTTP上行速率(bps)
	int	Http_Page_Rate;		// int	HTTP Page速率(bps)
	int	Email_POP_Rate;		// int	接收邮件的速率(bps)
	int	Email_SMTP_Rate;	// int	发送邮件的速率(bps)
	int	Ping_Delay; 		// int	Ping时延
	int	Ping_Size;			// int	Ping包字节数

	int APP_type;
	int APP_Status;
	int APP_Ftp_Status;
	int APP_DataStatus_DL;
	int APP_DataStatus_UL;
	int APP_TotalSize;				// 4字节（原来）
	INT64 APP_TotalSize_64bits;	// 8字节（LTE已用）
	int APP_TotalTime;
	int APP_TransferedSize;
	int APP_TransferedTime;
	int APP_Speed;
	int APP_Average_Speed;

	int APP_Continue_Type_DL; // 标识App的Continue，用于给栅格进行统计
	int APP_Continue_Type_UL; // 标识App的Continue，用于给栅格进行统计

	void Clear(void)
	{
		PESQScore = INITIALVALUE_FLOAT;
		PESQLQ = INITIALVALUE_FLOAT;
		PESQMos = INITIALVALUE_FLOAT;
		POLQA_Score_SWB = INITIALVALUE_FLOAT;

		AppBytesReceived = -1;	
		AppBytesSent = -1;		
		AppThroughputDL = -255; 	
		AppThroughputUL = -255;	

		FTP_Download_Bytes = 0;
		FTP_Upload_Bytes = 0;
		FTP_Download_Rate = -1;		
		FTP_Upload_Rate = -1;			
		Http_Download_Rate = -1;		
		Http_Upload_Rate = -1;	
		Http_Page_Rate = -1;
		Email_POP_Rate = -1;			
		Email_SMTP_Rate = -1;			
		Ping_Delay = -1; 		
		Ping_Size = -1;	
		
		APP_type = -1;
		APP_Status = -1;
		APP_Ftp_Status = -1;
		APP_DataStatus_DL = -1;
		APP_DataStatus_UL = -1;
		APP_TotalSize = -1;
		APP_TotalSize_64bits = -1;
		APP_TotalTime = -1;
		APP_TransferedSize = -1;
		APP_TransferedTime = -1;
		APP_Speed = -1;
		APP_Average_Speed = -1;

		APP_Continue_Type_DL = -1;
		APP_Continue_Type_UL = -1;
	};
} tSTRU_Application;

//ATU陀螺仪打点测试图片信息
typedef struct STRU_CQT_ATU_FPS
{
	void Clear(void){
		memset(this, 0, sizeof(STRU_CQT_ATU_FPS));
	};
	char	TestImage   [MAX_PATH];
	char	Floor       [MAX_PATH];
	char	KeyPointID  [MAX_PATH];
	char	KeyPointInfo[MAX_PATH];
	char	AlignPoint  [MAX_PATH];
	char	FloorChange [MAX_PATH];
} tSTRU_CQT_ATU_FPS;

typedef struct STRU_TB_CQT_ATU_FPS
{
	int ifileid;
	int sequencenumber;
	//FPS
	tSTRU_CQT_ATU_FPS tfps;
} tSTRU_TB_CQT_ATU_FPS;
///////////////////////////////////////////////////////////////////////////
typedef struct Stru_RtpPacket
{
	void Clear(void)
	{
		CodecType = INITIALVALUE_MAXUINT;
		MediaType = INITIALVALUE_MAXUINT;
		Timestamp = INITIALVALUE_MAXUINT;
		nLastTimestamp = INITIALVALUE_MAXUINT;
		nCurRtpSsrc = INITIALVALUE_MAXUINT;
		nLastRtpSsrc = INITIALVALUE_MAXUINT;
		nCurRtpSeq = INITIALVALUE_MAXUINT;
		nLastRtpSeq = INITIALVALUE_MAXUINT;
		nRtpPacketLostNum = 0;
		nRtpPacketNum = 0;
		Payload = 0;
		HandSetTime = INITIALVALUE_MAXUINT;
		nLastHandSetTime = INITIALVALUE_MAXUINT;
	};
	UINT CodecType;
	UINT MediaType;
	UINT Timestamp;
	UINT nLastTimestamp;
	UINT nCurRtpSsrc;
	UINT nCurRtpSeq;
	UINT nLastRtpSsrc;
	UINT nLastRtpSeq;
	UINT nRtpPacketLostNum;
	UINT nRtpPacketNum;
	UINT Payload;
	__int64 HandSetTime;
	__int64 nLastHandSetTime;
}tRtpPacket;
///////////////////////////////////////////////////////////////////////////
enum eSam_Field_Type
{
	eSam_Index = -1,
	eSam_MtMsg_Version = 0,
	eSam_APP_type = 1,// app层业务类型,包括FTPDownload,FTPUPLOAD,emailPOP3/IMAP,email SMTP,HTTP Download,WAP_Page,WAP_Download,MMS_Send,MMS_Recv等
	eSam_APP_Status,			// 包括FTPDownload状态，FTPUPLOAD状态，emailPOP3/IMAP状态，email SMTP状态，HTTP Download状态等
	eSam_APP_DataStatus_DL,		// 用于标识当前处于HSPA,R4384,R4128,R464,EDGE/GPRS状态
	eSam_APP_DataStatus_UL,		// 用于标识当前处于HSPA,R4384,R4128,R464,EDGE/GPRS状态
	eSam_APP_TransferedSize,	// BYTES
	eSam_APP_TransferedTime,	// ms
	eSam_APP_Speed,				// bps
	eSam_APP_TotalSize,			// BYTES
	eSam_APP_Average_Speed,		// bps
	eSam_APP_TotalTime,			// ms
	eSam_APP_FRAMES,			// 原始值
	eSam_StrComment,			// 字符串

	eSam_PESQ_LQ,				// 原始值*1000
	eSam_PESQ_Score,			// 原始值*1000
	eSam_PESQ_MOS,				// 原始值*1000

	eSam_Mode,					// 原始值
	eSam_LAC,					// 原始值
	eSam_CI,					// 原始值 5GNR的时候NCI已经超出了int范围，此处写文件时统一入成int64
	eSam_RAC,					// 原始值
	eSam_VoiceHangup_Reason,  /**原始值 Event-Voice Hangup原因值:(0x00000000	Reason: Normal, 0x00000001	Reason: Blocked, 0x00000002	Reason: Droped, 0x00000005 MT Finished, 0x00000006	Reason: StopTest)**/

	eSam_APP_HSPA_TransferedSize,	// ATU 应用层当前下载(GPRSEDGE)字节数,单位(BYTE)
	eSam_APP_R4_TransferedSize,		// ATU 应用层当前下载(R4)字节数,单位(BYTE)
	eSam_APP_EDGE_TransferedSize,   // ATU 应用层当前下载(HSDPA)字节数,单位(BYTE)
	eSam_APP_HSPA_TransferedTime, // ATU 应用层当前下载(HSDPA)时长,单位(ms)
	eSam_APP_R4_TransferedTime,    // ATU 应用层当前下载(R4)时长,单位(ms)
	eSam_APP_EDGE_TransferedTime,// ATU 应用层当前下载(GPRSEDGE)时长,单位(ms)
	eSam_APP_HSPA_TotalSize, // ATU 应用层总下载(HSDPA)字节数,单位(BYTE)
	eSam_APP_R4_TotalSize,    // ATU 应用层总下载(R4)字节数,单位(BYTE)
	eSam_APP_EDGE_TotalSize,// ATU 应用层总下载(GPRSEDGE)字节数,单位(BYTE)
	eSam_APP_HSPA_TotalTime, // ATU 应用层总下载(HSDPA)时长,单位(ms)
	eSam_APP_R4_TotalTime,    // ATU 应用层总下载(R4)时长,单位(ms)
	eSam_APP_EDGE_TotalTime,// ATU 应用层总下载(GPRSEDGE)时长,单位(ms)

	eSam_Ping_Ip,    //原始值, int表示************(4247718666)
	eSam_Ping_Size,  //原始值, 单位:Byte
	eSam_Ping_TTL,   //原始值, 单位:次 Time to Live生存周期(经过一个网络设备减一)
	eSam_Ping_Delay, //原始值, 单位:ms 
	eSam_Ping_ULRate,//原始值*1000
	eSam_Ping_DLRate,//原始值*1000
	eSam_Ping_Jitter,//原始值, 单位:ms invalid：0xFFFFD8F1(-9999)

	eSam_HTTPPage_Delay,       //原始值, 单位:ms 
	eSam_HTTPPage_ConnectTime, //原始值, 单位:ms 

	eSam_PPPDial_TestKind,//原始值
	eSam_PPPDial_NetFlag,//原始值

	//////////////////////////////////////////////////////////////////////////
	eSam_APP_LTE_TransferedTime, // ATU 应用层当前下载(LTE)时长,单位(ms)
	eSam_APP_LTE_TransferedSize,	// ATU 应用层当前下载(LTE)字节数,单位(BYTE)
	eSam_APP_LTE_TotalTime, // ATU 应用层总下载(LTE)时长,单位(ms)
	eSam_APP_LTE_TotalSize_bits64, // ATU 应用层总下载(LTE)字节数,单位(BYTE)
	eSam_APP_TotalSize_64bits, //UINT64(64bits, BYTES, 替代原来的eSam_APP_TotalSize)
	//////////////////////////////////////////////////////////////////////////

	eSam_Current_Network_Type,   // 原始值（统计是否脱网，脱网值为128）
	eSam_APP_type_Per,           // app层业务类型，每个采样点都写（eSam_APP_type则不是）
	eSam_APP_Status_Per,         // 用于标记测试业务状态，每个采样点都写（eSam_APP_Status则不是）

	eSam_Network_Carrier,       // 原始值，运营商
	eSam_ATU_Device_AlarmType,  // 原始值，设备告警类型
	eSam_Layer3_MessageId,      // 原始值，消息ID

	//////////////////////////////////////////////////////////////////////////
	eSam_APP_HSPA_ADV_TransferedTime, // ATU 应用层当前下载(HSPA+)时长,单位(ms)
	eSam_APP_HSPA_ADV_TransferedSize,	// ATU 应用层当前下载(HSPA+)字节数,单位(BYTE)
	eSam_APP_HSPA_ADV_TotalTime, // ATU 应用层总下载(HSPA+)时长,单位(ms)
	eSam_APP_HSPA_ADV_TotalSize, // ATU 应用层总下载(HSPA+)字节数,单位(BYTE)
	eSam_APP_EVDO_TransferedTime, // ATU 应用层当前下载(EVDO)时长,单位(ms)
	eSam_APP_EVDO_TransferedSize,	// ATU 应用层当前下载(EVDO)字节数,单位(BYTE)
	eSam_APP_EVDO_TotalTime, // ATU 应用层总下载(EVDO)时长,单位(ms)
	eSam_APP_EVDO_TotalSize, // ATU 应用层总下载(EVDO)字节数,单位(BYTE)
	eSam_APP_1x_TransferedTime, // ATU 应用层当前下载(1x)时长,单位(ms)
	eSam_APP_1x_TransferedSize,	// ATU 应用层当前下载(1x)字节数,单位(BYTE)
	eSam_APP_1x_TotalTime, // ATU 应用层总下载(1x)时长,单位(ms)
	eSam_APP_1x_TotalSize, // ATU 应用层总下载(1x)字节数,单位(BYTE)
	//////////////////////////////////////////////////////////////////////////

	eSam_APP_Continue_Type_DL, // 标识App的Continue，用于给栅格进行统计
	eSam_APP_Continue_Type_UL, // 标识App的Continue，用于给栅格进行统计

	eSam_Qualcomm_TimeStamp_64bits, // 高通芯片的手机时间戳（毫秒），目前只是插到消息的blockcode字段

	eSam_APP_HttpBrowse_URL_Status, // 原始值：1-OK，2-Failed
	eSam_APP_HttpBrowse_URL_Size, // 原始值：Byte
	eSam_APP_HttpBrowse_URL_Time, // 原始值：毫秒
	eSam_APP_HttpBrowse_URL_Speed_avg, //原始值：bps

	eSam_APP_NR_TransferedTime, // ATU 应用层当前下载(LTE)时长,单位(ms)
	eSam_APP_NR_TransferedSize,	// ATU 应用层当前下载(LTE)字节数,单位(BYTE)
	eSam_APP_NR_TotalTime, // ATU 应用层总下载(LTE)时长,单位(ms)
	eSam_APP_NR_TotalSize_bits64, // ATU 应用层总下载(LTE)字节数,单位(BYTE)

	eSam_GPRS_BLER = 1000,		// %原始值*1000
	eSam_GPRS_RLC_UL_Thr,		// bps
	eSam_GPRS_RLC_DL_Thr,		// bps
	eSam_GPRS_RLC_UL_RTX_Rate,	// 原始值*1000
	eSam_GPRS_RLC_DL_RTX_Rate,	// 原始值*1000
	eSam_EDGE_UL_MCS,			// 原始值
	eSam_EDGE_DL_MCS,			// 原始值
	eSam_Gsm_RxlevSub,			// 原始值*1000
	eSam_Gsm_RxlevFull,         // 原始值*1000
	eSam_Gsm_RxqualSub,			// 原始值
	eSam_Gsm_RxqualFull,		// 原始值
	eSam_Gsm_RxLevBCCH,         // 原始值*1000
	eSam_Gsm_TA,
	eSam_Gsm_TXPOWER,			// 原始值*1000
	eSam_Gsm_BCCH,				// 原始值
	eSam_Gsm_BSIC,				// 原始值
	eSam_Gsm_SQI,				// 原始值
	eSam_EDGE_UL_TS_NUM,		// EDGE 上行时隙数 原始值
	eSam_EDGE_DL_TS_NUM,		// EDGE 下行时隙数 原始值
	eSam_EDGE_DL_TS_DURATION,	// EDGE 下行时隙持续时长
	eSam_Gsm_SpeechCode,		// 原始值
	eSam_Gsm_C_I,				// 原始值*1000 eSam_Gsm_TCH_CI
	eSam_Gsm_TCH,               // 原始值
	eSam_Gsm_RLTMax,			// 原始值
	eSam_Gsm_RLTCur,			// 原始值
	eSam_GMSK_BEP_Mean,        // 原始值
	eSam_GMSK_BEP_Variance,    // 原始值
	eSam_PSK8_BEP_Mean,        // 原始值
	eSam_PSK8_BEP_Variance,    // 原始值
	eSam_GSM_SERNEIINFO,       //邻区信息
	eSam_Gsm_BCCH_CI,		   // 原始值*1000
	eSam_Gsm_TCHCI_ARFCN,      // 原始值(TCH C/I上的频点)
	eSam_Gsm_BCCHCI_ARFCN,     // 原始值(BCCH C/I上的频点)
	eSam_Gsm_AMRULCodecCurr,   // 原始值
	eSam_Gsm_AMRDLCodecCurr,   // 原始值
	eSam_GPRS_LLC_UL_Thr,		// bps
	eSam_GPRS_LLC_DL_Thr,		// bps

	eSam_CSFB_MR_MEAS_VALID,    // 原始值（MR报告是否有效）
	eSam_CSFB_RR_SACCH_RxQual,    // 原始值（临时使用）

	eSam_TD_PCCPCH_RSCP = 3000,	// 原始值*1000
	eSam_TD_PCCPCH_C_I,			// 原始值*1000
	eSam_TD_UL_RLC_Thr,			// bps  RRCConnect setup~release
	eSam_TD_DL_RLC_Thr,			// bps  RRCConnect setup~release
	eSam_TD_BLER,				// 原始值*1000
	eSam_TD_RLC_UL_RTX_Rate,	// 原始值*1000 RRCConnect setup~release
	eSam_TD_CPI,				// 原始值
	eSam_TD_UARFCN,				// 原始值
	eSam_TD_UL_PDCP_Thr,		// RRCConnect setup~release
	eSam_TD_DL_PDCP_Thr,		// RRCConnect setup~release
	eSam_TD_DPCH_C_I,		    // 原始值*1000
	eSam_TD_RLC_DL_ERR_Rate,	// 原始值*1000 RRCConnect setup~release
	eSam_TD_DPCH_DURATION,		// DPCH时长
	eSam_TD_DPCH_CODE_NUM,		// DPCH码道数
	eSam_TD_HS_SCHEDULED_COUNT, // HSDPA 调度次数
	eSam_TD_HSDPA_CODE_NUM,		// HSDPA码道数
	eSam_TD_DPCH_RSCP,			// 原始值*1000
	eSam_TD_SERNEIINFO,			// 原始值
	eSam_APP_GPRS_EDGE_Size,	// BYTES
	eSam_APP_R4_Size,			// BYTES
	eSam_APP_HSDPA_Size,		// BYTES
	eSam_TD_HSDPA_QPSKRate ,    // 原始值*1000
	eSam_TD_HSDPA_16QAMRate,    // 原始值*1000
	eSam_TD_HSDPA_MaxCQI,	    // 原始值
	eSam_TD_HSDPA_MeanCQI,	    // 原始值
	eSam_TD_HSDPA_MinCQI,	    // 原始值
	eSam_TD_HS_ScchScheduled_Rate, // 原始值*1000
	eSam_TD_HS_DSCH_ErrBlocks  , // 原始值
	eSam_TD_HS_DSCH_ErrRate    , // 原始值*1000
	eSam_TD_HS_PDSCH_ErrBlocks , // 原始值
	eSam_TD_HS_PDSCH_TotalBLER , // 原始值*1000
	eSam_TD_DPCH_UARFCN,         // 原始值(栅格统计还没做处理)
	eSam_TD_HS_WorkUARFCN,       // 原始值(栅格统计还没做处理)
	eSam_TD_HS_SCHEDULED_DURATION,// HSDPA 调度时长(毫秒)
	eSam_TD_UE_TxPower,           // 原始值*1000
	eSam_TD_HS_PDSCH_RSCP,        // 原始值*1000
	eSam_TD_HS_PDSCH_ISCP,        // 原始值*1000
	eSam_TD_HS_PDSCH_CI,          // 原始值*1000
	eSam_TD_BLER_ErrBlock,        // 原始值
	eSam_TD_BLER_TotalBlock,      // 原始值

	eSam_WCDMA_PSC = 5000,
	eSam_WCDMA_FREQ,
	eSam_WCDMA_TotalRSCP,	// 原始值*1000
	eSam_WCDMA_TotalEc2Io,	// 原始值*1000
	eSam_WCDMA_RxPower,	    // 原始值*1000
	eSam_WCDMA_TxPower,	    // 原始值*1000
	eSam_WCDMA_SIR,         // 原始值*1000
	eSam_WCDMA_BLER,	    // 原始值*1000
	eSam_WCDMA_HS_MeanCQI,	// 原始值
	eSam_WCDMA_DL_PDU_BYTE, // 原始值
	eSam_WCDMA_DL_SDU_BYTE, // 原始值
	eSam_WCDMA_DL_PDU_Err,	// 原始值
	eSam_WCDMA_DL_TIME,		// 原始值
	eSam_WCDMA_UL_PDU_BYTE, // 原始值
	eSam_WCDMA_UL_SDU_BYTE, // 原始值
	eSam_WCDMA_UL_PDU_RTX,  // 原始值
	eSam_WCDMA_UL_TIME,		// 原始值
// 	eSam_WCDMA_DL_PDU_Thr,  // 原始值
// 	eSam_WCDMA_UL_PDU_Thr,  // 原始值
// 	eSam_WCDMA_UL_SDU_Thr,  // 原始值
// 	eSam_WCDMA_DL_SDU_Thr,  // 原始值
// 	eSam_WCDMA_DL_PDU_Err_Rate, // 原始值*1000
// 	eSam_WCDMA_UL_PDU_RTX_Rate, // 原始值*1000
	eSam_WCDMA_ACTIVESET,		// 原始值
	eSam_WCDMA_SERNEIINFO,		// 原始值
	eSam_WCDMA_HS_MedianCQI,	// 原始值
	eSam_WCDMA_maxRSCP,		// 原始值*1000
	eSam_WCDMA_maxEcIo,		// 原始值*1000

	eSam_WCDMA_HS_MPO,             // 原始值
	eSam_WCDMA_HS_MeanCQIminusMPO, // 原始值

	eSam_WCDMA_HSDPA_QPSKRate ,       // 原始值*1000
	eSam_WCDMA_HSDPA_16QAMRate     ,  // 原始值*1000
	eSam_WCDMA_HSDPA_DSCH_ErrRate    ,// 原始值*1000
	eSam_WCDMA_HSDPA_DSCH_ErrBlock   ,// 原始值
	eSam_WCDMA_HSDPA_DSCH_NackRate   ,// 原始值*1000
	eSam_WCDMA_HSDPA_DSCH_AckRate    ,// 原始值*1000
	eSam_WCDMA_HSDPA_DSCH_ThrPut     ,// bps ,未解码,sony有
	eSam_WCDMA_HSDPA_DSCH_DeSuccRate ,// 原始值*1000
	eSam_WCDMA_HSDPA_HARQResponseRate,// 原始值*1000
	eSam_WCDMA_HSDPA_ReqPhyLayerRate ,// 原始值
	eSam_WCDMA_HSDPA_DSCH_BLER       ,// 原始值*1000
	eSam_WCDMA_HSDPA_Shd_PhysLayer_Rate,// bps
	eSam_WCDMA_HSDPA_Srv_PhysLayer_Rate,// bps
	eSam_WCDMA_HSDPA_MacLayer_Rate     ,// bps

	eSam_WCDMA_HSUPA_MACThr          ,// bps
	eSam_WCDMA_HSUPA_SerThr          ,// bps
	eSam_WCDMA_HSUPA_HapBitRate      ,// 原始值*1000
	eSam_WCDMA_HSUPA_1RTX_Rate       ,// 原始值*1000
	eSam_WCDMA_HSUPA_2RTX_Rate       ,// 原始值*1000
	eSam_WCDMA_HSUPA_SGIAver         ,// 原始值
	eSam_WCDMA_HSUPA_ACKRate         ,// 原始值*1000
	eSam_WCDMA_HSUPA_NackRate        ,// 原始值*1000
	eSam_WCDMA_HSUPA_HARQResponseRate,// 原始值*1000
	eSam_WCDMA_HSUPA_ETFCI_LtdMP_Rate,// 原始值*1000
	eSam_WCDMA_HSUPA_ETFCI_LtdSG_Rate,// 原始值*1000
	eSam_WCDMA_HSUPA_ETFCI_LtdBo_Rate,// 原始值*1000
	eSam_WCDMA_HSUPA_UEFrame_Usage   ,// 原始值*1000
	eSam_WCDMA_PRIMARY_CPICH_TX_Power,// 原始值
	eSam_WCDMA_HSDPA_64QAMRate       ,// 原始值*1000

	eSam_CDMA_Rx_RLP_Thr = 7000, // 原始值
	eSam_CDMA_Tx_RLP_Thr,        // 原始值
	eSam_CDMA_RLP_Err_Rate,// 原始值*1000
	eSam_CDMA_RLP_RTX_Rate,// 原始值*1000
	eSam_CDMA_RX_PHYS_Rate,// 原始值 app应用建立连接到结束
	eSam_CDMA_TX_PHYS_Rate,// 原始值 app应用建立连接到结束
	eSam_CDMA_FFER,		   // 原始值*1000
	eSam_CDMA_TotalEcIo,   // 原始值*1000
	eSam_CDMA_ReferenceEcIo,// 原始值*1000
	eSam_CDMA_MaxEc,       // 原始值*1000
	eSam_CDMA_ReferenceEc, // 原始值*1000
	eSam_CDMA_RxAGC,       // 原始值*1000
	eSam_CDMA_TxAGC,       // 原始值*1000
	eSam_CDMA_TxPower,     // 原始值*1000
	eSam_CDMA_RxPower,
	eSam_CDMA_Freq,
	eSam_CDMA_RefPN,

	eSam_EVDO_EV_RXRLPThr = 9000,// 原始值
	eSam_EVDO_EV_TXRLPThr,// 原始值
	eSam_EVDO_EV_Duplicate_Rate,// 原始值*1000
	eSam_EVDO_EV_RLPErrRate,// 原始值*1000
	eSam_EVDO_EV_RLPRTXRate,// 原始值*1000
	eSam_EVDO_RxPacketThroughput,// 原始值
	eSam_EVDO_TxPacketThroughput,// 原始值
	eSam_EVDO_RxAGC0,// 原始值*1000
	eSam_EVDO_RxAGC1,// 原始值*1000
	eSam_EVDO_TxAGC,// 原始值*1000
	eSam_EVDO_1, // 废弃
	eSam_EVDO_2, // 废弃
	eSam_EVDO_3, // 废弃
	eSam_EVDO_4, // 废弃
	eSam_EVDO_EV_Freq,
	eSam_EVDO_Serving_Sector_PN,
	eSam_EVDO_UATI,
	eSam_EVDO_Total_SiNR,// 原始值*1000
	eSam_EVDO_RxPER,// 原始值*1000
	eSam_EVDO_TxPER,// 原始值*1000
	eSam_EVDO_DRC_Rate,// 原始值

	eSam_MTR_RxLev_Full_DL = 11000, // 原始值*1000
	eSam_MTR_RxLev_Full_UL,         // 原始值*1000
	eSam_MTR_RxLev_Sub_DL,          // 原始值*1000
	eSam_MTR_RxLev_Sub_UL,          // 原始值*1000
	eSam_MTR_RxQual_Full_DL,        // 原始值
	eSam_MTR_RxQual_Full_UL,        // 原始值
	eSam_MTR_RxQual_Sub_DL,         // 原始值
	eSam_MTR_RxQual_Sub_UL,         // 原始值
	eSam_MTR_DTX_DL,                // 原始值(0:not used, 1:used)
	eSam_MTR_DTX_UL,                // 原始值(0 = discontinuous transm not used by MS, 1 = discontinuous transm used by MS)
	eSam_MTR_TA,                    // 原始值
	eSam_MTR_SQI,                    // 原始值

	eSam_WLAN_Attempt_Time = 13000,  // 一次事件里的第几次细分事件
	eSam_WLAN_TTL,                   // (Time To Live ) 生存时间 
	eSam_WLAN_AP_Channel,            // 信道号(原始值)
	eSam_WLAN_AP_Freq,               // 频点(原始值)
	eSam_WLAN_AP_RSSI,               // RSSI信号强度(dBm)(原始值 * 1000)
	eSam_WLAN_AP_SFI,                // 同频AP干扰场强(dBm)(原始值 * 1000)
	eSam_WLAN_AP_NFI,                // 邻频AP干扰场强(dBm)(原始值 * 1000)
	eSam_WLAN_AP_CI,                 // 载干比(db)(原始值 * 1000)
	eSam_WLAN_BSS_Active_Users,      // 关联AP下并发用户数(原始值)
	eSam_WLAN_BSS_Total_Users,       // 关联AP下发现用户数(原始值)
	eSam_WLAN_Channel_Active_Users,  // 信道并发用户数(原始值)
	eSam_WLAN_Channel_Total_Users,   // 信道发现用户数(原始值)
	eSam_WLAN_NFI_Channel,           // 邻频干扰信道号(原始值)
	eSam_WLAN_RxThrput,              // 用户接收数据帧吞吐率(bps)(原始值)(除1000即改为kb/s，即和CDS对上)
	eSam_WLAN_RxRetrans,             // 用户接收数据帧重传率(%)(原始值 * 1000)
	eSam_WLAN_RxPhysicalRate,        // 用户接收数据平均调制速率(bps)(原始值)(除1000000即改为Mb/s，即和CDS对上)
	eSam_WLAN_TxThrput,              // 用户发送数据帧吞吐率(bps)(原始值)(除1000即改为kb/s，即和CDS对上)
	eSam_WLAN_TxRetrans,             // 用户数据帧重传率(%)(原始值 * 1000)
	eSam_WLAN_TxPhysicalRate,        // 用户发送数据平均调制速率(bps)(原始值)(除1000000即改为Mb/s，即和CDS对上)

	eSam_LTE_EARFCN = 15000,		// 原始值
	eSam_LTE_PCI,				    // 原始值
	eSam_LTE_RSRP,				    // 原始值 * 1000
	eSam_LTE_RSRQ,				    // 原始值 * 1000
	eSam_LTE_RSSI,				    // 原始值 * 1000
	eSam_LTE_SINR,				    // 原始值 * 1000
	eSam_LTE_SERNEIINFO,			// 邻区信息[组数(WORD)+值(int)]
	eSam_LTE_PUSCH_Power,           // 原始值

	eSam_LTE_PDCP_DL_Throughput,    // 原始值(bps)
	eSam_LTE_PDCP_UL_Throughput,    // 原始值(bps)

	eSam_LTE_PDCCH_DL_Grant_Count,  // 原始值
	eSam_LTE_PDCCH_UL_Grant_Count,  // 原始值
	eSam_LTE_PRBINFO_DL,            // [汇总(int)+组数(WORD)+Slot0(WORD)+Slot1(WORD)]（统计表写的是汇总值）
	eSam_LTE_PRBINFO_UL,            // [汇总(int)+组数(WORD)+Slot0(WORD)+Slot1(WORD)]（统计表写的是汇总值）

	eSam_LTE_UL_MCS_INFO,           // [组数(WORD)+MCS0 ~ MCS31(WORD*32)]
	eSam_LTE_DL_Code0_MCS_INFO,     // [组数(WORD)+MCS0 ~ MCS31(WORD*32)]
	eSam_LTE_DL_Code1_MCS_INFO,     // [组数(WORD)+MCS0 ~ MCS31(WORD*32)]

	eSam_LTE_PDSCH_BLER,            // 原始值 * 1000
	eSam_LTE_PDSCH_Code0_BLER,      // 原始值 * 1000
	eSam_LTE_PDSCH_Code1_BLER,      // 原始值 * 1000
	eSam_LTE_PDCCH_BLER,            // 原始值 * 1000
	eSam_LTE_PDCCH_Estimated_BLER,  // 原始值 * 1000
	eSam_LTE_PUSCH_Initial_BLER,    // 原始值 * 1000
	eSam_LTE_PUSCH_Residual_BLER,   // 原始值 * 1000
	
	eSam_LTE_UL_HARQ_ACK,           // 原始值 
	eSam_LTE_UL_HARQ_NACK,          // 原始值
	eSam_LTE_DL_Code0_HARQ_ACK,     // 原始值
	eSam_LTE_DL_Code0_HARQ_NACK,    // 原始值
	eSam_LTE_DL_Code1_HARQ_ACK,     // 原始值
	eSam_LTE_DL_Code1_HARQ_NACK,    // 原始值
	
	eSam_LTE_Modulation_UL_QPSK,    // 原始值
	eSam_LTE_Modulation_UL_16QAM,   // 原始值
	eSam_LTE_Modulation_UL_64QAM,   // 原始值

	eSam_LTE_Modulation_DL_Code0_QPSK,    // 原始值
	eSam_LTE_Modulation_DL_Code0_16QAM,   // 原始值
	eSam_LTE_Modulation_DL_Code0_64QAM,   // 原始值

	eSam_LTE_Modulation_DL_Code1_QPSK,    // 原始值
	eSam_LTE_Modulation_DL_Code1_16QAM,   // 原始值
	eSam_LTE_Modulation_DL_Code1_64QAM,   // 原始值

	eSam_LTE_Wideband_CQI_for_CW0,  // 原始值
	eSam_LTE_Wideband_CQI_for_CW1,  // 原始值

	eSam_LTE_Rank_Indicator,        // 原始值
	eSam_LTE_Transmission_Mode,     // 原始值

	eSam_LTE_PHY_DL_Throughput,    // 原始值(bps)
	eSam_LTE_PHY_UL_Throughput,    // 原始值(bps)
	eSam_LTE_MAC_DL_Throughput,    // 原始值(bps)
	eSam_LTE_MAC_UL_Throughput,    // 原始值(bps)
	eSam_LTE_RLC_DL_Throughput,    // 原始值(bps)
	eSam_LTE_RLC_UL_Throughput,    // 原始值(bps)
	eSam_LTE_PHY_Code0_DL_Throughput,    // 原始值(bps)
	eSam_LTE_PHY_Code1_DL_Throughput,    // 原始值(bps)
	eSam_LTE_PHY_DL_Single_Stream,       // 原始值(bps)
	eSam_LTE_PHY_DL_Dual_Stream,         // 原始值(bps)

	eSam_LTE_PLMN,                 // 原始值
	eSam_LTE_Power_Headroom,       // 原始值 * 1000

	eSam_LTE_RSRP_Rx0,             // 原始值 * 1000
	eSam_LTE_RSRP_Rx1,             // 原始值 * 1000
	eSam_LTE_SINR_Rx0,             // 原始值 * 1000
	eSam_LTE_SINR_Rx1,             // 原始值 * 1000

	eSam_LTE_PRACH_Power,          // 原始值
	eSam_LTE_PUCCH_Power,          // 原始值
	eSam_LTE_SRS_Power,            // 原始值
	eSam_LTE_PDCCH_CFI1,           // 原始值
	eSam_LTE_PDCCH_CFI2,           // 原始值
	eSam_LTE_PDCCH_CFI3,           // 原始值
	eSam_LTE_PDCCH_CFI4,           // 原始值

	eSam_LTE_PDCCH_DCI_Format,     // 原始值

	eSam_LTE_PDSCH_Init_BLER,			 // 原始值 * 1000
	eSam_LTE_PDSCH_Init_BLER_Code0,      // 原始值 * 1000
	eSam_LTE_PDSCH_Init_BLER_Code1,      // 原始值 * 1000
	eSam_LTE_PDSCH_Remain_BLER,			 // 原始值 * 1000
	eSam_LTE_PDSCH_Remain_BLER_Code0,    // 原始值 * 1000
	eSam_LTE_PDSCH_Remain_BLER_Code1,    // 原始值 * 1000
	eSam_LTE_PUSCH_BLER,    // 原始值 * 1000

	eSam_LTE_DL_Bandwidth,           // 原始值
	eSam_LTE_UL_Bandwidth,           // 原始值

	eSam_LTE_SINR_ATU,               // 原始值 * 1000（我们的算法。集团取的是Rx0（对应我们:eSam_LTE_SINR），我们这个取的是Max(Rx0, Rx1)）
	eSam_LTE_Work_Mode,

	eSam_VOLTE_RTP_Direction,       // 原始值(1下2上)
	eSam_VOLTE_RTP_Source_SSRC,     // 原始值
	eSam_VOLTE_RTP_Sequence_Number, // 原始值
	eSam_VOLTE_RTP_Timestamp,       // 原始值
	eSam_VOLTE_POLQA_Score_SWB,     // 原始值*1000 (不再使用，赋值于eSam_PESQ_LQ)
	eSam_VOLTE_RAT_Type,            // 原始值
	eSam_VOLTE_RTP_Codec_Type,      // 原始值
	eSam_VOLTE_RTP_Media_Type,      // 原始值
	eSam_VOLTE_RTP_Payload_size,    // 原始值
	eSam_VOLTE_RTP_Packets_Lost_Num,// 原始值（RTP丢包数量）
	eSam_VOLTE_RTP_Packets_Num,     // 原始值（RTP包总数量）

	eSam_LTE_EPS_QCI,               // 原始值
	eSam_LTE_Pathloss,              // 原始值

	eSam_LTE_CCE_Type1_Count,       // 原始值
	eSam_LTE_CCE_Type2_Count,       // 原始值
	eSam_LTE_CCE_Type4_Count,       // 原始值
	eSam_LTE_CCE_Type8_Count,       // 原始值

	eSam_VOLTE_RTP_Jitter,             // 原始值(ms)
	eSam_VOLTE_RTP_StatJitterSum,      // 原始值，解码做了小粒度统计，用于栅格统计，求平均值
	eSam_VOLTE_RTP_StatJitterNum,      // 原始值，解码做了小粒度统计，用于栅格统计，求平均值

	eSam_LTE_PDSCH_PRb_Num_slot,    // 原始值
	eSam_LTE_PUSCH_PRb_Num_slot,    // 原始值
	eSam_LTE_RF_Mode,    // 原始值

	//Nr
	eSam_NR_SSB_ARFCN = 17000,      // 原始值
	eSam_NR_PointA_ARFCN,
	eSam_NR_DC_ARFCN,
	eSam_NR_PCI,
	eSam_NR_TAC,
	eSam_NR_NCI,
	eSam_NR_C_RNTI,
	eSam_NR_gNodeB_ID,

	eSam_NR_SS_RSRP,          // 原始值 * 1000
	eSam_NR_SS_RSRQ,          // 原始值 * 1000
	eSam_NR_SS_RSSI,          // 原始值 * 1000
	eSam_NR_SS_SINR,          // 原始值 * 1000
	eSam_NR_CSI_RSRP,         // 原始值 * 1000
	eSam_NR_CSI_RSRQ,         // 原始值 * 1000
	eSam_NR_CSI_RSSI,         // 原始值 * 1000
	eSam_NR_CSI_SINR,         // 原始值 * 1000
	eSam_NR_NCellInfo,        // 邻区信息[组数(WORD)+值(int)]

	eSam_NR_Throughput_SDAP_DL,    // 原始值
	eSam_NR_Throughput_PDCP_DL,    // 原始值
	eSam_NR_Throughput_RLC_DL,     // 原始值
	eSam_NR_Throughput_MAC_DL,     // 原始值
	eSam_NR_Throughput_PHY_DL,     // 原始值
	eSam_NR_Throughput_SDAP_UL,    // 原始值
	eSam_NR_Throughput_PDCP_UL,    // 原始值
	eSam_NR_Throughput_RLC_UL,     // 原始值
	eSam_NR_Throughput_MAC_UL,     // 原始值	
	eSam_NR_Throughput_PHY_UL,     // 原始值
	
	eSam_NR_Modulation_Most_DL,   // 原始值
	eSam_NR_Modulation_High_DL,   // 原始值
	eSam_NR_Modulation_Most_UL,   // 原始值
	eSam_NR_Modulation_High_UL,   // 原始值

	eSam_NR_Modulation_256QAM_Ratio_DL,  // 原始值 * 1000
	eSam_NR_Modulation_64QAM_Ratio_DL,   // 原始值 * 1000
	eSam_NR_Modulation_16QAM_Ratio_DL,   // 原始值 * 1000
	eSam_NR_Modulation_QPSK_Ratio_DL,    // 原始值 * 1000
	eSam_NR_Modulation_BPSK_Ratio_DL,    // 原始值 * 1000

	eSam_NR_Modulation_256QAM_Ratio_UL,   // 原始值 * 1000
	eSam_NR_Modulation_64QAM_Ratio_UL,    // 原始值 * 1000
	eSam_NR_Modulation_16QAM_Ratio_UL,    // 原始值 * 1000
	eSam_NR_Modulation_QPSK_Ratio_UL,     // 原始值 * 1000
	eSam_NR_Modulation_BPSK_Ratio_UL,     // 原始值 * 1000

	eSam_NR_PDSCH_BLER,            // 原始值 * 1000
	eSam_NR_PDSCH_Initial_BLER,    // 原始值 * 1000
	eSam_NR_PUSCH_BLER,            // 原始值 * 1000
	eSam_NR_PUSCH_Initial_BLER,    // 原始值 * 1000

	eSam_NR_PUCCH_Path_Loss, // 原始值
	eSam_NR_PUSCH_Path_Loss, // 原始值
	eSam_NR_CQI, // 原始值 * 1000
	eSam_NR_MCS_UL_Info, // 原始值 * 1000
	eSam_NR_MCS_DL_Info, // 原始值 * 1000
	eSam_NR_PDCCH_DL_GrantCount, // 原始值
	eSam_NR_PDCCH_UL_GrantCount, // 原始值

	eSam_NR_Modulation_QPSK_Ratio_DL_TB0_s, // 原始值 * 1000
	eSam_NR_Modulation_16QAM_Ratio_DL_TB0_s, // 原始值 * 1000
	eSam_NR_Modulation_64QAM_Ratio_DL_TB0_s, // 原始值 * 1000
	eSam_NR_Modulation_256QAM_Ratio_DL_TB0_s, // 原始值 * 1000

	eSam_NR_Modulation_QPSK_Ratio_DL_TB1_s, // 原始值 * 1000
	eSam_NR_Modulation_16QAM_Ratio_DL_TB1_s, // 原始值 * 1000
	eSam_NR_Modulation_64QAM_Ratio_DL_TB1_s, // 原始值 * 1000
	eSam_NR_Modulation_256QAM_Ratio_DL_TB1_s, // 原始值 * 1000

	eSam_NR_Throughput_DC_PDCP_DL,    // 原始值
	eSam_NR_Throughput_DC_RLC_DL,     // 原始值
	eSam_NR_Throughput_DC_MAC_DL,     // 原始值
	eSam_NR_Throughput_DC_PHY_DL,     // 原始值
	eSam_NR_Throughput_DC_PDCP_UL,    // 原始值
	eSam_NR_Throughput_DC_RLC_UL,     // 原始值
	eSam_NR_Throughput_DC_MAC_UL,     // 原始值	
	eSam_NR_Throughput_DC_PHY_UL,     // 原始值

	eSam_NR_Bandwidth_MHz, // 原始值

	eSam_NR_PDSCH_Residual_BLER,  // 原始值 * 1000
	eSam_NR_PUSCH_Residual_BLER,  // 原始值 * 1000

	eSam_NR_Modulation_TYPE_DL, // 中兴文件使用标识
	eSam_NR_Modulation_TYPE_UL, // 中兴文件使用标识

	//r b
	eSam_NR_PRB_Num_DL_s,
	eSam_NR_PRB_Num_UL_s,

	eSam_NR_Rank_Indicator, // 原始值

	eSam_NR_PRB_Num_DL_Slot, // 原始值 * 1000
	eSam_NR_PRB_Num_UL_Slot, // 原始值 * 1000
	eSam_NR_NetDeployType, //原始值 （小区部署类型：1.独立组网 2.非独立组网）
	eSam_NR_BAND, //原始值 （band：41:2.6G   28:700M）

	eSam_NR_RTP_MediaFormat, // 原始值  语音编码类型
	eSam_NR_RTP_Payload,     // 原始值  有效负载
	eSam_NR_RTP_SamplingRate,// 原始值  采样率
	eSam_ChipType,           // 芯片类型，（enum eMSCHIPTYPE）
	eSam_MsType,             // 手机型号，（enum eMSTYPE）
	eSam_NetType,            // 手机网络制式

	// rank
	eSam_NR_Rank_Most_DL,
	eSam_NR_Rank_Most_UL,
	eSam_NR_Rank_Avg_DL,
	eSam_NR_Rank_Avg_UL,
	
	// TA
	eSam_NR_TA,
};

// ims sip 信令枚举
enum IMS_SIP_MSGID
{
	eMsg_IMS_SIP_INVITE = 0x42060000,
	eMsg_IMS_SIP_INVITE_Trying = 0x42064064,
	eMsg_IMS_SIP_INVITE_Session_Progress = 0x420640B7,
	eMsg_IMS_SIP_INVITE_Ringing = 0x420640B4,
	eMsg_IMS_SIP_INVITE_OK = 0x420640C8,
	eMsg_IMS_SIP_INVITE_Server_Internal_Error = 0x420641F4,
	eMsg_IMS_SIP_INVITE_Forbidden = 0x42064193,

	eMsg_IMS_SIP_PRACK = 0x420A0000,
	eMsg_IMS_SIP_PRACK_OK = 0x420A40C8,

	eMsg_IMS_SIP_UPDATE = 0x42100000,
	eMsg_IMS_SIP_UPDATE_OK = 0x421040C8,

	eMsg_IMS_SIP_ACK = 0x42010000,

	eMsg_IMS_SIP_BYE = 0x42020000,
	eMsg_IMS_SIP_BYE_OK = 0x420240C8,

	eMsg_IMS_SIP_CANCEL = 0x42030000,
	eMsg_IMS_SIP_CANCEL_OK = 0x420340C8,
	eMsg_IMS_SIP_DO = 0x42040000,
	eMsg_IMS_SIP_INFO = 0x42050000,
	eMsg_IMS_SIP_MESSAGE = 0x42070000,
	eMsg_IMS_SIP_NOTIFY = 0x42080000,
	eMsg_IMS_SIP_OPTIONS = 0x42090000,
	eMsg_IMS_SIP_QAUTH = 0x420B0000,
	eMsg_IMS_SIP_REFER = 0x420C0000,
	eMsg_IMS_SIP_REGISTER = 0x420D0000,
	eMsg_IMS_SIP_REGISTER_OK = 0x420D40C8,
	eMsg_IMS_SIP_REGISTER_Unauthorized = 0x420D4191,
	eMsg_IMS_SIP_SPRACK = 0x420E0000,
	eMsg_IMS_SIP_SUBSCRIBE = 0x420F0000,
	eMsg_IMS_SIP_PUBLISH = 0x42110000,
};

enum IMS_SIP_MediaFormat
{
	eMF_NONE,
	eMF_AMR_NB,
	eMF_AMR_WB,
	eMF_EVS_NB,
	eMF_EVS_WB,
	eMF_EVS_SWB,
	eMF_EVS_FB,
};

/*
 *文件信息行
 *格式：FileInfo <DtlogVersion> <Vendor> <Version> <NetMode> <Time> <Device> <Type><Test plan version>

 *示例：FileInfo 0.1 KnowYou 6 LTE 201205230910121 MBOX DT 125
*/
enum eATUFileInfoLine
{
	eATUFileInfoLine_FileInfo,
	eATUFileInfoLine_DtlogVersion,
	eATUFileInfoLine_Vendor,
	eATUFileInfoLine_Version,
	eATUFileInfoLine_NetMode,
	eATUFileInfoLine_Time,
	eATUFileInfoLine_Device,
	eATUFileInfoLine_Type,
	eATUFileInfoLine_Test_plan_version,

	eATUFileInfoLine_None
};
#define ISTDLTENET (((m_pWorkBox->m_tFileInfo.tPath.nettype) == (NET_LTE_TDD)) || ((m_pWorkBox->m_tFileInfo.tPath.nettype) == (NET_LTE_FDD)))
#define GET_TOTALSIZE_TYPE ((ISTDLTENET) ? eSam_APP_TotalSize_64bits : eSam_APP_TotalSize)
/**============ #define ASSIGN_APPTOTALSIZE(iValue) (ISTDLTENET) ? ((m_pWorkBox->m_pUnion->tAppData.APP_TotalSize_64bits) = \
                                                    (iValue)) : ((m_pWorkBox->m_pUnion->tAppData.APP_TotalSize) = ((int)iValue))
==========**/
//解码消息和采样点类型
#define DECODE_NEEDPROCESSED	0x14    //需要进一步处理成以下状态
#define DECODE_UNKNOW_VERSION   -1
#define DECODE_UNPROCESSED		0x13    //不处理 (return -1; 也是不处理)
#define DECODE_MSG				0x03    //层三信令(包括事件:Call,CSFB,MOS,FTP,Ping,HTTP,IOT,CA...)
#define DECODE_SAMPLE			0x23    //回放采样点
#define DECODE_SAMPLE3			0x33    //统计采样点
#define DECODE_SAMPLE_SAMPLE3	0x53    //回放和统计采样点
#define DECODE_MSG_SAMPLE		0x63    //层三信令和回放采样点
#define DECODE_MSG_SAMPLE3		0x83    //层三信令和统计采样点
#define DECODE_ALL				0x73    //层三信令、回放采样点、统计采样点

#define MIN_SAMPLE_TIMESPAN	350  //正常的写回放采样点的时间间隔
#define MAX_SAMPLE_TIMESPAN	10000 //最大的回放采样点的时间间隔
#define HISILICONV2_SAMPLE_TIMESPAN	1000 //海思V2芯片的写采样点时间间隔为1秒

#endif
