#include "ConfigSetting.h"
#include "StdCommond.h"
#include "linux_api.h"
#include "./ColumnDef.h"

#include "./SockTestDataMngDeal.h"
#include "./SockConfigMngDeal.h"
// #include "./SockDBMngDeal.h"
// #include "./SockSearchDeal.h"
// #include "./SockStatisticDeal.h"
// #include "./SockUserMngDeal.h"
// #include "./SockCommMngDeal.h"
#include "./SockDIYDeal.h"
// #include "./DataItem.h"

// #include "./ResultDef.h"
#include "./DataBaseInfo.h"
#include "./DBColumnDef.h"

// #include "../../Exceller/excelreader.h"

#include "./DtdrvApp_Public_Var.h"
#include "./SqlMaker.h"

#define DTDRVAPP_PORT 23456
#define FALSERETURN(X) if(!(X)) \
{\
	return FALSE;\
}

CConfigSetting* CConfigSetting::Instance (void)
{
  static CConfigSetting *m_pConfigSetting = nullptr;

  if (m_pConfigSetting == nullptr)
	{
		m_pConfigSetting = new CConfigSetting;
	}

  return m_pConfigSetting;
}

CConfigSetting::CConfigSetting()
:m_bIsValid_(0),
m_bClientLogger(0),
m_bIsIPv6_Enable(0)
{
	memset(m_pchCQTImagePath_,0,sizeof(m_pchCQTImagePath_));
	memset(m_pchXLSXPath_,0,sizeof(m_pchXLSXPath_));
	memset(m_pchMCCPath_, 0, sizeof(m_pchMCCPath_));

	m_str_matchinfo_.assign("");

	memset(szPath, 0, sizeof(szPath));
	CStdCommond::GetProcessPath(szPath);
		
}

CConfigSetting::~CConfigSetting()
{
   delete CColumnDef::Instance();
   delete CDataBaseInfo::Instance();
   delete CDBColumnDef::Instance();

   //接口池要到程序关闭释放的时候，进行释放。
   delete CSockTestDataMngDeal::Instance();
  //  delete CSockSearchDeal::Instance();
   delete CSockDIYDeal::Instance();
   delete CSockConfigMngDeal::Instance();
  //  delete CSockDBMngDeal::Instance();
  //  delete CSockStatisticDeal::Instance();
  //  delete CSockCommMngDeal::Instance();
  //  delete CSockUserMngDeal::Instance();
   delete CSqlMaker_MSSQL::Instance();
   //释放结构单类
   delete CDataItemManager::Instance();
}

BOOL CConfigSetting::ReadConfig()
{
	
    //读取系统信息
	FALSERETURN(ReadSystemConfig());

	//my test place
	/************************************************************************/
	/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
	/************************************************************************/


	//读取数据库信息
	FALSERETURN(ReadDataBaseConfig());

	//读取本地文档配置
	FALSERETURN(ReadLocalConfig());

	return TRUE;
}

BOOL CConfigSetting::ReadSystemConfig()
{ 
	char szClientVersionPath[MAX_PATH] = "";

	char szPathInI[MAX_PATH] = "";

	strcat(szPathInI,szPath);
	strcat(szPathInI,"ini/DtDrvApp.ini");
	CStdProfile profile(szPathInI);

	//获取端口配置
	FALSERETURN(profile.GetProIntValue("COMM", "DataPort", m_iDataport_, "23456"));
	FALSERETURN(profile.GetProIntValue("COMM", "ClientLogger", m_bClientLogger, "0"));

	//是否启用IPv6协议
	FALSERETURN(profile.GetProIntValue("COMM", "IsIPv6_Enable", m_bIsIPv6_Enable, "0"));

	//生成系统路径
	FALSERETURN(profile.GetProStrValue("COMM","clientpath",szClientVersionPath,"e:\\temp\\"));

	FALSERETURN(profile.GetProStrValue("COMM","CqtImageTemper",m_pchCQTImagePath_,szPath));
	CStdCommond::CreateDirec(m_pchCQTImagePath_);

	FALSERETURN(profile.GetProStrValue("COMM","XLSXTemper",m_pchXLSXPath_,szPath));
	CStdCommond::CreateDirec(m_pchXLSXPath_);

	FALSERETURN(profile.GetProStrValue("COMM", "MccFilePath", m_pchMCCPath_, szPath));

	m_bIsValid_ = 1;

	FALSERETURN(profile.GetProIntValue("COMM", "forciblyUpdate", m_forciblyUpdate, "0"));

	//读取数据库设置
	FALSERETURN(profile.GetProStrValue("DBSETTING","dbserver",m_stru_DBSetting_.pchDBServer,""));
	FALSERETURN(profile.GetProStrValue("DBSETTING","dbname",m_stru_DBSetting_.pchDBName,""));
	FALSERETURN(profile.GetProStrValue("DBSETTING","dbuser",m_stru_DBSetting_.pchUserName,""));
	FALSERETURN(profile.GetProStrValue("DBSETTING","dbpassword",m_stru_DBSetting_.pchPassword,""));
	FALSERETURN(profile.GetProIntValue("DBSETTING","dbport",m_stru_DBSetting_.nPort,""));
	FALSERETURN(profile.GetProIntValue("DBSETTING","displaysql",m_stru_DBSetting_.nDisplaySql,""));

	if (!EncryptDatabaseInfo(&profile))
	{
		return false;
	}

	SetMainDbConnPara();

	//读取文件下载地址配置
	FALSERETURN(profile.GetProIntValue("FILEDOWNLOADPATH","filepathgroup",m_FileDownLoadPathGroup, "0"));

	for (int i=1; i<=m_FileDownLoadPathGroup; ++i)
	{
		char tm_localPathIndex[512];
		std::string localPathName = "fileremotepathindex" + CStdCommond::itostr(i);
    	FALSERETURN(profile.GetProStrValue("FILEDOWNLOADPATH", localPathName.c_str(), tm_localPathIndex,""));

		char tm_remotePath[512];
		std::string remotePathName = "fileremotepath" + CStdCommond::itostr(i); 	
		FALSERETURN(profile.GetProStrValue("FILEDOWNLOADPATH", remotePathName.c_str(), tm_remotePath,""));

		STRU_FILEDownLoadPath fileDownLoadPath;

		std::string remotePath(tm_remotePath);
		CStrutils::ToUpperString(remotePath);
		std::string localPath(tm_localPathIndex);
		CStrutils::ToUpperString(localPath);
    	fileDownLoadPath.localPath = localPath;
		fileDownLoadPath.remotePath = remotePath;
    	m_vec_FileDownLoadPath.push_back(fileDownLoadPath);
	}

	//读取日志设置信息
	if (!ReadLogSetting(&profile)){
		return FALSE;
	}
	//读取文件下载地址配置
	//读取mc文件时间
	FALSERETURN(ReadMCFileTime());

	return TRUE;
}

BOOL CConfigSetting::ReadLogSetting(void* pProfile)
{
	CStdProfile* profile = (CStdProfile*)pProfile;
	char tm_strhead[50];
	PPM_OS::snprintf(tm_strhead,sizeof(tm_strhead),"%s","LOGLEVEL");
	FALSERETURN(profile->GetProIntValue(tm_strhead, "INFO", m_stru_logsetting_.bINFO, "1"));
	if(m_stru_logsetting_.bINFO > 0){
		m_stru_logsetting_.bINFO = LM_INFO;
	}
	FALSERETURN(profile->GetProIntValue(tm_strhead, "DEBUG", m_stru_logsetting_.bDEBUG, "1"));
	if(m_stru_logsetting_.bDEBUG > 0){
		m_stru_logsetting_.bDEBUG = LM_DEBUG;
	}
	FALSERETURN(profile->GetProIntValue(tm_strhead, "ERROR", m_stru_logsetting_.bERROR, "1"));
	if(m_stru_logsetting_.bERROR > 0){
		m_stru_logsetting_.bERROR = LM_ERROR;
	}
	FALSERETURN(profile->GetProIntValue(tm_strhead, "WARNING", m_stru_logsetting_.bWARNING, "1"));
	if(m_stru_logsetting_.bWARNING > 0){
		m_stru_logsetting_.bWARNING = LM_WARNING;
	}
	FALSERETURN(profile->GetProIntValue(tm_strhead, "NOTICE", m_stru_logsetting_.bNOTICE, "0"));
	if(m_stru_logsetting_.bNOTICE > 0){
		m_stru_logsetting_.bNOTICE = LM_NOTICE;
	}
	PPM_OS::snprintf(tm_strhead,sizeof(tm_strhead),"%s","LOGOUTPUT");
	FALSERETURN(profile->GetProIntValue(tm_strhead, "STDERR", m_stru_logsetting_.bSTDERR, "1"));
	if(m_stru_logsetting_.bSTDERR > 0){
		m_stru_logsetting_.bSTDERR = PPM_Log_Msg::STDERR;
	}
	FALSERETURN(profile->GetProIntValue(tm_strhead, "OSTREAM", m_stru_logsetting_.bOSTREAM, "1"));
	if(m_stru_logsetting_.bOSTREAM > 0){
		m_stru_logsetting_.bOSTREAM = PPM_Log_Msg::OSTREAM;
	}

#ifdef WIN32
	FALSERETURN(profile->GetProIntValue(tm_strhead, "CALLBACK", m_stru_logsetting_.bCALLBACK, "1"));
#else
	FALSERETURN(profile->GetProIntValue(tm_strhead, "CALLBACK", m_stru_logsetting_.bCALLBACK, "0"));
#endif

	if(m_stru_logsetting_.bCALLBACK > 0){
		m_stru_logsetting_.bCALLBACK = PPM_Log_Msg::MSG_CALLBACK;
	}
	return TRUE;
}

BOOL CConfigSetting::EncryptDatabaseInfo(void* pProfile)
{
	if (!EncryptDBserver(pProfile, m_stru_DBSetting_.pchDBServer))
	{
		return FALSE;
	}

	if (!EncryptDBname(pProfile, m_stru_DBSetting_.pchDBName))
	{
		return FALSE;
	}

	if (!EncryptDBpassword(pProfile, m_stru_DBSetting_.pchPassword))
	{
		return FALSE;
	}

	if (!EncryptDBuser(pProfile, m_stru_DBSetting_.pchUserName))
	{
		return FALSE;
	}

	return true;
}

BOOL CConfigSetting::EncryptDBserver(void* pProfile, char* DBServer)
{
	CStdProfile* profile = (CStdProfile*)pProfile;

	std::string strTemp(DBServer);
	if (!CUtility::DesDecrypt(DBServer))
	{
		OUTPUT_ERR(CUtility::OutPutInfo("配置文件数据库地址(%s)未加密，程序不予以运行", strTemp.c_str()).c_str());
		return FALSE;
	}
	// 如果密码还没加密，则加密修改配置
	if (strTemp.compare(DBServer) == 0)
	{
		strTemp = CUtility::DesEncrypt((char*)strTemp.c_str());
		profile->SetProStrValue("DBSETTING", "dbserver", strTemp.c_str());
	}

	return TRUE;
}

BOOL CConfigSetting::EncryptDBname(void* pProfile, char* DBname)
{
	CStdProfile* profile = (CStdProfile*)pProfile;

	std::string strTemp(DBname);
	if (!CUtility::DesDecrypt(DBname))
	{
		OUTPUT_ERR(CUtility::OutPutInfo("配置文件数据库名字(%s)未加密，程序不予以运行", strTemp.c_str()).c_str());
		return FALSE;
	}
	// 如果密码还没加密，则加密修改配置
	if (strTemp.compare(DBname) == 0)
	{
		strTemp = CUtility::DesEncrypt((char*)strTemp.c_str());
		profile->SetProStrValue("DBSETTING", "dbname", strTemp.c_str());
	}

	return TRUE;
}

BOOL CConfigSetting::EncryptDBpassword(void* pProfile, char* DBpassword)
{
	CStdProfile* profile = (CStdProfile*)pProfile;

	std::string strTemp(DBpassword);
	if (!CUtility::DesDecrypt(DBpassword))
	{
		OUTPUT_ERR(CUtility::OutPutInfo("配置文件数据库密码字(%s)未加密，程序不予以运行\n", strTemp.c_str()).c_str());
		return FALSE;
	}
	// 如果密码还没加密，则加密修改配置
	if (strTemp.compare(DBpassword) == 0)
	{
		strTemp = CUtility::DesEncrypt((char*)strTemp.c_str());
		profile->SetProStrValue("DBSETTING", "dbpassword", strTemp.c_str());
	}

	return TRUE;
}

BOOL CConfigSetting::EncryptDBuser(void* pProfile, char* DBuser)
{
	CStdProfile* profile = (CStdProfile*)pProfile;

	std::string strTemp(DBuser);
	if (!CUtility::DesDecrypt(DBuser))
	{
		OUTPUT_ERR(CUtility::OutPutInfo("配置文件数据库用户名(%s)未加密，程序不予以运行", strTemp.c_str()).c_str());
		return FALSE;
	}
	// 如果密码还没加密，则加密修改配置
	if (strTemp.compare(DBuser) == 0)
	{
		strTemp = CUtility::DesEncrypt((char*)strTemp.c_str());
		profile->SetProStrValue("DBSETTING", "dbuser", strTemp.c_str());
	}

	return TRUE;
}

void CConfigSetting::SetMainDbConnPara(void)
{
	memcpy(m_stru_MainDBSetting_.pchDBServer, m_stru_DBSetting_.pchDBServer, sizeof(m_stru_DBSetting_.pchDBServer));
	memcpy(m_stru_MainDBSetting_.pchDBName, m_stru_DBSetting_.pchDBName, sizeof(m_stru_DBSetting_.pchDBName));
	memcpy(m_stru_MainDBSetting_.pchUserName, m_stru_DBSetting_.pchUserName, sizeof(m_stru_DBSetting_.pchUserName));
	memcpy(m_stru_MainDBSetting_.pchPassword, m_stru_DBSetting_.pchPassword, sizeof(m_stru_DBSetting_.pchPassword));
	m_stru_MainDBSetting_.nPort = m_stru_DBSetting_.nPort;
	m_stru_MainDBSetting_.nDisplaySql = m_stru_DBSetting_.nDisplaySql;
}

BOOL CConfigSetting::ReadMCFileTime()
{
	m_vec_mcTimePath.clear();
	std::string iamgeDef_Path = "";

	std::vector<std::string> tableColumnVec;
	GetTableColumnVec(tableColumnVec, true);

	std::string fZipPath = CUtility::S_Printf("%s/temp",szPath);
	fZipPath = CStrutils::ReplaceStr(fZipPath, "//", "/");

	if (Clinux_api::mkdir_p(fZipPath) != 0)
	{
		return FALSE;
	}

	for (std::vector<std::string>::size_type i = 0; i<tableColumnVec.size(); ++i)
	{
		iamgeDef_Path = CUtility::S_Printf("%s/config/%s",szPath, tableColumnVec[i].c_str());
		iamgeDef_Path = CStrutils::ReplaceStr(iamgeDef_Path, "//", "/");

		// 获取文件信息
		struct stat file_info;
    if (stat(iamgeDef_Path.c_str(), &file_info) != 0) {
        perror("stat");
        return FALSE;
    }
		
		STRU_MCFILETimePath mcFileTimePath;
		memcpy(mcFileTimePath.cFilePath, iamgeDef_Path.c_str(), sizeof(mcFileTimePath.cFilePath));
		memcpy(mcFileTimePath.cFileName, tableColumnVec[i].c_str(), sizeof(mcFileTimePath.cFileName));
		// 获取最后修改时间
		memcpy(mcFileTimePath.cTime, CUtility::GetStrTime(file_info.st_mtime, 0).c_str(), sizeof(mcFileTimePath.cTime));
		mcFileTimePath.bSend = FALSE;
		m_vec_mcTimePath.push_back(mcFileTimePath);

		if (1 != CUtility::ZipFile(iamgeDef_Path.c_str(), (char*)fZipPath.c_str()))
		{
			OUTPUT_ERR("压缩 MC 文件失败！");
		}
	}
	return TRUE;
}

void CConfigSetting::GetTableColumnVec(std::vector<std::string>& tableColumnVec, BOOL bWantImage16)
{
  tableColumnVec.clear();
	if (bWantImage16)
	{
		tableColumnVec.push_back("Image16配置.mc");
	}
	tableColumnVec.push_back("表字段配置_CDMA.mc");
	tableColumnVec.push_back("表字段配置_EVDO.mc");
	tableColumnVec.push_back("表字段配置_GSM.mc");
	tableColumnVec.push_back("表字段配置_SCAN.mc");
	tableColumnVec.push_back("表字段配置_TDSCDMA.mc");
	tableColumnVec.push_back("表字段配置_LTE.mc");
	tableColumnVec.push_back("表字段配置_LTE_UEP.mc");
	tableColumnVec.push_back("表字段配置_LTE_FDD.mc");
	tableColumnVec.push_back("表字段配置_WCDMA.mc");
	tableColumnVec.push_back("表字段配置_WLAN.mc");
	tableColumnVec.push_back("表字段配置_其他.mc");
	tableColumnVec.push_back("表字段配置_SIGNAL.mc");
	tableColumnVec.push_back("表字段配置_NR.mc");
}

// BOOL
// CConfigSetting::CheckMCFileSend(char iamgeDef_Path[MAX_PATH])
// {
// 	BOOL bReturn = FALSE;
// 	for (vector<STRU_MCFILETimePath>::size_type i = 0; i < m_vec_mcTimePath.size(); ++i)
// 	{
// 		if (PPM_OS::memcmp(m_vec_mcTimePath[i].cFilePath, iamgeDef_Path, PPM_OS::strlen(m_vec_mcTimePath[i].cFilePath)) == 0)
// 		{
// 			bReturn = TRUE;
// 			WIN32_FIND_DATA ffd;
// 			HANDLE hFind = FindFirstFile(iamgeDef_Path, &ffd);
// 			FindClose(hFind);
// 			SYSTEMTIME systemTime;
// 			FileTimeToSystemTime(&(ffd.ftLastWriteTime), &systemTime);
// 			TIME_ZONE_INFORMATION chinaZone;
// 			GetTimeZoneInformation(&chinaZone);
// 			SystemTimeToTzSpecificLocalTime(&chinaZone, &systemTime, &systemTime);
// 			char cMCTime[MAX_PATH];
// 			PPM_OS::snprintf(cMCTime, sizeof(cMCTime), "%d-%d-%d %d:%d:%d", systemTime.wYear, systemTime.wMonth, systemTime.wDay, 
// 				systemTime.wHour, systemTime.wMinute, systemTime.wSecond);
// 			if (PPM_OS::memcmp(m_vec_mcTimePath[i].cTime, cMCTime, PPM_OS::strlen(m_vec_mcTimePath[i].cTime)) != 0)
// 			{
// 				m_vec_mcTimePath[i].bSend = TRUE;
// 			}
// 			break;
// 		}
// 	}
// 	return bReturn;
// }

BOOL CConfigSetting::ReadDataBaseConfig()
{
	CDataBaseInfo::Instance()->SetDBConnInfo(m_stru_DBSetting_);

	FALSERETURN(CDataBaseInfo::Instance()->ReadData());	

	return TRUE;
}

BOOL
CConfigSetting::ReadLocalConfig()
{
  CStdFile imageDef_File;

	char lineBuffer[1024];
	std::string iamgeDef_Path = "";

	//读取IMAGE DEF 配置
	iamgeDef_Path = CUtility::S_Printf("%s/config/Image16配置.mc",szPath);
	iamgeDef_Path = CStrutils::ReplaceStr(iamgeDef_Path, "//","/");

	if(!imageDef_File.Open(iamgeDef_Path.c_str(), "rb"))
	{
		PPM_DEBUG((LM_ERROR, "open file faild...Image16配置.mc"));
		return FALSE;
	}
	imageDef_File.SeekToBegin();   
  memset(lineBuffer, 0, sizeof(lineBuffer));

	CorrectLineBuffer(&imageDef_File, lineBuffer, E_IMAGE_COLUMNITEM);
	imageDef_File.Close();

	//table column
	std::vector<std::string> tableColumnVec;
	GetTableColumnVec(tableColumnVec, false);

	for (std::vector<std::string>::size_type i = 0; i<tableColumnVec.size(); ++i)
	{
		iamgeDef_Path = CUtility::S_Printf("%s/config/%s",szPath, tableColumnVec[i].c_str());
		iamgeDef_Path = CStrutils::ReplaceStr(iamgeDef_Path, "//","/");
		if(!imageDef_File.Open(iamgeDef_Path.c_str(), "rb"))
		{
			PPM_DEBUG((LM_ERROR, ("open file faild..." + tableColumnVec[i]).c_str()));
			return FALSE;
		}
		imageDef_File.SeekToBegin();   
		memset(lineBuffer, 0, sizeof(lineBuffer));

		CorrectLineBuffer(&imageDef_File, lineBuffer, E_TABLE_COLUMNITEM);
		imageDef_File.Close();
	}

	//读取RESULT DEF 配置
	iamgeDef_Path = CUtility::S_Printf("%s/config/ResultDef.mc",szPath);
	iamgeDef_Path = CStrutils::ReplaceStr(iamgeDef_Path, "//","/");
	CHECLFALSE(imageDef_File.Open(iamgeDef_Path.c_str()));
	imageDef_File.SeekToBegin();   
	memset(lineBuffer, 0, sizeof(lineBuffer));
	while (imageDef_File.GetStr(lineBuffer, 1024 - 1) != NULL)
	{
		std::string temp = std::string(lineBuffer);
		//去掉换行符和结尾符
		if ((int)temp[temp.length()-1] == 10)
		{
			temp = temp.substr(0, temp.length()-1);
		}
		if ((int)temp[temp.length()-1] == 9)
		{
			temp = temp.substr(0, temp.length()-1);
		}

		CResultDef::Instance()->AddResultDef(temp);
	}
	imageDef_File.Close();
    
	return TRUE;
}

void CConfigSetting::CorrectLineBuffer(CStdFile* imageDef_File, char* lineBuffer, ColumnItemType citype)
{
	while (imageDef_File->GetStr(lineBuffer, 1024 - 1) != NULL)
	{
		std::string temp = std::string(lineBuffer);
		//去掉换行符和结尾符
		if ((int)temp[temp.length()-1] == 10)
		{
			temp = temp.substr(0, temp.length()-1);
		}
		if ((int)temp[temp.length()-1] == 9)
		{
			temp = temp.substr(0, temp.length()-1);
		}

		AddColumnItemMap(temp, citype);
	}
}

bool CConfigSetting::IsNullLine(std::string temp)
{
	bool IsNullRow = true;
	for (int i =0; i<(int)temp.length(); ++i)
	{
		if ((int)temp[i] != 9)
		{
			IsNullRow = false;
			break;
		}
	}

	return IsNullRow;
}

void CConfigSetting::AddColumnItemMap(std::string temp, ColumnItemType citype)
{
	CColumnItem* p_ColumnItem = NULL;
	if (citype == E_IMAGE_COLUMNITEM)
	{
		p_ColumnItem = CColumnItem::GetImageColumnItem(temp);
	}
	else if (citype == E_TABLE_COLUMNITEM)
	{
		p_ColumnItem = CColumnItem::GetTableColumnItem(temp);
	}
	else
	{
		//do nothing...
	}

	if (NULL == p_ColumnItem)
	{
		//判断是否是空行
		if (!IsNullLine(temp))
		{
			PPM_DEBUG((LM_ERROR, "TableColumn配置存在异常项，请检查！\n"));
			std::string outputstr = "TableColumn配置存在异常项，请检查！ ： "  + temp + "\n";
			PPM_APPLOG((LM_ERROR, outputstr.c_str()));
		}
	}
	else
	{
		CColumnDef::Instance()->PushItemIntoMap(p_ColumnItem);
	}
}

// string 
// CConfigSetting::GetFileName(string strFullPath,int itaskidflat)
// {
// 	string strmid;
// 	strmid = strFullPath;
// 	int ipos;
// 	if(0 != itaskidflat)
// 	{
// 		ipos = strmid.rfind(".");
// 		if(-1 != ipos){
// 			strmid.erase(ipos,strmid.size());	
// 		}
// 	}
// 	ipos = strmid.rfind("\\");
// 	if( (-1 == ipos) || (ipos >= (int)strmid.size()) )
// 	{
// 		ipos = strmid.rfind("/");
// 		if((ipos < 0) || (ipos >= (int)strmid.size()))
// 		{
// 			PPM_DEBUG((LM_ERROR,"get filename failed:%s\n",
// 				strmid.c_str()
// 				));
// 			strmid.assign("");
// 			return strmid;
// 		}
// 	}
// 	strmid.erase(0,ipos+1);
// 	return strmid;
// }

/************************************************************************
通过Logon Name 查找对应的密码信息
************************************************************************/
STRU_USERINFO 
CConfigSetting::FindAuthPassword(std::string strLogon)
{
	CStdAutoLock tm_autolock(&m_lock_auth_);
	auto itr = m_map_authpassword_.find(strLogon);
	if(itr != m_map_authpassword_.end()){
		return itr->second;
	}else{
		STRU_USERINFO tm_userinfo;
		return tm_userinfo;
	}

}

/************************************************************************
通过Logon Name 查找对应的密码信息
************************************************************************/
void  
CConfigSetting::SetAuthInfo(std::string strLogon,std::string strPassword,int dbid)
{
	CStdAutoLock tm_autolock(&m_lock_auth_);
	STRU_USERINFO tm_dbinfo;
	tm_dbinfo.idbid = dbid;
	tm_dbinfo.strusername = strLogon;
	tm_dbinfo.strpassword = strPassword;
	auto itr = m_map_authpassword_.find(strLogon);
	if(itr != m_map_authpassword_.end()){
		itr->second = tm_dbinfo;
	}else{
		m_map_authpassword_.insert(std::pair<std::string,STRU_USERINFO>(strLogon,tm_dbinfo));
	}
}

// /************************************************************************
// 获取文件的CRC校验码
// ************************************************************************/
// unsigned long 
// CConfigSetting::GetCRCNumber(const char* filename)
// {
// 	//先读取文件
// 	CStdFile tm_file;
// 	if(!tm_file.Open(filename,"rb")){
// 		tm_file.Close();
// 		PPM_OS::sleep(100);
// 		if(!tm_file.Open(filename,"rb")){
// 			PPM_DEBUG((LM_ERROR,"cannot openfile:%s ,%m\n",filename));
// 			return 0;
// 		}
// 	}
// 	unsigned long oldcrc32; 
// 	unsigned long crc32; 
// 	unsigned long oldcrc; 
// 	unsigned int charcnt; 
// 	BYTE c,t; 
// 	oldcrc32 = 0x00000000; //初值为0 
// 	charcnt=0; 
// 	BYTE tm_buf[100];
// 	int tm_ireadlen;
// 	while(!tm_file.IsEof()){
// 		PPM_OS::memset(tm_buf,0,sizeof(tm_buf));
// 		tm_ireadlen = tm_file.Read(tm_buf,100);
// 		charcnt = 0;
// 		while(tm_ireadlen--){
// 			t= BYTE((oldcrc32 >> 24) & 0xFF); //要移出的字节的值 
// 			oldcrc=crc32_table[t]; //根据移出的字节的值查表 
// 			c=tm_buf[charcnt]; //新移进来的字节值 
// 			oldcrc32= (oldcrc32 << 8) | c; //将新移进来的字节值添在寄存器末字节中 
// 			oldcrc32=oldcrc32^oldcrc; //将寄存器与查出的值进行xor运算 
// 			charcnt++; 
// 		}
// 	}
// 	tm_file.Close();
// 	crc32=oldcrc32; 
// 	return crc32; 
// }

BOOL CConfigSetting::RowResultDeal(int nQType)
{
	switch(nQType)
	{
	case TYPE_VEC_CONFIGTYPE_VERSION:
		{
			m_iVersion_ = *(int*)(m_vecColumn[0].pColValue);
		}
		break;

	case TYPE_VEC_CONFIGTYPE_UPDATE_DESC:
		{
			STRU_UPDATE_DESC tm;
			
			tm.version = *(int*)(m_vecColumn[0].pColValue);
			tm.time = *(int*)(m_vecColumn[1].pColValue);
			tm.desc = std::string((char*)(m_vecColumn[2].pColValue));
			
			m_vec_pUpdateDesc_.push_back(tm);
		}
		break;

	case TYPE_VEC_CONFIGTYPE_ROLE:
		{
			STRU_ROLE tm;
			
			tm.role_id = *(int*)(m_vecColumn[0].pColValue);
			tm.role_name = std::string((char*)(m_vecColumn[1].pColValue));
			tm.role_desc = std::string((char*)(m_vecColumn[2].pColValue));	
			m_vec_pRole_.push_back(tm);
		}
		break;

	case TYPE_VEC_CONFIGTYPE_ROLE_FUNC:
		{
			STRU_ROLE_FUNC tm;
			
			tm.role_id = *(int*)(m_vecColumn[0].pColValue);
			tm.subfunc_id = *(int*)(m_vecColumn[1].pColValue);
			m_vec_pRoleFunc_.push_back(tm);
		}
		break;

	case TYPE_VEC_CONFIGTYPE_USER_CITY:
		{
			m_vecUserCity.push_back(*(int*)(m_vecColumn[0].pColValue));
		}
        break;
	default:
		{

		}
		break;
	}
	return TRUE;
}


BOOL CConfigSetting::ReadVersion()
{
    CStdAutoLock tm_autoLock(&m_lock_);

	//读取db定义
#ifdef Cell_EXT
	string lo_strsql1 = "select isnull(iversion,-111) from tb_cfg_static_version_ext";
#else
	#ifdef USE_MYSQL
	std::string lo_strsql1 = "select ifnull(iversion,-111) from tb_cfg_static_version";
	#else
	std::string lo_strsql1 = "select isnull(iversion,-111) from tb_cfg_static_version";
	#endif
#endif
	Clear();
	
	m_pDataBaseDeal_ = new CDataBaseDeal();

	m_pDataBaseDeal_->SetDbConnPara(m_stru_DBSetting_);

	if(m_pDataBaseDeal_->SearchSql(lo_strsql1.c_str(),this, TYPE_VEC_CONFIGTYPE_VERSION) > 0)
	{
		delete m_pDataBaseDeal_;
		m_pDataBaseDeal_ = NULL;	
		Clear();

		return TRUE;
	}
	else
	{
		delete m_pDataBaseDeal_;
		m_pDataBaseDeal_ = NULL;
		Clear();

		return FALSE;
	}
} 

BOOL CConfigSetting::ReadUserCity(const BYTE* const pData, const int nCount,int& ioffset)
{
    CStdAutoLock tm_autoLock(&m_lock_);

    m_vecUserCity.clear();

	char  tm_username[255];
	PPM_OS::memset(tm_username,0,sizeof(tm_username));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_username,sizeof(tm_username)));

	std::string unix_username = CUtility::GBKToUtf8(tm_username);

	//读取db定义
	std::string lo_strsql1 = " select b.city_id from tb_cfg_static_user a, tb_cfg_static_user_city_id b where a.logon_code = '" +
													unix_username + "' and a.iid = b.user_id ";

	Clear();

	m_pDataBaseDeal_ = new CDataBaseDeal();

	m_pDataBaseDeal_->SetDbConnPara(m_stru_DBSetting_);

	if(m_pDataBaseDeal_->SearchSql(lo_strsql1.c_str(), this, TYPE_VEC_CONFIGTYPE_USER_CITY) > 0)
	{
		delete m_pDataBaseDeal_;
		m_pDataBaseDeal_ = NULL;	
		Clear();

		return TRUE;
	}
	else
	{
		delete m_pDataBaseDeal_;
		m_pDataBaseDeal_ = NULL;
		Clear();

		return FALSE;
	}
} 

BOOL CConfigSetting::ReadUpdateDesc(int version)
{
  CStdAutoLock tm_autoLock(&m_lock_);

	m_vec_pUpdateDesc_.clear();

	char tm_sql[512];
#ifdef Cell_EXT
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),	"select iversion,itime,strdesc from tb_cfg_update_desc_ext\
											where iversion > %d order by itime desc ",version);
#else
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),	"select iversion,itime,strdesc from tb_cfg_update_desc\
											where iversion > %d order by itime desc ",version);
#endif

	Clear();
	//版本号、时间、描述
	
	m_pDataBaseDeal_ = new CDataBaseDeal();
	
	m_pDataBaseDeal_->SetDbConnPara(m_stru_DBSetting_);

	if(m_pDataBaseDeal_->SearchSql(tm_sql,this,TYPE_VEC_CONFIGTYPE_UPDATE_DESC) > 0)
	{
		delete m_pDataBaseDeal_;
		m_pDataBaseDeal_ = NULL;
		Clear();
		
		return TRUE;
	}
	else
	{
		delete m_pDataBaseDeal_;
		m_pDataBaseDeal_ = NULL;
		Clear();
		
		return FALSE;
	}
}

BOOL CConfigSetting::ReadRole()
{
    CStdAutoLock tm_autoLock(&m_lock_);

	m_vec_pRole_.clear();
	
	char tm_sql[512];	
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),	"select iid,strname,strcomment from tb_cfg_static_role order by iid ");

	Clear();
	
	m_pDataBaseDeal_ = new CDataBaseDeal();
	
	m_pDataBaseDeal_->SetDbConnPara(m_stru_DBSetting_);
	
	if(m_pDataBaseDeal_->SearchSql(tm_sql,this,TYPE_VEC_CONFIGTYPE_ROLE) > 0)
	{
		delete m_pDataBaseDeal_;
		m_pDataBaseDeal_ = NULL;
		Clear();
		
		return TRUE;
	}
	else
	{
		delete m_pDataBaseDeal_;
		m_pDataBaseDeal_ = NULL;
		Clear();
		
		return FALSE;
	}
} 

BOOL CConfigSetting::ReadRoleFunc()
{
    CStdAutoLock tm_autoLock(&m_lock_);

	m_vec_pRoleFunc_.clear();
	
	char tm_sql[512];	
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),	"select role_id,subfunc_id from tb_cfg_static_role_func order by role_id, subfunc_id ");

	Clear();
	
	m_pDataBaseDeal_ = new CDataBaseDeal();
	
	m_pDataBaseDeal_->SetDbConnPara(m_stru_DBSetting_);
	
	if(m_pDataBaseDeal_->SearchSql(tm_sql,this,TYPE_VEC_CONFIGTYPE_ROLE_FUNC) > 0)
	{
		delete m_pDataBaseDeal_;
		m_pDataBaseDeal_ = NULL;
		Clear();
		
		return TRUE;
	}
	else
	{
		delete m_pDataBaseDeal_;
		m_pDataBaseDeal_ = NULL;
		Clear();
		
		return FALSE;
	}
} 



