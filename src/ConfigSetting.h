#ifndef _CONFIGSETTING_H_DTDRV_
#define _CONFIGSETTING_H_DTDRV_
 
#include "StdHeader.h"
#include "StdProfile.h"
// #include "../../comm/PPM_typedef.h"
// #include "../../comm/PPM_OS.h"
// #include "../../stdclass/StdLock.h"
#include "DataBaseDeal.h"
#include "PPM_CONN_Actor.h"

#include "ResultDef.h"

#include "StdTime.h"
#include "Strutils.h"

// #include <fstream>


#define TYPE_VEC_CONFIGTYPE_VERSION						0x01
#define TYPE_VEC_CONFIGTYPE_UPDATE_DESC					0x02
#define TYPE_VEC_CONFIGTYPE_ROLE						0x03
#define TYPE_VEC_CONFIGTYPE_ROLE_FUNC		     		0x04
#define TYPE_VEC_CONFIGTYPE_USER_CITY		     		0x05
#define EXCUTE_SQL_FILE_SIZE							0x200000

#define OUTPUTFILESIZEMAX								0x500000	//日志文件限定大小5M
/////////////////////////////////////////////////////////////////////////////////////////


// struct STRU_CFGFILE
// {
// 	int icfgtime;
// 	char pchfilename[255];
// };


struct STRU_LOGSetting
{
	int bINFO;
	int bDEBUG;
	int bERROR;
	int bWARNING;
	int bNOTICE;	
	//输出到命令行
	int bSTDERR;
	//输出到文件
	int bOSTREAM;
	//输出到界面
	int bCALLBACK;
	
};

struct STRU_USERINFO
{
	STRU_USERINFO(){
		idbid = 0;
		strusername = "";
		strpassword = "";
	}
	std::string strusername;
	std::string strpassword;
	int    idbid;
};

struct STRU_UPDATE_DESC
{
	STRU_UPDATE_DESC(){
		version = 0;
		time = 0;
		desc = "";
	}
	int version;
	int time;
	std::string desc;
};

struct STRU_ROLE
{
	STRU_ROLE(){
		role_id = 0;
		role_name = "";
		role_desc = "";
	}
	int role_id;
	std::string role_name;
	std::string role_desc;
};

struct STRU_ROLE_FUNC
{
	STRU_ROLE_FUNC(){
		role_id = 0;
		subfunc_id = 0;
	}
	int role_id;
	int subfunc_id;
};

struct STRU_SockInfo
{
	CDataBaseDeal* pDataBaseDeal_;
	PPM_Server_Deal_Base* pServerDeal_;
	BYTE* pSendBuf_;

	STRU_SockInfo(CDataBaseDeal* m_pDataBaseDeal_temp, PPM_Server_Deal_Base* m_pServerDeal_temp, BYTE* m_pSendBuf_temp)
	{
		pDataBaseDeal_ = m_pDataBaseDeal_temp;
		pServerDeal_ = m_pServerDeal_temp;
		pSendBuf_ = m_pSendBuf_temp;
	}

	STRU_SockInfo()
	{
       pDataBaseDeal_ = NULL;
       pServerDeal_ = NULL;
       pSendBuf_ = NULL;
	}
};

// struct STRU_DBSqlInfo
// {
// 	BOOL bNeedOutPutSql;
// 	char SqlPath[1024];

//    STRU_DBSqlInfo()
//    {
//       bNeedOutPutSql = FALSE;
// 	  PPM_OS::memset(SqlPath, 0, sizeof(SqlPath));
//    }
// };

struct STRU_FILEDownLoadPath
{
	std::string localPath;
	std::string remotePath;

  STRU_FILEDownLoadPath()
	{
       localPath = "";
       remotePath = "";
	}

  std::string GetFileRemotePath(std::string path)
	{
		std::string fileRemotePath = ""; 
		// char* _path = (char*)path.c_str();
		CStrutils::ToUpperString(path);

		// replace(path.begin(),path.end(),'/','\\');   
		if (path.find(localPath) != std::string::npos)
		{
      fileRemotePath = remotePath + path.substr(localPath.length());
		}
    return fileRemotePath;
	}
};

struct STRU_MCFILETimePath 
{
	char cTime[MAX_PATH];//最后修改时间
	char cFilePath[MAX_PATH];//所在路径
	char cFileName[MAX_PATH];//文件名
	bool bSend;//是否发送

	STRU_MCFILETimePath()
	{
		memset(cTime, 0, MAX_PATH);
		memset(cFilePath, 0, MAX_PATH);
		memset(cFileName, 0, MAX_PATH);
		bSend = FALSE;
	}
};

enum ColumnItemType
{
	E_TABLE_COLUMNITEM,
	E_IMAGE_COLUMNITEM,
	E_TYPE_MAX
};

class CConfigSetting : public CQueryRecord
{
public:
	CConfigSetting();
	~CConfigSetting();

	static CConfigSetting* Instance (void);

	virtual BOOL RowResultDeal(int nQType);
// 	int m_iResultType_;

	BOOL ReadConfig();

	BOOL ReadVersion();
	BOOL ReadUpdateDesc(int version);

	BOOL ReadRole();
	BOOL ReadRoleFunc();

	BOOL ReadUserCity(const BYTE* const pData, const int nCount,int& ioffset);

  BOOL ReadSystemConfig();
	BOOL ReadDataBaseConfig();

	BOOL ReadLocalConfig();

	BOOL ReadMCFileTime();
// 	BOOL CheckMCFileSend(char iamgeDef_Path[MAX_PATH]);
	
// 	string GetFileName(string strFullPath,int itaskidflat = 1);

	STRU_USERINFO FindAuthPassword(std::string strLogon);
	void SetAuthInfo(std::string strLogon,std::string strPassword,int dbid);

// 	unsigned long GetCRCNumber(const char* filename);

	BOOL EncryptDatabaseInfo(void* pProfile);
	BOOL EncryptDBserver(void* pProfile, char* DBServer);
	BOOL EncryptDBname(void* pProfile, char* DBname);
	BOOL EncryptDBpassword(void* pProfile, char* DBpassword);
	BOOL EncryptDBuser(void* pProfile, char* DBuser);
  BOOL ReadLogSetting(void* pProfile);
	void SetMainDbConnPara(void);


	char szPath[MAX_PATH];   //系统目录
	char m_pchClientVersion_[MAX_PATH]; //client版本号
	char m_pchCQTImagePath_[MAX_PATH]; //CQT图片根目录所在路径
	char m_pchXLSXPath_[MAX_PATH];    //XLSX根目录所在路径

	char m_pchMCCPath_[MAX_PATH];	//MCC文件根目录所在路径

	// 日志设置信息
	STRU_LOGSetting m_stru_logsetting_;

// 	vector<STRU_ParaResult*> *pm_vec_Result_;

	tDBConnInfo m_stru_DBSetting_; //数据库设置
	tDBConnInfo m_stru_MainDBSetting_;

	int m_FileDownLoadPathGroup;
	std::vector<STRU_FILEDownLoadPath> m_vec_FileDownLoadPath;   //文件下载路径配置

	std::vector<STRU_UPDATE_DESC> m_vec_pUpdateDesc_;
	std::vector<STRU_ROLE> m_vec_pRole_;
	std::vector<STRU_ROLE_FUNC> m_vec_pRoleFunc_;
	std::vector<STRU_MCFILETimePath> m_vec_mcTimePath;	//mc配置文件时间路径
	int m_iVersion_;

// 	unsigned long crc32_table[256]; 

	int m_forciblyUpdate;//强制更新开关   1：打开强制更新    0：关闭强制更新
	
	int m_bIsValid_; //服务器启动的时候需要鉴权
	std::string m_str_matchinfo_; //机器相关信息

	std::vector<int> m_vecUserCity;

	int m_iDataport_;
	int m_bClientLogger; //是否起连集中监控的客户端

	int m_iNeedReStart;
	
	int m_bIsIPv6_Enable;
private:
  bool IsNullLine(std::string temp);
  void AddColumnItemMap(std::string temp, ColumnItemType citype);
  void CorrectLineBuffer(CStdFile* imageDef_File, char* lineBuffer, ColumnItemType citype);
  void GetTableColumnVec(std::vector<std::string>& tableColumnVec, BOOL bWantImage16);

	std::map<std::string,STRU_USERINFO> m_map_authpassword_;
	CStdLock m_lock_;
	CStdLock m_lock_auth_;
};

#endif

