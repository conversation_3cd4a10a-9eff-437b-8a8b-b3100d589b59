#include "DataBaseInfo.h"
#include "ResultDef.h"

CDataBaseInfo::CDataBaseInfo(void)
{
	for(int i=0;i<256;i++)
	{
		pfunc_RowResultDeal[i] = &CDataBaseInfo::RowResultDeal_Default;
	}

	pfunc_RowResultDeal[CDataBaseInfo::TYPE_VEC_DBSETTING] = &CDataBaseInfo::RowResultDeal_DBSetting;
	pfunc_RowResultDeal[CDataBaseInfo::TYPE_VEC_PROJECTSETTING] = &CDataBaseInfo::RowResultDeal_ProjectSetting;
	pfunc_RowResultDeal[CDataBaseInfo::TYPE_VEC_RESULTDEFINE] = &CDataBaseInfo::RowResultDeal_ResultDef;
	pfunc_RowResultDeal[CDataBaseInfo::TYPE_INT_PWDINVALIDCFG] = &CDataBaseInfo::RowResultDeal_PWDInvalid;
	pfunc_RowResultDeal[CDataBaseInfo::TYPE_MAP_USERSTATECFG] = &CDataBaseInfo::RowResultDeal_UserState;
	pfunc_RowResultDeal[CDataBaseInfo::TYPE_NULL_CHECKCOLUMN] = &CDataBaseInfo::RowResultDeal_Default;

	staleDays = INT_INVALID;
}

CDataBaseInfo::~CDataBaseInfo(void)
{
	m_map_pDBConnInfo_.clear();

	delete CResultDef::Instance();
}

void 
CDataBaseInfo::SetDBConnInfo(const tDBConnInfo& dbConnInfo)
{
	memcpy(&m_DBConnInfo, &dbConnInfo, sizeof(tDBConnInfo));
}


CDataBaseInfo *
CDataBaseInfo::Instance (void)
{
	static CDataBaseInfo *m_pCDataBaseInfo = NULL;

	if (m_pCDataBaseInfo == 0)
	{
		m_pCDataBaseInfo = new CDataBaseInfo;
		if(NULL == m_pCDataBaseInfo){
			return NULL;
		}
	}
	return m_pCDataBaseInfo;
}

BOOL
CDataBaseInfo::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	return TRUE;
}

/************************************************************************
处理输出结果
************************************************************************/
BOOL 
CDataBaseInfo::RowResultDeal(int nQType)
{
	return (this->*pfunc_RowResultDeal[nQType%0x100])();
}

BOOL 
CDataBaseInfo::RowResultDeal_Default()
{
	return FALSE;
}

BOOL
CDataBaseInfo::ReadData()
{
  m_pDataBaseDeal_ = new CDataBaseDeal();
	m_pDataBaseDeal_->SetDbConnPara(m_DBConnInfo);

  if (ReadDBDefine() == FALSE 
	|| ReadProjectDefine() == FALSE)
  {
	  delete m_pDataBaseDeal_;
	  m_pDataBaseDeal_ = NULL;

	  return FALSE;
  }

	isEnableAccountControl = ReadAccountControl();

  delete m_pDataBaseDeal_;
  m_pDataBaseDeal_ = NULL;
  return TRUE;
}


//读取数据库查询结果返回串
BOOL 
CDataBaseInfo::ReadResultDefine()
{
	std::string strsql = "select resultid,fieldtype from tb_cfg_static_resultdef  order by fieldid asc ";

	if(NULL != m_pDataBaseDeal_)
	{
		int ret = m_pDataBaseDeal_->SearchSql( strsql.c_str(),this, CDataBaseInfo::TYPE_VEC_RESULTDEFINE);
		if (ret < 0)
		{
			Clear();
			return FALSE;
		}
	}

	Clear();
	return TRUE;
} 

BOOL 
CDataBaseInfo::ReadDBDefine()
{
	//读取db定义
	std::string lo_strsql1 = "select id,servername,dbname,logonname,password,dbport from tb_cfg_static_dbsetting";

	if(m_pDataBaseDeal_->SearchSql(lo_strsql1.c_str(),this, CDataBaseInfo::TYPE_VEC_DBSETTING) > 0)
	{
		Clear();
		return TRUE;
	}

	Clear();
	return FALSE;
} 


BOOL 
CDataBaseInfo::ReadProjectDefine()
{
	//读取db定义
	std::string lo_strsql1 = "select iid,itesttype from tb_cfg_static_project";

	if(m_pDataBaseDeal_->SearchSql(lo_strsql1.c_str(),this,CDataBaseInfo::TYPE_VEC_PROJECTSETTING) > 0)
	{
		Clear();
		return TRUE;
	}

	Clear();
	return FALSE;
}

BOOL 
CDataBaseInfo::RowResultDeal_DBSetting()
{
	tDBConnInfo tm_stru_dbinfo;
	PPM_OS::memset(&tm_stru_dbinfo,0,sizeof(tm_stru_dbinfo));
	int tm_dbid = *((int*)(m_vecColumn[0].pColValue));
	if(tm_dbid < 0){
		return FALSE;
	}

	PPM_OS::strncpy(tm_stru_dbinfo.pchDBServer, (char*)(m_vecColumn[1].pColValue), sizeof(tm_stru_dbinfo.pchDBServer) - 1);
	PPM_OS::strncpy(tm_stru_dbinfo.pchDBName,   (char*)(m_vecColumn[2].pColValue), sizeof(tm_stru_dbinfo.pchDBName) - 1);
	PPM_OS::strncpy(tm_stru_dbinfo.pchUserName, (char*)(m_vecColumn[3].pColValue), sizeof(tm_stru_dbinfo.pchUserName) - 1);
	PPM_OS::strncpy(tm_stru_dbinfo.pchPassword, (char*)(m_vecColumn[4].pColValue), sizeof(tm_stru_dbinfo.pchPassword) - 1);
	tm_stru_dbinfo.nPort = atoi((char*)(m_vecColumn[5].pColValue));
	if (0 == PPM_OS::strcmp(tm_stru_dbinfo.pchPassword, " "))
	{
		PPM_OS::strcpy(tm_stru_dbinfo.pchPassword, "");
	}

	m_map_pDBConnInfo_.insert(std::map<int, tDBConnInfo>::value_type(tm_dbid, tm_stru_dbinfo));

	return TRUE;
}

BOOL
CDataBaseInfo::RowResultDeal_ProjectSetting()
{
	//将项目配置放入内存中
	int tm_projectid = *(int*)(m_vecColumn[0].pColValue);
	if(tm_projectid >= MAX_PROJECTSN){
		return FALSE;
	}
	int tm_itesttype = *(int*)(m_vecColumn[1].pColValue);
	m_pi_projectsetting_[tm_projectid] = tm_itesttype;

	return TRUE;
}

BOOL
CDataBaseInfo::RowResultDeal_ResultDef()
{
	int nType = *(int*)(m_vecColumn[0].pColValue);
	int nDatatype = *(int*)(m_vecColumn[1].pColValue);

	if (nType > 0 && nDatatype > 0)
	{
		CResultDef::Instance()->AddResultDef(nType, nDatatype);
	}
	return TRUE;
}

BOOL 
CDataBaseInfo::ReadAccountControl()
{
	if( !ReadPWDInvalidCfg() )
	{
		return FALSE;
	}

	if( !ReadUserStateCfg() )
	{
		return FALSE;
	}
	/**
	std::string sql = "select top 1 iid,strname,logon_code,logon_pwd,phone,strcomment,icityid,\
        lastlogin_time,lastlogin_ip,modifypwd_time,iuserstate from tb_cfg_static_user";

	if(m_pDataBaseDeal_->SearchSql(sql.c_str(),this) > 0)
	{
		Clear();
		return TRUE;
	}
	
	Clear();
	*/
	return TRUE;
}

BOOL 
CDataBaseInfo::ReadPWDInvalidCfg()
{
	#ifdef USE_MYSQL
	std::string sql = "select staleDays,tryTimes,alarmDays from tb_cfg_static_user_constraint limit 1;";
	#else
	std::string sql = "select top 1 staleDays,tryTimes,alarmDays from tb_cfg_static_user_constraint";
	#endif

	if(m_pDataBaseDeal_->SearchSql(sql.c_str(),this, CDataBaseInfo::TYPE_INT_PWDINVALIDCFG) > 0)
	{
		Clear();
		return staleDays != INT_INVALID;
	}

	Clear();
	return FALSE;
}

BOOL 
CDataBaseInfo::ReadUserStateCfg()
{
	std::string sql = "select iid,strcomment,iResultType from tb_cfg_static_user_state";

	m_vec_UserState.clear();

	if(m_pDataBaseDeal_->SearchSql(sql.c_str(),this, CDataBaseInfo::TYPE_MAP_USERSTATECFG) > 0)
	{
		Clear();

		return m_vec_UserState.size() != 0;
	}

	Clear();
	return FALSE;
}

BOOL 
CDataBaseInfo::RowResultDeal_UserState()
{
	int iid = *(int*)(m_vecColumn[0].pColValue);
	std::string strcomment((char*)m_vecColumn[1].pColValue);
	int itype = *(int*)(m_vecColumn[2].pColValue);
	UserStatus status(iid, strcomment, itype);
	m_vec_UserState.push_back(status);

	return TRUE;
}

BOOL 
CDataBaseInfo::RowResultDeal_PWDInvalid()
{
	staleDays = *(int*)(m_vecColumn[0].pColValue);
	tryTimes = *(int*)(m_vecColumn[1].pColValue);
	alarmDays = *(int*)(m_vecColumn[2].pColValue);

	return TRUE;
}

UserStatus*
CDataBaseInfo::GetUserState(int iid)
{

	for(size_t i = 0; i < m_vec_UserState.size(); i++)
	{
		if (m_vec_UserState[i].iid == iid)
		{
			return &m_vec_UserState[i];
		}
	}

	return NULL;
}

BOOL
CDataBaseInfo::IsDbConnInfoExist(int iid)
{
	std::map<int, tDBConnInfo>::iterator it = m_map_pDBConnInfo_.find(iid);

	return it != m_map_pDBConnInfo_.end();
}

int 
CDataBaseInfo::GetPwdInvalidDays()
{
	return staleDays;
}
