#pragma once

#include "SearchSock.h"
#include "UserStatus.h"

#define MAX_DBNUM 50 //最大数据库个数
#define MAX_PROJECTSN 100 //最大项目类型序号
#define INT_INVALID -9999

/************************************************************************
本类主要用于存储服务器的基本业务信息。
将数据库经常访问的信息，存放在此类。
************************************************************************/

class CDataBaseInfo : public CSearchSock
{
protected:
	enum RowResultType
	{
		//search
		TYPE_VEC_DBSETTING = 0x01,  //数据库配置定义
		TYPE_VEC_PROJECTSETTING = 0x02, //项目测试类型
		TYPE_VEC_RESULTDEFINE = 0x03,
		TYPE_VEC_IMAGEDEF = 0x04,
		TYPE_INT_PWDINVALIDCFG = 0x05,
		TYPE_MAP_USERSTATECFG = 0x06,
		TYPE_NULL_CHECKCOLUMN = 0x07
	};

public:
	CDataBaseInfo(void);
public:
	~CDataBaseInfo(void);

	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);

	static CDataBaseInfo* Instance (void);

    void SetDBConnInfo(const tDBConnInfo& dbConnInfo);

	UserStatus* GetUserState(int iid);

	BOOL IsDbConnInfoExist(int iid);

	BOOL ReadData();

	int GetTestTypeByProjectID(int projid){
		if(projid >= MAX_PROJECTSN){
			return -1;
		}
		return m_pi_projectsetting_[projid];
	};

	int GetPwdInvalidDays();

public:

	tDBConnInfo m_stru_DBSetting_; //主数据库设置
	std::map<int, tDBConnInfo> m_map_pDBConnInfo_;  //用户数据库配置
	int m_pi_projectsetting_[MAX_PROJECTSN]; //项目类型
	BOOL isEnableAccountControl;
	int staleDays; //密码有效天数
	int tryTimes;  //密码输入尝试次数
	int alarmDays; //密码即将过期告警天数

private:
	std::vector<UserStatus> m_vec_UserState; //用户状态表
private:
	//数据处理函数集
	BOOL (CDataBaseInfo::*pfunc_RowResultDeal[256])();

	virtual BOOL RowResultDeal(int nQType);
	virtual BOOL RowResultDeal_Default();


	tDBConnInfo m_DBConnInfo;

	BOOL RowResultDeal_DBSetting();
	BOOL RowResultDeal_ProjectSetting();
	BOOL RowResultDeal_ResultDef();
	BOOL RowResultDeal_UserState();
	BOOL RowResultDeal_PWDInvalid();

	BOOL ReadResultDefine();

	BOOL ReadDBDefine();

	BOOL ReadProjectDefine();

	BOOL ReadAccountControl();
	BOOL ReadPWDInvalidCfg();
	BOOL ReadUserStateCfg();
};
