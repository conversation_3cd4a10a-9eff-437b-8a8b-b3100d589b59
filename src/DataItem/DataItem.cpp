// DataItem.cpp: implementation of the DataItem class.
//
//////////////////////////////////////////////////////////////////////

#include "DataItem.h"
#include "Strutils.h"

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////
CDataItem*
CDataItem_LogFile::FillData(CStdSqlOpHelper& sqlOpHelper)
{

	CDataItem_LogFile* item = new CDataItem_LogFile;
	
	int index = 0;
	item->ifileid = sqlOpHelper.GetInt(index++);
	item->strfilename = sqlOpHelper.GetStr(index++);
	item->iimporttime = sqlOpHelper.GetInt(index++);
	item->istime = sqlOpHelper.GetInt(index++);
	item->ietime = sqlOpHelper.GetInt(index++);
	item->iduration = sqlOpHelper.GetInt(index++);
	item->idistance = sqlOpHelper.GetInt(index++);
	item->itllongitude = sqlOpHelper.GetInt(index++);
	item->itllatitude = sqlOpHelper.GetInt(index++);
	item->ibrlongitude = sqlOpHelper.GetInt(index++);
	item->ibrlatitude = sqlOpHelper.GetInt(index++);
	item->imsgnum = sqlOpHelper.GetInt(index++);
	item->ieventnum = sqlOpHelper.GetInt(index++);
	item->isamplenum = sqlOpHelper.GetInt(index++);
	item->ifilesize = sqlOpHelper.GetInt(index++);
	item->strsavepath = sqlOpHelper.GetStr(index++);
	item->strsampletbname = sqlOpHelper.GetStr(index++);
	item->strsampletbname2 = sqlOpHelper.GetStr(index++);
	item->strmsgtbname = sqlOpHelper.GetStr(index++);
	item->streventtbname = sqlOpHelper.GetStr(index++);
	item->iprojecttype = sqlOpHelper.GetInt(index++);
	item->itesttype = sqlOpHelper.GetInt(index++);
	item->iyear = sqlOpHelper.GetInt(index++);
	item->ibatch = sqlOpHelper.GetInt(index++);
	item->iareatype = sqlOpHelper.GetInt(index++);
	item->iareaid = sqlOpHelper.GetInt(index++);
	item->idevicetype = sqlOpHelper.GetInt(index++);
	item->ifiletype = sqlOpHelper.GetInt(index++);
	item->iservicetype = sqlOpHelper.GetInt(index++);
	item->icarriertype = sqlOpHelper.GetInt(index++);
	item->iagentid = sqlOpHelper.GetInt(index++);
	item->istaffid = sqlOpHelper.GetInt(index++);
	item->strdesc = sqlOpHelper.GetStr(index++);
  item->idbvalue = sqlOpHelper.GetInt(index++);
	item->isubtype1 = sqlOpHelper.GetInt(index++);
	item->isubtype2 = sqlOpHelper.GetInt(index++);
	item->statstatus = sqlOpHelper.GetByte(index++);
	item->suffix_week = sqlOpHelper.GetStr(index++);
	item->suffix_day = sqlOpHelper.GetStr(index++);
	item->logtbname = sqlOpHelper.GetStr(index++);

	return item;	
}

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////
CDataItem*
CDataItem_DBColumn::FillData(CStdSqlOpHelper& sqlOpHelper)
{
	CDataItem_DBColumn* item = new CDataItem_DBColumn;
	
	int index = 0;
	
	item->columnName = sqlOpHelper.GetStr(index++);
	#ifdef USE_MYSQL
	std::string strcoltypename = sqlOpHelper.GetStr(index++);
	item->datalength = sqlOpHelper.GetInt64(index++);
	#else
	item->xusertype = sqlOpHelper.GetSmallInt(index++);
	item->datalength = sqlOpHelper.GetSmallInt(index++);
	#endif

	CStrutils::ToLowerString(item->columnName);

	return item;	
}

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CDataItem*
CDataItem_LogFileTime::FillData(CStdSqlOpHelper& sqlOpHelper)
{	
	CDataItem_LogFileTime* item = new CDataItem_LogFileTime;
	
	int index = 0;
	item->tbname = sqlOpHelper.GetStr(index++);
	item->istime = sqlOpHelper.GetInt(index++);
	item->ietime = sqlOpHelper.GetInt(index++);

	return item;	
}


