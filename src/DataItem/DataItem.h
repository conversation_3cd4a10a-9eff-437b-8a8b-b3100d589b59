// DataItem.h
//
//////////////////////////////////////////////////////////////////////

#ifndef __DataItem_H__
#define __DataItem_H__

#include "./StdHeader.h"
#include "./DataBaseDeal.h"
#include "./StdSqlOpHelper.h"

//base class 
class CDataItem
{
public:
	CDataItem()
	{
	};
	virtual ~CDataItem()
	{

	};


public:

	virtual CDataItem* FillData(CStdSqlOpHelper& sqlOpHelper) = 0;
	virtual std::string GetColumnSelSql() = 0;

};


class CDataItem_LogFile : public CDataItem
{
public:
	static CDataItem_LogFile * Instance (void)
	{
		static CDataItem_LogFile *m_pCDataItem_LogFile = nullptr;

		if (m_pCDataItem_LogFile == nullptr)
		{
			m_pCDataItem_LogFile = new CDataItem_LogFile;
		}
		return m_pCDataItem_LogFile;
	}

private:
	CDataItem_LogFile(){};
public:
	virtual ~CDataItem_LogFile(){};
	
public:
	int ifileid;
	std::string strfilename;
	int iimporttime;
	int istime;
	int ietime;
	int iduration;
	int idistance;
	int itllongitude;
	int itllatitude;
	int ibrlongitude;
	int ibrlatitude;
	int imsgnum;
	int ieventnum;
	int isamplenum;
	int ifilesize;
	std::string strsavepath;
	std::string strsampletbname;
	std::string strsampletbname2;
	std::string strmsgtbname;
	std::string streventtbname;
	int iprojecttype;
	int itesttype;
	int iyear;
	int ibatch;
	int iareatype;
	int iareaid;
	int idevicetype;
	int ifiletype;
	int iservicetype;
	int icarriertype;
	int iagentid;
	int istaffid;
	std::string strdesc;
	int idbvalue;
	int isubtype1;
	int isubtype2;
	int statstatus;
	std::string suffix_week;
	std::string suffix_day;
  std::string logtbname;

  int icalltype;
	int ilogfileid;
	std::string strmsname;
	std::string strtestsoftname;
	std::string strtestsoftversion;

	int ireserved1;
	int ireserved2;
	int ireserved3;
	int ireserved4;
	int ireserved5;
	int ireserved6;
	int ireserved7;
	int ireserved8;
	int ireserved9;
	int ireserved10;

	std::string strreserved1;
  std::string strreserved2;
	std::string strreserved3;
	std::string strreserved4;
	std::string strreserved5;
	std::string strreserved6;
	std::string strreserved7;
	std::string strreserved8;
	std::string strreserved9;
	std::string strreserved10;

	
public:

	virtual CDataItem* FillData(CStdSqlOpHelper& sqlOpHelper);
	virtual std::string GetColumnSelSql()
	{
		return "ifileid,strfilename,iimporttime,istime,ietime,iduration,idistance,itllongitude,itllatitude,ibrlongitude,\
			   ibrlatitude,imsgnum,ieventnum,isamplenum,ifilesize,strsavepath,strsampletbname,strsampletbname2,strmsgtbname,streventtbname,\
			   iprojecttype,itesttype,iyear,ibatch,iareatype,iareaid,idevicetype,ifiletype,iservicetype,icarriertype,\
			   iagentid,istaffid,strdesc,idbvalue,isubtype1,isubtype2,statstatus,suffix_week,suffix_day,logtbname";
	}
};

class CDataItem_DBColumn : CDataItem
{
public:
	static CDataItem_DBColumn * Instance (void)
	{
		static CDataItem_DBColumn *m_pCDataItem_DBColumn = nullptr;

		if (m_pCDataItem_DBColumn == nullptr)
		{
			m_pCDataItem_DBColumn = new CDataItem_DBColumn;
		}
		return m_pCDataItem_DBColumn;
	}

private:
	CDataItem_DBColumn(){};
public:
	~CDataItem_DBColumn(){};

public:
	std::string tbname;

	std::string columnName;
	int xusertype;
	int datalength;

public:

	virtual CDataItem* FillData(CStdSqlOpHelper& sqlOpHelper);
	virtual std::string GetColumnSelSql()
	{
		#ifdef USE_MYSQL
		return " COLUMN_NAME AS name, DATA_TYPE AS xusertype, CHARACTER_MAXIMUM_LENGTH AS length ";
		#else
		return " name,xusertype,length ";
		#endif
	}
};

class CDataItem_LogFileTime : CDataItem
{
public:
	static CDataItem_LogFileTime * Instance (void)
	{
		static CDataItem_LogFileTime *m_pCDataItem_LogFileTime = nullptr;

		if (m_pCDataItem_LogFileTime == nullptr)
		{
			m_pCDataItem_LogFileTime = new CDataItem_LogFileTime;
		}
		return m_pCDataItem_LogFileTime;
	}

private:
	CDataItem_LogFileTime(){};
public:
	~CDataItem_LogFileTime(){};

public:
	std::string tbname;

	int istime;
	int ietime;

public:

	virtual CDataItem* FillData(CStdSqlOpHelper& sqlOpHelper);
	virtual std::string GetColumnSelSql()
	{
		return "logname,istime,ietime";
	}
};


/************************************************************************/
/* 新建的每个接收结构类都需要在这里进行析构操作                         */
/************************************************************************/
class CDataItemManager
{
public:
	static CDataItemManager * Instance (void)
	{
		static CDataItemManager *m_pCDataItemManager = nullptr;

		if (m_pCDataItemManager == nullptr)
		{
			m_pCDataItemManager = new CDataItemManager;
		}
		return m_pCDataItemManager;
	}

public:
	CDataItemManager(){};
	~CDataItemManager()
	{
		delete CDataItem_LogFile::Instance();
		delete CDataItem_DBColumn::Instance();
		delete CDataItem_LogFileTime::Instance();
	};
};



#endif
