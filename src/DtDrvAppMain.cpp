#include "DtDrvAppMain.h"
#include "ConfigSetting.h"
// #include "StdFileFind.h"
// #include "StdCommond.h"
// #include <tlhelp32.h>

BOOL 
CDtDrvAppMain::Main()
{
 	if(FALSE == m_bInit_)
	{
		m_bInit_ = TRUE;
 
		//初始化日志
		if(FALSE == Init_Log()){
            CloseProgram();
			return FALSE;
		}

		//主处理对象初始化
		if(FALSE == Init()){
            CloseProgram();
			return FALSE;
		}	
		return TRUE;	
	}
	else
	{
		if(FALSE == CConfigSetting::Instance()->m_bIsValid_){
			PPM_OS::sleep(1000);
			return TRUE;
		}

		//在凌晨2点的时候进行前7天的数据压缩
		PPM_Time_Value tm_time;
		tm_time = PPM_OS::gettimeofday();
		if( (2 == tm_time.GetHour()) && 
			(m_iLastDay_ != tm_time.GetDay()) )
		{			
			m_iLastDay_ = tm_time.GetDay();
		}

		PPM_OS::sleep(1000);
		return TRUE;
	}

}
/**
提供全局的访问Instence的入口
*/
CDtDrvAppMain *
CDtDrvAppMain::Instence ()
{
	static CDtDrvAppMain* pDtDrvApp_ = 0;

	if (pDtDrvApp_ == 0)
	{
		pDtDrvApp_ = new CDtDrvAppMain;
	}

	return pDtDrvApp_;
}

///构造函数
CDtDrvAppMain:: CDtDrvAppMain()
	:m_logfile_("DtDrvApp.log"),
	m_socklogfile_("DtDrvApp_Sock.log")
{
	m_bInit_ = FALSE;
	m_plog_ = NULL;
	m_pServer_ = NULL;
	m_pIPv6_Server_ = NULL;
	m_pSelfMonitor_ = NULL;
	m_pMiniDog = NULL;
	m_iLastDay_ = 0;
};

///析构函数
CDtDrvAppMain:: ~CDtDrvAppMain()
{
	PPM_DEBUG((LM_INFO,"stop thread\n"));
	this->StopThread();
	//析购的顺序应该为 先停止服务侦听（服务停->接受客户端停） -> 停止数据接收 -> 停止排序 -> 
	//停止业务分析 -> 停止客户端发送 -> 关闭日志文件

	if(NULL != m_pSelfMonitor_){
		PPM_DEBUG((LM_INFO,"stop monitor\n"));
		delete m_pSelfMonitor_;
		m_pSelfMonitor_ = NULL;
	}
	PPM_OS::sleep(10);
	//删除服务侦听对象
	if(NULL != m_pServer_){
		PPM_DEBUG((LM_INFO,"stop server\n"));
		delete m_pServer_;
		m_pServer_ = NULL;
	}

	if(NULL != m_pIPv6_Server_){
		PPM_DEBUG((LM_INFO,"stop IPv6 server\n"));
		delete m_pIPv6_Server_;
		m_pIPv6_Server_ = NULL;
	}

	PPM_DEBUG((LM_INFO,"app exit!\n"));
	PPM_Log_Msg::instance()->msg_ostream(NULL,NULL);

	PPM_Public_Var::instance_SockLog()->msg_ostream(NULL,NULL);
 
	return;	
};

/**
初始化函数入口
*/
BOOL
CDtDrvAppMain::Init()
{
	//读取配置文件
	if(FALSE == ReadConfig()){
		PPM_DEBUG((LM_ERROR,"Read Config error!\n"));
		return FALSE;
	}else{
		PPM_DEBUG((LM_INFO,"Read Config Succeed!\n"));
	}

	SetLogConfig();

	//初始化数据接受通讯服务端
	if(FALSE == Init_Sock_Server()){
		PPM_DEBUG((LM_ERROR,"Init Sock Server error!\n"));
		return FALSE;
	}else{
		PPM_DEBUG((LM_INFO,"Init Sock Server Succeed!\n"));
	}
	//初始化自身监控模块
	if(FALSE == Init_Monitor()){
		return FALSE;
	}else{
		PPM_DEBUG((LM_INFO,"Init Monitor Succeed!\n"));
	}
	//初始化任务分析线程
	if(FALSE == InitTaskAnalysis()){
		return FALSE;
	}else{
		PPM_DEBUG((LM_INFO,"Init task analysis Succeed!\n"));
	}
	if(FALSE == CConfigSetting::Instance()->m_bIsValid_){
		PPM_DEBUG((LM_ERROR,"Have no valid license,client cannot be used!\n"));
		PPM_DEBUG((LM_ERROR,"Please get license by code:%s\n", CConfigSetting::Instance()->m_str_matchinfo_.c_str()));

		return TRUE;
	}

	TestFunc(); 

	return TRUE;
}

/**
初始化日志,包括主程序日志和网络日志
*/
BOOL
CDtDrvAppMain::Init_Log()
{
	//打开日志文件
	m_logfile_.Initialize("DtDrvApp.log");
	//单实例,全局访问
	m_plog_ = PPM_Log_Msg::instance();
	
	if(NULL == m_plog_){
		PPM_DEBUG((LM_ERROR,"new log error !%m\n"));
		return FALSE;
	};
	//注意,对全局有效
	//设置日志输出到文件
	m_plog_->msg_ostream(m_logfile_.GetFile(),&m_logfile_);
	m_plog_->set_flags(PPM_Log_Msg::OSTREAM);

	//设置日志级别
	m_plog_->priority_mask(LM_ERROR | LM_DEBUG | LM_INFO);

	//设置通讯日志的输出文件
	m_socklogfile_.Initialize("DtDrvApp_Sock.log");
	PPM_Public_Var::instance_SockLog()->msg_ostream(m_socklogfile_.GetFile(),&m_socklogfile_);

	return TRUE;
}

/**
根据配置初始化通讯服务端
*/
BOOL
CDtDrvAppMain::Init_Sock_Server()
{
	//添加服务端对IPv6协议的服务
	if (CConfigSetting::Instance()->m_bIsIPv6_Enable)
	{
		PPM_INET_Addr IPv6addr(CConfigSetting::Instance()->m_iDataport_, AF_INET6);

		m_pIPv6_Server_ = new DTSERVER(IPv6addr,&m_SockServer_IPv6_Actor_,SOCK_STREAM,0,0,CNodeMng::NullMode,
											CNodeMng::NullMode);

		if (m_pIPv6_Server_->GetServerStatus() != 0)
		{
			return FALSE;
		}
	}
	else //添加服务端对IPv4协议的服务
	{
		PPM_INET_Addr addr(CConfigSetting::Instance()->m_iDataport_);

		//我们定义服务端侦听到的socket属性为处理完收数据,对同一ip和端口只能连接一个socket,收数据为内存模式,
		//表明单独一个线程来收数据,发数据为空模式
		m_pServer_ = new DTSERVER(addr,&m_SockServer_Actor_,SOCK_STREAM,0,0,CNodeMng::NullMode,
											CNodeMng::NullMode);

		if (m_pServer_->GetServerStatus() != 0)
		{
			return FALSE;
		}
	}

	return TRUE;
}

/**
读取配置
*/
BOOL
CDtDrvAppMain::ReadConfig()
{
   CConfigSetting* tm_setting = CConfigSetting::Instance();
   BOOL res = tm_setting->ReadConfig();
   return res;
}

/**
设置日志常数
*/
void
CDtDrvAppMain::SetLogConfig()
{
	///设置输出
	CConfigSetting* tm_setting = CConfigSetting::Instance();
	if(NULL == tm_setting){
	   PPM_DEBUG((LM_ERROR,"CConfigSetting is null!\n"));
	   return;
	}

	u_long tm_outputflags = 0;
	tm_outputflags = PPM_Log_Msg::instance()->flags();
	PPM_Log_Msg::instance()->clr_flags(tm_outputflags);
	tm_outputflags = tm_setting->m_stru_logsetting_.bCALLBACK | 
		            	 tm_setting->m_stru_logsetting_.bOSTREAM | 
					 				 tm_setting->m_stru_logsetting_.bSTDERR;
	PPM_Log_Msg::instance()->set_flags(tm_outputflags);

    //设置级别
	tm_outputflags = 0;
	tm_outputflags = tm_setting->m_stru_logsetting_.bINFO | 
		             	 tm_setting->m_stru_logsetting_.bDEBUG | 
					 				 tm_setting->m_stru_logsetting_.bERROR |
					 				 tm_setting->m_stru_logsetting_.bWARNING |
					 				 tm_setting->m_stru_logsetting_.bNOTICE;
	PPM_Log_Msg::instance()->priority_mask(tm_outputflags);
	
}

/**
初始化自身监控模块
*/
BOOL 
CDtDrvAppMain::Init_Monitor()
{
	char tm_programname[30];
	PPM_OS::memset(tm_programname,0,sizeof(tm_programname));
#ifdef _DEBUG
	PPM_OS::sprintf(tm_programname,"DtDrvAppd.exe");
#else
	PPM_OS::sprintf(tm_programname,"DtDrvApp.exe");
#endif

	m_pMiniDog = CMiniDog::Instance();
	m_pMiniDog->InitInstance(tm_programname); 
	WatchDog();

	return TRUE;
}

BOOL 
CDtDrvAppMain::WatchDog(void)
{
	if (m_pMiniDog != NULL)
	{
		m_pMiniDog->DogIsBarking();
	}

	return true;
}


/**
初始任务分析线程
*/
BOOL 
CDtDrvAppMain::InitTaskAnalysis()
{
	return TRUE;
}

void 
CDtDrvAppMain::TestFunc()
{
    return ;

}

///获取客户端连接数
DWORD 
CDtDrvAppMain::GetClientCount()
{
	return m_SockServer_Actor_.GetVecSize();
}


///获取客户端连接数峰值
DWORD 
CDtDrvAppMain::GetMaxClientCount()
{
	return m_SockServer_Actor_.GetMaxClientCount();
}


///重置客户端连接数峰值
void 
CDtDrvAppMain::ResetClientCount()
{
	return m_SockServer_Actor_.ResetClientCount();
}

void CDtDrvAppMain::CloseProgram(void)
{
	PPM_DEBUG((LM_ERROR,"init error!program will exit in 2 seconds!\n"));
	PPM_OS::sleep(2000);
}


