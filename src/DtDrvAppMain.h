#ifndef _DTDRVAPPMAIN_H_
#define _DTDRVAPPMAIN_H_

// #include "../../comm/PPM_Client.h"
#include "./PPM_Tcp_Server.h"
#include "./PPM_Log_Msg.h"
#include "./StdMonitor.h"
// #include "../../reused/PPM_SelfMonitor.h"

#include "./DtSockServer.h"
#include "./DtSockServerActor.h"
#include "./MiniDog.h"
#include "./PPM_Thread_Manager.h"
#include "./PPM_Public_Var.h"

typedef struct processinfo
{
	std::string exename;
	int nproid;
	void Clear(void) {
		exename = "";
		nproid = -1;
	};
}tProinfo;

typedef PPM_Tcp_Server<CDtSockServer> DTSERVER;


class CDtDrvAppMain : public PPM_ThreadDeal_Base
{
public:
	CDtDrvAppMain();
	virtual ~CDtDrvAppMain();
	///复位线程
	virtual BOOL ResetThread(){ return TRUE;};

	///线程处理函数
	virtual BOOL Main();

	///初始化任务线程
	BOOL InitTaskAnalysis();

	///提供全局访问入口
	static CDtDrvAppMain* Instence();

	///初始化日志
	BOOL Init_Log();
	
	///初始化函数入口
	BOOL Init();

	BOOL WatchDog();

	///获取客户端连接数
	DWORD GetClientCount();

	///获取客户端连接数峰值
	DWORD GetMaxClientCount();

	///重置客户端连接数峰值
	void ResetClientCount();

private:
	///初始化数据接收通讯服务端
	BOOL Init_Sock_Server();

	///初始化自身监控模块
	BOOL Init_Monitor();

	///读取配置文件
	BOOL ReadConfig();
	

	///设置日志参数
	void SetLogConfig();

	void TestFunc(); 

	void CloseProgram(void);

	// 通讯服务端侦听对象，一个服务侦听端口对应一个此对象
	DTSERVER* m_pServer_;
	DTSERVER* m_pIPv6_Server_;
	// 通讯服务端反应器,用于通知新连接的到达和连接的删除
	CDtSockServerActor m_SockServer_Actor_;
	CDtSockServerActor m_SockServer_IPv6_Actor_;

	PPM_SelfMonitor* m_pSelfMonitor_;

	///日志文件
	CStdLog m_logfile_;

	///网络日志文件
	CStdLog m_socklogfile_;

	///日志指针
	PPM_Log_Msg* m_plog_;

	CMiniDog* m_pMiniDog;

	BOOL m_bInit_;
	int m_iLastDay_;
	std::vector<std::string> m_vec_ziplist_;
};


#endif





