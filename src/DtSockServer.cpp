#include "./DtSockServer.h"
// #include "./CommandDefine.h"

// #include "../util/Search_PI.h"

#include "./SockTestDataMngDeal.h"
#include "./SockConfigMngDeal.h"
#include "./SockDBMngDeal.h"
#include "./SockSearchDeal.h"
#include "./SockStatisticDeal.h"
#include "./SockUserMngDeal.h"
#include "./SockCommMngDeal.h"
#include "./SockDIYDeal.h"
#include "./DataBaseInfo.h"
#include "./StdNetBuffer.h"

const char DbConnectFailed[] = "get db connection failed!\n";

CDtSockServer::CDtSockServer()
{
  m_pDataBaseDeal_ = new CDataBaseDeal();

	m_SockAuthenDeal_.Init(m_pDataBaseDeal_, this, m_pSendBuf_);
};

CDtSockServer::~CDtSockServer()
{
	if(m_pDataBaseDeal_ != NULL)
	{
		delete m_pDataBaseDeal_;
		m_pDataBaseDeal_ = NULL;
	}
}

    
/**
收到数据后的处理,需要将收到的数据填入到排序元素中
*/
BOOL
CDtSockServer::DealData(const BYTE* const pData, const int nCount)
{ 
	int tm_ioffset = 0;

	BYTE spByte[4] = {0x0a, 0x0b, 0x0c, 0x0d};
	BYTE temp[4];
	CStdNetBufferReader::GetBuffer(pData, tm_ioffset, 4, temp, nCount);
	if (memcmp(temp, spByte, 4) == 0)
	{
		return DealServerSock(pData, nCount, tm_ioffset);
	}
	else 
	{
		tm_ioffset = 0;
	}

  if (TRUE == m_SockAuthenDeal_.Authencate(pData,nCount,tm_ioffset))
	{
		return DealAuthencateSuccess(pData, nCount, tm_ioffset);
	}
	else 
	{
		return DealAuthencateFialed();
	}
	return TRUE;
}

BOOL CDtSockServer::DealAuthencateSuccess(const BYTE* const pData, const int nCount, int& tm_ioffset)
{
	int tm_icurrentdbid = 
        m_SockAuthenDeal_.m_stru_authinfo_.iDBID == -1?m_SockAuthenDeal_.m_stru_authinfo_.iDBIDSub:m_SockAuthenDeal_.m_stru_authinfo_.iDBID;

	if(tm_icurrentdbid > 0 && CDataBaseInfo::Instance()->IsDbConnInfoExist(tm_icurrentdbid))
	{
		m_pDataBaseDeal_->SetDbConnPara(
			CDataBaseInfo::Instance()->m_map_pDBConnInfo_[tm_icurrentdbid]);	
	}
	else
	{
		PPM_DEBUG((LM_ERROR,DbConnectFailed));
		m_SockAuthenDeal_.FeedBackOverInt(CMD1_SEARCH,-1);
		return FALSE;
	}

	m_sockInfo.pDataBaseDeal_ = m_pDataBaseDeal_;
	m_sockInfo.pSendBuf_ = m_pSendBuf_;
	m_sockInfo.pServerDeal_ = this;

	if (DealSock(pData, nCount, tm_ioffset) == FALSE)
	{
		return FALSE;
	}
    
	return TRUE;
}
BOOL CDtSockServer::DealAuthencateFialed(void)
{
	if(CDataBaseInfo::Instance()->isEnableAccountControl && m_SockAuthenDeal_.m_eUserState == CSockAuthenDeal::Authen_Success)
	{
		UpdateUserLoginInfo();
	}

	if(m_SockAuthenDeal_.m_eUserState == CSockAuthenDeal::Authen_UnknowUser && m_SockAuthenDeal_.m_bIsAuthenticated_ != 0xaa)
	{
		WriteUserLoginFailLog("未知用户", "用户名错误！");
	}
	else if(m_SockAuthenDeal_.m_eUserState == CSockAuthenDeal::Authen_PasswdErr && m_SockAuthenDeal_.m_bIsAuthenticated_ != 0xaa)
	{
		WriteUserLoginFailLog(m_SockAuthenDeal_.m_stru_authinfo_.pchLogonName, "密码错误！");
	}
	else if(m_SockAuthenDeal_.m_eUserState == CSockAuthenDeal::Authen_DIYErr && m_SockAuthenDeal_.m_bIsAuthenticated_ != 0xaa)
	{
		WriteUserLoginFailLog(m_SockAuthenDeal_.m_stru_authinfo_.pchLogonName, m_SockAuthenDeal_.m_strError.c_str());
	}
    
	return TRUE;
}

BOOL CDtSockServer::DealServerSock(const BYTE* const pData, const int nCount, int& tm_ioffset)
{
	int dbid = -1;
	CStdNetBufferReader::GetInt(pData, tm_ioffset, dbid, nCount);

	if(dbid > 0 && CDataBaseInfo::Instance()->IsDbConnInfoExist(dbid))
	{
		m_pDataBaseDeal_->SetDbConnPara(CDataBaseInfo::Instance()->m_map_pDBConnInfo_[dbid]);	
	}
	else
	{
		PPM_DEBUG((LM_ERROR,DbConnectFailed));
		m_SockAuthenDeal_.FeedBackOverInt(CMD1_SEARCH,-1);
		return FALSE;
	}

	m_sockInfo.pDataBaseDeal_ = m_pDataBaseDeal_;
	m_sockInfo.pSendBuf_ = m_pSendBuf_;
	m_sockInfo.pServerDeal_ = this;

	if (DealSock(pData, nCount, tm_ioffset) == FALSE)
	{
		return FALSE;
	}
	return TRUE;
}
 
/**
当收到连接请求的时候，发送鉴权请求
when received connect request,send Authentication request
*/
void 
CDtSockServer::BeginDeal()
{

}


BOOL
CDtSockServer::DealSock(const BYTE* const pData, const int nCount,int& ioffset)
{
  CSockFactory* sockFactory = SwitchSock(pData, nCount, ioffset);

	if (nullptr == sockFactory)
	{
		this->m_ptcpclient_->CloseClient();
		return FALSE;//返回错误并释放连接
	}

	if (sockFactory->DealSock(pData, nCount,ioffset, m_sockInfo) == FALSE)
	{
		CSearch_PI::FeedBackOver(CMD1_SEARCH, RESTYPE_SEARCHEND, m_pSendBuf_, this);
		return FALSE;
	}

    return TRUE;
}


CSockFactory*
CDtSockServer::SwitchSock(const BYTE* const pData, const int nCount,int& ioffset)
{
	CSockFactory* sock = nullptr;

	switch(pData[ioffset++])
	{
	case CMD1_TESTDATAMANAGEMENT://测试数据维护命令字
		sock = CSockTestDataMngDeal::Instance();
		break;

	case CMD1_SEARCH://信息查询命令字
		sock = CSockSearchDeal::Instance();
		break;

	case CMD1_DIYSEARCH://自定义查询命令字
		sock = CSockDIYDeal::Instance();
		break;

	case CMD1_CONFIG_MNG://配置管理命令字
		sock = CSockConfigMngDeal::Instance();
		break;

	case CMD1_DBMANAGEMENT://数据库管理命令字
		sock = CSockDBMngDeal::Instance();
		break;

	case CMD1_SEARCH_STATISTIC: //统计信息查询命令字
		sock = CSockStatisticDeal::Instance();
		break;

	case CMD1_COMMUNITY_MNG:   //楼宇信息管理
		sock = CSockCommMngDeal::Instance();
		break;

	case CMD1_USER_MNG://用户权限管理
		sock = CSockUserMngDeal::Instance();
		break;

	default: //不能识别的命令字 
		{
		}
		break;
	}

	return sock;
}


void 
CDtSockServer::UpdateUserLoginInfo()
{
	char szTemp[32] = {0};
	char ip_addr[20] = {0};
	char sql[1024] = {0};

	time_t t = time(NULL);
	struct tm* stime = localtime(&t);
	PPM_OS::snprintf(szTemp, 32,"%04d-%02d-%02d %02d:%02d:%02d",1900+stime->tm_year, 1+stime->tm_mon,stime->tm_mday,
		stime->tm_hour, stime->tm_min,stime->tm_sec);

	this->m_ptcpclient_->GetRemoteAddr().get_host_addr(ip_addr, 20);

	PPM_OS::snprintf(sql, 1024,"update tb_cfg_static_user set lastlogin_time = '%s', lastlogin_ip = '%s' where iid = %d", 
		szTemp, ip_addr, m_SockAuthenDeal_.m_stru_authinfo_.iUserID);

	if(nullptr != m_pDataBaseDeal_)
	{
		m_pDataBaseDeal_->ExecSql(sql);
	}
}

///用户登录失败写日志
void
CDtSockServer::WriteUserLoginFailLog(const char* username, const char *event_desc)
{
	char tm_sql[8000];
	char user_ip[20] = {0};

	this->m_ptcpclient_->GetRemoteAddr().get_host_addr(user_ip, 20);

	#ifdef USE_MYSQL
		PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		" call rd_sp_log_sysop_insertbyname('%s','%s',0,3100,3105, '%s', -10000); ",
		username,
		user_ip,
		event_desc);
	#else
		PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		" use %s exec rd_sp_log_sysop_insertbyname '%s','%s',0,3100,3105, '%s', -10000",
		CConfigSetting::Instance()->m_stru_MainDBSetting_.pchDBName,
		username,
		user_ip,
		event_desc);
	#endif

	if(nullptr != m_pDataBaseDeal_)
	{
		m_pDataBaseDeal_->ExecSql(tm_sql);
	}
}



