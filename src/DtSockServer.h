#ifndef _DTSOCKSERVER_H_
#define _DTSOCKSERVER_H_

#include "./PPM_CONN_Actor.h"

#include "./DataBaseDeal.h"
#include "./StdHeader.h"
#include "./ConfigSetting.h"
#include "./SearchHead.h"

#include "./SockAuthenDeal.h"
#include "./SockFactory.h"


class CDtSockServer : public PPM_Server_Deal_Base
{
public:
	CDtSockServer();
	virtual ~CDtSockServer();

	///收到数据后的处理
	virtual BOOL DealData(const BYTE* const pData, const int nCount);

	//连接到来后的处理
	virtual void BeginDeal();
private:

	BOOL DealSock(const BYTE* const pData, const int nCount,int& ioffset);

	CSockFactory* SwitchSock(const BYTE* const pData, const int nCount,int& ioffset);

  CSockAuthenDeal m_SockAuthenDeal_;

  CDataBaseDeal* m_pDataBaseDeal_;

	//BYTE      m_bIsAuthenticated_;//是否已经经过鉴权
	//BYTE      m_bIsAuthenticated_Step_;//0:还未鉴权,1表示已经发送用户名,2表示已经发送密码
	char m_pchRandomStr_[30];  //随机串序列

	CStdLock m_lock_;

	//add by ljc 
	BYTE m_pSendBuf_[SEND_BUF_MAXSIZE];

  STRU_SockInfo m_sockInfo;

	void UpdateUserLoginInfo();
	void WriteUserLoginFailLog(const char* username, const char *event_desc);
	BOOL DealAuthencateSuccess(const BYTE* const pData, const int nCount, int& tm_ioffset);
  BOOL DealAuthencateFialed(void);
  BOOL DealServerSock(const BYTE* const pData, const int nCount, int& tm_ioffset);
};


#endif
