#include "./DtSockServerActor.h"
///当有新的连接来时的处理
void 
CDtSockServerActor::NewClientShow(PPM_Server_Deal_Base* pServer_Deal)
{
	char temp[12] = {0};

	DWORD size = GetVecSize();

	sprintf(temp, "%ld",size);

	m_nMaxCount = m_nMaxCount > size ? m_nMaxCount : size;
}

///当已建立的连接删除时的处理
void 
CDtSockServerActor::ClientDelete(PPM_Server_Deal_Base* pServer_Deal)
{
	
}

///当已建立的连接删除时的处理
void 
CDtSockServerActor::ClientSockInvalid(PPM_Server_Deal_Base* pServer_Deal)
{
	char temp[12] = {0};

	sprintf(temp, "%d", GetVecSize()-1);
}

///获取客户端连接数峰值
DWORD 
CDtSockServerActor::GetMaxClientCount()
{
	return m_nMaxCount;
}

///重置客户端连接数峰值
void 
CDtSockServerActor::ResetClientCount()
{
	m_nMaxCount = 0;
}





