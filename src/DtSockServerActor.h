#ifndef _DTSOCKSERVER_ACTOR_H_
#define _DTSOCKSERVER_ACTOR_H_

#include "./PPM_CONN_Actor.h"
#include "./DtSockServer.h"
#include "./PPM_SelfMonitor.h"

/**
此类为通讯的服务端的反应类,用于通知应用有新的连接,或者是有连接关闭等
*/
class CDtSockServerActor : public PPM_Tcp_Server_Actor
{
public:
	CDtSockServerActor(){
		m_nMaxCount = 0;
	};
	virtual ~CDtSockServerActor(){
	};

	///当有新的连接来时的处理
	virtual void NewClientShow(PPM_Server_Deal_Base* pServer_Deal);

	///当已建立的连接删除时的处理
	virtual void ClientDelete(PPM_Server_Deal_Base* pServer_Deal);

	///当已建立的连接删除时的处理
	virtual void ClientSockInvalid(PPM_Server_Deal_Base* pServer_Deal);

	///获取客户端连接数峰值
	DWORD GetMaxClientCount();

	///重置客户端连接数峰值
	void ResetClientCount();

private:
	///通讯服务端所建立的连接列表
	std::list<PPM_Server_Deal_Base*> m_list_SockServer_;

	//连接数最大值
	DWORD m_nMaxCount;
};

#endif

