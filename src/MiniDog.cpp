// MiniDog.cpp: implementation of the CMiniDog class.
//
//////////////////////////////////////////////////////////////////////

#include "./MiniDog.h"
#include "./StdThread.h"

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CMiniDog::CMiniDog(void)
:m_ThrdMnger(),
m_Lock(),
m_pclsFileMapping(NULL)
{

}

CMiniDog::~CMiniDog(void)
{
	Stop();
}

void CMiniDog::Stop(void)
{
	CStdAutoLock theLock(&m_Lock);

	m_ThrdMnger.clear();

	if (m_pclsFileMapping != NULL)
	{
		delete m_pclsFileMapping;
		m_pclsFileMapping = NULL;
	}
}

CMiniDog* CMiniDog::Instance(void)
{
	static CMiniDog theInstance;

	return &theInstance;
}

bool CMiniDog::InitInstance(const char* szMapName)
{
	m_pclsFileMapping = new CStdFileMapping(szMapName, 4);

	return true;
}

void CMiniDog::Register(CStdThread* const pThread)
{
	if (NULL != pThread)
	{
		CStdAutoLock theLock(&m_Lock);

		m_ThrdMnger.push_back(pThread);

		int nFlag = 1;

		if (m_pclsFileMapping != NULL)
		{
			m_pclsFileMapping->WriteFileMap(&nFlag, 4);
		}
	}
}

bool CMiniDog::DogIsBarking(void)
{
	CStdAutoLock theLock(&m_Lock);

	int nFlag = 1;

	CStdThread* pTemp = NULL;

	std::size_t i;
	for (i = 0; i < m_ThrdMnger.size(); i++)
	{
		pTemp = m_ThrdMnger[i];

		if ((pTemp != NULL) && (!(pTemp->IsAlive())))
		{
			nFlag = 0;

			if (m_pclsFileMapping != NULL)
			{
				m_pclsFileMapping->WriteFileMap(&nFlag, 4);
			}

			return false;
		}
	}

	if (i == m_ThrdMnger.size())
	{
		if (m_pclsFileMapping == NULL)
		{
			return true;
		}

		m_pclsFileMapping->WriteFileMap(&nFlag, 4);
	}

	return true;
}

