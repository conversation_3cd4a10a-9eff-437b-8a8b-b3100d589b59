// MiniDog.h: interface for the CMiniDog class.
//
//////////////////////////////////////////////////////////////////////

#ifndef __MINIDOG_H__
#define __MINIDOG_H__

#pragma once

#include "./StdFileMapping.h"

class CStdThread;

class CMiniDog 
{
public:
	CMiniDog(void);
	
	virtual ~CMiniDog(void);

	static CMiniDog* Instance(void);

	bool InitInstance(const char* szMapName);

	void Register(CStdThread* const pThread);

	bool DogIsBarking(void);

	void Stop(void);
private:
	std::vector<CStdThread*> m_ThrdMnger;

	CStdLock m_Lock;

	CStdFileMapping* m_pclsFileMapping;
};

#endif
