// ComDef_DIY.h: interface for the CComDef_DIY class.
//
//////////////////////////////////////////////////////////////////////

#ifndef __COMDEF_DIY_H__
#define __COMDEF_DIY_H__

#define REQTYPE_DIY_AREA_COVER_GRID						  0x10	//REQUEST
#define REQTYPE_DIY_AREA_COVER_SAMPLE					  0x11	//REQUEST
#define REQTYPE_DIY_AREA_EVENT					          0x12	//REQUEST
#define REQTYPE_DIY_AREA_LOG                              0x13	//REQUEST
#define REQTYPE_DIY_TABLE_BYDBID                          0x14	//REQUEST
#define REQTYPE_DIY_AREA_CELL_KPI                         0x15	//REQUEST

#define REQTYPE_DIY_CELL_GRID                             0x16	//REQUEST
#define REQTYPE_DIY_CELL_SAMPLE                           0x17	//REQUEST 
#define REQTYPE_DIY_AREA_COVER_SAMPLE_COVERLEAK           0x18	//REQUEST 

#define REQTYPE_DIY_LOG_KPI                               0x19	//REQUEST 

#define REQTYPE_DIY_AREASTAT_KPI                          0x1a	//REQUEST 
#define REQTYPE_DIY_AREASTAT_EVNET                        0x1b	//REQUEST 

#define REQTYPE_DIY_AREA_EVNET_ES                         0x1c	//REQUEST 

#define REQTYPE_DIY_STATI_SCAN_CELL_GRID                  0x1d	//REQUEST 

#define REQTYPE_DIY_STATI_NB_SCAN_GRID			          0x1e	//REQUEST 

#define REQTYPE_DIY_AREA_GSMR_EVENT                       0x1F	//REQUEST ;

#define REQTYPE_DIY_LOG_EVENT                             0x70 	//REQUEST
#define REQTYPE_DIY_LOG_SAMPLE                            0x71	//REQUEST 
#define REQTYPE_DIY_LOG_MSG                               0x72	//REQUEST   
#define REQTYPE_DIY_LOG_MSG_WITHAllRTP                    0x73  //REQUEST
#define REQTYPE_DIY_LOG_GSMR_EVENT                        0x74  //REQUEST

#define REQTYPE_DIY_MODEL_TABLE                           0xa0	//REQUEST
#define REQTYPE_DIY_MODEL_SQL                             0xa1	//REQUEST
#define REQTYPE_DIY_MODEL_SQL_FORMAINDB                   0xa2	//REQUEST





#endif 
