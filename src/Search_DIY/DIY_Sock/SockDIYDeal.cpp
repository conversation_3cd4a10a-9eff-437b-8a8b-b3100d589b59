// SockDIYDeal.cpp: implementation of the CSockDIYDeal class.
//
//////////////////////////////////////////////////////////////////////

#include "./SearchArea_Event.h"
#include "./SearchArea_Gsmr_Event.h"
#include "./SearchArea_Grid.h"
#include "./SearchArea_Log.h"
#include "./SearchArea_Sample.h"
#include "./SearchArea_Sample_CoverLeak.h"
#include "./SearchArea_Cell_Kpi.h"
#include "./SearchArea_Cell_Grid.h"
#include "./SearchCell_Sample.h"
#include "./SearchAreaStat_Kpi.h"
#include "./SearchAreaStat_Event.h"
#include "./SearchArea_Event_ES.h"
#include "./SearchArea_Scan_Grid.h"
#include "./SearchArea_NB_Grid.h"

#include "./SearchLog_Event.h"
#include "./SearchLog_Sample.h"
#include "./SearchLog_Msg.h"
#include "./SearchLog_Kpi.h"
#include "./SearchLog_Gsmr_Event.h"

#include "./SearchModel_Table.h"
#include "./SearchModel_SqlSearch.h"

#include "./CommandDefine.h"

#include "./SockDIYDeal.h"


//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CSockDIYDeal::CSockDIYDeal()
:m_mapSddReqTypePos()
{
	InitmReqTypeMap();
}

CSockDIYDeal::~CSockDIYDeal()
{
	m_mapSddReqTypePos.clear();
}

BOOL
CSockDIYDeal::DealSock(const BYTE* const pData, const int nCount,int& ioffset, const STRU_SockInfo& sockInfo)
{
  char tm_addr[512];
	sockInfo.pServerDeal_->m_ptcpclient_->GetInetAddr()->addr_to_string(tm_addr, sizeof(tm_addr), 1);
	std::string strUserInfo = "[" + std::string(tm_addr) + "]";

	if(CMD2_REQUEST == pData[ioffset])//请求
	{
		MOVENEXT(ioffset,1,nCount);

		auto it = m_mapSddReqTypePos.find(pData[ioffset++]);

		if (it != m_mapSddReqTypePos.end())
		{
			(this->*(it->second))(pData, nCount, ioffset, sockInfo, strUserInfo);
		}
		else
		{
			PPM_DEBUG((LM_ERROR, "unknown search diy request type:%d\n", pData[ioffset - 1]));
			return FALSE;
		}
	}
	else //响应 
	{
		PPM_DEBUG((LM_ERROR,"should not be search data management response:%d\n",pData[ioffset+1]));
		return FALSE;
	}
	return TRUE;
}

void 
CSockDIYDeal::


Deal_Reqtype_DIY_Area_Cover_Grid(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo)
{
	DEALDATA2(CSearchArea_Grid, strUserInfo);
}
void 
CSockDIYDeal::
Deal_Reqtype_DIY_StatI_NB_Scan_Grid(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo)
{
	DEALDATA2(CSearchArea_NB_Grid, strUserInfo);
}
void 
CSockDIYDeal::
Deal_Reqtype_DIY_Area_Cover_Sample(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo)
{
	DEALDATA2(CSearchArea_Sample, strUserInfo);
}
void 
CSockDIYDeal::
Deal_Reqtype_DIY_Area_Cover_Sample_CoverLeak(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo)
{
	DEALDATA2(CSearchArea_Sample_CoverLeak, strUserInfo);
}
void 
CSockDIYDeal::
Deal_Reqtype_DIY_Area_Event(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo)
{
	DEALDATA2(CSearchArea_Event, strUserInfo);
}
void 
CSockDIYDeal::
Deal_Reqtype_DIY_Area_Log(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo)
{
	DEALDATA2(CSearchArea_Log, strUserInfo);
}
void 
CSockDIYDeal::
Deal_Reqtype_DIY_Area_Cell_Kpi(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo)
{
	DEALDATA2(CSearchArea_Cell_Kpi, strUserInfo);
}
void 
CSockDIYDeal::
Deal_Reqtype_DIY_AreaStat_Kpi(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo)
{
	DEALDATA2(CSearchAreaStat_Kpi, strUserInfo);
}
void 
CSockDIYDeal::
Deal_Reqtype_DIY_AreaStat_Evnet(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo)
{
	DEALDATA2(CSearchAreaStat_Event, strUserInfo);
}
void 
CSockDIYDeal::
Deal_Reqtype_DIY_Area_Evnet_Es(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo)
{
	DEALDATA2(CSearchArea_Event_ES, strUserInfo);
}
void 
CSockDIYDeal::
Deal_Reqtype_DIY_StatI_Scan_Cell_Grid(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo)
{
	DEALDATA2(CSearchArea_Scan_Grid, strUserInfo);
}
void 
CSockDIYDeal::
Deal_Reqtype_DIY_Log_Event(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo)
{
	DEALDATA2(CSearchLog_Event, strUserInfo);
}
void 
CSockDIYDeal::
Deal_Reqtype_DIY_Log_Sample(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo)
{
	DEALDATA2(CSearchLog_Sample, strUserInfo);
}
void 
CSockDIYDeal::
Deal_Reqtype_DIY_Log_Msg(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo)
{
	DEALDATA2(CSearchLog_Msg, strUserInfo);
}
void 
CSockDIYDeal::
Deal_Reqtype_DIY_Log_Msg_WithAllRtp(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo)
{
	DEALDATA2(CSearchLog_Msg_WithAllRtp, strUserInfo);
}
void 
CSockDIYDeal::
Deal_Reqtype_DIY_Log_Kpi(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo)
{
	DEALDATA2(CSearchLog_Kpi, strUserInfo);
}
void 
CSockDIYDeal::
Deal_Reqtype_DIY_Cell_Grid(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo)
{
	DEALDATA2(CSearchArea_Cell_Grid, strUserInfo);
}
void 
CSockDIYDeal::
Deal_Reqtype_DIY_Cell_Sample(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo)
{
	DEALDATA2(CSearchCell_Sample, strUserInfo);
}
void 
CSockDIYDeal::
Deal_Reqtype_DIY_Model_Table(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo)
{
	DEALDATA2(CSearchModel_Table, strUserInfo);
}
void 
CSockDIYDeal::
Deal_Reqtype_DIY_Model_Sql(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo)
{
	DEALDATA2(CSearchModel_SqlSearch, strUserInfo);
}
void 
CSockDIYDeal::
Deal_Reqtype_DIY_Model_Sql_Formaindb(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo)
{
	DEALDATA2(CSearchModel_SqlSearch_ForMainDB, strUserInfo);
}
void
CSockDIYDeal::
Deal_Reqtype_DIY_Area_Gsmr_Event(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo)
{
	DEALDATA2(CSearchArea_Gsmr_Event, strUserInfo);
}
void
CSockDIYDeal::
Deal_Reqtype_DIY_Log_Gsmr_Event(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo)
{
	DEALDATA2(CSearchLog_Gsmr_Event, strUserInfo);
}
void CSockDIYDeal::InitmReqTypeMap(void)
{
	m_mapSddReqTypePos.insert(make_pair(REQTYPE_DIY_AREA_COVER_GRID, &CSockDIYDeal::Deal_Reqtype_DIY_Area_Cover_Grid));
	m_mapSddReqTypePos.insert(make_pair(REQTYPE_DIY_AREA_COVER_SAMPLE, &CSockDIYDeal::Deal_Reqtype_DIY_Area_Cover_Sample));
	m_mapSddReqTypePos.insert(make_pair(REQTYPE_DIY_AREA_EVENT, &CSockDIYDeal::Deal_Reqtype_DIY_Area_Event));
	m_mapSddReqTypePos.insert(make_pair(REQTYPE_DIY_AREA_LOG, &CSockDIYDeal::Deal_Reqtype_DIY_Area_Log));
	m_mapSddReqTypePos.insert(make_pair(REQTYPE_DIY_AREA_CELL_KPI, &CSockDIYDeal::Deal_Reqtype_DIY_Area_Cell_Kpi));
	m_mapSddReqTypePos.insert(make_pair(REQTYPE_DIY_CELL_GRID, &CSockDIYDeal::Deal_Reqtype_DIY_Cell_Grid));
	m_mapSddReqTypePos.insert(make_pair(REQTYPE_DIY_CELL_SAMPLE, &CSockDIYDeal::Deal_Reqtype_DIY_Cell_Sample));
	m_mapSddReqTypePos.insert(make_pair(REQTYPE_DIY_AREA_COVER_SAMPLE_COVERLEAK, &CSockDIYDeal::Deal_Reqtype_DIY_Area_Cover_Sample_CoverLeak));
	m_mapSddReqTypePos.insert(make_pair(REQTYPE_DIY_LOG_KPI, &CSockDIYDeal::Deal_Reqtype_DIY_Log_Kpi));
	m_mapSddReqTypePos.insert(make_pair(REQTYPE_DIY_AREASTAT_KPI, &CSockDIYDeal::Deal_Reqtype_DIY_AreaStat_Kpi));
	m_mapSddReqTypePos.insert(make_pair(REQTYPE_DIY_AREASTAT_EVNET, &CSockDIYDeal::Deal_Reqtype_DIY_AreaStat_Evnet));
	m_mapSddReqTypePos.insert(make_pair(REQTYPE_DIY_AREA_EVNET_ES, &CSockDIYDeal::Deal_Reqtype_DIY_Area_Evnet_Es));
	m_mapSddReqTypePos.insert(make_pair(REQTYPE_DIY_STATI_SCAN_CELL_GRID, &CSockDIYDeal::Deal_Reqtype_DIY_StatI_Scan_Cell_Grid));
	m_mapSddReqTypePos.insert(make_pair(REQTYPE_DIY_STATI_NB_SCAN_GRID, &CSockDIYDeal::Deal_Reqtype_DIY_StatI_NB_Scan_Grid));
	m_mapSddReqTypePos.insert(make_pair(REQTYPE_DIY_LOG_EVENT, &CSockDIYDeal::Deal_Reqtype_DIY_Log_Event));
	m_mapSddReqTypePos.insert(make_pair(REQTYPE_DIY_LOG_SAMPLE, &CSockDIYDeal::Deal_Reqtype_DIY_Log_Sample));
	m_mapSddReqTypePos.insert(make_pair(REQTYPE_DIY_LOG_MSG, &CSockDIYDeal::Deal_Reqtype_DIY_Log_Msg));
	m_mapSddReqTypePos.insert(make_pair(REQTYPE_DIY_LOG_MSG_WITHAllRTP, &CSockDIYDeal::Deal_Reqtype_DIY_Log_Msg_WithAllRtp));
	m_mapSddReqTypePos.insert(make_pair(REQTYPE_DIY_MODEL_TABLE, &CSockDIYDeal::Deal_Reqtype_DIY_Model_Table));
	m_mapSddReqTypePos.insert(make_pair(REQTYPE_DIY_MODEL_SQL, &CSockDIYDeal::Deal_Reqtype_DIY_Model_Sql));
	m_mapSddReqTypePos.insert(make_pair(REQTYPE_DIY_MODEL_SQL_FORMAINDB, &CSockDIYDeal::Deal_Reqtype_DIY_Model_Sql_Formaindb));
	m_mapSddReqTypePos.insert(make_pair(REQTYPE_DIY_AREA_GSMR_EVENT, &CSockDIYDeal::Deal_Reqtype_DIY_Area_Gsmr_Event));
	m_mapSddReqTypePos.insert(make_pair(REQTYPE_DIY_LOG_GSMR_EVENT, &CSockDIYDeal::Deal_Reqtype_DIY_Log_Gsmr_Event));

}




















