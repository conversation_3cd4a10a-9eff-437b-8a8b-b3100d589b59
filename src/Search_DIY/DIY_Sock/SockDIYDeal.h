#pragma once

#include "SockFactory.h"
#include "StdHeader.h"




class CSockDIYDeal : public CSockFactory , public Singleton<CSockDIYDeal>
{
public:
	CSockDIYDeal(void);
	virtual ~CSockDIYDeal(void);

public:
	virtual BOOL DealSock(const BYTE* const pData, const int nCount,int& ioffset, const STRU_SockInfo& sockInfo);

private:
	typedef void (CSockDIYDeal::*DealReqTypeData)(const BYTE* const pData, const int nCount, int& ioffset,
                                                          const STRU_SockInfo& sockInfo, std::string strUserInfo);
	std::map<BYTE, DealReqTypeData> m_mapSddReqTypePos;

    void Deal_Reqtype_DIY_Area_Cover_Grid(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo);
    void Deal_Reqtype_DIY_StatI_NB_Scan_Grid(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo);
    void Deal_Reqtype_DIY_Area_Cover_Sample(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo);
    void Deal_Reqtype_DIY_Area_Cover_Sample_CoverLeak(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo);
    void Deal_Reqtype_DIY_Area_Event(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo);
    void Deal_Reqtype_DIY_Area_Log(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo);
    void Deal_Reqtype_DIY_Area_Cell_Kpi(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo);
    void Deal_Reqtype_DIY_AreaStat_Kpi(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo);
    void Deal_Reqtype_DIY_AreaStat_Evnet(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo);
    void Deal_Reqtype_DIY_Area_Evnet_Es(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo);
    void Deal_Reqtype_DIY_StatI_Scan_Cell_Grid(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo);
    void Deal_Reqtype_DIY_Log_Event(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo);
    void Deal_Reqtype_DIY_Log_Sample(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo);
    void Deal_Reqtype_DIY_Log_Msg(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo);
    void Deal_Reqtype_DIY_Log_Msg_WithAllRtp(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo);
    void Deal_Reqtype_DIY_Log_Kpi(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo);
    void Deal_Reqtype_DIY_Cell_Grid(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo);
    void Deal_Reqtype_DIY_Cell_Sample(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo);
    void Deal_Reqtype_DIY_Model_Table(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo);
    void Deal_Reqtype_DIY_Model_Sql(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo);
    void Deal_Reqtype_DIY_Model_Sql_Formaindb(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo);
	void Deal_Reqtype_DIY_Area_Gsmr_Event(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo);
	void Deal_Reqtype_DIY_Log_Gsmr_Event(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo);

	void InitmReqTypeMap(void);
};
