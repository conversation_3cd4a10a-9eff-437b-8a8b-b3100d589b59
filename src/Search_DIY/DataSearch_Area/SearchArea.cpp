// SearchArea.cpp: implementation of the CSearchArea class.
//
//////////////////////////////////////////////////////////////////////

#include "SearchArea.h"


//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CSearchArea::CSearchArea()
{
}

CSearchArea::~CSearchArea()
{
   ClearData();
}

BOOL 
CSearchArea::FetchSearchCondiction(const BYTE* const pData, const int nCount,int& ioffset)
{
  CHECLFALSE(conditionMaker.InitSerchCondition(pData, nCount, ioffset));

	//获取指定的查询条件，没有则退出,查询必须带上时间
  timeSpanIntersect = 
        (CSearchConditionItem_TimeSpanIntersect*)conditionMaker.GetSearchCondition(CSearchConditionMaker::SearchConditionType_TimeSpanIntersect);  
	CHECLNULL(timeSpanIntersect);

	return TRUE;
}

BOOL
CSearchArea::GetLogFile()
{
   CColumnResult columnResult_tb_log_file_time;

   CHECLFALSE(   columnResult_tb_log_file_time.Init
	   ( CColumnDef::tb_log_file_time, 
	   "tb_log_file_time",										   
	   m_pDataBaseDeal_,
	   m_pServerDeal_,
	   m_pSendBuf_)
	   );

   std::string strSQL = p_SqlMaker->Sql_Select(columnResult_tb_log_file_time.GetDBSearchSql(), "tb_log_file_time", timeSpanIntersect->sql);
   columnResult_tb_log_file_time.SearchSqlGetData(strSQL, (CDataItem*)CDataItem_LogFileTime::Instance(), &m_pDataRows_LogFileTime);


   CColumnResult columnResult_tb_log_file;

   CHECLFALSE(   columnResult_tb_log_file.Init
	   ( CColumnDef::tb_log_file, 
	   "tb_model_log_file",									   
	   m_pDataBaseDeal_,
	   m_pServerDeal_,
	   m_pSendBuf_)
	   );

   for (auto p_CDataItem = m_pDataRows_LogFileTime.begin(); p_CDataItem != m_pDataRows_LogFileTime.end(); ++p_CDataItem)
   {
	   CDataItem_LogFileTime* p_DataItem_LogFileTime = (CDataItem_LogFileTime*)*p_CDataItem;

	   strSQL = p_SqlMaker->Sql_Select_File_Add_Status(columnResult_tb_log_file.GetDBSearchSql("a"), 
		   p_DataItem_LogFileTime->tbname, conditionMaker.GetSearchConfitionSql("a"));

	   columnResult_tb_log_file.SendColumnsIDToClient(CMD1_DIYSEARCH,
													   CMD2_RESPONSE,
													   RESTYPE_COLUMN_FILE_INFO);

	   columnResult_tb_log_file.SearchSqlGetDataAndSendData(
							   CMD1_DIYSEARCH,
							   CMD2_RESPONSE,
							   RESTYPE_DIY_FILE_INFO,
							   strSQL, 
							   (CDataItem*)CDataItem_LogFile::Instance(),
							   &m_pDataRows_LogFile);   

   }

	return TRUE;
}


BOOL 
CSearchArea::MakeLogFileGroup()
{
   return TRUE;
}


BOOL 
CSearchArea::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
   if (FALSE == FetchSearchCondiction(pData, nCount, ioffset)
	   || FALSE == GetLogFile()
	   || FALSE == MakeLogFileGroup()
	   || FALSE == DealDataSub())
   {
       ClearData();

	   CSearch_PI::FeedBackOver(CMD1_DIYSEARCH,RESTYPE_SEARCHERROR, m_pServerDeal_);
	   return FALSE;
   }

   ClearData();

   CSearch_PI::FeedBackOver(CMD1_DIYSEARCH,RESTYPE_SEARCHEND, m_pServerDeal_);
   return TRUE;
}

BOOL 
CSearchArea::DealDataSub()
{
	return TRUE;
}

BOOL
CSearchArea::ClearData()
{
  for (auto p_pDataItem = m_pDataRows_LogFileTime.begin(); p_pDataItem != m_pDataRows_LogFileTime.end(); ++p_pDataItem)
	{
		delete(*p_pDataItem);
        *p_pDataItem = NULL;
	}
	m_pDataRows_LogFileTime.clear();

	for (auto p_pDataItem = m_pDataRows_LogFile.begin(); p_pDataItem != m_pDataRows_LogFile.end(); ++p_pDataItem)
	{
		delete(*p_pDataItem);
		*p_pDataItem = NULL;
	}
	m_pDataRows_LogFile.clear();

  m_map_fileItemList.clear();

  conditionMaker.ClearItem();

	return TRUE;
}


