// SearchArea.h: interface for the CSearchArea class.
//
//////////////////////////////////////////////////////////////////////

#ifndef __SEARCHAREA_H__
#define __SEARCHAREA_H__

#include "./SearchDIY.h"

#define RESTYPE_COLUMN_FILE_INFO                      0x11
#define	RESTYPE_DIY_FILE_INFO                         0x12

class CSearchArea : public CSearchDIY
{
public:
	CSearchArea();
	virtual ~CSearchArea();
    
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);

protected:
	virtual BOOL DealDataSub();
	virtual BOOL FetchSearchCondiction(const BYTE* const pData, const int nCount,int& ioffset);
	virtual BOOL GetLogFile();	
	virtual BOOL ClearData();

    virtual BOOL MakeLogFileGroup();

protected:
	std::map<STRU_FILE_LIST2, std::vector<CDataItem_LogFile*>> m_map_fileItemList;
	CSearchConditionItem_TimeSpanIntersect* timeSpanIntersect;

	char m_column[8000];  //获取的文件ID
	CSearchConditionMaker conditionMaker;

	std::vector<CDataItem*> m_pDataRows_LogFile;
	std::vector<CDataItem*> m_pDataRows_LogFileTime;
};

#endif
