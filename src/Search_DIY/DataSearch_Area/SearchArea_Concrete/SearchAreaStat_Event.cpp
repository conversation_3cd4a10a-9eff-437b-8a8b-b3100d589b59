#include "SearchAreaStat_Event.h"

CSearchAreaStat_Event::CSearchAreaStat_Event(void)
{
}

CSearchAreaStat_Event::~CSearchAreaStat_Event(void)
{
}

BOOL
CSearchAreaStat_Event::FetchSearchCondiction(const BYTE* const pData, const int nCount,int& ioffset)
{
	CHECLFALSE(CSearchArea::FetchSearchCondiction(pData, nCount, ioffset));

	CHECLFALSE(conditionMaker_Event.InitSerchCondition(pData, nCount, ioffset));

	PPM_OS::memset(&m_column, 0, sizeof(m_column));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,m_column,sizeof(m_column)));

	return TRUE;
}

BOOL 
CSearchAreaStat_Event::MakeLogFileGroup()
{
	m_map_fileItemList.clear();

	for (auto p_DataItem = m_pDataRows_LogFile.begin();
		p_DataItem != m_pDataRows_LogFile.end(); ++p_DataItem)
	{
		CDataItem_LogFile* p_logfile = (CDataItem_LogFile*)*p_DataItem;

		STRU_FILE_LIST2 tm1;

		tm1.testtype = p_logfile->itesttype;
		tm1.strlog = p_logfile->logtbname;
		tm1.strtb = p_logfile->suffix_week;	
		tm1.servicetype = p_logfile->iservicetype;

		m_map_fileItemList[tm1].push_back(p_logfile); 
	}
	return TRUE;
}

BOOL 
CSearchAreaStat_Event::DealDataSub_GSM(int iservicetype, std::string tm_file, std::string tbname)
{
	DealDataSub_Model(CColumnDef::tb_model_stat_area_event, "tb_gsm_stat_event_area_" +
		tbname, std::string(tm_file), RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREASTAT_KPI_EVENT_GSM);

	return TRUE;
}

BOOL 
CSearchAreaStat_Event::DealDataSub_CDMA(int iservicetype, std::string tm_file, std::string tbname)
{
	DealDataSub_Model(CColumnDef::tb_model_stat_area_event, "tb_cdma_stat_event_area_" + 
		tbname, std::string(tm_file), RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREASTAT_KPI_EVENT_CDMA);

	return TRUE;
}


BOOL 
CSearchAreaStat_Event::DealDataSub_WCDMA(int iservicetype, std::string tm_file, std::string tbname)
{
	DealDataSub_Model(CColumnDef::tb_model_stat_area_event, "tb_wcdma_stat_event_area_" + 
		tbname, std::string(tm_file), RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREASTAT_KPI_EVENT_WCDMA);

	return TRUE;
}

BOOL 
CSearchAreaStat_Event::DealDataSub_TDSCDMA(int iservicetype, std::string tm_file, std::string tbname)
{
	DealDataSub_Model(CColumnDef::tb_model_stat_area_event, "tb_tdscdma_stat_event_area_" +
		tbname, std::string(tm_file), RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREASTAT_KPI_EVENT_TDSCDMA);

	return TRUE;
}

BOOL 
CSearchAreaStat_Event::DealDataSub_LTE(int iservicetype, std::string tm_file, std::string tbname)
{
	DealDataSub_Model(CColumnDef::tb_model_stat_area_event, "tb_lte_stat_event_area_" + 
		tbname, std::string(tm_file), RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREASTAT_KPI_EVENT_LTE);

	return TRUE;
}

BOOL 
CSearchAreaStat_Event::DealDataSub_LTE_FDD(int iservicetype, std::string tm_file, std::string tbname)
{
	DealDataSub_Model(CColumnDef::tb_model_stat_area_event, "tb_lte_fdd_stat_event_area_" +
		tbname, std::string(tm_file), RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREASTAT_KPI_EVENT_LTE_FDD);

	return TRUE;
}

BOOL 
CSearchAreaStat_Event::DealDataSub_CDMA2000(int iservicetype, std::string tm_file, std::string tbname)
{
	DealDataSub_Model(CColumnDef::tb_model_stat_area_event, "tb_cdma2000_stat_event_area_" +
		tbname, std::string(tm_file), RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREASTAT_KPI_EVENT_CDMA2000);

	return TRUE;
}

BOOL
CSearchAreaStat_Event::DealDataSub_NR(int iservicetype, std::string tm_file, std::string tbname)
{
	DealDataSub_Model(CColumnDef::tb_model_stat_area_event, "tb_nr_stat_event_area_" +
		tbname, std::string(tm_file), RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREASTAT_KPI_EVENT_NR);

	return TRUE;
}

BOOL 
CSearchAreaStat_Event::DealDataSub_Model(const int sampletbid, const std::string sampletbname, 
                                         const std::string ifileidSql, const BYTE bColumIDCmd, const BYTE bThirdCmd)
{
	CColumnResult columnResult_tb_model_event;

	CHECLFALSE(   columnResult_tb_model_event.Init
		( std::string(m_column),
		CStdCommond::itostr(sampletbid), 
		sampletbname,
		m_pDataBaseDeal_,
		m_pServerDeal_,
		m_pSendBuf_)
		);

	std::string sql_conditionMaker_Event = conditionMaker_Event.GetSearchConfitionSql();
	if (0 == sql_conditionMaker_Event.length())
	{
		sql_conditionMaker_Event = " ifileid in (" + std::string(ifileidSql) + ")";
	}
	else 
	{
		sql_conditionMaker_Event += " and ifileid in (" + std::string(ifileidSql) + ")";
	}

	std::string strSQL = p_SqlMaker->Sql_Select(columnResult_tb_model_event.GetDBSearchSql(), sampletbname, sql_conditionMaker_Event);

	columnResult_tb_model_event.SendColumnsIDToClient(CMD1_DIYSEARCH, CMD2_RESPONSE, bColumIDCmd);    

	columnResult_tb_model_event.SearchSqlSendData(	   
		CMD1_DIYSEARCH,
		CMD2_RESPONSE,
		bThirdCmd,
		strSQL);  

	return TRUE;
}
