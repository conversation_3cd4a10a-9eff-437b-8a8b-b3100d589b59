#pragma once
#include "./SearchArea_Sub.h"

#define	RESTYPE_DIY_AREASTAT_KPI_EVENT_GSM     		    0x21

#define	RESTYPE_DIY_AREASTAT_KPI_EVENT_TDSCDMA   		0x23

#define	RESTYPE_DIY_AREASTAT_KPI_EVENT_WCDMA    		0x25

#define	RESTYPE_DIY_AREASTAT_KPI_EVENT_CDMA			    0x27

#define	RESTYPE_DIY_AREASTAT_KPI_EVENT_CDMA2000		    0x29

#define	RESTYPE_DIY_AREASTAT_KPI_EVENT_LTE       		0x2A

#define	RESTYPE_DIY_AREASTAT_KPI_EVENT_LTE_FDD			0x2B

#define	RESTYPE_DIY_AREASTAT_KPI_EVENT_NR       		0x2C

#define RESTYPE_COLUMN_DEFINE						    0xeb		//通用Column	

class CSearchAreaStat_Event
	: public CSearchArea_Sub
{
public:
	CSearchAreaStat_Event(void);
public:
	~CSearchAreaStat_Event(void);

protected:
	BOOL FetchSearchCondiction(const BYTE* const pData, const int nCount,int& ioffset);
	virtual BOOL MakeLogFileGroup();

	virtual BOOL DealDataSub_GSM(int iservicetype, std::string tm_file, std::string tbname);
	virtual BOOL DealDataSub_CDMA(int iservicetype, std::string tm_file, std::string tbname);
	virtual BOOL DealDataSub_CDMA2000(int iservicetype, std::string tm_file, std::string tbname);
	virtual BOOL DealDataSub_TDSCDMA(int iservicetype, std::string tm_file, std::string tbname);
	virtual BOOL DealDataSub_LTE(int iservicetype, std::string tm_file, std::string tbname);
	virtual BOOL DealDataSub_LTE_FDD(int iservicetype, std::string tm_file, std::string tbname);
	virtual BOOL DealDataSub_WCDMA(int iservicetype, std::string tm_file, std::string tbname);
	virtual BOOL DealDataSub_Scan(int iservicetype, std::string tm_file, std::string tbname){return true;}
	virtual BOOL DealDataSub_NR(int iservicetype, std::string tm_file, std::string tbname);
	virtual BOOL DealDataSub_Model(const int sampletbid, const std::string sampletbname, const std::string ifileidSql, const BYTE bColumIDCmd, const BYTE bThirdCmd);



	CSearchConditionMaker conditionMaker_Event;
};
