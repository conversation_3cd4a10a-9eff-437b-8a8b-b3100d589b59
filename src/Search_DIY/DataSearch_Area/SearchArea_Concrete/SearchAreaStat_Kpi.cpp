#include "SearchAreaStat_Kpi.h"

const std::string tb_wcdma_stati_pshs_area_ = "tb_wcdma_stati_pshs_area_";

CSearchAreaStat_Kpi::CSearchAreaStat_Kpi(void)
{
}

CSearchAreaStat_Kpi::~CSearchAreaStat_Kpi(void)
{
}


BOOL
CSearchAreaStat_Kpi::FetchSearchCondiction(const BYTE* const pData, const int nCount,int& ioffset)
{
	CHECLFALSE(CSearchArea::FetchSearchCondiction(pData, nCount, ioffset));

	CHECLFALSE(searchCondition_AreaStat.InitSerchCondition(pData, nCount, ioffset));

	PPM_OS::memset(&m_column, 0, sizeof(m_column));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,m_column,sizeof(m_column)));

	return TRUE;
}

BOOL 
CSearchAreaStat_Kpi::MakeLogFileGroup()
{
	for (auto p_DataItem = m_pDataRows_LogFile.begin(); p_DataItem != m_pDataRows_LogFile.end(); ++p_DataItem)
	{
		CDataItem_LogFile* p_logfile = (CDataItem_LogFile*)*p_DataItem;

		STRU_FILE_LIST2 tm1;
		tm1.testtype = p_logfile->itesttype;
		tm1.strlog = p_logfile->logtbname;		
		tm1.servicetype = p_logfile->iservicetype;
		tm1.strtb = p_logfile->suffix_week;	

		m_map_fileItemList[tm1].push_back(p_logfile); 
	}

	return TRUE;
}

BOOL
CSearchAreaStat_Kpi::DealDataSub_GSM(int iservicetype, std::string tm_file, std::string tbname)
{
	std::string strSQL = "";

	switch (iservicetype)
	{
	case CEnumDef::Ser_GSM_VOICE:
	case CEnumDef::Ser_GSM_IDLE:
		{	
			DealDataSub_Model(CColumnDef::tb_model_gsm_stati_voice_area, "tb_gsm_stati_voice_area_" + 
				tbname, tm_file, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREASTAT_KPI_GSM);
		}
		break;

	case CEnumDef::Ser_GPRS_DATA:
	case CEnumDef::Ser_EDGE_DATA:
		{	
			DealDataSub_Model(CColumnDef::tb_model_gsm_stati_data_area, "tb_gsm_stati_data_area_" +
				tbname, tm_file, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREASTAT_KPI_GPRS);	  
		}
		break;
	default:
		break;
	}
	return TRUE;
}

BOOL
CSearchAreaStat_Kpi::DealDataSub_CDMA(int iservicetype, std::string tm_file, std::string tbname)
{
	std::string strSQL;

	switch(iservicetype)
	{
	case CEnumDef::Ser_CDMA_VOICE:
	case CEnumDef::Ser_CDMA_IDLE:
		{
			DealDataSub_Model(CColumnDef::tb_model_cdma_stati_voice_area, "tb_cdma_stati_voice_area_" + 
				tbname, tm_file, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREASTAT_KPI_CDMA_V);
		}
		break;

	case CEnumDef::Ser_CDMA1X_DATA:
		{
             DealDataSub_Model(CColumnDef::tb_model_cdma_stati_d_area, "tb_cdma_stati_data_area_" + 
				 tbname, tm_file, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREASTAT_KPI_CDMA_D);
		}
	default:
		break;
	}
	return TRUE;
}

BOOL
CSearchAreaStat_Kpi::DealDataSub_CDMA2000(int iservicetype, std::string tm_file, std::string tbname)
{ 
	std::string strSQL = "";

	switch(iservicetype)
	{
	case CEnumDef::Ser_CDMA2000_VOICE:
	case CEnumDef::Ser_CDMA2000_VIDEO:
		{
		}
		break;

	case CEnumDef::Ser_CDMA2000_IDLE:
	case CEnumDef::Ser_CDMA2000_DATA:
		{
			DealDataSub_Model(CColumnDef::tb_model_cdma2000_stati_data_area, "tb_cdma2000_stati_data_area_" +
				tbname, tm_file, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREASTAT_KPI_CDMA2000_D);	
		}
		break;
	default:
		break;
	}
	return TRUE;
}

BOOL
CSearchAreaStat_Kpi::DealDataSub_TDSCDMA(int iservicetype, std::string tm_file, std::string tbname)
{
	std::string strSQL = "";

	switch(iservicetype)
	{
	case CEnumDef::Ser_TDSCDMA_VOICE:
	case CEnumDef::Ser_TDSCDMA_IDLE:
		{
			DealDataSub_Model(CColumnDef::tb_model_tdscdma_stati_amr_area, "tb_tdscdma_stati_amr_area_" + 
				tbname, tm_file, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREASTAT_KPI_TDSCDMA_AMR);
		}
		break;

	case CEnumDef::Ser_TDSCDMA_VIDEO:
		{
			DealDataSub_Model(CColumnDef::tb_model_tdscdma_stati_vp_area, "tb_tdscdma_stati_vp_area_" + 
				tbname, tm_file, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREASTAT_KPI_TDSCDMA_VP);	
		}
		break;

	case CEnumDef::Ser_TDSCDMA_DATA:
	case CEnumDef::Ser_TDSCDMA_HSDPA:
		{
			DealDataSub_Model(CColumnDef::tb_model_tdscdma_stati_ps_area, "tb_tdscdma_stati_ps_area_" +
				tbname, tm_file, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREASTAT_KPI_TDSCDMA_PS);	
		}
		break;
	default:
		break;
	}
	return TRUE;
}

BOOL
CSearchAreaStat_Kpi::DealDataSub_LTE(int iservicetype, std::string tm_file, std::string tbname)
{
	if (CEnumDef::IsLteTddServiceType(iservicetype))
	{
		DealDataSub_Model(CColumnDef::tb_model_lte_stati_amr_area, "tb_lte_stati_amr_area_" +
			tbname, tm_file, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREASTAT_KPI_LTE_AMR);
	}

	return TRUE;
}

BOOL
CSearchAreaStat_Kpi::DealDataSub_LTE_FDD(int iservicetype, std::string tm_file, std::string tbname)
{
	if (CEnumDef::IsLteFddServiceType(iservicetype))
	{
		DealDataSub_Model(CColumnDef::tb_model_lte_fdd_stati_amr_area, "tb_lte_fdd_stati_amr_area_" +
			tbname, tm_file, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREASTAT_KPI_LTE_FDD_AMR);
	}

	return TRUE;
}

BOOL
CSearchAreaStat_Kpi::DealDataSub_Signal(int iservicetype, std::string tm_file, std::string tbname)
{
	std::string strSQL = "";

	switch(iservicetype)
	{
	case CEnumDef::SER_LTE_SIGNAL:
		{
			DealDataSub_Model(CColumnDef::tb_model_lte_signal_stati_area, "tb_signal_stati_amr_area_" +
				tbname, tm_file, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREASTAT_KPI_LTE_SIGNAL);
		}
		break;
	default:
		break;
	}
	return TRUE;
}

BOOL
CSearchAreaStat_Kpi::DealDataSub_Scan(int iservicetype, std::string tm_file, std::string tbname)
{
	std::string strSQL = "";

	switch(iservicetype)
	{
	case CEnumDef::Ser_SCAN:
		{
			DealDataSub_Model(CColumnDef::tb_scan_gsm_stati_area, "tb_scan_gsm_stati_area_" +
				tbname, tm_file, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREASTAT_KPI_SCAN_GSM);
		}
		break;
	case CEnumDef::Ser_SCAN_TD:
		{
			DealDataSub_Model(CColumnDef::tb_scan_tdscdma_stati_area, "tb_scan_tdscdma_stati_area_" +
				tbname, tm_file, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREASTAT_KPI_SCAN_TDSCDMA);
		}
		break;
	case CEnumDef::Ser_LTE_SCAN_TOPN:
		{
			DealDataSub_Model(CColumnDef::tb_scan_ltetopn_stati_area, "tb_scan_ltetopn_stati_area_" +
				tbname, tm_file, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREASTAT_KPI_SCAN_LTETOPN);
		}
		break;

	case CEnumDef::Ser_NBIOT_SCAN_TOPN:
		{
			DealDataSub_Model(CColumnDef::tb_scan_nbiot_topn_stati_area, "tb_scan_nbiot_topn_stati_area_" +
				tbname, tm_file, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREASTAT_KPI_SCAN_NBIOT_TOPN);
		}
		break;
	default:
		break;
	}
	return TRUE;
}


BOOL
CSearchAreaStat_Kpi::DealDataSub_WCDMA(int iservicetype, std::string tm_file, std::string tbname)
{
	std::string strSQL = "";

	switch (iservicetype)
	{
	case CEnumDef::Ser_WCDMA_VOICE:
	case CEnumDef::Ser_WCDMA_IDLE:
		{
			DealDataSub_Model(CColumnDef::tb_model_wcdma_stati_amr_area, "tb_wcdma_stati_amr_area_" + 
				tbname, tm_file, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREASTAT_KPI_WCDMA_AMR);		
		}
		break;

	case CEnumDef::Ser_WCDMA_VIDEO:
		{
			DealDataSub_Model(CColumnDef::tb_model_wcdma_stati_vp_area, "tb_wcdma_stati_vp_area_" + 
				tbname, tm_file, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREASTAT_KPI_WCDMA_VP);	 	
		}
		break;

	case CEnumDef::Ser_WCDMA_DATA:
		{
			DealDataSub_Model(CColumnDef::tb_model_wcdma_stati_ps_area, "tb_wcdma_stati_ps_area_" +
				tbname, tm_file, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREASTAT_KPI_WCDMA_PS);	 	
		}
		break;

	case CEnumDef::Ser_WCDMA_HSDPA:
		{
			DealDataSub_Model(CColumnDef::tb_model_wcdma_stati_pshs_area_psdpa, tb_wcdma_stati_pshs_area_ + 
				tbname, tm_file, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREASTAT_KPI_WCDMA_PSHS);
		}
		break;
	case CEnumDef::Ser_WCDMA_HSUPA:
		{
			DealDataSub_Model(CColumnDef::tb_model_wcdma_stati_pshs_area_psupa, tb_wcdma_stati_pshs_area_ +
				tbname, tm_file, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREASTAT_KPI_WCDMA_PSHS);
		}
		break;
	default:
		break;
	}
	return TRUE;
}

BOOL
CSearchAreaStat_Kpi::DealDataSub_NR(int iservicetype, std::string tm_file, std::string tbname)
{
	if (CEnumDef::IsNrServiceType(iservicetype))
	{
		DealDataSub_Model(CColumnDef::tb_nr_stati_amr_area, "tb_nr_stati_amr_area_" +
			tbname, tm_file, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREASTAT_KPI_NR_AMR);
	}

	return TRUE;
}

BOOL 
CSearchAreaStat_Kpi::DealDataSub_Model(const int tbid, const std::string tbname, const std::string tm_file, const BYTE bColumIDCmd, const BYTE bThirdCmd)
{
	std::string columnid = "";
	CHECLFALSE(AddSearchColumn(tbid, std::string(m_column), columnid));


	CColumnResult columnResult_stati_area;

	CHECLFALSE(   columnResult_stati_area.Init
		( columnid, 
		CStdCommond::itostr(tbid), 
		tbname,
		m_pDataBaseDeal_,
		m_pServerDeal_,
		m_pSendBuf_)
		);


	std::vector<STRU_ColumnAppendData> mapColumnAppendData;
	STRU_ColumnAppendData columnAppendData;
	std::string sql_area = " iareatype, iareaid, ";

	columnAppendData.columnName = "iareatype"; 
	columnAppendData.dataType = TYPE_CS_INT;
	mapColumnAppendData.push_back(columnAppendData);
	columnAppendData.columnName = "iareaid"; 
	columnAppendData.dataType = TYPE_CS_INT;
	mapColumnAppendData.push_back(columnAppendData);

	std::string sql = searchCondition_AreaStat.GetSearchConfitionSql() + " and ifileid in (" + tm_file + ")";
	std::string strSQL = p_SqlMaker->Sql_Select(sql_area + columnResult_stati_area.GetDBSearchSql(), tbname, sql);     

	columnResult_stati_area.SendColumnsIDToClient(CMD1_DIYSEARCH, CMD2_RESPONSE, bColumIDCmd);    

	columnResult_stati_area.SearchSqlSendDataWithMyData(	   
		CMD1_DIYSEARCH,
		CMD2_RESPONSE,
		bThirdCmd,
		strSQL, 
		mapColumnAppendData);  

	return TRUE;
}

BOOL
CSearchAreaStat_Kpi::AddSearchColumn(const int gridtbid, const std::string s_column, std::string& t_column)
{ 
	if (0 == (int)s_column.length())
	{
		return FALSE;
	}

	CColumnItem* p_FileidColumn = CColumnDef::Instance()->GetColumn(gridtbid, "image1");
	CHECLNULL(p_FileidColumn);

	t_column = CStdCommond::itostr(p_FileidColumn->imageID) + "," + CStdCommond::itostr(p_FileidColumn->paraID) + "," + 
		CStdCommond::itostr(p_FileidColumn->tbID) + "," + s_column;

	return TRUE;
}

BOOL
CSearchAreaStat_Kpi::ClearData(void)
{
	CSearchArea::ClearData();
	searchCondition_AreaStat.ClearItem();
	m_map_fileItemList.clear();
	return TRUE;
}
