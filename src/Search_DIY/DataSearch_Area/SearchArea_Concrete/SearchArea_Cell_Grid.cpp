#include "SearchArea_Cell_Grid.h"

const std::string longitude = "itllongitude";
const std::string latitude = "itllatitude";
const std::string tb_wcdma_stati_pshs_cell_grid_ = "tb_wcdma_stati_pshs_cell_grid_";

CSearchArea_Cell_Grid::CSearchArea_Cell_Grid(void)
{
}

CSearchArea_Cell_Grid::~CSearchArea_Cell_Grid(void)
{
}


BOOL
CSearchArea_Cell_Grid::FetchSearchCondiction(const BYTE* const pData, const int nCount,int& ioffset)
{
	CHECLFALSE(CSearchArea::FetchSearchCondiction(pData, nCount, ioffset));

	CHECLFALSE(conditionMaker_Cell.InitSerchCondition(pData, nCount, ioffset));

	areaSelectIntersect = 
        (CSearchConditionItem_AreaSelectIntersect*)conditionMaker.GetSearchCondition(CSearchConditionMaker::SearchConditionType_AreaSelectIntersect);  
    cellSelect = (CSearchConditionItem_CellSelect*)conditionMaker_Cell.GetSearchCondition(CSearchConditionMaker::SearchConditionType_CellSelect);
	nrcellSelect = (CSearchConditionItem_NrCellSelect*)conditionMaker_Cell.GetSearchCondition(CSearchConditionMaker::SearchConditionType_NrCellSelect);

	PPM_OS::memset(&m_column, 0, sizeof(m_column));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,m_column,sizeof(m_column)));

	return TRUE;
}

BOOL 
CSearchArea_Cell_Grid::MakeLogFileGroup()
{
	for (auto p_DataItem = m_pDataRows_LogFile.begin(); p_DataItem != m_pDataRows_LogFile.end(); ++p_DataItem)
	{
		CDataItem_LogFile* p_logfile = (CDataItem_LogFile*)*p_DataItem;

		STRU_FILE_LIST2 tm1;
		tm1.testtype = p_logfile->itesttype;
		tm1.strlog = p_logfile->logtbname;		
		tm1.servicetype = p_logfile->iservicetype;
		tm1.strtb = p_logfile->suffix_week;	

		m_map_fileItemList[tm1].push_back(p_logfile); 
	}
	return TRUE;
}


BOOL
CSearchArea_Cell_Grid::DealDataSub_GSM(int iservicetype, std::string tm_file, std::string tbname)
{
	std::string strSQL = "";

	switch (iservicetype)
	{
	case CEnumDef::Ser_GSM_VOICE:
	case CEnumDef::Ser_GSM_IDLE:
		{	
			DealDataSub_Model(CColumnDef::tb_model_gsm_stati_voice_cell_grid, "tb_gsm_stati_voice_cell_grid_" + 
				tbname, tm_file, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_CELL_COVER_GRID_GSM);
		}
		break;

	case CEnumDef::Ser_GPRS_DATA:
	case CEnumDef::Ser_EDGE_DATA:
		{	
			DealDataSub_Model(CColumnDef::tb_model_gsm_stati_data_cell_grid, "tb_gsm_stati_data_cell_grid_" + 
				tbname, tm_file, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_CELL_COVER_GRID_GPRS);	  
		}
		break;	
	default:
		break;
	}
	return TRUE;
}

BOOL
CSearchArea_Cell_Grid::DealDataSub_CDMA(int iservicetype, std::string tm_file, std::string tbname)
{
	std::string strSQL;

	switch(iservicetype)
	{
	case CEnumDef::Ser_CDMA_VOICE:
		{

		}
		break;

	case CEnumDef::Ser_CDMA1X_DATA:
		{

		}
	default:
		break;
	}
	return TRUE;
}

BOOL
CSearchArea_Cell_Grid::DealDataSub_CDMA2000(int iservicetype, std::string tm_file, std::string tbname)
{ 
	std::string strSQL = "";

	switch(iservicetype)
	{
	case CEnumDef::Ser_CDMA2000_VOICE:
	case CEnumDef::Ser_CDMA2000_VIDEO:
		{		   
		}
		break;

	case CEnumDef::Ser_CDMA2000_DATA:
		{

		}
		break;
	default:
		break;
	}
	return TRUE;
}

BOOL
CSearchArea_Cell_Grid::DealDataSub_TDSCDMA(int iservicetype, std::string tm_file, std::string tbname)
{
	std::string strSQL = "";

	switch(iservicetype)
	{
	case CEnumDef::Ser_TDSCDMA_VOICE:
	case CEnumDef::Ser_TDSCDMA_IDLE:
		{
			DealDataSub_Model(CColumnDef::tb_model_tdscdma_stati_amr_cell_grid, "tb_tdscdma_stati_amr_cell_grid_" +
				tbname, tm_file, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_CELL_COVER_GRID_TDSCDMA_AMR);
		}
		break;

	case CEnumDef::Ser_TDSCDMA_VIDEO:
		{
			DealDataSub_Model(CColumnDef::tb_model_tdscdma_stati_vp_cell_grid, "tb_tdscdma_stati_vp_cell_grid_" +
				tbname, tm_file, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_CELL_COVER_GRID_TDSCDMA_VP);	
		}
		break;

	case CEnumDef::Ser_TDSCDMA_DATA:
	case CEnumDef::Ser_TDSCDMA_HSDPA:
		{
			DealDataSub_Model(CColumnDef::tb_model_tdscdma_stati_ps_cell_grid, "tb_tdscdma_stati_ps_cell_grid_" +
				tbname, tm_file, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_CELL_COVER_GRID_TDSCDMA_PS);	
		}
		break;
	default:
		break;
	}
	return TRUE;
}


BOOL
CSearchArea_Cell_Grid::DealDataSub_WCDMA(int iservicetype, std::string tm_file, std::string tbname)
{
	std::string strSQL = "";

	switch (iservicetype)
	{
	case CEnumDef::Ser_WCDMA_VOICE:
	case CEnumDef::Ser_WCDMA_IDLE:
		{
			DealDataSub_Model(CColumnDef::tb_model_wcdma_stati_amr_cell_grid, "tb_wcdma_stati_amr_cell_grid_" +
				tbname, tm_file, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_CELL_COVER_GRID_WCDMA_AMR);		
		}
		break;

	case CEnumDef::Ser_WCDMA_VIDEO:
		{
			DealDataSub_Model(CColumnDef::tb_model_wcdma_stati_vp_cell_grid, "tb_wcdma_stati_vp_cell_grid_" +
				tbname, tm_file, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_CELL_COVER_GRID_WCDMA_VP);	 	

		}
		break;

	case CEnumDef::Ser_WCDMA_DATA:
		{
			DealDataSub_Model(CColumnDef::tb_model_wcdma_stati_ps_cell_grid, "tb_wcdma_stati_ps_cell_grid_" +
				tbname, tm_file, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_CELL_COVER_GRID_WCDMA_PS);	 	
		}
		break;

	case CEnumDef::Ser_WCDMA_HSDPA:
		{
			DealDataSub_Model(CColumnDef::tb_model_wcdma_stati_pshs_grid_psdpa, tb_wcdma_stati_pshs_cell_grid_ +
				tbname, tm_file, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_CELL_COVER_GRID_WCDMA_PSHS);
		}
		break;

	case CEnumDef::Ser_WCDMA_HSUPA:
		{
			DealDataSub_Model(CColumnDef::tb_model_wcdma_stati_pshs_grid_psupa, tb_wcdma_stati_pshs_cell_grid_ +
				tbname, tm_file, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_CELL_COVER_GRID_WCDMA_PSHS);
		}
		break;
	default:
		break;
	}
	return TRUE;
}

BOOL
CSearchArea_Cell_Grid::DealDataSub_Scan(int iservicetype, std::string tm_file, std::string tbname)
{
	std::string strSQL = "";

	switch (iservicetype)
	{
	case CEnumDef::Ser_SCAN:
		{
			DealDataSub_Model_ScanGsm(CColumnDef::tb_scan_gsm_stati_cell_grid, "tb_scan_gsm_stati_cell_grid_" +
				tbname, tm_file, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_CELL_COVER_GRID_SCAN_GSM);		
		}
		break;

	case CEnumDef::Ser_SCAN_CDMA:
		{
		}
		break;

	case CEnumDef::Ser_SCAN_TD:
		{
           DealDataSub_Model_ScanTdscdma(CColumnDef::tb_scan_tdscdma_stati_cell_grid, "tb_scan_tdscdma_stati_cell_grid_" +
			   tbname, tm_file, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_CELL_COVER_GRID_SCAN_TDSCDMA);		
		}
		break;

	case CEnumDef::Ser_LTE_SCAN_TOPN:
		{
			DealDataSub_Model(CColumnDef::tb_scan_ltetopn_stati_cell_grid, "tb_scan_ltetopn_stati_cell_grid_" +
				tbname, tm_file, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_CELL_COVER_GRID_SCAN_LTE_TOPN);
		}
		break;

	case CEnumDef::Ser_NBIOT_SCAN_TOPN:
		{
			DealDataSub_Model(CColumnDef::tb_scan_nbiot_topn_stati_cell_grid, "tb_scan_nbiot_topn_stati_cell_grid_" +
				tbname, tm_file, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_CELL_COVER_GRID_SCAN_NBIOT_TOPN);
		}
		break;
	default:
		break;
	}
	return TRUE;
}

BOOL
CSearchArea_Cell_Grid::DealDataSub_LTE(int iservicetype, std::string tm_file, std::string tbname)
{
	if (CEnumDef::IsLteTddServiceType(iservicetype))
	{
		DealDataSub_Model(CColumnDef::tb_model_lte_stati_amr_cell_grid, "tb_lte_stati_amr_cell_grid_" +
			tbname, tm_file, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_CELL_COVER_GRID_LET_AMR);
	}

	return TRUE;
}

BOOL
CSearchArea_Cell_Grid::DealDataSub_LTE_FDD(int iservicetype, std::string tm_file, std::string tbname)
{
	if (CEnumDef::IsLteFddServiceType(iservicetype))
	{
		DealDataSub_Model(CColumnDef::tb_model_lte_fdd_stati_amr_cell_grid, "tb_lte_fdd_stati_amr_cell_grid_" +
			tbname, tm_file, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_CELL_COVER_GRID_LET_FDD_AMR);
	}

	return TRUE;
}

BOOL
CSearchArea_Cell_Grid::DealDataSub_Signal(int iservicetype, std::string tm_file, std::string tbname)
{
	std::string strSQL = "";

	switch (iservicetype)
	{
	case CEnumDef::SER_LTE_SIGNAL:
		{
			DealDataSub_Model(CColumnDef::tb_model_lte_signal_stati_cell_grid, "tb_signal_stat_cell_grid_image_hw_" +
				tbname, tm_file, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_CELL_COVER_GRID_LET_SIGNAL);		
		}
		break;
	default:
		break;
	}
	return TRUE;
}

BOOL 
CSearchArea_Cell_Grid::DealDataSub_NR(int iservicetype, std::string tm_file, std::string tbname)
{
	if (CEnumDef::IsNrServiceType(iservicetype))
	{
		DealDataSub_Model_Nr(CColumnDef::tb_nr_stati_amr_cell_grid, "tb_nr_stati_amr_cell_grid_" +
			tbname, tm_file, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_CELL_COVER_GRID_NR);
	}

	return TRUE;
}

BOOL 
CSearchArea_Cell_Grid::DealDataSub_Model(const int tbid, const std::string gridtbname, const std::string tm_file, const BYTE bColumIDCmd, const BYTE bThirdCmd)
{
	std::string columnid = "";
	CHECLFALSE(AddSearchColumn(tbid, std::string(m_column), columnid));

	CColumnResult columnResult_stati_grid;

	CHECLFALSE(   columnResult_stati_grid.Init
		( columnid,
		CStdCommond::itostr(tbid), 
		gridtbname,
		m_pDataBaseDeal_,
		m_pServerDeal_,
		m_pSendBuf_)
		);

	std::vector<STRU_ColumnAppendData> mapColumnAppendData;
	STRU_ColumnAppendData columnAppendData;
	std::string sql_area = " ilac,ici,itllongitude,itllatitude, ";
	
  columnAppendData.columnName = "ilac"; 
	columnAppendData.dataType = TYPE_CS_INT;
  mapColumnAppendData.push_back(columnAppendData);
	columnAppendData.columnName = "ici"; 
	columnAppendData.dataType = TYPE_CS_INT;
	mapColumnAppendData.push_back(columnAppendData);
	columnAppendData.columnName = longitude; 
	columnAppendData.dataType = TYPE_CS_INT;
	mapColumnAppendData.push_back(columnAppendData);
	columnAppendData.columnName = latitude; 
	columnAppendData.dataType = TYPE_CS_INT;
	mapColumnAppendData.push_back(columnAppendData);


	std::string strSQL = p_SqlMaker->Sql_Stati_Cell_Grid(sql_area + columnResult_stati_grid.GetDBSearchSql(), gridtbname,
		timeSpanIntersect->istime, timeSpanIntersect->ietime, 
		cellSelect,
		areaSelectIntersect,
		tm_file);

	columnResult_stati_grid.SendColumnsIDToClient(CMD1_DIYSEARCH, CMD2_RESPONSE, bColumIDCmd);    


	columnResult_stati_grid.SearchSqlSendDataWithMyData(	   
		CMD1_DIYSEARCH,
		CMD2_RESPONSE,
		bThirdCmd,
		strSQL, 
		mapColumnAppendData);  

	return TRUE;
}

BOOL
CSearchArea_Cell_Grid::DealDataSub_Model_Nr(const int tbid, const std::string gridtbname, const std::string tm_file, const BYTE bColumIDCmd, const BYTE bThirdCmd)
{
	std::string columnid = "";
	CHECLFALSE(AddSearchColumn(tbid, std::string(m_column), columnid));

	CColumnResult columnResult_stati_grid;

	CHECLFALSE(columnResult_stati_grid.Init
	(columnid,
		CStdCommond::itostr(tbid),
		gridtbname,
		m_pDataBaseDeal_,
		m_pServerDeal_,
		m_pSendBuf_)
	);

	std::vector<STRU_ColumnAppendData> mapColumnAppendData;
	STRU_ColumnAppendData columnAppendData;
	std::string sql_area = " ilac,ici,itllongitude,itllatitude, ";

	columnAppendData.columnName = "ilac";
	columnAppendData.dataType = TYPE_CS_INT;
	mapColumnAppendData.push_back(columnAppendData);
	columnAppendData.columnName = "ici";
	columnAppendData.dataType = TYPE_CS_INT;
	mapColumnAppendData.push_back(columnAppendData);
	columnAppendData.columnName = longitude;
	columnAppendData.dataType = TYPE_CS_INT;
	mapColumnAppendData.push_back(columnAppendData);
	columnAppendData.columnName = latitude;
	columnAppendData.dataType = TYPE_CS_INT;
	mapColumnAppendData.push_back(columnAppendData);


	std::string strSQL = p_SqlMaker->Sql_Stati_Cell_Grid_Nr(sql_area + columnResult_stati_grid.GetDBSearchSql(), gridtbname,
		timeSpanIntersect->istime, timeSpanIntersect->ietime,
		nrcellSelect,
		areaSelectIntersect,
		tm_file);

	columnResult_stati_grid.SendColumnsIDToClient(CMD1_DIYSEARCH, CMD2_RESPONSE, bColumIDCmd);


	columnResult_stati_grid.SearchSqlSendDataWithMyData(
		CMD1_DIYSEARCH,
		CMD2_RESPONSE,
		bThirdCmd,
		strSQL,
		mapColumnAppendData);

	return TRUE;
}

BOOL 
CSearchArea_Cell_Grid::DealDataSub_Model_ScanGsm(const int tbid, const std::string gridtbname, const std::string tm_file,
                                                 const BYTE bColumIDCmd, const BYTE bThirdCmd)
{

	std::string columnid = "";
	CHECLFALSE(AddSearchColumn(tbid, std::string(m_column), columnid));


	CColumnResult columnResult_stati_grid;

	CHECLFALSE(   columnResult_stati_grid.Init
		( columnid,
		CStdCommond::itostr(tbid), 
		gridtbname,
		m_pDataBaseDeal_,
		m_pServerDeal_,
		m_pSendBuf_)
		);


	std::vector<STRU_ColumnAppendData> mapColumnAppendData;
	STRU_ColumnAppendData columnAppendData;
	std::string sql_area = " ibcch,ibsic,itllongitude,itllatitude,  ";

	columnAppendData.columnName = "ibcch"; 
	columnAppendData.dataType = TYPE_CS_INT;
	mapColumnAppendData.push_back(columnAppendData);
	columnAppendData.columnName = "ibsic";
	columnAppendData.dataType = TYPE_CS_INT;
	mapColumnAppendData.push_back(columnAppendData);
	columnAppendData.columnName = longitude;
	columnAppendData.dataType = TYPE_CS_INT;
	mapColumnAppendData.push_back(columnAppendData);
	columnAppendData.columnName = latitude;
	columnAppendData.dataType = TYPE_CS_INT;
	mapColumnAppendData.push_back(columnAppendData);

	std::string 	strSQL = p_SqlMaker->Sql_Stati_Cell2(sql_area + columnResult_stati_grid.GetDBSearchSql(), gridtbname,
		timeSpanIntersect->istime, timeSpanIntersect->ietime, 
		cellSelect,
		tm_file);

	columnResult_stati_grid.SendColumnsIDToClient(CMD1_DIYSEARCH, CMD2_RESPONSE, bColumIDCmd);    


	columnResult_stati_grid.SearchSqlSendDataWithMyData(	   
		CMD1_DIYSEARCH,
		CMD2_RESPONSE,
		bThirdCmd,
		strSQL, 
		mapColumnAppendData);  

	return TRUE;

}

BOOL 
CSearchArea_Cell_Grid::DealDataSub_Model_ScanTdscdma(const int tbid, const std::string gridtbname, const std::string tm_file, 
                                                     const BYTE bColumIDCmd, const BYTE bThirdCmd)
{
	std::string columnid = "";
	CHECLFALSE(AddSearchColumn(tbid, std::string(m_column), columnid));


	CColumnResult columnResult_stati_grid;

	CHECLFALSE(   columnResult_stati_grid.Init
		( columnid,
		CStdCommond::itostr(tbid), 
		gridtbname,
		m_pDataBaseDeal_,
		m_pServerDeal_,
		m_pSendBuf_)
		);

	std::vector<STRU_ColumnAppendData> mapColumnAppendData;
	STRU_ColumnAppendData columnAppendData;
	std::string sql_area = " ichannel,icpi,itllongitude,itllatitude,  ";

	columnAppendData.columnName = "ichannel";
	columnAppendData.dataType = TYPE_CS_INT;
	mapColumnAppendData.push_back(columnAppendData);
	columnAppendData.columnName = "icpi";
	columnAppendData.dataType = TYPE_CS_INT;
	mapColumnAppendData.push_back(columnAppendData);
	columnAppendData.columnName = longitude;
	columnAppendData.dataType = TYPE_CS_INT;
	mapColumnAppendData.push_back(columnAppendData);
	columnAppendData.columnName = latitude;
	columnAppendData.dataType = TYPE_CS_INT;
	mapColumnAppendData.push_back(columnAppendData);

	std::string 	strSQL = p_SqlMaker->Sql_Stati_Cell2_Td(sql_area + columnResult_stati_grid.GetDBSearchSql(), gridtbname,
		timeSpanIntersect->istime, timeSpanIntersect->ietime, 
		cellSelect,
		tm_file);

	columnResult_stati_grid.SendColumnsIDToClient(CMD1_DIYSEARCH, CMD2_RESPONSE, bColumIDCmd);    


	columnResult_stati_grid.SearchSqlSendDataWithMyData(	   
		CMD1_DIYSEARCH,
		CMD2_RESPONSE,
		bThirdCmd,
		strSQL, 
		mapColumnAppendData);  

	return TRUE;

}


BOOL
CSearchArea_Cell_Grid::ClearData(void)
{
	CSearchArea::ClearData();

	conditionMaker_Cell.ClearItem();
	m_map_fileItemList.clear();
	return TRUE;
}

BOOL
CSearchArea_Cell_Grid::AddSearchColumn(const int gridtbid, const std::string s_column, std::string& t_column)
{ 
	if (0 == (int)s_column.length())
	{
		return FALSE;
	}

	CColumnItem* p_FileidColumn = CColumnDef::Instance()->GetColumn(gridtbid, "image1");
	CHECLNULL(p_FileidColumn);

	t_column = CStdCommond::itostr(p_FileidColumn->imageID) + "," + CStdCommond::itostr(p_FileidColumn->paraID) + "," + 
		CStdCommond::itostr(p_FileidColumn->tbID) + "," + s_column;

	return TRUE;
}


