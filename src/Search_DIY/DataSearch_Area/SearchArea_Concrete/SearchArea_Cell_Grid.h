#pragma once
#include "./SearchArea_Sub.h"

#define	RESTYPE_DIY_CELL_COVER_GRID_GSM     		  0xa1

#define	RESTYPE_DIY_CELL_COVER_GRID_GPRS			  0xa5

#define	RESTYPE_DIY_CELL_COVER_GRID_TDSCDMA_AMR		  0xa9

#define	RESTYPE_DIY_CELL_COVER_GRID_TDSCDMA_PS		  0xab

#define	RESTYPE_DIY_CELL_COVER_GRID_TDSCDMA_VP		  0xad

#define	RESTYPE_DIY_CELL_COVER_GRID_WCDMA_AMR		  0xb0

#define	RESTYPE_DIY_CELL_COVER_GRID_WCDMA_PS		  0xb2

#define	RESTYPE_DIY_CELL_COVER_GRID_WCDMA_VP		  0xb4

#define	RESTYPE_DIY_CELL_COVER_GRID_WCDMA_PSHS		  0xb6

#define	RESTYPE_DIY_CELL_COVER_GRID_CDMA_V			  0xb8

#define	RESTYPE_DIY_CELL_COVER_GRID_CDMA_D			  0xba

#define	RESTYPE_DIY_CELL_COVER_GRID_CDMA2000_D		  0xbc

#define	RESTYPE_DIY_CELL_COVER_GRID_SCAN_GSM		  0xbe

#define	RESTYPE_DIY_CELL_COVER_GRID_SCAN_TDSCDMA      0xc4

#define RESTYPE_DIY_CELL_COVER_GRID_LET_AMR			  0xc8

#define	RESTYPE_DIY_CELL_COVER_GRID_SCAN_LTE_TOPN	  0xca

#define RESTYPE_DIY_CELL_COVER_GRID_LET_FDD_AMR	      0xcc

#define RESTYPE_DIY_CELL_COVER_GRID_LET_SIGNAL	      0xcd

#define	RESTYPE_DIY_CELL_COVER_GRID_SCAN_NBIOT_TOPN	  0xd1

#define	RESTYPE_DIY_CELL_COVER_GRID_NR	  0xd2

class CSearchArea_Cell_Grid : public CSearchArea_Sub
{
public:
	CSearchArea_Cell_Grid(void);
	~CSearchArea_Cell_Grid(void);

public:

protected:
	virtual BOOL FetchSearchCondiction(const BYTE* const pData, const int nCount,int& ioffset);

	virtual BOOL MakeLogFileGroup();
	virtual BOOL DealDataSub_GSM(int iservicetype, std::string tm_file, std::string tbname);
	virtual BOOL DealDataSub_CDMA(int iservicetype, std::string tm_file, std::string tbname);
	virtual BOOL DealDataSub_CDMA2000(int iservicetype, std::string tm_file, std::string tbname);
	virtual BOOL DealDataSub_TDSCDMA(int iservicetype, std::string tm_file, std::string tbname);
	virtual BOOL DealDataSub_WCDMA(int iservicetype, std::string tm_file, std::string tbname);
	virtual BOOL DealDataSub_Scan(int iservicetype, std::string tm_file, std::string tbname);
	virtual BOOL DealDataSub_WLAN(int iservicetype, std::string tm_file, std::string tbname) {return true;}
	virtual BOOL DealDataSub_LTE(int iservicetype, std::string tm_file, std::string tbname);
	virtual BOOL DealDataSub_LTE_FDD(int iservicetype, std::string tm_file, std::string tbname);
	virtual BOOL DealDataSub_Signal(int iservicetype, std::string tm_file, std::string tbname);
	virtual BOOL DealDataSub_NR(int iservicetype, std::string tm_file, std::string tbname);
	virtual BOOL DealDataSub_Model(const int gridtbid, const std::string gridtbname, const std::string tm_file, const BYTE bColumIDCmd, const BYTE bThirdCmd);
	virtual BOOL DealDataSub_Model_Nr(const int gridtbid, const std::string gridtbname, const std::string tm_file, const BYTE bColumIDCmd, const BYTE bThirdCmd);
	virtual BOOL DealDataSub_Model_ScanGsm(const int gridtbid, const std::string gridtbname, const std::string tm_file, const BYTE bColumIDCmd, const BYTE bThirdCmd);
  virtual BOOL DealDataSub_Model_ScanTdscdma(const int gridtbid, const std::string gridtbname, const std::string tm_file, const BYTE bColumIDCmd, const BYTE bThirdCmd);
    

	virtual BOOL ClearData(void);
	virtual BOOL AddSearchColumn(const int gridtbid, const std::string s_column, std::string& t_column);

protected:
	CSearchConditionMaker conditionMaker_Cell;

	CSearchConditionItem_AreaSelectIntersect* areaSelectIntersect;
	CSearchConditionItem_CellSelect* cellSelect;
	CSearchConditionItem_NrCellSelect* nrcellSelect;
};
