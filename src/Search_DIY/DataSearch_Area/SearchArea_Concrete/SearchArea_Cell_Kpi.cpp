#include "SearchArea_Cell_Kpi.h"

CSearchArea_Cell_Kpi::CSearchArea_Cell_Kpi(void)
{
}

CSearchArea_Cell_Kpi::~CSearchArea_Cell_Kpi(void)
{
}


BOOL
CSearchArea_Cell_Kpi::DealDataSub_GSM(int iservicetype, std::string tm_file, std::string tbname)
{
	std::string strSQL = "";

	switch (iservicetype)
	{
	case CEnumDef::Ser_GSM_VOICE:
	case CEnumDef::Ser_GSM_IDLE:
		{	
			DealDataSub_Model(CColumnDef::tb_model_gsm_stati_voice_cell, "tb_gsm_stati_voice_cell_" + 
				tbname, tm_file, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_CELL_COVER_GRID_GSM);
		}
		break;

	case CEnumDef::Ser_GPRS_DATA:
	case CEnumDef::Ser_EDGE_DATA:
		{	
			DealDataSub_Model(CColumnDef::tb_model_gsm_stati_data_cell, "tb_gsm_stati_data_cell_" + 
				tbname, tm_file, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_CELL_COVER_GRID_GPRS);	  
		}
		break;	 
	default:
		break;
	}
	return TRUE;
}

BOOL
CSearchArea_Cell_Kpi::DealDataSub_CDMA(int iservicetype, std::string tm_file, std::string tbname)
{
	std::string strSQL;

	switch(iservicetype)
	{
	case CEnumDef::Ser_CDMA_VOICE:
		{
			
		}
		break;

	case CEnumDef::Ser_CDMA1X_DATA:
		{

		}
	default:
		break;
	}
	return TRUE;
}

BOOL
CSearchArea_Cell_Kpi::DealDataSub_CDMA2000(int iservicetype, std::string tm_file, std::string tbname)
{ 
	std::string strSQL = "";

	switch(iservicetype)
	{
	case CEnumDef::Ser_CDMA2000_VOICE:
	case CEnumDef::Ser_CDMA2000_VIDEO:
		{		   
		}
		break;

	case CEnumDef::Ser_CDMA2000_DATA:
		{

		}
		break;
	default:
		break;
	}
	return TRUE;
}

BOOL
CSearchArea_Cell_Kpi::DealDataSub_TDSCDMA(int iservicetype, std::string tm_file, std::string tbname)
{
	std::string strSQL = "";

	switch(iservicetype)
	{
	case CEnumDef::Ser_TDSCDMA_VOICE:
	case CEnumDef::Ser_TDSCDMA_IDLE:
		{
			DealDataSub_Model(CColumnDef::tb_model_tdscdma_stati_amr_cell, "tb_tdscdma_stati_amr_cell_" + 
				tbname, tm_file, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_CELL_COVER_GRID_TDSCDMA_AMR);
		}
		break;

	case CEnumDef::Ser_TDSCDMA_VIDEO:
		{
			DealDataSub_Model(CColumnDef::tb_model_tdscdma_stati_vp_cell, "tb_tdscdma_stati_vp_cell_" + 
				tbname, tm_file, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_CELL_COVER_GRID_TDSCDMA_VP);	
		}
		break;

	case CEnumDef::Ser_TDSCDMA_DATA:
	case CEnumDef::Ser_TDSCDMA_HSDPA:
		{
			DealDataSub_Model(CColumnDef::tb_model_tdscdma_stati_ps_cell, "tb_tdscdma_stati_ps_cell_" + 
				tbname, tm_file, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_CELL_COVER_GRID_TDSCDMA_PS);	
		}
		break;
	default:
		break;
	}
	return TRUE;
}

BOOL
CSearchArea_Cell_Kpi::DealDataSub_LTE(int iservicetype, std::string tm_file, std::string tbname)
{
	if (CEnumDef::IsLteTddServiceType(iservicetype))
	{
		DealDataSub_Model(CColumnDef::tb_model_lte_stati_amr_cell, "tb_lte_stati_amr_cell_" +
			tbname, tm_file, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_CELL_COVER_GRID_LET_AMR);
	}

	return TRUE;
}

BOOL
CSearchArea_Cell_Kpi::DealDataSub_LTE_FDD(int iservicetype, std::string tm_file, std::string tbname)
{
	if (CEnumDef::IsLteFddServiceType(iservicetype))
	{
		DealDataSub_Model(CColumnDef::tb_model_lte_fdd_stati_amr_cell, "tb_lte_fdd_stati_amr_cell_" +
			tbname, tm_file, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_CELL_COVER_GRID_LET_FDD_AMR);
	}

	return TRUE;
}

BOOL
CSearchArea_Cell_Kpi::DealDataSub_Signal(int iservicetype, std::string tm_file, std::string tbname)
{
	std::string strSQL = "";

	switch(iservicetype)
	{
	case CEnumDef::SER_LTE_SIGNAL:
		{
			DealDataSub_Model(CColumnDef::tb_model_lte_signal_stati_cell, "tb_signal_stat_cell_image_hw_" +
				tbname, tm_file, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_CELL_COVER_GRID_LET_SIGNAL);	
		}
		break;
	default:
		break;
	}
	return TRUE;
}

BOOL
CSearchArea_Cell_Kpi::DealDataSub_WCDMA(int iservicetype, std::string tm_file, std::string tbname)
{
	std::string strSQL = "";

	switch (iservicetype)
	{
	case CEnumDef::Ser_WCDMA_VOICE:
	case CEnumDef::Ser_WCDMA_IDLE:
		{
			DealDataSub_Model(CColumnDef::tb_model_wcdma_stati_amr_cell, "tb_wcdma_stati_amr_cell_" + 
				tbname, tm_file, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_CELL_COVER_GRID_WCDMA_AMR);		
		}
		break;

	case CEnumDef::Ser_WCDMA_VIDEO:
		{
			DealDataSub_Model(CColumnDef::tb_model_wcdma_stati_vp_cell, "tb_wcdma_stati_vp_cell_" +
				tbname, tm_file, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_CELL_COVER_GRID_WCDMA_VP);	 	

		}
		break;

	case CEnumDef::Ser_WCDMA_DATA:
		{
			DealDataSub_Model(CColumnDef::tb_model_wcdma_stati_ps_cell, "tb_wcdma_stati_ps_cell_" + 
				tbname, tm_file, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_CELL_COVER_GRID_WCDMA_PS);	 	
		}
		break;

	case CEnumDef::Ser_WCDMA_HSDPA:
	case CEnumDef::Ser_WCDMA_HSUPA:
		{
			DealDataSub_Model(CColumnDef::tb_model_wcdma_stati_pshs_cell_psdpa, "tb_wcdma_stati_pshs_cell_" +
				tbname, tm_file, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_CELL_COVER_GRID_WCDMA_PSHS);
		}
		break;
	default:
		break;
	}
	return TRUE;
}

BOOL 
CSearchArea_Cell_Kpi::DealDataSub_NR(int iservicetype, std::string tm_file, std::string tbname)
{
	if (CEnumDef::IsNrServiceType(iservicetype))
	{
		DealDataSub_Model_Nr(CColumnDef::tb_nr_stati_amr_cell, "tb_nr_stati_amr_cell_" +
			tbname, tm_file, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_CELL_COVER_GRID_NR);
	}

	return TRUE;
}

BOOL 
CSearchArea_Cell_Kpi::DealDataSub_Model(const int tbid, const std::string tbname, const std::string tm_file, const BYTE bColumIDCmd, const BYTE bThirdCmd)
{
	std::string columnid = "";
	CHECLFALSE(AddSearchColumn(tbid, std::string(m_column), columnid));
	

    CColumnResult columnResult_stati_grid;

	CHECLFALSE(   columnResult_stati_grid.Init
				  ( columnid, 
					CStdCommond::itostr(tbid), 
					tbname,
					m_pDataBaseDeal_,
					m_pServerDeal_,
					m_pSendBuf_)
		);


   std::vector<STRU_ColumnAppendData> mapColumnAppendData;
   STRU_ColumnAppendData columnAppendData;
   std::string sql_area = " ilac, ici, ";  

   columnAppendData.columnName = "ilac"; 
   columnAppendData.dataType = TYPE_CS_INT;
   mapColumnAppendData.push_back(columnAppendData);
   columnAppendData.columnName = "ici"; 
   columnAppendData.dataType = TYPE_CS_INT;
   mapColumnAppendData.push_back(columnAppendData);


   std::string strSQL = p_SqlMaker->Sql_Stati_Cell(sql_area + columnResult_stati_grid.GetDBSearchSql(), tbname,
	   timeSpanIntersect->istime, timeSpanIntersect->ietime, 
	   cellSelect,
	   tm_file);

   columnResult_stati_grid.SendColumnsIDToClient(CMD1_DIYSEARCH, CMD2_RESPONSE, bColumIDCmd);    


   columnResult_stati_grid.SearchSqlSendDataWithMyData(	   
	   CMD1_DIYSEARCH,
	   CMD2_RESPONSE,
	   bThirdCmd,
	   strSQL, 
	   mapColumnAppendData);  

	return TRUE;
}


BOOL
CSearchArea_Cell_Kpi::DealDataSub_Model_Nr(const int tbid, const std::string tbname, const std::string tm_file, const BYTE bColumIDCmd, const BYTE bThirdCmd)
{
	std::string columnid = "";
	CHECLFALSE(AddSearchColumn(tbid, std::string(m_column), columnid));


	CColumnResult columnResult_stati_grid;

	CHECLFALSE(columnResult_stati_grid.Init
	(columnid,
		CStdCommond::itostr(tbid),
		tbname,
		m_pDataBaseDeal_,
		m_pServerDeal_,
		m_pSendBuf_)
	);


	std::vector<STRU_ColumnAppendData> mapColumnAppendData;
	STRU_ColumnAppendData columnAppendData;
	std::string sql_area = " ilac, ici, ";

	columnAppendData.columnName = "ilac";
	columnAppendData.dataType = TYPE_CS_INT;
	mapColumnAppendData.push_back(columnAppendData);
	columnAppendData.columnName = "ici";
	columnAppendData.dataType = TYPE_CS_INT;
	mapColumnAppendData.push_back(columnAppendData);


	std::string strSQL = p_SqlMaker->Sql_Stati_Cell_Nr(sql_area + columnResult_stati_grid.GetDBSearchSql(), tbname,
		timeSpanIntersect->istime, timeSpanIntersect->ietime,
		nrcellSelect,
		tm_file);

	columnResult_stati_grid.SendColumnsIDToClient(CMD1_DIYSEARCH, CMD2_RESPONSE, bColumIDCmd);


	columnResult_stati_grid.SearchSqlSendDataWithMyData(
		CMD1_DIYSEARCH,
		CMD2_RESPONSE,
		bThirdCmd,
		strSQL,
		mapColumnAppendData);

	return TRUE;
}


