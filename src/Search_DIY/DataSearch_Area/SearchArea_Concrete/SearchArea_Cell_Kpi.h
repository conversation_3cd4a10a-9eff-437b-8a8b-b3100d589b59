#pragma once
#include "./SearchArea_Cell_Grid.h"

class CSearchArea_Cell_Kpi : public CSearchArea_Cell_Grid
{
public:
	CSearchArea_Cell_Kpi(void);
public:
	~CSearchArea_Cell_Kpi(void);

protected:

	virtual BOOL DealDataSub_GSM(int iservicetype, std::string tm_file, std::string tbname);
	virtual BOOL DealDataSub_CDMA(int iservicetype, std::string tm_file, std::string tbname);
	virtual BOOL DealDataSub_CDMA2000(int iservicetype, std::string tm_file, std::string tbname);
	virtual BOOL DealDataSub_TDSCDMA(int iservicetype, std::string tm_file, std::string tbname);
	virtual BOOL DealDataSub_WCDMA(int iservicetype, std::string tm_file, std::string tbname);
	virtual BOOL DealDataSub_LTE(int iservicetype, std::string tm_file, std::string tbname);
	virtual BOOL DealDataSub_LTE_FDD(int iservicetype, std::string tm_file, std::string tbname);
	virtual BOOL DealDataSub_Signal(int iservicetype, std::string tm_file, std::string tbname);
	virtual BOOL DealDataSub_NR(int iservicetype, std::string tm_file, std::string tbname);
	virtual BOOL DealDataSub_Scan(int iservicetype, std::string tm_file, std::string tbname) {return true;}
	virtual BOOL DealDataSub_Model(const int gridtbid, const std::string gridtbname, const std::string ifileidSql, const BYTE bColumIDCmd, const BYTE bThirdCmd);
	virtual BOOL DealDataSub_Model_Nr(const int gridtbid, const std::string gridtbname, const std::string ifileidSql, const BYTE bColumIDCmd, const BYTE bThirdCmd);
};
