// CSearchArea_Event.cpp: implementation of the CSearchArea_Event class.
//
//////////////////////////////////////////////////////////////////////

#include "SearchArea_Event.h"
#include "./DtdrvApp_Public_Var.h"

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CSearchArea_Event::CSearchArea_Event()
{
}

CSearchArea_Event::~CSearchArea_Event()
{

}

BOOL
CSearchArea_Event::FetchSearchCondiction(const BYTE* const pData, const int nCount,int& ioffset)
{
	CHECLFALSE(CSearchArea::FetchSearchCondiction(pData, nCount, ioffset));

	CHECLFALSE(conditionMaker_Event.InitSerchCondition(pData, nCount, ioffset));

	PPM_OS::memset(&m_column, 0, sizeof(m_column));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,m_column,sizeof(m_column)));

	return TRUE;
}

BOOL 
CSearchArea_Event::MakeLogFileGroup()
{
	m_map_fileItemList.clear();

	for (auto p_DataItem = m_pDataRows_LogFile.begin(); p_DataItem != m_pDataRows_LogFile.end(); ++p_DataItem)
	{
		CDataItem_LogFile* p_logfile = (CDataItem_LogFile*)*p_DataItem;

		STRU_FILE_LIST2 tm1;

		tm1.testtype = p_logfile->itesttype;
		tm1.strlog = p_logfile->logtbname;
		tm1.strtb = p_logfile->streventtbname;	
		tm1.servicetype = p_logfile->iservicetype;

		m_map_fileItemList[tm1].push_back(p_logfile); 
	}


	return TRUE;
}


BOOL 
CSearchArea_Event::DealDataSub()
{
	char tm_file[8000];//ado对提交的sql语句限制为8000字节以内

	int bOnce;
	int cutnum = 600;         
	auto p_map = m_map_fileItemList.begin();   

	for (; p_map != m_map_fileItemList.end(); p_map++)   
	{	  
		auto p_vec = p_map->second.begin();	
		PPM_OS::memset(&tm_file, 0, sizeof(tm_file));	
		size_t allcount=1;	
		bOnce = 0;	

		for (int count=1; p_vec != p_map->second.end(); p_vec++, ++count, ++allcount)   
		{   
			if(0 == bOnce)
			{
				PPM_OS::snprintf(tm_file,sizeof(tm_file),"%d",(*p_vec)->ifileid); 
				bOnce = 1;
			}
			else
			{
				size_t orglen = PPM_OS::strlen(tm_file);
				PPM_OS::snprintf(tm_file + orglen, sizeof(tm_file) - orglen, ",%d", (*p_vec)->ifileid); 
			}	
			if((cutnum == count)
				|| (allcount == p_map->second.size()))
			{ 
				DealSearchAreaEvent((*p_vec)->iservicetype, p_map->first.strtb, tm_file);

				PPM_OS::memset(tm_file, 0, sizeof(tm_file));
				bOnce = 0;
				count = 0;
			} 
		}
	}

	return TRUE;
}

void CSearchArea_Event::DealSearchAreaEvent(int serviceid, std::string strtb, char* tm_file)
{
	if (CEnumDef::IsNrServiceType(serviceid))
	{
		DealDataSub_Model(CColumnDef::tb_model_event, strtb, tm_file, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREA_EVENT_NR);
	}
	else
	{
		DealDataSub_Model(CColumnDef::tb_model_event, strtb, tm_file, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREA_EVENT);
	}
}

BOOL
CSearchArea_Event::DealDataSub_Model(const int tbid, const std::string tbname, char* tm_file, const BYTE bColumIDCmd, const BYTE bThirdCmd)
{
	CColumnResult columnResult_tb_model_event;

	CHECLFALSE(columnResult_tb_model_event.Init
			(std::string(m_column),
			CStdCommond::itostr(tbid),
			tbname,
			m_pDataBaseDeal_,
			m_pServerDeal_,
			m_pSendBuf_)
		);

	std::string sql_conditionMaker_Event = conditionMaker_Event.GetSearchConfitionSql();
	if (0 == sql_conditionMaker_Event.length())
	{
		sql_conditionMaker_Event = " ifileid in (" + std::string(tm_file) + ")";
	}
	else
	{
		sql_conditionMaker_Event += " and ifileid in (" + std::string(tm_file) + ")";
	}

	std::string strSQL = "";
	strSQL = p_SqlMaker->Sql_Select(columnResult_tb_model_event.GetDBSearchSql(), tbname, sql_conditionMaker_Event);

	columnResult_tb_model_event.SendColumnsIDToClient(CMD1_DIYSEARCH, CMD2_RESPONSE, bColumIDCmd);

	columnResult_tb_model_event.SearchSqlSendData(
		CMD1_DIYSEARCH,
		CMD2_RESPONSE,
		bThirdCmd,
		strSQL);

	return TRUE;
}





