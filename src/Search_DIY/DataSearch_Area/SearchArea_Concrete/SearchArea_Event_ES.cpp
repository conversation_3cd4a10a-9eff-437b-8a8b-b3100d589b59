#include "SearchArea_Event_ES.h"

const std::string tb_es_event_result_ = "tb_es_event_result_";

CSearchArea_Event_ES::CSearchArea_Event_ES(void)
{
}

CSearchArea_Event_ES::~CSearchArea_Event_ES(void)
{
}


BOOL 
CSearchArea_Event_ES::DealDataSub()
{
	int bOnce;
	int cutnum = 600;         
	char tm_file[8000];//ado对提交的sql语句限制为8000字节以内
	auto p_map = m_map_fileItemList.begin();   

	for (; p_map != m_map_fileItemList.end(); p_map++)   
	{	  
		auto p_vec = p_map->second.begin();	
		PPM_OS::memset(&tm_file, 0, sizeof(tm_file));	
		bOnce = 0;	
		size_t allcount=1;	

		for (int count=1; p_vec != p_map->second.end(); p_vec++, ++count, ++allcount)   
		{  
			if(0 != bOnce)
			{
				size_t orglen = PPM_OS::strlen(tm_file);
				PPM_OS::snprintf(tm_file + orglen, sizeof(tm_file) - orglen, ",%d", (*p_vec)->ifileid); 
			}
			else
			{
				PPM_OS::snprintf(tm_file,sizeof(tm_file),"%d",(*p_vec)->ifileid); 
				bOnce = 1;
			}	

			if((allcount == p_map->second.size()) || (cutnum == count))
			{ 
				DealSearchAreaEventES(p_map->first.strtb, tm_file);

				PPM_OS::memset(tm_file, 0, sizeof(tm_file));
				count = 0;
				bOnce = 0;
			} 
		}
	}

	return TRUE;
}

void CSearchArea_Event_ES::DealSearchAreaEventES(std::string strtb, char* tm_file)
{
	std::string strSQL = "";
	CColumnResult columnResult_tb_model_event;

	if(columnResult_tb_model_event.Init(
		std::string(m_column),
		CStdCommond::itostr(CColumnDef::tb_es_event_result), 
		tb_es_event_result_ + strtb.substr(strtb.length()-4),
		m_pDataBaseDeal_,
		m_pServerDeal_,
		m_pSendBuf_	
		))
	{
		std::string sql_conditionMaker_Event = conditionMaker_Event.GetSearchConfitionSql();
		if (0 == sql_conditionMaker_Event.length())
		{
			sql_conditionMaker_Event = " ifileid in (" + std::string(tm_file) + ")";
		}
		else 
		{
			sql_conditionMaker_Event += " and ifileid in (" + std::string(tm_file) + ")";
		}

		strSQL = p_SqlMaker->Sql_Select(columnResult_tb_model_event.GetDBSearchSql(), tb_es_event_result_ + 
		    strtb.substr(strtb.length()-4), sql_conditionMaker_Event);

		columnResult_tb_model_event.SendColumnsIDToClient(CMD1_DIYSEARCH, CMD2_RESPONSE, RESTYPE_COLUMN_DEFINE);

		columnResult_tb_model_event.SearchSqlSendData(
			CMD1_DIYSEARCH,
			CMD2_RESPONSE,
			RESTYPE_DIY_AREA_EVENT_ES,
			strSQL);  
    }
}



