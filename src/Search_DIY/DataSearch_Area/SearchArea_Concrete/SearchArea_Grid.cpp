// SearchArea_Grid.cpp: implementation of the CSearchArea_Grid class.
//
//////////////////////////////////////////////////////////////////////

#include "SearchArea_Grid.h"

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

const std::string tb_tdscdma_stati_ps_grid_ = "tb_tdscdma_stati_ps_grid_";
const std::string tb_wcdma_stati_pshs_grid_ = "tb_wcdma_stati_pshs_grid_";

CSearchArea_Grid::CSearchArea_Grid()
{

}

CSearchArea_Grid::~CSearchArea_Grid()
{

}

BOOL
CSearchArea_Grid::FetchSearchCondiction(const BYTE* const pData, const int nCount,int& ioffset)
{
	PPM_DEBUG((LM_INFO, std::string("查询栅格,正在获取查询条件...").c_str()));

	CHECLFALSE(CSearchArea::FetchSearchCondiction(pData, nCount, ioffset));

	areaSelectIntersect = 
        (CSearchConditionItem_AreaSelectIntersect*)conditionMaker.GetSearchCondition(CSearchConditionMaker::SearchConditionType_AreaSelectIntersect);  

	PPM_DEBUG((LM_INFO, std::string("获取查询条件结束...").c_str()));

	PPM_OS::memset(&m_column, 0, sizeof(m_column));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,m_column,sizeof(m_column)));

	std::string strTemp(m_column);
	strTemp = "获取查询字段信息...:" + strTemp;
	PPM_DEBUG((LM_INFO, strTemp.c_str()));

	return TRUE;
}

BOOL 
CSearchArea_Grid::MakeLogFileGroup()
{
	for (auto p_DataItem = m_pDataRows_LogFile.begin(); p_DataItem != m_pDataRows_LogFile.end(); ++p_DataItem)
	{
		CDataItem_LogFile* p_logfile = (CDataItem_LogFile*)*p_DataItem;

		STRU_FILE_LIST2 tm1;
		tm1.testtype = p_logfile->itesttype;
		tm1.strlog = p_logfile->logtbname;		
		tm1.servicetype = p_logfile->iservicetype;
		tm1.strtb = p_logfile->suffix_week;	

		m_map_fileItemList[tm1].push_back(p_logfile); 
	}

	return TRUE;
}

BOOL
CSearchArea_Grid::DealDataSub_GSM(int iservicetype, std::string tm_file, std::string tbname)
{
	switch (iservicetype)
	{
	case CEnumDef::Ser_GSM_VOICE:
	case CEnumDef::Ser_GSM_IDLE:
		{	
      DealDataSub_Model(CColumnDef::tb_model_gsm_stati_voice_grid, "tb_gsm_stati_voice_grid_" + tbname,
				tm_file, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREA_COVER_GRID_GSM);
		}
		break;

	case CEnumDef::Ser_GPRS_DATA:
	case CEnumDef::Ser_EDGE_DATA:
		{	
			DealDataSub_Model(CColumnDef::tb_model_gsm_stati_data_grid, "tb_gsm_stati_data_grid_" + tbname,
				tm_file, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREA_COVER_GRID_GPRS);	  
		}
		break;	  

	case CEnumDef::Ser_GSM_UPLINK:
		{	
			PPM_DEBUG((LM_INFO, std::string("正在查询GSM_UPLINK。。。").c_str()));
			DealDataSub_Model(CColumnDef::tb_model_gsm_stati_mtr_grid, "tb_gsm_stati_mtr_grid_" + tbname,
				tm_file, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREA_COVER_GRID_GSM_MTR);	  
		}
		break;
	default:
		break;
	}
	return TRUE;
}

BOOL
CSearchArea_Grid::DealDataSub_CDMA(int iservicetype, std::string tm_file, std::string tbname)
{
	switch(iservicetype)
	{
	case CEnumDef::Ser_CDMA_VOICE:
	case CEnumDef::Ser_CDMA_IDLE:
		{
      DealDataSub_Model(CColumnDef::tb_model_cdma_stati_voice_grid, "tb_cdma_stati_voice_grid_" + tbname,
				tm_file, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREA_COVER_GRID_CDMA_V);
		}
		break;

	case CEnumDef::Ser_CDMA1X_DATA:
		{
      DealDataSub_Model(CColumnDef::tb_model_cdma_stati_d_grid, "tb_cdma_stati_data_grid_" + tbname,
				tm_file, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREA_COVER_GRID_CDMA_D);
		}
	default:
		break;
	}
	return TRUE;
}

BOOL
CSearchArea_Grid::DealDataSub_CDMA2000(int iservicetype, std::string tm_file, std::string tbname)
{ 
	switch(iservicetype)
	{
	case CEnumDef::Ser_CDMA2000_VOICE:
	case CEnumDef::Ser_CDMA2000_VIDEO:
		{	
          
		}
		break;

	case CEnumDef::Ser_CDMA2000_IDLE:
	case CEnumDef::Ser_CDMA2000_DATA:
		{
			DealDataSub_Model(CColumnDef::tb_model_cdma2000_stati_data_grid, "tb_cdma2000_stati_data_grid_" + tbname,
				tm_file, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREA_COVER_GRID_CDMA2000_D);
		}
		break;
	default:
		break;
	}
	return TRUE;
}

BOOL
CSearchArea_Grid::DealDataSub_TDSCDMA(int iservicetype, std::string tm_file, std::string tbname)
{
	switch(iservicetype)
	{
	case CEnumDef::Ser_TDSCDMA_VOICE:
	case CEnumDef::Ser_TDSCDMA_IDLE:
		{
      DealDataSub_Model(CColumnDef::tb_model_tdscdma_stati_amr_grid, "tb_tdscdma_stati_amr_grid_" + tbname,
			   tm_file, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREA_COVER_GRID_TDSCDMA_AMR);
		}
		break;

	case CEnumDef::Ser_TDSCDMA_VIDEO:
		{
      DealDataSub_Model(CColumnDef::tb_model_tdscdma_stati_vp_grid, "tb_tdscdma_stati_vp_grid_" + tbname,
			  tm_file, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREA_COVER_GRID_TDSCDMA_VP);	
		}
		break;

	case CEnumDef::Ser_TDSCDMA_DATA:
		{
      DealDataSub_Model(CColumnDef::tb_model_tdscdma_stati_ps_grid, tb_tdscdma_stati_ps_grid_ + tbname,
				tm_file, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREA_COVER_GRID_TDSCDMA_PS);	
		}
		break;
	case CEnumDef::Ser_TDSCDMA_HSDPA:
		{
			DealDataSub_Model(CColumnDef::tb_model_tdscdma_stati_ps_grid_hsdpa, tb_tdscdma_stati_ps_grid_ + tbname,
				tm_file, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREA_COVER_GRID_TDSCDMA_PS);	
		}
	default:
		break;
	}
	return TRUE;
}


BOOL
CSearchArea_Grid::DealDataSub_WCDMA(int iservicetype, std::string tm_file, std::string tbname)
{
	switch (iservicetype)
	{
	case CEnumDef::Ser_WCDMA_VOICE:
	case CEnumDef::Ser_WCDMA_IDLE:
		{
      DealDataSub_Model(CColumnDef::tb_model_wcdma_stati_amr_grid, "tb_wcdma_stati_amr_grid_" + tbname, 
			   tm_file, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREA_COVER_GRID_WCDMA_AMR);		
		}
		break;

	case CEnumDef::Ser_WCDMA_VIDEO:
		{
      DealDataSub_Model(CColumnDef::tb_model_wcdma_stati_vp_grid, "tb_wcdma_stati_vp_grid_" + tbname,
			   tm_file, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREA_COVER_GRID_WCDMA_VP);	 	

		}
		break;

	case CEnumDef::Ser_WCDMA_DATA:
		{
			DealDataSub_Model(CColumnDef::tb_model_wcdma_stati_ps_grid, "tb_wcdma_stati_ps_grid_" + tbname, 
				tm_file, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREA_COVER_GRID_WCDMA_PS);	 	
		}
        break;

	case CEnumDef::Ser_WCDMA_HSDPA:
		{
			DealDataSub_Model(CColumnDef::tb_model_wcdma_stati_pshs_grid_psdpa, tb_wcdma_stati_pshs_grid_ + tbname, 
				tm_file, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREA_COVER_GRID_WCDMA_PSHS);
		}
		break;

	case CEnumDef::Ser_WCDMA_HSUPA:
		{
            DealDataSub_Model(CColumnDef::tb_model_wcdma_stati_pshs_grid_psupa, tb_wcdma_stati_pshs_grid_ + tbname,
				tm_file, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREA_COVER_GRID_WCDMA_PSHS);
		}
		break;
	default:
		break;
	}
	return TRUE;
}


BOOL
CSearchArea_Grid::DealDataSub_Scan(int iservicetype, std::string tm_file, std::string tbname)
{
	switch (iservicetype)
	{
	case CEnumDef::Ser_SCAN:
		{
			DealDataSub_Model(CColumnDef::tb_scan_gsm_stati_grid, "tb_scan_gsm_stati_grid_" + tbname,
				tm_file, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREA_COVER_GRID_SCAN_GSM);		
		}
		break;

	case CEnumDef::Ser_SCAN_TD:
		{
      DealDataSub_Model(CColumnDef::tb_scan_tdscdma_stati_grid, "tb_scan_tdscdma_stati_grid_" + tbname,
				tm_file, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREA_COVER_GRID_SCAN_TDSCDMA);	
		}
		break;

	case CEnumDef::Ser_LTE_SCAN_TOPN:
		{
			DealDataSub_Model(CColumnDef::tb_scan_ltetopn_stati_grid, "tb_scan_ltetopn_stati_grid_" + tbname,
				tm_file, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREA_COVER_GRID_SCAN_LTE_TOPN);
		}
		break;

	case CEnumDef::Ser_NBIOT_SCAN_TOPN:
		{
			DealDataSub_Model(CColumnDef::tb_scan_nbiot_topn_stati_grid, "tb_scan_nbiot_topn_stati_grid_" + tbname,
				tm_file, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREA_COVER_GRID_SCAN_NBIOT_TOPN);
		}
		break;

	case CEnumDef::Ser_LTE_SCAN_FREQSPECTRUM:
		{
			DealDataSub_Model(CColumnDef::tb_scan_lte_freqspectrum_stati_grid,"tb_scan_lte_freqspectrum_stati_grid_"+ tbname,
				tm_file, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREA_COVER_GRID_LTE_FREQSPECTRUM);
		}
		break;

	case CEnumDef::Ser_SCAN_NR:
		{
			DealDataSub_Model(CColumnDef::tb_scan_nr_stati_grid, "tb_scan_nr_stati_grid_" + tbname,
				tm_file, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREA_COVER_GRID_SCAN_NR);
		}
		break;

	case CEnumDef::SER_SCAN_NRFREQ:
		{
			DealDataSub_Model(CColumnDef::tb_scan_nr_freqspectrum_stati_grid, "tb_scan_nr_freqspectrum_stati_grid_" + tbname,
				tm_file, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREA_COVER_GRID_NR_FREQSPECTRUM);
		}
		break;
	default:
		break;
	}
	return TRUE;
}

BOOL
CSearchArea_Grid::DealDataSub_LTE(int iservicetype, std::string tm_file, std::string tbname)
{
	if (CEnumDef::IsLteTddServiceType(iservicetype))
	{
		DealDataSub_Model(CColumnDef::tb_model_lte_stati_amr_grid, "tb_lte_stati_amr_grid_" + tbname,
			tm_file, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREA_COVER_GRID_LTE_AMR);
	}

	return TRUE;
}

BOOL
CSearchArea_Grid::DealDataSub_LTE_FDD(int iservicetype, std::string tm_file, std::string tbname)
{
	if (CEnumDef::IsLteFddServiceType(iservicetype))
	{
		DealDataSub_Model(CColumnDef::tb_model_lte_fdd_stati_amr_grid, "tb_lte_fdd_stati_amr_grid_" + tbname,
			tm_file, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREA_COVER_GRID_LTE_FDD_AMR);
	}

	return TRUE;
}

BOOL 
CSearchArea_Grid::DealDataSub_Signal(int iservicetype, std::string tm_file, std::string tbname)
{
	switch (iservicetype)
	{
	case CEnumDef::SER_LTE_SIGNAL:
		{
			DealDataSub_Model(CColumnDef::tb_model_lte_signal_stati_grid, "tb_signal_stat_grid_image_hw_" + tbname,
				tm_file, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREA_COVER_GRID_LTE_SIGNAL);		
		}
		break;
	default:
		break;
	}
	return TRUE;
}

BOOL 
CSearchArea_Grid::DealDataSub_NR(int iservicetype, std::string tm_file, std::string tbname)
{
	if (CEnumDef::IsNrServiceType(iservicetype))
	{
		DealDataSub_Model(CColumnDef::tb_nr_stati_amr_grid, "tb_nr_stati_amr_grid_" + tbname,
			tm_file, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREA_COVER_GRID_NR_AMR);
	}

	return TRUE;
}

BOOL 
CSearchArea_Grid::DealDataSub_Model(const int gridtbid, const std::string gridtbname, const std::string tm_file, const BYTE bColumIDCmd, const BYTE bThirdCmd)
{
	std::string columnid = "";
	CHECLFALSE(AddSearchColumn(gridtbid, std::string(m_column), columnid));

	CColumnResult columnResult_stati_grid;

    CHECLFALSE(columnResult_stati_grid.Init(columnid,CStdCommond::itostr(gridtbid), gridtbname,m_pDataBaseDeal_,m_pServerDeal_,m_pSendBuf_));


	std::vector<STRU_ColumnAppendData> mapColumnAppendData;
	BuildColumnAppendDataVector(mapColumnAppendData);

	std::string sql_area = " itllongitude, itllatitude, ";  

	std::string strSQL = "";
	STRU_Region_Rect stru_Rect;
	if (NULL != areaSelectIntersect)
	{
		stru_Rect.itllong = areaSelectIntersect->itllongitude;
		stru_Rect.itllat = areaSelectIntersect->itllatitude;
		stru_Rect.ibrlong = areaSelectIntersect->ibrlongitude;
		stru_Rect.ibrlai = areaSelectIntersect->ibrlatitude;

		strSQL = p_SqlMaker->Sql_Stati_Grid(sql_area + columnResult_stati_grid.GetDBSearchSql(), gridtbname,
			timeSpanIntersect->istime, timeSpanIntersect->ietime, 
      stru_Rect,
			tm_file);
	}
	else 
	{
		stru_Rect.itllong = 0;
		stru_Rect.itllat = 0;
		stru_Rect.ibrlong = 0;
		stru_Rect.ibrlai = 0;

		strSQL = p_SqlMaker->Sql_Stati_Grid(sql_area + columnResult_stati_grid.GetDBSearchSql(), gridtbname,
			timeSpanIntersect->istime, timeSpanIntersect->ietime, 
			stru_Rect,
			tm_file);            
	}

	columnResult_stati_grid.SendColumnsIDToClient(CMD1_DIYSEARCH, CMD2_RESPONSE, bColumIDCmd);    


	columnResult_stati_grid.SearchSqlSendDataWithMyData(	   
		CMD1_DIYSEARCH,
		CMD2_RESPONSE,
		bThirdCmd,
		strSQL, 
		mapColumnAppendData);  

	return TRUE;

}

void CSearchArea_Grid::BuildColumnAppendDataVector(std::vector<STRU_ColumnAppendData>& mapColumnAppendData)
{
	STRU_ColumnAppendData columnAppendData;
	columnAppendData.columnName = "itllongitude"; 
	columnAppendData.dataType = TYPE_CS_INT;
	mapColumnAppendData.push_back(columnAppendData);
	columnAppendData.columnName = "itllatitude";
	columnAppendData.dataType = TYPE_CS_INT;
	mapColumnAppendData.push_back(columnAppendData);
}

BOOL
CSearchArea_Grid::AddSearchColumn(const int gridtbid, const std::string s_column, std::string& t_column)
{ 
	if (0 == (int)s_column.length())
	{
		return FALSE;
	}

	CColumnItem* p_FileidColumn = CColumnDef::Instance()->GetColumn(gridtbid, "image1");
	CHECLNULL(p_FileidColumn);

	t_column = CStdCommond::itostr(p_FileidColumn->imageID) + "," + CStdCommond::itostr(p_FileidColumn->paraID) + "," 
		+ CStdCommond::itostr(p_FileidColumn->tbID) + "," + s_column;

	return TRUE;
}

BOOL
CSearchArea_Grid::ClearData(void)
{
	CSearchArea::ClearData();

    m_map_fileItemList.clear();
	return TRUE;
}





