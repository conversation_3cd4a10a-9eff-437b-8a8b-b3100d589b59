// SearchArea_Grid.h: interface for the CSearchArea_Grid class.
//
//////////////////////////////////////////////////////////////////////

#ifndef __SEARCHAREA_GRID_H__
#define __SEARCHAREA_GRID_H__

#include "./SearchArea_Sub.h"

#define	RESTYPE_DIY_AREA_COVER_GRID_GSM     		  0xa1

#define	RESTYPE_DIY_AREA_COVER_GRID_GPRS			  0xa5

#define	RESTYPE_DIY_AREA_COVER_GRID_TDSCDMA_AMR		  0xa9

#define	RESTYPE_DIY_AREA_COVER_GRID_TDSCDMA_PS		  0xab

#define	RESTYPE_DIY_AREA_COVER_GRID_TDSCDMA_VP		  0xad

#define	RESTYPE_DIY_AREA_COVER_GRID_WCDMA_AMR		  0xb0

#define	RESTYPE_DIY_AREA_COVER_GRID_WCDMA_PS		  0xb2

#define	RESTYPE_DIY_AREA_COVER_GRID_WCDMA_VP		  0xb4

#define	RESTYPE_DIY_AREA_COVER_GRID_WCDMA_PSHS		  0xb6

#define	RESTYPE_DIY_AREA_COVER_GRID_CDMA_V			  0xb8

#define	RESTYPE_DIY_AREA_COVER_GRID_CDMA_D			  0xba

#define	RESTYPE_DIY_AREA_COVER_GRID_CDMA2000_D		  0xbc

#define	RESTYPE_DIY_AREA_COVER_GRID_SCAN_GSM		  0xbe

#define	RESTYPE_DIY_AREA_COVER_GRID_SCAN_CDMA		  0xc0

#define	RESTYPE_DIY_AREA_COVER_GRID_SCAN_WCDMA		  0xc2

#define	RESTYPE_DIY_AREA_COVER_GRID_SCAN_TDSCDMA	  0xc4

#define	RESTYPE_DIY_AREA_COVER_GRID_GSM_MTR	          0xc6

#define RESTYPE_DIY_AREA_COVER_GRID_LTE_AMR           0xc8

#define	RESTYPE_DIY_AREA_COVER_GRID_SCAN_LTE_TOPN	  0xca

#define RESTYPE_DIY_AREA_COVER_GRID_LTE_FDD_AMR       0xcc

#define RESTYPE_DIY_AREA_COVER_GRID_LTE_SIGNAL        0xcd

#define RESTYPE_DIY_AREA_COVER_GRID_LTE_FREQSPECTRUM  0xce

#define	RESTYPE_DIY_AREA_COVER_GRID_SCAN_NBIOT_TOPN	  0xd0

#define RESTYPE_DIY_AREA_COVER_GRID_NR_AMR            0xd3

#define	RESTYPE_DIY_AREA_COVER_GRID_SCAN_NR	  0xd5

#define RESTYPE_DIY_AREA_COVER_GRID_NR_FREQSPECTRUM  0xd7

class CSearchArea_Grid : public CSearchArea_Sub
{
public:
	CSearchArea_Grid();
	virtual ~CSearchArea_Grid();

protected: 
	virtual BOOL FetchSearchCondiction(const BYTE* const pData, const int nCount,int& ioffset);

  virtual BOOL MakeLogFileGroup();
  virtual BOOL DealDataSub_GSM(int iservicetype, std::string tm_file, std::string tbname);
	virtual BOOL DealDataSub_CDMA(int iservicetype, std::string tm_file, std::string tbname);
	virtual BOOL DealDataSub_CDMA2000(int iservicetype, std::string tm_file, std::string tbname);
	virtual BOOL DealDataSub_TDSCDMA(int iservicetype, std::string tm_file, std::string tbname);
	virtual BOOL DealDataSub_WCDMA(int iservicetype, std::string tm_file, std::string tbname);
	virtual BOOL DealDataSub_Scan(int iservicetype, std::string tm_file, std::string tbname);
	virtual BOOL DealDataSub_WLAN(int iservicetype, std::string tm_file, std::string tbname) {return true;}
	virtual BOOL DealDataSub_LTE(int iservicetype, std::string tm_file, std::string tbname);
	virtual BOOL DealDataSub_LTE_FDD(int iservicetype, std::string tm_file, std::string tbname);
	virtual BOOL DealDataSub_Signal(int iservicetype, std::string tm_file, std::string tbname);
	virtual BOOL DealDataSub_NR(int iservicetype, std::string tm_file, std::string tbname);
	virtual BOOL DealDataSub_Model(const int gridtbid, const std::string gridtbname, const std::string ifileidSql, const BYTE bColumIDCmd, const BYTE bThirdCmd);
    
	virtual BOOL ClearData(void);
	BOOL AddSearchColumn(const int gridtbid, const std::string s_column, std::string& t_column);

protected:
	CSearchConditionItem_AreaSelectIntersect* areaSelectIntersect;

private:
	void BuildColumnAppendDataVector(std::vector<STRU_ColumnAppendData>& mapColumnAppendData);
};

#endif
