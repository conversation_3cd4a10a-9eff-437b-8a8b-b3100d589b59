
#include "SearchArea_Gsmr_Event.h"
#include "./DtdrvApp_Public_Var.h"

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CSearchArea_Gsmr_Event::CSearchArea_Gsmr_Event()
{
}

CSearchArea_Gsmr_Event::~CSearchArea_Gsmr_Event()
{

}

BOOL
CSearchArea_Gsmr_Event::FetchSearchCondiction(const BYTE* const pData, const int nCount, int& ioffset)
{
	CHECLFALSE(CSearchArea::FetchSearchCondiction(pData, nCount, ioffset));

	CHECLFALSE(conditionMaker_Event.InitSerchCondition(pData, nCount, ioffset));

	PPM_OS::memset(&m_column, 0, sizeof(m_column));
	//CHECLFALSE(CSearch_PI::GetTextValue_word(pData, nCount, ioffset, m_column, sizeof(m_column)));

	sprintf(m_column, "%s", "0,1,60,0,2,60,0,3,60,0,4,60,0,5,60,0,6,60,0,7,60,0,8,60,0,9,60,0,10,60,0,11,60,0,12,60,0,13,60,0,14,60,0,15,60,0,16,60,0,17,60,0,18,60,0,19,60,0,20,60,0,21,60,0,22,60,0,23,60,0,24,60,0,25,60,0,26,60,0,27,60,0,28,60,0,29,60,0,30,60,0,31,60,0,32,60,0,33,60,0,34,60,0,35,60,0,36,60");

	return TRUE;
}

BOOL
CSearchArea_Gsmr_Event::MakeLogFileGroup()
{
	m_map_fileItemList.clear();

	for (auto p_DataItem = m_pDataRows_LogFile.begin(); p_DataItem != m_pDataRows_LogFile.end(); ++p_DataItem)
	{
		CDataItem_LogFile* p_logfile = (CDataItem_LogFile*)*p_DataItem;

		STRU_FILE_LIST2 tm1;

		tm1.testtype = p_logfile->itesttype;
		tm1.strlog = p_logfile->logtbname;
		tm1.strtb = p_logfile->streventtbname;
		tm1.servicetype = p_logfile->iservicetype;

		m_map_fileItemList[tm1].push_back(p_logfile);
	}


	return TRUE;
}


BOOL
CSearchArea_Gsmr_Event::DealDataSub()
{
	char tm_file[8000];//ado对提交的sql语句限制为8000字节以内

	int bOnce;
	int cutnum = 600;
	auto p_map = m_map_fileItemList.begin();

	for (; p_map != m_map_fileItemList.end(); p_map++)
	{
		auto p_vec = p_map->second.begin();
		PPM_OS::memset(&tm_file, 0, sizeof(tm_file));
		size_t allcount = 1;
		bOnce = 0;

		for (int count = 1; p_vec != p_map->second.end(); p_vec++, ++count, ++allcount)
		{
			if (0 == bOnce)
			{
				PPM_OS::snprintf(tm_file, sizeof(tm_file), "%d", (*p_vec)->ifileid);
				bOnce = 1;
			}
			else
			{
				size_t orglen = PPM_OS::strlen(tm_file);
				PPM_OS::snprintf(tm_file + orglen, sizeof(tm_file) - orglen, ",%d", (*p_vec)->ifileid);
			}
			if ((cutnum == count)
				|| (allcount == p_map->second.size()))
			{
				DealSearchAreaEvent((*p_vec)->iservicetype, p_map->first.strtb, tm_file);

				PPM_OS::memset(tm_file, 0, sizeof(tm_file));
				bOnce = 0;
				count = 0;
			}
		}
	}

	return TRUE;
}

void CSearchArea_Gsmr_Event::DealSearchAreaEvent(int serviceid, std::string strtb, char* tm_file)
{
	DealDataSub_Model(CColumnDef::tb_gsmr_event, strtb, tm_file, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREA_EVENT);
}

BOOL
CSearchArea_Gsmr_Event::DealDataSub_Model(const int tbid, const std::string tbname, char* tm_file, const BYTE bColumIDCmd, const BYTE bThirdCmd)
{
	CColumnResult columnResult_tb_model_event;

	CHECLFALSE(columnResult_tb_model_event.Init
	(std::string(m_column),
		CStdCommond::itostr(tbid),
		std::string("tb_model_gsmr_event"),
		m_pDataBaseDeal_,
		m_pServerDeal_,
		m_pSendBuf_)
	);

	std::string sql_conditionMaker_Event = conditionMaker_Event.GetSearchConfitionSql();
	if (0 == sql_conditionMaker_Event.length())
	{
		sql_conditionMaker_Event = " a.ifileid in (" + std::string(tm_file) + ")";
	}
	else
	{
		sql_conditionMaker_Event += " and a.ifileid in (" + std::string(tm_file) + ")";
	}

	char cSql[8192]{ 0 };
	
	sprintf(cSql, "select a.ifileid, a.iprojecttype, a.iseqid, a.itime, a.wtimems, a.bms, a.ieventid, "
		"a.ilongitude, a.ilatitude, a.icqtposid, a.ilac, a.wrac, a.ici, a.itargetlac, a.wtargetrac, "
		"a.itargetci, a.ivalue1, a.ivalue2, a.ivalue3, a.ivalue4, a.ivalue5, a.ivalue6, a.ivalue7, a.ivalue8, a.ivalue9, a.ivalue10, "
		"b.机车号 AS trainnumber, b.车次号 AS trainlinenumber, b.线路 AS trainline, b.MSISDN AS msisdn, b.IMSI AS imsi, b.stime, b.起始小区 AS startcell, b.etime, b.结束小区 AS endcell, "
		"'K'+ CONVERT(varchar(10),FLOOR(CONVERT(numeric(18,3),c.K公里标)/1000)) + '+' + CONVERT(varchar(10),FLOOR(CONVERT(numeric(18,3),c.K公里标)%%1000)) AS kilometerpost "
		"from %s a left join tb_railway_InfoRelation b on a.ifileid = b.ifileid "
		"and dateadd(s, a.itime, '19700101 8:00') >= b.stime and dateadd(s, a.itime, '19700101 8:00') <= b.etime "
		"left join tb_cfg_railway_gsmr_cell c on a.iLAC = c.LAC and a.iCI = c.CI and CHARINDEX(b.线路, c.线路) > 0 "
		"where %s",
		tbname.c_str(), sql_conditionMaker_Event.c_str());

	columnResult_tb_model_event.SendGsmrEventColumnsIDToClient(CMD1_DIYSEARCH, CMD2_RESPONSE, bColumIDCmd);

	columnResult_tb_model_event.SearchSqlSendData(
		CMD1_DIYSEARCH,
		CMD2_RESPONSE,
		bThirdCmd,
		std::string(cSql));

	return TRUE;
}





