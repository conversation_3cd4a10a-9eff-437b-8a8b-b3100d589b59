#pragma once
#include "SearchArea.h"

#define	RESTYPE_DIY_AREA_EVENT			   0xa3
#define	RESTYPE_DIY_AREA_EVENT_NR		   0xa4

class CSearchArea_Gsmr_Event : public CSearchArea
{
public:
	CSearchArea_Gsmr_Event(void);
public:
	virtual ~CSearchArea_Gsmr_Event(void);

	CSearchConditionMaker conditionMaker_Event;

public:

	virtual BOOL FetchSearchCondiction(const BYTE* const pData, const int nCount, int& ioffset);
	virtual BOOL MakeLogFileGroup();
	virtual BOOL DealDataSub();

private:
	void DealSearchAreaEvent(int serviceid, std::string strtb, char* tm_file);
	BOOL DealDataSub_Model(const int tbid, const std::string tbname, char* tm_file, const BYTE bColumIDCmd, const BYTE bThirdCmd);

};



