#include "SearchArea_Log.h"

CSearchArea_Log::CSearchArea_Log(void)
{
}

CSearchArea_Log::~CSearchArea_Log(void)
{

}

BOOL
CSearchArea_Log::FetchSearchCondiction(const BYTE* const pData, const int nCount,int& ioffset)
{
	CHECLFALSE(CSearchArea::FetchSearchCondiction(pData, nCount, ioffset));

	PPM_OS::memset(&m_column, 0, sizeof(m_column));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,m_column,sizeof(m_column)));

	return TRUE;
}

BOOL
CSearchArea_Log::GetLogFile()
{
   CColumnResult columnResult_tb_log_file_time; 

   CHECLFALSE(   
	       columnResult_tb_log_file_time.Init
		   ( 
			   CColumnDef::tb_log_file_time, 
			   "tb_log_file_time",
			   m_pDataBaseDeal_,
			   m_pServerDeal_,
			   m_pSendBuf_
		   )
	   );

   std::string strSQL = p_SqlMaker->Sql_Select(columnResult_tb_log_file_time.GetDBSearchSql(), "tb_log_file_time", timeSpanIntersect->sql);
   columnResult_tb_log_file_time.SearchSqlGetData(strSQL, (CDataItem*)CDataItem_LogFileTime::Instance(), &m_pDataRows_LogFileTime);


   for (auto p_CDataItem = m_pDataRows_LogFileTime.begin(); p_CDataItem != m_pDataRows_LogFileTime.end(); ++p_CDataItem)
   {
	   CDataItem_LogFileTime* p_DataItem_LogFileTime = (CDataItem_LogFileTime*)*p_CDataItem;

       CColumnResult columnResult_tb_log_file;

	   if(FALSE == columnResult_tb_log_file.Init
				   (CColumnDef::tb_log_file, 
				   p_DataItem_LogFileTime->tbname,
				   m_pDataBaseDeal_,
				   m_pServerDeal_,
				   m_pSendBuf_)
		  )
	   {
           continue;
	   }

	   strSQL = p_SqlMaker->Sql_Select_File_Add_Status(columnResult_tb_log_file.GetDBSearchSql("a"),
		   p_DataItem_LogFileTime->tbname, conditionMaker.GetSearchConfitionSql("a"));

	   columnResult_tb_log_file.SendColumnsIDToClient(CMD1_DIYSEARCH, CMD2_RESPONSE, RESTYPE_COLUMN_FILE_INFO);

	   columnResult_tb_log_file.SearchSqlSendData(CMD1_DIYSEARCH,
		   CMD2_RESPONSE,
		   RESTYPE_DIY_FILE_INFO,
		   strSQL
		   ); 
   }

   return TRUE;

}

BOOL 
CSearchArea_Log::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	if (FALSE == FetchSearchCondiction(pData, nCount, ioffset) 
		|| FALSE == GetLogFile())
	{
		ClearData();

		CSearch_PI::FeedBackOver(CMD1_DIYSEARCH,RESTYPE_SEARCHERROR, m_pServerDeal_);
		return FALSE;
	} 

	ClearData(); 

	CSearch_PI::FeedBackOver(CMD1_DIYSEARCH,RESTYPE_SEARCHEND, m_pServerDeal_);
	return TRUE;
}

BOOL
CSearchArea_Log::ClearData(void)
{
   CSearchArea::ClearData(); 
   return TRUE;
}
