#include "SearchArea_NB_Grid.h"

CSearchArea_NB_Grid::CSearchArea_NB_Grid(void)
{
}

CSearchArea_NB_Grid::~CSearchArea_NB_Grid(void)
{
}

BOOL
CSearchArea_NB_Grid::FetchSearchCondiction(const BYTE* const pData, const int nCount,int& ioffset)
{
	PPM_DEBUG((LM_INFO, std::string("查询栅格,正在获取查询条件。。。").c_str()));

	CHECLFALSE(CSearchArea::FetchSearchCondiction(pData, nCount, ioffset));

	areaSelectIntersect = 
        (CSearchConditionItem_AreaSelectIntersect*)conditionMaker.GetSearchCondition(CSearchConditionMaker::SearchConditionType_AreaSelectIntersect);  

	PPM_DEBUG((LM_INFO, std::string("获取查询条件结束。。。").c_str()));

	PPM_OS::memset(&m_column, 0, sizeof(m_column));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,m_column,sizeof(m_column)));

	std::string strTemp(m_column);
	strTemp = "获取查询字段信息。。。:" + strTemp;
	PPM_DEBUG((LM_INFO, strTemp.c_str()));

	return TRUE;
}

BOOL 
CSearchArea_NB_Grid::MakeLogFileGroup()
{
	for (auto p_DataItem = m_pDataRows_LogFile.begin(); p_DataItem != m_pDataRows_LogFile.end(); ++p_DataItem)
	{
		CDataItem_LogFile* p_logfile = (CDataItem_LogFile*)*p_DataItem;

		STRU_FILE_LIST2 tm1;
		tm1.testtype = p_logfile->itesttype;
		tm1.strlog = p_logfile->logtbname;		
		tm1.servicetype = p_logfile->iservicetype;
		tm1.strtb = p_logfile->suffix_week;	

		m_map_fileItemList[tm1].push_back(p_logfile); 
	}

	return TRUE;
}

BOOL 
CSearchArea_NB_Grid::DealDataSub_Scan(int iservicetype, std::string tm_file, std::string tbname)
{
	std::string strSQL = "";

	switch (iservicetype)
	{
	case CEnumDef::Ser_NBIOT_SCAN_TOPN:
		{
			DealDataSub_Model(CColumnDef::tb_scan_nbiot_topn_stati_rams_grid, "tb_scan_nbiot_topn_stati_rams_grid_" + tbname, 
				tm_file, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREA_COVER_GRID_SCAN_NBIOT_TOPN);
		}
		break;

	default:
		break;

	}
	return TRUE;
}

BOOL 
CSearchArea_NB_Grid::DealDataSub_Model(const int gridtbid, const std::string gridtbname, const std::string tm_file, const BYTE bColumIDCmd, const BYTE bThirdCmd)
{
	std::string columnid = "";
	CHECLFALSE(AddSearchColumn(gridtbid, std::string(m_column), columnid));

	CColumnResult columnResult_stati_grid;

	CHECLFALSE(columnResult_stati_grid.Init(columnid, CStdCommond::itostr(gridtbid), 
               gridtbname,m_pDataBaseDeal_,m_pServerDeal_,m_pSendBuf_));


	std::vector<STRU_ColumnAppendData> mapColumnAppendData;
	std::string sql_area = " itllongitude, itllatitude, ";  

	BuildNBColumnAppendDataVector(mapColumnAppendData);

	std::string strSQL = "";
	STRU_Region_Rect stru_Rect;

	if (NULL != areaSelectIntersect)
	{
		stru_Rect.ibrlong = areaSelectIntersect->ibrlongitude;
		stru_Rect.ibrlai = areaSelectIntersect->ibrlatitude;
		stru_Rect.itllong = areaSelectIntersect->itllongitude;
		stru_Rect.itllat = areaSelectIntersect->itllatitude;

		strSQL = p_SqlMaker->Sql_Stati_Grid(sql_area + columnResult_stati_grid.GetDBSearchSql(), gridtbname,
			timeSpanIntersect->istime, timeSpanIntersect->ietime, 
            stru_Rect,
			tm_file);
	}
	else 
	{
		stru_Rect.ibrlong = 0;
		stru_Rect.ibrlai = 0;
		stru_Rect.itllong = 0;
		stru_Rect.itllat = 0;

		strSQL = p_SqlMaker->Sql_Stati_Grid(sql_area + columnResult_stati_grid.GetDBSearchSql(), gridtbname,
			timeSpanIntersect->istime, timeSpanIntersect->ietime, 
			stru_Rect,
			tm_file);            
	}

	columnResult_stati_grid.SendColumnsIDToClient(CMD1_DIYSEARCH, CMD2_RESPONSE, bColumIDCmd);    


	columnResult_stati_grid.SearchSqlSendDataWithMyData(	   
		CMD1_DIYSEARCH,
		CMD2_RESPONSE,
		bThirdCmd,
		strSQL, 
		mapColumnAppendData);  

	return TRUE;

}

void CSearchArea_NB_Grid::BuildNBColumnAppendDataVector(std::vector<STRU_ColumnAppendData>& mapColumnAppendData)
{
	STRU_ColumnAppendData columnAppendData;

	columnAppendData.columnName = "itllongitude"; 
	columnAppendData.dataType = TYPE_CS_INT;
	mapColumnAppendData.push_back(columnAppendData);
	columnAppendData.columnName = "itllatitude"; 
	columnAppendData.dataType = TYPE_CS_INT;
	mapColumnAppendData.push_back(columnAppendData);
}

BOOL
CSearchArea_NB_Grid::ClearData(void)
{
	CSearchArea::ClearData();

	m_map_fileItemList.clear();
	return TRUE;
}

BOOL
CSearchArea_NB_Grid::AddSearchColumn(const int gridtbid, const std::string s_column, std::string& t_column)
{ 
	if (0 == (int)s_column.length())
	{
		return FALSE;
	}

	CColumnItem* p_FileidColumn = CColumnDef::Instance()->GetColumn(gridtbid, "image1");
	CHECLNULL(p_FileidColumn);

	t_column = CStdCommond::itostr(p_FileidColumn->imageID) + "," + CStdCommond::itostr(p_FileidColumn->paraID) + "," +
		CStdCommond::itostr(p_FileidColumn->tbID) + "," + s_column;

	return TRUE;
}





