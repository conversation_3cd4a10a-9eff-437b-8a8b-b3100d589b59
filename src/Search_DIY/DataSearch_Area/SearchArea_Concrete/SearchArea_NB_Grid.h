// SearchArea_NB_Grid.h: interface for the CSearchArea_NB_Grid class.
//
//////////////////////////////////////////////////////////////////////

#ifndef __SEARCHAREA_NB_GRID_H__
#define __SEARCHAREA_NB_GRID_H__

#include "./SearchArea_Sub.h"

#define	RESTYPE_DIY_AREA_COVER_GRID_SCAN_NBIOT_TOPN	  0xd0

class CSearchArea_NB_Grid : public CSearchArea_Sub
{
public:
	CSearchArea_NB_Grid(void);
	virtual ~CSearchArea_NB_Grid(void);

protected: 
	virtual BOOL FetchSearchCondiction(const BYTE* const pData, const int nCount,int& ioffset);

	virtual BOOL MakeLogFileGroup();
	virtual BOOL DealDataSub_GSM(int iservicetype, std::string tm_file, std::string tbname){return true;}
	virtual BOOL DealDataSub_CDMA(int iservicetype, std::string tm_file, std::string tbname){return true;}
	virtual BOOL DealDataSub_CDMA2000(int iservicetype, std::string tm_file, std::string tbname){return true;}
	virtual BOOL DealDataSub_TDSCDMA(int iservicetype, std::string tm_file, std::string tbname){return true;}
	virtual BOOL DealDataSub_WCDMA(int iservicetype, std::string tm_file, std::string tbname){return true;}
	virtual BOOL DealDataSub_Scan(int iservicetype, std::string tm_file, std::string tbname);
	virtual BOOL DealDataSub_WLAN(int iservicetype, std::string tm_file, std::string tbname) {return true;}
	virtual BOOL DealDataSub_LTE(int iservicetype, std::string tm_file, std::string tbname){return true;}
	virtual BOOL DealDataSub_LTE_FDD(int iservicetype, std::string tm_file, std::string tbname){return true;}
	virtual BOOL DealDataSub_Signal(int iservicetype, std::string tm_file, std::string tbname){return true;}
	virtual BOOL DealDataSub_Model(const int gridtbid, const std::string gridtbname, const std::string ifileidSql, const BYTE bColumIDCmd, const BYTE bThirdCmd);

	virtual BOOL ClearData(void);
	BOOL AddSearchColumn(const int gridtbid, const std::string s_column, std::string& t_column);

protected:
	CSearchConditionItem_AreaSelectIntersect* areaSelectIntersect;

private:
	void BuildNBColumnAppendDataVector(std::vector<STRU_ColumnAppendData>& mapColumnAppendData);

};

#endif






