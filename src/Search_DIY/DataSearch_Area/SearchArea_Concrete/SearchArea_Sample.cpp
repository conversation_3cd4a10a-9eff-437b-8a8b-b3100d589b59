
// SearchArea_Sample.cpp: implementation of the SearchArea_Sample class.
//
//////////////////////////////////////////////////////////////////////

#include "SearchArea_Sample.h"

const std::string _sample6_ = "_sample6_";
const std::string tb_dttdscdma_sample_ = "tb_dttdscdma_sample_";
const std::string tb_dttdscdma_sample6_ = "tb_dttdscdma_sample6_";
const std::string tb_cqttdscdma_sample6_ = "tb_cqttdscdma_sample6_";
const std::string tb_dttdscdma_sample5_ = "tb_dttdscdma_sample5_";
const std::string tb_cqttdscdma_sample5_ = "tb_cqttdscdma_sample5_";

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CSearchArea_Sample::CSearchArea_Sample()
:m_mapSubScanPos(),
m_mapSubLtePos(),
m_mapSubNrPos()
{
	InitSubScanMap();
	InitSubLteMap();
	InitSubNrMap();
}

CSearchArea_Sample::~CSearchArea_Sample()
{
	m_mapSubScanPos.clear();
	m_mapSubLtePos.clear();
	m_mapSubNrPos.clear();
}

BOOL
CSearchArea_Sample::FetchSearchCondiction(const BYTE* const pData, const int nCount,int& ioffset)
{
	CHECLFALSE(CSearchArea::FetchSearchCondiction(pData, nCount, ioffset));

	CHECLFALSE(conditionMaker_Sample.InitSerchCondition(pData, nCount, ioffset));

	PPM_OS::memset(&m_column, 0, sizeof(m_column));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,m_column,sizeof(m_column)));

	std::string outputStr = "CSearchArea_Sample::FetchSearchCondiction: " + std::string(m_column);
	PPM_DEBUG((LM_INFO, outputStr.c_str()));

	return TRUE;
}

BOOL 
CSearchArea_Sample::MakeLogFileGroup()
{
	m_map_fileItemList.clear();

	for (auto p_DataItem = m_pDataRows_LogFile.begin(); p_DataItem != m_pDataRows_LogFile.end(); ++p_DataItem)
	{
		CDataItem_LogFile* p_logfile = (CDataItem_LogFile*)*p_DataItem;

		STRU_FILE_LIST2 tm1;

		tm1.testtype = p_logfile->itesttype;
		tm1.strlog = p_logfile->logtbname;
		tm1.strtb = p_logfile->strsampletbname;	
		tm1.servicetype = p_logfile->iservicetype;

		m_map_fileItemList[tm1].push_back(p_logfile); 
	}
	return TRUE;
}

BOOL 
CSearchArea_Sample::DealDataSub_GSM(int iservicetype, std::string tm_file, std::string tbname)
{
	std::string ifileidSql = " and ifileid in (" + std::string(tm_file) + ")";

	switch(iservicetype)
	{
	case CEnumDef::Ser_GSM_VOICE:
    case CEnumDef::Ser_GSM_IDLE:
		{
		    DealData_Service_GSM_Voice(tbname, ifileidSql);
		}
		break;

	case CEnumDef::Ser_GPRS_DATA:
	case CEnumDef::Ser_EDGE_DATA:
		{
            DealDataSub_Model(CColumnDef::tb_dtgsm_sample5, tbname, ifileidSql, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREA_COVER_SAMPLE_GPRS);
		}
		break;

	default:
		{
			return FALSE;
		}
	}

	return TRUE;
}

void CSearchArea_Sample::DealData_Service_GSM_Voice(std::string tbname, std::string ifileidSql)
{
	//dt
	if ((tbname.find("_autodtgsm_") != std::string::npos) && (tbname.find(_sample6_) != std::string::npos))
	{
		DealDataSub_Model(CColumnDef::tb_dtgsm_sample6, tbname, ifileidSql, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREA_COVER_SAMPLE_GSMV);
	}
	else if ((tbname.find("_dtgsm_") != std::string::npos) && (tbname.find(_sample6_) != std::string::npos))
	{
		DealDataSub_Model(CColumnDef::tb_dtgsm_sample6, tbname, ifileidSql, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREA_COVER_SAMPLE_GSMV);
	}
	//cqt
	else if ((tbname.find("_autocqtgsm_") != std::string::npos) && (tbname.find(_sample6_) != std::string::npos))
	{
		DealDataSub_Model(CColumnDef::tb_cqtgsm_sample6, tbname, ifileidSql, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREA_COVER_SAMPLE_GSMV);
	}
	else if ((tbname.find("_cqtgsm_") != std::string::npos) && (tbname.find(_sample6_) != std::string::npos))
	{
		DealDataSub_Model(CColumnDef::tb_cqtgsm_sample6, tbname, ifileidSql, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREA_COVER_SAMPLE_GSMV);
	}
}

BOOL 
CSearchArea_Sample::DealDataSub_Signal(int iservicetype, std::string tm_file, std::string tbname)
{
	std::string ifileidSql = " and ifileid in (" + std::string(tm_file) + ")";

	switch(iservicetype)
	{
	case CEnumDef::SER_LTE_SIGNAL:
		{
			//dt
			if (tbname.find("_dtsignal_") != std::string::npos)
			{
                DealDataSub_Model(CColumnDef::tb_dtsignal_sample, tbname, ifileidSql, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREA_COVER_SAMPLE_SIGNAL);
			}
			//cqt
			else if (tbname.find("_cqtsignal_") != std::string::npos)
			{
                DealDataSub_Model(CColumnDef::tb_cqtsignal_sample, tbname, ifileidSql, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREA_COVER_SAMPLE_SIGNAL);
			}
		}
		break;

	default:
		{
			return FALSE;
		}
	}

	return TRUE;
}

BOOL 
CSearchArea_Sample::DealDataSub_CDMA(int iservicetype, std::string tm_file, std::string tbname)
{
	std::string ifileidSql = " and ifileid in (" + std::string(tm_file) + ")";

	switch(iservicetype)
	{
	case CEnumDef::Ser_CDMA_VOICE:
	case CEnumDef::Ser_CDMA_IDLE:
		{
            DealDataSub_Model(CColumnDef::tb_dtcdma_sample, tbname, ifileidSql, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREA_COVER_SAMPLE_CDMA_V);
		}
		break;

	case CEnumDef::Ser_CDMA1X_DATA:
		{
            DealDataSub_Model(CColumnDef::tb_dtcdma_sample5, tbname, ifileidSql, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREA_COVER_SAMPLE_CDMA_D);
		}
		break;

	default:
		{
			return FALSE;
		}
	}

	return TRUE;
}


BOOL 
CSearchArea_Sample::DealDataSub_WCDMA(int iservicetype, std::string tm_file, std::string tbname)
{
	std::string ifileidSql = " and ifileid in (" + std::string(tm_file) + ")";

	switch(iservicetype)
	{
	case CEnumDef::Ser_WCDMA_VOICE:
	case CEnumDef::Ser_WCDMA_VIDEO:
	case CEnumDef::Ser_WCDMA_IDLE:
		{
            DealDataSub_Model(CColumnDef::tb_dtwcdma_sample, tbname, ifileidSql, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREA_COVER_SAMPLE_WCDMA_V);
		}
		break;

	case CEnumDef::Ser_WCDMA_DATA:
	case CEnumDef::Ser_WCDMA_HSDPA:
	case CEnumDef::Ser_WCDMA_HSUPA:
		{
            DealDataSub_Model(CColumnDef::tb_dtwcdma_sample5, tbname, ifileidSql, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREA_COVER_SAMPLE_WCDMA_D);
		}
		break;

	default:
		{
			return FALSE;
		}
	}

	return TRUE;
}

BOOL 
CSearchArea_Sample::DealDataSub_TDSCDMA(int iservicetype, std::string tm_file, std::string tbname)
{
    std::string ifileidSql = " and ifileid in (" + std::string(tm_file) + ")";

	switch(iservicetype)
	{
	case CEnumDef::Ser_TDSCDMA_VOICE:
	case CEnumDef::Ser_TDSCDMA_VIDEO:
	case CEnumDef::Ser_TDSCDMA_IDLE:
		{
		    DealData_Service_TDSCDMA_Voice(tbname, ifileidSql);
		}
		break;

	case CEnumDef::Ser_TDSCDMA_DATA:
	case CEnumDef::Ser_TDSCDMA_HSDPA:
		{
		    DealData_Service_TDSCDMA_Data(tbname, ifileidSql);
		}
		break;

	default:
		{
			return FALSE;
		}

	}

	return TRUE;

}

void CSearchArea_Sample::DealData_Service_TDSCDMA_Voice(std::string tbname, std::string ifileidSql)
{
	if (tbname.substr(0, tb_dttdscdma_sample_.length() - 1) == tb_dttdscdma_sample_)
	{
		DealDataSub_Model(CColumnDef::tb_dttdscdma_sample, tbname, ifileidSql, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREA_COVER_SAMPLE_TDSCDMA_V);
	}
	else if (tbname.substr(0, tb_dttdscdma_sample6_.length() - 1) == tb_dttdscdma_sample6_)
	{
		DealDataSub_Model(CColumnDef::tb_dttdscdma_sample6, tbname, ifileidSql, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREA_COVER_SAMPLE_TDSCDMA_V);
	}
	else if (tbname.substr(0, tb_cqttdscdma_sample6_.length() - 1) == tb_cqttdscdma_sample6_)
	{
		DealDataSub_Model(CColumnDef::tb_cqttdscdma_sample6, tbname, ifileidSql, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREA_COVER_SAMPLE_TDSCDMA_V);
	}
}

void CSearchArea_Sample::DealData_Service_TDSCDMA_Data(std::string tbname, std::string ifileidSql)
{
	if (tbname.substr(0, tb_dttdscdma_sample5_.length() - 1) == tb_dttdscdma_sample5_)
	{
		DealDataSub_Model(CColumnDef::tb_dttdscdma_sample5, tbname, ifileidSql, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREA_COVER_SAMPLE_TDSCDMA_D);
	}
	else if (tbname.substr(0, tb_cqttdscdma_sample5_.length() - 1) == tb_cqttdscdma_sample5_)
	{
		DealDataSub_Model(CColumnDef::tb_cqttdscdma_sample5, tbname, ifileidSql, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREA_COVER_SAMPLE_TDSCDMA_D);
	}
}

BOOL 
CSearchArea_Sample::DealDataSub_CDMA2000(int iservicetype, std::string tm_file, std::string tbname)
{
	std::string ifileidSql = " and ifileid in (" + std::string(tm_file) + ")";

	switch(iservicetype)
	{
	case CEnumDef::Ser_CDMA2000_VOICE:
	case CEnumDef::Ser_CDMA2000_VIDEO:
		{
            DealDataSub_Model(CColumnDef::tb_dtevdo_sample, tbname, ifileidSql, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREA_COVER_SAMPLE_CDMA2000_V);
		}
		break;

	case CEnumDef::Ser_CDMA2000_DATA:
		{
            DealDataSub_Model(CColumnDef::tb_dtevdo_sample5, tbname, ifileidSql, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREA_COVER_SAMPLE_CDMA2000_D);
		}
		break;

	default:
		{
			return FALSE;
		}
	}

	return TRUE;
}

BOOL 
CSearchArea_Sample::DealDataSub_Scan(int iservicetype, std::string tm_file, std::string tbname)
{
	std::string ifileidSql = " and ifileid in (" + std::string(tm_file) + ")";

	std::map<int, DealScanData>::iterator it;
	it = m_mapSubScanPos.find(iservicetype);

	if (it != m_mapSubScanPos.end())
	{
		(this->*(it->second))(tbname, ifileidSql);
	}
	else
	{
		return FALSE;
	}

	return TRUE;
}


void CSearchArea_Sample::DealDataSub_Ser_SCAN(std::string tbname, std::string ifileidSql)
{
    std::string tbnameTemp = "tb_scan_sample2_" + tbname.substr(tbname.length() - 8, 8);
    DealDataSub_Model(CColumnDef::tb_scan_sample2, tbnameTemp, ifileidSql, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREA_COVER_SAMPLE_SCAN_GSM);
}

void CSearchArea_Sample::DealDataSub_Ser_SCAN_TD(std::string tbname, std::string ifileidSql)
{
    DealDataSub_Model(CColumnDef::tb_scan_td_sample, tbname, ifileidSql, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREA_COVER_SAMPLE_SCAN_TD);
}
void CSearchArea_Sample::DealDataSub_Ser_SCAN_WCDMA(std::string tbname, std::string ifileidSql)
{
    DealDataSub_Model(CColumnDef::tb_scan_w_sample, tbname, ifileidSql, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREA_COVER_SAMPLE_SCAN_W);
}
void CSearchArea_Sample::DealDataSub_Ser_LTE_SCAN_TOPN(std::string tbname, std::string ifileidSql)
{
    DealDataSub_Model(CColumnDef::tb_scan_lte_sample, tbname, ifileidSql, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREA_COVER_SAMPLE_SCAN_LTE);
}
void CSearchArea_Sample::DealDataSub_Ser_NBIOT_SCAN_TOPN(std::string tbname, std::string ifileidSql)
{
    DealDataSub_Model(CColumnDef::tb_scan_nbiot_topn_sample, tbname, ifileidSql, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREA_COVER_SAMPLE_SCAN_NBIOT_TOPN);
}
void CSearchArea_Sample::DealDataSub_Ser_NR_SCAN(std::string tbname, std::string ifileidSql)
{
	DealDataSub_Model(CColumnDef::tb_scan_nr_sample, tbname, ifileidSql, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREA_COVER_SAMPLE_SCAN_NR);
}
void CSearchArea_Sample::DealDataSub_Ser_LTE_SCAN_CW(std::string tbname, std::string ifileidSql)
{
    DealDataSub_Model(CColumnDef::tb_scan_cw_measure_sample, tbname, ifileidSql, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREA_COVER_SAMPLE_SCAN_CW);
}
void CSearchArea_Sample::DealDataSub_Ser_GSM_SCAN_FREQSPECTRUM(std::string tbname, std::string ifileidSql)
{
    DealDataSub_Model(CColumnDef::tb_scan_FreqSpectrum_sample, tbname, ifileidSql,
        RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREA_COVER_SAMPLE_SCAN_FREQSPECTURM);
}

void CSearchArea_Sample::InitSubScanMap(void)
{
    m_mapSubScanPos.insert(make_pair(CEnumDef::Ser_SCAN, &CSearchArea_Sample::DealDataSub_Ser_SCAN));
    m_mapSubScanPos.insert(make_pair(CEnumDef::Ser_SCAN_TD, &CSearchArea_Sample::DealDataSub_Ser_SCAN_TD));
    m_mapSubScanPos.insert(make_pair(CEnumDef::Ser_SCAN_WCDMA, &CSearchArea_Sample::DealDataSub_Ser_SCAN_WCDMA));
    m_mapSubScanPos.insert(make_pair(CEnumDef::Ser_LTE_SCAN_TOPN, &CSearchArea_Sample::DealDataSub_Ser_LTE_SCAN_TOPN));
    m_mapSubScanPos.insert(make_pair(CEnumDef::Ser_NBIOT_SCAN_TOPN, &CSearchArea_Sample::DealDataSub_Ser_NBIOT_SCAN_TOPN));
    m_mapSubScanPos.insert(make_pair(CEnumDef::Ser_LTE_SCAN_CW, &CSearchArea_Sample::DealDataSub_Ser_LTE_SCAN_CW));
	m_mapSubScanPos.insert(make_pair(CEnumDef::Ser_SCAN_NR, &CSearchArea_Sample::DealDataSub_Ser_NR_SCAN));

	m_mapSubScanPos.insert(make_pair(CEnumDef::SER_SCAN_NRFREQ, &CSearchArea_Sample::DealDataSub_Ser_GSM_SCAN_FREQSPECTRUM));
    m_mapSubScanPos.insert(make_pair(CEnumDef::Ser_LTE_SCAN_FREQSPECTRUM, &CSearchArea_Sample::DealDataSub_Ser_GSM_SCAN_FREQSPECTRUM));
    m_mapSubScanPos.insert(make_pair(CEnumDef::Ser_TD_SCAN_FREQSPECTRUM, &CSearchArea_Sample::DealDataSub_Ser_GSM_SCAN_FREQSPECTRUM));
    m_mapSubScanPos.insert(make_pair(CEnumDef::Ser_GSM_SCAN_FREQSPECTRUM, &CSearchArea_Sample::DealDataSub_Ser_GSM_SCAN_FREQSPECTRUM));

}

BOOL 
CSearchArea_Sample::DealDataSub_WLAN(int iservicetype, std::string tm_file, std::string tbname)
{
	std::string ifileidSql = " and ifileid in (" + std::string(tm_file) + ")";

	switch(iservicetype)
	{
	case CEnumDef::Ser_WLAN:
		{
            DealDataSub_Model(CColumnDef::tb_dtwlan_sample, tbname, ifileidSql, RESTYPE_COLUMN_AREA_COVER_SAMPLE_WLAN, RESTYPE_DIY_AREA_COVER_SAMPLE_WLAN);
		}
		break;

	default:
		{
			return FALSE;
		}
	}

	return TRUE;
}

BOOL 
CSearchArea_Sample::DealDataSub_LTE(int iservicetype, std::string tm_file, std::string tbname)
{
	std::string ifileidSql = " and ifileid in (" + std::string(tm_file) + ")";

	std::map<int, DealScanData>::iterator it;
	it = m_mapSubLtePos.find(iservicetype);

	if (it != m_mapSubLtePos.end())
	{
		(this->*(it->second))(tbname, ifileidSql);
	}
	else
	{
		return FALSE;
	}

	return TRUE;
}

void CSearchArea_Sample::DealDataSub_Dt_Cqt_LTE(std::string tbname, std::string ifileidSql)
{
	if (tbname.find("_dtlte_") != std::string::npos)
	{
		DealDataSub_Model(CColumnDef::tb_dtlte_sample, tbname, ifileidSql, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREA_COVER_SAMPLE_LTE);
	}
	else if (tbname.find("_cqtlte_") != std::string::npos)
	{
		DealDataSub_Model(CColumnDef::tb_cqtlte_sample, tbname, ifileidSql, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREA_COVER_SAMPLE_LTE);
	}
}

void CSearchArea_Sample::InitSubLteMap(void)
{
	m_mapSubLtePos.insert(make_pair(CEnumDef::Ser_LTE_VOICE, &CSearchArea_Sample::DealDataSub_Dt_Cqt_LTE));
	m_mapSubLtePos.insert(make_pair(CEnumDef::Ser_LTE_DATA, &CSearchArea_Sample::DealDataSub_Dt_Cqt_LTE));
	m_mapSubLtePos.insert(make_pair(CEnumDef::Ser_TDD_LTE_IDLE, &CSearchArea_Sample::DealDataSub_Dt_Cqt_LTE));
	m_mapSubLtePos.insert(make_pair(CEnumDef::Ser_TDD_LTE_MULTI, &CSearchArea_Sample::DealDataSub_Dt_Cqt_LTE));
	m_mapSubLtePos.insert(make_pair(CEnumDef::Ser_TDD_LTE_VOLTE, &CSearchArea_Sample::DealDataSub_Dt_Cqt_LTE));
	m_mapSubLtePos.insert(make_pair(CEnumDef::Ser_TDD_LTE_VIDEO_VOLTE, &CSearchArea_Sample::DealDataSub_Dt_Cqt_LTE));
	m_mapSubLtePos.insert(make_pair(CEnumDef::Ser_NBIOT_DATA, &CSearchArea_Sample::DealDataSub_Dt_Cqt_LTE));
}

BOOL 
CSearchArea_Sample::DealDataSub_LTE_UEP(int iservicetype, std::string tm_file, std::string tbname)
{
	std::string ifileidSql = " and ifileid in (" + std::string(tm_file) + ")";

	switch(iservicetype)
	{
	case CEnumDef::Ser_LTE_TDD_UEP:
		{
            DealDataSub_Model(CColumnDef::tb_lte_uep_sample, tbname, ifileidSql, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREA_COVER_SAMPLE_LTE_UEP);
		}
		break;

	default:
		{
			return FALSE;
		}
	}

	return TRUE;
}

BOOL 
CSearchArea_Sample::DealDataSub_LTE_FDD(int iservicetype, std::string tm_file, std::string tbname)
{
	std::string ifileidSql = " and ifileid in (" + std::string(tm_file) + ")";

	if (CEnumDef::IsLteFddServiceType(iservicetype))
	{
		if (tbname.find("_dtlte_fdd_") != std::string::npos)
		{
			DealDataSub_Model(CColumnDef::tb_dtlte_fdd_sample, tbname, ifileidSql, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREA_COVER_SAMPLE_LTE_FDD);
		}
		else if (tbname.find("_cqtlte_fdd_") != std::string::npos)
		{
			DealDataSub_Model(CColumnDef::tb_cqtlte_fdd_sample, tbname, ifileidSql, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREA_COVER_SAMPLE_LTE_FDD);
		}

		return TRUE;
	}

	return FALSE;
}

BOOL CSearchArea_Sample::DealDataSub_NR(int iservicetype, std::string tm_file, std::string tbname)
{
	std::string ifileidSql = " and ifileid in (" + std::string(tm_file) + ")";
	std::map<int, DealScanData>::iterator it;
	it = m_mapSubNrPos.find(iservicetype);

	if (it != m_mapSubNrPos.end())
	{
		(this->*(it->second))(tbname, ifileidSql);
	}
	else
	{
		return FALSE;
	}

	return TRUE;
}

void CSearchArea_Sample::InitSubNrMap(void)
{
	m_mapSubNrPos.insert(make_pair(CEnumDef::Ser_NR_NSA_TDD_IDLE, &CSearchArea_Sample::DealDataSub_Dt_Cqt_NR));
	m_mapSubNrPos.insert(make_pair(CEnumDef::Ser_NR_NSA_TDD_DATA, &CSearchArea_Sample::DealDataSub_Dt_Cqt_NR));
	m_mapSubNrPos.insert(make_pair(CEnumDef::Ser_NR_NSA_TDD_VOLTE, &CSearchArea_Sample::DealDataSub_Dt_Cqt_NR));
	m_mapSubNrPos.insert(make_pair(CEnumDef::Ser_NR_SA_TDD_IDLE, &CSearchArea_Sample::DealDataSub_Dt_Cqt_NR));
	m_mapSubNrPos.insert(make_pair(CEnumDef::Ser_NR_SA_TDD_DATA, &CSearchArea_Sample::DealDataSub_Dt_Cqt_NR));
	m_mapSubNrPos.insert(make_pair(CEnumDef::Ser_NR_SA_TDD_VOLTE, &CSearchArea_Sample::DealDataSub_Dt_Cqt_NR));
	m_mapSubNrPos.insert(make_pair(CEnumDef::Ser_NR_DM_TDD_IDLE, &CSearchArea_Sample::DealDataSub_Dt_Cqt_NR));
	m_mapSubNrPos.insert(make_pair(CEnumDef::Ser_NR_DM_TDD_DATA, &CSearchArea_Sample::DealDataSub_Dt_Cqt_NR));
	m_mapSubNrPos.insert(make_pair(CEnumDef::Ser_NR_DM_TDD_VOLTE, &CSearchArea_Sample::DealDataSub_Dt_Cqt_NR));
	m_mapSubNrPos.insert(make_pair(CEnumDef::Ser_NR_SA_TDD_EPSFB, &CSearchArea_Sample::DealDataSub_Dt_Cqt_NR));
	m_mapSubNrPos.insert(make_pair(CEnumDef::Ser_NR_DM_TDD_EPSFB, &CSearchArea_Sample::DealDataSub_Dt_Cqt_NR));
	m_mapSubNrPos.insert(make_pair(CEnumDef::Ser_NR_NSA_TDD_MULTI, &CSearchArea_Sample::DealDataSub_Dt_Cqt_NR));
	m_mapSubNrPos.insert(make_pair(CEnumDef::Ser_NR_SA_TDD_MULTI, &CSearchArea_Sample::DealDataSub_Dt_Cqt_NR));
	m_mapSubNrPos.insert(make_pair(CEnumDef::Ser_NR_DM_TDD_MULTI, &CSearchArea_Sample::DealDataSub_Dt_Cqt_NR));
}

void CSearchArea_Sample::DealDataSub_Dt_Cqt_NR(std::string tbname, std::string ifileidSql)
{
	if (tbname.find("_dtnr_") != std::string::npos)
	{
		DealDataSub_Model(CColumnDef::tb_dtnr_sample, tbname, ifileidSql, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREA_COVER_SAMPLE_NR);
	}
	else if (tbname.find("_cqtnr_") != std::string::npos)
	{
		DealDataSub_Model(CColumnDef::tb_cqtnr_sample, tbname, ifileidSql, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREA_COVER_SAMPLE_NR);
	}
}



BOOL 
CSearchArea_Sample::DealDataSub_Model(const int sampletbid, const std::string sampletbname, const std::string ifileidSql, 
                                      const BYTE bColumIDCmd, const BYTE bThirdCmd)
{
	std::string columnid = "";

	CColumnResult columnResult_sample;

	CHECLFALSE(   columnResult_sample.Init
		( std::string(m_column),
		CStdCommond::itostr(sampletbid),
		sampletbname,
		m_pDataBaseDeal_,
		m_pServerDeal_,
		m_pSendBuf_)
		);


	std::vector<STRU_ColumnAppendData> mapColumnAppendData;
	STRU_ColumnAppendData columnAppendData;
	std::string sql_sample = " ilongitude,ilatitude,ifileid,itime, ";

	columnAppendData.columnName = "ilongitude"; 
	columnAppendData.dataType = TYPE_CS_INT;
	mapColumnAppendData.push_back(columnAppendData);
	columnAppendData.columnName = "ilatitude"; 
	columnAppendData.dataType = TYPE_CS_INT;
	mapColumnAppendData.push_back(columnAppendData);
	columnAppendData.columnName = "ifileid"; 
	columnAppendData.dataType = TYPE_CS_INT;
	mapColumnAppendData.push_back(columnAppendData);
	columnAppendData.columnName = "itime"; 
	columnAppendData.dataType = TYPE_CS_INT;
	mapColumnAppendData.push_back(columnAppendData);


	std::string strSQL = p_SqlMaker->Sql_Select(sql_sample + " " + columnResult_sample.GetDBSearchSql(), sampletbname, 
		conditionMaker_Sample.GetSearchConfitionSql() + ifileidSql);

	columnResult_sample.SendColumnsIDToClient(CMD1_DIYSEARCH, CMD2_RESPONSE, bColumIDCmd);

	columnResult_sample.SearchSqlSendDataWithMyData(	   
		CMD1_DIYSEARCH,
		CMD2_RESPONSE,
		bThirdCmd,
		strSQL, 
		mapColumnAppendData);  


	return TRUE;
}

