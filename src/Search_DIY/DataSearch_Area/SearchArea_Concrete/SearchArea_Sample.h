#pragma once
#include "./SearchArea_Sub.h"

#define	RESTYPE_DIY_AREA_COVER_SAMPLE_GSMV			    0xa1

#define	RESTYPE_DIY_AREA_COVER_SAMPLE_GPRS			    0xa5

#define	RESTYPE_DIY_AREA_COVER_SAMPLE_SCAN			    0xa7

#define	RESTYPE_DIY_AREA_COVER_SAMPLE_TDSCDMA_V		    0xa9

#define	RESTYPE_DIY_AREA_COVER_SAMPLE_TDSCDMA_D		    0xab

#define	RESTYPE_DIY_AREA_COVER_SAMPLE_WCDMA_V	        0xb0

#define	RESTYPE_DIY_AREA_COVER_SAMPLE_WCDMA_D		    0xb2

#define	RESTYPE_DIY_AREA_COVER_SAMPLE_CDMA_V			0xb8

#define	RESTYPE_DIY_AREA_COVER_SAMPLE_CDMA_D			0xba

#define	RESTYPE_DIY_AREA_COVER_SAMPLE_CDMA2000_D		0xbc

#define	RESTYPE_DIY_AREA_COVER_SAMPLE_CDMA2000_V		0xbe

#define	RESTYPE_DIY_AREA_COVER_SAMPLE_SCAN_GSM		    0xc0

#define	RESTYPE_DIY_AREA_COVER_SAMPLE_SCAN_TD		    0xc2

#define	RESTYPE_DIY_AREA_COVER_SAMPLE_SCAN_W		    0xc4

#define RESTYPE_COLUMN_AREA_COVER_SAMPLE_WLAN           0xc5
#define	RESTYPE_DIY_AREA_COVER_SAMPLE_WLAN  		    0xc6

#define	RESTYPE_DIY_AREA_COVER_SAMPLE_LTE  		        0xc8

#define	RESTYPE_DIY_AREA_COVER_SAMPLE_SCAN_FREQSPECTURM	        0xca

#define	RESTYPE_DIY_AREA_COVER_SAMPLE_SCAN_LTE	        0xcc

#define	RESTYPE_DIY_AREA_COVER_SAMPLE_SCAN_CW	        0xce

#define	RESTYPE_DIY_AREA_COVER_SAMPLE_LTE_UEP  		    0xd0

#define	RESTYPE_DIY_AREA_COVER_SAMPLE_LTE_FDD  		    0xd1

#define RESTYPE_DIY_AREA_COVER_SAMPLE_SIGNAL			0xd2

#define	RESTYPE_DIY_AREA_COVER_SAMPLE_SCAN_NBIOT_TOPN	0xd4

#define	RESTYPE_DIY_AREA_COVER_SAMPLE_NR  		        0xd5

#define	RESTYPE_DIY_AREA_COVER_SAMPLE_SCAN_NR  		    0xd6

class CSearchArea_Sample :public CSearchArea_Sub
{
public:
	CSearchArea_Sample(void);
	virtual ~CSearchArea_Sample(void);

protected:
   BOOL FetchSearchCondiction(const BYTE* const pData, const int nCount,int& ioffset);
   virtual BOOL MakeLogFileGroup();

   virtual BOOL DealDataSub_GSM(int iservicetype, std::string tm_file, std::string tbname);
   virtual BOOL DealDataSub_CDMA(int iservicetype, std::string tm_file, std::string tbname);
   virtual BOOL DealDataSub_CDMA2000(int iservicetype, std::string tm_file, std::string tbname);
   virtual BOOL DealDataSub_TDSCDMA(int iservicetype, std::string tm_file, std::string tbname);
   virtual BOOL DealDataSub_WCDMA(int iservicetype, std::string tm_file, std::string tbname);
   virtual BOOL DealDataSub_Scan(int iservicetype, std::string tm_file, std::string tbname);
   virtual BOOL DealDataSub_WLAN(int iservicetype, std::string tm_file, std::string tbname);
   virtual BOOL DealDataSub_LTE(int iservicetype, std::string tm_file, std::string tbname);
   virtual BOOL DealDataSub_LTE_UEP(int iservicetype, std::string tm_file, std::string tbname);
   virtual BOOL DealDataSub_LTE_FDD(int iservicetype, std::string tm_file, std::string tbname);
   virtual BOOL DealDataSub_Signal(int iservicetype, std::string tm_file, std::string tbname);
   virtual BOOL DealDataSub_NR(int iservicetype, std::string tm_file, std::string tbname);
   virtual BOOL DealDataSub_Model(const int sampletbid, const std::string sampletbname, const std::string ifileidSql, const BYTE bColumIDCmd, const BYTE bThirdCmd);


   CSearchConditionMaker conditionMaker_Sample;

private:
	void DealData_Service_GSM_Voice(std::string tbname, std::string ifileidSql);
	void DealData_Service_TDSCDMA_Voice(std::string tbname, std::string ifileidSql);
	void DealData_Service_TDSCDMA_Data(std::string tbname, std::string ifileidSql);

    typedef void (CSearchArea_Sample::*DealScanData)(std::string tbname, std::string ifileidSql);
    std::map<int, DealScanData> m_mapSubScanPos;

    void DealDataSub_Ser_SCAN(std::string tbname, std::string ifileidSql);
    void DealDataSub_Ser_SCAN_TD(std::string tbname, std::string ifileidSql);
    void DealDataSub_Ser_SCAN_WCDMA(std::string tbname, std::string ifileidSql);
    void DealDataSub_Ser_LTE_SCAN_TOPN(std::string tbname, std::string ifileidSql);
    void DealDataSub_Ser_NBIOT_SCAN_TOPN(std::string tbname, std::string ifileidSql);
	void DealDataSub_Ser_NR_SCAN(std::string tbname, std::string ifileidSql);

    void DealDataSub_Ser_LTE_SCAN_CW(std::string tbname, std::string ifileidSql);
    void DealDataSub_Ser_GSM_SCAN_FREQSPECTRUM(std::string tbname, std::string ifileidSql);
	void InitSubScanMap(void);

	std::map<int, DealScanData> m_mapSubLtePos;
	std::map<int, DealScanData> m_mapSubNrPos;
	void DealDataSub_Dt_Cqt_LTE(std::string tbname, std::string ifileidSql);
	void DealDataSub_Dt_Cqt_NR(std::string tbname, std::string ifileidSql);

	void InitSubLteMap(void);
	void InitSubNrMap(void);

};
