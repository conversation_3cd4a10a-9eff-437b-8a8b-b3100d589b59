#include "SearchArea_Sample_CoverLeak.h"

CSearchArea_Sample_CoverLeak::CSearchArea_Sample_CoverLeak(void)
{
}

CSearchArea_Sample_CoverLeak::~CSearchArea_Sample_CoverLeak(void)
{
}



BOOL 
CSearchArea_Sample_CoverLeak::MakeLogFileGroup()
{
	m_map_fileItemList.clear();

	for (auto p_DataItem = m_pDataRows_LogFile.begin(); p_DataItem != m_pDataRows_LogFile.end(); ++p_DataItem)
	{
		CDataItem_LogFile* p_logfile = (CDataItem_LogFile*)*p_DataItem;

		STRU_FILE_LIST2 tm1;

		tm1.testtype = p_logfile->itesttype;
		tm1.strlog = p_logfile->logtbname;
		tm1.strtb = p_logfile->suffix_week;	
		tm1.servicetype = p_logfile->iservicetype;

		m_map_fileItemList[tm1].push_back(p_logfile); 
	}
	return TRUE;
}


BOOL 
CSearchArea_Sample_CoverLeak::DealDataSub_GSM(int iservicetype, std::string tm_file, std::string tbname)
{
	std::string ifileidSql = " and ifileid in (" + std::string(tm_file) + ")";

	switch(iservicetype)
	{
	case CEnumDef::Ser_GSM_VOICE:
	case CEnumDef::Ser_GSM_IDLE:
		{
			tbname = "tb_coverleak_gsm_voice_" + tbname;
			DealDataSub_Model(CColumnDef::tb_dtgsm_sample6, tbname, ifileidSql, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREA_COVER_SAMPLE_GSMV);
		}
		break;

	case CEnumDef::Ser_GPRS_DATA:
	case CEnumDef::Ser_EDGE_DATA:
		{
			tbname = "tb_coverleak_gsm_data_" + tbname;
			DealDataSub_Model(CColumnDef::tb_dtgsm_sample5, tbname, ifileidSql, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREA_COVER_SAMPLE_GPRS);
		}
		break;

	default:
		{
			return FALSE;
		}
	}

	return TRUE;
}

BOOL 
CSearchArea_Sample_CoverLeak::DealDataSub_CDMA(int iservicetype, std::string tm_file, std::string tbname)
{
	std::string ifileidSql = " and ifileid in (" + std::string(tm_file) + ")";

	switch(iservicetype)
	{
	case CEnumDef::Ser_CDMA_VOICE:
	case CEnumDef::Ser_CDMA_IDLE:
		{
		}
		break;

	case CEnumDef::Ser_CDMA1X_DATA:
		{
		}
		break;

	default:
		{
			return FALSE;
		}
	}

	return TRUE;
}


BOOL 
CSearchArea_Sample_CoverLeak::DealDataSub_WCDMA(int iservicetype, std::string tm_file, std::string tbname)
{
	std::string ifileidSql = " and ifileid in (" + std::string(tm_file) + ")";

	switch(iservicetype)
	{
	case CEnumDef::Ser_WCDMA_VOICE:
	case CEnumDef::Ser_WCDMA_VIDEO:
	case CEnumDef::Ser_WCDMA_IDLE:
		{
		}
		break;

	case CEnumDef::Ser_WCDMA_DATA:
	case CEnumDef::Ser_WCDMA_HSDPA:
		{
		}
		break;

	default:
		{
			return FALSE;
		}
	}

	return TRUE;
}

BOOL 
CSearchArea_Sample_CoverLeak::DealDataSub_TDSCDMA(int iservicetype, std::string tm_file, std::string tbname)
{
	std::string ifileidSql = " and ifileid in (" + std::string(tm_file) + ")";

	switch(iservicetype)
	{
	case CEnumDef::Ser_TDSCDMA_VOICE:
	case CEnumDef::Ser_TDSCDMA_VIDEO:
	case CEnumDef::Ser_TDSCDMA_IDLE:
		{
			tbname = "tb_coverleak_tdscdma_voice_" + tbname;
			DealDataSub_Model(CColumnDef::tb_dttdscdma_sample, tbname, ifileidSql, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREA_COVER_SAMPLE_TDSCDMA_V);
		}
		break;

	case CEnumDef::Ser_TDSCDMA_DATA:
	case CEnumDef::Ser_TDSCDMA_HSDPA:
		{
			tbname = "tb_coverleak_tdscdma_data_" + tbname;
			DealDataSub_Model(CColumnDef::tb_dttdscdma_sample5, tbname, ifileidSql, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_AREA_COVER_SAMPLE_TDSCDMA_D);
		}
		break;

	default:
		{
			return FALSE;
		}

	}

	return TRUE;

}

BOOL 
CSearchArea_Sample_CoverLeak::DealDataSub_Scan(int iservicetype, std::string tm_file, std::string tbname)
{
	std::string ifileidSql = " and ifileid in (" + std::string(tm_file) + ")";

	switch(iservicetype)
	{
	case CEnumDef::Ser_SCAN:
		{
		}
		break;

	case CEnumDef::Ser_SCAN_TD:
		{
		}
		break;

	case CEnumDef::Ser_SCAN_WCDMA:
		{
		}
		break;

	default:
		{
			return FALSE;
		}
	}

	return TRUE;
}

BOOL 
CSearchArea_Sample_CoverLeak::DealDataSub_CDMA2000(int iservicetype, std::string tm_file, std::string tbname)
{
	std::string ifileidSql = " and ifileid in (" + std::string(tm_file) + ")";

	switch(iservicetype)
	{
	case CEnumDef::Ser_CDMA2000_VOICE:
	case CEnumDef::Ser_CDMA2000_VIDEO:
		{
		}
		break;

	case CEnumDef::Ser_CDMA2000_DATA:
		{
		}
		break;

	default:
		{
			return FALSE;
		}
	}

	return TRUE;
}
