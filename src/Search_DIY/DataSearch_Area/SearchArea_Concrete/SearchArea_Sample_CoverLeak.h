#pragma once

#include "./SearchArea_Sample.h"

class CSearchArea_Sample_CoverLeak : public CSearchArea_Sample
{
public:
	CSearchArea_Sample_CoverLeak(void);
public:
	~CSearchArea_Sample_CoverLeak(void);

protected:
	virtual BOOL MakeLogFileGroup();

	virtual BOOL DealDataSub_GSM(int iservicetype, std::string tm_file, std::string tbname);
	virtual BOOL DealDataSub_CDMA(int iservicetype, std::string tm_file, std::string tbname);
	virtual BOOL DealDataSub_CDMA2000(int iservicetype, std::string tm_file, std::string tbname);
	virtual BOOL DealDataSub_TDSCDMA(int iservicetype, std::string tm_file, std::string tbname);
	virtual BOOL DealDataSub_WCDMA(int iservicetype, std::string tm_file, std::string tbname);
	virtual BOOL DealDataSub_Scan(int iservicetype, std::string tm_file, std::string tbname);
};
