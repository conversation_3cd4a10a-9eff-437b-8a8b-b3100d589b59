#pragma once
#include "./SearchArea_Sub.h"

#define	RESTYPE_DIY_AREA_COVER_GRID_SCAN_DTWCDMA	  0xbc

#define	RESTYPE_DIY_AREA_COVER_GRID_SCAN_GSM		  0xbe

#define	RESTYPE_DIY_AREA_COVER_GRID_SCAN_CDMA		  0xc0

#define	RESTYPE_DIY_AREA_COVER_GRID_SCAN_WCDMA		  0xc2

#define	RESTYPE_DIY_AREA_COVER_GRID_SCAN_TDSCDMA	  0xc4

#define	RESTYPE_DIY_AREA_COVER_GRID_GSM_MTR	          0xc6

#define	RESTYPE_DIY_AREA_COVER_GRID_SCAN_DTGSM		  0xc8

#define	RESTYPE_DIY_AREA_COVER_GRID_SCAN_LTE_TOPN	  0xca

#define	RESTYPE_DIY_AREA_COVER_GRID_SCAN_NBIOT_TOPN	  0xd0


class CSearchArea_Scan_Grid: public CSearchArea_Sub
{
public:
	CSearchArea_Scan_Grid();
	virtual ~CSearchArea_Scan_Grid();

protected: 
	virtual BOOL FetchSearchCondiction(const BYTE* const pData, const int nCount,int& ioffset);

	virtual BOOL MakeLogFileGroup();
	virtual BOOL DealDataSub_GSM(int iservicetype, std::string tm_file, std::string tbname);
	virtual BOOL DealDataSub_CDMA(int iservicetype, std::string tm_file, std::string tbname);
	virtual BOOL DealDataSub_CDMA2000(int iservicetype, std::string tm_file, std::string tbname);
	virtual BOOL DealDataSub_TDSCDMA(int iservicetype, std::string tm_file, std::string tbname);
	virtual BOOL DealDataSub_WCDMA(int iservicetype, std::string tm_file, std::string tbname);
	virtual BOOL DealDataSub_Scan(int iservicetype, std::string tm_file, std::string tbname);
	virtual BOOL DealDataSub_Model(const int gridtbid, const std::string gridtbname, const std::string ifileidSql, const BYTE bColumIDCmd, const BYTE bThirdCmd);

	virtual BOOL ClearData(void);
	BOOL AddSearchColumn(const int gridtbid, const std::string s_column, std::string& t_column);

protected:
	CSearchConditionItem_AreaSelectIntersect* areaSelectIntersect;
};
