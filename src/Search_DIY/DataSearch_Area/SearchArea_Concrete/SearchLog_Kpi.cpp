#include "SearchLog_Kpi.h"

CSearchLog_Kpi::CSearchLog_Kpi(void)
{
}

CSearchLog_Kpi::~CSearchLog_Kpi(void)
{
}



BOOL
CSearchLog_Kpi::FetchSearchCondiction(const BYTE* const pData, const int nCount,int& ioffset)
{
	CHECLFALSE(CSearchArea::FetchSearchCondiction(pData, nCount, ioffset));

	PPM_OS::memset(&m_column, 0, sizeof(m_column));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,m_column,sizeof(m_column)));

	return TRUE;
}

BOOL 
CSearchLog_Kpi::MakeLogFileGroup()
{
	for (auto p_DataItem = m_pDataRows_LogFile.begin(); p_DataItem != m_pDataRows_LogFile.end(); ++p_DataItem)
	{
		CDataItem_LogFile* p_logfile = (CDataItem_LogFile*)*p_DataItem;

		STRU_FILE_LIST2 tm1;
		tm1.testtype = p_logfile->itesttype;
		tm1.strlog = p_logfile->logtbname;		
		tm1.servicetype = p_logfile->iservicetype;
		tm1.strtb = "";	

		m_map_fileItemList[tm1].push_back(p_logfile); 
	}

	return TRUE;
}

BOOL
CSearchLog_Kpi::DealDataSub_GSM(int iservicetype, std::string tm_file, std::string tbname)
{
	switch (iservicetype)
	{
	case CEnumDef::Ser_GSM_VOICE:
	case CEnumDef::Ser_GSM_IDLE:
		{	
			DealDataSub_Model(CColumnDef::tb_model_gsm_stati_voice_log, "tb_gsm_stati_voice_log" , tm_file, 
				RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_LOG_KPI_GSM);
		}
		break;

	case CEnumDef::Ser_GPRS_DATA:
	case CEnumDef::Ser_EDGE_DATA:
		{	
			DealDataSub_Model(CColumnDef::tb_model_gsm_stati_data_log, "tb_gsm_stati_data_log", tm_file, 
				RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_LOG_KPI_GPRS);	  
		}
		break;	

	case CEnumDef::Ser_GSM_UPLINK:
		{	
			DealDataSub_Model(CColumnDef::tb_model_gsm_stati_mtr_log, "tb_gsm_stati_mtr_log" , tm_file, 
				RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_LOG_KPI_GSM_MTR);
		}
		break;
	default:
		break;
	}
	return TRUE;
}

BOOL
CSearchLog_Kpi::DealDataSub_CDMA(int iservicetype, std::string tm_file, std::string tbname)
{
	switch(iservicetype)
	{
	case CEnumDef::Ser_CDMA_VOICE:
	case CEnumDef::Ser_CDMA_IDLE:
		{
			DealDataSub_Model(CColumnDef::tb_model_cdma_stati_voice_log, "tb_cdma_stati_voice_log", tm_file, 
				RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_LOG_KPI_CDMA_V);
		}
		break;

	case CEnumDef::Ser_CDMA1X_DATA:
		{
           DealDataSub_Model(CColumnDef::tb_model_cdma_stati_d_log, "tb_cdma_stati_data_log" , tm_file, 
			   RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_LOG_KPI_CDMA_D);
		}
	default:
		break;
	}
	return TRUE;
}

BOOL
CSearchLog_Kpi::DealDataSub_CDMA2000(int iservicetype, std::string tm_file, std::string tbname)
{ 
	switch(iservicetype)
	{
	case CEnumDef::Ser_CDMA2000_VOICE:
	case CEnumDef::Ser_CDMA2000_VIDEO:
		{		   
		}
		break;

	case CEnumDef::Ser_CDMA2000_IDLE:
	case CEnumDef::Ser_CDMA2000_DATA:
		{
            DealDataSub_Model(CColumnDef::tb_model_cdma2000_stati_data_log, "tb_cdma2000_stati_data_log", tm_file, 
				RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_LOG_KPI_CDMA2000_D);
		}
		break;
	default:
		break;
	}
	return TRUE;
}

BOOL
CSearchLog_Kpi::DealDataSub_TDSCDMA(int iservicetype, std::string tm_file, std::string tbname)
{
	switch(iservicetype)
	{
	case CEnumDef::Ser_TDSCDMA_VOICE:
	case CEnumDef::Ser_TDSCDMA_IDLE:
		{
			DealDataSub_Model(CColumnDef::tb_model_tdscdma_stati_amr_log, "tb_tdscdma_stati_amr_log", tm_file, 
				RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_LOG_KPI_TDSCDMA_AMR);
		}
		break;

	case CEnumDef::Ser_TDSCDMA_VIDEO:
		{
			DealDataSub_Model(CColumnDef::tb_model_tdscdma_stati_vp_log, "tb_tdscdma_stati_vp_log", tm_file, 
				RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_LOG_KPI_TDSCDMA_VP);	
		}
		break;

	case CEnumDef::Ser_TDSCDMA_DATA:
		{
			DealDataSub_Model(CColumnDef::tb_model_tdscdma_stati_ps_log, "tb_tdscdma_stati_ps_log", tm_file, 
				RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_LOG_KPI_TDSCDMA_PS);	
		}
		break;
	case CEnumDef::Ser_TDSCDMA_HSDPA:
		{
			DealDataSub_Model(CColumnDef::tb_model_tdscdma_stati_ps_log_hsdpa, "tb_tdscdma_stati_ps_log", tm_file,
				RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_LOG_KPI_TDSCDMA_PS);	
		}
		break;
	default:
		break;
	}
	return TRUE;
}


BOOL
CSearchLog_Kpi::DealDataSub_WCDMA(int iservicetype, std::string tm_file, std::string tbname)
{
	switch (iservicetype)
	{
	case CEnumDef::Ser_WCDMA_VOICE:
	case CEnumDef::Ser_WCDMA_IDLE:
		{
			DealDataSub_Model(CColumnDef::tb_model_wcdma_stati_amr_log, "tb_wcdma_stati_amr_log", tm_file,
				RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_LOG_KPI_WCDMA_AMR);		
		}
		break;

	case CEnumDef::Ser_WCDMA_VIDEO:
		{
			DealDataSub_Model(CColumnDef::tb_model_wcdma_stati_vp_log, "tb_wcdma_stati_vp_log", tm_file, 
				RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_LOG_KPI_WCDMA_VP);	 	

		}
		break;

	case CEnumDef::Ser_WCDMA_DATA:
		{
			DealDataSub_Model(CColumnDef::tb_model_wcdma_stati_ps_log, "tb_wcdma_stati_ps_log", tm_file,
				RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_LOG_KPI_WCDMA_PS);	 	
		}
		break;

	case CEnumDef::Ser_WCDMA_HSDPA:
		{
			DealDataSub_Model(CColumnDef::tb_model_wcdma_stati_pshs_log_psdpa, "tb_wcdma_stati_pshs_log", tm_file,
				RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_LOG_KPI_WCDMA_PSHS);
		}
		break;
	case CEnumDef::Ser_WCDMA_HSUPA:
		{
			DealDataSub_Model(CColumnDef::tb_model_wcdma_stati_pshs_log_psupa, "tb_wcdma_stati_pshs_log", tm_file,
				RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_LOG_KPI_WCDMA_PSHS);
		}
		break;
	default:
		break;
	}
	return TRUE;
}

BOOL
CSearchLog_Kpi::DealDataSub_Scan(int iservicetype, std::string tm_file, std::string tbname)
{
	switch (iservicetype)
	{
	case CEnumDef::Ser_SCAN:
		{
			DealDataSub_Model(CColumnDef::tb_scan_gsm_stati_log, "tb_scan_gsm_stati_log", tm_file,
				RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_LOG_KPI_SCAN_GSM);		
		}
		break;

	case CEnumDef::Ser_SCAN_TD:
		{
			DealDataSub_Model(CColumnDef::tb_scan_tdscdma_stati_log, "tb_scan_tdscdma_stati_log", tm_file,
				RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_LOG_KPI_SCAN_TDSCDMA);		
		}
		break;

	case CEnumDef::Ser_LTE_SCAN_TOPN:
		{
			DealDataSub_Model(CColumnDef::tb_scan_ltetopn_stati_log,"tb_scan_ltetopn_stati_log", tm_file, 
				RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_LOG_KPI_SCAN_LTETOPN);
		}
		break;

	case CEnumDef::Ser_NBIOT_SCAN_TOPN:
		{
			DealDataSub_Model(CColumnDef::tb_scan_nbiot_topn_stati_log,"tb_scan_nbiot_topn_stati_log", tm_file,
				RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_LOG_KPI_SCAN_NBIOT_TOPN);
		}
		break;

	case CEnumDef::Ser_LTE_SCAN_FREQSPECTRUM:
		{
			DealDataSub_Model(CColumnDef::tb_scan_lte_freqspectrum_stati_log,"tb_scan_lte_freqspectrum_stati_log", tm_file,
				RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_LOG_KPI_LTE_FREQSPECTRUM);
		}
		break;
	case CEnumDef::Ser_SCAN_NR:
		{
			DealDataSub_Model(CColumnDef::tb_scan_nr_stati_log, "tb_scan_nr_stati_log", tm_file,
				RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_LOG_KPI_SCAN_NR);
		}
		break;
	case CEnumDef::SER_SCAN_NRFREQ:
		{
			DealDataSub_Model(CColumnDef::tb_scan_nr_freqspectrum_stati_log, "tb_scan_nr_freqspectrum_stati_log", tm_file,
				RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_LOG_KPI_NR_FREQSPECTRUM);
		}
		break;
	default:
		break;
	}
	return TRUE;
}


BOOL 
CSearchLog_Kpi::DealDataSub_WLAN(int iservicetype, std::string tm_file, std::string tbname)
{
	switch (iservicetype)
	{
	case CEnumDef::Ser_WLAN:
		{	
			DealDataSub_Model(CColumnDef::tb_wlan_stati_log, "tb_wlan_stati_log" , tm_file,
				RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_LOG_KPI_WLAN);
		}
		break;
	default:
		break;
	}
	return TRUE;
}

BOOL 
CSearchLog_Kpi::DealDataSub_LTE(int iservicetype, std::string tm_file, std::string tbname)
{
	if (CEnumDef::IsLteTddServiceType(iservicetype))
	{
		DealDataSub_Model(CColumnDef::tb_model_lte_stati_amr_log, "tb_lte_stati_amr_log", tm_file,
			RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_LOG_KPI_LTE_AMR);
	}

	return TRUE;
}

BOOL 
CSearchLog_Kpi::DealDataSub_LTE_FDD(int iservicetype, std::string tm_file, std::string tbname)
{
	if (CEnumDef::IsLteFddServiceType(iservicetype))
	{
		DealDataSub_Model(CColumnDef::tb_model_lte_fdd_stati_amr_log, "tb_lte_fdd_stati_amr_log", tm_file,
			RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_LOG_KPI_LTE_FDD_AMR);
	}

	return TRUE;
}

BOOL 
CSearchLog_Kpi::DealDataSub_Signal(int iservicetype, std::string tm_file, std::string tbname)
{
	switch (iservicetype)
	{
	case CEnumDef::SER_LTE_SIGNAL:
		{
			DealDataSub_Model(CColumnDef::tb_model_lte_signal_stati_log, "tb_signal_stati_amr_log" , tm_file,
				RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_LOG_KPI_LTE_SIGNAL);
		}
		break;
	default:
		break;
	}
	return TRUE;
}

BOOL 
CSearchLog_Kpi::DealDataSub_NR(int iservicetype, std::string tm_file, std::string tbname)
{
	if (CEnumDef::IsNrServiceType(iservicetype))
	{
		DealDataSub_Model(CColumnDef::tb_nr_stati_amr_log, "tb_nr_stati_amr_log", tm_file,
			RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_LOG_KPI_NR_AMR);
	}

	return TRUE;
}


BOOL 
CSearchLog_Kpi::DealDataSub_Model(const int tbid, const std::string tbname, const std::string tm_file, const BYTE bColumIDCmd, const BYTE bThirdCmd)
{
	std::string columnid = "";
	CHECLFALSE(AddSearchColumn(tbid, std::string(m_column), columnid));

	CColumnResult columnResult_stati_log;

	CHECLFALSE(   columnResult_stati_log.Init
		( columnid,
		CStdCommond::itostr(tbid), 
		tbname,
		m_pDataBaseDeal_,
		m_pServerDeal_,
		m_pSendBuf_)
		);

	std::string strSQL = "";
	if (0 != tm_file.length())
	{
		std::string sql_file = " ifileid in (" + tm_file + ")";
		strSQL = p_SqlMaker->Sql_Select(columnResult_stati_log.GetDBSearchSql(), tbname, sql_file);
	}
	else 
	{
		strSQL = p_SqlMaker->Sql_Select(columnResult_stati_log.GetDBSearchSql(), tbname, "");        
	}

	columnResult_stati_log.SendColumnsIDToClient(CMD1_DIYSEARCH, CMD2_RESPONSE, bColumIDCmd);    

	columnResult_stati_log.SearchSqlSendData(	   
		CMD1_DIYSEARCH,
		CMD2_RESPONSE,
		bThirdCmd,
		strSQL);  

	return TRUE;

}

BOOL
CSearchLog_Kpi::AddSearchColumn(const int gridtbid, const std::string s_column, std::string& t_column)
{ 
	if (0 == (int)s_column.length())
	{
		return FALSE;
	}

	CColumnItem* p_FileidColumn = CColumnDef::Instance()->GetColumn(gridtbid, "image1");
	CHECLNULL(p_FileidColumn);

	t_column = CStdCommond::itostr(p_FileidColumn->imageID) + "," + CStdCommond::itostr(p_FileidColumn->paraID) + "," +
		CStdCommond::itostr(p_FileidColumn->tbID) + "," + s_column;

	return TRUE;
}

BOOL
CSearchLog_Kpi::ClearData(void)
{
	CSearchArea::ClearData();

	m_map_fileItemList.clear();
	return TRUE;
}
