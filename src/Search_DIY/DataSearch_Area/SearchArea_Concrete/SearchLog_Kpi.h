#pragma once

#include "./SearchArea_Sub.h"

#define	RESTYPE_DIY_LOG_KPI_GSM     		  0x21

#define	RESTYPE_DIY_LOG_KPI_GPRS			  0x23

#define	RESTYPE_DIY_LOG_KPI_TDSCDMA_AMR		  0x27

#define	RESTYPE_DIY_LOG_KPI_TDSCDMA_PS		  0x29

#define	RESTYPE_DIY_LOG_KPI_TDSCDMA_VP		  0x2b

#define	RESTYPE_DIY_LOG_KPI_WCDMA_AMR		  0x2d

#define	RESTYPE_DIY_LOG_KPI_WCDMA_PS		  0x2f

#define	RESTYPE_DIY_LOG_KPI_WCDMA_VP		  0x31

#define	RESTYPE_DIY_LOG_KPI_WCDMA_PSHS		  0x33

#define	RESTYPE_DIY_LOG_KPI_CDMA_V			  0x35

#define	RESTYPE_DIY_LOG_KPI_CDMA_D			  0x37

#define	RESTYPE_DIY_LOG_KPI_CDMA2000_D		  0x39

#define	RESTYPE_DIY_LOG_KPI_SCAN_GSM		  0x3b

#define	RESTYPE_DIY_LOG_KPI_GSM_MTR		      0x3d

#define	RESTYPE_DIY_LOG_KPI_SCAN_TDSCDMA	  0x3f

#define	RESTYPE_DIY_LOG_KPI_WLAN			  0x41

#define RESTYPE_DIY_LOG_KPI_LTE_AMR           0x43

#define RESTYPE_DIY_LOG_KPI_SCAN_LTETOPN	  0x45

#define RESTYPE_DIY_LOG_KPI_LTE_FDD_AMR       0x46

#define RESTYPE_DIY_LOG_KPI_LTE_SIGNAL        0xcd

#define RESTYPE_DIY_LOG_KPI_LTE_FREQSPECTRUM  0xce

#define RESTYPE_DIY_LOG_KPI_SCAN_NBIOT_TOPN	  0xd1

#define RESTYPE_DIY_LOG_KPI_NR_AMR            0xd2

#define RESTYPE_DIY_LOG_KPI_SCAN_NR	  0xd4

#define RESTYPE_DIY_LOG_KPI_NR_FREQSPECTRUM  0xd6

class CSearchLog_Kpi : public CSearchArea_Sub
{
public:
	CSearchLog_Kpi(void);
	~CSearchLog_Kpi(void);

protected: 
	virtual BOOL FetchSearchCondiction(const BYTE* const pData, const int nCount,int& ioffset);

	virtual BOOL MakeLogFileGroup();
	virtual BOOL DealDataSub_GSM(int iservicetype, std::string tm_file, std::string tbname);
	virtual BOOL DealDataSub_CDMA(int iservicetype, std::string tm_file, std::string tbname);
	virtual BOOL DealDataSub_CDMA2000(int iservicetype, std::string tm_file, std::string tbname);
	virtual BOOL DealDataSub_TDSCDMA(int iservicetype, std::string tm_file, std::string tbname);
	virtual BOOL DealDataSub_WCDMA(int iservicetype, std::string tm_file, std::string tbname);
	virtual BOOL DealDataSub_Scan(int iservicetype, std::string tm_file, std::string tbname);
	virtual BOOL DealDataSub_WLAN(int iservicetype, std::string tm_file, std::string tbname);
	virtual BOOL DealDataSub_LTE(int iservicetype, std::string tm_file, std::string tbname);
	virtual BOOL DealDataSub_LTE_FDD(int iservicetype, std::string tm_file, std::string tbname);
	virtual BOOL DealDataSub_Signal(int iservicetype, std::string tm_file, std::string tbname);
	virtual BOOL DealDataSub_NR(int iservicetype, std::string tm_file, std::string tbname);
	virtual BOOL DealDataSub_Model(const int gridtbid, const std::string gridtbname, const std::string ifileidSql, const BYTE bColumIDCmd, const BYTE bThirdCmd);
	BOOL AddSearchColumn(const int gridtbid, const std::string s_column, std::string& t_column);

	virtual BOOL ClearData(void);

protected:
	CSearchConditionItem_AreaSelectIntersect* areaSelectIntersect;

};
