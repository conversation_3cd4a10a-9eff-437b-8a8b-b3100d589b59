#include "SearchArea_Sub.h"

CSearchArea_Sub::CSearchArea_Sub(void)
{
	for (int i = 0; i < 256; i++)
	{
		pfunc_TestTypeData[i] = &CSearchArea_Sub::DealDataSub_Default;
	}

	pfunc_TestTypeData[CEnumDef::T_GSM_DT] = &CSearchArea_Sub::DealDataSub_GSM;
	pfunc_TestTypeData[CEnumDef::T_GSM_CQT] = &CSearchArea_Sub::DealDataSub_GSM;
	pfunc_TestTypeData[CEnumDef::T_AUTODT] = &CSearchArea_Sub::DealDataSub_GSM;
	pfunc_TestTypeData[CEnumDef::T_AUTODT_TA] = &CSearchArea_Sub::DealDataSub_GSM;
	pfunc_TestTypeData[CEnumDef::T_AUTOCQT] = &CSearchArea_Sub::DealDataSub_GSM;

	pfunc_TestTypeData[CEnumDef::T_SCANTEST] = &CSearchArea_Sub::DealDataSub_Scan;

	pfunc_TestTypeData[CEnumDef::T_CDMA_DT] = &CSearchArea_Sub::DealDataSub_CDMA;
	pfunc_TestTypeData[CEnumDef::T_CDMA_CQT] = &CSearchArea_Sub::DealDataSub_CDMA;

	pfunc_TestTypeData[CEnumDef::T_CDMA2000_DT] = &CSearchArea_Sub::DealDataSub_CDMA2000;
	pfunc_TestTypeData[CEnumDef::T_CDMA2000_CQT] = &CSearchArea_Sub::DealDataSub_CDMA2000;

	pfunc_TestTypeData[CEnumDef::T_TDSCDMA_DT] = &CSearchArea_Sub::DealDataSub_TDSCDMA;
	pfunc_TestTypeData[CEnumDef::T_TDSCDMA_CQT] = &CSearchArea_Sub::DealDataSub_TDSCDMA;

	pfunc_TestTypeData[CEnumDef::T_WCDMA_DT] = &CSearchArea_Sub::DealDataSub_WCDMA;
	pfunc_TestTypeData[CEnumDef::T_WCDMA_CQT] = &CSearchArea_Sub::DealDataSub_WCDMA;

	pfunc_TestTypeData[CEnumDef::T_WLAN_DT] = &CSearchArea_Sub::DealDataSub_WLAN;
	pfunc_TestTypeData[CEnumDef::T_WLAN_CQT] = &CSearchArea_Sub::DealDataSub_WLAN;

	pfunc_TestTypeData[CEnumDef::T_LTE_DT] = &CSearchArea_Sub::DealDataSub_LTE;
	pfunc_TestTypeData[CEnumDef::T_LTE_CQT] = &CSearchArea_Sub::DealDataSub_LTE;
	pfunc_TestTypeData[CEnumDef::T_NBIOT_CQT] = &CSearchArea_Sub::DealDataSub_LTE;
	pfunc_TestTypeData[CEnumDef::T_NBIOT_DT] = &CSearchArea_Sub::DealDataSub_LTE;

	pfunc_TestTypeData[CEnumDef::T_LTE_UEP] = &CSearchArea_Sub::DealDataSub_LTE_UEP;

	pfunc_TestTypeData[CEnumDef::T_LTE_FDD_DT] = &CSearchArea_Sub::DealDataSub_LTE_FDD;
	pfunc_TestTypeData[CEnumDef::T_LTE_FDD_CQT] = &CSearchArea_Sub::DealDataSub_LTE_FDD;

	pfunc_TestTypeData[CEnumDef::T_Signal_DT] = &CSearchArea_Sub::DealDataSub_Signal;
	pfunc_TestTypeData[CEnumDef::T_Signal_CQT] = &CSearchArea_Sub::DealDataSub_Signal;

	pfunc_TestTypeData[CEnumDef::T_NR_TDD_DT] = &CSearchArea_Sub::DealDataSub_NR;
	pfunc_TestTypeData[CEnumDef::T_NR_TDD_CQT] = &CSearchArea_Sub::DealDataSub_NR;

}

CSearchArea_Sub::~CSearchArea_Sub(void)
{
}

BOOL  
CSearchArea_Sub::DealDataSub()
{  
	char tm_file[8000];

	int bOnce;
	int cutnum = 600;         //ado对提交的sql语句限制为8000字节以内
	auto p_map = m_map_fileItemList.begin();   

	for (; p_map != m_map_fileItemList.end(); p_map++)   
	{	  
		auto p_vec = p_map->second.begin();	
		PPM_OS::memset(&tm_file, 0, sizeof(tm_file));	
		bOnce = 0;	
		size_t allcount=1;	

		for (int count=1; p_vec != p_map->second.end(); p_vec++, ++count, ++allcount)   
		{   
			if(0 == bOnce)
			{
				PPM_OS::snprintf(tm_file,sizeof(tm_file),"%d",(*p_vec)->ifileid); 
				bOnce = 1;
			}
			else
			{
				size_t orglen = PPM_OS::strlen(tm_file);
				PPM_OS::snprintf(tm_file + orglen, sizeof(tm_file) - orglen, ",%d", (*p_vec)->ifileid); 
			}	
			if(cutnum == count || allcount == p_map->second.size())
			{ 
        (this->*pfunc_TestTypeData[p_map->first.testtype])(p_map->first.servicetype,std::string(tm_file),p_map->first.strtb);

				PPM_OS::memset(tm_file, 0, sizeof(tm_file));
				count = 0;
				bOnce = 0;
			} 
		}
	}

	return TRUE;
}

