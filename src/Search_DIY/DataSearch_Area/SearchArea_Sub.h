#pragma once
#include "./SearchArea.h"

class CSearchArea_Sub : public CSearchArea
{
public:
	CSearchArea_Sub(void);
	virtual ~CSearchArea_Sub(void);

protected:
	virtual BOOL MakeLogFileGroup() = 0;
	BOOL DealDataSub();

	BOOL (CSearchArea_Sub::*pfunc_TestTypeData[256])(int iservicetype, std::string tm_file, std::string tbname);
	BOOL DealDataSub_Default(int iservicetype, std::string tm_file, std::string tbname){return true;};
	virtual BOOL DealDataSub_GSM(int iservicetype, std::string tm_file, std::string tbname) = 0;
	virtual BOOL DealDataSub_CDMA(int iservicetype, std::string tm_file, std::string tbname) = 0;
	virtual BOOL DealDataSub_CDMA2000(int iservicetype, std::string tm_file, std::string tbname) = 0;
	virtual BOOL DealDataSub_TDSCDMA(int iservicetype, std::string tm_file, std::string tbname) = 0;
	virtual BOOL DealDataSub_WCDMA(int iservicetype, std::string tm_file, std::string tbname) = 0;
	virtual BOOL DealDataSub_Model(const int sampletbid, const std::string sampletbname, const std::string ifileidSql, const BYTE bColumIDCmd, const BYTE bThirdCmd) = 0;
	virtual BOOL DealDataSub_Scan(int iservicetype, std::string tm_file, std::string tbname) = 0;
	virtual BOOL DealDataSub_WLAN(int iservicetype, std::string tm_file, std::string tbname){return true;};
	virtual BOOL DealDataSub_LTE(int iservicetype, std::string tm_file, std::string tbname) {return true;};
	virtual BOOL DealDataSub_LTE_FDD(int iservicetype, std::string tm_file, std::string tbname) {return true;};
	virtual BOOL DealDataSub_LTE_UEP(int iservicetype, std::string tm_file, std::string tbname) {return true;};
	virtual BOOL DealDataSub_Signal(int iservicetype, std::string tm_file, std::string tbname) {return true;};
	virtual BOOL DealDataSub_NR(int iservicetype, std::string tm_file, std::string tbname) { return true; };

};
