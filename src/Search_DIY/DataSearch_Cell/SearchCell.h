// SearchCell.h: interface for the CSearchCell class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_SEARCHCELL_H__F16C593B_75D2_4D9F_B18A_2F1D7563C581__INCLUDED_)
#define AFX_SEARCHCELL_H__F16C593B_75D2_4D9F_B18A_2F1D7563C581__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

#include "./SearchArea.h"

class CSearchCell : public CSearchArea
{
public:
	CSearchCell();
	virtual ~CSearchCell();


};

#endif // !defined(AFX_SEARCHCELL_H__F16C593B_75D2_4D9F_B18A_2F1D7563C581__INCLUDED_)
