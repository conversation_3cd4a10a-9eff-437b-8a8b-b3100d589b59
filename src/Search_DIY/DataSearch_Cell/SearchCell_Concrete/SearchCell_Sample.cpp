#include "SearchCell_Sample.h"

CSearchCell_Sample::<PERSON>earch<PERSON><PERSON>_Sample(void)
{
}

CSearchCell_Sample::~CSearchCell_Sample(void)
{
}


BOOL
CSearchCell_Sample::FetchSearchCondiction(const BYTE* const pData, const int nCount,int& ioffset)
{
	CHECLFALSE(CSearchArea::FetchSearchCondiction(pData, nCount, ioffset));

	CHECLFALSE(conditionMaker_Sample.InitSerchCondition(pData, nCount, ioffset));

	CHECLNULL(conditionMaker_Sample.GetSearchCondition(CSearchConditionMaker::SearchConditionType_CellSelect));

	PPM_OS::memset(&m_column, 0, sizeof(m_column));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,m_column,sizeof(m_column)));

	return TRUE;
}
