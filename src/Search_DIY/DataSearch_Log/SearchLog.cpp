// SearchLog.cpp: implementation of the CSearchLog class.
//
//////////////////////////////////////////////////////////////////////

#include "SearchLog.h"

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CSearchLog::CSearchLog()
{

}

CSearchLog::~CSearchLog()
{

}

BOOL 
CSearchLog::FetchSearchCondiction(const BYTE* const pData, const int nCount,int& ioffset)
{
	PPM_OS::memcpy(&m_ifileid,pData + ioffset,4);
	m_ifileid = MAKEINT_NETSEQ1(m_ifileid);
	MOVENEXT(ioffset,4,nCount);

	PPM_OS::memset(&m_logfile_name, 0, sizeof(m_logfile_name));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,m_logfile_name,sizeof(m_logfile_name)));

	std::string unix_logfile_name = CUtility::GBKToUtf8(m_logfile_name);
	PPM_OS::memset(&m_logfile_name, 0, sizeof(m_logfile_name));
	PPM_OS::memcpy(m_logfile_name, unix_logfile_name.c_str(), unix_logfile_name.length());

	return TRUE;
}

BOOL
CSearchLog::GetLogFile()
{
	std::string tbname = std::string(m_logfile_name);

  CColumnResult columnResult_tb_log_file;

	CHECLFALSE( columnResult_tb_log_file.Init
		( CColumnDef::tb_log_file, 
		"tb_model_log_file",									   
		m_pDataBaseDeal_,
		m_pServerDeal_,
		m_pSendBuf_)
		);

	std::string sql_ifileid = " a.ifileid = " + CStdCommond::itostr(m_ifileid);

	std::string strSQL = p_SqlMaker->Sql_Select_File_Add_Status(columnResult_tb_log_file.GetDBSearchSql("a"), tbname, sql_ifileid);

	columnResult_tb_log_file.SendColumnsIDToClient( CMD1_DIYSEARCH,
													CMD2_RESPONSE,
													RESTYPE_COLUMN_FILE_INFO);

	columnResult_tb_log_file.SearchSqlGetDataAndSendData(
													CMD1_DIYSEARCH,
													CMD2_RESPONSE,
													RESTYPE_DIY_FILE_INFO,
													strSQL,
													(CDataItem*)CDataItem_LogFile::Instance(),
													&m_pDataRows_LogFile);   

	if (0 == m_pDataRows_LogFile.size())
	{
		return FALSE;
	}

	return TRUE;
}


BOOL 
CSearchLog::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	if (FALSE == FetchSearchCondiction(pData, nCount, ioffset) 
		|| FALSE == GetLogFile()
		|| FALSE == DealDataSub())
	{
    ClearData();
		CSearch_PI::FeedBackOver(CMD1_DIYSEARCH,RESTYPE_SEARCHERROR, m_pServerDeal_);
		return FALSE;
	} 

  ClearData();
	CSearch_PI::FeedBackOver(CMD1_DIYSEARCH,RESTYPE_SEARCHEND, m_pServerDeal_);
	return TRUE;
}


BOOL
CSearchLog::ClearData()
{
	for (auto p_pDataItem = m_pDataRows_LogFile.begin(); p_pDataItem != m_pDataRows_LogFile.end(); ++p_pDataItem)
	{
		delete(*p_pDataItem);
		*p_pDataItem = NULL;
	}

	m_pDataRows_LogFile.clear();

	return TRUE;
}


