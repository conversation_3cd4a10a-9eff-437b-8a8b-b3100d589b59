// SearchLog.h: interface for the CSearchLog class.
//
//////////////////////////////////////////////////////////////////////

#ifndef __SEARCHLOG_H__
#define __SEARCHLOG_H__

#include "./SearchDIY.h"
#include "./SearchConditionMaker.h"

#define RESTYPE_COLUMN_FILE_INFO                0x11
#define	RESTYPE_DIY_FILE_INFO			              0x12

class CSearchLog : public CSearchDIY
{
public:
	CSearchLog();
	virtual ~CSearchLog();

protected:
	int m_ifileid;
	char m_logfile_name[255];
  std::vector<CDataItem*> m_pDataRows_LogFile;

public:
	virtual BOOL FetchSearchCondiction(const BYTE* const pData, const int nCount,int& ioffset);
	virtual BOOL GetLogFile();
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
	virtual BOOL DealDataSub() = 0;
	virtual BOOL ClearData();

};

#endif
