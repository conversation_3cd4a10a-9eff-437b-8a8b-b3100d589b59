#include "SearchLog_Event.h"

CSearchLog_Event::CSearchLog_Event(void)
{
}

CSearchLog_Event::~CSearchLog_Event(void)
{
}


BOOL 
CSearchLog_Event::FetchSearchCondiction(const BYTE* const pData, const int nCount,int& ioffset)
{
	CHECLFALSE(CSearchLog::FetchSearchCondiction(pData, nCount, ioffset));

	CHECLFALSE(conditionMaker_Event.InitSerchCondition(pData, nCount, ioffset));

	PPM_OS::memset(&m_column, 0, sizeof(m_column));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,m_column,sizeof(m_column)));

	return TRUE;
}

BOOL
CSearchLog_Event::DealDataSub()
{
	CDataItem_LogFile* pDataRows_LogFile =   (CDataItem_LogFile*)m_pDataRows_LogFile[0];

	if (CEnumDef::IsNrServiceType(pDataRows_LogFile->iservicetype))
	{
		DealDataSub_Model(CColumnDef::tb_model_event, pDataRows_LogFile->streventtbname, 
			RESTYPE_COLUMN_l0G_EVENT, RESTYPE_DIY_LOG_EVENT_NR);
	}
	else
	{
		DealDataSub_Model(CColumnDef::tb_model_event, pDataRows_LogFile->streventtbname,
			RESTYPE_COLUMN_l0G_EVENT, RESTYPE_DIY_LOG_EVENT);
	}

	return TRUE;
}

BOOL 
CSearchLog_Event::DealDataSub_Model(const int tbid, const std::string eventtbname, const BYTE bColumIDCmd, const BYTE bThirdCmd)
{
	CColumnResult columnResult_tb_model_event;

	CHECLFALSE(columnResult_tb_model_event.Init
		(std::string(m_column),
		CStdCommond::itostr(tbid),
		eventtbname,
		m_pDataBaseDeal_,
		m_pServerDeal_,
		m_pSendBuf_)
		);

	std::string sql_confition = conditionMaker_Event.GetSearchConfitionSql();
	if (sql_confition.length() > 0)
	{
		sql_confition += " and ifileid = " +  CStdCommond::itostr(m_ifileid);
	}
	else 
	{
		sql_confition = " ifileid = " +  CStdCommond::itostr(m_ifileid);
	}

	std::string strSQL = p_SqlMaker->Sql_Select(columnResult_tb_model_event.GetDBSearchSql(), eventtbname, sql_confition);


	columnResult_tb_model_event.SendColumnsIDToClient(CMD1_DIYSEARCH, CMD2_RESPONSE, bColumIDCmd);


	columnResult_tb_model_event.SearchSqlSendData(
		CMD1_DIYSEARCH,
		CMD2_RESPONSE,
		bThirdCmd,
		strSQL);  

	return TRUE;
}


