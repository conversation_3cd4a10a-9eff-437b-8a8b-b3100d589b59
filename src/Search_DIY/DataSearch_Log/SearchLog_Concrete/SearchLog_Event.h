#pragma once
#include "SearchLog.h"


#define RESTYPE_COLUMN_l0G_EVENT     0x20
#define RESTYPE_DIY_LOG_EVENT        0x21
#define RESTYPE_DIY_LOG_EVENT_NR     0x22


class CSearchLog_Event : public CSearchLog
{
public:
	CSearchLog_Event(void);
	virtual ~CSearchLog_Event(void);	

protected:
	CSearchConditionMaker conditionMaker_Event;
	char m_column[8000];  //获取的文件ID

protected:
	virtual BOOL FetchSearchCondiction(const BYTE* const pData, const int nCount,int& ioffset);
	virtual BOOL DealDataSub();
	virtual BOOL DealDataSub_Model(const int tbid, const std::string eventtbname, const BYTE bColumIDCmd, const BYTE bThirdCmd);
};
