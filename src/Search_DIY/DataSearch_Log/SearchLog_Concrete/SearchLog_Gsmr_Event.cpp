#include "SearchLog_Gsmr_Event.h"

CSearchLog_Gsmr_Event::CSearchLog_Gsmr_Event(void)
{
}

CSearchLog_Gsmr_Event::~CSearchLog_Gsmr_Event(void)
{
}


BOOL 
CSearchLog_Gsmr_Event::FetchSearchCondiction(const BYTE* const pData, const int nCount,int& ioffset)
{
	CHECLFALSE(CSearchLog::FetchSearchCondiction(pData, nCount, ioffset));

	CHECLFALSE(conditionMaker_Event.InitSerchCondition(pData, nCount, ioffset));

	PPM_OS::memset(&m_column, 0, sizeof(m_column));
	//CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,m_column,sizeof(m_column)));

	sprintf(m_column, "%s", "0,1,60,0,2,60,0,3,60,0,4,60,0,5,60,0,6,60,0,7,60,0,8,60,0,9,60,0,10,60,0,11,60,0,12,60,0,13,60,0,14,60,0,15,60,0,16,60,0,17,60,0,18,60,0,19,60,0,20,60,0,21,60,0,22,60,0,23,60,0,24,60,0,25,60,0,26,60,0,27,60,0,28,60,0,29,60,0,30,60,0,31,60,0,32,60,0,33,60,0,34,60,0,35,60,0,36,60");

	return TRUE;
}

BOOL
CSearchLog_Gsmr_Event::DealDataSub()
{
	CDataItem_LogFile* pDataRows_LogFile =   (CDataItem_LogFile*)m_pDataRows_LogFile[0];

	DealDataSub_Model(CColumnDef::tb_gsmr_event, pDataRows_LogFile->streventtbname,	RESTYPE_COLUMN_l0G_EVENT, RESTYPE_DIY_LOG_EVENT);

	return TRUE;
}

BOOL 
CSearchLog_Gsmr_Event::DealDataSub_Model(const int tbid, const std::string eventtbname, const BYTE bColumIDCmd, const BYTE bThirdCmd)
{
	CColumnResult columnResult_tb_model_event;

	CHECLFALSE(columnResult_tb_model_event.Init
		(std::string(m_column),
		CStdCommond::itostr(tbid),
		std::string("tb_model_gsmr_event"),
		m_pDataBaseDeal_,
		m_pServerDeal_,
		m_pSendBuf_)
		);

	std::string sql_confition = conditionMaker_Event.GetSearchConfitionSql();
	if (sql_confition.length() > 0)
	{
		sql_confition += " and a.ifileid = " +  CStdCommond::itostr(m_ifileid);
	}
	else 
	{
		sql_confition = " a.ifileid = " +  CStdCommond::itostr(m_ifileid);
	}

	char cSql[8192]{ 0 };

	sprintf(cSql, "select a.ifileid, a.iprojecttype, a.iseqid, a.itime, a.wtimems, a.bms, a.ieventid, "
		"a.ilongitude, a.ilatitude, a.icqtposid, a.ilac, a.wrac, a.ici, a.itargetlac, a.wtargetrac, "
		"a.itargetci, a.ivalue1, a.ivalue2, a.ivalue3, a.ivalue4, a.ivalue5, a.ivalue6, a.ivalue7, a.ivalue8, a.ivalue9, a.ivalue10, "
		"b.机车号 AS trainnumber, b.车次号 AS trainlinenumber, b.线路 AS trainline, b.MSISDN AS msisdn, b.IMSI AS imsi, b.stime, b.起始小区 AS startcell, b.etime, b.结束小区 AS endcell, "
		"'K'+ CONVERT(varchar(10),FLOOR(CONVERT(numeric(18,3),c.K公里标)/1000)) + '+' + CONVERT(varchar(10),FLOOR(CONVERT(numeric(18,3),c.K公里标)%%1000)) AS kilometerpost "
		"from %s a left join tb_railway_InfoRelation b on a.ifileid = b.ifileid "
		"and dateadd(s, a.itime, '19700101 8:00') >= b.stime and dateadd(s, a.itime, '19700101 8:00') <= b.etime "
		"left join tb_cfg_railway_gsmr_cell c on a.iLAC = c.LAC and a.iCI = c.CI and CHARINDEX(b.线路, c.线路) > 0 "
		"where %s",
		eventtbname.c_str(), sql_confition.c_str());



	columnResult_tb_model_event.SendGsmrEventColumnsIDToClient(CMD1_DIYSEARCH, CMD2_RESPONSE, bColumIDCmd);


	columnResult_tb_model_event.SearchSqlSendData(	   
		CMD1_DIYSEARCH,
		CMD2_RESPONSE,
		bThirdCmd,
		std::string(cSql));

	return TRUE;
}


