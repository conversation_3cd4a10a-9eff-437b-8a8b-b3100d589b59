#include "SearchLog_Msg.h"

CSearchLog_Msg::CSearchLog_Msg(void)
{
}

CSearchLog_Msg::~CSearchLog_Msg(void)
{
}


BOOL 
CSearchLog_Msg::FetchSearchCondiction(const BYTE* const pData, const int nCount,int& ioffset)
{
	CHECLFALSE(CSearchLog::FetchSearchCondiction(pData, nCount, ioffset));

	CHECLFALSE(conditionMaker_Msg.InitSerchCondition(pData, nCount, ioffset));

	PPM_OS::memset(&m_column, 0, sizeof(m_column));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,m_column,sizeof(m_column)));

	return TRUE;
}

BOOL
CSearchLog_Msg::DealDataSub()
{
	CDataItem_LogFile* pDataRows_LogFile =   (CDataItem_LogFile*)m_pDataRows_LogFile[0];

    CColumnResult columnResult_tb_model_msg;

	CHECLFALSE(   columnResult_tb_model_msg.Init
		(std::string(m_column),
		CStdCommond::itostr(CColumnDef::tb_model_msg), 
		pDataRows_LogFile->strmsgtbname,
		m_pDataBaseDeal_,
		m_pServerDeal_,
		m_pSendBuf_)
		);

	std::string sql_confition = conditionMaker_Msg.GetSearchConfitionSql();
	if (sql_confition.length() > 0)
	{
		sql_confition += " and ifileid = " +  CStdCommond::itostr(m_ifileid);
	}
	else 
	{
		sql_confition = " ifileid = " +  CStdCommond::itostr(m_ifileid);
	}

	std::string strSQL = p_SqlMaker->Sql_Select(columnResult_tb_model_msg.GetDBSearchSql(), pDataRows_LogFile->strmsgtbname, sql_confition);

	columnResult_tb_model_msg.SendColumnsIDToClient(CMD1_DIYSEARCH, CMD2_RESPONSE, RESTYPE_COLUMN_l0G_MSG);

	columnResult_tb_model_msg.SearchSqlSendData(
		CMD1_DIYSEARCH,
		CMD2_RESPONSE,
		RESTYPE_DIY_LOG_MSG,
		strSQL
		);  

	return TRUE;
}

CSearchLog_Msg_WithAllRtp::CSearchLog_Msg_WithAllRtp(void)
{
}

CSearchLog_Msg_WithAllRtp::~CSearchLog_Msg_WithAllRtp(void)
{
}

BOOL 
CSearchLog_Msg_WithAllRtp::FetchSearchCondiction(const BYTE* const pData, const int nCount,int& ioffset)
{
	CHECLFALSE(CSearchLog::FetchSearchCondiction(pData, nCount, ioffset));

	CHECLFALSE(conditionMaker_Msg.InitSerchCondition(pData, nCount, ioffset));

	PPM_OS::memset(&m_column, 0, sizeof(m_column));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,m_column,sizeof(m_column)));

	return TRUE;
}

BOOL
CSearchLog_Msg_WithAllRtp::DealDataSub()
{
	CDataItem_LogFile* pDataRows_LogFile =   (CDataItem_LogFile*)m_pDataRows_LogFile[0];

	CColumnResult columnResult_tb_model_allmsg;

	CHECLFALSE(   columnResult_tb_model_allmsg.Init
		( std::string(m_column),
		CStdCommond::itostr(CColumnDef::tb_model_msg), 
		pDataRows_LogFile->strmsgtbname,
		m_pDataBaseDeal_,
		m_pServerDeal_,
		m_pSendBuf_)
		);

	std::string sql_confition = conditionMaker_Msg.GetSearchConfitionSql();
	if (sql_confition.length() > 0)
	{
		sql_confition += " and ifileid = " +  CStdCommond::itostr(m_ifileid);
	}
	else 
	{
		sql_confition = " ifileid = " +  CStdCommond::itostr(m_ifileid);
	}

	std::string temp = pDataRows_LogFile->strmsgtbname;
	int pos = temp.find("msg");
	temp.replace(pos, 3, "rtpmsg");

#ifdef MSG_RTP 
	std::string SearchMsg = p_SqlMaker->sql_select(columnResult_tb_model_allmsg.GetDBSearchSql(), pDataRows_LogFile->strmsgtbname, sql_confition);
	std::string SearchRtp = p_SqlMaker->sql_select(columnResult_tb_model_allmsg.GetDBSearchSql(), temp, sql_confition);
	std::string strSQL = SearchMsg + " union " + SearchRtp;
#else
	std::string strSQL = p_SqlMaker->Sql_Select(columnResult_tb_model_allmsg.GetDBSearchSql(), temp, sql_confition);
#endif		

	columnResult_tb_model_allmsg.SendColumnsIDToClient(CMD1_DIYSEARCH, CMD2_RESPONSE, RESTYPE_COLUMN_l0G_MSG);

	columnResult_tb_model_allmsg.SearchSqlSendData(
		CMD1_DIYSEARCH,
		CMD2_RESPONSE,
		RESTYPE_DIY_LOG_MSG,
		strSQL
		);
	

	return TRUE;
}






