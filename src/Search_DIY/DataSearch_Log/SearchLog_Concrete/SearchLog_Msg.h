#pragma once
#include "SearchLog.h"

#define RESTYPE_COLUMN_l0G_MSG     0x20
#define RESTYPE_DIY_LOG_MSG        0x21

class CSearchLog_Msg : public CSearchLog
{
public:
	CSearchLog_Msg(void);
public:
	virtual ~CSearchLog_Msg(void);


protected:
	CSearchConditionMaker conditionMaker_Msg;
	char m_column[8000];  //获取的文件ID

protected:
	virtual BOOL FetchSearchCondiction(const BYTE* const pData, const int nCount,int& ioffset);
	virtual BOOL DealDataSub();
};


class CSearchLog_Msg_WithAllRtp : public CSearchLog
{
public:
	CSearchLog_Msg_WithAllRtp(void);
public:
	virtual ~CSearchLog_Msg_WithAllRtp(void);


protected:
	CSearchConditionMaker conditionMaker_Msg;
	char m_column[8000];  //获取的文件ID

protected:
	virtual BOOL FetchSearchCondiction(const BYTE* const pData, const int nCount,int& ioffset);
	virtual BOOL DealDataSub();
};





