#include "SearchLog_Sample.h"

CSearchLog_Sample::CSearchLog_Sample(void)
:m_mapTestTypePos(),
m_mapSerTypePos(),
m_mapScanTypePos(),
m_mapGsmTestTypePos(),
m_mapGsmDtSerTypePos(),
m_mapGsmCqtSerTypePos(),
m_mapCdmaDtSerTypePos(),
m_mapWcdmaDtSerTypePos(),
m_mapWcdmaCqtSerTypePos(),
m_mapTdscdmaDtSerTypePos(),
m_mapTdscdmaCqtSerTypePos(),
m_mapLteFddDtSerTypePos(),
m_mapLteFddCqtSerTypePos()
{
	InitTestTypeMap();
	InitServiceTypeMap();
	InitScanMap();

	InitGsmDtSerTypePosMap();
	InitGsmCqtSerTypePosMap();
	InitGsmTestTypePosMap();

	InitLteFddDtSerTypeMap();
	InitLteFddCqtSerTypeMap();

	InitWcdmaDtSerTypeMap();
	InitWcdmaCqtSerTypeMap();

	InitCdmaDtSerTypeMap();

	InitTdscdmaDtSerTypeMap();
	InitTdscdmaCqtSerTypeMap();
}

CSearchLog_Sample::~CSearchLog_Sample(void)
{
	m_mapTestTypePos.clear();
	m_mapSerTypePos.clear();
	m_mapScanTypePos.clear();
	m_mapGsmTestTypePos.clear();
	m_mapGsmDtSerTypePos.clear();
	m_mapGsmCqtSerTypePos.clear();
	m_mapLteFddDtSerTypePos.clear();
	m_mapLteFddCqtSerTypePos.clear();
	m_mapWcdmaDtSerTypePos.clear();
	m_mapWcdmaCqtSerTypePos.clear();
	m_mapCdmaDtSerTypePos.clear();
	m_mapTdscdmaDtSerTypePos.clear();
	m_mapTdscdmaCqtSerTypePos.clear();
}

BOOL 
CSearchLog_Sample::FetchSearchCondiction(const BYTE* const pData, const int nCount,int& ioffset)
{
	CHECLFALSE(CSearchLog::FetchSearchCondiction(pData, nCount, ioffset));

	CHECLFALSE(conditionMaker_Sample.InitSerchCondition(pData, nCount, ioffset));

	PPM_OS::memset(&m_column, 0, sizeof(m_column));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,m_column,sizeof(m_column)));

	return TRUE;
}

BOOL  
CSearchLog_Sample::DealDataSub()
{  
	CDataItem_LogFile* pDataRows_LogFile = (CDataItem_LogFile*)m_pDataRows_LogFile[0];

	auto it = m_mapTestTypePos.find(pDataRows_LogFile->itesttype);

	if (it != m_mapTestTypePos.end())
	{
		(this->*(it->second))(pDataRows_LogFile);
	}

	return TRUE;
}

BOOL
CSearchLog_Sample::DealDataSub_SCAN(const CDataItem_LogFile* const p_logfile)
{
	auto it = m_mapScanTypePos.find(p_logfile->iservicetype);
	if (it == m_mapScanTypePos.end())
	{
		return FALSE;
	}

	(this->*(it->second))(p_logfile);

    return TRUE;
}

BOOL 
CSearchLog_Sample::DealDataSub_GSM(const CDataItem_LogFile* const p_logfile)
{
	auto it = m_mapGsmTestTypePos.find(p_logfile->itesttype);
	if (it == m_mapGsmTestTypePos.end())
	{
		return FALSE;
	}

	BOOL bRet = (this->*(it->second))(p_logfile);

	return bRet;
}

BOOL CSearchLog_Sample::DealDataSub_Gsm_Dt(const CDataItem_LogFile* const p_logfile)
{
	auto it = m_mapGsmDtSerTypePos.find(p_logfile->iservicetype);
	if (it == m_mapGsmDtSerTypePos.end())
	{
		return FALSE;
	}

	(this->*(it->second))(p_logfile);

	return TRUE;
}
BOOL CSearchLog_Sample::DealDataSub_Gsm_Cqt(const CDataItem_LogFile* const p_logfile)
{
	auto it = m_mapGsmCqtSerTypePos.find(p_logfile->iservicetype);
	if (it == m_mapGsmCqtSerTypePos.end())
	{
		return FALSE;
	}

	(this->*(it->second))(p_logfile);

	return TRUE;
}
void CSearchLog_Sample::InitGsmTestTypePosMap(void)
{
  m_mapGsmTestTypePos.insert(std::make_pair(CEnumDef::T_GSM_DT, &CSearchLog_Sample::DealDataSub_Gsm_Dt));
	m_mapGsmTestTypePos.insert(std::make_pair(CEnumDef::T_AUTODT, &CSearchLog_Sample::DealDataSub_Gsm_Dt));
	m_mapGsmTestTypePos.insert(std::make_pair(CEnumDef::T_AUTODT_TA, &CSearchLog_Sample::DealDataSub_Gsm_Dt));
	m_mapGsmTestTypePos.insert(std::make_pair(CEnumDef::T_GSM_CQT, &CSearchLog_Sample::DealDataSub_Gsm_Cqt));
	m_mapGsmTestTypePos.insert(std::make_pair(CEnumDef::T_AUTOCQT, &CSearchLog_Sample::DealDataSub_Gsm_Cqt));
	m_mapGsmTestTypePos.insert(std::make_pair(CEnumDef::T_ATU_CQT, &CSearchLog_Sample::DealDataSub_Gsm_Cqt));
}

BOOL CSearchLog_Sample::DealDataSub_Gsm_Dt_Ser_Gsm_Idel_Voice(const CDataItem_LogFile* const p_logfile)
{
	if (p_logfile->strsampletbname.find("_sample6_") != std::string::npos)
	{
		DealDataSub_Model(CColumnDef::tb_dtgsm_sample6, p_logfile->strsampletbname, p_logfile->ifileid,
			RESTYPE_COLUMN_SAMPLE_GSM_V, RESTYPE_DIY_SAMPLE_GSM_V);
	}

	return TRUE;
}
BOOL CSearchLog_Sample::DealDataSub_Gsm_Dt_Ser_Gprs_Edge_Data(const CDataItem_LogFile* const p_logfile)
{
	if (p_logfile->strsampletbname.find("_sample5_") != std::string::npos)
	{
		DealDataSub_Model(CColumnDef::tb_dtgsm_sample5, p_logfile->strsampletbname, p_logfile->ifileid, 
			RESTYPE_COLUMN_SAMPLE_GSM_D, RESTYPE_DIY_SAMPLE_GSM_D);
	}

    return TRUE;
}
BOOL CSearchLog_Sample::DealDataSub_Gsm_Dt_Ser_Gsm_Uplink(const CDataItem_LogFile* const p_logfile)
{
	DealDataSub_Model(CColumnDef::tb_ulgsm_sample, "tb_ulgsm_sample_" + p_logfile->suffix_day, p_logfile->ifileid,
		RESTYPE_COLUMN_SAMPLE_GSM_MTR, RESTYPE_DIY_SAMPLE_GSM_MTR);
    return TRUE;
}

void CSearchLog_Sample::InitGsmDtSerTypePosMap(void)
{
    m_mapGsmDtSerTypePos.insert(std::make_pair(CEnumDef::Ser_GSM_VOICE, &CSearchLog_Sample::DealDataSub_Gsm_Dt_Ser_Gsm_Idel_Voice));
    m_mapGsmDtSerTypePos.insert(std::make_pair(CEnumDef::Ser_GSM_IDLE, &CSearchLog_Sample::DealDataSub_Gsm_Dt_Ser_Gsm_Idel_Voice));
    m_mapGsmDtSerTypePos.insert(std::make_pair(CEnumDef::Ser_GPRS_DATA, &CSearchLog_Sample::DealDataSub_Gsm_Dt_Ser_Gprs_Edge_Data));
    m_mapGsmDtSerTypePos.insert(std::make_pair(CEnumDef::Ser_EDGE_DATA, &CSearchLog_Sample::DealDataSub_Gsm_Dt_Ser_Gprs_Edge_Data));
    m_mapGsmDtSerTypePos.insert(std::make_pair(CEnumDef::Ser_GSM_UPLINK, &CSearchLog_Sample::DealDataSub_Gsm_Dt_Ser_Gsm_Uplink));
}


BOOL CSearchLog_Sample::DealDataSub_Gsm_Cqt_Ser_Gsm_Idel_Voice(const CDataItem_LogFile* const p_logfile)
{
	if (p_logfile->strsampletbname == "tb_cqtgsm_sample6_"  + p_logfile->suffix_day)
	{
		DealDataSub_Model(CColumnDef::tb_cqtgsm_sample6, p_logfile->strsampletbname, p_logfile->ifileid, 
			RESTYPE_COLUMN_SAMPLE_GSM_V, RESTYPE_DIY_SAMPLE_GSM_V);
	}	

	return TRUE;
}
BOOL CSearchLog_Sample::DealDataSub_Gsm_Cqt_Ser_Gprs_Edge_Data(const CDataItem_LogFile* const p_logfile)
{
	DealDataSub_Model(CColumnDef::tb_cqtgsm_sample5, "tb_cqtgsm_sample5_" + p_logfile->suffix_day, p_logfile->ifileid,
		RESTYPE_COLUMN_SAMPLE_GSM_D, RESTYPE_DIY_SAMPLE_GSM_D);
    return TRUE;
}
BOOL CSearchLog_Sample::DealDataSub_Gsm_Cqt_Ser_Gsm_Uplink(const CDataItem_LogFile* const p_logfile)
{
	DealDataSub_Model(CColumnDef::tb_ulgsm_sample, "tb_ulgsm_sample_" + p_logfile->suffix_day, p_logfile->ifileid,
		RESTYPE_COLUMN_SAMPLE_GSM_MTR, RESTYPE_DIY_SAMPLE_GSM_MTR);
    return TRUE;
}
void CSearchLog_Sample::InitGsmCqtSerTypePosMap(void)
{
    m_mapGsmCqtSerTypePos.insert(std::make_pair(CEnumDef::Ser_GSM_VOICE, &CSearchLog_Sample::DealDataSub_Gsm_Cqt_Ser_Gsm_Idel_Voice));
    m_mapGsmCqtSerTypePos.insert(std::make_pair(CEnumDef::Ser_GSM_IDLE, &CSearchLog_Sample::DealDataSub_Gsm_Cqt_Ser_Gsm_Idel_Voice));
    m_mapGsmCqtSerTypePos.insert(std::make_pair(CEnumDef::Ser_GPRS_DATA, &CSearchLog_Sample::DealDataSub_Gsm_Cqt_Ser_Gprs_Edge_Data));
    m_mapGsmCqtSerTypePos.insert(std::make_pair(CEnumDef::Ser_EDGE_DATA, &CSearchLog_Sample::DealDataSub_Gsm_Cqt_Ser_Gprs_Edge_Data));
    m_mapGsmCqtSerTypePos.insert(std::make_pair(CEnumDef::Ser_GSM_UPLINK, &CSearchLog_Sample::DealDataSub_Gsm_Cqt_Ser_Gsm_Uplink));
}


BOOL 
CSearchLog_Sample::DealDataSub_Signal(const CDataItem_LogFile* const p_logfile)
{
	if (p_logfile->iservicetype == CEnumDef::SER_LTE_SIGNAL)
	{
		if (p_logfile->itesttype == CEnumDef::T_Signal_DT)
		{
			DealDataSub_Model(CColumnDef::tb_dtsignal_sample, p_logfile->strsampletbname, p_logfile->ifileid,
				RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_SAMPLE_SIGNAL);
			return TRUE;
		}
		else if (p_logfile->itesttype == CEnumDef::T_Signal_CQT)
		{
			DealDataSub_Model(CColumnDef::tb_cqtsignal_sample, p_logfile->strsampletbname, p_logfile->ifileid,
				RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_SAMPLE_SIGNAL);
			return TRUE;
		}
	}

	return FALSE;
}

BOOL 
CSearchLog_Sample::DealDataSub_CDMA(const CDataItem_LogFile* const p_logfile)
{
    switch(p_logfile->itesttype)
	{
	case CEnumDef::T_CDMA_DT:
		{
			if (!DealDataSub_Cdma_Dt(p_logfile))
			{
				return FALSE;
			}
		}
		break;
	case CEnumDef::T_CDMA_CQT:
	case CEnumDef::T_ATU_CQT:
		{
			if (!DealDataSub_Cdma_Cqt(p_logfile))
			{
				return FALSE;
			}
		}
		break;
	default:
		{
			return FALSE;
		}
	}

	return TRUE;
}

BOOL 
CSearchLog_Sample::DealDataSub_Cdma_Dt(const CDataItem_LogFile* const p_logfile)
{
	auto it = m_mapCdmaDtSerTypePos.find(p_logfile->iservicetype);
	if (it == m_mapCdmaDtSerTypePos.end())
	{
		return FALSE;
	}

	(this->*(it->second))(p_logfile);

	return TRUE;
}

BOOL 
CSearchLog_Sample::DealDataSub_Cdma_Dt_Service_Data(const CDataItem_LogFile* const p_logfile)
{
	DealDataSub_Model(CColumnDef::tb_dtcdma_sample5, "tb_dtcdma_sample5_" + p_logfile->suffix_day, p_logfile->ifileid, 
		RESTYPE_COLUMN_SAMPLE_CDMA_D, RESTYPE_DIY_SAMPLE_CDMA_D);
	return TRUE;
}
BOOL 
CSearchLog_Sample::DealDataSub_Cdma_Dt_Service_Voice(const CDataItem_LogFile* const p_logfile)
{
	DealDataSub_Model(CColumnDef::tb_dtcdma_sample, "tb_dtcdma_sample_" + p_logfile->suffix_day, p_logfile->ifileid,
		RESTYPE_COLUMN_SAMPLE_CDMA_V, RESTYPE_DIY_SAMPLE_CDMA_V);
	return TRUE;
}
void 
CSearchLog_Sample::InitCdmaDtSerTypeMap(void)
{
  m_mapCdmaDtSerTypePos.insert(std::make_pair(CEnumDef::Ser_CDMA_VOICE, &CSearchLog_Sample::DealDataSub_Cdma_Dt_Service_Voice));
	m_mapCdmaDtSerTypePos.insert(std::make_pair(CEnumDef::Ser_CDMA_IDLE, &CSearchLog_Sample::DealDataSub_Cdma_Dt_Service_Voice));
	m_mapCdmaDtSerTypePos.insert(std::make_pair(CEnumDef::Ser_CDMA1X_DATA, &CSearchLog_Sample::DealDataSub_Cdma_Dt_Service_Data));
}


BOOL 
CSearchLog_Sample::DealDataSub_Cdma_Cqt(const CDataItem_LogFile* const p_logfile)
{
	switch(p_logfile->iservicetype)
	{
	case CEnumDef::Ser_CDMA_VOICE:
		{
			DealDataSub_Model(CColumnDef::tb_cqtcdma_sample, "tb_cqtcdma_sample_" + p_logfile->suffix_day, p_logfile->ifileid,
				RESTYPE_COLUMN_SAMPLE_CDMA_V, RESTYPE_DIY_SAMPLE_CDMA_V);
		}
		break;

	case CEnumDef::Ser_CDMA1X_DATA:
		{
			DealDataSub_Model(CColumnDef::tb_cqtcdma_sample5, "tb_cqtcdma_sample5_" + p_logfile->suffix_day, p_logfile->ifileid,
				RESTYPE_COLUMN_SAMPLE_CDMA_D, RESTYPE_DIY_SAMPLE_CDMA_D);
		}
		break;

	default:
		{
			return FALSE;
		}
	}

	return TRUE;
}

BOOL 
CSearchLog_Sample::DealDataSub_WCDMA(const CDataItem_LogFile* const p_logfile)
{
	switch(p_logfile->itesttype)
	{
	case CEnumDef::T_WCDMA_DT:
		{
			if (!DealDataSub_Wcdma_Dt_Service(p_logfile))
			{
				return FALSE;
			}
		}
		break;
	case CEnumDef::T_WCDMA_CQT:
	case CEnumDef::T_ATU_CQT:
		{
			if (!DealDataSub_Wcdma_Cqt_Service(p_logfile))
			{
				return FALSE;
			}
		}
		break;
	default:
		{
			return FALSE;
		}
	}

	return TRUE;
}

BOOL 
CSearchLog_Sample::DealDataSub_Wcdma_Dt_Service(const CDataItem_LogFile* const p_logfile)
{
	auto it = m_mapWcdmaDtSerTypePos.find(p_logfile->iservicetype);
	if (it == m_mapWcdmaDtSerTypePos.end())
	{
		return FALSE;
	}

	(this->*(it->second))(p_logfile);

	return TRUE;
}


void
CSearchLog_Sample::InitWcdmaDtSerTypeMap(void)
{
   m_mapWcdmaDtSerTypePos.insert(std::make_pair(CEnumDef::Ser_WCDMA_VOICE, &CSearchLog_Sample::DealDataSub_Wcdma_Dt_Service_Voice));
   m_mapWcdmaDtSerTypePos.insert(std::make_pair(CEnumDef::Ser_WCDMA_VIDEO, &CSearchLog_Sample::DealDataSub_Wcdma_Dt_Service_Voice));
   m_mapWcdmaDtSerTypePos.insert(std::make_pair(CEnumDef::Ser_WCDMA_IDLE, &CSearchLog_Sample::DealDataSub_Wcdma_Dt_Service_Voice));
   m_mapWcdmaDtSerTypePos.insert(std::make_pair(CEnumDef::Ser_WCDMA_DATA, &CSearchLog_Sample::DealDataSub_Wcdma_Dt_Service_Data));
   m_mapWcdmaDtSerTypePos.insert(std::make_pair(CEnumDef::Ser_WCDMA_HSDPA, &CSearchLog_Sample::DealDataSub_Wcdma_Dt_Service_Data));
   m_mapWcdmaDtSerTypePos.insert(std::make_pair(CEnumDef::Ser_WCDMA_HSUPA, &CSearchLog_Sample::DealDataSub_Wcdma_Dt_Service_Data));
}

BOOL
CSearchLog_Sample::DealDataSub_Wcdma_Dt_Service_Data(const CDataItem_LogFile* const p_logfile)
{
	DealDataSub_Model(CColumnDef::tb_dtwcdma_sample5, "tb_dtwcdma_sample5_" + p_logfile->suffix_day, p_logfile->ifileid,
		RESTYPE_COLUMN_SAMPLE_WCDMA_D, RESTYPE_DIY_SAMPLE_WCDMA_D);
	return TRUE;
}

BOOL
CSearchLog_Sample::DealDataSub_Wcdma_Dt_Service_Voice(const CDataItem_LogFile* const p_logfile)
{
	DealDataSub_Model(CColumnDef::tb_dtwcdma_sample, "tb_dtwcdma_sample_" + p_logfile->suffix_day, p_logfile->ifileid,
		RESTYPE_COLUMN_SAMPLE_WCDMA_V, RESTYPE_DIY_SAMPLE_WCDMA_V);
	return TRUE;
}

BOOL 
CSearchLog_Sample::DealDataSub_Wcdma_Cqt_Service(const CDataItem_LogFile* const p_logfile)
{
	auto it = m_mapWcdmaCqtSerTypePos.find(p_logfile->iservicetype);
	if (it == m_mapWcdmaCqtSerTypePos.end())
	{
		return FALSE;
	}

	(this->*(it->second))(p_logfile);

	return TRUE;
}


BOOL 
CSearchLog_Sample::DealDataSub_Wcdma_Cqt_Service_Data(const CDataItem_LogFile* const p_logfile)
{
	DealDataSub_Model(CColumnDef::tb_cqtwcdma_sample5, "tb_cqtwcdma_sample5_" + p_logfile->suffix_day, p_logfile->ifileid,
		RESTYPE_COLUMN_SAMPLE_WCDMA_D, RESTYPE_DIY_SAMPLE_WCDMA_D);
	return TRUE;
}

BOOL 
CSearchLog_Sample::DealDataSub_Wcdma_Cqt_Service_Voice(const CDataItem_LogFile* const p_logfile)
{
	DealDataSub_Model(CColumnDef::tb_cqtwcdma_sample, "tb_cqtwcdma_sample_" + p_logfile->suffix_day, p_logfile->ifileid,
		RESTYPE_COLUMN_SAMPLE_WCDMA_V, RESTYPE_DIY_SAMPLE_WCDMA_V);
	return TRUE;
}

void 
CSearchLog_Sample::InitWcdmaCqtSerTypeMap(void)
{
	m_mapWcdmaCqtSerTypePos.insert(std::make_pair(CEnumDef::Ser_WCDMA_VOICE, &CSearchLog_Sample::DealDataSub_Wcdma_Cqt_Service_Voice));
	m_mapWcdmaCqtSerTypePos.insert(std::make_pair(CEnumDef::Ser_WCDMA_VIDEO, &CSearchLog_Sample::DealDataSub_Wcdma_Cqt_Service_Voice));
	m_mapWcdmaCqtSerTypePos.insert(std::make_pair(CEnumDef::Ser_WCDMA_IDLE, &CSearchLog_Sample::DealDataSub_Wcdma_Cqt_Service_Voice));
	m_mapWcdmaCqtSerTypePos.insert(std::make_pair(CEnumDef::Ser_WCDMA_DATA, &CSearchLog_Sample::DealDataSub_Wcdma_Cqt_Service_Data));
	m_mapWcdmaCqtSerTypePos.insert(std::make_pair(CEnumDef::Ser_WCDMA_HSDPA, &CSearchLog_Sample::DealDataSub_Wcdma_Cqt_Service_Data));
	m_mapWcdmaCqtSerTypePos.insert(std::make_pair(CEnumDef::Ser_WCDMA_HSUPA, &CSearchLog_Sample::DealDataSub_Wcdma_Cqt_Service_Data));
}


BOOL 
CSearchLog_Sample::DealDataSub_TDSCDMA(const CDataItem_LogFile* const p_logfile)
{
	switch(p_logfile->itesttype)
	{
	case CEnumDef::T_TDSCDMA_DT:
		{
			if (!DealDataSub_Tdscdma_Dt(p_logfile))
			{
				return FALSE;
			}
		}
		break;
	case CEnumDef::T_TDSCDMA_CQT:
	case CEnumDef::T_ATU_CQT:
		{
			if (!DealDataSub_Tdscdma_Cqt(p_logfile))
			{
				return FALSE;
			}
		}
		break;
	default:
		{
			return FALSE;
		}
	}

	return TRUE;
}

BOOL 
CSearchLog_Sample::DealDataSub_Tdscdma_Dt(const CDataItem_LogFile* const p_logfile)
{
	auto it = m_mapTdscdmaDtSerTypePos.find(p_logfile->iservicetype);
	if (it == m_mapTdscdmaDtSerTypePos.end())
	{
		return FALSE;
	}

	(this->*(it->second))(p_logfile);

	return TRUE;
}

BOOL 
CSearchLog_Sample::DealDataSub_Tdscdma_Dt_Service_Data(const CDataItem_LogFile* const p_logfile)
{
	DealDataSub_Model(CColumnDef::tb_dttdscdma_sample5, "tb_dttdscdma_sample5_"  + p_logfile->suffix_day, p_logfile->ifileid,
		RESTYPE_COLUMN_SAMPLE_TDSCDMA_D, RESTYPE_DIY_SAMPLE_TDSCDMA_D);
	return TRUE;
}

BOOL 
CSearchLog_Sample::DealDataSub_Tdscdma_Dt_Service_Voice(const CDataItem_LogFile* const p_logfile)
{
	if (p_logfile->strsampletbname == "tb_dttdscdma_sample_"  + p_logfile->suffix_day)
	{
		DealDataSub_Model(CColumnDef::tb_dttdscdma_sample, p_logfile->strsampletbname, p_logfile->ifileid,
			RESTYPE_COLUMN_SAMPLE_TDSCDMA_V, RESTYPE_DIY_SAMPLE_TDSCDMA_V);
	}
	else if (p_logfile->strsampletbname == "tb_dttdscdma_sample6_"  + p_logfile->suffix_day)
	{
		DealDataSub_Model(CColumnDef::tb_dttdscdma_sample6, p_logfile->strsampletbname, p_logfile->ifileid,
			RESTYPE_COLUMN_SAMPLE_TDSCDMA_V, RESTYPE_DIY_SAMPLE_TDSCDMA_V);
	}			

	return TRUE;
}

void 
CSearchLog_Sample::InitTdscdmaDtSerTypeMap(void)
{
  m_mapTdscdmaDtSerTypePos.insert(std::make_pair(CEnumDef::Ser_TDSCDMA_VOICE, &CSearchLog_Sample::DealDataSub_Tdscdma_Dt_Service_Voice));
	m_mapTdscdmaDtSerTypePos.insert(std::make_pair(CEnumDef::Ser_TDSCDMA_VIDEO, &CSearchLog_Sample::DealDataSub_Tdscdma_Dt_Service_Voice));
	m_mapTdscdmaDtSerTypePos.insert(std::make_pair(CEnumDef::Ser_TDSCDMA_IDLE, &CSearchLog_Sample::DealDataSub_Tdscdma_Dt_Service_Voice));
	m_mapTdscdmaDtSerTypePos.insert(std::make_pair(CEnumDef::Ser_TDSCDMA_DATA, &CSearchLog_Sample::DealDataSub_Tdscdma_Dt_Service_Data));
	m_mapTdscdmaDtSerTypePos.insert(std::make_pair(CEnumDef::Ser_TDSCDMA_HSDPA, &CSearchLog_Sample::DealDataSub_Tdscdma_Dt_Service_Data));
	m_mapTdscdmaDtSerTypePos.insert(std::make_pair(CEnumDef::Ser_TDSCDMA_HSUPA, &CSearchLog_Sample::DealDataSub_Tdscdma_Dt_Service_Data));
}


BOOL 
CSearchLog_Sample::DealDataSub_Tdscdma_Cqt(const CDataItem_LogFile* const p_logfile)
{
	auto it = m_mapTdscdmaCqtSerTypePos.find(p_logfile->iservicetype);
	if (it == m_mapTdscdmaCqtSerTypePos.end())
	{
		return FALSE;
	}

	(this->*(it->second))(p_logfile);

	return TRUE;
}

BOOL 
CSearchLog_Sample::DealDataSub_Tdscdma_Cqt_Service_Data(const CDataItem_LogFile* const p_logfile)
{
	DealDataSub_Model(CColumnDef::tb_cqttdscdma_sample5, "tb_cqttdscdma_sample5_"  + p_logfile->suffix_day, 
		p_logfile->ifileid, RESTYPE_COLUMN_SAMPLE_TDSCDMA_D, RESTYPE_DIY_SAMPLE_TDSCDMA_D);
	return TRUE;
}

BOOL 
CSearchLog_Sample::DealDataSub_Tdscdma_Cqt_Service_Voice(const CDataItem_LogFile* const p_logfile)
{
	if (p_logfile->strsampletbname == "tb_cqttdscdma_sample_"  + p_logfile->suffix_day)
	{
		DealDataSub_Model(CColumnDef::tb_cqttdscdma_sample, p_logfile->strsampletbname, p_logfile->ifileid,
			RESTYPE_COLUMN_SAMPLE_TDSCDMA_V, RESTYPE_DIY_SAMPLE_TDSCDMA_V);
	}
	else if (p_logfile->strsampletbname == "tb_cqttdscdma_sample6_"  + p_logfile->suffix_day)
	{
		DealDataSub_Model(CColumnDef::tb_cqttdscdma_sample6, p_logfile->strsampletbname, p_logfile->ifileid, 
			RESTYPE_COLUMN_SAMPLE_TDSCDMA_V, RESTYPE_DIY_SAMPLE_TDSCDMA_V);
	}

	return TRUE;
}

void 
CSearchLog_Sample::InitTdscdmaCqtSerTypeMap(void)
{
	m_mapTdscdmaCqtSerTypePos.insert(std::make_pair(CEnumDef::Ser_TDSCDMA_VOICE, &CSearchLog_Sample::DealDataSub_Tdscdma_Cqt_Service_Voice));
	m_mapTdscdmaCqtSerTypePos.insert(std::make_pair(CEnumDef::Ser_TDSCDMA_VIDEO, &CSearchLog_Sample::DealDataSub_Tdscdma_Cqt_Service_Voice));
	m_mapTdscdmaCqtSerTypePos.insert(std::make_pair(CEnumDef::Ser_TDSCDMA_IDLE, &CSearchLog_Sample::DealDataSub_Tdscdma_Cqt_Service_Voice));
	m_mapTdscdmaCqtSerTypePos.insert(std::make_pair(CEnumDef::Ser_TDSCDMA_DATA, &CSearchLog_Sample::DealDataSub_Tdscdma_Cqt_Service_Data));
	m_mapTdscdmaCqtSerTypePos.insert(std::make_pair(CEnumDef::Ser_TDSCDMA_HSDPA, &CSearchLog_Sample::DealDataSub_Tdscdma_Cqt_Service_Data));
	m_mapTdscdmaCqtSerTypePos.insert(std::make_pair(CEnumDef::Ser_TDSCDMA_HSUPA, &CSearchLog_Sample::DealDataSub_Tdscdma_Cqt_Service_Data));

}


BOOL 
CSearchLog_Sample::DealDataSub_CDMA2000(const CDataItem_LogFile* const p_logfile)
{
  switch(p_logfile->itesttype)
	{
	case CEnumDef::T_CDMA2000_DT:
		{
			if (!DealDataSub_Cdma2000_Dt(p_logfile))
			{
				return FALSE;
			}
		}
		break;
	case CEnumDef::T_CDMA2000_CQT:
	case CEnumDef::T_ATU_CQT:
		{
			if (!DealDataSub_Cdma2000_Cqt(p_logfile))
			{
				return FALSE;
			}
		}
		break;
	default:
		{
			return FALSE;
		}
	}

	return TRUE;
}

BOOL 
CSearchLog_Sample::DealDataSub_Cdma2000_Dt(const CDataItem_LogFile* const p_logfile)
{
	switch(p_logfile->iservicetype)
	{
	case CEnumDef::Ser_CDMA2000_VOICE:
	case CEnumDef::Ser_CDMA2000_VIDEO:
	case CEnumDef::Ser_CDMA2000_IDLE:
		{
			DealDataSub_Model(CColumnDef::tb_dtevdo_sample, "tb_dtevdo_sample_" + p_logfile->suffix_day, 
				p_logfile->ifileid, RESTYPE_COLUMN_SAMPLE_CDMA2000_V, RESTYPE_DIY_SAMPLE_CDMA2000_V);
		}
		break;

	case CEnumDef::Ser_CDMA2000_DATA:
		{
			DealDataSub_Model(CColumnDef::tb_dtevdo_sample5, "tb_dtevdo_sample5_" + p_logfile->suffix_day,
				p_logfile->ifileid, RESTYPE_COLUMN_SAMPLE_CDMA2000_D, RESTYPE_DIY_SAMPLE_CDMA2000_D);
		}
		break;

	default:
		{
			return FALSE;
		}
	}

	return TRUE;
}

BOOL 
CSearchLog_Sample::DealDataSub_Cdma2000_Cqt(const CDataItem_LogFile* const p_logfile)
{
	switch(p_logfile->iservicetype)
	{
	case CEnumDef::Ser_CDMA2000_VOICE:
	case CEnumDef::Ser_CDMA2000_VIDEO:
	case CEnumDef::Ser_CDMA2000_IDLE:
		{
			DealDataSub_Model(CColumnDef::tb_cqtevdo_sample, "tb_cqtevdo_sample_" + p_logfile->suffix_day, 
				p_logfile->ifileid, RESTYPE_COLUMN_SAMPLE_CDMA2000_V, RESTYPE_DIY_SAMPLE_CDMA2000_V);
		}
		break;

	case CEnumDef::Ser_CDMA2000_DATA:
		{
			DealDataSub_Model(CColumnDef::tb_cqtevdo_sample5, "tb_cqtevdo_sample5_" + p_logfile->suffix_day, 
				p_logfile->ifileid, RESTYPE_COLUMN_SAMPLE_CDMA2000_D, RESTYPE_DIY_SAMPLE_CDMA2000_D);
		}
		break;

	default:
		{
			return FALSE;
		}
	}

	return TRUE;
}


BOOL 
CSearchLog_Sample::DealDataSub_WLAN(const CDataItem_LogFile* const p_logfile)
{
	switch(p_logfile->itesttype)
	{
	case CEnumDef::T_WLAN_DT:
		{
			switch(p_logfile->iservicetype)
			{
			case CEnumDef::Ser_WLAN:
				{
					DealDataSub_Model(CColumnDef::tb_dtwlan_sample, "tb_dtwlan_sample_" + p_logfile->suffix_day,
						p_logfile->ifileid, RESTYPE_COLUMN_SAMPLE_WLAN, RESTYPE_DIY_SAMPLE_WLAN);
				}
				break;

			default:
				{
					return FALSE;
				}
			}
		}
		break;
	case CEnumDef::T_WLAN_CQT:
		{
			switch(p_logfile->iservicetype)
			{
			case CEnumDef::Ser_WLAN:
				{
					DealDataSub_Model(CColumnDef::tb_cqtwlan_sample, "tb_cqtwlan_sample_" + p_logfile->suffix_day,
						p_logfile->ifileid, RESTYPE_COLUMN_SAMPLE_WLAN, RESTYPE_DIY_SAMPLE_WLAN);
				}
				break;

			default:
				{
					return FALSE;
				}
			}
		}
		break;
	default:
		{
			return FALSE;
		}
	}
	return TRUE;

}

BOOL 
CSearchLog_Sample::DealDataSub_LTE(const CDataItem_LogFile* const p_logfile)
{
	switch(p_logfile->itesttype)
	{
	case CEnumDef::T_LTE_DT:
		{
			if (!DealDataSub_LTE_Dt(p_logfile))
			{
				return FALSE;
			}
		}
		break;
	case CEnumDef::T_LTE_CQT:
		{
			if (!DealDataSub_LTE_Cqt(p_logfile))
			{
				return FALSE;
			}
		}
		break;

	case CEnumDef::T_NBIOT_DT:
		{
			if (!DealDataSub_LTE_NB_Dt(p_logfile))
			{
				return FALSE;
			}
		}
		break;

	case CEnumDef::T_NBIOT_CQT:
		{
			if (!DealDataSub_LTE_NB_Cqt(p_logfile))
			{
				return FALSE;
			}
		}
		break;

	default:
		{
			return FALSE;
		}
	}
	return TRUE;

}

BOOL 
CSearchLog_Sample::DealDataSub_LTE_Dt(const CDataItem_LogFile* const p_logfile)
{
	if (CEnumDef::IsLteTddServiceType(p_logfile->iservicetype))
	{
		DealDataSub_Model(CColumnDef::tb_dtlte_sample, "tb_dtlte_sample_" + p_logfile->suffix_day,
			p_logfile->ifileid, RESTYPE_COLUMN_SAMPLE_LTE, RESTYPE_DIY_SAMPLE_LTE);

		return TRUE;
	}
			
	return FALSE;
}

BOOL 
CSearchLog_Sample::DealDataSub_LTE_Cqt(const CDataItem_LogFile* const p_logfile)
{
	if (CEnumDef::IsLteTddServiceType(p_logfile->iservicetype))
	{
		DealDataSub_Model(CColumnDef::tb_cqtlte_sample, "tb_cqtlte_sample_" + p_logfile->suffix_day,
			p_logfile->ifileid, RESTYPE_COLUMN_SAMPLE_LTE, RESTYPE_DIY_SAMPLE_LTE);
		return TRUE;
	}
			
	return FALSE;
}

BOOL 
CSearchLog_Sample::DealDataSub_LTE_NB_Dt(const CDataItem_LogFile* const p_logfile)
{
	switch(p_logfile->iservicetype)
	{
	case CEnumDef::Ser_NBIOT_DATA:
		{
			DealDataSub_Model(CColumnDef::tb_dtlte_sample, "tb_dtlte_sample_" + p_logfile->suffix_day,
				p_logfile->ifileid, RESTYPE_COLUMN_SAMPLE_LTE, RESTYPE_DIY_SAMPLE_LTE);
		}
		break;	

	default:
		{
			return FALSE;
		}
	}

	return TRUE;
}

BOOL 
CSearchLog_Sample::DealDataSub_LTE_NB_Cqt(const CDataItem_LogFile* const p_logfile)
{
	switch(p_logfile->iservicetype)
	{
	case CEnumDef::Ser_NBIOT_DATA:
		{
			DealDataSub_Model(CColumnDef::tb_cqtlte_sample, "tb_cqtlte_sample_" + p_logfile->suffix_day,
				p_logfile->ifileid, RESTYPE_COLUMN_SAMPLE_LTE, RESTYPE_DIY_SAMPLE_LTE);
		}
		break;	

	default:
		{
			return FALSE;
		}
	}

	return TRUE;
}

BOOL 
CSearchLog_Sample::DealDataSub_LTE_UEP(const CDataItem_LogFile* const p_logfile)
{
	switch(p_logfile->itesttype)
	{
	case CEnumDef::T_LTE_UEP:
		{
			switch(p_logfile->iservicetype)
			{
			case CEnumDef::Ser_LTE_TDD_UEP:
				{
					DealDataSub_Model(CColumnDef::tb_lte_uep_sample, "tb_lte_uep_sample_" + p_logfile->suffix_day,
						p_logfile->ifileid, RESTYPE_COLUMN_DEFINE, RESTYPE_DIY_SAMPLE_LTE_UEP);
				}
				break;			

			default:
				{
					return FALSE;
				}
			}
		}
		break;
	
	default:
		{
			return FALSE;
		}
	}
	return TRUE;

}

BOOL 
CSearchLog_Sample::DealDataSub_LTE_FDD(const CDataItem_LogFile* const p_logfile)
{
	switch(p_logfile->itesttype)
	{
	case CEnumDef::T_LTE_FDD_DT:
		{
			if (!DealDataSub_LTE_FDD_Dt(p_logfile))
			{
				return FALSE;
			}
		}
		break;
	case CEnumDef::T_LTE_FDD_CQT:
		{
			if (!DealDataSub_LTE_FDD_Cqt(p_logfile))
			{
				return FALSE;
			}
		}
		break;
	default:
		{
			return FALSE;
		}
	}
	return TRUE;

}

BOOL 
CSearchLog_Sample::DealDataSub_LTE_FDD_Dt(const CDataItem_LogFile* const p_logfile)
{
	auto it = m_mapLteFddDtSerTypePos.find(p_logfile->iservicetype);

	if (it == m_mapLteFddDtSerTypePos.end())
	{
		return FALSE;
	}

	BOOL bRet = (this->*(it->second))(p_logfile);
	return bRet;
}

BOOL 
CSearchLog_Sample::DealDataSub_LTE_FDD_Dt_Service(const CDataItem_LogFile* const p_logfile)
{
	DealDataSub_Model(CColumnDef::tb_dtlte_fdd_sample, "tb_dtlte_fdd_sample_" + p_logfile->suffix_day,
		p_logfile->ifileid, RESTYPE_COLUMN_SAMPLE_LTE, RESTYPE_DIY_SAMPLE_LTE_FDD);
    return TRUE;
}

void CSearchLog_Sample::InitLteFddDtSerTypeMap(void)
{
    m_mapLteFddDtSerTypePos.insert(std::make_pair(CEnumDef::Ser_LTE_FDD_VOICE, &CSearchLog_Sample::DealDataSub_LTE_FDD_Dt_Service));
    m_mapLteFddDtSerTypePos.insert(std::make_pair(CEnumDef::Ser_LTE_FDD_DATA, &CSearchLog_Sample::DealDataSub_LTE_FDD_Dt_Service));
    m_mapLteFddDtSerTypePos.insert(std::make_pair(CEnumDef::Ser_LTE_FDD_IDLE, &CSearchLog_Sample::DealDataSub_LTE_FDD_Dt_Service));
    m_mapLteFddDtSerTypePos.insert(std::make_pair(CEnumDef::Ser_LTE_FDD_MULTI, &CSearchLog_Sample::DealDataSub_LTE_FDD_Dt_Service));
    m_mapLteFddDtSerTypePos.insert(std::make_pair(CEnumDef::Ser_LTE_FDD_VOLTE, &CSearchLog_Sample::DealDataSub_LTE_FDD_Dt_Service));
    m_mapLteFddDtSerTypePos.insert(std::make_pair(CEnumDef::Ser_LTE_FDD_VIDEO_VOLTE, &CSearchLog_Sample::DealDataSub_LTE_FDD_Dt_Service));
}

BOOL 
CSearchLog_Sample::DealDataSub_LTE_FDD_Cqt(const CDataItem_LogFile* const p_logfile)
{
	auto it = m_mapLteFddCqtSerTypePos.find(p_logfile->iservicetype);

	if (it == m_mapLteFddCqtSerTypePos.end())
	{
		return FALSE;
	}

	BOOL bRet = (this->*(it->second))(p_logfile);
    return bRet;
}

BOOL 
CSearchLog_Sample::DealDataSub_LTE_FDD_Cqt_Service(const CDataItem_LogFile* const p_logfile)
{
	DealDataSub_Model(CColumnDef::tb_cqtlte_fdd_sample, "tb_cqtlte_fdd_sample_" + p_logfile->suffix_day,
		p_logfile->ifileid, RESTYPE_COLUMN_SAMPLE_LTE, RESTYPE_DIY_SAMPLE_LTE_FDD);
    return TRUE;
}

void CSearchLog_Sample::InitLteFddCqtSerTypeMap(void)
{
    m_mapLteFddCqtSerTypePos.insert(std::make_pair(CEnumDef::Ser_LTE_FDD_VOICE, &CSearchLog_Sample::DealDataSub_LTE_FDD_Cqt_Service));
    m_mapLteFddCqtSerTypePos.insert(std::make_pair(CEnumDef::Ser_LTE_FDD_DATA, &CSearchLog_Sample::DealDataSub_LTE_FDD_Cqt_Service));
    m_mapLteFddCqtSerTypePos.insert(std::make_pair(CEnumDef::Ser_LTE_FDD_IDLE, &CSearchLog_Sample::DealDataSub_LTE_FDD_Cqt_Service));
    m_mapLteFddCqtSerTypePos.insert(std::make_pair(CEnumDef::Ser_LTE_FDD_MULTI, &CSearchLog_Sample::DealDataSub_LTE_FDD_Cqt_Service));
    m_mapLteFddCqtSerTypePos.insert(std::make_pair(CEnumDef::Ser_LTE_FDD_VOLTE, &CSearchLog_Sample::DealDataSub_LTE_FDD_Cqt_Service));
    m_mapLteFddCqtSerTypePos.insert(std::make_pair(CEnumDef::Ser_LTE_FDD_VIDEO_VOLTE, &CSearchLog_Sample::DealDataSub_LTE_FDD_Cqt_Service));
}

//NR
BOOL
CSearchLog_Sample::DealDataSub_NR_TDD(const CDataItem_LogFile* const p_logfile)
{
	switch (p_logfile->itesttype)
	{
	case CEnumDef::T_NR_TDD_DT:
	{
		if (!DealDataSub_NR_TDD_Dt(p_logfile))
		{
			return FALSE;
		}
	}
	break;
	case CEnumDef::T_NR_TDD_CQT:
	{
		if (!DealDataSub_NR_TDD_Cqt(p_logfile))
		{
			return FALSE;
		}
	}
	break;

	default:
		return FALSE;
	}
	return TRUE;
}

BOOL
CSearchLog_Sample::DealDataSub_NR_TDD_Dt(const CDataItem_LogFile* const p_logfile)
{
	if (CEnumDef::IsNrServiceType(p_logfile->iservicetype))
	{
		DealDataSub_Model(CColumnDef::tb_dtnr_sample, "tb_dtnr_tdd_sample_" + p_logfile->suffix_day,
			p_logfile->ifileid, RESTYPE_COLUMN_SAMPLE_NR, RESTYPE_DIY_SAMPLE_NR);
		return TRUE;
	}

	return FALSE;
}

BOOL
CSearchLog_Sample::DealDataSub_NR_TDD_Cqt(const CDataItem_LogFile* const p_logfile)
{
	if (CEnumDef::IsNrServiceType(p_logfile->iservicetype))
	{
		DealDataSub_Model(CColumnDef::tb_cqtnr_sample, "tb_cqtnr_tdd_sample_" + p_logfile->suffix_day,
			p_logfile->ifileid, RESTYPE_COLUMN_SAMPLE_NR, RESTYPE_DIY_SAMPLE_NR);
		return TRUE;
	}

	return FALSE;
}


BOOL 
CSearchLog_Sample::DealDataSub_Model(const int sampletbid, const std::string sampletbname, const int ifileid, const BYTE bColumIDCmd, const BYTE bThirdCmd)
{
	std::string strSQL = "";

	std::vector<STRU_COLUMNID> vec_ColumnID;
	CHECLFALSE(ChangeTableID(std::string(m_column), sampletbid, vec_ColumnID));

	std::vector<int> vec_TableID;
	vec_TableID.push_back(sampletbid);

	std::vector<std::string> vec_TableName;
	vec_TableName.push_back(sampletbname);
  
  CColumnResult columnResult_sample;

	CHECLFALSE(columnResult_sample.Init
		(vec_ColumnID,
		vec_TableID,
		vec_TableName,
		m_pDataBaseDeal_,
		m_pServerDeal_,
		m_pSendBuf_)
		);
  
	std::string sql_confition = conditionMaker_Sample.GetSearchConfitionSql();
	if (sql_confition.length() > 0)
	{
		sql_confition += " and ifileid = " +  CStdCommond::itostr(ifileid);
	}
	else 
	{
		sql_confition = " ifileid = " +  CStdCommond::itostr(ifileid);
	}

	strSQL = p_SqlMaker->Sql_Select(columnResult_sample.GetDBSearchSql(), sampletbname, sql_confition);

	columnResult_sample.SendColumnsIDToClient(CMD1_DIYSEARCH, CMD2_RESPONSE, bColumIDCmd);

	columnResult_sample.SearchSqlSendData(	   
						CMD1_DIYSEARCH,
						CMD2_RESPONSE,
						bThirdCmd,
						strSQL);  

	return TRUE;
}

BOOL
CSearchLog_Sample::ClearData(void)
{
	CSearchLog::ClearData();
	conditionMaker_Sample.ClearItem();
	return TRUE;
}

BOOL
CSearchLog_Sample::ChangeTableID(std::string columnid, const int tableid, std::vector<STRU_COLUMNID>& vec_ColumnID)
{
  vec_ColumnID.clear();

	if (0 == columnid.size())
	{
		return FALSE;
	}

  CColumnItem* p_FileidColumn = CColumnDef::Instance()->GetColumn(tableid, "ifileid");
	CHECLNULL(p_FileidColumn);  
	columnid = CStdCommond::itostr(p_FileidColumn->imageID) + "," + CStdCommond::itostr(p_FileidColumn->paraID) + "," + 
		CStdCommond::itostr(p_FileidColumn->tbID) + "," + columnid;

	std::vector<std::string> vec_str = CStdCommond::str_slipt(columnid, ",");

	if (vec_str.size()%3 != 0)
	{
		return FALSE;
	}

	for (auto p_vecStr = vec_str.begin(); p_vecStr != vec_str.end(); p_vecStr += 3)
	{
		STRU_COLUMNID temp;
		temp.imageID = atoi((*p_vecStr).c_str());
		temp.paraID = atoi((*(p_vecStr + 1)).c_str());
		temp.tbID = tableid;
		vec_ColumnID.push_back(temp);
	}

	return TRUE;
}

BOOL 
CSearchLog_Sample::DealDataSub_ATU_CQT(const CDataItem_LogFile* const p_logfile)
{
	auto it = m_mapSerTypePos.find(p_logfile->iservicetype);

	if (it != m_mapSerTypePos.end())
	{
		(this->*(it->second))(p_logfile);
	}

  return TRUE;
}

void 
CSearchLog_Sample::InitTestTypeMap(void)
{
	m_mapTestTypePos.insert(std::make_pair(CEnumDef::T_GSM_DT, &CSearchLog_Sample::DealDataSub_GSM));
	m_mapTestTypePos.insert(std::make_pair(CEnumDef::T_GSM_CQT, &CSearchLog_Sample::DealDataSub_GSM));
	m_mapTestTypePos.insert(std::make_pair(CEnumDef::T_AUTODT, &CSearchLog_Sample::DealDataSub_GSM));
	m_mapTestTypePos.insert(std::make_pair(CEnumDef::T_AUTODT_TA, &CSearchLog_Sample::DealDataSub_GSM));
	m_mapTestTypePos.insert(std::make_pair(CEnumDef::T_AUTOCQT, &CSearchLog_Sample::DealDataSub_GSM));

	m_mapTestTypePos.insert(std::make_pair(CEnumDef::T_SCANTEST, &CSearchLog_Sample::DealDataSub_SCAN));

	m_mapTestTypePos.insert(std::make_pair(CEnumDef::T_CDMA_DT, &CSearchLog_Sample::DealDataSub_CDMA));
	m_mapTestTypePos.insert(std::make_pair(CEnumDef::T_CDMA_CQT, &CSearchLog_Sample::DealDataSub_CDMA));

	m_mapTestTypePos.insert(std::make_pair(CEnumDef::T_CDMA2000_CQT, &CSearchLog_Sample::DealDataSub_CDMA2000));
	m_mapTestTypePos.insert(std::make_pair(CEnumDef::T_CDMA2000_DT, &CSearchLog_Sample::DealDataSub_CDMA2000));

	m_mapTestTypePos.insert(std::make_pair(CEnumDef::T_TDSCDMA_DT, &CSearchLog_Sample::DealDataSub_TDSCDMA));
	m_mapTestTypePos.insert(std::make_pair(CEnumDef::T_TDSCDMA_CQT, &CSearchLog_Sample::DealDataSub_TDSCDMA));

	m_mapTestTypePos.insert(std::make_pair(CEnumDef::T_WCDMA_DT, &CSearchLog_Sample::DealDataSub_WCDMA));
	m_mapTestTypePos.insert(std::make_pair(CEnumDef::T_WCDMA_CQT, &CSearchLog_Sample::DealDataSub_WCDMA));

	m_mapTestTypePos.insert(std::make_pair(CEnumDef::T_WLAN_CQT, &CSearchLog_Sample::DealDataSub_WLAN));
	m_mapTestTypePos.insert(std::make_pair(CEnumDef::T_WLAN_DT, &CSearchLog_Sample::DealDataSub_WLAN));

	m_mapTestTypePos.insert(std::make_pair(CEnumDef::T_LTE_CQT, &CSearchLog_Sample::DealDataSub_LTE));
	m_mapTestTypePos.insert(std::make_pair(CEnumDef::T_LTE_DT, &CSearchLog_Sample::DealDataSub_LTE));
	m_mapTestTypePos.insert(std::make_pair(CEnumDef::T_NBIOT_CQT, &CSearchLog_Sample::DealDataSub_LTE));
	m_mapTestTypePos.insert(std::make_pair(CEnumDef::T_NBIOT_DT, &CSearchLog_Sample::DealDataSub_LTE));

	m_mapTestTypePos.insert(std::make_pair(CEnumDef::T_LTE_FDD_CQT, &CSearchLog_Sample::DealDataSub_LTE_FDD));
	m_mapTestTypePos.insert(std::make_pair(CEnumDef::T_LTE_FDD_DT, &CSearchLog_Sample::DealDataSub_LTE_FDD));

	m_mapTestTypePos.insert(std::make_pair(CEnumDef::T_ATU_CQT, &CSearchLog_Sample::DealDataSub_ATU_CQT));

	m_mapTestTypePos.insert(std::make_pair(CEnumDef::T_LTE_UEP, &CSearchLog_Sample::DealDataSub_LTE_UEP));

	m_mapTestTypePos.insert(std::make_pair(CEnumDef::T_Signal_DT, &CSearchLog_Sample::DealDataSub_Signal));
	m_mapTestTypePos.insert(std::make_pair(CEnumDef::T_Signal_CQT, &CSearchLog_Sample::DealDataSub_Signal));

	m_mapTestTypePos.insert(std::make_pair(CEnumDef::T_NR_TDD_DT, &CSearchLog_Sample::DealDataSub_NR_TDD));
	m_mapTestTypePos.insert(std::make_pair(CEnumDef::T_NR_TDD_CQT, &CSearchLog_Sample::DealDataSub_NR_TDD));

}
void CSearchLog_Sample::InitServiceTypeMap(void)
{
	//T_ATU_CQT
	m_mapSerTypePos.insert(std::make_pair(CEnumDef::Ser_GSM_VOICE, &CSearchLog_Sample::DealDataSub_GSM));
	m_mapSerTypePos.insert(std::make_pair(CEnumDef::Ser_GPRS_DATA, &CSearchLog_Sample::DealDataSub_GSM));
	m_mapSerTypePos.insert(std::make_pair(CEnumDef::Ser_EDGE_DATA, &CSearchLog_Sample::DealDataSub_GSM));
	m_mapSerTypePos.insert(std::make_pair(CEnumDef::Ser_GSM_IDLE, &CSearchLog_Sample::DealDataSub_GSM));

	m_mapSerTypePos.insert(std::make_pair(CEnumDef::Ser_TDSCDMA_VOICE, &CSearchLog_Sample::DealDataSub_TDSCDMA));
	m_mapSerTypePos.insert(std::make_pair(CEnumDef::Ser_TDSCDMA_DATA, &CSearchLog_Sample::DealDataSub_TDSCDMA));
	m_mapSerTypePos.insert(std::make_pair(CEnumDef::Ser_TDSCDMA_VIDEO, &CSearchLog_Sample::DealDataSub_TDSCDMA));
	m_mapSerTypePos.insert(std::make_pair(CEnumDef::Ser_TDSCDMA_IDLE, &CSearchLog_Sample::DealDataSub_TDSCDMA));
	m_mapSerTypePos.insert(std::make_pair(CEnumDef::Ser_TDSCDMA_HSDPA, &CSearchLog_Sample::DealDataSub_TDSCDMA));
	m_mapSerTypePos.insert(std::make_pair(CEnumDef::Ser_TDSCDMA_HSUPA, &CSearchLog_Sample::DealDataSub_TDSCDMA));

	m_mapSerTypePos.insert(std::make_pair(CEnumDef::Ser_CDMA_VOICE, &CSearchLog_Sample::DealDataSub_CDMA));
	m_mapSerTypePos.insert(std::make_pair(CEnumDef::Ser_CDMA_IDLE, &CSearchLog_Sample::DealDataSub_CDMA));
	m_mapSerTypePos.insert(std::make_pair(CEnumDef::Ser_CDMA1X_DATA, &CSearchLog_Sample::DealDataSub_CDMA));

	m_mapSerTypePos.insert(std::make_pair(CEnumDef::Ser_CDMA2000_VOICE, &CSearchLog_Sample::DealDataSub_CDMA2000));
	m_mapSerTypePos.insert(std::make_pair(CEnumDef::Ser_CDMA2000_DATA, &CSearchLog_Sample::DealDataSub_CDMA2000));
	m_mapSerTypePos.insert(std::make_pair(CEnumDef::Ser_CDMA2000_VIDEO, &CSearchLog_Sample::DealDataSub_CDMA2000));

	m_mapSerTypePos.insert(std::make_pair(CEnumDef::Ser_WCDMA_VOICE, &CSearchLog_Sample::DealDataSub_WCDMA));
	m_mapSerTypePos.insert(std::make_pair(CEnumDef::Ser_WCDMA_DATA, &CSearchLog_Sample::DealDataSub_WCDMA));
	m_mapSerTypePos.insert(std::make_pair(CEnumDef::Ser_WCDMA_VIDEO, &CSearchLog_Sample::DealDataSub_WCDMA));
	m_mapSerTypePos.insert(std::make_pair(CEnumDef::Ser_WCDMA_HSDPA, &CSearchLog_Sample::DealDataSub_WCDMA));
	m_mapSerTypePos.insert(std::make_pair(CEnumDef::Ser_WCDMA_IDLE, &CSearchLog_Sample::DealDataSub_WCDMA));
	m_mapSerTypePos.insert(std::make_pair(CEnumDef::Ser_WCDMA_HSUPA, &CSearchLog_Sample::DealDataSub_WCDMA));

}

void
CSearchLog_Sample::InitScanMap(void)
{
	m_mapScanTypePos.insert(std::make_pair(CEnumDef::Ser_SCAN, &CSearchLog_Sample::DealData_SCAN_GSM));
	m_mapScanTypePos.insert(std::make_pair(CEnumDef::Ser_SCAN_TD, &CSearchLog_Sample::DealData_SCAN_TDSCDMA));
	m_mapScanTypePos.insert(std::make_pair(CEnumDef::Ser_SCAN_WCDMA, &CSearchLog_Sample::DealData_SCAN_WCDMA));
	m_mapScanTypePos.insert(std::make_pair(CEnumDef::Ser_LTE_SCAN_TOPN, &CSearchLog_Sample::DealData_SCAN_LTE));
	m_mapScanTypePos.insert(std::make_pair(CEnumDef::Ser_NBIOT_SCAN_TOPN, &CSearchLog_Sample::DealData_SCAN_NB));
	m_mapScanTypePos.insert(std::make_pair(CEnumDef::Ser_SCAN_NR, &CSearchLog_Sample::DealData_SCAN_NR));
	m_mapScanTypePos.insert(std::make_pair(CEnumDef::Ser_LTE_SCAN_CW, &CSearchLog_Sample::DealData_SCAN_CW));

	m_mapScanTypePos.insert(std::make_pair(CEnumDef::SER_SCAN_NRFREQ, &CSearchLog_Sample::DealData_SCAN_FREQSPECTRUM));
	m_mapScanTypePos.insert(std::make_pair(CEnumDef::Ser_TD_SCAN_FREQSPECTRUM, &CSearchLog_Sample::DealData_SCAN_FREQSPECTRUM));
	m_mapScanTypePos.insert(std::make_pair(CEnumDef::Ser_LTE_SCAN_FREQSPECTRUM, &CSearchLog_Sample::DealData_SCAN_FREQSPECTRUM));
	m_mapScanTypePos.insert(std::make_pair(CEnumDef::Ser_GSM_SCAN_FREQSPECTRUM, &CSearchLog_Sample::DealData_SCAN_FREQSPECTRUM));
}

BOOL CSearchLog_Sample::DealData_SCAN_GSM(const CDataItem_LogFile* const p_logfile)
{
    DealDataSub_Model(CColumnDef::tb_scan_sample2, p_logfile->strsampletbname2, p_logfile->ifileid, 
        RESTYPE_COLUMN_SAMPLE_GSM_SCAN, RESTYPE_DIY_SAMPLE_GSM_SCAN);
    return TRUE;
}

BOOL CSearchLog_Sample::DealData_SCAN_TDSCDMA(const CDataItem_LogFile* const p_logfile)
{
    DealDataSub_Model(CColumnDef::tb_scan_td_sample, p_logfile->strsampletbname, p_logfile->ifileid, 
        RESTYPE_COLUMN_SAMPLE_TDSCDMA_SCAN, RESTYPE_DIY_SAMPLE_TDSCDMA_SCAN);
    return TRUE;
}

BOOL CSearchLog_Sample::DealData_SCAN_WCDMA(const CDataItem_LogFile* const p_logfile)
{
    DealDataSub_Model(CColumnDef::tb_scan_w_sample, p_logfile->strsampletbname, p_logfile->ifileid, 
        RESTYPE_COLUMN_SAMPLE_WCDMA_SCAN, RESTYPE_DIY_SAMPLE_WCDMA_SCAN);
    return TRUE;
}

BOOL CSearchLog_Sample::DealData_SCAN_LTE(const CDataItem_LogFile* const p_logfile)
{
    DealDataSub_Model(CColumnDef::tb_scan_lte_sample, p_logfile->strsampletbname, p_logfile->ifileid, 
        RESTYPE_COLUMN_SAMPLE_LTE_SCAN, RESTYPE_DIY_SAMPLE_LTE_SCAN);
    return TRUE;
}

BOOL CSearchLog_Sample::DealData_SCAN_NB(const CDataItem_LogFile* const p_logfile)
{
    DealDataSub_Model(CColumnDef::tb_scan_nbiot_topn_sample, p_logfile->strsampletbname, p_logfile->ifileid,
        RESTYPE_COLUMN_SAMPLE_SCAN_NBIOT_TOPN, RESTYPE_DIY_SAMPLE_SCAN_NBIOT_TOPN);
    return TRUE;
}

BOOL CSearchLog_Sample::DealData_SCAN_NR(const CDataItem_LogFile* const p_logfile)
{
	DealDataSub_Model(CColumnDef::tb_scan_nr_sample, p_logfile->strsampletbname, p_logfile->ifileid,
		RESTYPE_COLUMN_SAMPLE_NR_SCAN, RESTYPE_DIY_SAMPLE_NR_SCAN);
	return TRUE;
}

BOOL CSearchLog_Sample::DealData_SCAN_CW(const CDataItem_LogFile* const p_logfile)
{
    DealDataSub_Model(CColumnDef::tb_scan_cw_measure_sample, p_logfile->strsampletbname, p_logfile->ifileid,
        RESTYPE_COLUMN_SAMPLE_CW_SCAN, RESTYPE_DIY_SAMPLE_CW_SCAN);
    return TRUE;
}

BOOL CSearchLog_Sample::DealData_SCAN_FREQSPECTRUM(const CDataItem_LogFile* const p_logfile)
{
	DealDataSub_Model(CColumnDef::tb_scan_FreqSpectrum_sample, p_logfile->strsampletbname, p_logfile->ifileid,
		RESTYPE_COLUMN_SAMPLE_FREQSPECTURM_SCAN, RESTYPE_DIY_SAMPLE_FREQSPECTURM_SCAN);
    return TRUE;
}






