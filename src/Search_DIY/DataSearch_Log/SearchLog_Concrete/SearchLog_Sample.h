#pragma once
#include "SearchLog.h"

#define RESTYPE_COLUMN_SAMPLE_CDMA_V               0x20
#define RESTYPE_DIY_SAMPLE_CDMA_V                  0x21

#define RESTYPE_COLUMN_SAMPLE_CDMA_D               0x22
#define RESTYPE_DIY_SAMPLE_CDMA_D                  0x23

#define RESTYPE_COLUMN_SAMPLE_TDSCDMA_V            0x24
#define RESTYPE_DIY_SAMPLE_TDSCDMA_V               0x25

#define RESTYPE_COLUMN_SAMPLE_TDSCDMA_D            0x26
#define RESTYPE_DIY_SAMPLE_TDSCDMA_D               0x27

#define RESTYPE_COLUMN_SAMPLE_WCDMA_V              0x28
#define RESTYPE_DIY_SAMPLE_WCDMA_V                 0x29

#define RESTYPE_COLUMN_SAMPLE_WCDMA_D              0x2a
#define RESTYPE_DIY_SAMPLE_WCDMA_D                 0x2b

#define RESTYPE_COLUMN_SAMPLE_CDMA2000_D           0x2c
#define RESTYPE_DIY_SAMPLE_CDMA2000_D              0x2d

#define RESTYPE_COLUMN_SAMPLE_TDSCDMA_SCAN         0x2e
#define RESTYPE_DIY_SAMPLE_TDSCDMA_SCAN            0x2f

#define RESTYPE_COLUMN_SAMPLE_CDMA2000_V           0x30
#define RESTYPE_DIY_SAMPLE_CDMA2000_V              0x31

#define RESTYPE_COLUMN_SAMPLE_GSM_SCAN             0x32
#define RESTYPE_DIY_SAMPLE_GSM_SCAN                0x33

#define RESTYPE_COLUMN_SAMPLE_WCDMA_SCAN           0x34
#define RESTYPE_DIY_SAMPLE_WCDMA_SCAN              0x35

#define RESTYPE_COLUMN_SAMPLE_GSM_V                0x36
#define RESTYPE_DIY_SAMPLE_GSM_V                   0x37

#define RESTYPE_COLUMN_SAMPLE_GSM_D                0x38
#define RESTYPE_DIY_SAMPLE_GSM_D                   0x39

#define RESTYPE_COLUMN_SAMPLE_GSM_MTR              0x3a
#define RESTYPE_DIY_SAMPLE_GSM_MTR                 0x3b

#define RESTYPE_COLUMN_SAMPLE_TDSCDMA_CALLTRACE    0x3c
#define RESTYPE_DIY_SAMPLE_TDSCDMA_CALLTRACE       0x3d

#define RESTYPE_COLUMN_SAMPLE_WLAN                 0x3e
#define RESTYPE_DIY_SAMPLE_WLAN                    0x3f

#define RESTYPE_COLUMN_SAMPLE_LTE                  0x40
#define RESTYPE_DIY_SAMPLE_LTE                     0x41

#define RESTYPE_COLUMN_SAMPLE_LTE_SCAN             0x42
#define RESTYPE_DIY_SAMPLE_LTE_SCAN                0x43

#define RESTYPE_COLUMN_SAMPLE_CW_SCAN              0x44
#define RESTYPE_DIY_SAMPLE_CW_SCAN                 0x45

#define RESTYPE_COLUMN_SAMPLE_FREQSPECTURM_SCAN    0x46
#define RESTYPE_DIY_SAMPLE_FREQSPECTURM_SCAN       0x47

#define RESTYPE_DIY_SAMPLE_LTE_UEP                 0x49

#define RESTYPE_DIY_SAMPLE_LTE_FDD                 0x4A

#define RESTYPE_DIY_SAMPLE_SIGNAL				           0x4B

#define RESTYPE_COLUMN_SAMPLE_SCAN_NBIOT_TOPN      0x4C
#define RESTYPE_DIY_SAMPLE_SCAN_NBIOT_TOPN         0x4D

#define RESTYPE_COLUMN_SAMPLE_NR                   0x4E
#define RESTYPE_DIY_SAMPLE_NR                      0x4F

#define RESTYPE_COLUMN_SAMPLE_NR_SCAN              0x50
#define RESTYPE_DIY_SAMPLE_NR_SCAN                 0x51

class CSearchLog_Sample : public CSearchLog
{
public:
	CSearchLog_Sample(void);
public:
	virtual ~CSearchLog_Sample(void);

protected:
	virtual BOOL FetchSearchCondiction(const BYTE* const pData, const int nCount,int& ioffset);
  virtual BOOL DealDataSub();
	virtual BOOL DealDataSub_GSM(const CDataItem_LogFile* const p_logfile);
	virtual BOOL DealDataSub_CDMA(const CDataItem_LogFile* const p_logfile);
	virtual BOOL DealDataSub_CDMA2000(const CDataItem_LogFile* const p_logfile);
	virtual BOOL DealDataSub_TDSCDMA(const CDataItem_LogFile* const p_logfile);
	virtual BOOL DealDataSub_WCDMA(const CDataItem_LogFile* const p_logfile);
	virtual BOOL DealDataSub_SCAN(const CDataItem_LogFile* const p_logfile);
	virtual BOOL DealDataSub_WLAN(const CDataItem_LogFile* const p_logfile);
	virtual BOOL DealDataSub_LTE(const CDataItem_LogFile* const p_logfile);
	virtual BOOL DealDataSub_LTE_UEP(const CDataItem_LogFile* const p_logfile);
	virtual BOOL DealDataSub_LTE_FDD(const CDataItem_LogFile* const p_logfile);
	virtual BOOL DealDataSub_NR_TDD(const CDataItem_LogFile* const p_logfile);

	virtual BOOL DealDataSub_Signal(const CDataItem_LogFile* const p_logfile);
	virtual BOOL DealDataSub_Model(const int sampletbid, const std::string sampletbname, const int ifileid, const BYTE bColumIDCmd, const BYTE bThirdCmd);
  virtual BOOL ClearData(void);
	BOOL ChangeTableID(std::string columnid, const int tableid, std::vector<STRU_COLUMNID>& vec_ColumnID);
	BOOL DealDataSub_ATU_CQT(const CDataItem_LogFile* const p_logfile);

protected:
	char m_column[8000];  //获取的文件ID
	CSearchConditionMaker conditionMaker_Sample;

private:
	typedef BOOL (CSearchLog_Sample::*DealSearchLog)(const CDataItem_LogFile* const p_logfile);
	std::map<unsigned int, DealSearchLog> m_mapTestTypePos;
	std::map<unsigned int, DealSearchLog> m_mapSerTypePos;
	std::map<unsigned int, DealSearchLog> m_mapScanTypePos;

	std::map<unsigned int, DealSearchLog> m_mapGsmTestTypePos;
	std::map<unsigned int, DealSearchLog> m_mapGsmDtSerTypePos;
	std::map<unsigned int, DealSearchLog> m_mapGsmCqtSerTypePos;

	std::map<unsigned int, DealSearchLog> m_mapCdmaDtSerTypePos;

	std::map<unsigned int, DealSearchLog> m_mapWcdmaDtSerTypePos;
	std::map<unsigned int, DealSearchLog> m_mapWcdmaCqtSerTypePos;

	std::map<unsigned int, DealSearchLog> m_mapTdscdmaDtSerTypePos;
	std::map<unsigned int, DealSearchLog> m_mapTdscdmaCqtSerTypePos;

	std::map<unsigned int, DealSearchLog> m_mapLteFddDtSerTypePos;
	std::map<unsigned int, DealSearchLog> m_mapLteFddCqtSerTypePos;

	void InitTestTypeMap(void);
	void InitServiceTypeMap(void);
	void InitScanMap(void);

	BOOL DealData_SCAN_GSM(const CDataItem_LogFile* const p_logfile);
	BOOL DealData_SCAN_TDSCDMA(const CDataItem_LogFile* const p_logfile);
	BOOL DealData_SCAN_WCDMA(const CDataItem_LogFile* const p_logfile);
	BOOL DealData_SCAN_LTE(const CDataItem_LogFile* const p_logfile);
	BOOL DealData_SCAN_NB(const CDataItem_LogFile* const p_logfile);
	BOOL DealData_SCAN_CW(const CDataItem_LogFile* const p_logfile);
	BOOL DealData_SCAN_NR(const CDataItem_LogFile* const p_logfile);
	BOOL DealData_SCAN_FREQSPECTRUM(const CDataItem_LogFile* const p_logfile);

	BOOL DealDataSub_Gsm_Dt(const CDataItem_LogFile* const p_logfile);
	BOOL DealDataSub_Gsm_Cqt(const CDataItem_LogFile* const p_logfile);
	void InitGsmTestTypePosMap(void);

	BOOL DealDataSub_Gsm_Dt_Ser_Gsm_Idel_Voice(const CDataItem_LogFile* const p_logfile);
	BOOL DealDataSub_Gsm_Dt_Ser_Gprs_Edge_Data(const CDataItem_LogFile* const p_logfile);
	BOOL DealDataSub_Gsm_Dt_Ser_Gsm_Uplink(const CDataItem_LogFile* const p_logfile);
	void InitGsmDtSerTypePosMap(void);

	BOOL DealDataSub_Gsm_Cqt_Ser_Gsm_Idel_Voice(const CDataItem_LogFile* const p_logfile);
	BOOL DealDataSub_Gsm_Cqt_Ser_Gprs_Edge_Data(const CDataItem_LogFile* const p_logfile);
	BOOL DealDataSub_Gsm_Cqt_Ser_Gsm_Uplink(const CDataItem_LogFile* const p_logfile);
	void InitGsmCqtSerTypePosMap(void);

	BOOL DealDataSub_Cdma_Dt(const CDataItem_LogFile* const p_logfile);
	BOOL DealDataSub_Cdma_Cqt(const CDataItem_LogFile* const p_logfile);

	BOOL DealDataSub_Wcdma_Dt_Service(const CDataItem_LogFile* const p_logfile);
	BOOL DealDataSub_Wcdma_Cqt_Service(const CDataItem_LogFile* const p_logfile);

	BOOL DealDataSub_Tdscdma_Dt(const CDataItem_LogFile* const p_logfile);
	BOOL DealDataSub_Tdscdma_Cqt(const CDataItem_LogFile* const p_logfile);

	BOOL DealDataSub_Cdma2000_Dt(const CDataItem_LogFile* const p_logfile);
	BOOL DealDataSub_Cdma2000_Cqt(const CDataItem_LogFile* const p_logfile);

	BOOL DealDataSub_LTE_Dt(const CDataItem_LogFile* const p_logfile);
	BOOL DealDataSub_LTE_Cqt(const CDataItem_LogFile* const p_logfile);
	BOOL DealDataSub_LTE_NB_Dt(const CDataItem_LogFile* const p_logfile);
	BOOL DealDataSub_LTE_NB_Cqt(const CDataItem_LogFile* const p_logfile);

	BOOL DealDataSub_LTE_FDD_Dt(const CDataItem_LogFile* const p_logfile);
	BOOL DealDataSub_LTE_FDD_Dt_Service(const CDataItem_LogFile* const p_logfile);
	BOOL DealDataSub_LTE_FDD_Cqt(const CDataItem_LogFile* const p_logfile);
	BOOL DealDataSub_LTE_FDD_Cqt_Service(const CDataItem_LogFile* const p_logfile);
	void InitLteFddDtSerTypeMap(void);
	void InitLteFddCqtSerTypeMap(void);

	BOOL DealDataSub_Wcdma_Dt_Service_Data(const CDataItem_LogFile* const p_logfile);
	BOOL DealDataSub_Wcdma_Dt_Service_Voice(const CDataItem_LogFile* const p_logfile);
	void InitWcdmaDtSerTypeMap(void);

	BOOL DealDataSub_Wcdma_Cqt_Service_Data(const CDataItem_LogFile* const p_logfile);
	BOOL DealDataSub_Wcdma_Cqt_Service_Voice(const CDataItem_LogFile* const p_logfile);
	void InitWcdmaCqtSerTypeMap(void);

	BOOL DealDataSub_Tdscdma_Dt_Service_Data(const CDataItem_LogFile* const p_logfile);
	BOOL DealDataSub_Tdscdma_Dt_Service_Voice(const CDataItem_LogFile* const p_logfile);
	void InitTdscdmaDtSerTypeMap(void);

	BOOL DealDataSub_Tdscdma_Cqt_Service_Data(const CDataItem_LogFile* const p_logfile);
	BOOL DealDataSub_Tdscdma_Cqt_Service_Voice(const CDataItem_LogFile* const p_logfile);
	void InitTdscdmaCqtSerTypeMap(void);

	BOOL DealDataSub_Cdma_Dt_Service_Data(const CDataItem_LogFile* const p_logfile);
	BOOL DealDataSub_Cdma_Dt_Service_Voice(const CDataItem_LogFile* const p_logfile);
	void InitCdmaDtSerTypeMap(void);

	BOOL DealDataSub_NR_TDD_Dt(const CDataItem_LogFile* const p_logfile);
	BOOL DealDataSub_NR_TDD_Cqt(const CDataItem_LogFile* const p_logfile);
};
