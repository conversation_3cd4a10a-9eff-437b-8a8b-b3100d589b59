#include "SearchModel.h"
#include "./DtSockServer.h"

CSearchModel::CSearchModel(void)
{
}

CSearchModel::~CSearchModel(void)
{
}

BOOL
CSearchModel::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	if (FALSE == FetchSearchCondiction(pData, nCount, ioffset) 
		|| FALSE == DealDataSub())
	{
		ClearData();
		CSearch_PI::FeedBackOver(CMD1_DIYSEARCH,RESTYPE_SEARCHERROR, m_pServerDeal_);
		return FALSE;
	} 

	ClearData();
	CSearch_PI::FeedBackOver(CMD1_DIYSEARCH,RESTYPE_SEARCHEND, m_pServerDeal_);
	return TRUE;
}

BOOL 
CSearchModel::RowResultDeal(int nQType)
{

	return TRUE;
}

