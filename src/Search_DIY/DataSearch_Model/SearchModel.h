#pragma once
#include "SearchDIY.h"

class CSearchModel :   public CSearchDIY
{
public:
	CSearchModel(void);
public:
	virtual ~CSearchModel(void);

public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);

protected:
	virtual BOOL FetchSearchCondiction(const BYTE* const pData, const int nCount,int& ioffset) = 0;
	virtual BOOL ClearData(void) = 0;
	virtual BOOL DealDataSub(void) = 0;
	virtual BOOL RowResultDeal(int nQType);
    
};
