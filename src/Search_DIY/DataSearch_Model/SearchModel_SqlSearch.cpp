#include "SearchModel_SqlSearch.h"
// #include "./ConfigSetting.h"
#include "./StdSqlOpHelper.h"
#include "./StdNetBuffer.h"
#include "Strutils.h"

CSearchModel_SqlSearch::CSearchModel_SqlSearch(void)
{
}

CSearchModel_SqlSearch::~CSearchModel_SqlSearch(void)
{
    ClearData();
}

BOOL
CSearchModel_SqlSearch::FetchSearchCondiction(const BYTE* const pData, const int nCount,int& ioffset)
{
  char sql[8000];
 	PPM_OS::memset(&sql, 0, sizeof(sql));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,sql,sizeof(sql)));
	m_strSQL = std::string(sql);

	char fetchdataTemp[2000];
	PPM_OS::memset(&fetchdataTemp, 0, sizeof(fetchdataTemp));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,fetchdataTemp,sizeof(fetchdataTemp)));
	m_strFetchDataParas = std::string(fetchdataTemp);

	if (0 == m_strSQL.length())
	{
		return FALSE;
	}

	if (0 == m_strFetchDataParas.length())
	{
		return FALSE;
	}

	return TRUE;
}

BOOL
CSearchModel_SqlSearch::DealDataSub(void)
{
	if (CheckIsSqlNoQuery(m_strSQL))
	{
		PPM_OS::memset(m_pSendBuf_,0,SEND_BUF_MAXSIZE);

		CStdSqlOpHelper sqlOpHelper(&m_dbConnInfo);
    	int res = sqlOpHelper.ExecuteNonQuery(m_strSQL.c_str());;

		//构造信息发送
		int tm_ioffset = 0;

		m_pSendBuf_[tm_ioffset++] = CMD1_DIYSEARCH;//命令字
		m_pSendBuf_[tm_ioffset++] = CMD2_RESPONSE;//子命令字
		m_pSendBuf_[tm_ioffset++] = res >= 0?RESTYPE_DIYSEARCH_SQL_SUCCESS:RESTYPE_DIYSEARCH_SQL_FAIL;//响应类型

		if(0 > m_pServerDeal_->SendData(m_pSendBuf_,tm_ioffset)){
			return FALSE;
		}
	}
	else
	{
		if (!SendInfo()){
			return FALSE;
		}
	}

	return TRUE;
}

BOOL CSearchModel_SqlSearch::SendInfo(void)
{
	std::vector<std::string> typeVec = CStrutils::Split(m_strFetchDataParas, ",", true, true);
	CStdSqlOpHelper sqlOpHelper(&m_dbConnInfo);
	int res = sqlOpHelper.ExecuteReader(m_strSQL.c_str());
	if (res == FALSE){
		return FALSE;
	}

	size_t colCnt = sqlOpHelper.GetColumnCount();
	if(typeVec.size() != colCnt){
		return FALSE;
	}

	while(sqlOpHelper.Read())
	{
		//发送结果集列信息
		int ioffset = 0;
		PPM_OS::memset(m_pSendBuf_,0,SEND_BUF_MAXSIZE);

		CStdNetBufferWriter writer(m_pSendBuf_);
		CHECLFALSE(writer.PushByte(ioffset, CMD1_DIYSEARCH, SEND_BUF_MAXSIZE))
		CHECLFALSE(writer.PushByte(ioffset, CMD2_RESPONSE, SEND_BUF_MAXSIZE))
		CHECLFALSE(writer.PushByte(ioffset, RESTYPE_DIYSEARCH_SQL, SEND_BUF_MAXSIZE))

		for (size_t i = 0; i < colCnt; ++i)
		{
			int lenth = 0;
			CHECLFALSE(sqlOpHelper.PushToBufferWihtNetSeq(m_pSendBuf_, ioffset, atoi(typeVec[i].c_str()), SEND_BUF_MAXSIZE, i, lenth))
		}
		if(0 > m_pServerDeal_->SendData(m_pSendBuf_,ioffset)){
			return FALSE;
		}
	}

	return TRUE;
}

BOOL 
CSearchModel_SqlSearch::CheckIsSqlNoQuery(std::string sql)
{
	sql = CStrutils::Trim(sql);
	CStrutils::ToLowerString(sql);
	if (CStrutils::Strnicmp(sql, "delete ", 7) == 0
		|| CStrutils::Strnicmp(sql, "update ", 7) == 0
		|| CStrutils::Strnicmp(sql, "insert ", 7) == 0)
	{
		return TRUE;
	}
	return FALSE;
}


//////////////////////////////////////////////////////////////////////////
CSearchModel_SqlSearch_ForMainDB::CSearchModel_SqlSearch_ForMainDB(void)
{
}

CSearchModel_SqlSearch_ForMainDB::~CSearchModel_SqlSearch_ForMainDB(void)
{
	ClearData();
}


BOOL
CSearchModel_SqlSearch_ForMainDB::FetchSearchCondiction(const BYTE* const pData, const int nCount,int& ioffset)
{
	char sql[8000];
	PPM_OS::memset(&sql, 0, sizeof(sql));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,sql,sizeof(sql)))
	m_strSQL = CUtility::GBKToUtf8(sql);

	char fetchdataTemp[2000];
	PPM_OS::memset(&fetchdataTemp, 0, sizeof(fetchdataTemp));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,fetchdataTemp,sizeof(fetchdataTemp)))
	m_strFetchDataParas = CUtility::GBKToUtf8(fetchdataTemp);

	if (0 == m_strSQL.length())
	{
		return FALSE;
	}

	#ifdef USE_MYSQL
	m_strSQL = m_strSQL + ";";
	#else
	m_strSQL = " use " + std::string(CConfigSetting::Instance()->m_stru_MainDBSetting_.pchDBName) + " " + m_strSQL;
	#endif

	if (0 == m_strFetchDataParas.length() || !m_pDataBaseDeal_->ResetDBConnInfo(CConfigSetting::Instance()->m_stru_MainDBSetting_) || !ResetDBConnInfo())
	{
		return FALSE;
	}

	return TRUE;
}

BOOL
CSearchModel_SqlSearch_ForMainDB::ResetDBConnInfo()
{
	DBConnInfo m_stru_MainDBSetting_ = CConfigSetting::Instance()->m_stru_MainDBSetting_;
	memcpy(m_dbConnInfo.pchDBName, m_stru_MainDBSetting_.pchDBName, sizeof(m_dbConnInfo.pchDBName)-1);
	memcpy(m_dbConnInfo.pchDBServer, m_stru_MainDBSetting_.pchDBServer, sizeof(m_dbConnInfo.pchDBServer)-1);
	memcpy(m_dbConnInfo.pchUserName, m_stru_MainDBSetting_.pchUserName, sizeof(m_dbConnInfo.pchUserName)-1);
	memcpy(m_dbConnInfo.pchUserPassword, m_stru_MainDBSetting_.pchPassword, sizeof(m_dbConnInfo.pchUserPassword)-1);
	m_dbConnInfo.nPort = m_stru_MainDBSetting_.nPort;
	m_dbConnInfo.DisplaySql = m_stru_MainDBSetting_.nDisplaySql;
	return true;
}

BOOL 
CSearchModel_SqlSearch_ForMainDB::CheckIsSqlNoQuery(std::string sql)
{
	CStrutils::ToLowerString(sql);
	if (sql.find(" delete ") != sql.npos
	|| sql.find(" update ") != sql.npos
	|| sql.find(" insert ") != sql.npos)
	{
	return TRUE;
	}
	return FALSE;
}
