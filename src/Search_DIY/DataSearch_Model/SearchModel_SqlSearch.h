#pragma once
#include "./SearchModel.h"

#define  RESTYPE_DIYSEARCH_SQL              0x20
#define  RESTYPE_DIYSEARCH_SQL_SUCCESS      0x21
#define  RESTYPE_DIYSEARCH_SQL_FAIL         0x22

class CSearchModel_SqlSearch : public CSearchModel
{
public:
	CSearchModel_SqlSearch(void);
public:
	~CSearchModel_SqlSearch(void);

protected:
    std::string m_strSQL;
    std::string m_strFetchDataParas;

protected:
	virtual BOOL FetchSearchCondiction(const BYTE* const pData, const int nCount,int& ioffset);
	virtual BOOL DealDataSub(void);
	virtual BOOL CheckIsSqlNoQuery(std::string sql);
	virtual BOOL ClearData(void){
		return TRUE;
	};
	BOOL SendInfo(void);
};

class CSearchModel_SqlSearch_ForMainDB : public CSearchModel_SqlSearch
{
public:
	CSearchModel_SqlSearch_ForMainDB(void);
	~CSearchModel_SqlSearch_ForMainDB(void);

protected:
	virtual BOOL FetchSearchCondiction(const BYTE* const pData, const int nCount,int& ioffset);
  virtual BOOL CheckIsSqlNoQuery(std::string sql);

	BOOL ResetDBConnInfo();
};


