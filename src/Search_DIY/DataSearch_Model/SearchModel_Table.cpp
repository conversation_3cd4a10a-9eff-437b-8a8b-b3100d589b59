#include "SearchModel_Table.h"

BOOL 
CSearchModel_Table::FetchSearchCondiction(const BYTE* const pData, const int nCount,int& ioffset)
{
	CHECLFALSE(conditionMaker.InitSerchCondition(pData, nCount, ioffset));

	PPM_OS::memcpy(&m_tableid,pData + ioffset,4);
	m_tableid = MAKEINT_NETSEQ1(m_tableid);
	MOVENEXT(ioffset,4,nCount);
	CHECLFALSE(CColumnDef::Instance()->IsTableExist(m_tableid));

	PPM_OS::memset(&tm_tablename, 0, sizeof(tm_tablename));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_tablename,sizeof(tm_tablename)));

	PPM_OS::memset(&m_column, 0, sizeof(m_column));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,m_column,sizeof(m_column)));
	return TRUE;
}

BOOL
CSearchModel_Table::DealDataSub()
{
  CColumnResult columnResult_tb;

	CHECLFALSE(   columnResult_tb.Init
		( std::string(m_column),
		CStdCommond::itostr(m_tableid), 
		std::string(tm_tablename),
		m_pDataBaseDeal_,
		m_pServerDeal_,
		m_pSendBuf_)
		);

	std::string strSQL = p_SqlMaker->Sql_Select(columnResult_tb.GetDBSearchSql(), std::string(tm_tablename), conditionMaker.GetSearchConfitionSql());

	columnResult_tb.SendColumnsIDToClient(CMD1_DIYSEARCH, CMD2_RESPONSE, RESTYPE_COLUMN_MODEL_TABLE);

	columnResult_tb.SearchSqlSendData(
		CMD1_DIYSEARCH,
		CMD2_RESPONSE,
		RESTYPE_DIY_MODEL_TABLE,
		strSQL );

	return TRUE;
}


BOOL
CSearchModel_Table::ClearData(void)
{
	conditionMaker.ClearItem();
	return TRUE;
}

