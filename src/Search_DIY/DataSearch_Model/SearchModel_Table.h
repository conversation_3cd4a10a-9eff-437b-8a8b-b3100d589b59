
#pragma once
#include "./SearchModel.h"

class CSearchModel_Table :
public CSearchModel
{
public:
	enum SearchModelType
	{
       RESTYPE_COLUMN_MODEL_TABLE  = 0x10,
       RESTYPE_DIY_MODEL_TABLE  = 0x11
	};

protected:
    CSearchConditionMaker conditionMaker;

	int m_tableid;
	char tm_tablename[255];
	char m_column[8000];

protected:
	virtual BOOL FetchSearchCondiction(const BYTE* const pData, const int nCount,int& ioffset);	
	virtual BOOL DealDataSub();
	virtual BOOL ClearData();
};
