#include "SearchConditionMaker.h"
#include "./StdCommond.h"
#include "./ColumnDef.h"

std::string
CSearchConditionItem::GetSql(std::string tbName)
{
	std::string sql = "";
	for (auto it = vecSql.begin(); it != vecSql.end(); ++it)
	{
		sql += std::string(" " + tbName + "." + *it + " and ");
	}
	sql = sql.substr(0, sql.length() - 4);
	return sql;
}

BOOL
CSearchConditionItem_AreaSelectIntersect::Init(const BYTE* const pData, const int nCount,int& ioffset)
{
	PPM_OS::memcpy(&itllongitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&itllatitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&ibrlongitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&ibrlatitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);

	itllongitude = MAKEINT_NETSEQ1(itllongitude);
	itllatitude = MAKEINT_NETSEQ1(itllatitude);
	ibrlongitude = MAKEINT_NETSEQ1(ibrlongitude);
	ibrlatitude = MAKEINT_NETSEQ1(ibrlatitude);

	vecSql.clear();
	vecSql.push_back(std::string("ibrlongitude >= " + CStdCommond::itostr(itllongitude)));
	vecSql.push_back(std::string("ibrlatitude <= " + CStdCommond::itostr(itllatitude)));
	vecSql.push_back(std::string("itllongitude <= " + CStdCommond::itostr(ibrlongitude)));
	vecSql.push_back(std::string("itllatitude >= " + CStdCommond::itostr(ibrlatitude)));

	sql = " ibrlongitude >= " + CStdCommond::itostr(itllongitude) + " and ibrlatitude <= " + 
		CStdCommond::itostr(itllatitude) + " and itllongitude <= " + CStdCommond::itostr(ibrlongitude) + 
		" and itllatitude >= " + CStdCommond::itostr(ibrlatitude);

	return TRUE;   
}


BOOL
CSearchConditionItem_AreaSelectInside::Init(const BYTE* const pData, const int nCount,int& ioffset)
{
	PPM_OS::memcpy(&itllongitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&itllatitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&ibrlongitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&ibrlatitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);

	itllongitude = MAKEINT_NETSEQ1(itllongitude);
	itllatitude = MAKEINT_NETSEQ1(itllatitude);
	ibrlongitude = MAKEINT_NETSEQ1(ibrlongitude);
	ibrlatitude = MAKEINT_NETSEQ1(ibrlatitude);

	vecSql.clear();
	vecSql.push_back(std::string("ibrlongitude <= " + CStdCommond::itostr(ibrlongitude)));
	vecSql.push_back(std::string("ibrlatitude >= " + CStdCommond::itostr(ibrlatitude)));
	vecSql.push_back(std::string("itllongitude >= " + CStdCommond::itostr(itllongitude)));
	vecSql.push_back(std::string("itllatitude <= " + CStdCommond::itostr(itllatitude)));

	sql = " ibrlongitude <= " + CStdCommond::itostr(ibrlongitude) + " and ibrlatitude >= " + 
		CStdCommond::itostr(ibrlatitude) + " and itllongitude >= " + CStdCommond::itostr(itllongitude) + 
		" and itllatitude <= " + CStdCommond::itostr(itllatitude);


	return TRUE;   
}


BOOL
CSearchConditionItem_AreaSelectSample::Init(const BYTE* const pData, const int nCount,int& ioffset)
{
	PPM_OS::memcpy(&itllongitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&itllatitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&ibrlongitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&ibrlatitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);

	itllongitude = MAKEINT_NETSEQ1(itllongitude);
	itllatitude = MAKEINT_NETSEQ1(itllatitude);
	ibrlongitude = MAKEINT_NETSEQ1(ibrlongitude);
	ibrlatitude = MAKEINT_NETSEQ1(ibrlatitude);

	vecSql.clear();
	vecSql.push_back(std::string("ilongitude <= " + CStdCommond::itostr(ibrlongitude)));
	vecSql.push_back(std::string("ilongitude >= " + CStdCommond::itostr(itllongitude)));
	vecSql.push_back(std::string("ilatitude <= " + CStdCommond::itostr(itllatitude)));
	vecSql.push_back(std::string("ilatitude >= " + CStdCommond::itostr(ibrlatitude)));

	sql = " ilongitude <= " + CStdCommond::itostr(ibrlongitude) + " and ilongitude >= " +
		CStdCommond::itostr(itllongitude) + " and ilatitude <= " + CStdCommond::itostr(itllatitude) +
		" and ilatitude >= " + CStdCommond::itostr(ibrlatitude);


	return TRUE;   
}

BOOL
CSearchConditionItem_CellSelect::Init(const BYTE* const pData, const int nCount,int& ioffset)
{
	PPM_OS::memcpy(&ilac,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&ici,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);

	ilac = MAKEINT_NETSEQ1(ilac);
	ici = MAKEINT_NETSEQ1(ici);

	vecSql.clear();
	vecSql.push_back(std::string("iLAC = " + CStdCommond::itostr(ilac)));
	vecSql.push_back(std::string("iCI = " + CStdCommond::itostr(ici)));

	sql = " iLAC = " + CStdCommond::itostr(ilac) + " and iCI = " + CStdCommond::itostr(ici) ;

	return TRUE;   
}

BOOL
CSearchConditionItem_NrCellSelect::Init(const BYTE* const pData, const int nCount, int& ioffset)
{
	PPM_OS::memcpy(&ilac, pData + ioffset, 4);
	MOVENEXT(ioffset, 4, nCount);
	PPM_OS::memcpy(&ici, pData + ioffset, 8);
	MOVENEXT(ioffset, 8, nCount);

	ilac = MAKEINT_NETSEQ1(ilac);
	ici = REVERTINT64_NETSEQ1(ici);

	vecSql.clear();
	vecSql.push_back(std::string("iLAC = " + CStdCommond::itostr(ilac)));
	vecSql.push_back(std::string("iCI = " + CStdCommond::int64tostr(ici)));

	sql = " iLAC = " + CStdCommond::itostr(ilac) + " and iCI = " + CStdCommond::int64tostr(ici);

	return TRUE;
}

BOOL
CSearchConditionItem_MaxThan::Init(const BYTE* const pData, const int nCount,int& ioffset)
{
	char tm_columnid[1024];
	PPM_OS::memset(&tm_columnid, 0, sizeof(tm_columnid));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_columnid,sizeof(tm_columnid)));

	PPM_OS::memcpy(&dataValue,pData + ioffset,4);
	dataValue = MAKEINT_NETSEQ1(dataValue);
	MOVENEXT(ioffset,4,nCount);

	std::vector<STRU_COLUMNID> vec_STRU_COLUMNID = CColumnDef::Instance()->ConvertIDStrToColumnID(std::string(tm_columnid));

	if (vec_STRU_COLUMNID.size() == 0)
	{
		return FALSE;
	}

	columnItem = CColumnDef::Instance()->GetColumnItem(vec_STRU_COLUMNID[0]);

	if (NULL == columnItem)
	{
		return FALSE;
	}

	vecSql.clear();
	vecSql.push_back(std::string(columnItem->dbColumnName + " >= " + CStdCommond::itostr(dataValue)));

	sql = " " + columnItem->dbColumnName + " >= " + CStdCommond::itostr(dataValue);

	return TRUE;    
}

BOOL
CSearchConditionItem_MinThan::Init(const BYTE* const pData, const int nCount,int& ioffset)
{
	char tm_columnid[1024];
	PPM_OS::memset(&tm_columnid, 0, sizeof(tm_columnid));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_columnid,sizeof(tm_columnid)));

	PPM_OS::memcpy(&dataValue,pData + ioffset,4);
	dataValue = MAKEINT_NETSEQ1(dataValue);
	MOVENEXT(ioffset,4,nCount);

	std::vector<STRU_COLUMNID> vec_STRU_COLUMNID = CColumnDef::Instance()->ConvertIDStrToColumnID(std::string(tm_columnid));

	if (vec_STRU_COLUMNID.size() == 0)
	{
		return FALSE;
	}

	columnItem = CColumnDef::Instance()->GetColumnItem(vec_STRU_COLUMNID[0]);
    CHECLNULL(columnItem);

	vecSql.clear();
	vecSql.push_back(std::string(columnItem->dbColumnName + " <= " + CStdCommond::itostr(dataValue)));

	sql = " " + columnItem->dbColumnName + " <= " + CStdCommond::itostr(dataValue);

	return TRUE;    
}

BOOL
CSearchConditionItem_Between::Init(const BYTE* const pData, const int nCount,int& ioffset)
{
	char tm_columnid[1024];
	PPM_OS::memset(&tm_columnid, 0, sizeof(tm_columnid));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_columnid,sizeof(tm_columnid)));

	PPM_OS::memcpy(&dataValue_Min,pData + ioffset,4);
	dataValue_Min = MAKEINT_NETSEQ1(dataValue_Min);
	MOVENEXT(ioffset,4,nCount);

	PPM_OS::memcpy(&dataValue_Max,pData + ioffset,4);
	dataValue_Max = MAKEINT_NETSEQ1(dataValue_Max);
	MOVENEXT(ioffset,4,nCount);

	std::vector<STRU_COLUMNID> vec_STRU_COLUMNID = CColumnDef::Instance()->ConvertIDStrToColumnID(std::string(tm_columnid));

	if (vec_STRU_COLUMNID.size() == 0)
	{
		return FALSE;
	}

	columnItem = CColumnDef::Instance()->GetColumnItem(vec_STRU_COLUMNID[0]);
    CHECLNULL(columnItem);

	vecSql.clear();
	vecSql.push_back(std::string(columnItem->dbColumnName + " between " + CStdCommond::itostr(dataValue_Min) + " and " + 
		CStdCommond::itostr(dataValue_Max)));

	sql = " " + columnItem->dbColumnName + " between " + CStdCommond::itostr(dataValue_Min) + " and " + 
		CStdCommond::itostr(dataValue_Max);

	return TRUE;    
}

BOOL
CSearchConditionItem_InSelect::Init(const BYTE* const pData, const int nCount,int& ioffset)
{
	char tm_columnid[1000];
	PPM_OS::memset(&tm_columnid, 0, sizeof(tm_columnid));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_columnid,sizeof(tm_columnid)));

	char tm_inlist[8000];
	PPM_OS::memset(&tm_inlist, 0, sizeof(tm_inlist));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_inlist,sizeof(tm_inlist)));

	std::vector<STRU_COLUMNID> vec_STRU_COLUMNID = CColumnDef::Instance()->ConvertIDStrToColumnID(std::string(tm_columnid));

	dataValue_in = tm_inlist;

	if (vec_STRU_COLUMNID.size() == 0)
	{
		return FALSE;
	}

	columnItem = CColumnDef::Instance()->GetColumnItem(vec_STRU_COLUMNID[0]);
	CHECLNULL(columnItem);

	vecSql.clear();
	vecSql.push_back(std::string(columnItem->dbColumnName + " in ( " + dataValue_in + ")"));

	sql = " " + columnItem->dbColumnName + " in ( " + dataValue_in + ")";

	return TRUE;    
}

BOOL
CSearchConditionItem_TimeSpanIntersect::Init(const BYTE* const pData, const int nCount,int& ioffset)
{
	PPM_OS::memcpy(&istime,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&ietime,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);

	istime = MAKEINT_NETSEQ1(istime);
	ietime = MAKEINT_NETSEQ1(ietime);

	vecSql.clear();
	vecSql.push_back(std::string("istime <= " + CStdCommond::itostr(ietime)));
	vecSql.push_back(std::string("ietime >= " + CStdCommond::itostr(istime)));

	sql = " istime <= " + CStdCommond::itostr(ietime) + " and ietime >= " + CStdCommond::itostr(istime) ;

	return TRUE;    
}

BOOL
CSearchConditionItem_TimeSpanInside::Init(const BYTE* const pData, const int nCount,int& ioffset)
{
	PPM_OS::memcpy(&istime,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&ietime,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);

	istime = MAKEINT_NETSEQ1(istime);
	ietime = MAKEINT_NETSEQ1(ietime);

	vecSql.clear();
	vecSql.push_back(std::string("istime >= " + CStdCommond::itostr(istime)));
	vecSql.push_back(std::string("ietime <= " + CStdCommond::itostr(ietime)));

	sql = " istime >= " + CStdCommond::itostr(istime) + " and ietime <= " + CStdCommond::itostr(ietime) ;

	return TRUE;    
}

BOOL
CSearchConditionItem_iTimeSelect::Init(const BYTE* const pData, const int nCount,int& ioffset)
{
	PPM_OS::memcpy(&istime,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&ietime,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);

	istime = MAKEINT_NETSEQ1(istime);
	ietime = MAKEINT_NETSEQ1(ietime);

	vecSql.clear();
	vecSql.push_back(std::string("itime between " + CStdCommond::itostr(istime) + " and " + CStdCommond::itostr(ietime)));

	sql = " itime between " + CStdCommond::itostr(istime) + " and " + CStdCommond::itostr(ietime) ;

	return TRUE;    
}

BOOL
CSearchConditionItem_StrLike::Init(const BYTE* const pData, const int nCount,int& ioffset)
{
	char tm_columnid[1024];
	PPM_OS::memset(&tm_columnid, 0, sizeof(tm_columnid));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_columnid,sizeof(tm_columnid)));

	char tm_columnName[1024*8];
	PPM_OS::memset(&tm_columnName, 0, sizeof(tm_columnName));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_columnName,sizeof(tm_columnName)));

	vec_strName = CStdCommond::str_slipt(std::string(tm_columnName), "{,}");

	std::vector<STRU_COLUMNID> vec_STRU_COLUMNID = CColumnDef::Instance()->ConvertIDStrToColumnID(std::string(tm_columnid));

	if (vec_STRU_COLUMNID.size() == 0 || vec_STRU_COLUMNID.size() != vec_strName.size())
	{
		return FALSE;
	}

	CColumnItem* columnItem = CColumnDef::Instance()->GetColumnItem(vec_STRU_COLUMNID[0]);
	CHECLNULL(columnItem);
	vec_columnItem.push_back(columnItem);

	char tm_unequal[8];
	PPM_OS::strcpy(tm_unequal,"<> ");

	vecSql.clear();
	for (size_t i=0; i<vec_STRU_COLUMNID.size(); ++i)
	{
		CColumnItem* columnItem = CColumnDef::Instance()->GetColumnItem(vec_STRU_COLUMNID[0]);
		if (columnItem == NULL)
		{
			continue;
		}

		// 此处为【检索条件】【文件筛选】【文件名 或 路径】功能，如果为文件名检索，需要进行转码
		char _strname[MAX_FNAME]{0};
		memcpy(_strname, vec_strName[i].c_str(), vec_strName[i].length());
		vec_strName[i] = CUtility::GBKToUtf8(_strname);

		if(strncmp(tm_unequal, vec_strName[i].c_str(), PPM_OS::strlen(tm_unequal)) == 0)
		{
			if (0 == i)
			{			
				sql = " " + columnItem->dbColumnName + " <>'" + vec_strName[i].substr(3) + "'";
			}
			else 
			{
				sql += " or " + columnItem->dbColumnName + " <>'" + vec_strName[i].substr(3) + "'";
			}
			vecSql.push_back(std::string(columnItem->dbColumnName + " <>'" + vec_strName[i].substr(3) + "'"));
		}
		else if (0 == i)
		{			
			sql = " " + columnItem->dbColumnName + " like '%" + vec_strName[i] + "%' ";
			vecSql.push_back(std::string(columnItem->dbColumnName + " like '%" + vec_strName[i] + "%' "));
		}
		else 
		{
            sql += " or " + columnItem->dbColumnName + " like '%" + vec_strName[i] + "%' ";
			vecSql.push_back(std::string(columnItem->dbColumnName + " like '%" + vec_strName[i] + "%' "));
		}     
	}

	sql = " (" + sql + ") ";


	return TRUE;    
}

std::string
CSearchConditionItem_StrLike::GetSql(std::string tbName)
{
	std::string sql = "";
	for (auto it = vecSql.begin(); it != vecSql.end(); ++it)
	{
		sql += std::string(" " + tbName + "." + *it + " or ");
	}
	sql = sql.substr(0, sql.length() - 3);
	sql = " (" + sql + ") ";
	return sql;
}

CSearchConditionItem_Equal::CSearchConditionItem_Equal()
:m_mapCsTypePos()
{
	columnItem = NULL;
	dataValue = "";

	InitCsTypeMap();
}

CSearchConditionItem_Equal::~CSearchConditionItem_Equal()
{
	m_mapCsTypePos.clear();
}

BOOL
CSearchConditionItem_Equal::Init(const BYTE* const pData, const int nCount,int& ioffset)
{
	char tm_columnid[255];
	PPM_OS::memset(&tm_columnid, 0, sizeof(tm_columnid));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_columnid,sizeof(tm_columnid)));

	std::vector<STRU_COLUMNID> vec_STRU_COLUMNID = CColumnDef::Instance()->ConvertIDStrToColumnID(std::string(tm_columnid));

	if (vec_STRU_COLUMNID.size() == 0)
	{
		return FALSE;
	}

	char tm_data[255];
	PPM_OS::memset(&tm_data, 0, sizeof(tm_data));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_data,sizeof(tm_data)));

	columnItem = CColumnDef::Instance()->GetColumnItem(vec_STRU_COLUMNID[0]);
    CHECLNULL(columnItem);

	vecSql.clear();

	auto it = m_mapCsTypePos.find(columnItem->dataType);

	if (it != m_mapCsTypePos.end())
	{
		(this->*(it->second))(tm_data);
	}

	return TRUE;    
}

void CSearchConditionItem_Equal::Deal_Type_CS_Number_Type(char* tm_data)
{
	sql = " " + columnItem->dbColumnName + " = " + std::string(tm_data);
	vecSql.push_back(std::string(columnItem->dbColumnName + " = " + std::string(tm_data)));
}
void CSearchConditionItem_Equal::Deal_Type_CS_Text_Type(char* tm_data)
{
	sql = " " + columnItem->dbColumnName + " = '" + std::string(tm_data) + "'";
	vecSql.push_back(std::string(columnItem->dbColumnName + " = '" + std::string(tm_data) + "'"));
}


void CSearchConditionItem_Equal::InitCsTypeMap(void)
{
	m_mapCsTypePos.insert(std::make_pair(TYPE_CS_TINYINT, &CSearchConditionItem_Equal::Deal_Type_CS_Number_Type));
	m_mapCsTypePos.insert(std::make_pair(TYPE_CS_SMALLINT, &CSearchConditionItem_Equal::Deal_Type_CS_Number_Type));
	m_mapCsTypePos.insert(std::make_pair(TYPE_CS_USMALLINT, &CSearchConditionItem_Equal::Deal_Type_CS_Number_Type));
	m_mapCsTypePos.insert(std::make_pair(TYPE_CS_INT, &CSearchConditionItem_Equal::Deal_Type_CS_Number_Type));
	m_mapCsTypePos.insert(std::make_pair(TYPE_CS_FLOAT, &CSearchConditionItem_Equal::Deal_Type_CS_Number_Type));
	m_mapCsTypePos.insert(std::make_pair(TYPE_CS_INT_FLOAT, &CSearchConditionItem_Equal::Deal_Type_CS_Number_Type));
	m_mapCsTypePos.insert(std::make_pair(TYPE_CS_INT64, &CSearchConditionItem_Equal::Deal_Type_CS_Number_Type));
	m_mapCsTypePos.insert(std::make_pair(TYPE_CS_UINT64, &CSearchConditionItem_Equal::Deal_Type_CS_Number_Type));

	m_mapCsTypePos.insert(std::make_pair(TYPE_CS_TEXT, &CSearchConditionItem_Equal::Deal_Type_CS_Text_Type));
}

BOOL
CSearchConditionItem_UnEqual::Init(const BYTE* const pData, const int nCount,int& ioffset)
{
	char tm_columnid[255];
	PPM_OS::memset(&tm_columnid, 0, sizeof(tm_columnid));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_columnid,sizeof(tm_columnid)));

	char tm_data[255];
	PPM_OS::memset(&tm_data, 0, sizeof(tm_data));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_data,sizeof(tm_data)));

	std::vector<STRU_COLUMNID> vec_STRU_COLUMNID = CColumnDef::Instance()->ConvertIDStrToColumnID(std::string(tm_columnid));

	if (vec_STRU_COLUMNID.size() == 0)
	{
		return FALSE;
	}

	columnItem = CColumnDef::Instance()->GetColumnItem(vec_STRU_COLUMNID[0]);
	CHECLNULL(columnItem);

	vecSql.clear();
	switch(columnItem->dataType)
	{
	case TYPE_CS_TINYINT:
	case TYPE_CS_SMALLINT:
	case TYPE_CS_INT:
	case TYPE_CS_FLOAT:
		{
			sql = " " + columnItem->dbColumnName + " != " + std::string(tm_data);
			vecSql.push_back(std::string(columnItem->dbColumnName + " != " + std::string(tm_data)));
		}
		break;
	case TYPE_CS_TEXT:
		{
			sql = " " + columnItem->dbColumnName + " != '" + std::string(tm_data) + "'";
			vecSql.push_back(std::string(columnItem->dbColumnName + " != '" + std::string(tm_data) + "'"));
		}
		break;
	default:
		{

		}
		break;
	}

	return TRUE;    
}

BOOL
CSearchConditionItem_DIYSql::Init(const BYTE* const pData, const int nCount,int& ioffset)
{
	char tm_sql[5120];
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_sql,sizeof(tm_sql)));

	sql = std::string(tm_sql);

	// 此处为【检索条件】【文件筛选】【高级】功能，如果为文件名检索，需要进行转码
	if (sql.find("strfilename") != std::string::npos)
	{
		char _sqltmp[MAX_PATH]{0};
		memcpy(_sqltmp, sql.c_str(), sql.length());
		sql = CUtility::GBKToUtf8(_sqltmp);
	}

	if (0 == sql.length())
	{
		return FALSE;
	}

	return TRUE;    
}

std::string
CSearchConditionItem_DIYSql::GetSql(std::string tbName)
{
	return sql;
}


CSearchConditionMaker::CSearchConditionMaker()
{
	for (int i = 0; i < 256; i++)
	{
		pFunc_Maker[i] = &CSearchConditionMaker::GetConditionType_Default;
		pFunc_InitMaker[i] = &CSearchConditionMaker::InitConditionType_Default;
	}

	pFunc_InitMaker[SearchConditionType_AreaSelectIntersect] = &CSearchConditionMaker::InitConditionType_AreaSelectIntersect;
	pFunc_InitMaker[SearchConditionType_AreaSelectInside] = &CSearchConditionMaker::InitConditionType_AreaSelectInside;
	pFunc_InitMaker[SearchConditionType_AreaSelectSample] = &CSearchConditionMaker::InitConditionType_AreaSelectSample;
	pFunc_InitMaker[SearchConditionType_MaxThan] = &CSearchConditionMaker::InitConditionType_MaxThan;
	pFunc_InitMaker[SearchConditionType_MinThan] = &CSearchConditionMaker::InitConditionType_MinThan;
	pFunc_InitMaker[SearchConditionType_Between] = &CSearchConditionMaker::InitConditionType_Between;
	pFunc_InitMaker[SearchConditionType_InSelect] = &CSearchConditionMaker::InitConditionType_InSelect;
	pFunc_InitMaker[SearchConditionType_TimeSpanIntersect] = &CSearchConditionMaker::InitConditionType_TimeSpanIntersect;
	pFunc_InitMaker[SearchConditionType_TimeSpanInside] = &CSearchConditionMaker::InitConditionType_TimeSpanInside;
	pFunc_InitMaker[SearchConditionType_iTimeSelect] = &CSearchConditionMaker::InitConditionType_iTimeSelect;
	pFunc_InitMaker[SearchConditionType_StrLike] = &CSearchConditionMaker::InitConditionType_StrLike;
	pFunc_InitMaker[SearchConditionType_Equal] = &CSearchConditionMaker::InitConditionType_Equal;
	pFunc_InitMaker[SearchConditionType_UnEqual] = &CSearchConditionMaker::InitConditionType_UnEqual;
	pFunc_InitMaker[SearchConditionType_CellSelect] = &CSearchConditionMaker::InitConditionType_CellSelect;
	pFunc_InitMaker[SearchConditionType_DIYSql] = &CSearchConditionMaker::InitConditionType_DIYSql;
	pFunc_InitMaker[SearchConditionType_NrCellSelect] = &CSearchConditionMaker::InitConditionType_NrCellSelect;

	pFunc_Maker[SearchConditionType_AreaSelectIntersect] = &CSearchConditionMaker::GetConditionType_AreaSelectIntersect;
	pFunc_Maker[SearchConditionType_AreaSelectInside] = &CSearchConditionMaker::GetConditionType_AreaSelectInside;
	pFunc_Maker[SearchConditionType_AreaSelectSample] = &CSearchConditionMaker::GetConditionType_AreaSelectSample;
	pFunc_Maker[SearchConditionType_MaxThan] = &CSearchConditionMaker::GetConditionType_MaxThan;
	pFunc_Maker[SearchConditionType_MinThan] = &CSearchConditionMaker::GetConditionType_MinThan;
	pFunc_Maker[SearchConditionType_Between] = &CSearchConditionMaker::GetConditionType_Between;
	pFunc_Maker[SearchConditionType_InSelect] = &CSearchConditionMaker::GetConditionType_InSelect;
	pFunc_Maker[SearchConditionType_TimeSpanIntersect] = &CSearchConditionMaker::GetConditionType_TimeSpanIntersect;
	pFunc_Maker[SearchConditionType_TimeSpanInside] = &CSearchConditionMaker::GetConditionType_TimeSpanInside;
	pFunc_Maker[SearchConditionType_iTimeSelect] = &CSearchConditionMaker::GetConditionType_iTimeSelect;
	pFunc_Maker[SearchConditionType_StrLike] = &CSearchConditionMaker::GetConditionType_StrLike;
	pFunc_Maker[SearchConditionType_Equal] = &CSearchConditionMaker::GetConditionType_Equal;
	pFunc_Maker[SearchConditionType_UnEqual] = &CSearchConditionMaker::GetConditionType_UnEqual;
	pFunc_Maker[SearchConditionType_CellSelect] = &CSearchConditionMaker::GetConditionType_CellSelect;
	pFunc_Maker[SearchConditionType_DIYSql] = &CSearchConditionMaker::GetConditionType_DIYSql;
	pFunc_Maker[SearchConditionType_NrCellSelect] = &CSearchConditionMaker::GetConditionType_DIYSql;

}
CSearchConditionMaker::~CSearchConditionMaker()
{
	ClearItem();
}

BOOL
CSearchConditionMaker::InitSerchCondition(const BYTE* const pData, const int nCount,int& ioffset)
{
  ClearItem();

	BOOL DoAgain = TRUE;
	BOOL GoodBuffer = FALSE;
	
	while (DoAgain)
	{
		BYTE btemp ;
		PPM_OS::memcpy(&btemp,pData + ioffset,1);
		int tm_type = btemp & 0x000000ff;
		MOVENEXT(ioffset,1,nCount);

		GoodBuffer = (this->*pFunc_InitMaker[tm_type])(pData, nCount, ioffset);

		if (tm_type == SearchConditionType_EndFlag)
		{
			GoodBuffer = TRUE;
      DoAgain = FALSE;
		}

		if (FALSE == GoodBuffer)
		{
      DoAgain = FALSE;
		}
	}

  return GoodBuffer;
}

BOOL CSearchConditionMaker::InitConditionType_Default(const BYTE* const pData, const int nCount,int& ioffset)
{
    return FALSE;
}
BOOL CSearchConditionMaker::InitConditionType_AreaSelectIntersect(const BYTE* const pData, const int nCount,int& ioffset)
{
	CSearchConditionItem* p_conditionItem = new CSearchConditionItem_AreaSelectIntersect();	
	BOOL GoodBuffer = p_conditionItem->Init(pData, nCount, ioffset);
	if (GoodBuffer)
	{
		map_SearchConditionStr["AreaSelectIntersect"] = p_conditionItem;
	}	
	else 
	{
		delete p_conditionItem;
	}
    
	return GoodBuffer;
}
BOOL CSearchConditionMaker::InitConditionType_AreaSelectInside(const BYTE* const pData, const int nCount,int& ioffset)
{
	CSearchConditionItem* p_conditionItem = new CSearchConditionItem_AreaSelectInside();	
	BOOL GoodBuffer = p_conditionItem->Init(pData, nCount, ioffset);
	if (GoodBuffer)
	{
		map_SearchConditionStr["AreaSelectInside"] = p_conditionItem;
	}	
	else 
	{
		delete p_conditionItem;
	}
    return GoodBuffer;
}
BOOL CSearchConditionMaker::InitConditionType_AreaSelectSample(const BYTE* const pData, const int nCount,int& ioffset)
{
	CSearchConditionItem* p_conditionItem = new CSearchConditionItem_AreaSelectSample();	
	BOOL GoodBuffer = p_conditionItem->Init(pData, nCount, ioffset);
	if (GoodBuffer)
	{
		map_SearchConditionStr["AreaSelectSample"] = p_conditionItem;
	}	
	else 
	{
		delete p_conditionItem;
	}

    return GoodBuffer;
}
BOOL CSearchConditionMaker::InitConditionType_MaxThan(const BYTE* const pData, const int nCount,int& ioffset)
{
	CSearchConditionItem* p_conditionItem = new CSearchConditionItem_MaxThan();	
	BOOL GoodBuffer = p_conditionItem->Init(pData, nCount, ioffset);
	if (GoodBuffer)
	{
		std::string key =  "MaxThan," + ((CSearchConditionItem_MaxThan*)p_conditionItem)->columnItem->dbColumnName;
		map_SearchConditionStr[key] = p_conditionItem;
	}
	else 
	{
		delete p_conditionItem;
	}
    return GoodBuffer;
}
BOOL CSearchConditionMaker::InitConditionType_MinThan(const BYTE* const pData, const int nCount,int& ioffset)
{
	CSearchConditionItem* p_conditionItem = new CSearchConditionItem_MinThan();	
	BOOL GoodBuffer = p_conditionItem->Init(pData, nCount, ioffset);
	if (GoodBuffer)
	{
		std::string key =  "MaxThan," + ((CSearchConditionItem_MinThan*)p_conditionItem)->columnItem->dbColumnName;
		map_SearchConditionStr[key] = p_conditionItem;
	}
	else 
	{
		delete p_conditionItem;
	}
    return GoodBuffer;
}
BOOL CSearchConditionMaker::InitConditionType_Between(const BYTE* const pData, const int nCount,int& ioffset)
{
	CSearchConditionItem* p_conditionItem = new CSearchConditionItem_Between();	
	BOOL GoodBuffer = p_conditionItem->Init(pData, nCount, ioffset);
	if (GoodBuffer)
	{
		std::string key =  "Between," + ((CSearchConditionItem_Between*)p_conditionItem)->columnItem->dbColumnName;
		map_SearchConditionStr[key] = p_conditionItem;
	}
	else 
	{
		delete p_conditionItem;
	}
	return GoodBuffer;
}
BOOL CSearchConditionMaker::InitConditionType_InSelect(const BYTE* const pData, const int nCount,int& ioffset)
{
	CSearchConditionItem* p_conditionItem = new CSearchConditionItem_InSelect();	
	BOOL GoodBuffer = p_conditionItem->Init(pData, nCount, ioffset);
	if (GoodBuffer)
	{
		std::string key =  "InSelect," + ((CSearchConditionItem_InSelect*)p_conditionItem)->columnItem->dbColumnName;
		map_SearchConditionStr[key] = p_conditionItem;
	}
	else 
	{
		delete p_conditionItem;
	}
    return GoodBuffer;
}
BOOL CSearchConditionMaker::InitConditionType_TimeSpanIntersect(const BYTE* const pData, const int nCount,int& ioffset)
{
	CSearchConditionItem* p_conditionItem = new CSearchConditionItem_TimeSpanIntersect();	
	BOOL GoodBuffer = p_conditionItem->Init(pData, nCount, ioffset);
	if (GoodBuffer)
	{
		map_SearchConditionStr["TimeSpanIntersect"] = p_conditionItem;
	}	
	else 
	{
		delete p_conditionItem;
	}
    return GoodBuffer;
}

BOOL CSearchConditionMaker::InitConditionType_TimeSpanInside(const BYTE* const pData, const int nCount,int& ioffset)
{
	CSearchConditionItem* p_conditionItem = new CSearchConditionItem_TimeSpanInside();	
	BOOL GoodBuffer = p_conditionItem->Init(pData, nCount, ioffset);
	if (GoodBuffer)
	{
		map_SearchConditionStr["TimeSpanInside"] = p_conditionItem;
	}
	else 
	{
		delete p_conditionItem;
	}
    return GoodBuffer;
}
BOOL CSearchConditionMaker::InitConditionType_iTimeSelect(const BYTE* const pData, const int nCount,int& ioffset)
{
	CSearchConditionItem* p_conditionItem = new CSearchConditionItem_iTimeSelect();	
	BOOL GoodBuffer = p_conditionItem->Init(pData, nCount, ioffset);
	if (GoodBuffer)
	{
		map_SearchConditionStr["iTimeSelect"] = p_conditionItem;
	}
	else 
	{
		delete p_conditionItem;
	}
    return GoodBuffer;
}
BOOL CSearchConditionMaker::InitConditionType_StrLike(const BYTE* const pData, const int nCount,int& ioffset)
{
	CSearchConditionItem* p_conditionItem = new CSearchConditionItem_StrLike();	
	BOOL GoodBuffer = p_conditionItem->Init(pData, nCount, ioffset);
	if (GoodBuffer)
	{
		std::string key =  "StrLike," + ((CSearchConditionItem_StrLike*)p_conditionItem)->vec_columnItem[0]->dbColumnName; //add ColumnName
		map_SearchConditionStr[key] = p_conditionItem;
	}	
	else 
	{
		delete p_conditionItem;
	}
    return GoodBuffer;
}
BOOL CSearchConditionMaker::InitConditionType_Equal(const BYTE* const pData, const int nCount,int& ioffset)
{
	CSearchConditionItem* p_conditionItem = new CSearchConditionItem_Equal();	
	BOOL GoodBuffer = p_conditionItem->Init(pData, nCount, ioffset);
	if (GoodBuffer)
	{
		std::string key =  "Equal," + ((CSearchConditionItem_Equal*)p_conditionItem)->columnItem->dbColumnName;
		map_SearchConditionStr[key] = p_conditionItem;
	}
	else 
	{
		delete p_conditionItem;
	}
    return GoodBuffer;
}
BOOL CSearchConditionMaker::InitConditionType_UnEqual(const BYTE* const pData, const int nCount,int& ioffset)
{
	CSearchConditionItem* p_conditionItem = new CSearchConditionItem_UnEqual();	
	BOOL GoodBuffer = p_conditionItem->Init(pData, nCount, ioffset);
	if (GoodBuffer)
	{
		std::string key =  "UnEqual," + ((CSearchConditionItem_UnEqual*)p_conditionItem)->columnItem->dbColumnName;
		map_SearchConditionStr[key] = p_conditionItem;
	}
	else 
	{
		delete p_conditionItem;
	}
    return GoodBuffer;
}

BOOL CSearchConditionMaker::InitConditionType_CellSelect(const BYTE* const pData, const int nCount,int& ioffset)
{
	CSearchConditionItem* p_conditionItem = new CSearchConditionItem_CellSelect();	
	BOOL GoodBuffer = p_conditionItem->Init(pData, nCount, ioffset);
	if (GoodBuffer)
	{
		map_SearchConditionStr["CellSelect"] = p_conditionItem;
	}	
	else 
	{
		delete p_conditionItem;
	}
    return GoodBuffer;
}

BOOL CSearchConditionMaker::InitConditionType_NrCellSelect(const BYTE* const pData, const int nCount, int& ioffset)
{
	CSearchConditionItem* p_conditionItem = new CSearchConditionItem_NrCellSelect();
	BOOL GoodBuffer = p_conditionItem->Init(pData, nCount, ioffset);
	if (GoodBuffer)
	{
		map_SearchConditionStr["NrCellSelect"] = p_conditionItem;
	}
	else
	{
		delete p_conditionItem;
	}

	return GoodBuffer;
}

BOOL CSearchConditionMaker::InitConditionType_DIYSql(const BYTE* const pData, const int nCount,int& ioffset)
{
	CSearchConditionItem* p_conditionItem = new CSearchConditionItem_DIYSql();	
	BOOL GoodBuffer = p_conditionItem->Init(pData, nCount, ioffset);
	if (GoodBuffer)
	{
		char key[25];
		memset(key, 0, 25);
		sprintf(key, "DIYSql%i", ioffset);
		map_SearchConditionStr[key] = p_conditionItem;
	}	
	else 
	{
		delete p_conditionItem;
	}
    return GoodBuffer;
}



CSearchConditionItem*
CSearchConditionMaker::GetSearchCondition(const int type, const std::string columnName)
{
	CSearchConditionItem* p_SearchConditionItem = (this->*pFunc_Maker[type])(columnName);

	return p_SearchConditionItem;
}

CSearchConditionItem* CSearchConditionMaker::GetConditionType_Default(std::string columnName)
{
	return NULL;
}
CSearchConditionItem* CSearchConditionMaker::GetConditionType_AreaSelectIntersect(std::string columnName)
{
	CSearchConditionItem* p_SearchConditionItem = NULL;

	if (map_SearchConditionStr.find("AreaSelectIntersect") != map_SearchConditionStr.end())
	{
		p_SearchConditionItem = map_SearchConditionStr["AreaSelectIntersect"];
	}

	return p_SearchConditionItem;
}
CSearchConditionItem* CSearchConditionMaker::GetConditionType_AreaSelectInside(std::string columnName)
{
	CSearchConditionItem* p_SearchConditionItem = NULL;
	if (map_SearchConditionStr.find("AreaSelectInside") != map_SearchConditionStr.end())
	{
		p_SearchConditionItem = map_SearchConditionStr["AreaSelectInside"];
	}
	return p_SearchConditionItem;
}
CSearchConditionItem* CSearchConditionMaker::GetConditionType_AreaSelectSample(std::string columnName)
{
	CSearchConditionItem* p_SearchConditionItem = NULL;
	if (map_SearchConditionStr.find("AreaSelectSample") != map_SearchConditionStr.end())
	{
		p_SearchConditionItem = map_SearchConditionStr["AreaSelectSample"];
	}
	return p_SearchConditionItem;
}
CSearchConditionItem* CSearchConditionMaker::GetConditionType_MaxThan(std::string columnName)
{
	CSearchConditionItem* p_SearchConditionItem = NULL;
	std::string key =  "MaxThan," + columnName;
	if (map_SearchConditionStr.find(key) != map_SearchConditionStr.end())
	{
		p_SearchConditionItem = map_SearchConditionStr[key];
	}

	return p_SearchConditionItem;
}
CSearchConditionItem* CSearchConditionMaker::GetConditionType_MinThan(std::string columnName)
{
    CSearchConditionItem* p_SearchConditionItem = NULL;
	std::string key =  "MinThan," + columnName;
	if (map_SearchConditionStr.find(key) != map_SearchConditionStr.end())
	{
		p_SearchConditionItem = map_SearchConditionStr[key];
	}
	return p_SearchConditionItem;
}
CSearchConditionItem* CSearchConditionMaker::GetConditionType_Between(std::string columnName)
{
    CSearchConditionItem* p_SearchConditionItem = NULL;
	std::string key =  "Between," + columnName;
	if (map_SearchConditionStr.find(key) != map_SearchConditionStr.end())
	{
		p_SearchConditionItem = map_SearchConditionStr[key];
	}	
	return p_SearchConditionItem;
}
CSearchConditionItem* CSearchConditionMaker::GetConditionType_InSelect(std::string columnName)
{
	CSearchConditionItem* p_SearchConditionItem = NULL;
	std::string key =  "InSelect," + columnName;
	if (map_SearchConditionStr.find(key) != map_SearchConditionStr.end())
	{
		p_SearchConditionItem = map_SearchConditionStr[key];
	}	
	return p_SearchConditionItem;
}
CSearchConditionItem* CSearchConditionMaker::GetConditionType_TimeSpanIntersect(std::string columnName)
{
	CSearchConditionItem* p_SearchConditionItem = NULL;
	if (map_SearchConditionStr.find("TimeSpanIntersect") != map_SearchConditionStr.end())
	{
		p_SearchConditionItem = map_SearchConditionStr["TimeSpanIntersect"];
	}
	return p_SearchConditionItem;
}
CSearchConditionItem* CSearchConditionMaker::GetConditionType_TimeSpanInside(std::string columnName)
{
	CSearchConditionItem* p_SearchConditionItem = NULL;
	if (map_SearchConditionStr.find("TimeSpanInside") != map_SearchConditionStr.end())
	{
		p_SearchConditionItem = map_SearchConditionStr["TimeSpanInside"];
	}
	return p_SearchConditionItem;
}
CSearchConditionItem* CSearchConditionMaker::GetConditionType_iTimeSelect(std::string columnName)
{
	CSearchConditionItem* p_SearchConditionItem = NULL;
	if (map_SearchConditionStr.find("iTimeSelect") != map_SearchConditionStr.end())
	{
		p_SearchConditionItem = map_SearchConditionStr["iTimeSelect"];
	}	
	return p_SearchConditionItem;
}
CSearchConditionItem* CSearchConditionMaker::GetConditionType_StrLike(std::string columnName)
{
	CSearchConditionItem* p_SearchConditionItem = NULL;
	std::string key =  "StrLike," + columnName;
	if (map_SearchConditionStr.find(key) != map_SearchConditionStr.end())
	{
		p_SearchConditionItem = map_SearchConditionStr[key];
	}	
	return p_SearchConditionItem;
}
CSearchConditionItem* CSearchConditionMaker::GetConditionType_Equal(std::string columnName)
{
	CSearchConditionItem* p_SearchConditionItem = NULL;
	std::string key =  "Equal," + columnName;
	if (map_SearchConditionStr.find(key) != map_SearchConditionStr.end())
	{
		p_SearchConditionItem = map_SearchConditionStr[key];
	}
	return p_SearchConditionItem;
}
CSearchConditionItem* CSearchConditionMaker::GetConditionType_UnEqual(std::string columnName)
{
	CSearchConditionItem* p_SearchConditionItem = NULL;
	std::string key =  "UnEqual," + columnName;
	if (map_SearchConditionStr.find(key) != map_SearchConditionStr.end())
	{
		p_SearchConditionItem = map_SearchConditionStr[key];
	}
	return p_SearchConditionItem;
}
CSearchConditionItem* CSearchConditionMaker::GetConditionType_CellSelect(std::string columnName)
{
	CSearchConditionItem* p_SearchConditionItem = NULL;
	if (map_SearchConditionStr.find("CellSelect") != map_SearchConditionStr.end())
	{
		p_SearchConditionItem = map_SearchConditionStr["CellSelect"];
	}
	return p_SearchConditionItem;
}
CSearchConditionItem* CSearchConditionMaker::GetConditionType_NrCellSelect(std::string columnName)
{
	CSearchConditionItem* p_SearchConditionItem = NULL;
	if (map_SearchConditionStr.find("NrCellSelect") != map_SearchConditionStr.end())
	{
		p_SearchConditionItem = map_SearchConditionStr["NrCellSelect"];
	}
	return p_SearchConditionItem;
}
CSearchConditionItem* CSearchConditionMaker::GetConditionType_DIYSql(std::string columnName)
{
	CSearchConditionItem* p_SearchConditionItem = NULL;
	if (map_SearchConditionStr.find("DIYSql") != map_SearchConditionStr.end())
	{
		p_SearchConditionItem = map_SearchConditionStr["DIYSql"];
	}	
	return p_SearchConditionItem;
}



std::string
CSearchConditionMaker::GetSearchConfitionSql()
{
   std::string sql = "";
   BOOL bOnce = TRUE;

   for (auto p_CSearchConditionItem = map_SearchConditionStr.begin();
	   p_CSearchConditionItem != map_SearchConditionStr.end(); ++p_CSearchConditionItem)
   {
	    CSearchConditionItem* p_Item = p_CSearchConditionItem->second;

       if (bOnce == TRUE)
       {
          sql += p_Item->sql;
          bOnce = FALSE;
       }
	   else 
	   {
		   sql += " and (" + p_Item->sql + ")";
	   }
   }
   return sql;
}

std::string
CSearchConditionMaker::GetSearchConfitionSql(std::string logfileTbName)
{
	std::string sql = "";
	BOOL bOnce = TRUE;

	for (auto p_CSearchConditionItem = map_SearchConditionStr.begin(); 
		p_CSearchConditionItem != map_SearchConditionStr.end(); ++p_CSearchConditionItem)
	{
		CSearchConditionItem* p_Item = p_CSearchConditionItem->second;

		if (bOnce == TRUE)
		{
			sql += p_Item->GetSql(logfileTbName);
			bOnce = FALSE;
		}
		else 
		{
			sql += " and (" + std::string(p_Item->GetSql(logfileTbName)) + ")";
		}
	}
	return sql;
}

BOOL
CSearchConditionMaker::ClearItem()
{
	for (auto p_CSearchConditionItem = map_SearchConditionStr.begin();
		p_CSearchConditionItem != map_SearchConditionStr.end(); ++p_CSearchConditionItem)
	{
		CSearchConditionItem* p_Item = p_CSearchConditionItem->second; 
        delete p_Item;
	}
    map_SearchConditionStr.clear();

	return TRUE;
}






