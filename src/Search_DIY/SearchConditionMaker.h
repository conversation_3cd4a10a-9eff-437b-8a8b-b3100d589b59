#pragma once

#include "./Search_PI.h"
#include "./ColumnDef.h"


class CSearchConditionItem
{
public:
	CSearchConditionItem()
	{
    searchType = -1;
		sql = "";
	}

	//父类的析构函数一定要是虚的。才能析构子类的结构
	virtual ~CSearchConditionItem()
	{
		vecSql.clear();
	}

public:
	int searchType;
	std::string sql;
	std::vector<std::string> vecSql;

public:
  virtual BOOL Init(const BYTE* const pData, const int nCount,int& ioffset) = 0;
	virtual std::string GetSql(std::string tbName);
};

class CSearchConditionItem_AreaSelectIntersect : public CSearchConditionItem
{
public:
	int itllongitude;
	int itllatitude;
	int ibrlongitude;
	int ibrlatitude;

public:
	CSearchConditionItem_AreaSelectIntersect()
	{
		itllongitude = 0;
		itllatitude  = 0;
		ibrlongitude = 0;
		ibrlatitude = 0;

		sql = "";
	}

public:
  virtual BOOL Init(const BYTE* const pData, const int nCount,int& ioffset);
};

class CSearchConditionItem_AreaSelectInside : public CSearchConditionItem
{
public:
	int itllongitude;
	int itllatitude;
	int ibrlongitude;
	int ibrlatitude;

public:
	CSearchConditionItem_AreaSelectInside()
	{
		itllongitude = 0;
		itllatitude  = 0;
		ibrlongitude = 0;
		ibrlatitude = 0;

		sql = "";
	}

public:
	virtual BOOL Init(const BYTE* const pData, const int nCount,int& ioffset);
};

class CSearchConditionItem_AreaSelectSample : public CSearchConditionItem
{
public:
	int itllongitude;
	int itllatitude;
	int ibrlongitude;
	int ibrlatitude;

public:
	CSearchConditionItem_AreaSelectSample()
	{
		itllongitude = 0;
		itllatitude  = 0;
		ibrlongitude = 0;
		ibrlatitude = 0;

		sql = "";
	}

public:
	virtual BOOL Init(const BYTE* const pData, const int nCount,int& ioffset);
};

class CSearchConditionItem_CellSelect : public CSearchConditionItem
{
public:
	int ilac;
	int ici;

public:
	CSearchConditionItem_CellSelect()
	{
		ilac = 0;
		ici  = 0;

		sql = "";
	}

public:
	virtual BOOL Init(const BYTE* const pData, const int nCount,int& ioffset);
};

class CSearchConditionItem_NrCellSelect : public CSearchConditionItem
{
public:
	int ilac;
	INT64 ici;

public:
	CSearchConditionItem_NrCellSelect()
	{
		ilac = 0;
		ici = 0;

		sql = "";
	}

public:
	virtual BOOL Init(const BYTE* const pData, const int nCount, int& ioffset);
};


class CSearchConditionItem_MaxThan : public CSearchConditionItem
{
public:
	CColumnItem* columnItem;
	int dataValue;


public:
	CSearchConditionItem_MaxThan()
	{
       columnItem = NULL;
	   dataValue = -1000000;
	}

public:
	virtual BOOL Init(const BYTE* const pData, const int nCount,int& ioffset);
};

class CSearchConditionItem_MinThan : public CSearchConditionItem
{
public:
	CColumnItem* columnItem;
	int dataValue;


public:
	CSearchConditionItem_MinThan()
	{
		columnItem = NULL;
		dataValue = -1000000;
	}

public:
	virtual BOOL Init(const BYTE* const pData, const int nCount,int& ioffset);
};


class CSearchConditionItem_Between : public CSearchConditionItem
{
public:
	CColumnItem* columnItem;
	int dataValue_Max;
    int dataValue_Min;

public:
    
	CSearchConditionItem_Between()
	{
		columnItem = NULL;
		dataValue_Max = -1000000;
		dataValue_Min = 1000000;
	}

public:
	virtual BOOL Init(const BYTE* const pData, const int nCount,int& ioffset);
};

class CSearchConditionItem_InSelect : public CSearchConditionItem
{
public:
	CColumnItem* columnItem;
	std::string dataValue_in;

public:
	CSearchConditionItem_InSelect()
	{
		columnItem = NULL;
		dataValue_in = " ";
	}
 
   ~CSearchConditionItem_InSelect()
   {
	   dataValue_in.erase();
   }

private:
	BOOL Init(const BYTE* const pData, const int nCount,int& ioffset);
};

class CSearchConditionItem_TimeSpanIntersect : public CSearchConditionItem
{
public:
	int istime;
	int ietime;

public:
	CSearchConditionItem_TimeSpanIntersect()
	{
		istime = 0;
		ietime  = 0;

		sql = "";
	}

private:
	BOOL Init(const BYTE* const pData, const int nCount,int& ioffset);
};

class CSearchConditionItem_TimeSpanInside : public CSearchConditionItem
{
public:
	int istime;
	int ietime;

public:
	CSearchConditionItem_TimeSpanInside()
	{
		istime = 0;
		ietime  = 0;

		sql = "";
	}

private:
	BOOL Init(const BYTE* const pData, const int nCount,int& ioffset);
};

class CSearchConditionItem_TimeSpanSample : public CSearchConditionItem
{
public:
	int istime;
	int ietime;

public:
	CSearchConditionItem_TimeSpanSample()
	{
		istime = 0;
		ietime  = 0;

		sql = "";
	}

private:
	BOOL Init(const BYTE* const pData, const int nCount,int& ioffset);
};

class CSearchConditionItem_StrLike : public CSearchConditionItem
{
public:
	std::vector<CColumnItem*> vec_columnItem;
  std::vector<std::string> vec_strName;
public:
	CSearchConditionItem_StrLike()
	{
		vec_strName.clear();
		vec_columnItem.clear();

		sql = "";
	}

private:
	BOOL Init(const BYTE* const pData, const int nCount,int& ioffset);
	virtual std::string GetSql(std::string tbName);
};


class CSearchConditionItem_Equal : public CSearchConditionItem
{
public:
	CColumnItem* columnItem;
	std::string dataValue;


public:
	CSearchConditionItem_Equal();
	virtual ~CSearchConditionItem_Equal();

private:
	BOOL Init(const BYTE* const pData, const int nCount,int& ioffset);

	typedef void (CSearchConditionItem_Equal::*DealCsTypeData)(char* tm_data);
	std::map<int, DealCsTypeData> m_mapCsTypePos;
	void Deal_Type_CS_Number_Type(char* tm_data);
	void Deal_Type_CS_Text_Type(char* tm_data);
	void InitCsTypeMap(void);
};

class CSearchConditionItem_UnEqual : public CSearchConditionItem
{
public:
	CColumnItem* columnItem;
	std::string dataValue;

public:
	CSearchConditionItem_UnEqual()
	{
		columnItem = NULL;
		dataValue = "";
	}

private:
	BOOL Init(const BYTE* const pData, const int nCount,int& ioffset);
};

class CSearchConditionItem_iTimeSelect : public CSearchConditionItem
{
public:
	int istime;
	int ietime;

public:
	CSearchConditionItem_iTimeSelect()
	{
		istime = 0;
		ietime  = 0;

		sql = "";
	}

private:
	BOOL Init(const BYTE* const pData, const int nCount,int& ioffset);
};

class CSearchConditionItem_DIYSql : public CSearchConditionItem
{
public:
	CSearchConditionItem_DIYSql()
	{
		sql = "";
	}

private:
	BOOL Init(const BYTE* const pData, const int nCount,int& ioffset);
	virtual std::string GetSql(std::string tbName);
};



class CSearchConditionMaker
{
public:
	enum 
	{
    SearchConditionType_AreaSelectIntersect = 1,
    SearchConditionType_AreaSelectInside = 2,
	  SearchConditionType_AreaSelectSample = 3,
    SearchConditionType_MaxThan = 4,
    SearchConditionType_MinThan = 5,
	  SearchConditionType_Between = 6,
    SearchConditionType_InSelect = 7,
	  SearchConditionType_TimeSpanIntersect = 8,
	  SearchConditionType_TimeSpanInside = 9,
	  SearchConditionType_StrLike = 10,
	  SearchConditionType_Equal = 11,
	  SearchConditionType_UnEqual = 12,
	  SearchConditionType_CellSelect = 13,
	  SearchConditionType_iTimeSelect = 14,
	  SearchConditionType_DIYSql = 15,
	  SearchConditionType_NrCellSelect = 16,

    SearchConditionType_EndFlag = 255
	};


public:
	BOOL InitSerchCondition(const BYTE* const pData, const int nCount,int& ioffset);
	CSearchConditionItem* GetSearchCondition(const int type, const std::string columnName = "");
	std::string GetSearchConfitionSql();
	std::string GetSearchConfitionSql(std::string logfileTbName);

public:
	CSearchConditionMaker();
	~CSearchConditionMaker();

    BOOL ClearItem(void);

private:
	CSearchConditionItem* (CSearchConditionMaker::*pFunc_Maker[256])(std::string columnName);
  CSearchConditionItem* GetConditionType_Default(std::string columnName);
	CSearchConditionItem* GetConditionType_AreaSelectIntersect(std::string columnName);
	CSearchConditionItem* GetConditionType_AreaSelectInside(std::string columnName);
	CSearchConditionItem* GetConditionType_AreaSelectSample(std::string columnName);
	CSearchConditionItem* GetConditionType_MaxThan(std::string columnName);
	CSearchConditionItem* GetConditionType_MinThan(std::string columnName);
	CSearchConditionItem* GetConditionType_Between(std::string columnName);
	CSearchConditionItem* GetConditionType_InSelect(std::string columnName);
	CSearchConditionItem* GetConditionType_TimeSpanIntersect(std::string columnName);
	CSearchConditionItem* GetConditionType_TimeSpanInside(std::string columnName);
	CSearchConditionItem* GetConditionType_iTimeSelect(std::string columnName);
	CSearchConditionItem* GetConditionType_StrLike(std::string columnName);
	CSearchConditionItem* GetConditionType_Equal(std::string columnName);
	CSearchConditionItem* GetConditionType_UnEqual(std::string columnName);
	CSearchConditionItem* GetConditionType_CellSelect(std::string columnName);
	CSearchConditionItem* GetConditionType_DIYSql(std::string columnName);
	CSearchConditionItem* GetConditionType_NrCellSelect(std::string columnName);

	BOOL (CSearchConditionMaker::*pFunc_InitMaker[256])(const BYTE* const pData, const int nCount,int& ioffset);
	BOOL InitConditionType_Default(const BYTE* const pData, const int nCount,int& ioffset);
	BOOL InitConditionType_AreaSelectIntersect(const BYTE* const pData, const int nCount,int& ioffset);
	BOOL InitConditionType_AreaSelectInside(const BYTE* const pData, const int nCount,int& ioffset);
	BOOL InitConditionType_AreaSelectSample(const BYTE* const pData, const int nCount,int& ioffset);
	BOOL InitConditionType_MaxThan(const BYTE* const pData, const int nCount,int& ioffset);
	BOOL InitConditionType_MinThan(const BYTE* const pData, const int nCount,int& ioffset);
	BOOL InitConditionType_Between(const BYTE* const pData, const int nCount,int& ioffset);
	BOOL InitConditionType_InSelect(const BYTE* const pData, const int nCount,int& ioffset);
	BOOL InitConditionType_TimeSpanIntersect(const BYTE* const pData, const int nCount,int& ioffset);
	BOOL InitConditionType_TimeSpanInside(const BYTE* const pData, const int nCount,int& ioffset);
	BOOL InitConditionType_iTimeSelect(const BYTE* const pData, const int nCount,int& ioffset);
	BOOL InitConditionType_StrLike(const BYTE* const pData, const int nCount,int& ioffset);
	BOOL InitConditionType_Equal(const BYTE* const pData, const int nCount,int& ioffset);
	BOOL InitConditionType_UnEqual(const BYTE* const pData, const int nCount,int& ioffset);
	BOOL InitConditionType_CellSelect(const BYTE* const pData, const int nCount,int& ioffset);
	BOOL InitConditionType_DIYSql(const BYTE* const pData, const int nCount,int& ioffset);
	BOOL InitConditionType_NrCellSelect(const BYTE* const pData, const int nCount, int& ioffset);

private:
	std::map<std::string,CSearchConditionItem*> map_SearchConditionStr;
};




