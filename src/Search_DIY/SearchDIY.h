#pragma once
#include "SearchSock.h"
#include "./ColumnResult.h"
#include "./SqlMaker.h"

/************************************************************************/
/* TYPE OF RowResultDeal                                                */
/************************************************************************/
#define TYPE_VEC_FETCH_DATA                 0x60   //将数据存储到相应的结构 
#define TYPE_VEC_FETCH_SEND_DATA            0x61
#define TYPE_VEC_NORMAL_COLUMN              0x62

#define RESTYPE_COLUMN_DEFINE               0xeb   //通用Column	

class CSearchDIY : public ISearchDeal
{
public:
	CSearchDIY(void)
	{
		p_SqlMaker = CSqlMaker_MSSQL::Instance();

	};
public:
	virtual ~CSearchDIY(void)
	{
		
	};

public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset) = 0;

protected:
	virtual BOOL RowResultDeal(int nQType)
	{
		return TRUE;
	}
	virtual BOOL FetchSearchCondiction(const BYTE* const pData, const int nCount,int& ioffset)= 0;
	
protected:
  CSqlMaker_MSSQL* p_SqlMaker;
	BYTE m_bResponseType_;
};



