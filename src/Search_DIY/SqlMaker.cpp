#include "SqlMaker.h"

/************************************************************************/
/*   CSqlMaker_MSSQL                                                     */
/************************************************************************/
std::string
CSqlMaker_MSSQL::Sql_Stati_Grid(const std::string column, const std::string tbname, const int istime, 
                                const int ietime, const STRU_Region_Rect tm_Rect, const std::string ifileids)
{
	int tm_size = column.length() + ifileids.length() + 1000;
	char* tm_sql = new char[tm_size];
	assert(tm_sql != NULL);
	PPM_OS::memset(tm_sql, 0, tm_size);

	if (0 == tm_Rect.itllong && 0 == tm_Rect.itllat)
	{
		#ifdef USE_MYSQL
		PPM_OS::snprintf(tm_sql,tm_size, " select %s from %s where ifiletime between %d and %d and ifileid in (%s) ",
		#else
		PPM_OS::snprintf(tm_sql,tm_size, " select %s from %s with (readpast) where ifiletime between %d and %d and ifileid in (%s) ",
		#endif
			column.c_str(),\
			tbname.c_str(),\
			istime, ietime,\
			ifileids.c_str());
	}
	else if (Is_Sql_Stati_Grid_Need_Tb(tbname))
	{
		#ifdef USE_MYSQL
		PPM_OS::snprintf(tm_sql,tm_size, " select %s from %s where itllongitude >= %d and "
		#else
		PPM_OS::snprintf(tm_sql,tm_size, " select %s from %s with (readpast) where itllongitude >= %d and "
		#endif
"itllatitude <= %d and itllongitude <= %d and itllatitude >= %d and ifiletime between %d and %d and ifileid in (%s) ",
			column.c_str(),\
			tbname.c_str(),\
			tm_Rect.itllong,tm_Rect.itllat,tm_Rect.ibrlong,tm_Rect.ibrlai,\
			istime, ietime,\
			ifileids.c_str());
	}
	else 
	{
		#ifdef USE_MYSQL
		PPM_OS::snprintf(tm_sql,tm_size, " select %s from %s where ibrlongitude >= %d and "
		#else
		PPM_OS::snprintf(tm_sql,tm_size, " select %s from %s with (readpast) where ibrlongitude >= %d and "
		#endif
"ibrlatitude <= %d and itllongitude <= %d and itllatitude >= %d and ifiletime between %d and %d and ifileid in (%s) ",
			column.c_str(),\
			tbname.c_str(),\
			tm_Rect.itllong,tm_Rect.itllat,tm_Rect.ibrlong,tm_Rect.ibrlai,\
			istime, ietime,\
			ifileids.c_str());
	}

	std::string strSql = std::string(tm_sql);
	delete tm_sql;

	return strSql;
}

bool CSqlMaker_MSSQL::Is_Sql_Stati_Grid_Need_Tb(const std::string tbname)
{
	char sztbnamePart[12][40] =
	{
		"tb_scan_gsm_stati_cell_grid_",
		"tb_scan_tdscdma_stati_cell_grid_",
		"tb_scan_tdscdma_stati_grid_",
		"tb_scan_gsm_stati_grid_",
		"tb_scan_wcdma_stati_cell_grid_",
		"tb_scan_ltetopn_stati_grid_",
		"tb_scan_ltetopn_stati_cell_grid_",
		"tb_scan_lte_freqspectrum_stati_grid_",
		"tb_scan_nbiot_topn_stati_grid_",
		"tb_scan_nbiot_topn_stati_cell_grid_",
		"tb_scan_nbiot_topn_stati_rams_grid_",
		"tb_scan_nr_stati_grid_"
	};

	bool IsFind = false;
	for (int i = 0; i < 12; i++)
	{
		if (tbname.find(sztbnamePart[i]) != std::string::npos)
		{
			IsFind = true;
      break;
		}
	}

	return IsFind;
}

std::string
CSqlMaker_MSSQL::Sql_Stati_Cell(const std::string column, const std::string tbname, const int istime, const int ietime,
								const CSearchConditionItem_CellSelect* cellSelect, const std::string ifileids)
{
	int tm_size = column.length() + ifileids.length() + 1000;
	char* tm_sql = new char[tm_size];
	assert(tm_sql != NULL);
	PPM_OS::memset(tm_sql, 0, tm_size);

	if (NULL != cellSelect)
	{
		#ifdef USE_MYSQL
		PPM_OS::snprintf(tm_sql,tm_size, " select %s from %s where ifiletime between %d and %d and ilac = %d and ici = %d and ifileid in (%s) ",
		#else
		PPM_OS::snprintf(tm_sql,tm_size, " select %s from %s with (readpast) where ifiletime between %d and %d and ilac = %d and ici = %d and ifileid in (%s) ",
		#endif
			column.c_str(),\
			tbname.c_str(),\
			istime, ietime,\
			cellSelect->ilac, cellSelect->ici,
			ifileids.c_str());

	}
	else
	{
		#ifdef USE_MYSQL
		PPM_OS::snprintf(tm_sql,tm_size, " select %s from %s where ifiletime between %d and %d and ifileid in (%s) ",
		#else
		PPM_OS::snprintf(tm_sql,tm_size, " select %s from %s with (readpast) where ifiletime between %d and %d and ifileid in (%s) ",
		#endif
			column.c_str(),\
			tbname.c_str(),\
			istime, ietime,\
			ifileids.c_str());     
	}

	std::string strSql = std::string(tm_sql);
	delete tm_sql;

	return strSql;
}

std::string
CSqlMaker_MSSQL::Sql_Stati_Cell_Nr(const std::string column, const std::string tbname, const int istime, const int ietime,
	const CSearchConditionItem_NrCellSelect* nrcellSelect, const std::string ifileids)
{
	int tm_size = column.length() + ifileids.length() + 1000;
	char* tm_sql = new char[tm_size];
	assert(tm_sql != NULL);
	PPM_OS::memset(tm_sql, 0, tm_size);

	if (NULL != nrcellSelect)
	{
		#ifdef USE_MYSQL
		PPM_OS::snprintf(tm_sql, tm_size, " select %s from %s where ifiletime between %d and %d and ilac = %d and ici = %lld and ifileid in (%s) ",
		#else
		PPM_OS::snprintf(tm_sql, tm_size, " select %s from %s with (readpast) where ifiletime between %d and %d and ilac = %d and ici = %lld and ifileid in (%s) ",
		#endif
			column.c_str(), \
			tbname.c_str(), \
			istime, ietime, \
			nrcellSelect->ilac, nrcellSelect->ici,
			ifileids.c_str());
	}
	else
	{
		#ifdef USE_MYSQL
		PPM_OS::snprintf(tm_sql, tm_size, " select %s from %s where ifiletime between %d and %d and ifileid in (%s) ",
		#else
		PPM_OS::snprintf(tm_sql, tm_size, " select %s from %s with (readpast) where ifiletime between %d and %d and ifileid in (%s) ",
		#endif
			column.c_str(), \
			tbname.c_str(), \
			istime, ietime, \
			ifileids.c_str());
	}

	std::string strSql = std::string(tm_sql);
	delete tm_sql;

	return strSql;
}

std::string
CSqlMaker_MSSQL::Sql_Stati_Cell_Grid(const std::string column, const std::string tbname, const int istime, const int ietime,
								const CSearchConditionItem_CellSelect* cellSelect, 
								const CSearchConditionItem_AreaSelectIntersect* areaSelectIntersect,
								const std::string ifileids)
{
	int tm_size = column.length() + ifileids.length() + 1000;
	char* tm_sql = new char[tm_size];
	assert(tm_sql != NULL);
	PPM_OS::memset(tm_sql, 0, tm_size);

	if (NULL == cellSelect)
	{
		#ifdef USE_MYSQL
		PPM_OS::snprintf(tm_sql,tm_size, " select %s from %s where itllongitude >= %d and "
		#else
		PPM_OS::snprintf(tm_sql,tm_size, " select %s from %s with (readpast) where itllongitude >= %d and "
		#endif
"itllongitude <= %d and itllatitude <= %d and itllatitude >= %d and ifiletime between %d and %d and ifileid in (%s) ",
			column.c_str(),\
			tbname.c_str(),\
			areaSelectIntersect->itllongitude,
			areaSelectIntersect->ibrlongitude,
			areaSelectIntersect->itllatitude,
			areaSelectIntersect->ibrlatitude,
			istime, ietime,\
			ifileids.c_str());      
	}
	else
	{
		#ifdef USE_MYSQL
		PPM_OS::snprintf(tm_sql,tm_size, " select %s from %s where  itllongitude >= %d and "
		#else
		PPM_OS::snprintf(tm_sql,tm_size, " select %s from %s with (readpast) where  itllongitude >= %d and "
		#endif
"itllongitude <= %d and itllatitude <= %d and itllatitude >= %d and ifiletime between %d and %d and ilac = %d and ici = %d and ifileid in (%s) ",
			column.c_str(),\
			tbname.c_str(),\
			areaSelectIntersect->itllongitude,
			areaSelectIntersect->ibrlongitude,
			areaSelectIntersect->itllatitude,
			areaSelectIntersect->ibrlatitude,
			istime, ietime,\
			cellSelect->ilac, cellSelect->ici,
			ifileids.c_str());
	}

	std::string strSql = std::string(tm_sql);
	delete tm_sql;

	return strSql;
}

std::string
CSqlMaker_MSSQL::Sql_Stati_Cell_Grid_Nr(const std::string column, const std::string tbname, const int istime, const int ietime,
                                   const CSearchConditionItem_NrCellSelect* nrcellSelect,
                                   const CSearchConditionItem_AreaSelectIntersect* areaSelectIntersect,
                                   const std::string ifileids)
{
	int tm_size = column.length() + ifileids.length() + 1000;
	char* tm_sql = new char[tm_size];
	assert(tm_sql != NULL);
	PPM_OS::memset(tm_sql, 0, tm_size);

	if (NULL == nrcellSelect)
	{
		#ifdef USE_MYSQL
		PPM_OS::snprintf(tm_sql, tm_size, " select %s from %s where itllongitude >= %d and "
		#else
		PPM_OS::snprintf(tm_sql, tm_size, " select %s from %s with (readpast) where itllongitude >= %d and "
		#endif
"itllongitude <= %d and itllatitude <= %d and itllatitude >= %d and ifiletime between %d and %d and ifileid in (%s) ",
			column.c_str(), \
			tbname.c_str(), \
			areaSelectIntersect->itllongitude,
			areaSelectIntersect->ibrlongitude,
			areaSelectIntersect->itllatitude,
			areaSelectIntersect->ibrlatitude,
			istime, ietime, \
			ifileids.c_str());
	}
	else
	{
		#ifdef USE_MYSQL
		PPM_OS::snprintf(tm_sql, tm_size, " select %s from %s where  itllongitude >= %d and "
		#else
		PPM_OS::snprintf(tm_sql, tm_size, " select %s from %s with (readpast) where  itllongitude >= %d and "
		#endif
"itllongitude <= %d and itllatitude <= %d and itllatitude >= %d and ifiletime between %d and %d and ilac = %d and ici = %lld and ifileid in (%s) ",
			column.c_str(), \
			tbname.c_str(), \
			areaSelectIntersect->itllongitude,
			areaSelectIntersect->ibrlongitude,
			areaSelectIntersect->itllatitude,
			areaSelectIntersect->ibrlatitude,
			istime, ietime, \
			nrcellSelect->ilac, nrcellSelect->ici,
			ifileids.c_str());
	}

	std::string strSql = std::string(tm_sql);
	delete tm_sql;

	return strSql;
}

std::string
CSqlMaker_MSSQL::Sql_Stati_Cell2(const std::string column, const std::string tbname, const int istime, const int ietime,
								const CSearchConditionItem_CellSelect* cellSelect, const std::string ifileids)
{
	int tm_size = column.length() + ifileids.length() + 1000;
	char* tm_sql = new char[tm_size];
	assert(tm_sql != NULL);
	PPM_OS::memset(tm_sql, 0, tm_size);

	if (NULL == cellSelect)
	{
		#ifdef USE_MYSQL
		PPM_OS::snprintf(tm_sql,tm_size, " select %s from %s where ifiletime between %d and %d and ifileid in (%s) ",
		#else
		PPM_OS::snprintf(tm_sql,tm_size, " select %s from %s with (readpast) where ifiletime between %d and %d and ifileid in (%s) ",
		#endif
			column.c_str(),\
			tbname.c_str(),\
			istime, ietime,\
			ifileids.c_str());      
	}
	else
	{
		#ifdef USE_MYSQL
		PPM_OS::snprintf(tm_sql,tm_size, " select %s from %s where ifiletime between %d and %d and ibcch = %d and ibsic = %d and ifileid in (%s) ",
		#else
		PPM_OS::snprintf(tm_sql,tm_size, " select %s from %s with (readpast) where ifiletime between %d and %d and ibcch = %d and ibsic = %d and ifileid in (%s) ",
		#endif
			column.c_str(),\
			tbname.c_str(),\
			istime, ietime,\
			cellSelect->ilac, cellSelect->ici,
			ifileids.c_str());
	}

	std::string strSql = std::string(tm_sql);
	delete tm_sql;

	return strSql;
}

std::string
CSqlMaker_MSSQL::Sql_Stati_Cell2_Td(const std::string column, const std::string tbname, const int istime, const int ietime,
								 const CSearchConditionItem_CellSelect* cellSelect, const std::string ifileids)
{
	int tm_size = column.length() + ifileids.length() + 1000;
	char* tm_sql = new char[tm_size];
	assert(tm_sql != NULL);
	PPM_OS::memset(tm_sql, 0, tm_size);

	if (NULL == cellSelect)
	{
		#ifdef USE_MYSQL
		PPM_OS::snprintf(tm_sql,tm_size, " select %s from %s where ifiletime between %d and %d and ifileid in (%s) ",
		#else
		PPM_OS::snprintf(tm_sql,tm_size, " select %s from %s with (readpast) where ifiletime between %d and %d and ifileid in (%s) ",
		#endif
			column.c_str(),
			tbname.c_str(),
			istime, 
			ietime,
			ifileids.c_str());      
	}
	else
	{
		#ifdef USE_MYSQL
		PPM_OS::snprintf(tm_sql,tm_size, " select %s from %s where ifiletime between %d and %d and ichannel = %d and icpi = %d and ifileid in (%s) ",
		#else
		PPM_OS::snprintf(tm_sql,tm_size, " select %s from %s with (readpast) where ifiletime between %d and %d and ichannel = %d and icpi = %d and ifileid in (%s) ",
		#endif
			column.c_str(),
			tbname.c_str(),
			istime, ietime,
			cellSelect->ilac, cellSelect->ici,
			ifileids.c_str());
	}

	std::string strSql = std::string(tm_sql);
	delete tm_sql;

	return strSql;
}

std::string
CSqlMaker_MSSQL::Sql_Log_File_Time(const std::string column, const std::string tbname, const STRU_SEARCH_TIMESPAN tm_Search_TimsSpan)
{
	char tm_sql[8000];
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));

	#ifdef USE_MYSQL
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), " select %s from %s where istime <= %d and ietime >= %d ",
	#else
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), " select %s from %s with (readpast) where istime <= %d and ietime >= %d ",
	#endif
		column.c_str(),\
		tbname.c_str(),\
		tm_Search_TimsSpan.ietime, \
		tm_Search_TimsSpan.istime);

	return std::string(tm_sql);
}

std::string
CSqlMaker_MSSQL::Sql_Log_File(const std::string column, const std::string tbname, const STRU_SEARCH_TIMESPAN tm_Search_TimsSpan,
                              const STRU_SEARCH_AREA tm_Search_Area, const std::string tm_sql_part)
{
	char tm_sql[8000];
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));

	#ifdef USE_MYSQL
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), " select %s from %s where istime <= %d and ietime >= %d and %s %s ",
	#else
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), " select %s from %s with (readpast) where istime <= %d and ietime >= %d and %s %s ",
	#endif

		column.c_str(),\
		tbname.c_str(),\
		tm_Search_TimsSpan.ietime, \
		tm_Search_TimsSpan.istime,
		GetSelectAreaSql(tm_Search_Area).c_str(), 
		tm_sql_part.c_str());

	return std::string(tm_sql);
}


std::string
CSqlMaker_MSSQL::Sql_Select(const std::string column, const std::string tbname, const std::string condition)
{
	int tm_size = column.length() + condition.length() + 1000;
	char* tm_sql = new char[tm_size];
	assert(tm_sql != NULL);
	PPM_OS::memset(tm_sql, 0, tm_size);

	std::string condition_sql = CStdCommond::str_trim(condition, ' ');

	if (condition_sql.length() > 4 
		&& condition_sql.substr(0, 4) == "and ")
	{
		condition_sql = condition_sql.substr(4, condition_sql.length() -4);
	}

	#ifdef USE_MYSQL
	if (condition_sql.length() > 0)
	{
		PPM_OS::snprintf(tm_sql,tm_size, " select %s from %s where %s ",
			column.c_str(),\
			tbname.c_str(),\
			condition_sql.c_str());
	}
	else 
	{
		PPM_OS::snprintf(tm_sql,tm_size, " select %s from %s ",
			column.c_str(),\
			tbname.c_str());
	}
	#else
	if (condition_sql.length() > 0)
	{
		PPM_OS::snprintf(tm_sql,tm_size, " select %s from %s  with (readpast) where %s ",
			column.c_str(),\
			tbname.c_str(),\
			condition_sql.c_str());
	}
	else 
	{
		PPM_OS::snprintf(tm_sql,tm_size, " select %s  with (readpast) from %s ",
			column.c_str(),\
			tbname.c_str());
	}
	#endif

	std::string strSql = std::string(tm_sql);
	delete tm_sql;

	return strSql;
}


std::string
CSqlMaker_MSSQL::Sql_Select_File_Add_Status(std::string column, const std::string tbname, const std::string condition)
{
	int tm_size = column.length() + condition.length() + 1000;
	char* tm_sql = new char[tm_size];
	assert(tm_sql != NULL);
	PPM_OS::memset(tm_sql, 0, tm_size);

	std::string condition_sql = CStdCommond::str_trim(condition, ' ');

	if (condition_sql.length() > 4 && condition_sql.substr(0, 4) == "and ")
	{
		condition_sql = condition_sql.substr(4, condition_sql.length() -4);
	}

	#ifdef USE_MYSQL
	if (condition_sql.length() > 0)
	{
		PPM_OS::snprintf(tm_sql,tm_size, " select distinct %s from %s a left join tb_log_event_predeal b on a.ifileid = b.ifileid where %s ",
			column.c_str(),\
			tbname.c_str(),\
			condition_sql.c_str());
	}
	else 
	{
		PPM_OS::snprintf(tm_sql,tm_size, " select distinct %s from %s a left join tb_log_event_predeal b on a.ifileid = b.ifileid ",
			column.c_str(),\
			tbname.c_str());
	}
	#else
	if (condition_sql.length() > 0)
	{
		PPM_OS::snprintf(tm_sql,tm_size, " select distinct %s from %s a left join tb_log_event_predeal b with (readpast) on a.ifileid = b.ifileid where %s ",
			column.c_str(),\
			tbname.c_str(),\
			condition_sql.c_str());
	}
	else 
	{
		PPM_OS::snprintf(tm_sql,tm_size, " select distinct %s with (readpast) from %s a left join tb_log_event_predeal b on a.ifileid = b.ifileid ",
			column.c_str(),\
			tbname.c_str());
	}
	#endif

	std::string strSql = std::string(tm_sql);
	delete tm_sql;

	return strSql;
}
