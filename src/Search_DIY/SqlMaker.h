#pragma once

#include "./SearchConditionMaker.h"

class CSqlMaker
{
public:
	CSqlMaker(void){};
	~CSqlMaker(void){};

public:
    virtual std::string Sql_Area_Cover_Grid(const std::string column, const std::string tbname, const STRU_SEARCH_TIMESPAN tm_Search_TimsSpan, 
		const STRU_SEARCH_AREA tm_Search_Area, const std::string ifileids) = 0;

	virtual std::string Sql_Log_File_Time(const std::string column, const std::string tbname, const STRU_SEARCH_TIMESPAN tm_Search_TimsSpan) = 0;

	virtual std::string Sql_Log_File(const std::string column, const std::string tbname, const STRU_SEARCH_TIMESPAN tm_Search_TimsSpan,
		const STRU_SEARCH_AREA tm_Search_Area, const std::string tm_sql_part) = 0;

};




class CSqlMaker_MSSQL //: public CSqlMaker
{
public:
	static CSqlMaker_MSSQL* Instance (void)
	{
		static CSqlMaker_MSSQL *m_pCSqlMaker_MSSQL = NULL;

		if (m_pCSqlMaker_MSSQL == 0)
		{
			m_pCSqlMaker_MSSQL = new CSqlMaker_MSSQL;
			if(NULL == m_pCSqlMaker_MSSQL){
				return NULL;
			}
		}
		return m_pCSqlMaker_MSSQL;
	}

private:
	CSqlMaker_MSSQL(void){};

public:
	virtual ~CSqlMaker_MSSQL(void){};

public:
	virtual std::string Sql_Log_File_Time(const std::string column, const std::string tbname, const STRU_SEARCH_TIMESPAN tm_Search_TimsSpan);

	virtual std::string Sql_Log_File(const std::string column, const std::string tbname, const STRU_SEARCH_TIMESPAN tm_Search_TimsSpan,
		const STRU_SEARCH_AREA tm_Search_Area, const std::string tm_sql_part);

	virtual std::string Sql_Stati_Grid(const std::string column, const std::string tbname, const int istime, const int ietime,
                                  const STRU_Region_Rect tm_Rect, 
                                  const std::string ifileids);

	virtual std::string Sql_Stati_Cell(const std::string column, const std::string tbname, const int istime, const int ietime,
		const CSearchConditionItem_CellSelect*, const std::string ifileids);
	
	virtual std::string Sql_Stati_Cell_Nr(const std::string column, const std::string tbname, const int istime, const int ietime,
		const CSearchConditionItem_NrCellSelect*, const std::string ifileids);

	virtual std::string Sql_Stati_Cell_Grid(const std::string column, const std::string tbname, const int istime, const int ietime,
		const CSearchConditionItem_CellSelect*, const CSearchConditionItem_AreaSelectIntersect*, const std::string ifileids);

	virtual std::string Sql_Stati_Cell_Grid_Nr(const std::string column, const std::string tbname, const int istime, const int ietime,
		const CSearchConditionItem_NrCellSelect*, const CSearchConditionItem_AreaSelectIntersect*, const std::string ifileids);

	virtual std::string Sql_Stati_Cell2(const std::string column, const std::string tbname, const int istime, const int ietime,
		const CSearchConditionItem_CellSelect*, const std::string ifileids);
	virtual std::string Sql_Stati_Cell2_Td(const std::string column, const std::string tbname, const int istime, const int ietime,
		const CSearchConditionItem_CellSelect*, const std::string ifileids);

	virtual std::string Sql_Select(const std::string column, const std::string tbname, const std::string condition);

	virtual std::string Sql_Select_File_Add_Status(std::string column, const std::string tbname, const std::string condition);

private:
	std::string GetSelectAreaSql(const STRU_SEARCH_AREA tm_Search_Area)
	{
		char tm_sql[2000];
		PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
		PPM_OS::snprintf(tm_sql,sizeof(tm_sql), " ibrlongitude >= %d and ibrlatitude <= %d and itllongitude <= %d and itllatitude >= %d", 
			            tm_Search_Area.itllongitude,
			            tm_Search_Area.itllatitude,
			            tm_Search_Area.ibrlongitude,
						tm_Search_Area.ibrlatitude);
		return std::string(tm_sql);
	};

	std::string GetSelectTimePeriodSql(const STRU_SEARCH_TIMESPAN tm_sql_part)
	{
		char tm_sql[2000];
		PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
		PPM_OS::snprintf(tm_sql,sizeof(tm_sql), " ifiletime between %d and %d",\
			tm_sql_part.istime,
			tm_sql_part.ietime);
		return std::string(tm_sql);
	};
	
	bool Is_Sql_Stati_Grid_Need_Tb(const std::string tbname);
};
