// ComDef_Authentication.h: interface for the CComDef_Authentication class.
//
//////////////////////////////////////////////////////////////////////

#ifndef __COMDEF_AUTHENTICATION_H__
#define __COMDEF_AUTHENTICATION_H__

//authentication
#define REQTYPE_AUTHENTICATION_USER				0x30	//用户名
#define REQTYPE_AUTHENTICATION_PASSWORD			0x31	//密码

#define REQTYPE_AUTHENTICATION_USER2			0x32	//用户名
#define REQTYPE_AUTHENTICATION_PASSWORD2		0x33	//密码


#define RESTYPE_AUTHENTICATION_RAND				0x70	//随机码
#define RESTYPE_AUTHENTICATION_RESULT			0x71	//鉴权结果

//鉴权结果
//0：鉴权成功
//1：不允许连接
//2：未知用户名
//3：密码错误
//4：达到最大连接数
//5：服务器注册失败
//8：密码过期
//9：账号无效

#define AUTH_RESULT_SUCCEED				0
#define AUTH_RESULT_DENIED				1
#define AUTH_RESULT_UNKNOWNUSER			2
#define AUTH_RESULT_PASSWORDERROR		3
#define AUTH_RESULT_MAXCONNECTNUM		4
#define AUTH_RESULT_ERRLICENSE			5
#define AUTH_RESULT_DBCONNECTFAILED		6
#define AUTH_VERSION_LOWER				7
#define AUTH_USER_EXCEPTION             8

#endif 
