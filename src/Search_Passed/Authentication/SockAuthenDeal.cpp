// SockAuthenDeal.cpp: implementation of the CSockAuthenDeal class.
//
//////////////////////////////////////////////////////////////////////

#include "./SockAuthenDeal.h"
#include "./StdNetBuffer.h"
#include "./md5.h"
// #include "./DtSockServer.h"
#include "DtDrvAppMain.h"
#include "DataBaseInfo.h"

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CSockAuthenDeal::CSockAuthenDeal()
{
	m_pDataBaseDeal_ = NULL;
    m_pServerDeal_ = NULL;
	PPM_OS::memset(&m_stru_authinfo_,0,sizeof(m_stru_authinfo_));

	m_bIsAuthenticated_ = 0;

	m_eUserState = Authen_None;

	for(int i=0;i<256;i++)
	{
		pfunc_RowResultDeal[i] = &CSockAuthenDeal::RowResultDeal_default;
		pfunc_AddSendValue[i] = &CSockAuthenDeal::AddDefaultValue;
		pfunc_AuthenDealData[i] = &CSockAuthenDeal::AuthenDealDefault;
		pFuncMng[i] = &CSockAuthenDeal::MngConfigDefault;
	}

	pFuncMng[REQTYPE_CONFIG_VERSION] = &CSockAuthenDeal::MngConfigVersion;
	pFuncMng[REQTYPE_CONFIG_UPDATE_DESC] = &CSockAuthenDeal::MngConfigUpdateDesc;
	pFuncMng[REQTYPE_CONFIG_ROLE] = &CSockAuthenDeal::MngConfigRole;
	pFuncMng[REQTYPE_CONFIG_ROLE_FUNC] = &CSockAuthenDeal::MngConfigRoleFunc;
	pFuncMng[REQTYPE_CONFIG_USERCITY] = &CSockAuthenDeal::MngConfigUser_City;

	pfunc_RowResultDeal[TYPE_VEC_AUTHUSERCHECK] = &CSockAuthenDeal::RowResultDeal_Auth_UserCheck;
	pfunc_RowResultDeal[TYPE_VEC_AUTHUSERCHECK2] = &CSockAuthenDeal::RowResultDeal_Auth_UserCheck2;
	pfunc_RowResultDeal[TYPE_VEC_AUTHUSERFUNCGET] = &CSockAuthenDeal::RowResultDeal_Auth_UserFuncGet;
	pfunc_RowResultDeal[TYPE_INT_USETSTATE] = &CSockAuthenDeal::RowResultDeal_default;
	
	pfunc_AuthenDealData[REQTYPE_AUTHENTICATION_USER] = &CSockAuthenDeal::AuthenDealUser;
	pfunc_AuthenDealData[REQTYPE_AUTHENTICATION_USER2] = &CSockAuthenDeal::AuthenDealUser2;
	pfunc_AuthenDealData[REQTYPE_AUTHENTICATION_PASSWORD] = &CSockAuthenDeal::AuthenDealPassWord;
	pfunc_AuthenDealData[REQTYPE_AUTHENTICATION_PASSWORD2] = &CSockAuthenDeal::AuthenDealPassWord2;		
}

CSockAuthenDeal::~CSockAuthenDeal()
{

}

/************************************************************************
设置通讯处理指针
************************************************************************/
void 
CSockAuthenDeal::SetSockServerDeal(PPM_Server_Deal_Base* pServerDeal)
{
	m_pServerDeal_ = pServerDeal;
}

/************************************************************************
处理输出结果
************************************************************************/
BOOL 
CSockAuthenDeal::RowResultDeal(int nQType)
{
	return (this->*pfunc_RowResultDeal[nQType % 0x100])();
}

BOOL 
CSockAuthenDeal::RowResultDeal_default()
{
	return FALSE;
}


/************************************************************************
 鉴权流程                                                                    
************************************************************************/
BOOL
CSockAuthenDeal::Authencate(const BYTE* const pData, const int nCount,int& ioffset)
{
	
	if(FALSE == CConfigSetting::Instance()->m_bIsValid_){
		FeedBackAuthResult(AUTH_RESULT_ERRLICENSE);
		return FALSE;
	}
	
	MOVENEXTTEST(ioffset,2,nCount);
	
	//首先查询版本信息
	if((pData[0] == CMD1_CONFIG_MNG) && (pData[1] == CMD2_REQUEST))
	{
		(this->*pFuncMng[pData[2]])(pData,nCount,ioffset);
	}
		
	//first should recv authentication response
	//首先 ，必须收到鉴权响应，如果没有收到鉴权响应 ，则发送失败并关闭连接
	
	if(0 == m_bIsAuthenticated_)
	{ 
		Authentication0(pData, nCount, ioffset);
	}
	else if(0xff == m_bIsAuthenticated_)
	{
		Authenticationff(pData, nCount, ioffset);
	}
	else if (1 == m_bIsAuthenticated_)
	{
		m_eUserState = Authen_Finish;
		return TRUE;
	}
	return FALSE;
}

void CSockAuthenDeal::Authentication0(const BYTE* const pData, const int nCount,int& ioffset)
{
	m_pDataBaseDeal_->SetDbConnPara(CConfigSetting::Instance()->m_stru_DBSetting_);

	if(CMD1_AUTHENTICATION == pData[ioffset++])
	{
		if(FALSE == Authentication(pData,nCount,ioffset))
		{
			PPM_DEBUG((LM_ERROR,"Authentication failed!\n"));
			m_bIsAuthenticated_ = 0xff;
		}
		else
		{

		}
	}
	else
	{
		m_bIsAuthenticated_ = 0xff;
	}
}

void CSockAuthenDeal::Authenticationff(const BYTE* const pData, const int nCount,int& ioffset)
{
	m_pDataBaseDeal_->SetDbConnPara(CConfigSetting::Instance()->m_stru_DBSetting_);

	if(CMD1_AUTHENTICATION == pData[ioffset++])
	{
		if(FALSE == Authentication(pData,nCount,ioffset))
		{
			PPM_DEBUG((LM_ERROR,"Authentication failed!\n"));
			m_bIsAuthenticated_ = 0xaa;
		}
	}
	else
	{
		m_bIsAuthenticated_ = 0xaa;
	}
}

BOOL CSockAuthenDeal::MngConfigDefault(const BYTE* const pData, const int nCount,int& ioffset)
{
	return FALSE;
}

BOOL CSockAuthenDeal::MngConfigUser_City(const BYTE* const pData, const int nCount,int& ioffset)
{
    MOVENEXT(ioffset,3,nCount)
    return MngConfigUserCity(pData,nCount,ioffset);
}

///返回鉴权结果
void 
CSockAuthenDeal::FeedBackAuthResult(BYTE btype)
{
	if(NULL != m_pServerDeal_){
		//构造信息发送
		int tm_ioffset = 0;
		BuildSendBufHeadCmdCode(tm_ioffset, btype);
		
		if(0 == btype){
			WORD tm_count = m_vec_funcidlist_.size();
			WORD tm_countbak = MAKEWORD_NETSEQ1(tm_count);
			PPM_OS::memcpy(m_pSendBuf_ + tm_ioffset,&tm_countbak,2);
			tm_ioffset += 2;
			for(int i = 0 ;i < tm_count;i++)
			{
				int tm_funcid = m_vec_funcidlist_[i];
				tm_funcid = MAKEINT_NETSEQ1(tm_funcid);
				PPM_OS::memcpy(m_pSendBuf_ + tm_ioffset,&tm_funcid,4);
				tm_ioffset += 4;
			}
			//返回当前客户端数量
			int nClient = MAKEINT_NETSEQ1(CDtDrvAppMain::Instence()->GetClientCount());
			PPM_OS::memcpy(m_pSendBuf_ + tm_ioffset,&nClient,4);
			tm_ioffset += 4;
		}

		m_pServerDeal_->SendData(m_pSendBuf_,tm_ioffset);
	}
	
}

void 
CSockAuthenDeal::BuildSendBufHeadCmdCode(int& tm_ioffset, BYTE btype)
{
	m_pSendBuf_[tm_ioffset++] = CMD1_AUTHENTICATION;//命令字
	m_pSendBuf_[tm_ioffset++] = CMD2_RESPONSE;//子命令字
	m_pSendBuf_[tm_ioffset++] = RESTYPE_AUTHENTICATION_RESULT;//响应类型
	m_pSendBuf_[tm_ioffset++] = btype;//响应类型
}

///返回鉴权结果
void 
CSockAuthenDeal::FeedBackAuthResult(BYTE btype, int iid, std::string strComment)
{
	if(NULL != m_pServerDeal_){
		//构造信息发送
		int tm_ioffset = 0;
		BuildSendBufHeadCmdCode(tm_ioffset, btype);

		if(0 == btype){
			WORD tm_count = m_vec_funcidlist_.size();
			WORD tm_countbak = MAKEWORD_NETSEQ1(tm_count);
			PPM_OS::memcpy(m_pSendBuf_ + tm_ioffset,&tm_countbak,2);
			tm_ioffset += 2;
			for(int i = 0 ;i < tm_count;i++){
				int tm_funcid = m_vec_funcidlist_[i];
				tm_funcid = MAKEINT_NETSEQ1(tm_funcid);
				PPM_OS::memcpy(m_pSendBuf_ + tm_ioffset,&tm_funcid,4);
				tm_ioffset += 4;
			}
			//返回当前客户端数量
			int nClient = MAKEINT_NETSEQ1(CDtDrvAppMain::Instence()->GetClientCount());
			PPM_OS::memcpy(m_pSendBuf_ + tm_ioffset,&nClient,4);
			tm_ioffset += 4;

			iid = MAKEINT_NETSEQ1(iid);
			PPM_OS::memcpy(m_pSendBuf_ + tm_ioffset, &iid, 4);
			tm_ioffset += 4;

			short len = PPM_OS::strlen(strComment.c_str());
			short nlen = MAKEWORD_NETSEQ1(len);
			PPM_OS::memcpy(m_pSendBuf_ + tm_ioffset, &nlen, 2);
			tm_ioffset += 2;
			PPM_OS::memcpy(m_pSendBuf_ + tm_ioffset, strComment.c_str(), len);
			tm_ioffset += len;
		}

		m_pServerDeal_->SendData(m_pSendBuf_,tm_ioffset);
	}

}

void CSockAuthenDeal::FeedBackAuthResultEx(int state, char* strcomment)
{
	if(NULL != m_pServerDeal_){
		//构造信息发送
		int tm_ioffset = 0;
	
		m_pSendBuf_[tm_ioffset++] = CMD1_AUTHENTICATION;//命令字
		m_pSendBuf_[tm_ioffset++] = CMD2_RESPONSE;//子命令字
		m_pSendBuf_[tm_ioffset++] = RESTYPE_AUTHENTICATION_RESULT;//响应类型
		m_pSendBuf_[tm_ioffset++] = AUTH_USER_EXCEPTION;//响应类型

		state =  MAKEINT_NETSEQ1(state);
		PPM_OS::memcpy(m_pSendBuf_ + tm_ioffset,&state,4);
		tm_ioffset += 4;

		short len = PPM_OS::strlen(strcomment);

		len = MAKEWORD_NETSEQ1(len);
		PPM_OS::memcpy(m_pSendBuf_ + tm_ioffset,&len,2);
		tm_ioffset += 2;

		len = MAKEWORD_NETSEQ1(len);
		PPM_OS::memcpy(m_pSendBuf_ + tm_ioffset,strcomment,len);
		tm_ioffset += len;

		m_pServerDeal_->SendData(m_pSendBuf_,tm_ioffset);
	}
}

/************************************************************************

************************************************************************/
BOOL  
CSockAuthenDeal::MngConfigVersion(const BYTE* const pData, const int nCount,int& ioffset)
{
	CConfigSetting* tm_setting = CConfigSetting::Instance();
	BOOL res = tm_setting->ReadVersion();

	if(FALSE == res){
		return FALSE;
	}

	if(NULL != m_pServerDeal_){
		PPM_OS::memset(m_pSendBuf_,0,SEND_BUF_MAXSIZE);
		
		int tm_ioffset = 0;
		//构造信息发送
		BuildSendBufHeadCmdCode(tm_ioffset);

		CSearch_PI::AddIntToBuf(m_pSendBuf_,tm_ioffset,CConfigSetting::Instance()->m_iVersion_, SEND_BUF_MAXSIZE);
		
		if(0 > m_pServerDeal_->SendData(m_pSendBuf_,tm_ioffset)){
			return -1;
		}
	}
	return TRUE;
	
}

void CSockAuthenDeal::BuildSendBufHeadCmdCode(int& tm_ioffset)
{
	m_pSendBuf_[tm_ioffset++] = CMD1_CONFIG_MNG;//命令字
	m_pSendBuf_[tm_ioffset++] = CMD2_RESPONSE;//子命令字
	m_pSendBuf_[tm_ioffset++] = RESTYPE_CONFIG_VERSION;//响应类型
}

/************************************************************************

************************************************************************/
BOOL  
CSockAuthenDeal::MngConfigUserCity(const BYTE* const pData, const int nCount,int& ioffset)
{
	CConfigSetting* tm_setting = CConfigSetting::Instance();
	BOOL res = tm_setting->ReadUserCity(pData, nCount,ioffset);

	if(FALSE == res)
	{
		//处理结束
		FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
		return FALSE;
	}
		
	if(NULL == m_pServerDeal_)
	{
		//处理结束
		FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
		return TRUE;
	}

	if(0 > SendData())
	{
		return -1;
	}

	//处理结束
	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
	return TRUE;
	
}




/************************************************************************

************************************************************************/
BOOL  
CSockAuthenDeal::MngConfigUpdateDesc(const BYTE* const pData, const int nCount,int& ioffset)
{
	ioffset = 3;
	int temp;
	PPM_OS::memcpy(&temp,pData + ioffset,4);
	temp = MAKEINT_NETSEQ1(temp);
	
	CConfigSetting* tm_setting = CConfigSetting::Instance();
	BOOL res = tm_setting->ReadUpdateDesc(temp);
	
	if(FALSE == res){
		return FALSE;
	}
	
	if(NULL != m_pServerDeal_)
	{
		PPM_OS::memset(m_pSendBuf_,0,SEND_BUF_MAXSIZE);
		
		for(size_t i = 0;i < tm_setting->m_vec_pUpdateDesc_.size();i++)
		{
			int tm_ioffset = 0;
			//构造信息发送
			m_pSendBuf_[tm_ioffset++] = CMD1_CONFIG_MNG;//命令字
			m_pSendBuf_[tm_ioffset++] = CMD2_RESPONSE;//子命令字
			m_pSendBuf_[tm_ioffset++] = RESTYPE_CONFIG_UPDATE_DESC;//响应类型
			
			CSearch_PI::AddIntToBuf(m_pSendBuf_,tm_ioffset,CConfigSetting::Instance()->m_vec_pUpdateDesc_[i].version, SEND_BUF_MAXSIZE);
			CSearch_PI::AddIntToBuf(m_pSendBuf_,tm_ioffset,CConfigSetting::Instance()->m_vec_pUpdateDesc_[i].time, SEND_BUF_MAXSIZE);
			CSearch_PI::AddStringToBuf(m_pSendBuf_,tm_ioffset,CConfigSetting::Instance()->m_vec_pUpdateDesc_[i].desc, SEND_BUF_MAXSIZE);
			
			if(0 > m_pServerDeal_->SendData(m_pSendBuf_,tm_ioffset)){
				return -1;
			}
		}
		
		//处理结束
		FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
	}
	return TRUE;
	
}


BOOL  
CSockAuthenDeal::MngConfigRole(const BYTE* const pData, const int nCount,int& ioffset)
{
	CConfigSetting* tm_setting = CConfigSetting::Instance();
	BOOL res = tm_setting->ReadRole();
	
	if(FALSE == res){
		return FALSE;
	}
	
	if(NULL != m_pServerDeal_)
	{
		PPM_OS::memset(m_pSendBuf_,0,SEND_BUF_MAXSIZE);
		
		for(std::vector<STRU_ROLE>::size_type i = 0;i < tm_setting->m_vec_pRole_.size();i++)
		{
			int tm_ioffset = 0;
			//构造信息发送
			m_pSendBuf_[tm_ioffset++] = CMD1_CONFIG_MNG;//命令字
			m_pSendBuf_[tm_ioffset++] = CMD2_RESPONSE;//子命令字
			m_pSendBuf_[tm_ioffset++] = RESTYPE_CONFIG_ROLE;//响应类型
			
			CSearch_PI::AddIntToBuf(m_pSendBuf_,tm_ioffset,CConfigSetting::Instance()->m_vec_pRole_[i].role_id, SEND_BUF_MAXSIZE);
			CSearch_PI::AddStringToBuf(m_pSendBuf_,tm_ioffset,CConfigSetting::Instance()->m_vec_pRole_[i].role_name, SEND_BUF_MAXSIZE);
			CSearch_PI::AddStringToBuf(m_pSendBuf_,tm_ioffset,CConfigSetting::Instance()->m_vec_pRole_[i].role_desc, SEND_BUF_MAXSIZE);
			
			if(0 > m_pServerDeal_->SendData(m_pSendBuf_,tm_ioffset)){
				return -1;
			}
		}
		
		//处理结束
		FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
	}
	return TRUE;
	
}


BOOL  
CSockAuthenDeal::MngConfigRoleFunc(const BYTE* const pData, const int nCount,int& ioffset)
{
	CConfigSetting* tm_setting = CConfigSetting::Instance();
	BOOL res = tm_setting->ReadRoleFunc();
	
	if(FALSE == res){
		return FALSE;
	}
	
	if(NULL != m_pServerDeal_)
	{
		PPM_OS::memset(m_pSendBuf_,0,SEND_BUF_MAXSIZE);
		
		for(size_t i = 0;i < tm_setting->m_vec_pRoleFunc_.size();i++)
		{
			int tm_ioffset = 0;
			//构造信息发送
			m_pSendBuf_[tm_ioffset++] = CMD1_CONFIG_MNG;//命令字
			m_pSendBuf_[tm_ioffset++] = CMD2_RESPONSE;//子命令字
			m_pSendBuf_[tm_ioffset++] = RESTYPE_CONFIG_ROLE_FUNC;//响应类型
			
			CSearch_PI::AddIntToBuf(m_pSendBuf_,tm_ioffset,CConfigSetting::Instance()->m_vec_pRoleFunc_[i].role_id, SEND_BUF_MAXSIZE);
			CSearch_PI::AddIntToBuf(m_pSendBuf_,tm_ioffset,CConfigSetting::Instance()->m_vec_pRoleFunc_[i].subfunc_id, SEND_BUF_MAXSIZE);
			
			if(0 > m_pServerDeal_->SendData(m_pSendBuf_,tm_ioffset)){
				return -1;
			}
		}
		
		//处理结束
		FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
	}
	return TRUE;
}


/**
用户鉴权
*/
BOOL  
CSockAuthenDeal::Authentication(const BYTE* const pData, const int nCount,int& ioffset)
{
	MOVENEXTTEST(ioffset,1,nCount);
	if(CMD2_REQUEST == pData[ioffset++])//请求
	{
		MOVENEXTTEST(ioffset,1,nCount)
    BOOL ret = (this->*pfunc_AuthenDealData[pData[ioffset++]])(pData, nCount, ioffset);
		return ret;
	}
	else //响应 
	{
		return FALSE;
	}
	return TRUE;
}

BOOL CSockAuthenDeal::AuthenDealUser(const BYTE* const pData, const int nCount,int& ioffset)
{
	int tm_ires = Authentication_user(pData,nCount,ioffset);
	if(0 >= tm_ires )
	{
		if(-2 == tm_ires)
		{
			FeedBackAuthResult(AUTH_RESULT_DBCONNECTFAILED);
		}
		else if (-3 == tm_ires)
		{
			FeedBackAuthResult(AUTH_VERSION_LOWER);
		}
		else
		{
			FeedBackAuthResult(AUTH_RESULT_UNKNOWNUSER);
			m_eUserState = Authen_UnknowUser;
		}
		return FALSE;
	}
	else
	{
		return TRUE;
	}
}
BOOL CSockAuthenDeal::AuthenDealUser2(const BYTE* const pData, const int nCount,int& ioffset)
{
	int tm_ires = Authentication_user(pData,nCount,ioffset);
	if(0 >= tm_ires )
	{
		if(-2 == tm_ires)
		{
			FeedBackAuthResult(AUTH_RESULT_DBCONNECTFAILED);
		}
		else if (-3 == tm_ires)
		{
			FeedBackAuthResult(AUTH_VERSION_LOWER);
		}
		else
		{
			FeedBackAuthResult(AUTH_RESULT_UNKNOWNUSER);
		}
		return FALSE;
	}
	else
	{
		return TRUE;
	}
}
BOOL CSockAuthenDeal::AuthenDealPassWord(const BYTE* const pData, const int nCount,int& ioffset)
{
	if(TRUE == Authentication_password(pData,nCount,ioffset))
	{
		m_bIsAuthenticated_ = 1;
		return TRUE;
	}
	else
	{
		return FALSE;
	}
}
BOOL CSockAuthenDeal::AuthenDealPassWord2(const BYTE* const pData, const int nCount,int& ioffset)
{
	if(TRUE == Authentication_password2(pData,nCount,ioffset))
	{
		m_bIsAuthenticated_ = 1;
		return TRUE;
	}
	else
	{
		return FALSE;
	}
}

BOOL CSockAuthenDeal::AuthenDealDefault(const BYTE* const pData, const int nCount,int& ioffset)
{
    return FALSE;
}
/************************************************************************
	1 鉴权类型: 1:登陆鉴权,2:后续操作鉴权
	2 用户名(字符串);
************************************************************************/
int 
CSockAuthenDeal::Authentication_user(const BYTE* const pData, const int nCount,int& ioffset)
{
	char tm_user[255];
	char tm_str [255];
	PPM_OS::memset(tm_user,0,sizeof(tm_user));
	PPM_OS::memset(tm_str,0,sizeof(tm_str));
	MOVENEXTTEST(ioffset,1,nCount);
	m_stru_authinfo_.bAuthType = pData[ioffset++];
	
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_user,sizeof(tm_user)));
	std::string unix_user = CUtility::GBKToUtf8(tm_user);

	PPM_OS::snprintf(m_stru_authinfo_.pchLogonName,sizeof(m_stru_authinfo_.pchLogonName),unix_user.c_str());
	
	//增加鉴权申请ID
	int tm_idbid = 0;
	MOVENEXTTEST(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_idbid,pData+ioffset,4);
	tm_idbid = MAKEINT_NETSEQ1(tm_idbid);
	MOVENEXT(ioffset,4,nCount);

	if (-3 == CompareVersion(pData, nCount, ioffset))
	{
		return -3;
	}

	if(1 == m_stru_authinfo_.bAuthType)
	{
		int nRet = DealAuthTypeOne(tm_user);
		if (0 >= nRet){
      return nRet;
		}
	}
	else
	{
		//检查该用户是否在列表中
		STRU_USERINFO tm_userinfo;
		tm_userinfo = CConfigSetting::Instance()->FindAuthPassword(m_stru_authinfo_.pchLogonName);
		if("" == tm_userinfo.strpassword)
		{
			int nRet = DealPasswordEmpty(tm_user, tm_idbid);
			if (0 > nRet)
			{
				return nRet;
			}
		}
		else
		{
			DealPasswordNonEmpty(tm_userinfo, tm_idbid);
		}
		
	}
	//返回结果
	return 1;
}

int CSockAuthenDeal::DealPasswordEmpty(char* tm_user, int tm_idbid)
{
	///有可能是服务器重启了
	if(NULL == m_pDataBaseDeal_)
	{
		return -1;
	}

	PPM_DEBUG((LM_ERROR,"judge rebooted!\n"));

	int tm_ires = IsUerNameExist(tm_user);
	if(0 > tm_ires)
	{
		return tm_ires;
	}

	if(m_stru_authinfo_.iUserID <= 0)
	{
		return -1;
	}

	CConfigSetting::Instance()->SetAuthInfo(m_stru_authinfo_.pchLogonName,m_stru_authinfo_.pchPassword,m_stru_authinfo_.iDBID);
	m_stru_authinfo_.iDBIDSub = tm_idbid;
    return 0;
}
int CSockAuthenDeal::DealAuthTypeOne(char* tm_user)
{
	//检查用户名是否存在 
	if(NULL == m_pDataBaseDeal_){
		return 0;
	}

	int tm_ires = IsUerNameExist(tm_user);
	if(0 > tm_ires){
		return tm_ires;
	}

	//查询用户的权限列表信息
	if(m_stru_authinfo_.iUserID <= 0)
	{
		return -1;
	}

	CConfigSetting::Instance()->SetAuthInfo(m_stru_authinfo_.pchLogonName,m_stru_authinfo_.pchPassword,m_stru_authinfo_.iDBID);

	if(nullptr != m_pDataBaseDeal_)
	{
		tm_ires = IsAuthInfoExist();
		if(0 > tm_ires){
			return tm_ires;
		}
	}
    return 1;
}

int CSockAuthenDeal::IsUerNameExist(char* tm_user)
{
	char tm_str [255];
	PPM_OS::memset(tm_str,0,sizeof(tm_str));
	#ifdef USE_MYSQL
	PPM_OS::snprintf(tm_str,sizeof(tm_str),"call sp_auth_usercheck('%s')",tm_user);
	#else
	PPM_OS::snprintf(tm_str,sizeof(tm_str),"exec sp_auth_usercheck '%s'",tm_user);
	#endif

	int tm_ires = m_pDataBaseDeal_->SearchSql(tm_str,this,TYPE_VEC_AUTHUSERCHECK);
	Clear();
	return tm_ires;
}

void CSockAuthenDeal::DealPasswordNonEmpty(STRU_USERINFO tm_userinfo, int tm_idbid)
{
	if( (tm_userinfo.idbid != tm_idbid) && (tm_userinfo.idbid != -1) )
	{
		//适应客户端地市A用户登录地市B
	}
	PPM_OS::snprintf(m_stru_authinfo_.pchPassword,sizeof(m_stru_authinfo_.pchPassword),
		"%s",tm_userinfo.strpassword.c_str());
	FeedBackKey();

	m_stru_authinfo_.iDBIDSub = tm_idbid;
	if(-1 == tm_userinfo.idbid)
	{
		m_stru_authinfo_.iDBID = -1;
	}
	else
	{
		m_stru_authinfo_.iDBID = tm_idbid;
	}
}

int CSockAuthenDeal::DealPasswordEmpty2(char* tm_user, int tm_idbid)
{
	///有可能是服务器重启了
	if (NULL == m_pDataBaseDeal_)
	{
		return -1;
	}

	PPM_DEBUG((LM_ERROR,"judge rebooted!\n"));

	int tm_ires = IsUerNameExist2(tm_user);
	if(0 > tm_ires)
	{
		return tm_ires;
	}

	if(m_stru_authinfo_.iUserID <= 0)
	{
		return -1;
	}

	if( (m_stru_authinfo_.iDBID != tm_idbid) && (m_stru_authinfo_.iDBID != -1) )
	{
		PPM_DEBUG((LM_ERROR,"apply id is not equal to setting in database!\n"));
		return -1;
	}

	CConfigSetting::Instance()->SetAuthInfo(m_stru_authinfo_.pchLogonName,m_stru_authinfo_.pchPassword,m_stru_authinfo_.iDBID);
	m_stru_authinfo_.iDBIDSub = tm_idbid;
    return 0;
}

int CSockAuthenDeal::DealAuthTypeOne2(char* tm_user)
{
	//检查用户名是否存在 
	if(NULL == m_pDataBaseDeal_){
		return 0;
	}

	int tm_ires = IsUerNameExist2(tm_user);
	if(0 > tm_ires)
	{
		return tm_ires;
	}

	//查询用户的权限列表信息
	if(m_stru_authinfo_.iUserID <= 0)
	{
		return -1;
	}

	CConfigSetting::Instance()->SetAuthInfo(m_stru_authinfo_.pchLogonName,m_stru_authinfo_.pchPassword,m_stru_authinfo_.iDBID);

	if(NULL != m_pDataBaseDeal_){
		tm_ires = IsAuthInfoExist();
		if(0 > tm_ires)
		{
			return tm_ires;
		}
	}

	return 1;
}

int CSockAuthenDeal::IsUerNameExist2(char* tm_user)
{
	char tm_str [255];
	PPM_OS::memset(tm_str,0,sizeof(tm_str));
	PPM_OS::snprintf(tm_str,sizeof(tm_str),"exec sp_auth_usercheck2 '%s'",tm_user);

	/*********************the password of sz is 1000 char*******************************/
	//深圳移动的数据库中，需要使用用户ID生成鉴权码，同时，密码长度上限为1000，
	//系统将密码切分为4段进行读取
	Clear();

	/*********************the password of sz is 1000 char*******************************/
	int tm_ires = m_pDataBaseDeal_->SearchSql(tm_str,this,TYPE_VEC_AUTHUSERCHECK2);
	Clear();
  return tm_ires;
}

int CSockAuthenDeal::IsAuthInfoExist(void)
{
	char tm_str [255];
	PPM_OS::memset(tm_str,0,sizeof(tm_str));
	#ifdef USE_MYSQL
	PPM_OS::snprintf(tm_str,sizeof(tm_str),"call sp_auth_userfunc_get (%d)",m_stru_authinfo_.iUserID);
	#else
	PPM_OS::snprintf(tm_str,sizeof(tm_str),"exec sp_auth_userfunc_get %d",m_stru_authinfo_.iUserID);
	#endif

	int tm_ires = m_pDataBaseDeal_->SearchSql(tm_str,this, TYPE_VEC_AUTHUSERFUNCGET);
	Clear();
  return tm_ires;
}

int CSockAuthenDeal::CompareVersion(const BYTE* const pData, const int nCount,int& ioffset)
{
	if(CConfigSetting::Instance()->m_forciblyUpdate == 1)
	{
		int tm_version = 0;
		if(ioffset + 4 > nCount)
		{
			return -3;
		}
		PPM_OS::memcpy(&tm_version,pData+ioffset,4);
		tm_version = MAKEINT_NETSEQ1(tm_version);

		if(CConfigSetting::Instance()->m_iVersion_ > tm_version){
			return -3;
		}
	}

	return 0;
}

// 客户端专用的MD5加密算法
int
CSockAuthenDeal::MD52String(char *dst, char *src, size_t srclen)
{
	const int complexMatrix[16] = {1,7,0,6,1,0,0,3,8,6,0,0,4,5,0,0};
	std::string md5tmp = md5(src, srclen);

	unsigned char md5chstr[16]{};
	PPM_OS::memcmp(md5chstr, md5tmp.c_str(), 16);

	for (int i = 0; i<16; i++){
		md5chstr[i] +=complexMatrix[i];
	}

	// 返回目标字符串
	int rlen = GsmBytes2String(md5chstr, dst, 16);

	return rlen;
}

int CSockAuthenDeal::GsmBytes2String(const unsigned char *pSrc, char *pDst, int nSrcLength)
{
	const char tab[]="0123456789abcdef";    // 0x0-0xf的字符查找表
    for(int i=0; i<nSrcLength; i++)
    {
        // 输出低4位
        *pDst++ = tab[*pSrc >> 4];
        // 输出高4位
        *pDst++ = tab[*pSrc & 0x0f];
        pSrc++;	
    }
    // 输出字符串加个结束符
    *pDst = '\0';
    // 返回目标字符串长度
    return nSrcLength * 2;	
}

/************************************************************************/
/* Authentication    用户鉴权                                           */
/************************************************************************/

/************************************************************************
	1 密码(字符串)
************************************************************************/
BOOL 
CSockAuthenDeal::Authentication_password(const BYTE* const pData, const int nCount,int& ioffset)
{
	char tm_password[255];
	
	PPM_OS::memset(tm_password,0,sizeof(tm_password));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_password,sizeof(tm_password)));

	std::string unix_password = CUtility::GBKToUtf8(tm_password);

	char tm_pchinput[255];
	PPM_OS::snprintf(tm_pchinput,sizeof(tm_pchinput),"%s%s",
		m_stru_authinfo_.pchPassword,
		m_stru_authinfo_.pchRandomStr
		);

	std::string md5Result = md5rams(tm_pchinput, PPM_OS::strlen(tm_pchinput));
	PPM_OS::memcpy(m_stru_authinfo_.pchResult, md5Result.c_str(), md5Result.length());
	if(0 != PPM_OS::strcmp(unix_password.c_str(),m_stru_authinfo_.pchResult))
	{
		UpdateUserTryTime(true);

		char buff[32] = {0};
		int iid = 0;
		if( ReadUserInfo(buff, iid) && iid == 4)
		{
			// UserStatus* userState = CDataBaseInfo::Instance()->GetUserState(iid);
			m_strError = CDataBaseInfo::Instance()->GetUserState(iid)->strComment;
			m_eUserState = Authen_DIYErr;
			FeedBackAuthResultEx(iid, (char*)m_strError.c_str());
			return FALSE;
		}
		FeedBackAuthResult(AUTH_RESULT_PASSWORDERROR); //密码错误
		m_eUserState = Authen_PasswdErr;
		m_stru_authinfo_.iUserID = 0;	// added by wj
		return FALSE;
	}
	if(1 == m_stru_authinfo_.bAuthType && CDataBaseInfo::Instance()->isEnableAccountControl)
	{
		UpdateUserTryTime(false);

		char buff[32] = {0};
		int iid = 0;

		m_eUserState = Authen_Success;

		if( ReadUserInfo(buff, iid))
		{
			HandleUserInfo(buff, iid);

			DealAuthResult(iid);

            return TRUE;
		}
	}

	FeedBackAuthResult(AUTH_RESULT_SUCCEED); ////返回鉴权结果
	
	return TRUE;
}



BOOL 
CSockAuthenDeal::Authentication_password2(const BYTE* const pData, const int nCount,int& ioffset)
{
	char tm_password[1024];
	
	PPM_OS::memset(tm_password,0,sizeof(tm_password));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_password,sizeof(tm_password)));
	char tm_pchinput[1300];
	PPM_OS::snprintf(tm_pchinput,sizeof(tm_pchinput),"%s%s",
		m_stru_authinfo_.pchPassword,
		m_stru_authinfo_.pchRandomStr
		);

	std::string md5Result = md5(tm_pchinput,PPM_OS::strlen(tm_pchinput));
	PPM_OS::memcpy(m_stru_authinfo_.pchResult, md5Result.c_str(), md5Result.length());
	
	if(0 != PPM_OS::strcmp(tm_password,m_stru_authinfo_.pchResult))
	{
		UpdateUserTryTime(false);

		char buff[32] = {0};
		int iid = 0;
		if( ReadUserInfo(buff, iid) && iid == 4)
		{
			// UserStatus* userState = CDataBaseInfo::Instance()->GetUserState(iid);
			m_strError = CDataBaseInfo::Instance()->GetUserState(iid)->strComment;
			m_eUserState = Authen_DIYErr;
			FeedBackAuthResultEx(iid, (char*)m_strError.c_str());
			return FALSE;
		}

		FeedBackAuthResult(AUTH_RESULT_PASSWORDERROR); //密码错误
		return FALSE;
	}
	if(1 == m_stru_authinfo_.bAuthType && CDataBaseInfo::Instance()->isEnableAccountControl)
	{
		UpdateUserTryTime(false);

		char buff[32] = {0};
		int iid = 0;

		m_eUserState = Authen_Success;

		if( ReadUserInfo(buff, iid))
		{
			HandleUserInfo(buff, iid);

			DealAuthResult(iid);

			return TRUE;
		}
	}
	//返回结果
	FeedBackAuthResult(AUTH_RESULT_SUCCEED); //返回权限列表
	
	return TRUE;
}

void CSockAuthenDeal::DealAuthResult(int iid)
{
	UserStatus* userState = CDataBaseInfo::Instance()->GetUserState(iid);
	std::string strComment = "";
	if(userState != NULL){
		strComment = userState->strComment;
	}
	if(userState == NULL || userState->iResultType == 0)
	{
		FeedBackAuthResult(AUTH_RESULT_SUCCEED, iid, strComment); ////返回鉴权结果
	}
	else if (userState->iResultType == 8)
	{
		m_strError = CDataBaseInfo::Instance()->GetUserState(iid)->strComment;
		m_eUserState = Authen_DIYErr;
		FeedBackAuthResultEx(iid, (char*)m_strError.c_str());
	}
}

void 
CSockAuthenDeal::FeedBackKey()
{
	int tm_irandom;
	srand( (unsigned)time( NULL ) );
	tm_irandom = rand();
	PPM_OS::snprintf(m_stru_authinfo_.pchRandomStr,sizeof(m_stru_authinfo_.pchRandomStr),
		"%d",tm_irandom);
	if(NULL != m_pServerDeal_){
		//构造信息发送
		int tm_ioffset = 0;
		m_pSendBuf_[tm_ioffset++] = CMD1_AUTHENTICATION;//命令字
		m_pSendBuf_[tm_ioffset++] = CMD2_RESPONSE;//子命令字
		m_pSendBuf_[tm_ioffset++] = RESTYPE_AUTHENTICATION_RAND;//响应类型
		//随机串
		WORD tm_wlen = PPM_OS::strlen(m_stru_authinfo_.pchRandomStr);
		CStdNetBufferWriter::PushSmallInt(m_pSendBuf_, tm_ioffset, tm_wlen, SEND_BUF_MAXSIZE);
		CStdNetBufferWriter::PushBuffer(m_pSendBuf_, tm_ioffset, (int)tm_wlen, (BYTE*)(m_stru_authinfo_.pchRandomStr), SEND_BUF_MAXSIZE);
		
		//版本号
		tm_wlen = PPM_OS::strlen(CConfigSetting::Instance()->m_pchClientVersion_);
		CStdNetBufferWriter::PushSmallInt(m_pSendBuf_, tm_ioffset, tm_wlen, SEND_BUF_MAXSIZE);
        CStdNetBufferWriter::PushBuffer(m_pSendBuf_, tm_ioffset, (int)tm_wlen, (BYTE*)(CConfigSetting::Instance()->m_pchClientVersion_), SEND_BUF_MAXSIZE);
		
		//确认的数据库ID
		int tm_idbid = m_stru_authinfo_.iDBID;
		CStdNetBufferWriter::PushInt(m_pSendBuf_, tm_ioffset, tm_idbid, SEND_BUF_MAXSIZE);
		
		if(m_nUserID != 0)
		{
			CStdNetBufferWriter::PushInt(m_pSendBuf_, tm_ioffset, m_nUserID, SEND_BUF_MAXSIZE);
		}
		
		m_pServerDeal_->SendData(m_pSendBuf_,tm_ioffset);
	}
	
}


/************************************************************************
用户鉴权，数据处理                                                                
************************************************************************/
//UserID
//Password
BOOL 
CSockAuthenDeal::RowResultDeal_Auth_UserCheck()
{
	if(m_vecColumn.size() != CResultDef::Instance()->GetVecResultSize(CResultDef::SEARCHTYPE_AUTHUSERCHEK)){
		return FALSE;
	}
	if(0 == m_vecColumn.size()){
		//返回随机字符串
		FeedBackKey();

	}
	//获取ID
	m_stru_authinfo_.iUserID = *(int*)(m_vecColumn[0].pColValue);
	m_nUserID = m_stru_authinfo_.iUserID;
	//获取密码
	WORD tm_len = 0;
	tm_len = PPM_OS::strlen(((char*)m_vecColumn[1].pColValue));
	if( (tm_len > sizeof(m_stru_authinfo_.pchPassword)) || 
		(m_stru_authinfo_.iUserID <= 0) )	
	{
		//反馈错误
		//释放链接
		return FALSE;
	}
	PPM_OS::memcpy(m_stru_authinfo_.pchPassword,m_vecColumn[1].pColValue,tm_len);
	//获取dbid
	m_stru_authinfo_.iDBID = *(int*)(m_vecColumn[2].pColValue);
	
	//返回随机字符串
	FeedBackKey();

	return TRUE;
}

BOOL 
CSockAuthenDeal::RowResultDeal_Auth_UserCheck2()
{
	if(m_vecColumn.size() != 7){
		return FALSE;
	}
	if(0 == m_vecColumn.size()){
		//返回随机字符串
		FeedBackKey();
		
	}
	//获取ID
	m_stru_authinfo_.iUserID = *(int*)(m_vecColumn[0].pColValue);
	//获取密码
	WORD tm_len = 0;
	tm_len = PPM_OS::strlen(((char*)m_vecColumn[1].pColValue));
	if( (tm_len > sizeof(m_stru_authinfo_.pchPassword)) || 
		(m_stru_authinfo_.iUserID <= 0) )	
	{
		//反馈错误,释放链接
		return FALSE;
	}

	PPM_OS::memset(&m_stru_authinfo_.pchPassword, 0 ,sizeof(m_stru_authinfo_.pchPassword));

	BYTE sztmp[255];
	int offset = 0;
	PPM_OS::memset(&sztmp, 0, sizeof(sztmp));
	CStdNetBufferWriter::PushBuffer(sztmp, offset, (int)tm_len, m_vecColumn[1].pColValue, sizeof(sztmp));
	PPM_OS::snprintf(m_stru_authinfo_.pchPassword,sizeof(m_stru_authinfo_.pchPassword),"%s%s",m_stru_authinfo_.pchPassword,sztmp);

	tm_len = PPM_OS::strlen(((char*)m_vecColumn[2].pColValue));
	offset = 0;
	PPM_OS::memset(&sztmp, 0, sizeof(sztmp));
	CStdNetBufferWriter::PushBuffer(sztmp, offset, tm_len, m_vecColumn[2].pColValue, sizeof(sztmp));
	PPM_OS::snprintf(m_stru_authinfo_.pchPassword,sizeof(m_stru_authinfo_.pchPassword),"%s%s",m_stru_authinfo_.pchPassword,sztmp);

	tm_len = PPM_OS::strlen(((char*)m_vecColumn[3].pColValue));
	offset = 0;
	PPM_OS::memset(&sztmp, 0, sizeof(sztmp));
	CStdNetBufferWriter::PushBuffer(sztmp, offset, tm_len, m_vecColumn[3].pColValue, sizeof(sztmp));
	PPM_OS::snprintf(m_stru_authinfo_.pchPassword,sizeof(m_stru_authinfo_.pchPassword),"%s%s",m_stru_authinfo_.pchPassword,sztmp);

	tm_len = PPM_OS::strlen(((char*)m_vecColumn[4].pColValue));
	offset = 0;
	PPM_OS::memset(&sztmp, 0, sizeof(sztmp));
	CStdNetBufferWriter::PushBuffer(sztmp, offset, tm_len, m_vecColumn[4].pColValue, sizeof(sztmp));
	PPM_OS::snprintf(m_stru_authinfo_.pchPassword,sizeof(m_stru_authinfo_.pchPassword),"%s%s",m_stru_authinfo_.pchPassword,sztmp);

	//获取dbid
	m_stru_authinfo_.iDBID = *(int*)(m_vecColumn[5].pColValue);

	m_nUserID = *(int*)(m_vecColumn[6].pColValue);
	
	//返回随机字符串
	FeedBackKey();
	
	return TRUE;
}

///用户权限结果集处理
BOOL 
CSockAuthenDeal::RowResultDeal_Auth_UserFuncGet()
{
	if(m_vecColumn.size() != CResultDef::Instance()->GetVecResultSize(CResultDef::SEARCHTYPE_AUTHUSERFUNCGET)){
		return FALSE;
	}
	//获取ID
	int tm_funcid = *(int*)(m_vecColumn[0].pColValue);
	m_vec_funcidlist_.push_back(tm_funcid);
	return TRUE;
} 

///读取用户状态
BOOL 
CSockAuthenDeal::ReadUserInfo(char *time, int &state)
{
	char sql[1024] = {0};
	state = INT_INVALID;

	PPM_OS::snprintf(sql, 1024, "select modifypwd_time,iuserstate from tb_cfg_static_user where iid = %d", m_stru_authinfo_.iUserID);

	Clear();

	if(m_pDataBaseDeal_->SearchSql(sql, this, TYPE_INT_USETSTATE) > 0)
	{
		if(m_vecColumn[0].iDataLen > 0)
		{
			memcpy(time, m_vecColumn[0].pColValue, PPM_OS::strlen((char*)m_vecColumn[0].pColValue));
		}
		else
		{
			time[0] = '\0';
		}

		if(m_vecColumn[1].iDataLen > 0)
		{
			state = *(int*)(m_vecColumn[1].pColValue);
		}
		
		Clear();

		return TRUE;
	}

	return FALSE;
}

void
CSockAuthenDeal::UpdateUserTryTime(bool wrong)
{
	if(!CDataBaseInfo::Instance()->isEnableAccountControl){
		return;
	}

	if (wrong)
	{
		char sql[1024];
		memset(sql, 0, 1024);
		PPM_OS::snprintf(sql, 1024, "update tb_cfg_static_user set iTryTimes = iTryTimes + 1 where iid = %d", m_stru_authinfo_.iUserID);
		m_pDataBaseDeal_->ExecSql(sql);

		char szTemp[32] = {0};
		time_t t = time(NULL);
		struct tm* stime = localtime(&t);
		PPM_OS::snprintf(szTemp, 32,"%04d-%02d-%02d %02d:%02d:%02d",1900+stime->tm_year, 1+stime->tm_mon,stime->tm_mday, 
			stime->tm_hour, stime->tm_min,stime->tm_sec);

		memset(sql, 0, 1024);
        PPM_OS::snprintf(sql, 1024, "update tb_cfg_static_user set iuserstate = 4,lastlocked_time='%s' where iid = %d and iTryTimes >= %d", 
			szTemp, m_stru_authinfo_.iUserID, CDataBaseInfo::Instance()->tryTimes);
		m_pDataBaseDeal_->ExecSql(sql);
	}
	else
	{
		char sql[1024];
		memset(sql, 0, 1024);
		PPM_OS::snprintf(sql, 1024, "update tb_cfg_static_user set iTryTimes = 0 where iid = %d and iuserstate != 4", m_stru_authinfo_.iUserID);
		m_pDataBaseDeal_->ExecSql(sql);
	}
}

///用户状态处理
void 
CSockAuthenDeal::HandleUserInfo(char *szTime, int &state)
{
	char szTemp[32] = {0};
	char sql[1024] = {0};
	__int64 iTimeSpan =CDataBaseInfo::Instance()->GetPwdInvalidDays() * 24 * 60 * 60;

	time_t t = time(NULL);
	struct tm* stime = localtime(&t);
	PPM_OS::snprintf(szTemp, 32,"%04d-%02d-%02d %02d:%02d:%02d",1900+stime->tm_year, 1+stime->tm_mon,stime->tm_mday, 
		stime->tm_hour, stime->tm_min,stime->tm_sec);

	if(PPM_OS::strlen(szTime) == 0)
	{
		PPM_OS::snprintf(sql, 1024, "update tb_cfg_static_user set modifypwd_time = '%s', iuserstate = 1 where iid = %d", 
			szTemp, m_stru_authinfo_.iUserID); 
		m_pDataBaseDeal_->ExecSql(sql);
		state = 1;
	}
	else if(state == 1 || state == 2 || state == INT_INVALID)
	{
		UpdateUserTable(iTimeSpan, szTime, t, state);
	}
	else if(state == INT_INVALID)
	{
		state = 1;
	}
}

void CSockAuthenDeal::UpdateUserTable(__int64 iTimeSpan, char* szTime, time_t t, int& state)
{
	if(iTimeSpan > 0)
	{
		time_t lasttime = StringToTime_T(szTime);

		if(t - lasttime < iTimeSpan)
		{
			if(state == 2 || state == INT_INVALID)
			{
				char sql[1024] = {0};
				PPM_OS::snprintf(sql, 1024, "update tb_cfg_static_user set iuserstate = 1 where iid = %d", m_stru_authinfo_.iUserID); 
				m_pDataBaseDeal_->ExecSql(sql);
			}

			state = 1;
		}
		else
		{
			state = 2;
		}
	}
	else
	{
		state = 1;
	}
}

time_t 
CSockAuthenDeal::StringToTime_T(char *time)
{
	tm tm_;
	int year, month, day, hour, minute,second;

	try{
		sscanf(time,"%d-%d-%d %d:%d:%d", &year, &month, &day, &hour, &minute, &second);
		tm_.tm_year  = year-1900;
		tm_.tm_mon   = month-1;
		tm_.tm_mday  = day;
		tm_.tm_hour  = hour;
		tm_.tm_min   = minute;
		tm_.tm_sec   = second;
		tm_.tm_isdst = 0;
		time_t t_ = mktime(&tm_); //已经减了8个时区
		return t_; //秒时间
	}catch(...)
	{
		return 0;
	}
}

int CSockAuthenDeal::SendData(void)
{
	for (std::size_t i = 0; i<CConfigSetting::Instance()->m_vecUserCity.size(); ++i)
	{
		if(NULL != m_pServerDeal_){
			PPM_OS::memset(m_pSendBuf_,0,SEND_BUF_MAXSIZE);

			int tm_ioffset = 0;
			//构造信息发送
			m_pSendBuf_[tm_ioffset++] = CMD1_CONFIG_MNG;//命令字
			m_pSendBuf_[tm_ioffset++] = CMD2_RESPONSE;//子命令字
			m_pSendBuf_[tm_ioffset++] = RESTYPE_CONFIG_USERCITY;//响应类型

			CSearch_PI::AddIntToBuf(m_pSendBuf_,tm_ioffset,CConfigSetting::Instance()->m_vecUserCity[i], SEND_BUF_MAXSIZE);

			if(0 > m_pServerDeal_->SendData(m_pSendBuf_,tm_ioffset)){
				return -1;
			}
		}
	}
    
	return 0;
}




