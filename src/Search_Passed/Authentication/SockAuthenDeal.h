// SockAuthenDeal.h: interface for the CSockAuthenDeal class.
//
//////////////////////////////////////////////////////////////////////

#pragma once

#include "./SearchSock.h"


#define TYPE_VEC_AUTHUSERCHECK2				        0X01
#define TYPE_VEC_AUTHUSERCHECK				        0X02
#define TYPE_VEC_AUTHUSERFUNCGET			        0X03
#define TYPE_INT_USETSTATE                          0x04

class CSockAuthenDeal : public CSearchSock, public Singleton<CSockAuthenDeal>
{
public:
	enum UserState
	{
		Authen_None,
		Authen_Success,
		Authen_UnknowUser,
		Authen_PasswdErr,
		Authen_DIYErr,
		Authen_Finish
	};

	CSockAuthenDeal();
	virtual ~CSockAuthenDeal();

public:
	std::vector<int> m_vec_funcidlist_;

	int m_bIsAuthenticated_;
	std::string m_strError;

	UserState m_eUserState;

	STRU_AUTHINFO m_stru_authinfo_;

  int m_nUserID;
	
public:
  BOOL Authencate(const BYTE* const pData, const int nCount,int& ioffset);
	void SetSockServerDeal(PPM_Server_Deal_Base* pServerDeal);

private:
	//数据处理函数集
	BOOL (CSockAuthenDeal::*pfunc_RowResultDeal[256])();
	
	virtual BOOL RowResultDeal(int nQType);
	virtual BOOL RowResultDeal_default();

	//返回鉴权结果
	void FeedBackAuthResult(BYTE btype);
	void FeedBackAuthResult(BYTE btype, int iid, std::string strComment);
	void FeedBackAuthResultEx(int state, char* strcomment);

	BOOL Authentication(const BYTE* const pData, const int nCount,int& ioffset);

	BOOL (CSockAuthenDeal::*pFuncMng[256])(const BYTE* const pData, const int nCount,int& ioffset);
	BOOL MngConfigDefault(const BYTE* const pData, const int nCount,int& ioffset);
	BOOL MngConfigVersion(const BYTE* const pData, const int nCount,int& ioffset);
	BOOL MngConfigUpdateDesc(const BYTE* const pData, const int nCount,int& ioffset);
	BOOL MngConfigRole(const BYTE* const pData, const int nCount,int& ioffset);
	BOOL MngConfigRoleFunc(const BYTE* const pData, const int nCount,int& ioffset);
	BOOL MngConfigUserCity(const BYTE* const pData, const int nCount,int& ioffset);
	BOOL MngConfigUser_City(const BYTE* const pData, const int nCount,int& ioffset);


	int Authentication_user(const BYTE* const pData, const int nCount,int& ioffset);
	int Authentication_password2(const BYTE* const pData, const int nCount,int& ioffset);
	BOOL Authentication_password(const BYTE* const pData, const int nCount,int& ioffset);
	///返回鉴权密钥
	void FeedBackKey();

	BOOL RowResultDeal_Auth_UserCheck();
	BOOL RowResultDeal_Auth_UserCheck2();
	BOOL RowResultDeal_Auth_UserFuncGet();
private:
	BOOL ReadUserInfo(char *time, int &state);
	void UpdateUserTryTime(bool wrong);
	void DealAuthResult(int iid);
	void DealPasswordNonEmpty(STRU_USERINFO tm_userinfo, int tm_idbid);
	int DealPasswordEmpty(char* tm_user, int tm_idbid);
	int DealPasswordEmpty2(char* tm_user, int tm_idbid);
	int DealAuthTypeOne(char* tm_user);
  int DealAuthTypeOne2(char* tm_user);
	int IsUerNameExist(char* tm_user);
  int IsUerNameExist2(char* tm_user);
  int IsAuthInfoExist(void);
  int CompareVersion(const BYTE* const pData, const int nCount,int& ioffset);

	void HandleUserInfo(char *szTime, int &state);
	time_t StringToTime_T(char *time);

  int SendData(void);
	BOOL AuthenDealDefault(const BYTE* const pData, const int nCount,int& ioffset);
  BOOL AuthenDealUser(const BYTE* const pData, const int nCount,int& ioffset);
	BOOL AuthenDealUser2(const BYTE* const pData, const int nCount,int& ioffset);
  BOOL AuthenDealPassWord(const BYTE* const pData, const int nCount,int& ioffset);
  BOOL AuthenDealPassWord2(const BYTE* const pData, const int nCount,int& ioffset);
  BOOL (CSockAuthenDeal::*pfunc_AuthenDealData[256])(const BYTE* const pData, const int nCount,int& ioffset);
	void UpdateUserTable(__int64 iTimeSpan, char* szTime, time_t t, int& state);
  void Authentication0(const BYTE* const pData, const int nCount,int& ioffset);
	void Authenticationff(const BYTE* const pData, const int nCount,int& ioffset);
	void BuildSendBufHeadCmdCode(int& tm_ioffset, BYTE btype);
	void BuildSendBufHeadCmdCode(int& tm_ioffset);

	int GsmBytes2String(const unsigned char *pSrc, char *pDst, int nSrcLength);
	int MD52String(char *dst, char *src, size_t srclen);
};


