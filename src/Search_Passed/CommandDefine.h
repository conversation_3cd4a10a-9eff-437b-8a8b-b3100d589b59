#ifndef _DT_COMMANDDEFINE_H_
#define _DT_COMMANDDEFINE_H_

#include "./ComDef_Search.h"
#include "./ComDef_Config.h"
#include "./ComDef_Stat.h"
#include "./ComDef_Community.h"
#include "./ComDef_UserMng.h"
#include "./ComDef_TestDataMng.h"
#include "./ComDef_DBMng.h"
#include "./ComDef_Authentication.h"
#include "./ComDef_DIY.h"

//命令字1
#define CMD1_AUTHENTICATION			0x01	//用户鉴权命令字
#define CMD1_TESTDATAMANAGEMENT		0x02	//测试数据维护命令字
#define CMD1_CONFIG_MNG				0x03	//配置管理命令字
#define CMD1_DBMANAGEMENT			0x04	//数据库管理命令字
#define CMD1_SEARCH					0x06	//信息查询命令字
#define CMD1_SEARCH_STATISTIC		0x07	//统计信息查询命令字
#define CMD1_USER_MNG				0x0a	//用户权限管理命令字
#define CMD1_COMMUNITY_MNG			0x0b    //楼宇信号分析命令字

#define CMD1_DIYSEARCH              0x0c    //自定义查询操作
 

//命令字2
#define CMD2_REQUEST			0x00  //请求
#define CMD2_RESPONSE			0x01  //响应


//the end mark, used in every search operation to mark the end of search
#define RESTYPE_SEARCHEND						0xE0	//查询信息结束
#define RESTYPE_SEARCHERROR						0xEE	//查询中错误


#endif
