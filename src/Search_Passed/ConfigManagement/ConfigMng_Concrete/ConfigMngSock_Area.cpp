// ConfigMngSock_Area.cpp: implementation of the CConfigMngSock_Area class.
//
//////////////////////////////////////////////////////////////////////

#include "ConfigMngSock_Area.h"

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

/************************************************************************
ConfigMngSock_AreaInfoConfig
************************************************************************/
BOOL  
ConfigMngSock_AreaInfoConfig::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	char tm_sql[512];	
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), "exec sp_config_area_get");
	
	char tm_sql2[512];
	
	//area type
	PPM_OS::memset(tm_sql2,0,sizeof(tm_sql2));
	PPM_OS::snprintf(tm_sql2,sizeof(tm_sql2),"%s %d",tm_sql,1);
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_CONFIG_STATIC_AREATYPE;
	
	if(NULL != m_pDataBaseDeal_){
		m_pDataBaseDeal_->SearchSql(tm_sql2,this, m_iRowType_);
	}
  Clear();
	
	//local
	PPM_OS::memset(tm_sql2,0,sizeof(tm_sql2));
	PPM_OS::snprintf(tm_sql2,sizeof(tm_sql2),"%s %d",tm_sql,2);
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_CONFIG_STATIC_LOCAL;
	
	if(NULL != m_pDataBaseDeal_){
		m_pDataBaseDeal_->SearchSql(tm_sql2,this, m_iRowType_);
	}
	Clear();
	
	//district
	PPM_OS::memset(tm_sql2,0,sizeof(tm_sql2));
	PPM_OS::snprintf(tm_sql2,sizeof(tm_sql2),"%s %d",tm_sql,3);
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_CONFIG_STATIC_DISTRICT;
	
	if(NULL != m_pDataBaseDeal_){
		m_pDataBaseDeal_->SearchSql(tm_sql2,this, m_iRowType_);
	}
	Clear();
	
	//opt_district
	PPM_OS::memset(tm_sql2,0,sizeof(tm_sql2));
	PPM_OS::snprintf(tm_sql2,sizeof(tm_sql2),"%s %d",tm_sql,4);
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_CONFIG_STATIC_OPT_DISTRICT;
	SEARCHSQL(m_iRowType_, tm_sql2);
	
	//community
	PPM_OS::memset(tm_sql2,0,sizeof(tm_sql2));
	PPM_OS::snprintf(tm_sql2,sizeof(tm_sql2),"%s %d",tm_sql,5);
	m_bResponseType_ = RESTYPE_CONFIG_STATIC_COMMUNITY;
	m_iRowType_ = TYPE_VEC_NORMAL;
	
	if(NULL != m_pDataBaseDeal_){
		m_pDataBaseDeal_->SearchSql(tm_sql2,this, m_iRowType_);
		//处理结束
		FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
	}
	Clear();
	
	return TRUE;		
}


/************************************************************************
ConfigMngSock_AreaTypeConfig
************************************************************************/
BOOL  
ConfigMngSock_AreaTypeConfig::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	char tm_sql[512];	
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), "exec sp_config_stat_areatype_get");
	
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_CONFIG_STAT_AREATYPE;
	
	if(NULL != m_pDataBaseDeal_){
		m_pDataBaseDeal_->SearchSql(tm_sql,this, m_iRowType_);
		//处理结束
		FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
	}
	Clear();
	
	return TRUE;		
}


/************************************************************************
ConfigMngSock_AreaScanConfig
************************************************************************/
BOOL  
ConfigMngSock_AreaScanConfig::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	char tm_sql[512];	
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec sp_config_stat_areascan_get");
	
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_CONFIG_STAT_AREASCAN;
	
	if(NULL != m_pDataBaseDeal_){
		m_pDataBaseDeal_->SearchSql(tm_sql,this, m_iRowType_);
		//处理结束
		FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
	}
	Clear();
	
	return TRUE;		
}


/************************************************************************
ConfigMngSock_CQTAreaListGisConfig
区域CQT查询
************************************************************************/
BOOL  
ConfigMngSock_CQTAreaListGisConfig::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	MOVENEXTTEST(ioffset,6,nCount);

	int areatype;
	PPM_OS::memcpy(&areatype,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	areatype = MAKEINT_NETSEQ1(areatype);

	char tm_file[255];
	PPM_OS::memset(&tm_file, 0, sizeof(tm_file));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_file,sizeof(tm_file)));

	MOVENEXTTEST(ioffset,16,nCount);

	int ltlongitude;
	PPM_OS::memcpy(&ltlongitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	ltlongitude = MAKEINT_NETSEQ1(ltlongitude);

	int ltlatitude;
	PPM_OS::memcpy(&ltlatitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	ltlatitude = MAKEINT_NETSEQ1(ltlatitude);

	int brlongitude;
	PPM_OS::memcpy(&brlongitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	brlongitude = MAKEINT_NETSEQ1(brlongitude);

	int brlatitude;
	PPM_OS::memcpy(&brlatitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	brlatitude = MAKEINT_NETSEQ1(brlatitude);

	char tm_sql[1024];
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec sp_config_cqt_area_list_get %d,'%s',%d,%d,%d,%d" ,
		areatype,
		tm_file,
		ltlongitude,
		ltlatitude,
		brlongitude,
		brlatitude
		);
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_GIS_CQT_AREA_LIST;

	if(NULL != m_pDataBaseDeal_)
	{
		m_pDataBaseDeal_->SearchSql(tm_sql,this, m_iRowType_);
	}
	Clear();

	//处理结束
	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
	return TRUE;
}


/************************************************************************
ConfigMngSock_AreaStatConfig
************************************************************************/
BOOL  
ConfigMngSock_AreaStatConfig::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	MOVENEXTTEST(ioffset,4,nCount);
	int itemp;

	PPM_OS::memcpy(&itemp,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);

	itemp = MAKEINT_NETSEQ1(itemp);

	char tm_sql[512];	
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),	"exec sp_config_stat_area_get %d",itemp);
	
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_CONFIG_STAT_AREA;
	
	if(NULL != m_pDataBaseDeal_){
		m_pDataBaseDeal_->SearchSql(tm_sql,this, m_iRowType_);
		//处理结束
		FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
	}
	Clear();
	
	return TRUE;		
}


/************************************************************************
ConfigMngSock_AreaTypeGisConfig
************************************************************************/
BOOL  
ConfigMngSock_AreaTypeGisConfig::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
  char tm_sql[8000];
  PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), "exec sp_config_area_type_get");
	
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_GIS_AREA_TYPE;
	if(NULL != m_pDataBaseDeal_)
	{
		m_pDataBaseDeal_->SearchSql(tm_sql,this, m_iRowType_);
	}
	Clear();

	//处理结束
	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
	return TRUE;
}

/************************************************************************
ConfigMngSock_AreaListGisConfig
地域值查询
************************************************************************/
BOOL  
ConfigMngSock_AreaListGisConfig::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	MOVENEXTTEST(ioffset,6,nCount);

	int areatype;
    PPM_OS::memcpy(&areatype,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	areatype = MAKEINT_NETSEQ1(areatype);
	 
	char tm_file[255];
	PPM_OS::memset(&tm_file, 0, sizeof(tm_file));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_file,sizeof(tm_file)));

	char tm_sql[1024];
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec sp_config_area_list_get %d,'%s'" ,
		areatype,
		tm_file
		);
    m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_GIS_AREA_LIST;

	if(NULL != m_pDataBaseDeal_)
	{
		m_pDataBaseDeal_->SearchSql(tm_sql,this, m_iRowType_);
	}
	Clear();

	//处理结束
	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
	return TRUE;
}


/************************************************************************
ConfigMngSock_AreaAddConfig
************************************************************************/
BOOL  
ConfigMngSock_AreaAddConfig::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	STRU_SEARCH_AREA tm_Search_Area;
	
	int areatypeid;
	
	PPM_OS::memcpy(&areatypeid,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);	
	areatypeid = MAKEINT_NETSEQ1(areatypeid);

	//longitude and latitude
	PPM_OS::memcpy(&tm_Search_Area.itllongitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Search_Area.itllatitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	
	tm_Search_Area.ConvertToNetSeq();
	
	char szName[255];
	PPM_OS::memset(szName,0,sizeof(szName));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,szName,sizeof(szName)));

	char strComment[255];
	PPM_OS::memset(strComment,0,sizeof(strComment));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,strComment,sizeof(strComment)));


	char tm_sql[512];	
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec sp_config_area_add %d,%d,%d,'%s','%s'",
		areatypeid,
		tm_Search_Area.itllongitude,
		tm_Search_Area.itllatitude,
		szName,
		strComment);

	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_GIS_AREA_ADD;
	
	if(NULL != m_pDataBaseDeal_){
		m_pDataBaseDeal_->SearchSql(tm_sql,this, m_iRowType_);
		//处理结束
		FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
	}
	Clear();
	
	return TRUE;		
}

/************************************************************************
ConfigMngSock_AreaEditConfig
************************************************************************/
BOOL  
ConfigMngSock_AreaEditConfig::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{

    int areatypeid;
	PPM_OS::memcpy(&areatypeid,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);	
	areatypeid = MAKEINT_NETSEQ1(areatypeid);

	int areaid;
	PPM_OS::memcpy(&areaid,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);	
	areaid = MAKEINT_NETSEQ1(areaid);

	STRU_SEARCH_AREA tm_Search_Area;
	//longitude and latitude
	PPM_OS::memcpy(&tm_Search_Area.itllongitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Search_Area.itllatitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	
	tm_Search_Area.ConvertToNetSeq();
	
	char szName[255];
	PPM_OS::memset(szName,0,sizeof(szName));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,szName,sizeof(szName)));

	char szdesc[255];
	PPM_OS::memset(szdesc,0,sizeof(szName));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,szdesc,sizeof(szdesc)));
	
	char tm_sql[512];	
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec sp_config_area_edit %d,%d,%d,%d,'%s','%s'",
		areatypeid,
		areaid,
        tm_Search_Area.itllongitude,
		tm_Search_Area.itllatitude,
		szName,
		szdesc
		);
	if(NULL != m_pDataBaseDeal_)
	{
		if(0 > m_pDataBaseDeal_->ExecSql(tm_sql))
		{
			FeedBackOverIntWithThirdCmd(CMD1_SEARCH,-1);
		}
		else
		{
			FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
		}
	}	

	return TRUE;		
}


/************************************************************************
ConfigMngSock_CAreaDeleteonfig
************************************************************************/
BOOL  
ConfigMngSock_AreaDeleteonfig::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{

    int areatypeid;
	PPM_OS::memcpy(&areatypeid,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);	
	areatypeid = MAKEINT_NETSEQ1(areatypeid);

	int areaid;
	PPM_OS::memcpy(&areaid,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);	
	areaid = MAKEINT_NETSEQ1(areaid);
	
	char tm_sql[512];	
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec sp_config_area_delete %d,%d",
		areatypeid,
		areaid
        );
	if(NULL != m_pDataBaseDeal_)
	{
		if(0 > m_pDataBaseDeal_->ExecSql(tm_sql))
		{
			FeedBackOverIntWithThirdCmd(CMD1_SEARCH,-1);
		}
		else
		{
			FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
		}
	}	

	return TRUE;		
}


/************************************************************************
ConfigMngSock_CoverRegionAddConfig
************************************************************************/
BOOL  
ConfigMngSock_CoverRegionAddConfig::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{

    int type;
	PPM_OS::memcpy(&type,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);	
	type = MAKEINT_NETSEQ1(type);

	char szName[255];
	PPM_OS::memset(szName,0,sizeof(szName));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,szName,sizeof(szName)));

	char szdesc[255];
	PPM_OS::memset(szdesc,0,sizeof(szName));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,szdesc,sizeof(szdesc)));

	int status;
	PPM_OS::memcpy(&status,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);	
	status = MAKEINT_NETSEQ1(status);

	int point_count;
	PPM_OS::memcpy(&point_count,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);	
	point_count = MAKEINT_NETSEQ1(point_count);

	std::vector<STRU_SEARCH_AREA> vec_area;
	for (int ii=0; ii<point_count; ii++)
	{
		STRU_SEARCH_AREA tm_Search_Area;
		//longitude and latitude
		PPM_OS::memcpy(&tm_Search_Area.itllongitude,pData + ioffset,4);
		MOVENEXT(ioffset,4,nCount);
		PPM_OS::memcpy(&tm_Search_Area.itllatitude,pData + ioffset,4);
     	MOVENEXT(ioffset,4,nCount);
		tm_Search_Area.ConvertToNetSeq();
        
		vec_area.push_back(tm_Search_Area);
	}

	char tm_sql[1024];	
	PPM_OS::memset(tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec sp_config_cover_region_add %d,'%s','%s', %d",
		type,
		szName,
		szdesc,
		status
		);
	int tm_maxid = -1;
	if (NULL != m_pDataBaseDeal_)
	{		
		tm_maxid = m_pDataBaseDeal_->ExecSql(tm_sql);
	}
	if(tm_maxid < 0)
	{
	    FeedBackOverIntWithThirdCmd(CMD1_SEARCH, RESTYPE_GIS_COVER_REGION_ADD, -1);
		//处理结束
    	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
		return FALSE;
	}

	bool issuccess = true;
	for (size_t i=0; i<vec_area.size(); i++)
	{
		PPM_OS::memset(tm_sql, 0, sizeof(tm_sql));
		PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
			"exec sp_config_cover_point_add %d, %d, %d,%d",
			tm_maxid,
			i+1,
			vec_area[i].itllongitude,
			vec_area[i].itllatitude
			);
		if(NULL != m_pDataBaseDeal_)
		{
			int ret = m_pDataBaseDeal_->ExecSql(tm_sql);
			if(0 > ret)
			{
				issuccess = false;
				break;
			}
		}	
	}

    if (issuccess)
    {   		
		FeedBackOverIntWithThirdCmd(CMD1_SEARCH, RESTYPE_GIS_COVER_REGION_ADD, tm_maxid);
    }
	else
	{
		FeedBackOverIntWithThirdCmd(CMD1_SEARCH, RESTYPE_GIS_COVER_REGION_ADD, -1);
	}

	//处理结束
	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
	return true;
}

/************************************************************************
ConfigMngSock_CoverRegionDeleteConfig
************************************************************************/
BOOL  
ConfigMngSock_CoverRegionDeleteConfig::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	
    int id;
	PPM_OS::memcpy(&id,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);	
	id = MAKEINT_NETSEQ1(id);
	
	char tm_sql[1024];	
	PPM_OS::memset(tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec sp_config_cover_region_delete %d",
		id
		);
	if(NULL != m_pDataBaseDeal_)
	{
		if(0 > m_pDataBaseDeal_->ExecSql(tm_sql))
		{
			FeedBackOverIntWithThirdCmd(CMD1_SEARCH, RESTYPE_GIS_COVER_REGION_DELETE, -1);
		}
		else 
		{
			FeedBackOverIntWithThirdCmd(CMD1_SEARCH, RESTYPE_GIS_COVER_REGION_DELETE, 1);
		}
	}	
	
	//处理结束
	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
	return true;
}

/************************************************************************
ConfigMngSock_CoverRegionDeleteConfig
************************************************************************/
BOOL  
ConfigMngSock_CoverRegionEditConfig::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	
	int id;
	PPM_OS::memcpy(&id,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);	
	id = MAKEINT_NETSEQ1(id);
	
    int type;
	PPM_OS::memcpy(&type,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);	
	type = MAKEINT_NETSEQ1(type);
	
	char szName[255];
	PPM_OS::memset(szName,0,sizeof(szName));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,szName,sizeof(szName)));
	
	char szdesc[255];
	PPM_OS::memset(szdesc,0,sizeof(szName));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,szdesc,sizeof(szdesc)));
	
	int status;
	PPM_OS::memcpy(&status,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);	
	status = MAKEINT_NETSEQ1(status);
	
	char tm_sql[1024];	
	PPM_OS::memset(tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec sp_config_cover_region_edit %d,%d,'%s','%s',%d",
		id,
		type,
		szName,
		szdesc,
		status
		);
	
	if(NULL != m_pDataBaseDeal_)
	{
		if(0 > m_pDataBaseDeal_->ExecSql(tm_sql))
		{
			FeedBackOverIntWithThirdCmd(CMD1_SEARCH,RESTYPE_GIS_COVER_REGION_EDIT, -1);
		}
		else 
		{
			FeedBackOverIntWithThirdCmd(CMD1_SEARCH,RESTYPE_GIS_COVER_REGION_EDIT, 1);
		}
	}	
	
	//处理结束
	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
	return true;
}

/************************************************************************
ConfigMngSock_CoverRegionGetConfig
************************************************************************/
BOOL  
ConfigMngSock_CoverRegionGetConfig::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	
	char tm_sql[1024];
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec sp_config_cover_region_get "
		);
	
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_GIS_COVER_REGION_GET;
	if(NULL != m_pDataBaseDeal_)
	{
		m_pDataBaseDeal_->SearchSql(tm_sql,this, m_iRowType_);
	}
	Clear();
	
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec sp_config_cover_point_get "
		);
	
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_GIS_COVER_REGION_POINT;
	if(NULL != m_pDataBaseDeal_)
	{
		m_pDataBaseDeal_->SearchSql(tm_sql,this, m_iRowType_);
	}
	Clear();
	
	//处理结束
	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
	
	return TRUE;
}
