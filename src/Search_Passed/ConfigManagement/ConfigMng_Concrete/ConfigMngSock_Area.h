// ConfigMngSock_Area.h: interface for the CConfigMngSock_Area class.
//
//////////////////////////////////////////////////////////////////////

#ifndef __CONFIGMNGSOCK_AREA_H__
#define __CONFIGMNGSOCK_AREA_H__

#include "./SearchSock.h"


class ConfigMngSock_AreaInfoConfig : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};


class ConfigMngSock_AreaTypeConfig : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};


class ConfigMngSock_AreaScanConfig : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};

class ConfigMngSock_CQTAreaListGisConfig : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};


class ConfigMngSock_AreaStatConfig : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};


class ConfigMngSock_AreaTypeGisConfig : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};


class ConfigMngSock_AreaListGisConfig : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};


class ConfigMngSock_AreaAddConfig : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};


class ConfigMngSock_AreaEditConfig : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};


class ConfigMngSock_AreaDeleteonfig : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};


class ConfigMngSock_CoverRegionAddConfig : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};


class ConfigMngSock_CoverRegionDeleteConfig : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};


class ConfigMngSock_CoverRegionEditConfig : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};


class ConfigMngSock_CoverRegionGetConfig : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};


#endif
