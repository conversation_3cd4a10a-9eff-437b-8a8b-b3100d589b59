// ConfigMngSock_Cell.cpp: implementation of the CConfigMngSock_Cell class.
//
//////////////////////////////////////////////////////////////////////

#include "ConfigMngSock_Cell.h"

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

const char SpConfigCellGet[] = "exec sp_config_cell_get";

/************************************************************************
ConfigMngSock_CellConfig
************************************************************************/
BOOL  
ConfigMngSock_CellConfig::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{

	char tm_sql[512];	
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), SpConfigCellGet);
	
	char tm_sql2[512];

	//bts
	PPM_OS::memset(tm_sql2,0,sizeof(tm_sql2));
	PPM_OS::snprintf(tm_sql2,sizeof(tm_sql2),"%s %d",tm_sql,1);
	m_bResponseType_ = RESTYPE_CONFIG_BTS;
	m_iRowType_ = TYPE_VEC_NORMAL;
  SEARCHSQL(m_iRowType_, tm_sql2);

	//Cell
	PPM_OS::memset(tm_sql2,0,sizeof(tm_sql2));
	PPM_OS::snprintf(tm_sql2,sizeof(tm_sql2),"%s %d",tm_sql,2);
	m_bResponseType_ = RESTYPE_CONFIG_CELL;
	m_iRowType_ = TYPE_VEC_NORMAL;
	SEARCHSQL(m_iRowType_, tm_sql2);

	//Power Divider
	PPM_OS::memset(tm_sql2,0,sizeof(tm_sql2));
	PPM_OS::snprintf(tm_sql2,sizeof(tm_sql2),"%s %d",tm_sql,3);
	m_bResponseType_ = RESTYPE_CONFIG_PD;
	m_iRowType_ = TYPE_VEC_NORMAL;
  SEARCHSQL(m_iRowType_, tm_sql2);

	//Repeater
	PPM_OS::memset(tm_sql2,0,sizeof(tm_sql2));
	PPM_OS::snprintf(tm_sql2,sizeof(tm_sql2),"%s %d",tm_sql,4);
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_CONFIG_REPEATER;
	SEARCHSQL(m_iRowType_, tm_sql2);

	//tdscdma bts
	PPM_OS::memset(tm_sql2,0,sizeof(tm_sql2));
	PPM_OS::snprintf(tm_sql2,sizeof(tm_sql2),"%s %d",tm_sql,5);
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_CONFIG_TDSCDMA_BTS;
	SEARCHSQL(m_iRowType_, tm_sql2);
	
	//tdscdma Cell
	PPM_OS::memset(tm_sql2,0,sizeof(tm_sql2));
	PPM_OS::snprintf(tm_sql2,sizeof(tm_sql2),"%s %d",tm_sql,6);
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_CONFIG_TDSCDMA_CELL;
  SEARCHSQL(m_iRowType_, tm_sql2);

	//tdscdma pd
	PPM_OS::memset(tm_sql2,0,sizeof(tm_sql2));
	PPM_OS::snprintf(tm_sql2,sizeof(tm_sql2),"%s %d",tm_sql,7);
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_CONFIG_TDSCDMA_PD;
  SEARCHSQL(m_iRowType_, tm_sql2);

	//gsm nbcell
	PPM_OS::memset(tm_sql2,0,sizeof(tm_sql2));
	PPM_OS::snprintf(tm_sql2,sizeof(tm_sql2),"%s %d",tm_sql,8);
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_CONFIG_GSM_NBCELL;
	SEARCHSQL(m_iRowType_, tm_sql2);

	//lte bts
	PPM_OS::memset(tm_sql2,0,sizeof(tm_sql2));
	PPM_OS::snprintf(tm_sql2,sizeof(tm_sql2),"%s %d",tm_sql,9);
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_CONFIG_LTE_BTS;
	SEARCHSQL(m_iRowType_, tm_sql2);

	//lte cell
	PPM_OS::memset(tm_sql2,0,sizeof(tm_sql2));
	PPM_OS::snprintf(tm_sql2,sizeof(tm_sql2),"%s %d",tm_sql,10);
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_CONFIG_LTE_CELL;
	SEARCHSQL(m_iRowType_, tm_sql2);

	//lte pd
	PPM_OS::memset(tm_sql2,0,sizeof(tm_sql2));
	PPM_OS::snprintf(tm_sql2,sizeof(tm_sql2),"%s %d",tm_sql,11);
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_CONFIG_LTE_PD;
	SEARCHSQL(m_iRowType_, tm_sql2);

	//lte nbcell
	PPM_OS::memset(tm_sql2,0,sizeof(tm_sql2));
	PPM_OS::snprintf(tm_sql2,sizeof(tm_sql2),"%s %d",tm_sql,12);
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_CONFIG_LTE_NBCELL;
	SEARCHSQL(m_iRowType_, tm_sql2);

	//NR bts
	PPM_OS::memset(tm_sql2, 0, sizeof(tm_sql2));
	PPM_OS::snprintf(tm_sql2, sizeof(tm_sql2), "%s %d", tm_sql, 13);
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_CONFIG_NR_BTS;
	SEARCHSQL(m_iRowType_, tm_sql2);

	//NR cell
	PPM_OS::memset(tm_sql2, 0, sizeof(tm_sql2));
	PPM_OS::snprintf(tm_sql2, sizeof(tm_sql2), "%s %d", tm_sql, 14);
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_CONFIG_NR_CELL;
	SEARCHSQL(m_iRowType_, tm_sql2);

	//NR pd
	PPM_OS::memset(tm_sql2, 0, sizeof(tm_sql2));
	PPM_OS::snprintf(tm_sql2, sizeof(tm_sql2), "%s %d", tm_sql, 15);
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_CONFIG_NR_PD;
	SEARCHSQL(m_iRowType_, tm_sql2);

	//NR nbcell
	PPM_OS::memset(tm_sql2, 0, sizeof(tm_sql2));
	PPM_OS::snprintf(tm_sql2, sizeof(tm_sql2), "%s %d", tm_sql, 16);
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_CONFIG_NR_NBCELL;
	SEARCHSQL(m_iRowType_, tm_sql2);

	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
	return TRUE;	
}

BOOL
ConfigMngSock_CellConfig_CDMA::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
    char tm_sql[1024];

	//cdma bts
	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
        "select iid,istime,ietime,strbtsname,strbtscode,iSID,iNID,iBID,ilongitude,ilatitude,itype,strcomment from tb_cdma_cfg_bts");
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_CONFIG_CDMA_BTS;

	if(NULL != m_pDataBaseDeal_){
		m_pDataBaseDeal_->SearchSql(tm_sql,this, m_iRowType_);
	}
	Clear();

	//cdma cell
	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
        "SELECT iid,istime,ietime,strcellname,strcellcode,ibtsid,iuarfcn,iPN,strPNs,uarfcnlist,strcomment from tb_cdma_cfg_cell ");
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_CONFIG_CDMA_CELL;

	if(NULL != m_pDataBaseDeal_){
		m_pDataBaseDeal_->SearchSql(tm_sql,this, m_iRowType_);
	}
	Clear();

	//cdma pd
	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
        "SELECT iid,istime,ietime,icellid,ilongitude,ilatitude,iazimuth,fdowntilt,fheight,strcomment from tb_cdma_cfg_pd ");
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_CONFIG_CDMA_PD;

	if(NULL != m_pDataBaseDeal_){
		m_pDataBaseDeal_->SearchSql(tm_sql,this, m_iRowType_);
	}
	Clear();

	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
	return TRUE;	
}


/************************************************************************
ConfigMngSock_CellNBConfig
************************************************************************/
BOOL  
ConfigMngSock_CellNBConfig::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	MOVENEXTTEST(ioffset,4,nCount);
	
	int icellid = 0;
	PPM_OS::memcpy(&icellid,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	icellid = MAKEINT_NETSEQ1(icellid);

	char tm_sql[512];	
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec sp_config_nbcell_get %d",
		icellid
		);

	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_CONFIG_NBCELL;
	
	if(NULL != m_pDataBaseDeal_){
		m_pDataBaseDeal_->SearchSql(tm_sql,this, m_iRowType_);
		//处理结束
		FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
	}
	Clear();
	
	return TRUE;	
}

/************************************************************************
ConfigMngSock_CellNBTDSCDMA
************************************************************************/
BOOL  
ConfigMngSock_CellNBTDSCDMAConfig::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
  int iid;
	PPM_OS::memcpy(&iid,pData + ioffset,4);
	iid = MAKEINT_NETSEQ1(iid);
	MOVENEXT(ioffset,4,nCount);
	
	char tm_sql[2300];
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec sp_config_tdscdma_nbcell_get %d " ,
		iid);
	
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RSQTYPE_CONFIG_TDSCDMA_NBCELL;
	if(NULL != m_pDataBaseDeal_)
	{
		m_pDataBaseDeal_->SearchSql(tm_sql,this, m_iRowType_);
	}
	Clear();
	
	//处理结束
	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);	
	return TRUE;	
}


/************************************************************************
ConfigMngSock_CellWCDMAConfig
************************************************************************/
BOOL  
ConfigMngSock_CellWCDMAConfig::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	char tm_sql[255];	
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec sp_config_wcdma_cell_get");
	
	char tm_sql2[512];
	
	//bts
	PPM_OS::memset(tm_sql2,0,sizeof(tm_sql2));
	PPM_OS::snprintf(tm_sql2,sizeof(tm_sql2),"%s %d",tm_sql,1);
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTPYE_CONFIG_WCDMA_NODEB;
	
	if(NULL != m_pDataBaseDeal_){
		m_pDataBaseDeal_->SearchSql(tm_sql2,this, m_iRowType_);
	}
	Clear();
	
	//Cell
	PPM_OS::memset(tm_sql2,0,sizeof(tm_sql2));
	PPM_OS::snprintf(tm_sql2,sizeof(tm_sql2),"%s %d",tm_sql,2);
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_CONFIG_WCDMA_CELL;
	
	if(NULL != m_pDataBaseDeal_){
		m_pDataBaseDeal_->SearchSql(tm_sql2,this, m_iRowType_);
	}
	Clear();
	
	//Power Divider
	PPM_OS::memset(tm_sql2,0,sizeof(tm_sql2));
	PPM_OS::snprintf(tm_sql2,sizeof(tm_sql2),"%s %d",tm_sql,3);
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_CONFIG_WCDMA_ANTENNA;
	
	if(NULL != m_pDataBaseDeal_){
		m_pDataBaseDeal_->SearchSql(tm_sql2,this, m_iRowType_);
	}
	Clear();
	
	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
	return TRUE;	
}


/************************************************************************
ConfigMngSock_CellNBWCDMAConfig
************************************************************************/
BOOL  
ConfigMngSock_CellNBWCDMAConfig::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
   	int iid;
	PPM_OS::memcpy(&iid,pData + ioffset,4);
	iid = MAKEINT_NETSEQ1(iid);
	MOVENEXT(ioffset,4,nCount);
	
	char tm_sql[2300];
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec sp_config_wcdma_nbcell_get %d " ,
		iid);
	
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_CONFIG_WCDMA_NBCELL;
	
	if(NULL != m_pDataBaseDeal_)
	{
		m_pDataBaseDeal_->SearchSql(tm_sql,this, m_iRowType_);
	}
	Clear();
	
	//处理结束
	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);	
	return TRUE;	
}

/************************************************************************
ConfigMngSock_CellCULTEConfig
************************************************************************/
BOOL  
ConfigMngSock_CellCULTEConfig::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	char tm_sql[512];	
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec sp_config_culte_get");

	char tm_sql2[512];

	//culte bts
	PPM_OS::memset(tm_sql2,0,sizeof(tm_sql2));
	PPM_OS::snprintf(tm_sql2,sizeof(tm_sql2),"%s %d",tm_sql,1);
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_CONFIG_LTE_BTS;
	SEARCHSQL(m_iRowType_, tm_sql2);

	//culte pd
	PPM_OS::memset(tm_sql2,0,sizeof(tm_sql2));
	PPM_OS::snprintf(tm_sql2,sizeof(tm_sql2),"%s %d",tm_sql,3);
	m_bResponseType_ = RESTYPE_CONFIG_LTE_PD;
	m_iRowType_ = TYPE_VEC_NORMAL;
	SEARCHSQL(m_iRowType_, tm_sql2);

	//culte cell
	PPM_OS::memset(tm_sql2,0,sizeof(tm_sql2));
	PPM_OS::snprintf(tm_sql2,sizeof(tm_sql2),"%s %d",tm_sql,2);
	m_bResponseType_ = RESTYPE_CONFIG_LTE_CELL;
	m_iRowType_ = TYPE_VEC_NORMAL;
	SEARCHSQL(m_iRowType_, tm_sql2);

	//culte nbcell
	PPM_OS::memset(tm_sql2,0,sizeof(tm_sql2));
	PPM_OS::snprintf(tm_sql2,sizeof(tm_sql2),"%s %d",tm_sql,4);
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_CONFIG_LTE_NBCELL;
  SEARCHSQL(m_iRowType_, tm_sql2);

	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
	return TRUE;	
}

/************************************************************************
ConfigMngSock_CellCTLTEConfig
************************************************************************/
BOOL  
ConfigMngSock_CellCTLTEConfig::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	char tm_sql[512];	
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec sp_config_ctlte_get");

	char tm_sql2[512];

	//ctlte bts
	PPM_OS::memset(tm_sql2,0,sizeof(tm_sql2));
	PPM_OS::snprintf(tm_sql2,sizeof(tm_sql2),"%s %d",tm_sql,1);
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_CONFIG_LTE_BTS;

	if(NULL != m_pDataBaseDeal_){
		m_pDataBaseDeal_->SearchSql(tm_sql2,this, m_iRowType_);
	}
	Clear();

	//ctlte cell
	PPM_OS::memset(tm_sql2,0,sizeof(tm_sql2));
	PPM_OS::snprintf(tm_sql2,sizeof(tm_sql2),"%s %d",tm_sql,2);
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_CONFIG_LTE_CELL;

	if(NULL != m_pDataBaseDeal_){
		m_pDataBaseDeal_->SearchSql(tm_sql2,this, m_iRowType_);
	}
	Clear();

	//ctlte pd
	PPM_OS::memset(tm_sql2,0,sizeof(tm_sql2));
	PPM_OS::snprintf(tm_sql2,sizeof(tm_sql2),"%s %d",tm_sql,3);
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_CONFIG_LTE_PD;

	if(NULL != m_pDataBaseDeal_){
		m_pDataBaseDeal_->SearchSql(tm_sql2,this, m_iRowType_);
	}
	Clear();

	//ctlte nbcell
	PPM_OS::memset(tm_sql2,0,sizeof(tm_sql2));
	PPM_OS::snprintf(tm_sql2,sizeof(tm_sql2),"%s %d",tm_sql,4);
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_CONFIG_LTE_NBCELL;

	if(NULL != m_pDataBaseDeal_){
		m_pDataBaseDeal_->SearchSql(tm_sql2,this, m_iRowType_);
	}
	Clear();


	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
	return TRUE;	
}


/************************************************************************
ConfigMngSock_CellTimeStamp
************************************************************************/
BOOL  
ConfigMngSock_CellTimeStamp::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	char tm_sql[1024];
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	#ifdef USE_MYSQL
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), "call sp_config_cell_timestamp();");
	#else
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), "exec sp_config_cell_timestamp ");
	#endif

	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_CONFIG_CELL_TIMESTAMP;
	
	if(NULL != m_pDataBaseDeal_)
	{
		m_pDataBaseDeal_->SearchSql(tm_sql,this, m_iRowType_);
	}
	Clear();

	//处理结束
	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);

	return TRUE;
}

/************************************************************************
ConfigMngSock_CellConfig_Test
************************************************************************/
BOOL  
ConfigMngSock_CellConfig_Test::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{

	char tm_sql[512];	
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), SpConfigCellGet);
	
	char tm_sql2[512];

	//bts
	PPM_OS::memset(tm_sql2,0,sizeof(tm_sql2));
	PPM_OS::snprintf(tm_sql2,sizeof(tm_sql2),
        "select top 1000 iid,istime,ietime,strbtsname,strmscname,strbscname,ilongitude,ilatitude,strtype,\
case strbandtype when '900' then 1 else 2 end,strcomment from tb_cfg_bts order by istime,iid");
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_CONFIG_BTS;
	
	if(NULL != m_pDataBaseDeal_){
		m_pDataBaseDeal_->SearchSql(tm_sql2,this, m_iRowType_);
	}
	Clear();

	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
	return TRUE;	
}

/************************************************************************
ConfigMngSock_Server_List
************************************************************************/
BOOL
ConfigMngSock_Server_List::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	char tm_sql[512];
	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));

	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),"select * from tb_cfg_static_server_list");

	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_CONFIG_SERVER_LIST;

	if(NULL != m_pDataBaseDeal_){
		m_pDataBaseDeal_->SearchSql(tm_sql,this, m_iRowType_);
	}

	Clear();

	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);

	return TRUE;
}
