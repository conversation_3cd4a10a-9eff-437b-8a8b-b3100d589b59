// ConfigMngSock_Cell.h: interface for the CConfigMngSock_Cell class.
//
//////////////////////////////////////////////////////////////////////

#ifndef __CONFIGMNGSOCK_CELL_H__
#define __CONFIGMNGSOCK_CELL_H__

#include "./SearchSock.h"


class ConfigMngSock_CellConfig : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};

class ConfigMngSock_CellConfig_CDMA : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};


class ConfigMngSock_CellNBConfig : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};


class ConfigMngSock_CellNBTDSCDMAConfig : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};


class ConfigMngSock_CellWCDMAConfig : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};


class ConfigMngSock_CellNBWCDMAConfig : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};

class ConfigMngSock_CellCULTEConfig : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};

class ConfigMngSock_CellCTLTEConfig : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};

class ConfigMngSock_CellTimeStamp : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};

class ConfigMngSock_CellConfig_Test : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};

class ConfigMngSock_Server_List : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};
#endif
