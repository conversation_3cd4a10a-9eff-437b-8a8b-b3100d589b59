// ConfigMngSock_Other.cpp: implementation of the CConfigMngSock_Other class.
//
//////////////////////////////////////////////////////////////////////

#include "ConfigMngSock_Other.h"
#include "./ColumnDef.h"
#include "./DtSockServer.h"

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

/************************************************************************
ConfigMngSock_VersionConfig
************************************************************************/
BOOL  
ConfigMngSock_VersionConfig::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	CConfigSetting* tm_setting = CConfigSetting::Instance();
	BOOL res = tm_setting->ReadVersion();

	if(FALSE == res){
		return FALSE;
	}
	if(NULL != m_pServerDeal_){
		PPM_OS::memset(m_pSendBuf_,0,SEND_BUF_MAXSIZE);
		
		int tm_ioffset = 0;
		//构造信息发送
		m_pSendBuf_[tm_ioffset++] = CMD1_CONFIG_MNG;//命令字
		m_pSendBuf_[tm_ioffset++] = CMD2_RESPONSE;//子命令字
		m_pSendBuf_[tm_ioffset++] = RESTYPE_CONFIG_VERSION;//响应类型

		CSearch_PI::AddIntToBuf(m_pSendBuf_,tm_ioffset,CConfigSetting::Instance()->m_iVersion_, SEND_BUF_MAXSIZE);
		
		if(0 > m_pServerDeal_->SendData(m_pSendBuf_,tm_ioffset)){
			return -1;
		}
	}

	return TRUE;
	
}



/************************************************************************
ConfigMngSock_StaticInfoConfig
************************************************************************/
BOOL  
ConfigMngSock_StaticInfoConfig::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	char tm_sql[512];	
	#ifdef USE_MYSQL
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),	"call sp_config_static_get");
	#else
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),	"exec sp_config_static_get");
	#endif
	
	char tm_sql2[512];

	//project
	PPM_OS::memset(tm_sql2,0,sizeof(tm_sql2));
	#ifdef USE_MYSQL
	PPM_OS::snprintf(tm_sql2,sizeof(tm_sql2),"%s (%d);",tm_sql,1);
	#else
	PPM_OS::snprintf(tm_sql2,sizeof(tm_sql2),"%s %d",tm_sql,1);
	#endif
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_CONFIG_STATIC_PROJECT;
	SEARCHSQL(m_iRowType_, tm_sql2);
	
	//test
	PPM_OS::memset(tm_sql2,0,sizeof(tm_sql2));
	#ifdef USE_MYSQL
	PPM_OS::snprintf(tm_sql2,sizeof(tm_sql2),"%s (%d);",tm_sql,2);
	#else
	PPM_OS::snprintf(tm_sql2,sizeof(tm_sql2),"%s %d",tm_sql,2);
	#endif
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_CONFIG_STATIC_TEST;
	SEARCHSQL(m_iRowType_, tm_sql2);
	
	//device
	PPM_OS::memset(tm_sql2,0,sizeof(tm_sql2));
	#ifdef USE_MYSQL
	PPM_OS::snprintf(tm_sql2,sizeof(tm_sql2),"%s (%d);",tm_sql,3);
	#else
	PPM_OS::snprintf(tm_sql2,sizeof(tm_sql2),"%s %d",tm_sql,3);
	#endif
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_CONFIG_STATIC_DEVICE;
	SEARCHSQL(m_iRowType_, tm_sql2);
			
	//file
	PPM_OS::memset(tm_sql2,0,sizeof(tm_sql2));
	#ifdef USE_MYSQL
	PPM_OS::snprintf(tm_sql2,sizeof(tm_sql2),"%s (%d);",tm_sql,4);
	#else
	PPM_OS::snprintf(tm_sql2,sizeof(tm_sql2),"%s %d",tm_sql,4);
	#endif
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_CONFIG_STATIC_FILE;
	SEARCHSQL(m_iRowType_, tm_sql2);
	
	//service
	PPM_OS::memset(tm_sql2,0,sizeof(tm_sql2));
	#ifdef USE_MYSQL
	PPM_OS::snprintf(tm_sql2,sizeof(tm_sql2),"%s (%d);",tm_sql,5);
	#else
	PPM_OS::snprintf(tm_sql2,sizeof(tm_sql2),"%s %d",tm_sql,5);
	#endif
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_CONFIG_STATIC_SERVICE;
	SEARCHSQL(m_iRowType_, tm_sql2);
	
	//carrier
	PPM_OS::memset(tm_sql2,0,sizeof(tm_sql2));
	#ifdef USE_MYSQL
	PPM_OS::snprintf(tm_sql2,sizeof(tm_sql2),"%s (%d);",tm_sql,6);
	#else
	PPM_OS::snprintf(tm_sql2,sizeof(tm_sql2),"%s %d",tm_sql,6);
	#endif
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_CONFIG_STATIC_CARRIER;
	SEARCHSQL(m_iRowType_, tm_sql2);

	//agent
	PPM_OS::memset(tm_sql2,0,sizeof(tm_sql2));
	#ifdef USE_MYSQL
	PPM_OS::snprintf(tm_sql2,sizeof(tm_sql2),"%s (%d);",tm_sql,7);
	#else
	PPM_OS::snprintf(tm_sql2,sizeof(tm_sql2),"%s %d",tm_sql,7);
	#endif
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_CONFIG_STATIC_AGENT;
	SEARCHSQL(m_iRowType_, tm_sql2);

	//staff
	PPM_OS::memset(tm_sql2,0,sizeof(tm_sql2));
	#ifdef USE_MYSQL
	PPM_OS::snprintf(tm_sql2,sizeof(tm_sql2),"%s (%d);",tm_sql,8);
	#else
	PPM_OS::snprintf(tm_sql2,sizeof(tm_sql2),"%s %d",tm_sql,8);
	#endif
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_CONFIG_STATIC_STAFF;
	SEARCHSQL(m_iRowType_, tm_sql2);

	//areatype
	PPM_OS::memset(tm_sql2,0,sizeof(tm_sql2));
	#ifdef USE_MYSQL
	PPM_OS::snprintf(tm_sql2,sizeof(tm_sql2),"%s (%d);",tm_sql,9);
	#else
	PPM_OS::snprintf(tm_sql2,sizeof(tm_sql2),"%s %d",tm_sql,9);
	#endif
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_CONFIG_STATIC_AREAHEAD;
	SEARCHSQL(m_iRowType_, tm_sql2);
	
	//areatype
	PPM_OS::memset(tm_sql2,0,sizeof(tm_sql2));
	#ifdef USE_MYSQL
	PPM_OS::snprintf(tm_sql2,sizeof(tm_sql2),"%s (%d);",tm_sql,10);
	#else
	PPM_OS::snprintf(tm_sql2,sizeof(tm_sql2),"%s %d",tm_sql,10);
	#endif
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_CONFIG_STATIC_AREADETAIL;
	SEARCHSQL(m_iRowType_, tm_sql2);
	
	//处理结束
	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);

	return TRUE;		
}


/************************************************************************
ConfigMngSock_DistrictCommunityStatConfig
************************************************************************/
BOOL  
ConfigMngSock_DistrictCommunityStatConfig::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{

	char tm_sql[512];	
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec sp_config_stat_district_community_get");
	
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_CONFIG_STAT_DISTRICT_COMMUNITY;
	
	if(NULL != m_pDataBaseDeal_){
		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
		//处理结束
		FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
	}
	Clear();
	
	return TRUE;		
}


/************************************************************************
ConfigMngSock_VillageInfoConfig
村查询条件
************************************************************************/
BOOL  
ConfigMngSock_VillageInfoConfig::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	//RESTYPE_RESULT_VILLAGEINFO

	char tm_sql[8000];
    PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), "exec sp_config_village_admin");
	
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_VILLAGE_Admin;

	if(NULL != m_pDataBaseDeal_)
	{
		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
	}
	Clear();

  PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), "exec sp_config_village_nature");
	
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_VILLAGE_Nature;
	
	if(NULL != m_pDataBaseDeal_)
	{
		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
	}
	Clear();

	//处理结束
	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
	return TRUE;
}


/************************************************************************
ConfigMngSock_CityConfig
************************************************************************/
BOOL  
ConfigMngSock_CityConfig::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{	
	char tm_sql[512];	
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec sp_config_city_get "
        );
	
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_CONFIG_STATIC_CITY;
	
	if(NULL != m_pDataBaseDeal_)
	{
		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
	}
	Clear();
				
	//处理结束
	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
	return TRUE;	
}


/************************************************************************
ConfigMngSock_RDLogInfoAdd
************************************************************************/
BOOL  
ConfigMngSock_RDLogInfoAdd::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	char username[50];
	PPM_OS::memset(username,0,sizeof(username));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,username,sizeof(username)));
	std::string unix_username = CUtility::GBKToUtf8(username);
	
	char user_ip[50];
	PPM_OS::memset(user_ip,0,sizeof(user_ip));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,user_ip,sizeof(user_ip)));
	std::string unix_user_ip = CUtility::GBKToUtf8(user_ip);
	
	int event_type_id;
	PPM_OS::memcpy(&event_type_id,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	event_type_id = MAKEINT_NETSEQ1(event_type_id);
	
	int func_id;
	PPM_OS::memcpy(&func_id,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	func_id = MAKEINT_NETSEQ1(func_id);
	
	int subfunc_id;
	PPM_OS::memcpy(&subfunc_id,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);	
	subfunc_id = MAKEINT_NETSEQ1(subfunc_id);
	
	char event_desc[255];
	PPM_OS::memset(event_desc,0,sizeof(event_desc));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,event_desc,sizeof(event_desc)));
	std::string unix_event_desc = CUtility::GBKToUtf8(event_desc);

	int cityid;
	PPM_OS::memcpy(&cityid,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);	
	cityid = MAKEINT_NETSEQ1(cityid);
	
	char tm_sql[8000];	
	#ifdef USE_MYSQL
		PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		" call rd_sp_log_sysop_insertbyname ('%s','%s',%d,%d,%d, '%s', %d); ",
		unix_username.c_str(),
		unix_user_ip.c_str(),
		event_type_id,
		func_id,
		subfunc_id,
		unix_event_desc.c_str(), 
		cityid);
	#else
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		" use %s exec rd_sp_log_sysop_insertbyname '%s','%s',%d,%d,%d, '%s', %d ",
		CConfigSetting::Instance()->m_stru_MainDBSetting_.pchDBName,
		unix_username.c_str(),
		unix_user_ip.c_str(),
		event_type_id,
		func_id,
		subfunc_id,
		unix_event_desc.c_str(), 
		cityid);
	#endif
	if(NULL != m_pDataBaseDeal_ /** && m_pDataBaseDeal_->ResetDBConnInfo()*/)
	{
		m_pDataBaseDeal_->ExecSql(tm_sql);
	}
	
	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
	return TRUE;		
}

/************************************************************************
ConfigMngSock_ProjExtendConfig
************************************************************************/
BOOL  
ConfigMngSock_ProjExtendConfig::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	char tm_sql[1024];
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	#ifdef USE_MYSQL
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), "call sp_config_project_extend();");
	#else
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), "exec sp_config_project_extend ");
	#endif

	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_CONFIG_PROJECT_EXTEND;
	
	SEARCHSQL(m_iRowType_, tm_sql);

	//处理结束
	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);

	return TRUE;
}

/************************************************************************
ConfigMngSock_ImageConfig
************************************************************************/
BOOL  
ConfigMngSock_ImageConfig::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	if (!CColumnDef::Instance()->IsColumnDefCanUsed())
    {
		return FALSE;
    }

    std::map<STRU_COLUMNID, std::map<std::string, CColumnItem*>>& MapClomnsStatItem = CColumnDef::Instance()->GetClomnsStatItem();

	for (auto p_mapCColumnItem = MapClomnsStatItem.begin(); 
		p_mapCColumnItem != MapClomnsStatItem.end(); ++p_mapCColumnItem)
	{
		for (auto p_CColumnItem = MapClomnsStatItem[p_mapCColumnItem->first].begin();
			p_CColumnItem != MapClomnsStatItem[p_mapCColumnItem->first].end(); ++p_CColumnItem)
        {
			if (p_CColumnItem->second->columnType != CColumnItem::StatImageType)
			{
				continue;
			}

			PPM_OS::memset(m_pSendBuf_,0,SEND_BUF_MAXSIZE);

			//构造信息发送
			int tm_ioffset = 0;
			
			m_pSendBuf_[tm_ioffset++] = CMD1_CONFIG_MNG;//命令字
			m_pSendBuf_[tm_ioffset++] = CMD2_RESPONSE;//子命令字
			m_pSendBuf_[tm_ioffset++] = REQTYPE_CONFIG_IMAGECOLUMN;//响应类型
			//
			CSearch_PI::AddIntToBuf(m_pSendBuf_, tm_ioffset, p_CColumnItem->second->imageID, SEND_BUF_MAXSIZE);
			CSearch_PI::AddIntToBuf(m_pSendBuf_, tm_ioffset, p_CColumnItem->second->paraID, SEND_BUF_MAXSIZE);
			CSearch_PI::AddIntToBuf(m_pSendBuf_, tm_ioffset, p_CColumnItem->second->tbID, SEND_BUF_MAXSIZE);
			CSearch_PI::AddStringToBuf(m_pSendBuf_, tm_ioffset, p_CColumnItem->second->servicetype, SEND_BUF_MAXSIZE);
			CSearch_PI::AddStringToBuf(m_pSendBuf_, tm_ioffset, p_CColumnItem->second->tbName, SEND_BUF_MAXSIZE);
			CSearch_PI::AddStringToBuf(m_pSendBuf_, tm_ioffset, p_CColumnItem->second->imageDataType, SEND_BUF_MAXSIZE);
			CSearch_PI::AddStringToBuf(m_pSendBuf_, tm_ioffset, p_CColumnItem->second->dbColumnName, SEND_BUF_MAXSIZE);
			CSearch_PI::AddStringToBuf(m_pSendBuf_, tm_ioffset, p_CColumnItem->second->imageDataTypeID, SEND_BUF_MAXSIZE);
			CSearch_PI::AddStringToBuf(m_pSendBuf_, tm_ioffset, p_CColumnItem->second->imageDataColumnID, SEND_BUF_MAXSIZE);
			CSearch_PI::AddStringToBuf(m_pSendBuf_, tm_ioffset, p_CColumnItem->second->clientColumnName, SEND_BUF_MAXSIZE);
			CSearch_PI::AddIntToBuf(m_pSendBuf_, tm_ioffset, p_CColumnItem->second->imageDataColumnDealType, SEND_BUF_MAXSIZE);
      CSearch_PI::AddStringToBuf(m_pSendBuf_, tm_ioffset, p_CColumnItem->second->imageDataColumnDealDisc, SEND_BUF_MAXSIZE);

			if(0 > m_pServerDeal_->SendData(m_pSendBuf_,tm_ioffset)){
				return -1;
			}
        }

	}

	//处理结束
	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
	return TRUE;
}

/************************************************************************
ConfigMngSock_ImageConfig
************************************************************************/
BOOL  
ConfigMngSock_ColumnConfig::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	if (!CColumnDef::Instance()->IsColumnDefCanUsed())
	{
		return FALSE;
	}

    std::map<STRU_COLUMNID, std::map<std::string, CColumnItem*>> MapClomnsStatItem = CColumnDef::Instance()->GetClomnsStatItem();

	for (auto p_mapCColumnItem =MapClomnsStatItem.begin(); 
		p_mapCColumnItem != MapClomnsStatItem.end(); ++p_mapCColumnItem)
	{
		for (auto p_CColumnItem = MapClomnsStatItem[p_mapCColumnItem->first].begin();
			p_CColumnItem != MapClomnsStatItem[p_mapCColumnItem->first].end(); ++p_CColumnItem)
		{
			if (p_CColumnItem->second->columnType != CColumnItem::TableColumnType)
			{
				continue;
			}

			PPM_OS::memset(m_pSendBuf_,0,SEND_BUF_MAXSIZE);

			//构造信息发送
			int tm_ioffset = 0;
			
			m_pSendBuf_[tm_ioffset++] = CMD1_CONFIG_MNG;//命令字
			m_pSendBuf_[tm_ioffset++] = CMD2_RESPONSE;//子命令字
			m_pSendBuf_[tm_ioffset++] = REQTYPE_CONFIG_TABLECOLUMN;//响应类型
			//
			CSearch_PI::AddIntToBuf(m_pSendBuf_, tm_ioffset, p_CColumnItem->second->imageID, SEND_BUF_MAXSIZE);
			CSearch_PI::AddIntToBuf(m_pSendBuf_, tm_ioffset, p_CColumnItem->second->paraID, SEND_BUF_MAXSIZE);
			CSearch_PI::AddIntToBuf(m_pSendBuf_, tm_ioffset, p_CColumnItem->second->tbID, SEND_BUF_MAXSIZE);
			CSearch_PI::AddStringToBuf(m_pSendBuf_, tm_ioffset, p_CColumnItem->second->tbName, SEND_BUF_MAXSIZE);
			CSearch_PI::AddIntToBuf(m_pSendBuf_, tm_ioffset, p_CColumnItem->second->maxGroup, SEND_BUF_MAXSIZE);
			CSearch_PI::AddIntToBuf(m_pSendBuf_, tm_ioffset, p_CColumnItem->second->dataType, SEND_BUF_MAXSIZE);
			CSearch_PI::AddStringToBuf(m_pSendBuf_, tm_ioffset, p_CColumnItem->second->abnormalData, SEND_BUF_MAXSIZE);
			CSearch_PI::AddStringToBuf(m_pSendBuf_, tm_ioffset, p_CColumnItem->second->dbColumnName, SEND_BUF_MAXSIZE);
			CSearch_PI::AddIntToBuf(m_pSendBuf_, tm_ioffset, p_CColumnItem->second->FixImage, SEND_BUF_MAXSIZE);
			CSearch_PI::AddIntToBuf(m_pSendBuf_, tm_ioffset, p_CColumnItem->second->subStart, SEND_BUF_MAXSIZE);
			CSearch_PI::AddIntToBuf(m_pSendBuf_, tm_ioffset, p_CColumnItem->second->subEnd, SEND_BUF_MAXSIZE);
			CSearch_PI::AddStringToBuf(m_pSendBuf_, tm_ioffset, p_CColumnItem->second->clientColumnName, SEND_BUF_MAXSIZE);

			if(0 > m_pServerDeal_->SendData(m_pSendBuf_,tm_ioffset)){
				return -1;
			}
		}
	}

	//处理结束
	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
	return TRUE;
}


BOOL  
ConfigMngConfigKpiTable::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{	
	char tm_sql[1024];
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
        " use %s SELECT iidx,strcolname,strcoldesc,icoltype,strtablename,strrptname from tb_popkpi_entry order by strtablename,iidx ", 
		CConfigSetting::Instance()->m_stru_MainDBSetting_.pchDBName);

	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_CONFIG_KPI_TABLE;
	SEARCHSQL(m_iRowType_, tm_sql);

	//处理结束
	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);

	return TRUE;
}


BOOL  
ConfigMngConfigAreaTypeSet::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	int ifileid;
	PPM_OS::memcpy(&ifileid,pData + ioffset,4);
	ifileid = MAKEINT_NETSEQ1(ifileid);
	MOVENEXT(ioffset,4,nCount);

	char tblogfile[50];
	PPM_OS::memset(&tblogfile, 0, sizeof(tblogfile));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tblogfile,sizeof(tblogfile)));

	int areatype;
	PPM_OS::memcpy(&areatype,pData + ioffset,4);
	areatype = MAKEINT_NETSEQ1(areatype);
	MOVENEXT(ioffset,4,nCount);

	int areaid;
	PPM_OS::memcpy(&areaid,pData + ioffset,4);
	areaid = MAKEINT_NETSEQ1(areaid);
	MOVENEXT(ioffset,4,nCount);

	char tm_sql[512];	
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"update %s set iareatype = %d, iareaid = %d where ifileid = %d ",
		&tblogfile,
		areatype,
		areaid, 
		ifileid
		);

	if(NULL != m_pDataBaseDeal_)
	{
		if(0 > m_pDataBaseDeal_->ExecSql(tm_sql))
		{
			FeedBackOverIntWithThirdCmd(CMD1_SEARCH,-1);
		}
		else
		{
			FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
		}
	}

	return TRUE;
}


BOOL  
ConfigMngSock_Image_Insert::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	int type_;
	PPM_OS::memcpy(&type_,pData + ioffset,4);
	type_ = MAKEINT_NETSEQ1(type_);
	MOVENEXT(ioffset,4,nCount);

	int iareatypeid;
	PPM_OS::memcpy(&iareatypeid,pData + ioffset,4);
	iareatypeid = MAKEINT_NETSEQ1(iareatypeid);
	MOVENEXT(ioffset,4,nCount);

	int iareaid;
	PPM_OS::memcpy(&iareaid,pData + ioffset,4);
	iareaid = MAKEINT_NETSEQ1(iareaid);
	MOVENEXT(ioffset,4,nCount);

	char strPicName[50];
	PPM_OS::memset(&strPicName, 0, sizeof(strPicName));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,strPicName,sizeof(strPicName)));

	int rate;
	PPM_OS::memcpy(&rate,pData + ioffset,4);
	rate = MAKEINT_NETSEQ1(rate);
	MOVENEXT(ioffset,4,nCount);

	char strDesc[50];
	PPM_OS::memset(&strDesc, 0, sizeof(strDesc));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,strDesc,sizeof(strDesc)));

	char strsql[8100 * 2 * 8 + 1024];
	PPM_OS::memset(&strsql, 0, sizeof(strsql));

	//接收数据
	//包号
	int packNum;
	PPM_OS::memcpy(&packNum,pData + ioffset,4);
	packNum = MAKEINT_NETSEQ1(packNum);
	MOVENEXT(ioffset,4,nCount);

	if(packNum == -1)
	{
		PPM_OS::snprintf(strsql, sizeof(strsql),"exec mc_sp_image_insert %d,%d,%d,'%s',%s,%d,'%s'",
			type_,
			iareatypeid,
			iareaid,
			&strPicName,
			"null",
			rate,
			&strDesc);
	}
	else
	{
		//文件体长度
		int receiveBufferLenth;
		PPM_OS::memcpy(&receiveBufferLenth,pData + ioffset,4);
		receiveBufferLenth = MAKEINT_NETSEQ1(receiveBufferLenth);
		MOVENEXT(ioffset,4,nCount)

		if (receiveBufferLenth > SEND_BUF_MAXSIZE * 4 - 8)
		{
			//处理结束
			FeedBackOver(CMD1_CONFIG_MNG,RESTYPE_SEARCHEND);
			return TRUE;
		}

		BYTE byteBuffer[8100*8];
		PPM_OS::memset(&byteBuffer, 0, 8100 * 8);
		PPM_OS::memcpy(byteBuffer, pData+ioffset, receiveBufferLenth);

		char pImage[8100 * 2 * 8];
		PPM_OS::memset(pImage, 0, 8100 * 2 * 8);

		PPM_OS::snprintf(pImage,sizeof(pImage),"0X");
		char *p = pImage;
		for (int i=0; i<receiveBufferLenth; i++)
		{
			p = p+2;
			PPM_OS::snprintf(p,2,"%02X",byteBuffer[i]);
		}

		PPM_OS::snprintf(strsql, sizeof(strsql),"exec mc_sp_image_insert %d,%d,%d,'%s',%s,%d,'%s'",
			type_,
			iareatypeid,
			iareaid,
			&strPicName,
			&pImage,
			rate,
			&strDesc);
	}

	if(NULL != m_pDataBaseDeal_)
	{
		if(0 > m_pDataBaseDeal_->ExecSql(strsql))
		{
			return FALSE;
		}
		return TRUE;
	}

	return FALSE;
}

BOOL  
ConfigMngSock_Image_Send::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	// PPM_OS::memcpy(&tImageSend.iareatypeid, pData + ioffset, 4);
	// tImageSend.iareatypeid = MAKEINT_NETSEQ1(tImageSend.iareatypeid);
	// MOVENEXT(ioffset, 4, nCount);

	// PPM_OS::memcpy(&tImageSend.iareaid, pData + ioffset, 4);
	// tImageSend.iareaid = MAKEINT_NETSEQ1(tImageSend.iareaid);
	// MOVENEXT(ioffset, 4, nCount);

	// char tm_sql[1024];
	// PPM_OS::memset(&tm_sql, 0, 1024);
	
	// PPM_OS::snprintf(tm_sql, sizeof(tm_sql), 
  //       "select strPicName,rate,strDesc,DATALENGTH(picture),picture from tb_cfg_static_cqt_picture where iareatypeid=%d and iareaid=%d",
	// 	tImageSend.iareatypeid,
	// 	tImageSend.iareaid);

	// if (NULL == m_pDataBaseDeal_)
	// {
	// 	return FALSE;
	// }

	// PPM_OS::memset(tImageSend.strPicName,0,sizeof(tImageSend.strPicName));
	// PPM_OS::memset(tImageSend.strDesc,0,sizeof(tImageSend.strDesc));
	// PPM_OS::memset(tImageSend.picture,0,sizeof(tImageSend.picture));

	// if (!m_pDataBaseDeal_->m_DBConn_.connect(m_pDataBaseDeal_->m_stru_DBConnInfo_.pchUserName, 
	// 	m_pDataBaseDeal_->m_stru_DBConnInfo_.pchUserPassword, 
	// 	m_pDataBaseDeal_->m_stru_DBConnInfo_.pchDBServer, 
	// 	m_pDataBaseDeal_->m_stru_DBConnInfo_.pchDBName))
	// {
	// 	return -2; 
	// }
	// SQLHDBC pConn = m_pDataBaseDeal_->m_DBConn_.getconnection();
	// CStdOdbc_Command theCmd(pConn);
	// RETCODE retcode;
	// long size_1,size_2,size_3,size_4,size_5;
	// if (!theCmd.Execute(tm_sql))
	// {
	// 	theCmd.Release();
	// 	return -1;
	// } 

	// theCmd.BindCol(1, SQL_C_CHAR, tImageSend.strPicName, 255, &size_1);
	// theCmd.BindCol(2, SQL_INTEGER, &tImageSend.rate, 0, &size_2);
	// theCmd.BindCol(3, SQL_C_CHAR, tImageSend.strDesc, 255, &size_3);
	// theCmd.BindCol(4, SQL_INTEGER, &tImageSend.picture_Size, 0, &size_4);
	// while((retcode = theCmd.nextRow()) != SQL_NO_DATA)
	// {
	// 	retcode = theCmd.GetData(5, SQL_C_BINARY, tImageSend.picture, 8000, &size_5);
	// 	int pos = 1;

	// 	PPM_OS::memset(m_pSendBuf_,0,SEND_BUF_MAXSIZE);
	// 	int tm_ioffset = 0;
	// 	//构造信息发送
	// 	BuildSendBufHeadCmdCode(tm_ioffset, pos, FILETRANSFER_BEGIN);
	// 	int imagelen = size_5 > 8000 ? 8000 : size_5;
	// 	int sendlen = MAKEINT_NETSEQ1(imagelen);
	// 	PPM_OS::memcpy(m_pSendBuf_ + tm_ioffset,&sendlen,4);
	// 	tm_ioffset += 4;
	// 	PPM_OS::memcpy(m_pSendBuf_ + tm_ioffset,tImageSend.picture,imagelen);
	// 	tm_ioffset += imagelen;
	// 	if(0 > m_pServerDeal_->SendData(m_pSendBuf_,tm_ioffset)){
	// 		return -1;
	// 	}
	// 	PPM_OS::memset(tImageSend.picture,0,sizeof(tImageSend.picture));
	// 	while ((retcode = theCmd.GetData(5, SQL_C_BINARY, tImageSend.picture, 8000, &size_5)) != SQL_NO_DATA)
	// 	{
	// 		pos++;

	// 		if (0 > DealFileDownLoadTask(size_5, pos))
	// 		{
	// 			return -1;
	// 		}
	// 	}
	// }
	// m_pSendBuf_[2] = FILETRANSFER_ERROR;
	// PPM_OS::memset(m_pSendBuf_ + 3,0,8);

	// if(0 > m_pServerDeal_->SendData(m_pSendBuf_,3)){
	// 	return -1;
	// }
	return 1;
}

void  
ConfigMngSock_Image_Send::BuildSendBufHeadCmdCode(int& tm_ioffset, int pos, BYTE tr_type)
{
	// m_pSendBuf_[tm_ioffset++] = RESTYPE_FILEDOWNLOAD; //下载响应
	// m_pSendBuf_[tm_ioffset++] = CMD2_RESPONSE; //响应类型
	// m_pSendBuf_[tm_ioffset++] = tr_type;//文件传输类型
	// CSearch_PI::AddIntToBuf(m_pSendBuf_, tm_ioffset, pos, SEND_BUF_MAXSIZE);//文件体包号
	// CSearch_PI::AddIntToBuf(m_pSendBuf_, tm_ioffset, tImageSend.iareatypeid, SEND_BUF_MAXSIZE);
	// CSearch_PI::AddIntToBuf(m_pSendBuf_, tm_ioffset, tImageSend.iareaid, SEND_BUF_MAXSIZE);
	// CSearch_PI::AddStringToBuf(m_pSendBuf_, tm_ioffset, tImageSend.strPicName, SEND_BUF_MAXSIZE);
	// CSearch_PI::AddIntToBuf(m_pSendBuf_, tm_ioffset, tImageSend.rate, SEND_BUF_MAXSIZE);
	// CSearch_PI::AddStringToBuf(m_pSendBuf_, tm_ioffset, tImageSend.strDesc, SEND_BUF_MAXSIZE);
}

BOOL ConfigMngSock_Image_Send::DealFileDownLoadTask(long lsize, int pos)
{
	if (lsize > 8000)
	{
		if (0 > FileDownLoadContinue(pos))
		{
			return -1;
		}
	}
	else
	{
		if (0 > FileDownLoadEnd(lsize, pos))
		{
			return -1;
		}
	}

	return TRUE;
}

BOOL ConfigMngSock_Image_Send::FileDownLoadContinue(int pos)
{
	// PPM_OS::memset(m_pSendBuf_,0,SEND_BUF_MAXSIZE);
	// int tm_ioffset = 0;
	// //构造信息发送
	// BuildSendBufHeadCmdCode(tm_ioffset, pos, FILETRANSFER_CONTINUE);
	// int imagelen = 8000;
	// int sendlen = MAKEINT_NETSEQ1(imagelen);
	// PPM_OS::memcpy(m_pSendBuf_ + tm_ioffset, &sendlen, 4);
	// tm_ioffset += 4;
	// PPM_OS::memcpy(m_pSendBuf_ + tm_ioffset, tImageSend.picture, imagelen);
	// tm_ioffset += imagelen;
	// if(0 > m_pServerDeal_->SendData(m_pSendBuf_, tm_ioffset)){
	// 	return -1;
	// }
	// PPM_OS::memset(tImageSend.picture, 0, sizeof(tImageSend.picture));
  return TRUE;
}

BOOL ConfigMngSock_Image_Send::FileDownLoadEnd(long lsize, int pos)
{
	PPM_OS::memset(m_pSendBuf_,0,SEND_BUF_MAXSIZE);
	int tm_ioffset = 0;
	//构造信息发送
	// BuildSendBufHeadCmdCode(tm_ioffset, pos, FILETRANSFER_END);
	int imagelen = lsize;
	int sendlen = MAKEINT_NETSEQ1(imagelen);
	PPM_OS::memcpy(m_pSendBuf_ + tm_ioffset,&sendlen,4);
	tm_ioffset += 4;
	PPM_OS::memcpy(m_pSendBuf_ + tm_ioffset,tImageSend.picture,imagelen);
	tm_ioffset += imagelen;
	if(0 > m_pServerDeal_->SendData(m_pSendBuf_,tm_ioffset)){
		return -1;
	}
    
	return TRUE;
}

BOOL  
ConfigMngSock_Picture_Update::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	int sqlOperator;
	PPM_OS::memcpy(&sqlOperator,pData + ioffset,4);
	sqlOperator = MAKEINT_NETSEQ1(sqlOperator);
	MOVENEXT(ioffset,4,nCount);

	int packNum;
	PPM_OS::memcpy(&packNum,pData + ioffset,4);
	packNum = MAKEINT_NETSEQ1(packNum);
	MOVENEXT(ioffset,4,nCount);


	if(sqlOperator == 1 || sqlOperator == 2 || sqlOperator == 3 || sqlOperator == 4)
	{
		if (packNum == 1)
		{
			// DealFirstPackage(sqlOperator, packNum, pData, nCount, ioffset);
		}
		else if(packNum > 1)
		{
			// DealOtherPackage(sqlOperator, packNum, pData, nCount, ioffset);
		}
	}
	else if (sqlOperator == 5)
	{
		// DataCQTPointPictureSend(sqlOperator, packNum, pData, nCount, ioffset);
	}
	else if (sqlOperator == 6)
	{
		// DataCQTPointCellSend(sqlOperator, packNum, pData, nCount, ioffset);
	}

	return TRUE;
}

BOOL  
ConfigMngSock_Picture_Update::DataCQTPointPictureSend(int sqlOperator, int packNum, const BYTE* const pData, const int nCount,int& ioffset)
{
// 	int pointID;
// 	PPM_OS::memcpy(&pointID,pData + ioffset,4);
// 	pointID = MAKEINT_NETSEQ1(pointID);
// 	MOVENEXT(ioffset,4,nCount);

// 	char tm_sql[1024];
// 	PPM_OS::memset(&tm_sql, 0, 1024);

// 	PPM_OS::snprintf(tm_sql, sizeof(tm_sql),"exec mc_sp_cqtpicture_operation %d,%d,%d,%d,'%s','%s',%d,%d,%d,%d,%d,%d,%d,'%s',%d,%d,%s",
// 		sqlOperator,
// 		packNum,
// 		0,
// 		pointID,
// 		"",
// 		"",
// 		0,
// 		0,
// 		0,
// 		0,
// 		0,
// 		0,
// 		0,
// 		"",
// 		0,
// 		0,
// 		"null");

// 	if (NULL == m_pDataBaseDeal_)
// 	{
// 		return FALSE;
// 	}

// 	int iPictureID;
// 	int iPointID;
// 	char strPicName[255];
// 	PPM_OS::memset(strPicName,0,sizeof(strPicName));
// 	char strDesc[255];
// 	PPM_OS::memset(strDesc,0,sizeof(strDesc));
// 	int iBottomFloor;
// 	int iTopFloor;
// 	int iTLLong;
// 	int iTLLat;
// 	int iBRLong;
// 	int iBRLat;
// 	int iAltitude;
// 	char strAliasName[255];
// 	PPM_OS::memset(strAliasName,0,sizeof(strAliasName));
// 	int iPictureLength;
// 	int iPictureWidth;
// 	char picture[8000];
// 	PPM_OS::memset(picture,0,sizeof(picture));

// 	if (!m_pDataBaseDeal_->m_DBConn_.connect(m_pDataBaseDeal_->m_stru_DBConnInfo_.pchUserName, 
// 		m_pDataBaseDeal_->m_stru_DBConnInfo_.pchUserPassword, 
// 		m_pDataBaseDeal_->m_stru_DBConnInfo_.pchDBServer, 
// 		m_pDataBaseDeal_->m_stru_DBConnInfo_.pchDBName))
// 	{
// 		return -2; 
// 	}
// 	SQLHDBC pConn = m_pDataBaseDeal_->m_DBConn_.getconnection();
// 	CStdOdbc_Command theCmd(pConn);
// 	RETCODE retcode;
// 	long size_1,size_2;
// 	if (!theCmd.Execute(tm_sql))
// 	{
// 		theCmd.Release();
// 		return -1;
// 	} 
// 	theCmd.BindCol(1, SQL_INTEGER, &iPictureID, 255, &size_1);
// 	theCmd.BindCol(2, SQL_INTEGER, &iPointID, 0, &size_1);
// 	theCmd.BindCol(3, SQL_C_CHAR, strPicName, 255, &size_1);
// 	theCmd.BindCol(4, SQL_C_CHAR, strDesc, 255, &size_1);
// 	theCmd.BindCol(5, SQL_INTEGER, &iBottomFloor, 0, &size_1);
// 	theCmd.BindCol(6, SQL_INTEGER, &iTopFloor, 0, &size_1);
// 	theCmd.BindCol(7, SQL_INTEGER, &iTLLong, 0, &size_1);
// 	theCmd.BindCol(8, SQL_INTEGER, &iTLLat, 0, &size_1);
// 	theCmd.BindCol(9, SQL_INTEGER, &iBRLong, 0, &size_1);
// 	theCmd.BindCol(10, SQL_INTEGER, &iBRLat, 0, &size_1);
// 	theCmd.BindCol(11, SQL_INTEGER, &iAltitude, 0, &size_1);
// 	theCmd.BindCol(12, SQL_C_CHAR, strAliasName, 255, &size_1);
// 	theCmd.BindCol(13, SQL_INTEGER, &iPictureLength, 0, &size_1);
// 	theCmd.BindCol(14, SQL_INTEGER, &iPictureWidth, 0, &size_1);
// 	while((retcode = theCmd.nextRow()) != SQL_NO_DATA)
// 	{
// 		int pos = 1;

// 		PPM_OS::memset(m_pSendBuf_,0,SEND_BUF_MAXSIZE);
// 		int tm_ioffset = 0;
// 		//构造信息发送
// 		m_pSendBuf_[tm_ioffset++] = RESTYPE_FILEDOWNLOAD; //下载响应
// 		m_pSendBuf_[tm_ioffset++] = CMD2_RESPONSE; //响应类型
// 		m_pSendBuf_[tm_ioffset++] = FILETRANSFER_BEGIN;//文件传输类型
// 		CSearch_PI::AddIntToBuf(m_pSendBuf_, tm_ioffset, pos, SEND_BUF_MAXSIZE);//文件体包号
// 		CSearch_PI::AddIntToBuf(m_pSendBuf_, tm_ioffset, iPictureID, SEND_BUF_MAXSIZE);
// 		CSearch_PI::AddIntToBuf(m_pSendBuf_, tm_ioffset, iPointID, SEND_BUF_MAXSIZE);
// 		CSearch_PI::AddStringToBuf(m_pSendBuf_, tm_ioffset, strPicName, SEND_BUF_MAXSIZE);
// 		CSearch_PI::AddStringToBuf(m_pSendBuf_, tm_ioffset, strDesc, SEND_BUF_MAXSIZE);
// 		CSearch_PI::AddIntToBuf(m_pSendBuf_, tm_ioffset, iBottomFloor, SEND_BUF_MAXSIZE);
// 		CSearch_PI::AddIntToBuf(m_pSendBuf_, tm_ioffset, iTopFloor, SEND_BUF_MAXSIZE);
// 		CSearch_PI::AddIntToBuf(m_pSendBuf_, tm_ioffset, iTLLong, SEND_BUF_MAXSIZE);
// 		CSearch_PI::AddIntToBuf(m_pSendBuf_, tm_ioffset, iTLLat, SEND_BUF_MAXSIZE);
// 		CSearch_PI::AddIntToBuf(m_pSendBuf_, tm_ioffset, iBRLong, SEND_BUF_MAXSIZE);
// 		CSearch_PI::AddIntToBuf(m_pSendBuf_, tm_ioffset, iBRLat, SEND_BUF_MAXSIZE);
// 		CSearch_PI::AddIntToBuf(m_pSendBuf_, tm_ioffset, iAltitude, SEND_BUF_MAXSIZE);
// 		CSearch_PI::AddStringToBuf(m_pSendBuf_, tm_ioffset, strAliasName, SEND_BUF_MAXSIZE);
// 		CSearch_PI::AddIntToBuf(m_pSendBuf_, tm_ioffset, iPictureLength, SEND_BUF_MAXSIZE);
// 		CSearch_PI::AddIntToBuf(m_pSendBuf_, tm_ioffset, iPictureWidth, SEND_BUF_MAXSIZE);

// 		if(0 > m_pServerDeal_->SendData(m_pSendBuf_,tm_ioffset)){
// 			return -1;
// 		}
// 		PPM_OS::memset(picture,0,sizeof(picture));
		
// 		while ((retcode = theCmd.GetData(15, SQL_C_BINARY, picture, 8000, &size_2)) != SQL_NO_DATA)
// 		{
// 			pos++;

// 			if (0 > DealFileDownLoadTask(size_2, pos, picture))
// 			{
// 				return -1;
// 			}
// 		}
// 	}
// 	m_pSendBuf_[2] = FILETRANSFER_ERROR;
// 	PPM_OS::memset(m_pSendBuf_ + 3,0,8);

// 	if(0 > m_pServerDeal_->SendData(m_pSendBuf_,3)){
// 		return -1;
// 	}

	return TRUE;
}

BOOL ConfigMngSock_Picture_Update::DealFileDownLoadTask(long lsize, int pos, char* picture)
{
// 	if (lsize > 8000)
// 	{
// 		if (0 > FileDownLoadContinue(pos, picture))
// 		{
// 			return -1;
// 		}
// 	}
// 	else
// 	{
// 		if (0 > FileDownLoadEnd(lsize, pos, picture))
// 		{
// 			return -1;
// 		}
// 	}

// 	return TRUE;
// }
// BOOL ConfigMngSock_Picture_Update::FileDownLoadContinue(int pos, char* picture)
// {
// 	PPM_OS::memset(m_pSendBuf_,0,SEND_BUF_MAXSIZE);
// 	int tm_ioffset = 0;
// 	//构造信息发送
// 	m_pSendBuf_[tm_ioffset++] = RESTYPE_FILEDOWNLOAD; //下载响应
// 	m_pSendBuf_[tm_ioffset++] = CMD2_RESPONSE; //响应类型
// 	m_pSendBuf_[tm_ioffset++] = FILETRANSFER_CONTINUE;//文件传输类型
// 	CSearch_PI::AddIntToBuf(m_pSendBuf_, tm_ioffset, pos, SEND_BUF_MAXSIZE);//文件体包号
// 	int imagelen = 8000;
// 	int sendlen = MAKEINT_NETSEQ1(imagelen);
// 	PPM_OS::memcpy(m_pSendBuf_ + tm_ioffset,&sendlen,4);
// 	tm_ioffset += 4;
// 	PPM_OS::memcpy(m_pSendBuf_ + tm_ioffset,picture,imagelen);
// 	tm_ioffset += imagelen;
// 	if(0 > m_pServerDeal_->SendData(m_pSendBuf_,tm_ioffset)){
// 		return -1;
// 	}

// 	PPM_OS::memset(picture,0,sizeof(picture));
	return TRUE;
}
BOOL ConfigMngSock_Picture_Update::FileDownLoadEnd(long lsize, int pos, char* picture)
{
// 	PPM_OS::memset(m_pSendBuf_,0,SEND_BUF_MAXSIZE);
// 	int tm_ioffset = 0;
// 	//构造信息发送
// 	m_pSendBuf_[tm_ioffset++] = RESTYPE_FILEDOWNLOAD; //下载响应
// 	m_pSendBuf_[tm_ioffset++] = CMD2_RESPONSE; //响应类型
// 	m_pSendBuf_[tm_ioffset++] = FILETRANSFER_END;//文件传输类型
// 	CSearch_PI::AddIntToBuf(m_pSendBuf_, tm_ioffset, pos, SEND_BUF_MAXSIZE);//文件体包号
// 	int imagelen = lsize;
// 	int sendlen = MAKEINT_NETSEQ1(imagelen);
// 	PPM_OS::memcpy(m_pSendBuf_ + tm_ioffset,&sendlen,4);
// 	tm_ioffset += 4;
// 	PPM_OS::memcpy(m_pSendBuf_ + tm_ioffset,picture,imagelen);
// 	tm_ioffset += imagelen;
// 	if(0 > m_pServerDeal_->SendData(m_pSendBuf_,tm_ioffset)){
// 		return -1;
// 	}
    return TRUE;
}

BOOL  
ConfigMngSock_Picture_Update::DataCQTPointCellSend(int sqlOperator, int packNum, const BYTE* const pData, const int nCount,int& ioffset)
{
// 	int pointID;
// 	PPM_OS::memcpy(&pointID,pData + ioffset,4);
// 	pointID = MAKEINT_NETSEQ1(pointID);
// 	MOVENEXT(ioffset,4,nCount);

// 	char tm_sql[1024];
// 	PPM_OS::memset(&tm_sql, 0, 1024);

// 	PPM_OS::snprintf(tm_sql, sizeof(tm_sql),
//         "exec mc_sp_cqtpicture_operation %d,%d,%d,%d,'%s','%s',%d,%d,%d,%d,%d,%d,%d,'%s',%d,%d,%s",
// 		sqlOperator,
// 		packNum,
// 		0,
// 		pointID,
// 		"",
// 		"",
// 		0, 0, 0, 0, 0, 0, 0, "", 0, 0, "null");

// 	if (NULL == m_pDataBaseDeal_)
// 	{
// 		return FALSE;
// 	}

// 	int ipointID;
// 	int networkType;
// 	int LAC;
// 	int CI;
// 	char cellName[255];
// 	PPM_OS::memset(cellName,0,sizeof(cellName));
// 	char BSCName[255];
// 	PPM_OS::memset(BSCName,0,sizeof(BSCName));
// 	int priority;

// 	if (!m_pDataBaseDeal_->m_DBConn_.connect(m_pDataBaseDeal_->m_stru_DBConnInfo_.pchUserName, 
// 		m_pDataBaseDeal_->m_stru_DBConnInfo_.pchUserPassword, 
// 		m_pDataBaseDeal_->m_stru_DBConnInfo_.pchDBServer, 
// 		m_pDataBaseDeal_->m_stru_DBConnInfo_.pchDBName))
// 	{
// 		return -2; 
// 	}
// 	SQLHDBC pConn = m_pDataBaseDeal_->m_DBConn_.getconnection();
// 	CStdOdbc_Command theCmd(pConn);
// 	RETCODE retcode;
// 	long size_1;
// 	if (!theCmd.Execute(tm_sql))
// 	{
// 		theCmd.Release();
// 		return -1;
// 	} 
// 	theCmd.BindCol(1, SQL_INTEGER, &ipointID, 255, &size_1);
// 	theCmd.BindCol(2, SQL_INTEGER, &networkType, 0, &size_1);
// 	theCmd.BindCol(3, SQL_INTEGER, &LAC, 0, &size_1);
// 	theCmd.BindCol(4, SQL_INTEGER, &CI, 0, &size_1);
// 	theCmd.BindCol(5, SQL_C_CHAR, cellName, 255, &size_1);
// 	theCmd.BindCol(6, SQL_C_CHAR, BSCName, 255, &size_1);
// 	theCmd.BindCol(7, SQL_INTEGER, &priority, 0, &size_1);
// 	while((retcode = theCmd.nextRow()) != SQL_NO_DATA)
// 	{
// 		int pos = 1;
// 		PPM_OS::memset(m_pSendBuf_,0,SEND_BUF_MAXSIZE);
// 		int tm_ioffset = 0;
// 		//构造信息发送
// 		m_pSendBuf_[tm_ioffset++] = RESTYPE_FILEDOWNLOAD; //下载响应
// 		m_pSendBuf_[tm_ioffset++] = CMD2_RESPONSE; //响应类型
// 		m_pSendBuf_[tm_ioffset++] = FILETRANSFER_BEGIN;//文件传输类型
// 		CSearch_PI::AddIntToBuf(m_pSendBuf_, tm_ioffset, pos, SEND_BUF_MAXSIZE);//文件体包号
// 		CSearch_PI::AddIntToBuf(m_pSendBuf_, tm_ioffset, ipointID, SEND_BUF_MAXSIZE);
// 		CSearch_PI::AddIntToBuf(m_pSendBuf_, tm_ioffset, networkType, SEND_BUF_MAXSIZE);
// 		CSearch_PI::AddIntToBuf(m_pSendBuf_, tm_ioffset, LAC, SEND_BUF_MAXSIZE);
// 		CSearch_PI::AddIntToBuf(m_pSendBuf_, tm_ioffset, CI, SEND_BUF_MAXSIZE);
// 		CSearch_PI::AddStringToBuf(m_pSendBuf_, tm_ioffset, cellName, SEND_BUF_MAXSIZE);
// 		CSearch_PI::AddStringToBuf(m_pSendBuf_, tm_ioffset, BSCName, SEND_BUF_MAXSIZE);
// 		CSearch_PI::AddIntToBuf(m_pSendBuf_, tm_ioffset, priority, SEND_BUF_MAXSIZE);

// 		if(0 > m_pServerDeal_->SendData(m_pSendBuf_,tm_ioffset)){
// 			return -1;
// 		}
// 	}
// 	m_pSendBuf_[2] = FILETRANSFER_ERROR;
// 	PPM_OS::memset(m_pSendBuf_ + 3,0,8);

// 	if(0 > m_pServerDeal_->SendData(m_pSendBuf_,3)){
// 		return -1;
// 	}

	return TRUE;
}

BOOL  
ConfigMngSock_Picture_Update::DealFirstPackage(int sqlOperator, int packNum, const BYTE* const pData, const int nCount,int& ioffset)
{
// 	int pictureID;
// 	PPM_OS::memcpy(&pictureID,pData + ioffset,4);
// 	pictureID = MAKEINT_NETSEQ1(pictureID);
// 	MOVENEXT(ioffset,4,nCount);

// 	int pointID;
// 	PPM_OS::memcpy(&pointID,pData + ioffset,4);
// 	pointID = MAKEINT_NETSEQ1(pointID);
// 	MOVENEXT(ioffset,4,nCount);

// 	int bottomFloor;
// 	PPM_OS::memcpy(&bottomFloor,pData + ioffset,4);
// 	bottomFloor = MAKEINT_NETSEQ1(bottomFloor);
// 	MOVENEXT(ioffset,4,nCount);

// 	int topFloor;
// 	PPM_OS::memcpy(&topFloor,pData + ioffset,4);
// 	topFloor = MAKEINT_NETSEQ1(topFloor);
// 	MOVENEXT(ioffset,4,nCount);

// 	int altitude;
// 	PPM_OS::memcpy(&altitude,pData + ioffset,4);
// 	altitude = MAKEINT_NETSEQ1(altitude);
// 	MOVENEXT(ioffset,4,nCount);

// 	int pictureLength;
// 	PPM_OS::memcpy(&pictureLength,pData + ioffset,4);
// 	pictureLength = MAKEINT_NETSEQ1(pictureLength);
// 	MOVENEXT(ioffset,4,nCount);

// 	int pictureWidth;
// 	PPM_OS::memcpy(&pictureWidth,pData + ioffset,4);
// 	pictureWidth = MAKEINT_NETSEQ1(pictureWidth);
// 	MOVENEXT(ioffset,4,nCount);

// 	int tlLongitude;
// 	PPM_OS::memcpy(&tlLongitude,pData + ioffset,4);
// 	tlLongitude = MAKEINT_NETSEQ1(tlLongitude);
// 	MOVENEXT(ioffset,4,nCount);

// 	int tlLatitude;
// 	PPM_OS::memcpy(&tlLatitude,pData + ioffset,4);
// 	tlLatitude = MAKEINT_NETSEQ1(tlLatitude);
// 	MOVENEXT(ioffset,4,nCount);

// 	int brLongitude;
// 	PPM_OS::memcpy(&brLongitude,pData + ioffset,4);
// 	brLongitude = MAKEINT_NETSEQ1(brLongitude);
// 	MOVENEXT(ioffset,4,nCount);

// 	int brLatitude;
// 	PPM_OS::memcpy(&brLatitude,pData + ioffset,4);
// 	brLatitude = MAKEINT_NETSEQ1(brLatitude);
// 	MOVENEXT(ioffset,4,nCount);

// 	char pictureName[50];
// 	PPM_OS::memset(&pictureName, 0, sizeof(pictureName));
// 	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,pictureName,sizeof(pictureName)));

// 	char aliasName[50];
// 	PPM_OS::memset(&aliasName, 0, sizeof(aliasName));
// 	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,aliasName,sizeof(aliasName)));

// 	char pictureDesc[50];
// 	PPM_OS::memset(&pictureDesc, 0, sizeof(pictureDesc));
// 	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,pictureDesc,sizeof(pictureDesc)));

// 	char strsql[8100 * 2 * 8 + 1024];
// 	PPM_OS::memset(&strsql, 0, sizeof(strsql));

// 	PPM_OS::snprintf(strsql, sizeof(strsql),"exec mc_sp_cqtpicture_operation %d,%d,%d,%d,'%s','%s',%d,%d,%d,%d,%d,%d,%d,'%s',%d,%d,%s",
// 		sqlOperator,
// 		packNum,
// 		pictureID,
// 		pointID,
// 		pictureName,
// 		pictureDesc,
// 		bottomFloor,
// 		topFloor,
// 		tlLongitude,
// 		tlLatitude,
// 		brLongitude,
// 		brLatitude,
// 		altitude,
// 		aliasName,
// 		pictureLength,
// 		pictureWidth,
// 		"null");

// 	if(NULL != m_pDataBaseDeal_)
// 	{
// 		if(0 > m_pDataBaseDeal_->ExecSql(strsql))
// 		{
// 			return FALSE;
// 		}
// 		return TRUE;
// 	}

	return TRUE;
}

BOOL  
ConfigMngSock_Picture_Update::DealOtherPackage(int sqlOperator, int packNum, const BYTE* const pData, const int nCount,int& ioffset)
{
// 	int pictureID;
// 	PPM_OS::memcpy(&pictureID,pData + ioffset,4);
// 	pictureID = MAKEINT_NETSEQ1(pictureID);
// 	MOVENEXT(ioffset,4,nCount);

// 	int pointID;
// 	PPM_OS::memcpy(&pointID,pData + ioffset,4);
// 	pointID = MAKEINT_NETSEQ1(pointID);
// 	MOVENEXT(ioffset,4,nCount);

// 	char pictureName[50];
// 	PPM_OS::memset(&pictureName, 0, sizeof(pictureName));
// 	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,pictureName,sizeof(pictureName)));

// 	int receiveBufferLenth;
// 	PPM_OS::memcpy(&receiveBufferLenth,pData + ioffset,4);
// 	receiveBufferLenth = MAKEINT_NETSEQ1(receiveBufferLenth);
// 	MOVENEXT(ioffset,4,nCount);

// 	char strsql[8200 * 2 + 2048];
// 	PPM_OS::memset(&strsql, 0, 8200 * 2 + 2048);

// 	if (receiveBufferLenth > SEND_BUF_MAXSIZE * 2 - 8)
// 	{
// 		//处理结束
// 		FeedBackOver(CMD1_CONFIG_MNG,RESTYPE_SEARCHEND);
// 		return TRUE;
// 	}

// 	BYTE byteBuffer[8200 + 512];
// 	PPM_OS::memset(&byteBuffer, 0, 8200 + 512);
// 	PPM_OS::memcpy(byteBuffer, pData+ioffset, receiveBufferLenth);

// 	char pImage[8200 * 2 + 1024];
// 	PPM_OS::memset(pImage, 0, 8200 * 2 + 1024);

// 	PPM_OS::snprintf(pImage,sizeof(pImage),"0X");
// 	char *p = pImage;
// 	for (int i=0; i<receiveBufferLenth; i++)
// 	{
// 		p = p+2;
// 		PPM_OS::snprintf(p,2,"%02X",byteBuffer[i]);
// 	}

// 	PPM_OS::snprintf(strsql, sizeof(strsql),"exec mc_sp_cqtpicture_operation %d,%d,%d,%d,'%s','%s',%d,%d,%d,%d,%d,%d,%d,'%s',%d,%d,%s",
// 		sqlOperator,
// 		packNum,
// 		pictureID,
// 		pointID,
// 		pictureName,
// 		"",
// 		0,
// 		0,
// 		0,
// 		0,
// 		0,
// 		0,
// 		0,
// 		"",
// 		0,
// 		0,
// 		&pImage);

// 	if(NULL != m_pDataBaseDeal_)
// 	{
// 		if(0 > m_pDataBaseDeal_->ExecSql(strsql))
// 		{
// 			return FALSE;
// 		}
// 		return TRUE;
// 	}

	return TRUE;
}




