// ConfigMngSock_Other.h: interface for the CConfigMngSock_Other class.
//
//////////////////////////////////////////////////////////////////////

#ifndef __CONFIGMNGSOCK_OTHER_H__
#define __CONFIGMNGSOCK_OTHER_H__

#include "./SearchSock.h"
   
class ConfigMngSock_VersionConfig : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};


class ConfigMngSock_StaticInfoConfig : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};


class ConfigMngSock_DistrictCommunityStatConfig : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};


class ConfigMngSock_VillageInfoConfig : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};


class ConfigMngSock_CityConfig : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};


class ConfigMngSock_RDLogInfoAdd : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};


class ConfigMngSock_ProjExtendConfig : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};

class ConfigMngSock_ImageConfig : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};

class ConfigMngSock_ColumnConfig : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};

class ConfigMngConfigKpiTable : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};


class ConfigMngConfigAreaTypeSet : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};


class ConfigMngSock_Image_Insert : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};

class ConfigMngSock_Image_Send : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);

private:
	struct Struc_ImageSend
	{
		int iareatypeid;
    int iareaid;
		int rate;
		int picture_Size;
		char strPicName[255];
		char strDesc[255];
		char picture[8000];
	} tImageSend;

	BOOL DealFileDownLoadTask(long lsize, int pos);
	BOOL FileDownLoadContinue(int pos);
	BOOL FileDownLoadEnd(long lsize, int pos);
	void BuildSendBufHeadCmdCode(int& tm_ioffset, int pos, BYTE tr_type);

};

class ConfigMngSock_Picture_Update : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
	virtual BOOL DataCQTPointPictureSend(int sqlOperator, int packNum, const BYTE* const pData, const int nCount,int& ioffset);
	virtual BOOL DataCQTPointCellSend(int sqlOperator, int packNum, const BYTE* const pData, const int nCount,int& ioffset);
	virtual BOOL DealFirstPackage(int sqlOperator, int packNum, const BYTE* const pData, const int nCount,int& ioffset);
	virtual BOOL DealOtherPackage(int sqlOperator, int packNum, const BYTE* const pData, const int nCount,int& ioffset);

private:
	BOOL DealFileDownLoadTask(long lsize, int pos, char* picture);
	BOOL FileDownLoadContinue(int pos, char* picture);
	BOOL FileDownLoadEnd(long lsize, int pos, char* picture);

};


#endif
