// ComDef_Config.h: interface for the CComDef_Config class.
//
//////////////////////////////////////////////////////////////////////

#ifndef __COMDEF_CONFIG_H__
#define __COMDEF_CONFIG_H__

#define RESTYPE_FILE_INFO                       0x01   //RESTYPE返回文件头信息
#define RESTYPE_FILE_INFO_MORE                  0x02   //RESTYPE返回文件头信息

#define REQTYPE_CONFIG_VERSION					0xa0   //REQUEST
#define RESTYPE_CONFIG_VERSION					0xa0
#define REQTYPE_CONFIG_UPDATE_DESC				0xa1   //REQUEST
#define RESTYPE_CONFIG_UPDATE_DESC				0xa1

#define REQTYPE_CONFIG_ROLE						0xa2	//REQUEST			//角色配置信息   
#define RESTYPE_CONFIG_ROLE						0xa2				//角色配置信息
#define REQTYPE_CONFIG_ROLE_FUNC				0xa3	//REQUEST			//角色对应的权限信息   
#define RESTYPE_CONFIG_ROLE_FUNC				0xa3				//角色对应的权限信息

#define REQTYPE_CONFIG_USERCITY				    0xa4	//REQUEST			//角色配置信息   
#define RESTYPE_CONFIG_USERCITY				    0xa4				//角色配置信息

#define REQTYPE_CONFIG_SERVER_LIST              0xa5    //服务器列表
#define RESTYPE_CONFIG_SERVER_LIST              0xa5

#define REQTYPE_CONFIG_MNG						0xb0	//REQUEST
#define	RESTYPE_CONFIG_BTS						0xb0
#define	RESTYPE_CONFIG_CELL						0xb1
#define	RESTYPE_CONFIG_PD						0xb2
#define RESTYPE_CONFIG_REPEATER					0xb3
#define RESTYPE_CONFIG_TDSCDMA_BTS              0xb4
#define RESTYPE_CONFIG_TDSCDMA_CELL             0xb5
#define RESTYPE_CONFIG_TDSCDMA_PD               0xb6
#define RESTYPE_CONFIG_GSM_NBCELL               0xb7
#define RESTYPE_CONFIG_LTE_BTS                  0xb9
#define RESTYPE_CONFIG_LTE_CELL                 0xba
#define RESTYPE_CONFIG_LTE_PD                   0xbb
#define RESTYPE_CONFIG_LTE_NBCELL               0xbc

#define RESTYPE_CONFIG_CULTE_CELL				0xbd
#define RESTYPE_CONFIG_CTLTE_CELL				0xbe

#define REQTYPE_CONFIG_MNG_CDMA				    0xb2	//REQUEST
#define RESTYPE_CONFIG_CDMA_BTS                 0xc0
#define RESTYPE_CONFIG_CDMA_CELL                0xc1
#define RESTYPE_CONFIG_CDMA_PD                  0xc2

#define REQTYPE_VILLAGE_INFO                    0xb1     //REQUEST
#define RESTYPE_VILLAGE_Admin                   0xb1
#define RESTYPE_VILLAGE_Nature                  0xb2

#define REQTYPE_CONFIG_NBCELL					0xc0	//REQUEST
#define RESTYPE_CONFIG_NBCELL					0xc0

#define REQTYPE_CONFIG_TDSCDMA_NBCELL           0xc1    //REQUEST
#define RSQTYPE_CONFIG_TDSCDMA_NBCELL           0xc1    

#define REQTYPE_CONFIG_WCDMA_CELL				0xd0
#define RESTPYE_CONFIG_WCDMA_NODEB				0xd0
#define RESTYPE_CONFIG_WCDMA_CELL				0xd1
#define RESTYPE_CONFIG_WCDMA_ANTENNA			0xd2

#define REQTYPE_CONFIG_WCDMA_NBCELL				0xd1
#define RESTYPE_CONFIG_WCDMA_NBCELL				0xd3

#define RESTYPE_CONFIG_NR_BTS                  0xd4
#define RESTYPE_CONFIG_NR_CELL                 0xd5
#define RESTYPE_CONFIG_NR_PD                   0xd6
#define RESTYPE_CONFIG_NR_NBCELL               0xd7

#define REQTYPE_CONFIG_STATIC_INFO				0x70	//REQUEST	//文件属性信息
#define RESTYPE_CONFIG_STATIC_PROJECT			0x70				//项目类型配置信息
#define RESTYPE_CONFIG_STATIC_TEST				0x71				//测试类型配置信息
#define RESTYPE_CONFIG_STATIC_DEVICE			0x72				//设备类型配置信息
#define RESTYPE_CONFIG_STATIC_FILE				0x73				//文件类型配置信息
#define RESTYPE_CONFIG_STATIC_SERVICE			0x74				//业务类型配置信息
#define RESTYPE_CONFIG_STATIC_CARRIER			0x75				//运营商配置信息
#define RESTYPE_CONFIG_STATIC_AGENT				0x76				//代维公司配置信息
#define RESTYPE_CONFIG_STATIC_STAFF				0x77				//使用人员配置信息
#define RESTYPE_CONFIG_STATIC_AREAHEAD			0x78				//区域类型配置信息
#define RESTYPE_CONFIG_STATIC_AREADETAIL		0x79				//区域ID配置信息

#define REQTYPE_CONFIG_STATIC_AREA				0x60	//REQUEST	//区域信息
#define RESTYPE_CONFIG_STATIC_AREATYPE			0x60				//区域类型配置信息
#define RESTYPE_CONFIG_STATIC_LOCAL				0x61				//本地网信息
#define RESTYPE_CONFIG_STATIC_DISTRICT			0x62				//行政区信息
#define RESTYPE_CONFIG_STATIC_OPT_DISTRICT		0x63				//优化片区信息
#define RESTYPE_CONFIG_STATIC_COMMUNITY			0x64				//街道办信息


#define	REQTYPE_CONFIG_STAT_AREATYPE			0x50   //REQUEST
#define	RESTYPE_CONFIG_STAT_AREATYPE			0x50

#define	REQTYPE_CONFIG_STAT_AREASCAN			0x51   //REQUEST
#define	RESTYPE_CONFIG_STAT_AREASCAN			0x51

#define	REQTYPE_CONFIG_STAT_AREA				0x40   //REQUEST
#define	RESTYPE_CONFIG_STAT_AREA				0x40

#define	REQTYPE_CONFIG_STAT_DISTRICT_COMMUNITY	0x30   //REQUEST
#define	RESTYPE_CONFIG_STAT_DISTRICT_COMMUNITY	0x30

#define REQTYPE_GIS_CQT_AREA_LIST               0x19   //REQUEST
#define RESTYPE_GIS_CQT_AREA_LIST               0x19

#define REQTYPE_GIS_AREA_TYPE					0x20   //REQUEST
#define RESTYPE_GIS_AREA_TYPE					0x20

#define REQTYPE_GIS_AREA_LIST					0x21   //REQUEST
#define RESTYPE_GIS_AREA_LIST					0x21

#define REQTYPE_GIS_AREA_ADD					0x22   //REQUEST
#define RESTYPE_GIS_AREA_ADD					0x22
#define REQTYPE_GIS_AREA_DELETE					0x23   //REQUEST
#define RESTYPE_GIS_AREA_DELETE					0x23
#define REQTYPE_GIS_AREA_EDIT					0x24   //REQUEST
#define RESTYPE_GIS_AREA_EDIT					0x24

#define REQTYPE_GIS_COVER_REGION_ADD              0x25   //REQUEST
#define RESTYPE_GIS_COVER_REGION_ADD              0x25
#define REQTYPE_GIS_COVER_REGION_DELETE           0x26   //REQUEST 
#define RESTYPE_GIS_COVER_REGION_DELETE           0x26  
#define REQTYPE_GIS_COVER_REGION_EDIT             0x27   //REQUEST
#define RESTYPE_GIS_COVER_REGION_EDIT             0x27       
#define REQTYPE_GIS_COVER_REGION_GET              0x28   //REQUEST
#define RESTYPE_GIS_COVER_REGION_GET              0x28
#define RESTYPE_GIS_COVER_REGION_POINT            0x29

#define REQTYPE_CONFIG_PROJECT_EXTEND             0x29   //REQUEST
#define RESTYPE_CONFIG_PROJECT_EXTEND             0x29

#define REQTYPE_CONFIG_STATIC_CITY              0x71    //REQUEST
#define RESTYPE_CONFIG_STATIC_CITY              0x80

#define REQTYPE_RECORD_LOG_ADD                  0X72    //REQUEST
#define RSQTYPE_RECORD_LOG_ADD                  0X72 

#define REQTYPE_CONFIG_CELL_TIMESTAMP             0x2a   //REQUEST
#define RESTYPE_CONFIG_CELL_TIMESTAMP             0x2a

#define REQTYPE_CONFIG_KPI_TABLE                  0x2b   //REQUEST
#define RESTYPE_CONFIG_KPI_TABLE                  0x2b

#define REQTYPE_CONFIG_AREATYPE_SET               0x2c   //REQUEST
#define REQTYPE_CONFIG_AREATYPE_SET               0x2c

//ID更正
#define REQTYPE_CONFIG_IMAGECOLUMN               0x31   //REQUEST
#define RESTYPE_CONFIG_IMAGECOLUMN               0x31

#define REQTYPE_CONFIG_TABLECOLUMN               0x32   //REQUEST
#define RESTYPE_CONFIG_TABLECOLUMN               0x32

#define REQTYPE_CQT_IMAGE_INFO_INSERT					 0x33   //REQUEST     //插入CQT图片二进制流到数据库
#define REQTYPE_CQT_IMAGE_INFO_SEND						 0x34   //REQUEST     //发送CQT图片二进制流到客户端

#define REQTYPE_CQT_PICTURE_INFO_UPDATE			0x35


#endif 
