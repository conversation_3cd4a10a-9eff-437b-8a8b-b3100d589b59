// SockConfigMngDeal.cpp: implementation of the CSockConfigMngDeal class.
//
//////////////////////////////////////////////////////////////////////



#include "./SockConfigMngDeal.h"

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CSockConfigMngDeal::CSockConfigMngDeal()
:m_mapScmdReqTypePos()
{
	InitmReqTypeMap();
}

CSockConfigMngDeal::~CSockConfigMngDeal()
{
	m_mapScmdReqTypePos.clear();
}

BOOL
CSockConfigMngDeal::DealSock(const BYTE* const pData, const int nCount,int& ioffset, const STRU_SockInfo& sockInfo)
{
	char tm_addr[512];
	sockInfo.pServerDeal_->m_ptcpclient_->GetInetAddr()->addr_to_string(tm_addr, sizeof(tm_addr), 1);
	std::string strUserInfo = "[" + std::string(tm_addr) + "]";

	if(CMD2_REQUEST == pData[ioffset])//请求
	{
		MOVENEXT(ioffset,1,nCount)

		std::map<BYTE, DealReqTypeData>::iterator it;
		it = m_mapScmdReqTypePos.find(pData[ioffset++]);

		if (it != m_mapScmdReqTypePos.end())
		{
			(this->*(it->second))(pData, nCount, ioffset, sockInfo, strUserInfo);
		}
		else
		{
			PPM_DEBUG((LM_ERROR, "unknown search request type:%d\n", pData[ioffset - 1]));
			return FALSE;
		}
	}
	else //响应 
	{
		PPM_DEBUG((LM_ERROR,"should not be search data management response:%d\n",pData[ioffset+1]));
		return FALSE;
	}

	return TRUE;
}

void 
CSockConfigMngDeal::
Deal_Reqtype_Config_Mng(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo)
{
	DEALDATA2(ConfigMngSock_CellConfig, strUserInfo);
}
void 
CSockConfigMngDeal::
Deal_Reqtype_Config_Mng_Cdma(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo)
{
	DEALDATA2(ConfigMngSock_CellConfig_CDMA, strUserInfo);
}
void 
CSockConfigMngDeal::
Deal_Reqtype_Config_NBCell(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo)
{
	DEALDATA2(ConfigMngSock_CellNBConfig, strUserInfo);
}
void 
CSockConfigMngDeal::
Deal_Reqtype_Config_TDScdma_NBCell(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo)
{
	DEALDATA2(ConfigMngSock_CellNBTDSCDMAConfig, strUserInfo);
}
void 
CSockConfigMngDeal::
Deal_Reqtype_Config_Wcdma_Cell(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo)
{
	DEALDATA2(ConfigMngSock_CellWCDMAConfig, strUserInfo);
}
void 
CSockConfigMngDeal::
Deal_Reqtype_Config_Wcdma_NBCell(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo)
{
	DEALDATA2(ConfigMngSock_CellNBWCDMAConfig, strUserInfo);
}
void 
CSockConfigMngDeal::
Deal_RESTYPE_Config_Culte_Cell(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo)
{
	DEALDATA2(ConfigMngSock_CellCULTEConfig, strUserInfo);
}
void 
CSockConfigMngDeal::
Deal_RESTYPE_Config_Ctlte_Cell(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo)
{
	DEALDATA2(ConfigMngSock_CellCTLTEConfig, strUserInfo);
}
void 
CSockConfigMngDeal::
Deal_Reqtype_Config_Cell_TimeStamp(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo)
{
	DEALDATA2(ConfigMngSock_CellTimeStamp, strUserInfo);
}
void 
CSockConfigMngDeal::
Deal_Reqtype_Config_Server_List(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo)
{
	DEALDATA2(ConfigMngSock_Server_List, strUserInfo);
}
void 
CSockConfigMngDeal::
Deal_Reqtype_Config_Static_Area(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo)
{
	DEALDATA2(ConfigMngSock_AreaInfoConfig, strUserInfo);
}
void 
CSockConfigMngDeal::
Deal_Reqtype_Config_Stat_AreaType(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo)
{
	DEALDATA2(ConfigMngSock_AreaTypeConfig, strUserInfo);
}
void 
CSockConfigMngDeal::
Deal_Reqtype_Config_Stat_AreaScan(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo)
{
	DEALDATA2(ConfigMngSock_AreaScanConfig, strUserInfo);
}
void 
CSockConfigMngDeal::
Deal_Reqtype_Config_Stat_Area(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo)
{
	DEALDATA2(ConfigMngSock_AreaStatConfig, strUserInfo);
}
void 
CSockConfigMngDeal::
Deal_Reqtype_Gis_Cqt_Area_List(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo)
{
	DEALDATA2(ConfigMngSock_CQTAreaListGisConfig, strUserInfo);
}
void 
CSockConfigMngDeal::
Deal_Reqtype_Gis_Area_Type(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo)
{
	DEALDATA2(ConfigMngSock_AreaTypeGisConfig, strUserInfo);
}
void 
CSockConfigMngDeal::
Deal_Reqtype_Gis_Area_List(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo)
{
	DEALDATA2(ConfigMngSock_AreaListGisConfig, strUserInfo);
}
void 
CSockConfigMngDeal::
Deal_Reqtype_Gis_Area_Add(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo)
{
	DEALDATA2(ConfigMngSock_AreaAddConfig, strUserInfo);
}
void 
CSockConfigMngDeal::
Deal_Reqtype_Gis_Area_Edit(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo)
{
	DEALDATA2(ConfigMngSock_AreaEditConfig, strUserInfo);
}
void 
CSockConfigMngDeal::
Deal_Reqtype_Gis_Area_Delete(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo)
{
	DEALDATA2(ConfigMngSock_AreaDeleteonfig, strUserInfo);
}
void 
CSockConfigMngDeal::
Deal_Reqtype_Gis_Cover_Region_Add(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo)
{
	DEALDATA2(ConfigMngSock_CoverRegionAddConfig, strUserInfo);
}
void 
CSockConfigMngDeal::
Deal_Reqtype_Gis_Cover_Region_Delete(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo)
{
	DEALDATA2(ConfigMngSock_CoverRegionDeleteConfig, strUserInfo);
}
void 
CSockConfigMngDeal::
Deal_Reqtype_Gis_Cover_Region_Edit(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo)
{
	DEALDATA2(ConfigMngSock_CoverRegionEditConfig, strUserInfo);
}
void 
CSockConfigMngDeal::
Deal_Reqtype_Gis_Cover_Region_Get(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo)
{
	DEALDATA2(ConfigMngSock_CoverRegionGetConfig, strUserInfo);
}
void 
CSockConfigMngDeal::
Deal_Reqtype_Config_Version(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo)
{
	DEALDATA2(ConfigMngSock_VersionConfig, strUserInfo);
}
void 
CSockConfigMngDeal::
Deal_Reqtype_Config_StatIC_Info(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo)
{
	DEALDATA2(ConfigMngSock_StaticInfoConfig, strUserInfo);
}
void 
CSockConfigMngDeal::
Deal_Reqtype_Config_Stat_District_Community(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo)
{
	DEALDATA2(ConfigMngSock_DistrictCommunityStatConfig, strUserInfo);
}
void 
CSockConfigMngDeal::
Deal_Reqtype_Village_Info(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo)
{
	DEALDATA2(ConfigMngSock_VillageInfoConfig, strUserInfo);
}
void 
CSockConfigMngDeal::
Deal_Reqtype_Config_Static_City(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo)
{
	DEALDATA2(ConfigMngSock_CityConfig, strUserInfo);
}
void 
CSockConfigMngDeal::
Deal_Reqtype_Record_Log_Add(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo)
{
	DEALDATA2(ConfigMngSock_RDLogInfoAdd, strUserInfo);
}
void 
CSockConfigMngDeal::
Deal_Reqtype_Config_Project_Extend(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo)
{
	DEALDATA2(ConfigMngSock_ProjExtendConfig, strUserInfo);
}
void 
CSockConfigMngDeal::
Deal_Reqtype_Config_ImageColumn(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo)
{
	DEALDATA2(ConfigMngSock_ImageConfig, strUserInfo);
}
void 
CSockConfigMngDeal::
Deal_Reqtype_Config_TableColumn(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo)
{
	DEALDATA2(ConfigMngSock_ColumnConfig, strUserInfo);
}
void 
CSockConfigMngDeal::
Deal_Reqtype_Config_Kpi_Table(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo)
{
	DEALDATA2(ConfigMngConfigKpiTable, strUserInfo);
}
void 
CSockConfigMngDeal::
Deal_Reqtype_Config_AreaTYPE_Set(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo)
{
	DEALDATA2(ConfigMngConfigAreaTypeSet, strUserInfo);
}
void 
CSockConfigMngDeal::
Deal_Reqtype_Cqt_Image_Info_Insert(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo)
{
	DEALDATA2(ConfigMngSock_Image_Insert, strUserInfo);
}
void 
CSockConfigMngDeal::
Deal_Reqtype_Cqt_Image_Info_Send(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo)
{
	DEALDATA2(ConfigMngSock_Image_Send, strUserInfo);
}
void 
CSockConfigMngDeal::
Deal_Reqtype_Cqt_Picture_Info_Update(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo)
{
	DEALDATA2(ConfigMngSock_Picture_Update, strUserInfo);
}

void CSockConfigMngDeal::InitmReqTypeMap(void)
{
	//ConfigMngSock_Cell
	m_mapScmdReqTypePos.insert(make_pair(REQTYPE_CONFIG_MNG, &CSockConfigMngDeal::Deal_Reqtype_Config_Mng));
	m_mapScmdReqTypePos.insert(make_pair(REQTYPE_CONFIG_MNG_CDMA, &CSockConfigMngDeal::Deal_Reqtype_Config_Mng_Cdma));
	m_mapScmdReqTypePos.insert(make_pair(REQTYPE_CONFIG_NBCELL, &CSockConfigMngDeal::Deal_Reqtype_Config_NBCell));
	m_mapScmdReqTypePos.insert(make_pair(REQTYPE_CONFIG_TDSCDMA_NBCELL, &CSockConfigMngDeal::Deal_Reqtype_Config_TDScdma_NBCell));
	m_mapScmdReqTypePos.insert(make_pair(REQTYPE_CONFIG_WCDMA_CELL, &CSockConfigMngDeal::Deal_Reqtype_Config_Wcdma_Cell));
	m_mapScmdReqTypePos.insert(make_pair(REQTYPE_CONFIG_WCDMA_NBCELL, &CSockConfigMngDeal::Deal_Reqtype_Config_Wcdma_NBCell));
	m_mapScmdReqTypePos.insert(make_pair(RESTYPE_CONFIG_CULTE_CELL, &CSockConfigMngDeal::Deal_RESTYPE_Config_Culte_Cell));
	m_mapScmdReqTypePos.insert(make_pair(RESTYPE_CONFIG_CTLTE_CELL, &CSockConfigMngDeal::Deal_RESTYPE_Config_Ctlte_Cell));
	m_mapScmdReqTypePos.insert(make_pair(REQTYPE_CONFIG_CELL_TIMESTAMP, &CSockConfigMngDeal::Deal_Reqtype_Config_Cell_TimeStamp));
	m_mapScmdReqTypePos.insert(make_pair(REQTYPE_CONFIG_SERVER_LIST, &CSockConfigMngDeal::Deal_Reqtype_Config_Server_List));
	//ConfigMngSock_Area 
	m_mapScmdReqTypePos.insert(make_pair(REQTYPE_CONFIG_STATIC_AREA, &CSockConfigMngDeal::Deal_Reqtype_Config_Static_Area));
	m_mapScmdReqTypePos.insert(make_pair(REQTYPE_CONFIG_STAT_AREATYPE, &CSockConfigMngDeal::Deal_Reqtype_Config_Stat_AreaType));
	m_mapScmdReqTypePos.insert(make_pair(REQTYPE_CONFIG_STAT_AREASCAN, &CSockConfigMngDeal::Deal_Reqtype_Config_Stat_AreaScan));
	m_mapScmdReqTypePos.insert(make_pair(REQTYPE_CONFIG_STAT_AREA, &CSockConfigMngDeal::Deal_Reqtype_Config_Stat_Area));
	m_mapScmdReqTypePos.insert(make_pair(REQTYPE_GIS_CQT_AREA_LIST, &CSockConfigMngDeal::Deal_Reqtype_Gis_Cqt_Area_List));
	m_mapScmdReqTypePos.insert(make_pair(REQTYPE_GIS_AREA_TYPE, &CSockConfigMngDeal::Deal_Reqtype_Gis_Area_Type));
	m_mapScmdReqTypePos.insert(make_pair(REQTYPE_GIS_AREA_LIST, &CSockConfigMngDeal::Deal_Reqtype_Gis_Area_List));
	m_mapScmdReqTypePos.insert(make_pair(REQTYPE_GIS_AREA_ADD, &CSockConfigMngDeal::Deal_Reqtype_Gis_Area_Add));
	m_mapScmdReqTypePos.insert(make_pair(REQTYPE_GIS_AREA_EDIT, &CSockConfigMngDeal::Deal_Reqtype_Gis_Area_Edit));
	m_mapScmdReqTypePos.insert(make_pair(REQTYPE_GIS_AREA_DELETE, &CSockConfigMngDeal::Deal_Reqtype_Gis_Area_Delete));
	m_mapScmdReqTypePos.insert(make_pair(REQTYPE_GIS_COVER_REGION_ADD, &CSockConfigMngDeal::Deal_Reqtype_Gis_Cover_Region_Add));
	m_mapScmdReqTypePos.insert(make_pair(REQTYPE_GIS_COVER_REGION_DELETE, &CSockConfigMngDeal::Deal_Reqtype_Gis_Cover_Region_Delete));
	m_mapScmdReqTypePos.insert(make_pair(REQTYPE_GIS_COVER_REGION_EDIT, &CSockConfigMngDeal::Deal_Reqtype_Gis_Cover_Region_Edit));
	m_mapScmdReqTypePos.insert(make_pair(REQTYPE_GIS_COVER_REGION_GET, &CSockConfigMngDeal::Deal_Reqtype_Gis_Cover_Region_Get));
	m_mapScmdReqTypePos.insert(make_pair(REQTYPE_CONFIG_VERSION, &CSockConfigMngDeal::Deal_Reqtype_Config_Version));
	//ConfigMngSock_Other 
	m_mapScmdReqTypePos.insert(make_pair(REQTYPE_CONFIG_STATIC_INFO, &CSockConfigMngDeal::Deal_Reqtype_Config_StatIC_Info));
	m_mapScmdReqTypePos.insert(make_pair(REQTYPE_CONFIG_STAT_DISTRICT_COMMUNITY, &CSockConfigMngDeal::Deal_Reqtype_Config_Stat_District_Community));
	m_mapScmdReqTypePos.insert(make_pair(REQTYPE_VILLAGE_INFO, &CSockConfigMngDeal::Deal_Reqtype_Village_Info));
	m_mapScmdReqTypePos.insert(make_pair(REQTYPE_CONFIG_STATIC_CITY, &CSockConfigMngDeal::Deal_Reqtype_Config_Static_City));
	m_mapScmdReqTypePos.insert(make_pair(REQTYPE_RECORD_LOG_ADD, &CSockConfigMngDeal::Deal_Reqtype_Record_Log_Add));
	m_mapScmdReqTypePos.insert(make_pair(REQTYPE_CONFIG_PROJECT_EXTEND, &CSockConfigMngDeal::Deal_Reqtype_Config_Project_Extend));
	m_mapScmdReqTypePos.insert(make_pair(REQTYPE_CONFIG_IMAGECOLUMN, &CSockConfigMngDeal::Deal_Reqtype_Config_ImageColumn));
	m_mapScmdReqTypePos.insert(make_pair(REQTYPE_CONFIG_TABLECOLUMN, &CSockConfigMngDeal::Deal_Reqtype_Config_TableColumn));
	m_mapScmdReqTypePos.insert(make_pair(REQTYPE_CONFIG_KPI_TABLE, &CSockConfigMngDeal::Deal_Reqtype_Config_Kpi_Table));
	m_mapScmdReqTypePos.insert(make_pair(REQTYPE_CONFIG_AREATYPE_SET, &CSockConfigMngDeal::Deal_Reqtype_Config_AreaTYPE_Set));
	m_mapScmdReqTypePos.insert(make_pair(REQTYPE_CQT_IMAGE_INFO_INSERT, &CSockConfigMngDeal::Deal_Reqtype_Cqt_Image_Info_Insert));
	m_mapScmdReqTypePos.insert(make_pair(REQTYPE_CQT_IMAGE_INFO_SEND, &CSockConfigMngDeal::Deal_Reqtype_Cqt_Image_Info_Send));
	m_mapScmdReqTypePos.insert(make_pair(REQTYPE_CQT_PICTURE_INFO_UPDATE, &CSockConfigMngDeal::Deal_Reqtype_Cqt_Picture_Info_Update));

}






