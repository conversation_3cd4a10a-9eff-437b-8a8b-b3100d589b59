// SockConfigMngDeal.h: interface for the CSockConfigMngDeal class.
//
//////////////////////////////////////////////////////////////////////

#ifndef __SOCKCONFIGMNGDEAL_H__
#define __SOCKCONFIGMNGDEAL_H__

#include "./SockFactory.h"
#include "./SearchSock.h"

#include "./ConfigMngSock_Cell.h"
#include "./ConfigMngSock_Area.h"
#include "./ConfigMngSock_Other.h"

class CSockConfigMngDeal : public CSockFactory, public Singleton<CSockConfigMngDeal>
{
public:
	CSockConfigMngDeal();
	virtual ~CSockConfigMngDeal();
	
public:
    virtual BOOL DealSock(const BYTE* const pData, const int nCount,int& ioffset, const STRU_SockInfo& sockInfo);
	
private:
    typedef void (CSockConfigMngDeal::*DealReqTypeData)(const BYTE* const pData, const int nCount, int& ioffset,
                                                        const STRU_SockInfo& sockInfo, std::string strUserInfo);
    std::map<BYTE, DealReqTypeData> m_mapScmdReqTypePos;

    void Deal_Reqtype_Config_Mng(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo);
    void Deal_Reqtype_Config_Mng_Cdma(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo);
    void Deal_Reqtype_Config_NBCell(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo);
    void Deal_Reqtype_Config_TDScdma_NBCell(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo);
    void Deal_Reqtype_Config_Wcdma_Cell(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo);
    void Deal_Reqtype_Config_Wcdma_NBCell(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo);
    void Deal_RESTYPE_Config_Culte_Cell(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo);
    void Deal_RESTYPE_Config_Ctlte_Cell(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo);
    void Deal_Reqtype_Config_Cell_TimeStamp(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo);
    void Deal_Reqtype_Config_Server_List(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo);
    void Deal_Reqtype_Config_Static_Area(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo);
    void Deal_Reqtype_Config_Stat_AreaType(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo);
    void Deal_Reqtype_Config_Stat_AreaScan(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo);
    void Deal_Reqtype_Config_Stat_Area(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo);
    void Deal_Reqtype_Gis_Cqt_Area_List(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo);
    void Deal_Reqtype_Gis_Area_Type(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo);
    void Deal_Reqtype_Gis_Area_List(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo);
    void Deal_Reqtype_Gis_Area_Add(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo);
    void Deal_Reqtype_Gis_Area_Edit(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo);
    void Deal_Reqtype_Gis_Area_Delete(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo);
    void Deal_Reqtype_Gis_Cover_Region_Add(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo);
    void Deal_Reqtype_Gis_Cover_Region_Delete(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo);
    void Deal_Reqtype_Gis_Cover_Region_Edit(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo);
    void Deal_Reqtype_Gis_Cover_Region_Get(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo);
    void Deal_Reqtype_Config_Version(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo);
    void Deal_Reqtype_Config_StatIC_Info(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo);
    void Deal_Reqtype_Config_Stat_District_Community(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo);
    void Deal_Reqtype_Village_Info(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo);
    void Deal_Reqtype_Config_Static_City(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo);
    void Deal_Reqtype_Record_Log_Add(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo);
    void Deal_Reqtype_Config_Project_Extend(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo);
    void Deal_Reqtype_Config_ImageColumn(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo);
    void Deal_Reqtype_Config_TableColumn(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo);
    void Deal_Reqtype_Config_Kpi_Table(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo);
    void Deal_Reqtype_Config_AreaTYPE_Set(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo);
    void Deal_Reqtype_Cqt_Image_Info_Insert(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo);
    void Deal_Reqtype_Cqt_Image_Info_Send(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo);
    void Deal_Reqtype_Cqt_Picture_Info_Update(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo);

	void InitmReqTypeMap(void);
};

#endif
