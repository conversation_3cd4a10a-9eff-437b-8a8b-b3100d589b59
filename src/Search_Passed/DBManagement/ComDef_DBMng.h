// ComDef_DBMng.h: interface for the CComDef_DBMng class.
//
//////////////////////////////////////////////////////////////////////

#ifndef __COMDEF_DBMNG_H__
#define __COMDEF_DBMNG_H__

//db management
#define REQTYPE_DBBACKUP				0x10	//数据库备份请求
#define REQTYPE_DBRECOVER				0x11	//数据库恢复请求
#define REQTYPE_DBRECOVEREND			0x12	//数据库恢复请求结束
#define REQTYPE_DBDELETE				0x13	//数据库清理请求
#define REQTYPE_DBINFOREQUEST			0x14	//数据库信息查询请求

#define RESTYPE_DBBACKUP				0x50	//原始数据备份响应
#define RESTYPE_DBRECOVER				0x51	//数据库恢复响应
#define RESTYPE_DBDELETE				0x52	//数据库清理响应
#define RESTYPE_DBINFOREQUEST			0x53	//数据库信息查询响应



#endif
