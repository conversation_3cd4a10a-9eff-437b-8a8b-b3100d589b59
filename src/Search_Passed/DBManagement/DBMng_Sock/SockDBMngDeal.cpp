// SockDBMngDeal.cpp: implementation of the CSockDBMngDeal class.
//
//////////////////////////////////////////////////////////////////////

#include "./SockDBMngDeal.h"

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CSockDBMngDeal::CSockDBMngDeal()
{

}

CSockDBMngDeal::~CSockDBMngDeal()
{

}

/**
数据库管理命令字
*/
BOOL
CSockDBMngDeal::DealSock(const BYTE* const pData, const int nCount,int& ioffset, const STRU_SockInfo& sockInfo)
{
	return TRUE;
}

