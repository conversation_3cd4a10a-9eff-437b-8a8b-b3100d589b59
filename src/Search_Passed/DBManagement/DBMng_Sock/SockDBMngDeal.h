// SockDBMngDeal.h: interface for the CSockDBMngDeal class.
//
//////////////////////////////////////////////////////////////////////

#ifndef __SOCKDBMNGDEAL_H__
#define __SOCKDBMNGDEAL_H__

#include "./SockFactory.h"


class CSockDBMngDeal : public CSockFactory, public Singleton<CSockDBMngDeal>
{
public:
	CSockDBMngDeal();
	virtual ~CSockDBMngDeal();
public:
    virtual BOOL DealSock(const BYTE* const pData, const int nCount,int& ioffset, const STRU_SockInfo& sockInfo);
	
};

#endif
