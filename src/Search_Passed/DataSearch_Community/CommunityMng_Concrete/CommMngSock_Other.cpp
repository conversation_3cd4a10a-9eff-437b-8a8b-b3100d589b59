// CommMngSock_Other.cpp: implementation of the CCommMngSock_Other class.
//
//////////////////////////////////////////////////////////////////////

#include "CommMngSock_Other.h"

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////


/************************************************************************
CCommMngSock_InfoAdd                                                          
************************************************************************/
BOOL
CCommMngSock_CommunityInfoAdd::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
    tCommunityInfo st_CommunityInfo;
    st_CommunityInfo.Clear();

    CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,st_CommunityInfo.tm_city,sizeof(st_CommunityInfo.tm_city)));
    CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,st_CommunityInfo.tm_county,sizeof(st_CommunityInfo.tm_county)));
    CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,st_CommunityInfo.tm_comname,sizeof(st_CommunityInfo.tm_comname)));
    CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,st_CommunityInfo.tm_comprop,sizeof(st_CommunityInfo.tm_comprop)));

	STRU_SEARCH_AREA tm_Search_Area;
	//longitude and latitude
	PPM_OS::memcpy(&tm_Search_Area.itllongitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Search_Area.itllatitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);

	tm_Search_Area.ConvertToNetSeq();

	char tm_cmcccoverage[255];
	PPM_OS::memset(&tm_cmcccoverage, 0, sizeof(tm_cmcccoverage));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_cmcccoverage,sizeof(tm_cmcccoverage)));

	char tm_districtrange[255];
	PPM_OS::memset(&tm_districtrange, 0, sizeof(tm_districtrange));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_districtrange,sizeof(tm_districtrange)));


	char tm_comscale[255];
	PPM_OS::memset(&tm_comscale, 0, sizeof(tm_comscale));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_comscale,sizeof(tm_comscale)));

	int area;
	PPM_OS::memcpy(&area,pData + ioffset,4);
	area = MAKEINT_NETSEQ1(area);
	MOVENEXT(ioffset,4,nCount);

	int liftnum;
	PPM_OS::memcpy(&liftnum,pData + ioffset,4);
	liftnum = MAKEINT_NETSEQ1(liftnum);
	MOVENEXT(ioffset,4,nCount);

	int garagearea;
	PPM_OS::memcpy(&garagearea,pData + ioffset,4);
	garagearea = MAKEINT_NETSEQ1(garagearea);
	MOVENEXT(ioffset,4,nCount);

	int garagenum;
	PPM_OS::memcpy(&garagenum,pData + ioffset,4);
	garagenum = MAKEINT_NETSEQ1(garagenum);
	MOVENEXT(ioffset,4,nCount);

	int price;
	PPM_OS::memcpy(&price,pData + ioffset,4);
	price = MAKEINT_NETSEQ1(price);
	MOVENEXT(ioffset,4,nCount);
   
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,st_CommunityInfo.tm_contact,sizeof(st_CommunityInfo.tm_contact)));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,st_CommunityInfo.tm_picture,sizeof(st_CommunityInfo.tm_picture)));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,st_CommunityInfo.tm_note,sizeof(st_CommunityInfo.tm_note)));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,st_CommunityInfo.tm_description,sizeof(st_CommunityInfo.tm_description)));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,st_CommunityInfo.tm_advice,sizeof(st_CommunityInfo.tm_advice)));
//
	int topnum ;
	PPM_OS::memcpy(&topnum,pData + ioffset,4);
	topnum = MAKEINT_NETSEQ1(topnum);
	MOVENEXT(ioffset,4,nCount);

	int totalnum ;
	PPM_OS::memcpy(&totalnum,pData + ioffset,4);
	totalnum = MAKEINT_NETSEQ1(totalnum);
	MOVENEXT(ioffset,4,nCount);

	char tm_comlevel [50];
	PPM_OS::memset(&tm_comlevel, 0, sizeof(tm_comlevel));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_comlevel,sizeof(tm_comlevel)));

	int peoplenum;
	PPM_OS::memcpy(&peoplenum,pData + ioffset,4);
	peoplenum = MAKEINT_NETSEQ1(peoplenum);
	MOVENEXT(ioffset,4,nCount);



	char tm_sql[8000];	
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
				"exec sp_community_info_add '%s','%s','%s','%s',%d,%d,'%s','%s','%s',%d,%d,%d,%d,%d,'%s','%s','%s','%s','%s',%d,%d,'%s',%d",
				st_CommunityInfo.tm_city, st_CommunityInfo.tm_county, st_CommunityInfo.tm_comname, st_CommunityInfo.tm_comprop,
				tm_Search_Area.itllongitude, tm_Search_Area.itllatitude,
				&tm_cmcccoverage, &tm_districtrange, &tm_comscale, 
				area, liftnum, garagearea, garagenum, price,
				st_CommunityInfo.tm_contact, st_CommunityInfo.tm_picture, st_CommunityInfo.tm_note, st_CommunityInfo.tm_description, st_CommunityInfo.tm_advice,
				topnum, totalnum, 
				&tm_comlevel, 
				peoplenum
	   );
	
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_COMMUNITY_INFO_ADD;
	
	if(NULL != m_pDataBaseDeal_){
		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
		//处理结束
		FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
	}	
	Clear();

	return TRUE;	
}


/************************************************************************
CCommMngSock_CommunityBuildingAdd                                                          
************************************************************************/
BOOL
CCommMngSock_CommunityBuildingAdd::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	tCommunityBuild st_CommunityBuild;
	st_CommunityBuild.Clear();

    CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,st_CommunityBuild.tm_city,sizeof(st_CommunityBuild.tm_city)));
    CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,st_CommunityBuild.tm_county,sizeof(st_CommunityBuild.tm_county)));   
    CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,st_CommunityBuild.tm_comname,sizeof(st_CommunityBuild.tm_comname)));
    CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,st_CommunityBuild.tm_buildingname,sizeof(st_CommunityBuild.tm_buildingname)));
    CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,st_CommunityBuild.tm_cmccGSM,sizeof(st_CommunityBuild.tm_cmccGSM)));

	int cmccResult;
	PPM_OS::memcpy(&cmccResult,pData + ioffset,4);
	cmccResult = MAKEINT_NETSEQ1(cmccResult);
	MOVENEXT(ioffset,4,nCount);

	char tm_cuComparecmcc[255];
	PPM_OS::memset(&tm_cuComparecmcc, 0, sizeof(tm_cuComparecmcc));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_cuComparecmcc,sizeof(tm_cuComparecmcc)));

	char tm_ctComparecmcc[255];
	PPM_OS::memset(&tm_ctComparecmcc, 0, sizeof(tm_ctComparecmcc));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_ctComparecmcc,sizeof(tm_ctComparecmcc)));
	
	int testtime;
	PPM_OS::memcpy(&testtime,pData + ioffset,4);
	testtime = MAKEINT_NETSEQ1(testtime);
	MOVENEXT(ioffset,4,nCount);

	char tm_description[512];
	PPM_OS::memset(&tm_description, 0, sizeof(tm_description));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_description,sizeof(tm_description)));

	int cmccCoverPercent;
	PPM_OS::memcpy(&cmccCoverPercent,pData + ioffset,4);
	cmccCoverPercent = MAKEINT_NETSEQ1(cmccCoverPercent);
	MOVENEXT(ioffset,4,nCount);

	int cuCoverPercent ;
	PPM_OS::memcpy(&cuCoverPercent ,pData + ioffset,4);
	cuCoverPercent  = MAKEINT_NETSEQ1(cuCoverPercent );
	MOVENEXT(ioffset,4,nCount);

	int ctCoverPercent  ;
	PPM_OS::memcpy(&ctCoverPercent  ,pData + ioffset,4);
	ctCoverPercent  = MAKEINT_NETSEQ1(ctCoverPercent  );
	MOVENEXT(ioffset,4,nCount);

	char tm_sql[8000];	
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec sp_community_building_add  '%s','%s','%s','%s','%s',%d,'%s','%s',%d,'%s',%d,%d,%d",
        st_CommunityBuild.tm_city, st_CommunityBuild.tm_county, st_CommunityBuild.tm_comname, 
        st_CommunityBuild.tm_buildingname, st_CommunityBuild.tm_cmccGSM,
		cmccResult,
		&tm_cuComparecmcc,&tm_ctComparecmcc,
		testtime,
		&tm_description,
        cmccCoverPercent,cuCoverPercent,ctCoverPercent
		);
	
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_COMMUNITY_BUILDING_ADD;
	
	if(NULL != m_pDataBaseDeal_){
		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
	}	
  Clear();

	//处理结束
	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
	return TRUE;	
}

/************************************************************************
CCommMngSock_CommunityTestPointAdd                                                          
************************************************************************/
BOOL
CCommMngSock_CommunityTestPointAdd::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	tCommunityTestPoint st_CommunityTestPoint;
    st_CommunityTestPoint.Clear();

    CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,st_CommunityTestPoint.tm_city,sizeof(st_CommunityTestPoint.tm_city)));
    CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,st_CommunityTestPoint.tm_county,sizeof(st_CommunityTestPoint.tm_county)));
    CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,st_CommunityTestPoint.tm_comname,sizeof(st_CommunityTestPoint.tm_comname)));
    CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,st_CommunityTestPoint.tm_buildingname,sizeof(st_CommunityTestPoint.tm_buildingname)));
    CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,st_CommunityTestPoint.tm_testpointname,sizeof(st_CommunityTestPoint.tm_testpointname)));
    CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,st_CommunityTestPoint.tm_verticalpos,sizeof(st_CommunityTestPoint.tm_verticalpos)));
    CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,st_CommunityTestPoint.tm_posprop,sizeof(st_CommunityTestPoint.tm_posprop)));

	STRU_SEARCH_AREA tm_Search_Area;
	//longitude and latitude
	PPM_OS::memcpy(&tm_Search_Area.itllongitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Search_Area.itllatitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);

	tm_Search_Area.ConvertToNetSeq();

	int cmccRxlev;
	PPM_OS::memcpy(&cmccRxlev,pData + ioffset,4);
	cmccRxlev = MAKEINT_NETSEQ1(cmccRxlev);
	MOVENEXT(ioffset,4,nCount);

	char tm_staticservingincell[255];
	PPM_OS::memset(&tm_staticservingincell, 0, sizeof(tm_staticservingincell));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_staticservingincell,sizeof(tm_staticservingincell)));

	int ta;
	PPM_OS::memcpy(&ta,pData + ioffset,4);
	ta = MAKEINT_NETSEQ1(ta);
	MOVENEXT(ioffset,4,nCount);

	char tm_voicequality[255];
	PPM_OS::memset(&tm_voicequality, 0, sizeof(tm_voicequality));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_voicequality,sizeof(tm_voicequality)));

	char tm_activeservingincell[255];
	PPM_OS::memset(&tm_activeservingincell, 0, sizeof(tm_activeservingincell));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_activeservingincell,sizeof(tm_activeservingincell)));

	int cuRexlev;
	PPM_OS::memcpy(&cuRexlev,pData + ioffset,4);
	cuRexlev = MAKEINT_NETSEQ1(cuRexlev);
	MOVENEXT(ioffset,4,nCount);

	int ctCDMA;
	PPM_OS::memcpy(&ctCDMA,pData + ioffset,4);
	ctCDMA = MAKEINT_NETSEQ1(ctCDMA);
	MOVENEXT(ioffset,4,nCount);

	char tm_testperson[255];
	PPM_OS::memset(&tm_testperson, 0, sizeof(tm_testperson));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_testperson,sizeof(tm_testperson)));

	int testtime;
	PPM_OS::memcpy(&testtime,pData + ioffset,4);
	testtime = MAKEINT_NETSEQ1(testtime);
	MOVENEXT(ioffset,4,nCount);

	char tm_testphonenum[255];
	PPM_OS::memset(&tm_testphonenum, 0, sizeof(tm_testphonenum));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_testphonenum,sizeof(tm_testphonenum)));

	char tm_note[255];
	PPM_OS::memset(&tm_note, 0, sizeof(tm_note));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_note,sizeof(tm_note)));

	char tm_sql[8000];	
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
    "exec sp_community_testpoint_add  '%s','%s','%s','%s','%s','%s','%s',%d,%d,%d,'%s',%d,'%s','%s',%d,%d,'%s',%d,'%s','%s'",
    st_CommunityTestPoint.tm_city, st_CommunityTestPoint.tm_county, st_CommunityTestPoint.tm_comname, st_CommunityTestPoint.tm_buildingname, 
    st_CommunityTestPoint.tm_testpointname, st_CommunityTestPoint.tm_verticalpos, st_CommunityTestPoint.tm_posprop,
		tm_Search_Area.itllongitude, tm_Search_Area.itllatitude, cmccRxlev,
		&tm_staticservingincell,
    ta,
    &tm_voicequality,tm_activeservingincell,
		cuRexlev,ctCDMA,
		&tm_testperson,
		testtime,
		&tm_testphonenum,&tm_note
	);
	
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_COMMUNITY_TESTPOINT_ADD;
	
	if(NULL != m_pDataBaseDeal_){
		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
		//处理结束
		FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
	}
	Clear();
	
	return TRUE;	
}



/************************************************************************
CCommMngSock_CommunityInfo                                           
************************************************************************/
BOOL  
CCommMngSock_CommunityInfo::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
  	char tm_city[255];
	PPM_OS::memset(&tm_city, 0, sizeof(tm_city));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_city,sizeof(tm_city)));

	char tm_county[255];
	PPM_OS::memset(&tm_county, 0, sizeof(tm_county));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_county,sizeof(tm_county)));
	
	char tm_comname[255];
	PPM_OS::memset(&tm_comname, 0, sizeof(tm_comname));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_comname,sizeof(tm_comname)));
	
	char tm_comprop[255];
	PPM_OS::memset(&tm_comprop, 0, sizeof(tm_comprop));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_comprop,sizeof(tm_comprop)));
	
	STRU_SEARCH_AREA tm_Search_Area;
	//longitude start,longitude end,latitude start,latitude end
	PPM_OS::memcpy(&tm_Search_Area.itllongitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Search_Area.ibrlongitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Search_Area.itllatitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Search_Area.ibrlatitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);	
	tm_Search_Area.ConvertToNetSeq();
	
	char tm_cmcccoverage[255];
	PPM_OS::memset(&tm_cmcccoverage, 0, sizeof(tm_cmcccoverage));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_cmcccoverage,sizeof(tm_cmcccoverage)));
	
	char tm_districtrange[255];
	PPM_OS::memset(&tm_districtrange, 0, sizeof(tm_districtrange));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_districtrange,sizeof(tm_districtrange)));
		
	char tm_comscale[255];
	PPM_OS::memset(&tm_comscale, 0, sizeof(tm_comscale));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_comscale,sizeof(tm_comscale)));
	
	int area_start;
	PPM_OS::memcpy(&area_start,pData + ioffset,4);
	area_start = MAKEINT_NETSEQ1(area_start);
	MOVENEXT(ioffset,4,nCount);

	int area_end;
	PPM_OS::memcpy(&area_end,pData + ioffset,4);
	area_end = MAKEINT_NETSEQ1(area_end);
	MOVENEXT(ioffset,4,nCount);
	
	int liftnum_start;
	PPM_OS::memcpy(&liftnum_start,pData + ioffset,4);
	liftnum_start = MAKEINT_NETSEQ1(liftnum_start);
	MOVENEXT(ioffset,4,nCount);

	int liftnum_end;
	PPM_OS::memcpy(&liftnum_end,pData + ioffset,4);
	liftnum_end = MAKEINT_NETSEQ1(liftnum_end);
	MOVENEXT(ioffset,4,nCount);
	
	int garagearea_start;
	PPM_OS::memcpy(&garagearea_start,pData + ioffset,4);
	garagearea_start = MAKEINT_NETSEQ1(garagearea_start);
	MOVENEXT(ioffset,4,nCount);

	int garagearea_end;
	PPM_OS::memcpy(&garagearea_end,pData + ioffset,4);
	garagearea_end = MAKEINT_NETSEQ1(garagearea_end);
	MOVENEXT(ioffset,4,nCount);
	
	int garagenum_start;
	PPM_OS::memcpy(&garagenum_start,pData + ioffset,4);
	garagenum_start = MAKEINT_NETSEQ1(garagenum_start);
	MOVENEXT(ioffset,4,nCount);

	int garagenum_end;
	PPM_OS::memcpy(&garagenum_end,pData + ioffset,4);
	garagenum_end = MAKEINT_NETSEQ1(garagenum_end);
	MOVENEXT(ioffset,4,nCount);
	
	int price_start;
	PPM_OS::memcpy(&price_start,pData + ioffset,4);
	price_start = MAKEINT_NETSEQ1(price_start);
	MOVENEXT(ioffset,4,nCount);

	int price_end;
	PPM_OS::memcpy(&price_end,pData + ioffset,4);
	price_end = MAKEINT_NETSEQ1(price_end);
	MOVENEXT(ioffset,4,nCount);
	
	char tm_contact[255];
	PPM_OS::memset(&tm_contact, 0, sizeof(tm_contact));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_contact,sizeof(tm_contact)));
	
	char tm_picture[255];
	PPM_OS::memset(&tm_picture, 0, sizeof(tm_picture));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_picture,sizeof(tm_picture)));
	
	char tm_note[512];
	PPM_OS::memset(&tm_note, 0, sizeof(tm_note));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_note,sizeof(tm_note)));
	
	char tm_description[512];
	PPM_OS::memset(&tm_description, 0, sizeof(tm_description));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_description,sizeof(tm_description)));
	
	char tm_advice[512];
	PPM_OS::memset(&tm_advice, 0, sizeof(tm_advice));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_advice,sizeof(tm_advice)));
	
	int topnum_start;
	PPM_OS::memcpy(&topnum_start,pData + ioffset,4);
	topnum_start = MAKEINT_NETSEQ1(topnum_start);
	MOVENEXT(ioffset,4,nCount);

	int topnum_end;
	PPM_OS::memcpy(&topnum_end,pData + ioffset,4);
	topnum_end = MAKEINT_NETSEQ1(topnum_end);
	MOVENEXT(ioffset,4,nCount);

	int totalnum_start;
	PPM_OS::memcpy(&totalnum_start,pData + ioffset,4);
	totalnum_start = MAKEINT_NETSEQ1(totalnum_start);
	MOVENEXT(ioffset,4,nCount);

	int totalnum_end;
	PPM_OS::memcpy(&totalnum_end,pData + ioffset,4);
	totalnum_end = MAKEINT_NETSEQ1(totalnum_end);
	MOVENEXT(ioffset,4,nCount);

	char tm_comlevel[50];
	PPM_OS::memset(&tm_comlevel, 0, sizeof(tm_comlevel));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_comlevel,sizeof(tm_comlevel)));

	int peoplenum_start;
	PPM_OS::memcpy(&peoplenum_start,pData + ioffset,4);
	peoplenum_start = MAKEINT_NETSEQ1(peoplenum_start);
	MOVENEXT(ioffset,4,nCount);
	
	int peoplenum_end;
	PPM_OS::memcpy(&peoplenum_end,pData + ioffset,4);
	peoplenum_end = MAKEINT_NETSEQ1(peoplenum_end);
	MOVENEXT(ioffset,4,nCount);

	
	char tm_sql[8000];	
	PPM_OS::memset(tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
    "exec sp_community_info_get  '%s','%s','%s','%s',%d,%d,%d,%d,'%s','%s','%s',%d,%d,%d,%d,%d,%d,%d,%d,%d,%d,'%s','%s','%s','%s','%s',%d,%d,%d,%d,'%s',%d,%d",
	 	&tm_city, &tm_county, &tm_comname, &tm_comprop,
	 	tm_Search_Area.itllongitude,tm_Search_Area.ibrlongitude, tm_Search_Area.itllatitude,tm_Search_Area.ibrlatitude,
	 	&tm_cmcccoverage, &tm_districtrange, &tm_comscale, 
    area_start,area_end, liftnum_start,liftnum_end, garagearea_start,garagearea_end, garagenum_start,garagenum_end, price_start,price_end,
	 	&tm_contact, &tm_picture, &tm_note, &tm_description, &tm_advice,
    topnum_start,topnum_end,totalnum_start,totalnum_end,
	 	&tm_comlevel,
    peoplenum_start,peoplenum_end
	);
	
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_COMMUNITY_INFO;
	
	if(NULL != m_pDataBaseDeal_){
		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
		//处理结束
		FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
	}
	Clear();
	
	return TRUE;	
}


/************************************************************************
CCommMngSock_CommunityBuilding                                                          
************************************************************************/
BOOL  
CCommMngSock_CommunityBuilding::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	char tm_city[255];
	PPM_OS::memset(&tm_city, 0, sizeof(tm_city));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_city,sizeof(tm_city)));
	
	char tm_county[255];
	PPM_OS::memset(&tm_county, 0, sizeof(tm_county));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_county,sizeof(tm_county)));   
	
	char tm_comname[255];
	PPM_OS::memset(&tm_comname, 0, sizeof(tm_comname));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_comname,sizeof(tm_comname)));
	
	char tm_buildingname[255];
	PPM_OS::memset(&tm_buildingname, 0, sizeof(tm_buildingname));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_buildingname,sizeof(tm_buildingname)));
	
	char tm_cmccGSM[255];
	PPM_OS::memset(&tm_cmccGSM, 0, sizeof(tm_cmccGSM));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_cmccGSM,sizeof(tm_cmccGSM)));
	
	int cmccResult_start;
	PPM_OS::memcpy(&cmccResult_start,pData + ioffset,4);
	cmccResult_start = MAKEINT_NETSEQ1(cmccResult_start);
	MOVENEXT(ioffset,4,nCount);

	int cmccResult_end;
	PPM_OS::memcpy(&cmccResult_end,pData + ioffset,4);
	cmccResult_end = MAKEINT_NETSEQ1(cmccResult_end);
	MOVENEXT(ioffset,4,nCount);

	char tm_cuComparecmcc[255];
	PPM_OS::memset(&tm_cuComparecmcc, 0, sizeof(tm_cuComparecmcc));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_cuComparecmcc,sizeof(tm_cuComparecmcc)));
	
	char tm_ctComparecmcc[255];
	PPM_OS::memset(&tm_ctComparecmcc, 0, sizeof(tm_ctComparecmcc));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_ctComparecmcc,sizeof(tm_ctComparecmcc)));
	
	int testtime_start;
	PPM_OS::memcpy(&testtime_start,pData + ioffset,4);
	testtime_start = MAKEINT_NETSEQ1(testtime_start);
	MOVENEXT(ioffset,4,nCount);

	int testtime_end;
	PPM_OS::memcpy(&testtime_end,pData + ioffset,4);
	testtime_end = MAKEINT_NETSEQ1(testtime_end);
	MOVENEXT(ioffset,4,nCount);

	char tm_description[512];
	PPM_OS::memset(&tm_description, 0, sizeof(tm_description));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_description,sizeof(tm_description)));

	int cmcccoverpercent_start;
	PPM_OS::memcpy(&cmcccoverpercent_start,pData + ioffset,4);
	cmcccoverpercent_start = MAKEINT_NETSEQ1(cmcccoverpercent_start);
	MOVENEXT(ioffset,4,nCount)
	
	int cmcccoverpercent_end;
	PPM_OS::memcpy(&cmcccoverpercent_end,pData + ioffset,4);
	cmcccoverpercent_end = MAKEINT_NETSEQ1(cmcccoverpercent_end);
	MOVENEXT(ioffset,4,nCount)

	int cucoverpercent_start;
	PPM_OS::memcpy(&cucoverpercent_start,pData + ioffset,4);
	cucoverpercent_start = MAKEINT_NETSEQ1(cucoverpercent_start);
	MOVENEXT(ioffset,4,nCount)
	
	int cucoverpercent_end;
	PPM_OS::memcpy(&cucoverpercent_end,pData + ioffset,4);
	cucoverpercent_end = MAKEINT_NETSEQ1(cucoverpercent_end);
	MOVENEXT(ioffset,4,nCount)

	int ctcoverpercent_start;
	PPM_OS::memcpy(&ctcoverpercent_start,pData + ioffset,4);
	ctcoverpercent_start = MAKEINT_NETSEQ1(ctcoverpercent_start);
	MOVENEXT(ioffset,4,nCount);

	int ctcoverpercent_end;
	PPM_OS::memcpy(&ctcoverpercent_end,pData + ioffset,4);
	ctcoverpercent_end = MAKEINT_NETSEQ1(ctcoverpercent_end);
	MOVENEXT(ioffset,4,nCount);	


	
	char tm_sql[8000];	
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
	"exec sp_community_building_get  '%s','%s','%s','%s','%s',%d,%d,'%s','%s',%d,%d,'%s',%d,%d,%d,%d,%d,%d",
	 &tm_city, &tm_county, &tm_comname, &tm_buildingname, &tm_cmccGSM,
	 cmccResult_start,cmccResult_end,
	 &tm_cuComparecmcc, &tm_ctComparecmcc,
	 testtime_start,testtime_end,
	 &tm_description,
     cmcccoverpercent_start,cmcccoverpercent_end,cucoverpercent_start,cucoverpercent_end,ctcoverpercent_start,ctcoverpercent_end
	 );
	
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_COMMUNITY_BUILDING;
	
	if(NULL != m_pDataBaseDeal_){
		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
		//处理结束
		FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
	}	
	Clear();

	return TRUE;
}


/************************************************************************
CCommMngSock_CommunityTestPoint                                                          
************************************************************************/
BOOL  
CCommMngSock_CommunityTestPoint::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
    char tm_city[255];
	PPM_OS::memset(&tm_city, 0, sizeof(tm_city));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_city,sizeof(tm_city)));
	
	char tm_county[255];
	PPM_OS::memset(&tm_county, 0, sizeof(tm_county));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_county,sizeof(tm_county)));   

	char tm_comname[255];
	PPM_OS::memset(&tm_comname, 0, sizeof(tm_comname));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_comname,sizeof(tm_comname)));

	char tm_buildingname[255];
	PPM_OS::memset(&tm_buildingname, 0, sizeof(tm_buildingname));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_buildingname,sizeof(tm_buildingname)));

	char tm_testpointname[255];
	PPM_OS::memset(&tm_testpointname, 0, sizeof(tm_testpointname));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_testpointname,sizeof(tm_testpointname)));

	char tm_verticalpos[255];
	PPM_OS::memset(&tm_verticalpos, 0, sizeof(tm_verticalpos));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_verticalpos,sizeof(tm_verticalpos)));

	char tm_posprop[255];
	PPM_OS::memset(&tm_posprop, 0, sizeof(tm_posprop));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_posprop,sizeof(tm_posprop)));
//
	STRU_SEARCH_AREA tm_Search_Area;
	//longitude start,longitude end,latitude start,latitude end
	PPM_OS::memcpy(&tm_Search_Area.itllongitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Search_Area.ibrlongitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Search_Area.itllatitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Search_Area.ibrlatitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);	
	tm_Search_Area.ConvertToNetSeq();

	int cmccRxlev_start;
	PPM_OS::memcpy(&cmccRxlev_start,pData + ioffset,4);
	cmccRxlev_start = MAKEINT_NETSEQ1(cmccRxlev_start);
	MOVENEXT(ioffset,4,nCount);

	int cmccRxlev_end;
	PPM_OS::memcpy(&cmccRxlev_end,pData + ioffset,4);
	cmccRxlev_end = MAKEINT_NETSEQ1(cmccRxlev_end);
	MOVENEXT(ioffset,4,nCount);

	char tm_staticservingincelllac[255];
	PPM_OS::memset(&tm_staticservingincelllac, 0, sizeof(tm_staticservingincelllac));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_staticservingincelllac,sizeof(tm_staticservingincelllac)));

	char tm_staticservingincellcid[255];
	PPM_OS::memset(&tm_staticservingincellcid, 0, sizeof(tm_staticservingincellcid));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_staticservingincellcid,sizeof(tm_staticservingincellcid)));

	int ta_start;
	PPM_OS::memcpy(&ta_start,pData + ioffset,4);
	ta_start = MAKEINT_NETSEQ1(ta_start);
	MOVENEXT(ioffset,4,nCount);

	int ta_end;
	PPM_OS::memcpy(&ta_end,pData + ioffset,4);
	ta_end = MAKEINT_NETSEQ1(ta_end);
	MOVENEXT(ioffset,4,nCount);

	char tm_voicequality[255];
	PPM_OS::memset(&tm_voicequality, 0, sizeof(tm_voicequality));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_voicequality,sizeof(tm_voicequality)));

	char tm_activeservingincelllac[255];
	PPM_OS::memset(&tm_activeservingincelllac, 0, sizeof(tm_activeservingincelllac));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_activeservingincelllac,sizeof(tm_activeservingincelllac)));

	char tm_activeservingincellcid[255];
	PPM_OS::memset(&tm_activeservingincellcid, 0, sizeof(tm_activeservingincellcid));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_activeservingincellcid,sizeof(tm_activeservingincellcid)));

	int cuRexlev_start;
	PPM_OS::memcpy(&cuRexlev_start,pData + ioffset,4);
	cuRexlev_start = MAKEINT_NETSEQ1(cuRexlev_start);
	MOVENEXT(ioffset,4,nCount);

	int cuRexlev_end;
	PPM_OS::memcpy(&cuRexlev_end,pData + ioffset,4);
	cuRexlev_end = MAKEINT_NETSEQ1(cuRexlev_end);
	MOVENEXT(ioffset,4,nCount);

	int ctCDMA_start;
	PPM_OS::memcpy(&ctCDMA_start,pData + ioffset,4);
	ctCDMA_start = MAKEINT_NETSEQ1(ctCDMA_start);
	MOVENEXT(ioffset,4,nCount);

	int ctCDMA_end;
	PPM_OS::memcpy(&ctCDMA_end,pData + ioffset,4);
	ctCDMA_end = MAKEINT_NETSEQ1(ctCDMA_end);
	MOVENEXT(ioffset,4,nCount);

	char tm_testperson[255];
	PPM_OS::memset(&tm_testperson, 0, sizeof(tm_testperson));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_testperson,sizeof(tm_testperson)));

	int testtime_start;
	PPM_OS::memcpy(&testtime_start,pData + ioffset,4);
	testtime_start = MAKEINT_NETSEQ1(testtime_start);
	MOVENEXT(ioffset,4,nCount);

	int testtime_end;
	PPM_OS::memcpy(&testtime_end,pData + ioffset,4);
	testtime_end = MAKEINT_NETSEQ1(testtime_end);
	MOVENEXT(ioffset,4,nCount);

	char tm_testphonenum[255];
	PPM_OS::memset(&tm_testphonenum, 0, sizeof(tm_testphonenum));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_testphonenum,sizeof(tm_testphonenum)));

	char tm_note[255];
	PPM_OS::memset(&tm_note, 0, sizeof(tm_note));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_note,sizeof(tm_note)));

	char tm_sql[8000];	
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
    "exec sp_community_testpoint_get  '%s','%s','%s','%s','%s','%s','%s',%d,%d,%d,%d,%d,%d,'%s','%s',%d,%d,'%s','%s','%s',%d,%d,%d,%d,'%s',%d,%d,'%s','%s'",
    &tm_city, &tm_county, &tm_comname, &tm_buildingname, &tm_testpointname, &tm_verticalpos, &tm_posprop,
	tm_Search_Area.itllongitude, tm_Search_Area.ibrlongitude, tm_Search_Area.itllatitude,tm_Search_Area.ibrlatitude,cmccRxlev_start,cmccRxlev_end,
	&tm_staticservingincelllac,&tm_staticservingincellcid,
    ta_start,ta_end,
    &tm_voicequality,&tm_activeservingincelllac,&tm_activeservingincellcid,
	cuRexlev_start,cuRexlev_end,ctCDMA_start,ctCDMA_end,
	&tm_testperson,
	testtime_start,testtime_end,
	&tm_testphonenum,&tm_note
	);
	
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_COMMUNITY_TESTPOINT;
	
	if(NULL != m_pDataBaseDeal_){
		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
		//处理结束
		FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
	}
	Clear();
	
	return TRUE;
}

/************************************************************************
CCommMngSock_CommunityBuildingPos                                                          
************************************************************************/
BOOL
CCommMngSock_CommunityBuildingPos::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	int buildingid;
	PPM_OS::memcpy(&buildingid,pData + ioffset,4);
	buildingid = MAKEINT_NETSEQ1(buildingid);
	MOVENEXT(ioffset,4,nCount);
	
   	char tm_sql[512];	
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec sp_community_building_pos %d",
		buildingid);
	
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_COMMUNITY_BUINDING_POS;
	
	if(NULL != m_pDataBaseDeal_){
		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
		//处理结束
		FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
	}
	Clear();
	
	return TRUE;	 
	
}



