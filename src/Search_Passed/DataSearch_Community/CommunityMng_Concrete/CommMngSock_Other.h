// CommMngSock_Other.h: interface for the CCommMngSock_Other class.
//
//////////////////////////////////////////////////////////////////////

#ifndef __COMMMNGSOCK_OTHER_H__
#define __COMMMNGSOCK_OTHER_H__

#include "./SearchSock.h"
   
class CCommMngSock_CommunityInfoAdd : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);

private:
	typedef struct Stru_CommunityInfo
	{
		void Clear(void)
		{
      memset(tm_city, 0, sizeof(tm_city));
			memset(tm_county, 0, sizeof(tm_county));
			memset(tm_comname, 0, sizeof(tm_comname));
			memset(tm_comprop, 0, sizeof(tm_comprop));
			memset(tm_contact, 0, sizeof(tm_contact));
			memset(tm_picture, 0, sizeof(tm_picture));
			memset(tm_note, 0, sizeof(tm_note));
			memset(tm_description, 0, sizeof(tm_description));
			memset(tm_advice, 0, sizeof(tm_advice));
		};

		char tm_city[255];
		char tm_county[255];
		char tm_comname[255];
		char tm_comprop[255];
		char tm_contact[255];
		char tm_picture[255];
		char tm_note[512];
		char tm_description[512];
		char tm_advice[512];
	}tCommunityInfo;
};


class CCommMngSock_CommunityBuildingAdd : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);

private:
	typedef struct Stru_CommunityBuild
	{
		void Clear(void)
		{
			memset(tm_city, 0, sizeof(tm_city));
			memset(tm_county, 0, sizeof(tm_county));
			memset(tm_comname, 0, sizeof(tm_comname));
			memset(tm_buildingname, 0, sizeof(tm_buildingname));
			memset(tm_cmccGSM, 0, sizeof(tm_cmccGSM));
		};
		char tm_city[255];
		char tm_county[255];
		char tm_comname[255];
		char tm_buildingname[255];
		char tm_cmccGSM[255];
	}tCommunityBuild;
};


class CCommMngSock_CommunityTestPointAdd : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);

private:
	typedef struct Stru_CommunityTestPoint
	{
		void Clear(void)
		{
			memset(tm_city, 0, sizeof(tm_city));
			memset(tm_county, 0, sizeof(tm_county));
			memset(tm_comname, 0, sizeof(tm_comname));
			memset(tm_buildingname, 0, sizeof(tm_buildingname));
			memset(tm_testpointname, 0, sizeof(tm_testpointname));
			memset(tm_verticalpos, 0, sizeof(tm_verticalpos));
			memset(tm_posprop, 0, sizeof(tm_posprop));
		};

		char tm_city[255];
		char tm_county[255];
		char tm_comname[255];
		char tm_buildingname[255];
		char tm_testpointname[255];
		char tm_verticalpos[255];
		char tm_posprop[255];
	}tCommunityTestPoint;
};


class CCommMngSock_CommunityInfo : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};


class CCommMngSock_CommunityBuilding : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};


class CCommMngSock_CommunityTestPoint : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};


class CCommMngSock_CommunityBuildingPos : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};

#endif
