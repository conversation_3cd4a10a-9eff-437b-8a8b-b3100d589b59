// ComDef_Community.h: interface for the CComDef_Community class.
//
//////////////////////////////////////////////////////////////////////

#ifndef __COMDEF_COMMUNITY_H__
#define __COMDEF_COMMUNITY_H__


//COMMUNITY command
#define  REQTYPE_COMMUNITY_INFO_ADD				0x01   //REQUEST
#define  RESTYPE_COMMUNITY_INFO_ADD				0x01 
#define  REQTYPE_COMMUNITY_BUILDING_ADD			0x02   //REQUEST
#define  RESTYPE_COMMUNITY_BUILDING_ADD			0x02 
#define  REQTYPE_COMMUNITY_TESTPOINT_ADD		0x03   //REQUEST
#define  RESTYPE_COMMUNITY_TESTPOINT_ADD		0x03 
#define  REQTYPE_COMMUNITY_INFO                 0x04   //REQUEST
#define  RESTYPE_COMMUNITY_INFO                 0x04
#define  REQTYPE_COMMUNITY_BUILDING             0x05   //REQUEST
#define  RESTYPE_COMMUNITY_BUILDING             0x05
#define  REQTYPE_COMMUNITY_TESTPOINT            0x06   //REQUEST
#define  RESTYPE_COMMUNITY_TESTPOINT            0x06
#define  REQTYPE_COMMUNITY_BUINDING_POS         0X07   //REQUEST
#define  RESTYPE_COMMUNITY_BUINDING_POS         0X07





#endif
