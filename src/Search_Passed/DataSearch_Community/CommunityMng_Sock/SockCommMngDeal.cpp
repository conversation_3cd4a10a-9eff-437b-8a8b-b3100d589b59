// SockCommMngDeal.cpp: implementation of the CSockCommMngDeal class.
//
//////////////////////////////////////////////////////////////////////

#include "./SockCommMngDeal.h"


//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CSockCommMngDeal::CSockCommMngDeal()
{

}

CSockCommMngDeal::~CSockCommMngDeal()
{

}

BOOL
CSockCommMngDeal::DealSock(const BYTE* const pData, const int nCount,int& ioffset, const STRU_SockInfo& sockInfo)
{
	char tm_addr[512];
	sockInfo.pServerDeal_->m_ptcpclient_->GetInetAddr()->addr_to_string(tm_addr, sizeof(tm_addr), 1);
	std::string strUserInfo = "[" + std::string(tm_addr) + "]";
	
	if(CMD2_REQUEST == pData[ioffset])//请求
	{
		MOVENEXT(ioffset,1,nCount)
		switch(pData[ioffset++])
		{
		/************************************************************************/
		/* CommMngSock_Other                                                    */
		/************************************************************************/
		case REQTYPE_COMMUNITY_INFO_ADD:
			{
			   DEALDATA2(CCommMngSock_CommunityInfoAdd, strUserInfo);
			}
			break;

		case REQTYPE_COMMUNITY_BUILDING_ADD:
			{
			   DEALDATA2(CCommMngSock_CommunityBuildingAdd, strUserInfo);
			}
			break;

		case REQTYPE_COMMUNITY_TESTPOINT_ADD:
			{
				DEALDATA2(CCommMngSock_CommunityTestPointAdd, strUserInfo);
			}
			break;

		case REQTYPE_COMMUNITY_INFO:
			{
				DEALDATA2(CCommMngSock_CommunityInfo, strUserInfo);
			}
			break;

		case REQTYPE_COMMUNITY_BUILDING:
			{
				DEALDATA2(CCommMngSock_CommunityBuilding, strUserInfo);
			}
			break;

		case REQTYPE_COMMUNITY_TESTPOINT:
			{
				DEALDATA2(CCommMngSock_CommunityTestPoint, strUserInfo);
			}
			break;

		case REQTYPE_COMMUNITY_BUINDING_POS:
			{
				DEALDATA2(CCommMngSock_CommunityBuildingPos, strUserInfo);
			}
			break;

		default:
			{
				PPM_DEBUG((LM_ERROR,"unknown SockCommMngDeal request type:%d\n",pData[ioffset - 1]));
				return FALSE;
			}
			break;
			
		}
	}
	else //响应 
	{
		PPM_DEBUG((LM_ERROR,"should not be SockCommMngDeal data management response:%d\n",pData[ioffset+1]));
		return FALSE;
	}

	return TRUE;
}





