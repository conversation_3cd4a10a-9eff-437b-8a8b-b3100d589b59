// SockCommMngDeal.h: interface for the CSockCommMngDeal class.
//
//////////////////////////////////////////////////////////////////////

#ifndef __SOCKCOMMMNGDEAL_H__
#define __SOCKCOMMMNGDEAL_H__

#include "./SockFactory.h"

#include "./CommMngSock_Other.h"


class CSockCommMngDeal : public CSockFactory, public Singleton<CSockCommMngDeal>
{
public:
	CSockCommMngDeal();
	virtual ~CSockCommMngDeal();

	
public:
    virtual BOOL DealSock(const BYTE* const pData, const int nCount,int& ioffset, const STRU_SockInfo& sockInfo);
	
};

#endif
