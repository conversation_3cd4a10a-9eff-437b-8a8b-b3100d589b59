// SearchSock_AreaCover.cpp: implementation of the CSearchSock_AreaCover class.
//
//////////////////////////////////////////////////////////////////////

#include "SearchSock_Area.h"

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

/************************************************************************
CSearchSock_Area
************************************************************************/
CSearchSock_AreaCover::CSearchSock_AreaCover()
{
	for(int i = 0; i < 256; i++)
	{
		pfunc_TestTypeData[i] = &CSearchSock_AreaCover::DealTestType_Default;
		pfunc_ServiceTypeGsm[i] = &CSearchSock_AreaCover::DealServiceType_Gsm_Default;
		pfunc_ServiceTypeCdma[i] = &CSearchSock_AreaCover::DealServiceType_Cdma_Default;
		pfunc_ServiceTypeCdma2000[i] = &CSearchSock_AreaCover::DealServiceType_Cdma2000_Default;
		pfunc_ServiceTypeTdscdma[i] = &CSearchSock_AreaCover::DealServiceType_Tdscdma_Default;
		pfunc_ServiceTypeWcdma[i] = &CSearchSock_AreaCover::DealServiceType_Wcdma_Default;
	}

	//Test Type Deal
	pfunc_TestTypeData[CEnumDef::T_GSM_DT] = &CSearchSock_AreaCover::DealTestType_Gsm;
	pfunc_TestTypeData[CEnumDef::T_GSM_CQT] = &CSearchSock_AreaCover::DealTestType_Gsm;
	pfunc_TestTypeData[CEnumDef::T_AUTODT] = &CSearchSock_AreaCover::DealTestType_Gsm;
	pfunc_TestTypeData[CEnumDef::T_AUTODT_TA] = &CSearchSock_AreaCover::DealTestType_Gsm;
	pfunc_TestTypeData[CEnumDef::T_AUTOCQT] = &CSearchSock_AreaCover::DealTestType_Gsm;
	pfunc_TestTypeData[CEnumDef::T_SCANTEST] = &CSearchSock_AreaCover::DealTestType_Scan;
	pfunc_TestTypeData[CEnumDef::T_CDMA_DT] = &CSearchSock_AreaCover::DealTestType_Cdma;
	pfunc_TestTypeData[CEnumDef::T_CDMA_CQT] = &CSearchSock_AreaCover::DealTestType_Cdma;
	pfunc_TestTypeData[CEnumDef::T_CDMA2000_DT] = &CSearchSock_AreaCover::DealTestType_Cdma2000;
	pfunc_TestTypeData[CEnumDef::T_TDSCDMA_DT] = &CSearchSock_AreaCover::DealTestType_Tdscdma;
	pfunc_TestTypeData[CEnumDef::T_TDSCDMA_CQT] = &CSearchSock_AreaCover::DealTestType_Tdscdma;
	pfunc_TestTypeData[CEnumDef::T_WCDMA_DT] = &CSearchSock_AreaCover::DealTestType_Wcdma;
	pfunc_TestTypeData[CEnumDef::T_WCDMA_CQT] = &CSearchSock_AreaCover::DealTestType_Wcdma;

    //Servioce Type Deal
	pfunc_ServiceTypeGsm[CEnumDef::Ser_GSM_VOICE] = &CSearchSock_AreaCover::DealServiceType_Gsm_Voice;
	pfunc_ServiceTypeGsm[CEnumDef::Ser_GPRS_DATA] = &CSearchSock_AreaCover::DealServiceType_Gsm_Data;
	pfunc_ServiceTypeGsm[CEnumDef::Ser_EDGE_DATA] = &CSearchSock_AreaCover::DealServiceType_Gsm_Data;
	pfunc_ServiceTypeGsm[CEnumDef::Ser_GSM_UPLINK] = &CSearchSock_AreaCover::DealServiceType_Gsm_Uplink;

  pfunc_ServiceTypeCdma[CEnumDef::Ser_CDMA_VOICE] = &CSearchSock_AreaCover::DealServiceType_Cdma_Voice;
  pfunc_ServiceTypeCdma[CEnumDef::Ser_CDMA1X_DATA] = &CSearchSock_AreaCover::DealServiceType_Cdma_Data;

  pfunc_ServiceTypeCdma2000[CEnumDef::Ser_CDMA2000_DATA] = &CSearchSock_AreaCover::DealServiceType_Cdma2000_Data;

  pfunc_ServiceTypeTdscdma[CEnumDef::Ser_TDSCDMA_VOICE] = &CSearchSock_AreaCover::DealServiceType_Tdscdma_Voice;
	pfunc_ServiceTypeTdscdma[CEnumDef::Ser_TDSCDMA_VIDEO] = &CSearchSock_AreaCover::DealServiceType_Tdscdma_Voice;
	pfunc_ServiceTypeTdscdma[CEnumDef::Ser_TDSCDMA_IDLE] = &CSearchSock_AreaCover::DealServiceType_Tdscdma_Voice;
  pfunc_ServiceTypeTdscdma[CEnumDef::Ser_TDSCDMA_DATA] = &CSearchSock_AreaCover::DealServiceType_Tdscdma_Data;
	pfunc_ServiceTypeTdscdma[CEnumDef::Ser_TDSCDMA_HSDPA] = &CSearchSock_AreaCover::DealServiceType_Tdscdma_Data;

	pfunc_ServiceTypeWcdma[CEnumDef::Ser_WCDMA_VOICE] = &CSearchSock_AreaCover::DealServiceType_Wcdma_Voice;
	pfunc_ServiceTypeWcdma[CEnumDef::Ser_WCDMA_VIDEO] = &CSearchSock_AreaCover::DealServiceType_Wcdma_Voice;
	pfunc_ServiceTypeWcdma[CEnumDef::Ser_WCDMA_DATA] = &CSearchSock_AreaCover::DealServiceType_Wcdma_Data;
	pfunc_ServiceTypeWcdma[CEnumDef::Ser_WCDMA_HSDPA] = &CSearchSock_AreaCover::DealServiceType_Wcdma_Data;

}

CSearchSock_AreaCover::~CSearchSock_AreaCover()
{

}
/************************************************************************
区域查询条件
＋
1)	覆盖查询类型（1BYTE）：0:概要，1:详细，2:基于栅格指标查询
************************************************************************/
BOOL  
CSearchSock_AreaCover::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{   
	MOVENEXTTEST(ioffset,17,nCount);	
	//time
	if (!GetTimeSpan(pData, nCount, ioffset))
	{
		return FALSE;
	}

	if (!GetBaseFileList2(pData, nCount, ioffset))
	{
		return FALSE;
	}

	if(0 >= m_map_filelist2.size())	//no data
	{
		//处理结束
		FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
		return TRUE;
	}

	char tm_file[8000];
	int bOnce;
	int cutnum = 600;         //ado对提交的sql语句限制为8000字节以内
	auto p_map = m_map_filelist2.begin();   

	for (; p_map != m_map_filelist2.end(); p_map++)   
	{

		auto p_vec = p_map->second.begin();	
		PPM_OS::memset(&tm_file, 0, sizeof(tm_file));	
		bOnce = 0;	
		size_t allcount=1;	

		for (int count=1; p_vec != p_map->second.end(); p_vec++, ++count, ++allcount)   
		{   
			if(0 == bOnce)
			{
				PPM_OS::snprintf(tm_file,sizeof(tm_file),"%d",
					*p_vec); 
				bOnce = 1;
			}
			else
			{
				PPM_OS::snprintf(tm_file,sizeof(tm_file),"%s,%d",tm_file,*p_vec); 
			}	
			if(cutnum == count || allcount == p_map->second.size())
			{ 
				//获取头文件信息
				SearchLogFileSInfo(p_map->first.strlog.c_str(), p_map->first.testtype, tm_file, 0);

                (this->*pfunc_TestTypeData[p_map->first.testtype])(p_map->first.servicetype, p_map->first.strtb, tm_file);

				PPM_OS::memset(tm_file, 0, sizeof(tm_file));
				count = 0;
				bOnce = 0;
			} 
		}
	}


	//处理结束
	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);

	return TRUE;

}

BOOL CSearchSock_AreaCover::GetTimeSpan(const BYTE* const pData, const int nCount,int& ioffset)
{
	PPM_OS::memcpy(&tm_Time_Span.istime,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Time_Span.ietime,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	tm_Time_Span.ConvertToNetSeq();	

	return TRUE;
}

BOOL CSearchSock_AreaCover::GetBaseFileList2(const BYTE* const pData, const int nCount,int& ioffset)
{
	//type
	char tm_sql_part[1024];
	PPM_OS::memset(&tm_sql_part, 0, sizeof(tm_sql_part));
	if(FALSE == CSearch_PI::GetTypeSql(pData, nCount, ioffset, tm_sql_part)){
		return FALSE;
	}

	//longitude and latitude
	if (!GetAreaCoverSearchAreaInfo(pData, nCount, ioffset))
	{
		return FALSE;
	}

	BYTE btemp = 0xff;
	PPM_OS::memcpy(&btemp, pData + ioffset, 1);
	m_Itemp = btemp & 0x000000ff;

	char tm_sql[9000];
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec mc_sp_area_cover_get_1 %d,%d, %d,%d,%d,%d, '%s',%d" ,
		tm_Time_Span.istime, 
		tm_Time_Span.ietime,
		tm_Search_Area.itllongitude,     
		tm_Search_Area.itllatitude,
		tm_Search_Area.ibrlongitude,
		tm_Search_Area.ibrlatitude,
		tm_sql_part,
		m_Itemp);

	m_map_filelist2.clear();

	m_iRowType_ = TYPE_VEC_FILE_LIST_SAMPLE2;             //文件id获取 +　采样点数目获取
    SEARCHSQL(m_iRowType_, tm_sql);

	return TRUE;
}

BOOL CSearchSock_AreaCover::GetAreaCoverSearchAreaInfo(const BYTE* const pData, const int nCount,int& ioffset)
{
	PPM_OS::memcpy(&tm_Search_Area.itllongitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Search_Area.itllatitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Search_Area.ibrlongitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Search_Area.ibrlatitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);

	tm_Search_Area.ConvertToNetSeq();	
    return TRUE;
}

void CSearchSock_AreaCover::DealTestType_Default(int servicetype, std::string strtb, char* tm_file)
{

}

void CSearchSock_AreaCover::DealTestType_Gsm(int servicetype, std::string strtb, char* tm_file)
{
	(this->*pfunc_ServiceTypeGsm[servicetype])(strtb, tm_file);
}

void CSearchSock_AreaCover::DealServiceType_Gsm_Default(std::string strtb, char* tm_file)
{

}

void CSearchSock_AreaCover::DealServiceType_Gsm_Voice(std::string strtb, char* tm_file)
{
	//voice
	char tm_sql[9000];
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec mc_sp_area_cover_gsm_dt_get %d,%d, %d,%d,%d,%d, '%s','%s',%d" ,
		tm_Time_Span.istime, 
		tm_Time_Span.ietime,
		tm_Search_Area.itllongitude,
		tm_Search_Area.itllatitude,
		tm_Search_Area.ibrlongitude,
		tm_Search_Area.ibrlatitude,
		strtb.c_str(),
		tm_file,
		m_Itemp);

	if(0 == m_Itemp)
	{	  
		m_iRowType_ = TYPE_VEC_NORMAL;
		m_bResponseType_ = RESTYPE_AREA_COVER_SUMMARY;
		SEARCHSQL(m_iRowType_, tm_sql);
	}
	else if(1 == m_Itemp)
	{					  
		m_iRowType_ = TYPE_VEC_NORMAL;
		m_bResponseType_ = RESTYPE_AREA_COVER_DETAIL;
		SEARCHSQL(m_iRowType_, tm_sql);
	}
	else if(2 == m_Itemp)
	{
		PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
		PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
			"exec mc_sp_area_cover_gsm_grid_get %d,%d, %d,%d,%d,%d ,'%s','%s'" ,
			tm_Time_Span.istime, 
			tm_Time_Span.ietime,
			tm_Search_Area.itllongitude,
			tm_Search_Area.itllatitude,
			tm_Search_Area.ibrlongitude,
			tm_Search_Area.ibrlatitude,
			strtb.c_str(),
			tm_file);

		char tm_sql2[8000];
		PPM_OS::memset(&tm_sql2, 0, sizeof(tm_sql2));
		PPM_OS::snprintf(tm_sql2,sizeof(tm_sql2),"%s,%d",tm_sql,1);
		m_iRowType_ = TYPE_VEC_NORMAL;
		m_bResponseType_ = RESTYPE_AREA_COVER_GRID_IDLE;
    SEARCHSQL(m_iRowType_, tm_sql2);

		PPM_OS::memset(&tm_sql2, 0, sizeof(tm_sql2));
		PPM_OS::snprintf(tm_sql2,sizeof(tm_sql2),"%s,%d",tm_sql,2);
		m_iRowType_ = TYPE_VEC_NORMAL;
		m_bResponseType_ = RESTYPE_AREA_COVER_GRID_DEDICATED;
		SEARCHSQL(m_iRowType_, tm_sql2);
	}
}

void CSearchSock_AreaCover::DealServiceType_Gsm_Data(std::string strtb, char* tm_file)
{
	//data
	char tm_sql[9000];
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec mc_sp_area_cover_gsm_d_dt_get %d,%d, %d,%d,%d,%d, '%s','%s',%d" ,
		tm_Time_Span.istime, 
		tm_Time_Span.ietime,
		tm_Search_Area.itllongitude,
		tm_Search_Area.itllatitude,
		tm_Search_Area.ibrlongitude,
		tm_Search_Area.ibrlatitude,
		strtb.c_str(),
		tm_file,
		m_Itemp);

	if (0 == m_Itemp)
	{
		m_iRowType_ = TYPE_VEC_REVIEW_DATA_SUMMARY;
		m_bResponseType_ = RESTYPE_AREA_COVER_GPRS_SUMMARY;
		SEARCHSQL(m_iRowType_, tm_sql);
	}
	else if (1 == m_Itemp)
	{
	}
	else if (2 == m_Itemp)
	{
		PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
		PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
			"exec mc_sp_area_cover_gsm_grid_get %d,%d, %d,%d,%d,%d ,'%s','%s'" ,
			tm_Time_Span.istime, 
			tm_Time_Span.ietime,
			tm_Search_Area.itllongitude,
			tm_Search_Area.itllatitude,
			tm_Search_Area.ibrlongitude,
			tm_Search_Area.ibrlatitude,
			strtb.c_str(),
			tm_file);

		char tm_sql2[8000];
		PPM_OS::memset(&tm_sql2, 0, sizeof(tm_sql2));
		PPM_OS::snprintf(tm_sql2,sizeof(tm_sql2),"%s,%d",tm_sql,3);
		m_iRowType_ = TYPE_VEC_GIS_DATA;
		m_bResponseType_ = RESTYPE_AREA_COVER_GRID_GPRS;
		SEARCHSQL(m_iRowType_,tm_sql2);  
	}
}

void CSearchSock_AreaCover::DealServiceType_Gsm_Uplink(std::string strtb, char* tm_file)
{
	if (2 == m_Itemp)
	{
		//GSM_UPLINK
		char tm_sql[9000];
		char strtbname[255];
		char strdata[1024];

		PPM_OS::memset(strtbname, 0, sizeof(strtbname));
		PPM_OS::snprintf(strtbname, sizeof(tm_sql), "tb_gsm_stati_mtr_grid_%s", strtb.c_str());
		PPM_OS::memset(strdata, 0, sizeof(strdata));
		PPM_OS::snprintf(strdata, sizeof(strdata), "''BaseInfo'',''RxlevDL'',''RxqualDL'',''RxlevUL'',''RxqualUL'',''Ta''");

		PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
		PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
			"exec mc_sp_stati_grid_get  0,0, %d,%d,%d,%d, '%s', '%s', '%s'",
			tm_Search_Area.itllongitude,
			tm_Search_Area.itllatitude,
			tm_Search_Area.ibrlongitude,
			tm_Search_Area.ibrlatitude,
			tm_file,
			strtbname,
			strdata
			);
		m_iRowType_ = TYPE_VEC_SEARCH_DATA_IMAGE;
		m_bResponseType_ = RESTYPE_AREAI_COVER_GRID_GSM_UPLINK;
		SEARCHSQL(m_iRowType_, tm_sql);
	}
}

void CSearchSock_AreaCover::DealTestType_Scan(int servicetype, std::string strtb, char* tm_file)
{
	char tm_sql[9000];
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec mc_sp_area_cover_scan_get %d,%d, %d,%d,%d,%d, '%s','%s',%d" ,
		tm_Time_Span.istime, 
		tm_Time_Span.ietime,
		tm_Search_Area.itllongitude,
		tm_Search_Area.itllatitude,
		tm_Search_Area.ibrlongitude,
		tm_Search_Area.ibrlatitude,
		strtb.c_str(),
		tm_file,
		m_Itemp);

	if(0 == m_Itemp)
	{
		m_iRowType_ = TYPE_VEC_NORMAL;
		m_bResponseType_ = RESTYPE_AREA_COVER_SCAN_SUMMARY;
		SEARCHSQL(m_iRowType_, tm_sql);
	}
	else if(1 == m_Itemp)
	{
		m_iRowType_ = TYPE_VEC_NORMAL;
		m_bResponseType_ = RESTYPE_AREA_COVER_SCAN_DETAIL;
    SEARCHSQL(m_iRowType_, tm_sql);
	}
	else if(2 == m_Itemp)
	{
		m_iRowType_ = TYPE_VEC_NORMAL;
		m_bResponseType_ = RESTYPE_AREA_COVER_GRID_SCAN;
		SEARCHSQL(m_iRowType_, tm_sql);
	}
}

void CSearchSock_AreaCover::DealTestType_Cdma(int servicetype, std::string strtb, char* tm_file)
{
    (this->*pfunc_ServiceTypeCdma[servicetype])(strtb, tm_file);
}

void CSearchSock_AreaCover::DealServiceType_Cdma_Default(std::string strtb, char* tm_file)
{

}

void CSearchSock_AreaCover::DealServiceType_Cdma_Voice(std::string strtb, char* tm_file)
{
	//cdma voice && cdma1x
	char tm_sql[9000];
	char strtbname[255];
	char strdata[1024];
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec mc_sp_area_cover_cdma_dt_get %d,%d, %d,%d,%d,%d ,'%s','%s', '%d'" ,
		tm_Time_Span.istime, 
		tm_Time_Span.ietime,
		tm_Search_Area.itllongitude,
		tm_Search_Area.itllatitude,
		tm_Search_Area.ibrlongitude,
		tm_Search_Area.ibrlatitude,
		strtb.c_str(),
		tm_file,
		m_Itemp);

	if(0 == m_Itemp)
	{
		m_bResponseType_ = RESTYPE_AREA_COVER_CDMA_V_SUMMARY;
		m_iRowType_ = TYPE_VEC_NORMAL;
    SEARCHSQL(m_iRowType_, tm_sql);
	}
	else if(1 == m_Itemp)
	{					  
	}
	else if(2 == m_Itemp)
	{
		char tm_sql2[8000];

		PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
		PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
			"exec mc_sp_area_cover_cdma_grid_get %d,%d, %d,%d,%d,%d, '%s','%s'" ,
			tm_Time_Span.istime, tm_Time_Span.ietime,
			tm_Search_Area.itllongitude,
			tm_Search_Area.itllatitude,
			tm_Search_Area.ibrlongitude,
			tm_Search_Area.ibrlatitude,
			strtb.c_str(),
			tm_file);

		PPM_OS::memset(&tm_sql2, 0, sizeof(tm_sql2));
		PPM_OS::snprintf(tm_sql2,sizeof(tm_sql2),"%s,%d",tm_sql,1);
		m_iRowType_ = TYPE_VEC_NORMAL;
		m_bResponseType_ = RESTYPE_AREA_COVER_CDMA_V_GRID;
    SEARCHSQL(m_iRowType_, tm_sql2);

		//cdma image voice
		PPM_OS::memset(strtbname, 0, sizeof(strtbname));
		PPM_OS::snprintf(strtbname, sizeof(tm_sql), "tb_cdma_stati_v_grid_%s", strtb.c_str());
		PPM_OS::memset(strdata, 0, sizeof(strdata));
		PPM_OS::snprintf(strdata,sizeof(strdata), "''BaseInfo'',''CoverRate'',''FFER'',''Ecio'',''Rxpower'',''Txpower'',''Pesq''");

		PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
		PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
			"exec mc_sp_stati_grid_get  %d,%d, %d,%d,%d,%d, '%s', '%s', '%s'",
			tm_Time_Span.istime,
			tm_Time_Span.ietime,
			tm_Search_Area.itllongitude,
			tm_Search_Area.itllatitude,
			tm_Search_Area.ibrlongitude,
			tm_Search_Area.ibrlatitude,
			tm_file,
			strtbname,
			strdata
			);
		m_iRowType_ = TYPE_VEC_SEARCH_DATA_IMAGE;
		m_bResponseType_ = RESTYPE_AREAI_COVER_CDMA_V_GRID;
    SEARCHSQL(m_iRowType_, tm_sql);
	}
}

void CSearchSock_AreaCover::DealServiceType_Cdma_Data(std::string strtb, char* tm_file)
{
	//cdma data && cdma1x
	char tm_sql[9000];
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec mc_sp_area_cover_cdma_dt_get %d,%d, %d,%d,%d,%d ,'%s','%s', '%d'" ,
		tm_Time_Span.istime, 
		tm_Time_Span.ietime,
		tm_Search_Area.itllongitude,
		tm_Search_Area.itllatitude,
		tm_Search_Area.ibrlongitude,
		tm_Search_Area.ibrlatitude,
		strtb.c_str(),
		tm_file,
		m_Itemp);

	if(0 == m_Itemp)
	{
		m_iRowType_ = TYPE_VEC_NORMAL;
		m_bResponseType_ = RESTYPE_AREA_COVER_CDMA_D_SUMMARY;
    SEARCHSQL(m_iRowType_, tm_sql);
	}
	else if (1 == m_Itemp)
	{

	}
	else if(2 == m_Itemp)
	{
		char tm_sql2[8000];

		PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
		PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
			"exec mc_sp_area_cover_cdma_grid_get %d,%d, %d,%d,%d,%d, '%s','%s'" ,
			tm_Time_Span.istime, 
			tm_Time_Span.ietime,
			tm_Search_Area.itllongitude,
			tm_Search_Area.itllatitude,
			tm_Search_Area.ibrlongitude,
			tm_Search_Area.ibrlatitude,
			strtb.c_str(),
			tm_file);

		PPM_OS::memset(&tm_sql2, 0, sizeof(tm_sql2));
		PPM_OS::snprintf(tm_sql2,sizeof(tm_sql2),"%s,%d",tm_sql,2);
		m_iRowType_ = TYPE_VEC_GIS_DATA;
		m_bResponseType_ = RESTYPE_AREA_COVER_CDMA_D_GRID;
		SEARCHSQL(m_iRowType_, tm_sql2);
	}
}

void CSearchSock_AreaCover::DealTestType_Cdma2000(int servicetype, std::string strtb, char* tm_file)
{
    (this->*pfunc_ServiceTypeCdma2000[servicetype])(strtb, tm_file);
}

void CSearchSock_AreaCover::DealServiceType_Cdma2000_Default(std::string strtb, char* tm_file)
{

}
void CSearchSock_AreaCover::DealServiceType_Cdma2000_Data(std::string strtb, char* tm_file)
{
    char tm_sql[9000];
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec mc_sp_area_cover_cdma2000_d_dt_get %d,%d, %d,%d,%d,%d ,'%s','%s', '%d'" ,
		tm_Time_Span.istime, 
		tm_Time_Span.ietime,
		tm_Search_Area.itllongitude,
		tm_Search_Area.itllatitude,
		tm_Search_Area.ibrlongitude,
		tm_Search_Area.ibrlatitude,
		strtb.c_str(),
		tm_file,
		m_Itemp);

	if(0 == m_Itemp)
	{
		m_iRowType_ = TYPE_VEC_NORMAL;
		m_bResponseType_ = RESTYPE_AREA_COVER_CDMA2000_D_SUMMARY;
    SEARCHSQL(m_iRowType_, tm_sql);
	}
	if (1 == m_Itemp)
	{

	}
	else if(2 == m_Itemp)
	{
		char tm_sql2[8000];

		PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
		PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
			"exec mc_sp_area_cover_cdma2000_grid_get %d,%d, %d,%d,%d,%d, '%s','%s'" ,
			tm_Time_Span.istime, 
			tm_Time_Span.ietime,
			tm_Search_Area.itllongitude,
			tm_Search_Area.itllatitude,
			tm_Search_Area.ibrlongitude,
			tm_Search_Area.ibrlatitude,
			strtb.c_str(),
			tm_file);

		PPM_OS::memset(&tm_sql2, 0, sizeof(tm_sql2));
		PPM_OS::snprintf(tm_sql2,sizeof(tm_sql2),"%s,%d",tm_sql,2);
		m_iRowType_ = TYPE_VEC_GIS_DATA;
		m_bResponseType_ = RESTYPE_AREA_COVER_CDMA2000_D_GRID;
		SEARCHSQL(m_iRowType_, tm_sql2);
	}
}

void CSearchSock_AreaCover::DealTestType_Tdscdma(int servicetype, std::string strtb, char* tm_file)
{
   (this->*pfunc_ServiceTypeTdscdma[servicetype])(strtb, tm_file);
}

void CSearchSock_AreaCover::DealServiceType_Tdscdma_Default(std::string strtb, char* tm_file)
{

}

void CSearchSock_AreaCover::DealServiceType_Tdscdma_Voice(std::string strtb, char* tm_file)
{
    char tm_sql[9000];
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec mc_sp_area_cover_tdscdma_dt_get %d,%d, %d,%d,%d,%d, '%s','%s',%d" ,
		tm_Time_Span.istime, 
		tm_Time_Span.ietime,
		tm_Search_Area.itllongitude,
		tm_Search_Area.itllatitude,
		tm_Search_Area.ibrlongitude,
		tm_Search_Area.ibrlatitude,
		strtb.c_str(),
		tm_file,
		m_Itemp);

	if(0 == m_Itemp)
	{
		m_iRowType_ = TYPE_VEC_NORMAL;
		m_bResponseType_ = RESTYPE_AREA_COVER_TDSCDMA_SUMMARY;
		SEARCHSQL(m_iRowType_, tm_sql);
	}
	else if(1 == m_Itemp)
	{
		m_iRowType_ = TYPE_VEC_NORMAL;
		m_bResponseType_ = RESTYPE_AREA_COVER_TDSCDMA_NORMAL;
		SEARCHSQL(m_iRowType_, tm_sql);
	}
	else if(2 == m_Itemp)
	{
		char tm_sql2[8000];
		char strtbname[255];
		char strdata[1024];
		PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
		PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
			"exec mc_sp_area_cover_tdscdma_grid_get %d,%d, %d,%d,%d,%d,'%s','%s'" ,
			tm_Time_Span.istime, 
			tm_Time_Span.ietime,
			tm_Search_Area.itllongitude,
			tm_Search_Area.itllatitude,
			tm_Search_Area.ibrlongitude, tm_Search_Area.ibrlatitude,
			strtb.c_str(),
			tm_file);

		m_iRowType_ = TYPE_VEC_NORMAL;
		m_bResponseType_ = RESTYPE_AREA_COVER_GRID_TDSCDMA_AMR;
		PPM_OS::memset(&tm_sql2, 0, sizeof(tm_sql2));
		PPM_OS::snprintf(tm_sql2, sizeof(tm_sql2), "%s,%d", tm_sql, 1);
		SEARCHSQL(m_iRowType_, tm_sql2);

		m_iRowType_ = TYPE_VEC_NORMAL;
		m_bResponseType_ = RESTYPE_AREA_COVER_GRID_TDSCDMA_VP;
		PPM_OS::memset(&tm_sql2, 0, sizeof(tm_sql2));
		PPM_OS::snprintf(tm_sql2, sizeof(tm_sql2), "%s,%d", tm_sql, 3);
    SEARCHSQL(m_iRowType_, tm_sql2);

		//TDSCDMA image amr
		PPM_OS::memset(strtbname, 0, sizeof(strtbname));
		PPM_OS::snprintf(strtbname, sizeof(tm_sql), "tb_tdscdma_stati_amr_grid_%s", strtb.c_str());
		PPM_OS::memset(strdata, 0, sizeof(strdata));
		PPM_OS::snprintf(strdata, sizeof(strdata), "''BaseInfo'',''Pccpch'',''Dpch'',''Bler'',''Mos'',''Rxlev'',''FreqJam''");

		PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
		PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
			"exec mc_sp_stati_grid_get  0,0, %d,%d,%d,%d, '%s', '%s', '%s'",
			tm_Search_Area.itllongitude,
			tm_Search_Area.itllatitude,
			tm_Search_Area.ibrlongitude,
			tm_Search_Area.ibrlatitude,
			tm_file,
			strtbname,
			strdata
			);
		m_iRowType_ = TYPE_VEC_SEARCH_DATA_IMAGE;
		m_bResponseType_ = RESTYPE_AREAI_COVER_GRID_TDSCDMA_AMR;
		SEARCHSQL(m_iRowType_, tm_sql);

		//TDSCDMA image VP
		PPM_OS::memset(strtbname, 0, sizeof(strtbname));
		PPM_OS::snprintf(strtbname, sizeof(tm_sql), "tb_tdscdma_stati_vp_grid_%s", strtb.c_str());
		PPM_OS::memset(strdata, 0, sizeof(strdata));
		PPM_OS::snprintf(strdata, sizeof(strdata), "''BaseInfo'',''Pccpch'',''Dpch'',''Bler'',''Mos'',''Rxlev'',''FreqJam''");

		PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
		PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
			"exec mc_sp_stati_grid_get  0,0, %d,%d,%d,%d, '%s', '%s', '%s'",
			tm_Search_Area.itllongitude,
			tm_Search_Area.itllatitude,
			tm_Search_Area.ibrlongitude,
			tm_Search_Area.ibrlatitude,
			tm_file,
			strtbname,
			strdata
			);
		m_iRowType_ = TYPE_VEC_SEARCH_DATA_IMAGE;
		m_bResponseType_ = RESTYPE_AREAI_COVER_GRID_TDSCDMA_VP;
		SEARCHSQL(m_iRowType_, tm_sql);
	}
}

void CSearchSock_AreaCover::DealServiceType_Tdscdma_Data(std::string strtb, char* tm_file)
{
	char tm_sql[9000];
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec mc_sp_area_cover_tdscdma_d_dt_get %d,%d, %d,%d,%d,%d, '%s','%s',%d" ,
		tm_Time_Span.istime, 
		tm_Time_Span.ietime,
		tm_Search_Area.itllongitude,
		tm_Search_Area.itllatitude,
		tm_Search_Area.ibrlongitude,
		tm_Search_Area.ibrlatitude,
		strtb.c_str(),
		tm_file,
		m_Itemp);

	if(0 == m_Itemp)
	{
		m_iRowType_ = TYPE_VEC_NORMAL;
		m_bResponseType_ = RESTYPE_AREA_COVER_TDSCDMA_D_SUMMARY;
    SEARCHSQL(m_iRowType_, tm_sql);
	}
	else if(1 == m_Itemp){

	}
	else if(2 == m_Itemp)
	{
		char tm_sql2[8000];
		char strtbname[255];
		char strdata[1024];
		PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
		PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
			"exec mc_sp_area_cover_tdscdma_grid_get %d,%d, %d,%d,%d,%d,'%s','%s'" ,
			tm_Time_Span.istime, 
			tm_Time_Span.ietime,
			tm_Search_Area.itllongitude,
			tm_Search_Area.itllatitude,
			tm_Search_Area.ibrlongitude,
			tm_Search_Area.ibrlatitude,
			strtb.c_str(),
			tm_file);

		m_iRowType_ = TYPE_VEC_GIS_DATA;
		m_bResponseType_ = RESTYPE_AREA_COVER_GRID_TDSCDMA_PS;
		PPM_OS::memset(&tm_sql2, 0, sizeof(tm_sql2));
		PPM_OS::snprintf(tm_sql2, sizeof(tm_sql2), "%s,%d", tm_sql, 2);
    SEARCHSQL(m_iRowType_, tm_sql2);

		//TDSCDMA image ps
		PPM_OS::memset(strtbname, 0, sizeof(strtbname));
		PPM_OS::snprintf(strtbname, sizeof(tm_sql), "tb_tdscdma_stati_ps_grid_%s", strtb.c_str());
		PPM_OS::memset(strdata, 0, sizeof(strdata));
		PPM_OS::snprintf(strdata, sizeof(strdata), 
			"''BaseInfo'',''Rscp'',''Bler'',''Mos'',''RLC'',''APP'',''MCS'',''FTPSlot'',''Bler_Data'',''Bru''");

		PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
		PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
			"exec mc_sp_stati_grid_get  0,0, %d,%d,%d,%d, '%s', '%s', '%s'",
			tm_Search_Area.itllongitude,
			tm_Search_Area.itllatitude,
			tm_Search_Area.ibrlongitude,
			tm_Search_Area.ibrlatitude,
			tm_file,
			strtbname,
			strdata
			);
		m_iRowType_ = TYPE_VEC_SEARCH_DATA_IMAGE;
		m_bResponseType_ = RESTYPE_AREAI_COVER_GRID_TDSCDMA_PS;
    SEARCHSQL(m_iRowType_, tm_sql);

		//TDSCDMA image ps
		PPM_OS::memset(strtbname, 0, sizeof(strtbname));
		PPM_OS::snprintf(strtbname, sizeof(tm_sql), "tb_tdscdma_stati_ps_grid_%s", strtb.c_str());
		PPM_OS::memset(strdata, 0, sizeof(strdata));
		PPM_OS::snprintf(strdata, sizeof(strdata), 
            "''BaseInfo'',''Pccpch'', ''Dpch'', ''Rxlev'',''RLC'',''APP'', ''MCS'', ''FTPSlot'',''Bler_Data'', ''Bru'',''FreqJam''");

		PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
		PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
			"exec mc_sp_stati_grid_get  0,0, %d,%d,%d,%d, '%s', '%s', '%s'",
			tm_Search_Area.itllongitude,
			tm_Search_Area.itllatitude,
			tm_Search_Area.ibrlongitude,
			tm_Search_Area.ibrlatitude,
			tm_file,
			strtbname,
			strdata
			);
		m_iRowType_ = TYPE_VEC_SEARCH_DATA_IMAGE;
		m_bResponseType_ = RESTYPE_STATI_AREA_KPI_TDSCDMA_PS;
		SEARCHSQL(m_iRowType_, tm_sql);
	}
}

void CSearchSock_AreaCover::DealTestType_Wcdma(int servicetype, std::string strtb, char* tm_file)
{
	(this->*pfunc_ServiceTypeWcdma[servicetype])(strtb, tm_file);
}
void CSearchSock_AreaCover::DealServiceType_Wcdma_Default(std::string strtb, char* tm_file)
{

}
void CSearchSock_AreaCover::DealServiceType_Wcdma_Voice(std::string strtb, char* tm_file)
{
	//WCDMA Voice
	char tm_sql[9000];
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec mc_sp_area_cover_wcdma_dt_get %d,%d, %d,%d,%d,%d, '%s','%s',%d" ,
		tm_Time_Span.istime, 
		tm_Time_Span.ietime,
		tm_Search_Area.itllongitude,
		tm_Search_Area.itllatitude,
		tm_Search_Area.ibrlongitude,
		tm_Search_Area.ibrlatitude,
		strtb.c_str(),
		tm_file,
		m_Itemp);

	if (0 == m_Itemp)
	{			  
		m_iRowType_ = TYPE_VEC_NORMAL;
		m_bResponseType_ = RESTYPE_AERA_COVER_WCDMA_V_SUMMARY;
		SEARCHSQL(m_iRowType_, tm_sql);
	}
	else if (1 == m_Itemp)
	{

	}
	else if (2 == m_Itemp)
	{
		char strtbname[255];
		char tm_sql2[8000];
		char strdata[1024];
		PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
		PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
			"exec mc_sp_area_cover_wcdma_grid_get %d,%d, %d,%d,%d,%d, '%s','%s'" ,
			tm_Time_Span.istime, 
			tm_Time_Span.ietime,
			tm_Search_Area.itllongitude,
			tm_Search_Area.itllatitude,
			tm_Search_Area.ibrlongitude,
			tm_Search_Area.ibrlatitude,
			strtb.c_str(),
			tm_file);

		//amr
		m_iRowType_ = TYPE_VEC_NORMAL;
		m_bResponseType_ = RESTYPE_AREA_COVER_GRID_WCDMA_AMR;
		PPM_OS::memset(&tm_sql2, 0, sizeof(tm_sql2));
		PPM_OS::snprintf(tm_sql2, sizeof(tm_sql2), "%s,%d", tm_sql, 1);
    SEARCHSQL(m_iRowType_, tm_sql2);

		//vp
		m_bResponseType_ = RESTYPE_AREA_COVER_GRID_WCDMA_VP;
		m_iRowType_ = TYPE_VEC_NORMAL;
		PPM_OS::memset(&tm_sql2, 0, sizeof(tm_sql2));
		PPM_OS::snprintf(tm_sql2, sizeof(tm_sql2), "%s,%d", tm_sql, 3);
		SEARCHSQL(m_iRowType_, tm_sql2);

		//wcdma image amr 
		PPM_OS::memset(strtbname, 0, sizeof(strtbname));
		PPM_OS::snprintf(strtbname, sizeof(tm_sql), "tb_wcdma_stati_amr_grid_%s", strtb.c_str());
		PPM_OS::memset(strdata, 0, sizeof(strdata));
		PPM_OS::snprintf(strdata, sizeof(strdata), 
			"''BaseInfo'',''CoverRate'', ''Rscp'',''Ecio'',''Rxpower'',''Txpower'',''Bler'',''Sir'', ''ActiveSet'',''FreqJam''");

		PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
		PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
			"exec mc_sp_stati_grid_get  %d,%d, %d,%d,%d,%d, '%s', '%s', '%s'",
			tm_Time_Span.istime, 
			tm_Time_Span.ietime,
			tm_Search_Area.itllongitude,
			tm_Search_Area.itllatitude,
			tm_Search_Area.ibrlongitude,
			tm_Search_Area.ibrlatitude,
			tm_file,
			strtbname,
			strdata
			);
		m_iRowType_ = TYPE_VEC_SEARCH_DATA_IMAGE;
		m_bResponseType_ = RESTYPE_AREAI_COVER_GRID_WCDMA_AMR;
		SEARCHSQL(m_iRowType_, tm_sql);

		//wcdma image vp 
		PPM_OS::memset(strtbname, 0, sizeof(strtbname));
		PPM_OS::snprintf(strtbname, sizeof(tm_sql), "tb_wcdma_stati_vp_grid_%s" , strtb.c_str());
		PPM_OS::memset(strdata, 0, sizeof(strdata));
		PPM_OS::snprintf(strdata, sizeof(strdata), 
			"''BaseInfo'',''CoverRate'', ''Rscp'',''Ecio'', ''Rxpower'',''Txpower'', ''Bler'',''Sir'',''ActiveSet'',''FreqJam''");

		PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
		PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
			"exec mc_sp_stati_grid_get  %d,%d, %d,%d,%d,%d, '%s', '%s', '%s'",
			tm_Time_Span.istime, 
			tm_Time_Span.ietime,
			tm_Search_Area.itllongitude,
			tm_Search_Area.itllatitude,
			tm_Search_Area.ibrlongitude,
			tm_Search_Area.ibrlatitude,
			tm_file,
			strtbname,
			strdata
			);
		m_iRowType_ = TYPE_VEC_SEARCH_DATA_IMAGE;
		m_bResponseType_ = RESTYPE_AREAI_COVER_GRID_WCDMA_VP;
		SEARCHSQL(m_iRowType_, tm_sql);
	}
}
void CSearchSock_AreaCover::DealServiceType_Wcdma_Data(std::string strtb, char* tm_file)
{
	//WCDMA Data
    char tm_sql[9000];
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec mc_sp_area_cover_wcdma_d_dt_get %d,%d, %d,%d,%d,%d, '%s','%s',%d" ,
		tm_Time_Span.istime, 
		tm_Time_Span.ietime,
		tm_Search_Area.itllongitude,
		tm_Search_Area.itllatitude,
		tm_Search_Area.ibrlongitude,
		tm_Search_Area.ibrlatitude,
		strtb.c_str(),
		tm_file,
		m_Itemp);

	if (0 == m_Itemp)
	{			  
		m_iRowType_ = TYPE_VEC_NORMAL;
		m_bResponseType_ = RESTYPE_AERA_COVER_WCDMA_D_SUMMARY;
		SEARCHSQL(m_iRowType_, tm_sql);
	}
	else if (1 == m_Itemp)
	{

	}
	else if (2 == m_Itemp)
	{
		char tm_sql2[8000];
		char strtbname[255];
		char strdata[1024];
		PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
		PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
			"exec mc_sp_area_cover_wcdma_grid_get %d,%d, %d,%d,%d,%d, '%s','%s'" ,
			tm_Time_Span.istime, 
			tm_Time_Span.ietime,
			tm_Search_Area.itllongitude,
			tm_Search_Area.itllatitude,
			tm_Search_Area.ibrlongitude,
			tm_Search_Area.ibrlatitude,
			strtb.c_str(),
			tm_file);

		//ps
		m_iRowType_ = TYPE_VEC_GIS_DATA;
		m_bResponseType_ = RESTYPE_AREA_COVER_GRID_WCDMA_PS;
		PPM_OS::memset(&tm_sql2, 0, sizeof(tm_sql2));
		PPM_OS::snprintf(tm_sql2, sizeof(tm_sql2), "%s,%d", tm_sql, 2);
		SEARCHSQL(m_iRowType_, tm_sql2);

		//pshs
		m_iRowType_ = TYPE_VEC_GIS_DATA;
		m_bResponseType_ = RESTYPE_AREA_COVER_GRID_WCDMA_PSHS;
		PPM_OS::memset(&tm_sql2, 0, sizeof(tm_sql2));
		PPM_OS::snprintf(tm_sql2, sizeof(tm_sql2), "%s,%d", tm_sql, 4);
		SEARCHSQL(m_iRowType_, tm_sql2);

		//wcdma image ps 
		PPM_OS::memset(strtbname, 0, sizeof(strtbname));
		PPM_OS::snprintf(strtbname, sizeof(tm_sql), "tb_wcdma_stati_ps_grid_%s" , strtb.c_str());
		PPM_OS::memset(strdata, 0, sizeof(strdata));
		PPM_OS::snprintf(strdata, sizeof(strdata), 
			"''BaseInfo'',''CoverRate'',''Rscp'',''Ecio'',''Rxpower'',''Txpower'', ''Bler'',\
			''Sir'',''ActiveSet'',''RLC'', ''APP'',''FreqJam'', ''PDUSDU'',''CQI''");

		PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
		PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
			"exec mc_sp_stati_grid_get  %d,%d, %d,%d,%d,%d, '%s', '%s', '%s'",
			tm_Time_Span.istime, 
			tm_Time_Span.ietime,
			tm_Search_Area.itllongitude,
			tm_Search_Area.itllatitude,
			tm_Search_Area.ibrlongitude,
			tm_Search_Area.ibrlatitude,
			tm_file,
			strtbname,
			strdata
			);
		m_iRowType_ = TYPE_VEC_SEARCH_DATA_IMAGE;
		m_bResponseType_ = RESTYPE_AREAI_COVER_GRID_WCDMA_PS;
		SEARCHSQL(m_iRowType_, tm_sql);

		//wcdma image pshs
		PPM_OS::memset(strtbname, 0, sizeof(strtbname));
		PPM_OS::snprintf(strtbname, sizeof(tm_sql), "tb_wcdma_stati_pshs_grid_%s" , strtb.c_str());
		PPM_OS::memset(strdata, 0, sizeof(strdata));
		PPM_OS::snprintf(strdata, sizeof(strdata), "''BaseInfo'',''CoverRate'', ''Rscp'',''Ecio'',''Rxpower'',\
''Txpower'',''Bler'',''Sir'',''ActiveSet'',''RLC'',\
''APP'',''FreqJam'',''PDUSDU'',''CQI''");

		PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
		PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
			"exec mc_sp_stati_grid_get  %d,%d, %d,%d,%d,%d, '%s', '%s', '%s'",
			tm_Time_Span.istime, 
			tm_Time_Span.ietime,
			tm_Search_Area.itllongitude,
			tm_Search_Area.itllatitude,
			tm_Search_Area.ibrlongitude,
			tm_Search_Area.ibrlatitude,
			tm_file,
			strtbname,
			strdata
			);
		m_iRowType_ = TYPE_VEC_SEARCH_DATA_IMAGE;
		m_bResponseType_ = RESTYPE_AREAI_COVER_GRID_WCDMA_PSHS;
		SEARCHSQL(m_iRowType_, tm_sql);
	}
}


/************************************************************************/
/* CSearchSock_AreaCoverLeak                                            */
/************************************************************************/
BOOL  
CSearchSock_AreaCoverLeak::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{   
	MOVENEXTTEST(ioffset,17,nCount);	
	//time
	if (!GetTime(pData, nCount, ioffset))
	{
	  return FALSE;
	}

	if (!GetFileList(pData, nCount, ioffset))
	{
	  return FALSE;
	}
	//no data
	if(0 >= m_map_filelist.size()){
	  //处理结束
	  FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
	  return TRUE;
	}
	  
	char tm_file[8000];
	int bOnce;
	int cutnum = 600;         //ado对提交的sql语句限制为8000字节以内
	auto p_map = m_map_filelist.begin();   
	  
	for (; p_map != m_map_filelist.end(); p_map++)   
	{	  
		PPM_OS::memset(&tm_file, 0, sizeof(tm_file));	
		bOnce = 0;	
		size_t allcount=1;	

		auto p_vec = p_map->second.begin();	

		for (int count=1; p_vec != p_map->second.end(); p_vec++, ++count, ++allcount)   
		{   
			if(0 == bOnce)
			{
				PPM_OS::snprintf(tm_file,sizeof(tm_file),"%d",*p_vec); 
				bOnce = 1;
			}
			else
			{
				PPM_OS::snprintf(tm_file,sizeof(tm_file),"%s,%d",
					tm_file,*p_vec); 
			}	
			if(cutnum == count || allcount == p_map->second.size())
			{ 
				//获取头文件信息
				SearchLogFileSInfo(p_map->first.strlog.c_str(), p_map->first.testtype, tm_file, 0);

				DealTestTypeData(p_map->first.testtype, p_map->first.strtb, tm_file);

				PPM_OS::memset(tm_file, 0, sizeof(tm_file));
				count = 0;
				bOnce = 0;
			}
		}         
	} 
	//处理结束
	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);

	return TRUE;
}

BOOL CSearchSock_AreaCoverLeak::GetTime(const BYTE* const pData, const int nCount,int& ioffset)
{
	PPM_OS::memcpy(&tm_Time_Span.istime,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Time_Span.ietime,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	tm_Time_Span.ConvertToNetSeq();
	return TRUE;
}
BOOL CSearchSock_AreaCoverLeak::GetFileList(const BYTE* const pData, const int nCount,int& ioffset)
{
	char tm_sql_part[1024];
	PPM_OS::memset(tm_sql_part, 0, sizeof(tm_sql_part));
	if(FALSE == CSearch_PI::GetTypeSql(pData,nCount,ioffset,tm_sql_part)){
		return FALSE;
	}

	//longitude and latitude
	if (!GetAreaCoverLeakSearchAreaInfo(pData, nCount, ioffset))
	{
		return FALSE;
	}

	BYTE btemp = 0xff;
	PPM_OS::memcpy(&btemp,pData + ioffset,1);
	m_Itemp = btemp & 0x000000ff;

	char tm_sql[9000];
	PPM_OS::memset(tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec mc_sp_area_cover_get %d,%d, %d,%d,%d,%d, '%s',%d" ,
		tm_Time_Span.istime, 
		tm_Time_Span.ietime,
		tm_Search_Area.itllongitude,     
		tm_Search_Area.itllatitude,
		tm_Search_Area.ibrlongitude,
		tm_Search_Area.ibrlatitude,
		tm_sql_part,
		m_Itemp);

	m_iRowType_ = TYPE_VEC_FILE_LIST; //文件id获取 +　采样点数目获取
	m_map_filelist.clear();
    SEARCHSQL(m_iRowType_, tm_sql);
	return TRUE;
}

BOOL CSearchSock_AreaCoverLeak::GetAreaCoverLeakSearchAreaInfo(const BYTE* const pData, const int nCount,int& ioffset)
{
	PPM_OS::memcpy(&tm_Search_Area.itllongitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Search_Area.itllatitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Search_Area.ibrlongitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Search_Area.ibrlatitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);

	tm_Search_Area.ConvertToNetSeq();	
    return TRUE;
}

void CSearchSock_AreaCoverLeak::DealTestTypeData(int testtype, std::string strtb, char* tm_file)
{
	if(CEnumDef::T_TDSCDMA_DT == testtype)  //TDSCDMA
	{
		DealTestTypeTdscdma(strtb, tm_file);					  
	}
	else
	{
		DealSearchSampleSummary(strtb, tm_file);
	}
}

void CSearchSock_AreaCoverLeak::DealTestTypeTdscdma(std::string strtb, char* tm_file)
{
	char tm_sql[9000];
	PPM_OS::memset(tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec mc_sp_area_coverleak_td_get %d,%d, %d,%d,%d,%d, '%s','%s',%d" ,
		tm_Time_Span.istime, 
		tm_Time_Span.ietime,
		tm_Search_Area.itllongitude,
		tm_Search_Area.itllatitude,
		tm_Search_Area.ibrlongitude,
		tm_Search_Area.ibrlatitude,
		strtb.c_str(),
		tm_file,
		m_Itemp);

	if(0 == m_Itemp)
	{
		m_iRowType_ = TYPE_VEC_NORMAL;
		m_bResponseType_ = RESTYPE_AREA_COVER_TDSCDMA_SUMMARY;
		SEARCHSQL(m_iRowType_, tm_sql);
	}
}

void CSearchSock_AreaCoverLeak::DealSearchSampleSummary(std::string strtb, char* tm_file)
{
	if(0 == m_Itemp)
	{
		char tm_sql[9000];
		PPM_OS::memset(tm_sql, 0, sizeof(tm_sql));
		PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
			"exec mc_sp_area_coverleak_gsm_get %d,%d, %d,%d,%d,%d, '%s','%s'" ,
			tm_Time_Span.istime, 
			tm_Time_Span.ietime,
			tm_Search_Area.itllongitude,
			tm_Search_Area.itllatitude,
			tm_Search_Area.ibrlongitude,
			tm_Search_Area.ibrlatitude,
			strtb.c_str(),
			tm_file);

		m_iRowType_ = TYPE_VEC_NORMAL;
		m_bResponseType_ = RESTYPE_LOG_REVIEW_SAMPLE_SUMMARY;
		SEARCHSQL(m_iRowType_, tm_sql);
	}		    
}

/************************************************************************/
/* CSearchSock_AreaCoverRecent                                          */
/************************************************************************/

BOOL
CSearchSock_AreaCoverRecent::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	MOVENEXTTEST(ioffset,17,nCount);
	//time
	if (!GetTime(pData, nCount, ioffset))
	{
	    return FALSE;
	}
	
	if (!GetFileList(pData, nCount, ioffset))
	{
		return FALSE;
	}

	if(0 >= m_map_filelist.size())	//no data
	{
		//处理结束
		FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
		return TRUE;
	}

	m_set_grid_recent.clear();
	m_set_grid_fileid_recent.clear();

	DealFileList();

	//grid_event_recent	
	DealGridEventRecent();

	//处理结束
	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
	return TRUE;	
}

void CSearchSock_AreaCoverRecent::DealFileList(void)
{
	char tm_file[8000];
	int bOnce;
	int cutnum = 600;         //ado对提交的sql语句限制为8000字节以内
	auto p_map = m_map_filelist.begin();   

	for (; p_map != m_map_filelist.end(); p_map++)   
	{	 
		auto p_vec = p_map->second.begin();	
		PPM_OS::memset(&tm_file, 0, sizeof(tm_file));	
		bOnce = 0;	
		size_t allcount=1;	

		for (int count=1; p_vec != p_map->second.end(); p_vec++, ++count, ++allcount)   
		{   
			if(0 == bOnce)
			{
				bOnce = 1;
				PPM_OS::snprintf(tm_file,sizeof(tm_file),"%d",
					*p_vec); 
			}
			else
			{
				PPM_OS::snprintf(tm_file,sizeof(tm_file),"%s,%d",tm_file,*p_vec); 
			}	
			if(cutnum == count || allcount == p_map->second.size())
			{ 
				//获取头文件信息
				SearchLogFileSInfo(p_map->first.strlog.c_str(), p_map->first.testtype, tm_file, 0);

				DealTestTypeGsm(p_map->first.testtype, p_map->first.strtb, tm_file);

				PPM_OS::memset(tm_file, 0, sizeof(tm_file));
				count = 0;
				bOnce = 0;
			}
		}
	} 
}

void CSearchSock_AreaCoverRecent::DealGridEventRecent(void)
{
	char tm_file[8000];
	std::set<int>::iterator p_set = m_set_grid_fileid_recent.begin();   
	PPM_OS::memset(&tm_file, 0, sizeof(tm_file));	
	int bOnce = 0;
	int cutnum = 600;
	std::set<std::string>::size_type setcount=1;	

	for (int count=1; p_set != m_set_grid_fileid_recent.end(); p_set++, ++count, ++setcount)   
	{   
		if(0 == bOnce)
		{
			PPM_OS::snprintf(tm_file,sizeof(tm_file),"%d",*p_set); 
			bOnce = 1;
		}
		else
		{
			PPM_OS::snprintf(tm_file,sizeof(tm_file),"%s,%d",tm_file,*p_set); 
		}	
		if(cutnum == count || setcount == m_set_grid_fileid_recent.size())
		{ 
			char tm_sql[9000];
			PPM_OS::memset(tm_sql, 0, sizeof(tm_sql));
			PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
				"exec mc_sp_area_cover_grid_evt_rct_get '%s'" ,
				tm_file);

			m_iRowType_ = TYPE_VEC_NORMAL;
			m_bResponseType_ = RESTYPE_AREA_EVENT;
      SEARCHSQL(m_iRowType_, tm_sql);

			PPM_OS::memset(tm_file, 0, sizeof(tm_file));
			count = 0;
			bOnce = 0;	    
		}
	}
}

BOOL CSearchSock_AreaCoverRecent::GetTime(const BYTE* const pData, const int nCount,int& ioffset)
{
	PPM_OS::memcpy(&tm_Time_Span.istime,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Time_Span.ietime,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	tm_Time_Span.ConvertToNetSeq();	
    return TRUE;
}

BOOL CSearchSock_AreaCoverRecent::GetAreaCoverRecentSearchAreaInfo(const BYTE* const pData, const int nCount,int& ioffset)
{
	PPM_OS::memcpy(&tm_Search_Area.itllongitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Search_Area.itllatitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Search_Area.ibrlongitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Search_Area.ibrlatitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);

	tm_Search_Area.ConvertToNetSeq();	
    return TRUE;
}

BOOL CSearchSock_AreaCoverRecent::GetFileList(const BYTE* const pData, const int nCount,int& ioffset)
{
	//type
	char tm_sql_part[1024];
	PPM_OS::memset(tm_sql_part, 0, sizeof(tm_sql_part));
	if(FALSE == CSearch_PI::GetTypeSql(pData,nCount,ioffset,tm_sql_part)){
		return FALSE;
	}

	//longitude and latitude
	if (!GetAreaCoverRecentSearchAreaInfo(pData, nCount, ioffset))
	{
		return FALSE;
	}

	BYTE btemp = 0xff;
	PPM_OS::memcpy(&btemp,pData + ioffset,1);
	m_Itemp = btemp & 0x000000ff;

	char tm_sql[9000];
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec mc_sp_area_cover_get %d,%d, %d,%d,%d,%d, '%s',%d" ,
		tm_Time_Span.istime, 
		tm_Time_Span.ietime,
		tm_Search_Area.itllongitude,     
		tm_Search_Area.itllatitude,
		tm_Search_Area.ibrlongitude,
		tm_Search_Area.ibrlatitude,
		tm_sql_part,
		m_Itemp);

	m_map_filelist.clear();
	m_iRowType_ = TYPE_VEC_FILE_LIST;//文件id获取 +　采样点数目获取
    SEARCHSQL(m_iRowType_, tm_sql);
	return TRUE;
}


void CSearchSock_AreaCoverRecent::DealTestTypeGsm(int testtype, std::string strtb, char* tm_file)
{
	if (testtype == CEnumDef::T_GSM_DT)
	{
		if(2 == m_Itemp)
		{
			char tm_sql[9000];
			PPM_OS::memset(tm_sql, 0, sizeof(tm_sql));
			PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
				"exec mc_sp_area_cover_grid_recent_get %d,%d, %d,%d,%d,%d, '%s','%s'" ,
				tm_Time_Span.istime, 
				tm_Time_Span.ietime,
				tm_Search_Area.itllongitude,
				tm_Search_Area.itllatitude,
				tm_Search_Area.ibrlongitude,
				tm_Search_Area.ibrlatitude,
				strtb.c_str(),
				tm_file);

			char tm_sql2[8000];
			PPM_OS::memset(tm_sql2, 0, sizeof(tm_sql2));
			PPM_OS::snprintf(tm_sql2,sizeof(tm_sql2),"%s,%d",tm_sql,2);
			m_iRowType_ = TYPE_VEC_GRID_DEDICATED_RECENT;   //需要保留fileid
			m_bResponseType_ = RESTYPE_AREA_COVER_GRID_DEDICATED;
      SEARCHSQL(m_iRowType_, tm_sql2);
			 
			PPM_OS::memset(&tm_sql2, 0, sizeof(tm_sql2));
			PPM_OS::snprintf(tm_sql2,sizeof(tm_sql2),"%s,%d",tm_sql,1);
			m_iRowType_ = TYPE_VEC_GRID_IDLE_RECENT;
			m_bResponseType_ = RESTYPE_AREA_COVER_GRID_IDLE;
			SEARCHSQL(m_iRowType_, tm_sql2);

			PPM_OS::memset(&tm_sql2, 0, sizeof(tm_sql2));
			PPM_OS::snprintf(tm_sql2,sizeof(tm_sql2),"%s,%d",tm_sql,3);
			m_iRowType_ = TYPE_VEC_GRID_GPRS_RECENT;
			m_bResponseType_ = RESTYPE_AREA_COVER_GRID_GPRS;
			SEARCHSQL(m_iRowType_, tm_sql2);
		}
		else
		{
			//do nothing...
		}
	}
}

BOOL
CSearchSock_AreaCoverRecent::RowResultDeal()
{
	switch(m_iRowType_)
	{
	case TYPE_VEC_GRID_DEDICATED_RECENT:
		{
           RowResultDeal_Grid_Dedicated_Recent();
		}
		break;

	case TYPE_VEC_GRID_IDLE_RECENT:
		{
          RowResultDeal_Grid_Idle_Recent();
		}
		break;

	case TYPE_VEC_GRID_GPRS_RECENT:
		{
			RowResultDeal_Grid_Gprs_Recent();
		}
		break;
	default:
		break;
	}

	return TRUE;
}


BOOL 
CSearchSock_AreaCoverRecent::RowResultDeal_Grid_Idle_Recent()
{
	if(m_vecColumn.size() != CResultDef::Instance()->GetVecResultSize(CResultDef::SEARCHTYPE_GRID_IDLE)){
		return FALSE;
	} 

	std::string key = "";
	char keychars[512];
	PPM_OS::memset(keychars, 0, sizeof(keychars));
	PPM_OS::snprintf(keychars, sizeof(keychars), "%d,%d,%d,%d", *((int*)m_vecColumn[0].pColValue),
		*((int*)m_vecColumn[1].pColValue),
		*((int*)m_vecColumn[2].pColValue),
		*((int*)m_vecColumn[3].pColValue)
		);
	key.assign(keychars);
	if (m_set_grid_recent.count(key) == 0){
		m_set_grid_recent.insert(key);
	}
	else 
	{
		return true;
	}

	int keyfileid = *((int*)m_vecColumn[11].pColValue);
	if (m_set_grid_fileid_recent.count(keyfileid) == 0)
	{
		m_set_grid_fileid_recent.insert(keyfileid);
	}

	if(NULL != m_pServerDeal_){
		PPM_OS::memset(m_pSendBuf_,0,SEND_BUF_MAXSIZE);

		//构造信息发送
		int tm_ioffset = 0;
		m_pSendBuf_[tm_ioffset++] = CMD1_SEARCH;//命令字
		m_pSendBuf_[tm_ioffset++] = CMD2_RESPONSE;//子命令字
		m_pSendBuf_[tm_ioffset++] = m_bResponseType_;//响应类型
		//
		for(size_t i = 0; i < m_vecColumn.size(); i++)
		{
			(this->*pfunc_AddSendValue[m_vecColumn[i].iColType])(tm_ioffset,i);
		}
		if(0 > m_pServerDeal_->SendData(m_pSendBuf_,tm_ioffset)){
			return -1;
		}
	}
	return TRUE;
}

BOOL 
CSearchSock_AreaCoverRecent::RowResultDeal_Grid_Dedicated_Recent()
{
	if(m_vecColumn.size() != CResultDef::Instance()->GetVecResultSize(CResultDef::SEARCHTYPE_GRID_DEDICATED)){
		return FALSE;
	} 

	char keychars[512];
	std::string key = "";		
	PPM_OS::memset(keychars, 0, sizeof(keychars));
	PPM_OS::snprintf(keychars, sizeof(keychars), "%d,%d,%d,%d", *((int*)m_vecColumn[0].pColValue),
		*((int*)m_vecColumn[1].pColValue),
		*((int*)m_vecColumn[2].pColValue),
		*((int*)m_vecColumn[3].pColValue)
		);
	key.assign(keychars);	
	if (m_set_grid_recent.count(key) == 0)
	{
		m_set_grid_recent.insert(key);
	}
	else {
		return true;
	}

	int keyfileid = 0;
	keyfileid = *((int*)m_vecColumn[11].pColValue);
	if (m_set_grid_fileid_recent.count(keyfileid) == 0)
	{
		m_set_grid_fileid_recent.insert(keyfileid);
	}

	if(NULL != m_pServerDeal_){
		PPM_OS::memset(m_pSendBuf_,0,SEND_BUF_MAXSIZE);

		//构造信息发送
		int tm_ioffset = 0;
		m_pSendBuf_[tm_ioffset++] = CMD1_SEARCH;//命令字
		m_pSendBuf_[tm_ioffset++] = CMD2_RESPONSE;//子命令字
		m_pSendBuf_[tm_ioffset++] = m_bResponseType_;//响应类型
		//
		for(size_t i = 0; i < m_vecColumn.size(); i++){
			(this->*pfunc_AddSendValue[m_vecColumn[i].iColType])(tm_ioffset,i);
		}
		if(0 > m_pServerDeal_->SendData(m_pSendBuf_,tm_ioffset)){
			return -1;
		}
	}
	return TRUE;
}

BOOL 
CSearchSock_AreaCoverRecent::RowResultDeal_Grid_Gprs_Recent()
{
	if(m_vecColumn.size() != CResultDef::Instance()->GetVecResultSize(CResultDef::SEARCHTYPE_GRID_GPRS)){
		return FALSE;
	} 

	char keychars[512];
	PPM_OS::memset(keychars, 0, sizeof(keychars));
	std::string key = "";

	PPM_OS::snprintf(keychars, sizeof(keychars), "%d,%d,%d,%d", *((int*)m_vecColumn[0].pColValue),
		*((int*)m_vecColumn[1].pColValue),
		*((int*)m_vecColumn[2].pColValue),
		*((int*)m_vecColumn[3].pColValue)
		);
	key.assign(keychars);
	if (m_set_grid_recent.count(key) == 0)
	{
		m_set_grid_recent.insert(key);
	}
	else 
	{
		return true;
	}

	int keyfileid = 0;
	keyfileid = *((int*)m_vecColumn[11].pColValue);
	if (m_set_grid_fileid_recent.count(keyfileid) == 0)
	{
		m_set_grid_fileid_recent.insert(keyfileid);
	}

	if(NULL != m_pServerDeal_){
		PPM_OS::memset(m_pSendBuf_,0,SEND_BUF_MAXSIZE);

		//构造信息发送
		int tm_ioffset = 0;
		m_pSendBuf_[tm_ioffset++] = CMD1_SEARCH;//命令字
		m_pSendBuf_[tm_ioffset++] = CMD2_RESPONSE;//子命令字
		m_pSendBuf_[tm_ioffset++] = m_bResponseType_;//响应类型
		//
		for(size_t i = 0; i < m_vecColumn.size(); i++){
			(this->*pfunc_AddSendValue[m_vecColumn[i].iColType])(tm_ioffset,i);
		}
		if(0 > m_pServerDeal_->SendData(m_pSendBuf_,tm_ioffset)){
			return -1;
		}
	}
	return TRUE;
}


/************************************************************************/
/* CSearchSock_AreaEvent                                                */
/************************************************************************/

/************************************************************************
基本查询条件+区域查询条件+事件列表
************************************************************************/
BOOL  
CSearchSock_AreaEvent::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	MOVENEXTTEST(ioffset,17,nCount);
	//time
	if (!GetTime(pData, nCount, ioffset))
	{
		return FALSE;
	}
	
	if (!GetFileList(pData, nCount, ioffset))
	{
		return FALSE;
	}
	
	if(0 >= m_map_filelist.size())	//no data
	{
		//处理结束
		FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
		return TRUE;
	}
	
	DealFileList();

	//处理结束
	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
	
	return TRUE;
	
}

BOOL CSearchSock_AreaEvent::GetTime(const BYTE* const pData, const int nCount,int& ioffset)
{
	PPM_OS::memcpy(&tm_Time_Span.istime,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Time_Span.ietime,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	tm_Time_Span.ConvertToNetSeq();	
	return TRUE;
}

BOOL CSearchSock_AreaEvent::GetAreaEventSearchAreaInfo(const BYTE* const pData, const int nCount,int& ioffset)
{
	PPM_OS::memcpy(&tm_Search_Area.itllongitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Search_Area.itllatitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Search_Area.ibrlongitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Search_Area.ibrlatitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	tm_Search_Area.ConvertToNetSeq();
    return TRUE;
}

BOOL CSearchSock_AreaEvent::GetFileList(const BYTE* const pData, const int nCount,int& ioffset)
{
	//type
	char tm_sql_part[1024];
	PPM_OS::memset(&tm_sql_part, 0, sizeof(tm_sql_part));
	if(FALSE == CSearch_PI::GetTypeSql(pData,nCount,ioffset,tm_sql_part)){
		return FALSE;
	}

	//longitude and latitude
	if (!GetAreaEventSearchAreaInfo(pData, nCount, ioffset))
	{
		return FALSE;
	}

	PPM_OS::memset(&tm_event_list , 0, sizeof(tm_event_list));

	int itemp;
	PPM_OS::memcpy(&itemp,pData + ioffset,4);

	itemp = MAKEINT_NETSEQ1(itemp);

	if(-1 == itemp)
	{
		MOVENEXT(ioffset,4,nCount)
	}
	else
	{
		if(FALSE == CSearch_PI::GetTextValue_int(pData,nCount,ioffset,tm_event_list)){
			return FALSE;
		}
	}

	char tm_sql[9000];
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec mc_sp_area_event_get %d,%d, %d,%d,%d,%d, '%s'" ,
		tm_Time_Span.istime, 
		tm_Time_Span.ietime,
		tm_Search_Area.itllongitude,
		tm_Search_Area.itllatitude,
		tm_Search_Area.ibrlongitude,
		tm_Search_Area.ibrlatitude,
		tm_sql_part);

	m_map_filelist.clear();
	m_iRowType_ = TYPE_VEC_FILE_LIST_SAMPLE;             //文件id获取 +　采样点数目获取
    SEARCHSQL(m_iRowType_, tm_sql);
	return TRUE;
}
void CSearchSock_AreaEvent::DealFileList(void)
{
	char tm_file[8000];
	int bOnce;
	int cutnum = 600;         //ado对提交的sql语句限制为8000字节以内

	auto p_map = m_map_filelist.begin();   

	for (; p_map != m_map_filelist.end(); p_map++)   
	{
		auto p_vec = p_map->second.begin();	
		PPM_OS::memset(&tm_file, 0, sizeof(tm_file));	
		bOnce = 0;	
		size_t allcount=1;	

		for (int count=1; p_vec != p_map->second.end(); p_vec++, ++count, ++allcount)   
		{   
			if(0 != bOnce)
			{
				PPM_OS::snprintf(tm_file,sizeof(tm_file),"%s,%d",tm_file,*p_vec); 
			}
			else
			{
				PPM_OS::snprintf(tm_file,sizeof(tm_file),"%d",*p_vec); 
				bOnce = 1;
			}	
			if(cutnum == count || allcount == p_map->second.size())
			{
				//获取头文件信息
				SearchLogFileSInfo(p_map->first.strlog.c_str(),  p_map->first.testtype, tm_file, 0);
                char tm_sql[9000];
				PPM_OS::memset(tm_sql, 0, sizeof(tm_sql));
				PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
					"exec mc_sp_area_event_get_1 %d,%d,%d,%d,'%s', '%s','%s'" ,
					tm_Search_Area.itllongitude,
					tm_Search_Area.itllatitude,
					tm_Search_Area.ibrlongitude,
					tm_Search_Area.ibrlatitude,
					tm_event_list,
					p_map->first.strtb.c_str(),
					tm_file);


				m_iRowType_ = TYPE_VEC_NORMAL;
				m_bResponseType_ = RESTYPE_AREA_EVENT;
				SEARCHSQL(m_iRowType_, tm_sql);

				PPM_OS::memset(tm_file, 0, sizeof(tm_file));
				count = 0;
				bOnce = 0;
			}
		}
	}
}
/************************************************************************/
/* CSearchSock_AreaAntennaRev                                           */
/************************************************************************/

BOOL  
CSearchSock_AreaAntennaRev::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{   
	MOVENEXTTEST(ioffset,17,nCount);	
	//time
	if (!GetTime(pData, nCount, ioffset))
	{
		return FALSE;
	}
	
	if (!GetFileList(pData, nCount, ioffset))
	{
		return FALSE;
	}

	if(0 >= m_map_filelist.size())	//no data
	{
		//处理结束
		FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
		return TRUE;
	}

	char tm_file[8000];
	int bOnce;
    int cutnum = 600;         //ado对提交的sql语句限制为8000字节以内
	auto p_map = m_map_filelist.begin();   

  for (; p_map != m_map_filelist.end(); p_map++)
	{
		PPM_OS::memset(&tm_file, 0, sizeof(tm_file));	
		bOnce = 0;	
		size_t allcount=1;	
		auto p_vec = p_map->second.begin();
		for (int count=1; p_vec != p_map->second.end(); p_vec++, ++count, ++allcount)   
		{   
		    if(0 == bOnce)
			{
				 PPM_OS::snprintf(tm_file,sizeof(tm_file),"%d",*p_vec); 
				 bOnce = 1;
			}
		    else
			{
				 PPM_OS::snprintf(tm_file,sizeof(tm_file),"%s,%d",tm_file,*p_vec); 
			}	

			if(cutnum == count || allcount == p_map->second.size())
			{ 
				DealTestTypeData(p_map->first.testtype, p_map->first.strlog, p_map->first.strtb, tm_file);

				PPM_OS::memset(tm_file, 0, sizeof(tm_file));
				count = 0;
				bOnce = 0;
			}
        }         
    } 
	//处理结束
	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);

	return TRUE;
}

BOOL CSearchSock_AreaAntennaRev::GetTime(const BYTE* const pData, const int nCount,int& ioffset)
{
	PPM_OS::memcpy(&tm_Time_Span.istime,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Time_Span.ietime,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	tm_Time_Span.ConvertToNetSeq();	
    return TRUE;
}

BOOL CSearchSock_AreaAntennaRev::GetAreaAntennaRevSearchAreaInfo(const BYTE* const pData, const int nCount,int& ioffset)
{
	PPM_OS::memcpy(&tm_Search_Area.itllongitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Search_Area.itllatitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Search_Area.ibrlongitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Search_Area.ibrlatitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);

	tm_Search_Area.ConvertToNetSeq();	
    return TRUE;
}

BOOL CSearchSock_AreaAntennaRev::GetFileList(const BYTE* const pData, const int nCount,int& ioffset)
{
	//type
	char tm_sql_part[1024];
	PPM_OS::memset(tm_sql_part, 0, sizeof(tm_sql_part));
	if(FALSE == CSearch_PI::GetTypeSql(pData,nCount,ioffset,tm_sql_part)){
		return FALSE;
	}

	//longitude and latitude
	if (!GetAreaAntennaRevSearchAreaInfo(pData, nCount, ioffset))
	{
		return FALSE;
	}

	BYTE btemp = 0xff;
	PPM_OS::memcpy(&btemp,pData + ioffset,1);
	m_Itemp = btemp & 0x000000ff;

	char tm_sql[9000];
	PPM_OS::memset(tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec mc_sp_area_cover_get %d,%d, %d,%d,%d,%d, '%s',%d" ,
		tm_Time_Span.istime, 
		tm_Time_Span.ietime,
		tm_Search_Area.itllongitude,     
		tm_Search_Area.itllatitude,
		tm_Search_Area.ibrlongitude,
		tm_Search_Area.ibrlatitude,
		tm_sql_part,
		m_Itemp);

	m_iRowType_ = TYPE_VEC_FILE_LIST;//文件id获取 +　采样点数目获取
	SEARCHSQL(m_iRowType_, tm_sql);
    return TRUE;
}

void CSearchSock_AreaAntennaRev::DealTestTypeData(int testtype, std::string strlog, std::string strtb, char* tm_file)
{
	if (5 != testtype && 7 != testtype && 9 != testtype)
	{
		if(2 == m_Itemp)
		{
			DealTestTypeOther(testtype, strlog, strtb, tm_file);
		}
		else
		{
			//do nothing...
		}		    
	}
}

void CSearchSock_AreaAntennaRev::DealTestTypeOther(int testtype, std::string strlog, std::string strtb, char* tm_file)
{
	char tm_sql[9000];
	PPM_OS::memset(tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec sp_antanna_rev_get %d,%d, %d,%d,%d,%d, %d,'%s','%s','%s'" ,
		tm_Time_Span.istime, 
		tm_Time_Span.ietime,
		tm_Search_Area.itllongitude,
		tm_Search_Area.itllatitude,
		tm_Search_Area.ibrlongitude,
		tm_Search_Area.ibrlatitude,
		testtype,
		strlog.c_str(),
		strtb.c_str(),
		tm_file);

	char tm_sql2[8000];

	PPM_OS::memset(tm_sql2, 0, sizeof(tm_sql2));
	PPM_OS::snprintf(tm_sql2,sizeof(tm_sql2),"%s,%d",tm_sql,1);
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_ANTENNA_REV_IDLE;
    SEARCHSQL(m_iRowType_, tm_sql2);

	PPM_OS::memset(tm_sql2, 0, sizeof(tm_sql2));
	PPM_OS::snprintf(tm_sql2,sizeof(tm_sql2),"%s,%d",tm_sql,2);
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_ANTENNA_REV_DEDICATE;
    SEARCHSQL(m_iRowType_, tm_sql2);

	PPM_OS::memset(tm_sql2, 0, sizeof(tm_sql2));
	PPM_OS::snprintf(tm_sql2,sizeof(tm_sql2),"%s,%d",tm_sql,3);
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_ANTENNA_REV_GPRS;
    SEARCHSQL(m_iRowType_, tm_sql2);
}


BOOL   
CSearchSock_SiteSimuSample::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{	
	MOVENEXTTEST(ioffset,17,nCount);	
	//time
	if (!GetSiteSimuSampleTimeSpan(pData, nCount, ioffset))
	{
		return FALSE;
	}

	//type
	char tm_sql_part[1024];
	PPM_OS::memset(&tm_sql_part, 0, sizeof(tm_sql_part));
	if(FALSE == CSearch_PI::GetTypeSql(pData,nCount,ioffset,tm_sql_part)){
		return FALSE;
	}

	//longitude and latitude
	if (!GetSiteSimuSampleSearchAreaInfo(pData, nCount, ioffset))
	{
		return FALSE;
	}

	char tm_sql[8000];
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec mc_sp_site_simu_info_get %d,%d, %d,%d,%d,%d, '%s'" ,
		tm_Time_Span.istime, 
		tm_Time_Span.ietime,
		tm_Search_Area.itllongitude,     
		tm_Search_Area.itllatitude,
		tm_Search_Area.ibrlongitude,
		tm_Search_Area.ibrlatitude,
		tm_sql_part);

	m_map_filelist.clear();	
	m_iRowType_ = TYPE_VEC_FILE_LIST;   //文件id获取 +　采样点数目获取

	SEARCHSQL(m_iRowType_, tm_sql);

	if(0 >= m_map_filelist.size())	//no data
	{
		//处理结束
		FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
		return TRUE;
	}

	char tm_file[7000];
	int cutnum = 600;
	int bOnce = 0;
	auto p_map = m_map_filelist.begin();   

	for (; p_map != m_map_filelist.end(); p_map++)   
	{
		auto p_vec = p_map->second.begin();	
		PPM_OS::memset(&tm_file, 0, sizeof(tm_file));
		bOnce = 0;

		size_t allcount=1;

		for (int count=1; p_vec != p_map->second.end(); p_vec++, ++count, ++allcount)
		{
			if(0 == bOnce)
			{
				PPM_OS::snprintf(tm_file,sizeof(tm_file),"%d",*p_vec);
				bOnce = 1;
			}
			else
			{
				PPM_OS::snprintf(tm_file,sizeof(tm_file),"%s,%d",tm_file,*p_vec);
			}

			if(cutnum == count || allcount == p_map->second.size())
			{
				PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
				PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
					"exec mc_sp_site_simu_sample_get  %d,%d,%d,%d, '%s','%s'",
					tm_Search_Area.itllongitude,
					tm_Search_Area.itllatitude,
					tm_Search_Area.ibrlongitude,
					tm_Search_Area.ibrlatitude,
					p_map->first.strtb.c_str(),
					tm_file);

				m_iRowType_ = TYPE_VEC_NORMAL;
				m_bResponseType_ = RESTYPE_SITESIMU_SAMPLE_INFO;
				SEARCHSQL(m_iRowType_, tm_sql);

				PPM_OS::memset(tm_file, 0, sizeof(tm_file));
				count = 0;
				bOnce = 0;				
			}
		}
	}

	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND); 
	return TRUE;
}
BOOL   
CSearchSock_SiteSimuSample::GetSiteSimuSampleTimeSpan(const BYTE* const pData, const int nCount,int& ioffset)
{
	STRU_SEARCH_TIMESPAN tm_Time_Span;
	//time
	PPM_OS::memcpy(&tm_Time_Span.istime,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Time_Span.ietime,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	tm_Time_Span.ConvertToNetSeq();	
    return TRUE;
}

BOOL   
CSearchSock_SiteSimuSample::GetSiteSimuSampleSearchAreaInfo(const BYTE* const pData, const int nCount,int& ioffset)
{
	PPM_OS::memcpy(&tm_Search_Area.itllongitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Search_Area.itllatitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Search_Area.ibrlongitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Search_Area.ibrlatitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);

	tm_Search_Area.ConvertToNetSeq();	
    return TRUE;
}




