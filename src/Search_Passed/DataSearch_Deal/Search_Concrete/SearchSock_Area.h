// SearchSock_AreaCover.h: interface for the CSearchSock_AreaCover class.
//
//////////////////////////////////////////////////////////////////////

#ifndef __SEARCHSOCK_AREACOVER_H__
#define __SEARCHSOCK_AREACOVER_H__

#include "./SearchSock.h"


class CSearchSock_AreaCover : public CSearchSock
{
public:
	CSearchSock_AreaCover();
	virtual ~CSearchSock_AreaCover();

	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);

private:
  BOOL GetTimeSpan(const BYTE* const pData, const int nCount,int& ioffset);
  BOOL GetBaseFileList2(const BYTE* const pData, const int nCount,int& ioffset);

	void (CSearchSock_AreaCover::*pfunc_TestTypeData[256])(int servicetype, std::string strtb, char* tm_file);
	void DealTestType_Default(int servicetype, std::string strtb, char* tm_file);

	void (CSearchSock_AreaCover::*pfunc_ServiceTypeGsm[256])(std::string strtb, char* tm_file);
	void DealTestType_Gsm(int servicetype, std::string strtb, char* tm_file);
	void DealServiceType_Gsm_Default(std::string strtb, char* tm_file);
  void DealServiceType_Gsm_Voice(std::string strtb, char* tm_file);
  void DealServiceType_Gsm_Data(std::string strtb, char* tm_file);
  void DealServiceType_Gsm_Uplink(std::string strtb, char* tm_file);

	void DealTestType_Scan(int servicetype, std::string strtb, char* tm_file);

	void (CSearchSock_AreaCover::*pfunc_ServiceTypeCdma[256])(std::string strtb, char* tm_file);
	void DealTestType_Cdma(int servicetype, std::string strtb, char* tm_file);
	void DealServiceType_Cdma_Default(std::string strtb, char* tm_file);
  void DealServiceType_Cdma_Voice(std::string strtb, char* tm_file);
	void DealServiceType_Cdma_Data(std::string strtb, char* tm_file);

	void (CSearchSock_AreaCover::*pfunc_ServiceTypeCdma2000[256])(std::string strtb, char* tm_file);
	void DealTestType_Cdma2000(int servicetype, std::string strtb, char* tm_file);
	void DealServiceType_Cdma2000_Default(std::string strtb, char* tm_file);
	void DealServiceType_Cdma2000_Data(std::string strtb, char* tm_file);

	void (CSearchSock_AreaCover::*pfunc_ServiceTypeTdscdma[256])(std::string strtb, char* tm_file);
	void DealTestType_Tdscdma(int servicetype, std::string strtb, char* tm_file);
	void DealServiceType_Tdscdma_Default(std::string strtb, char* tm_file);
	void DealServiceType_Tdscdma_Voice(std::string strtb, char* tm_file);
	void DealServiceType_Tdscdma_Data(std::string strtb, char* tm_file);

	void (CSearchSock_AreaCover::*pfunc_ServiceTypeWcdma[256])(std::string strtb, char* tm_file);
	void DealTestType_Wcdma(int servicetype, std::string strtb, char* tm_file);
	void DealServiceType_Wcdma_Default(std::string strtb, char* tm_file);
	void DealServiceType_Wcdma_Voice(std::string strtb, char* tm_file);
	void DealServiceType_Wcdma_Data(std::string strtb, char* tm_file);

	BOOL GetAreaCoverSearchAreaInfo(const BYTE* const pData, const int nCount,int& ioffset);

	STRU_SEARCH_TIMESPAN tm_Time_Span;
	STRU_SEARCH_AREA tm_Search_Area;
	int m_Itemp;
};

class CSearchSock_AreaCoverLeak : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
private:
  BOOL GetTime(const BYTE* const pData, const int nCount,int& ioffset);
  BOOL GetFileList(const BYTE* const pData, const int nCount,int& ioffset);
  void DealTestTypeData(int testtype, std::string strtb, char* tm_file);
	void DealTestTypeTdscdma(std::string strtb, char* tm_file);
  void DealSearchSampleSummary(std::string strtb, char* tm_file);
	BOOL GetAreaCoverLeakSearchAreaInfo(const BYTE* const pData, const int nCount,int& ioffset);

private:
	STRU_SEARCH_TIMESPAN tm_Time_Span;
	STRU_SEARCH_AREA tm_Search_Area;
	int m_Itemp;
};

class CSearchSock_AreaCoverRecent : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);

	virtual BOOL RowResultDeal();

protected:
  std::set<std::string> m_set_grid_recent;
	std::set<int> m_set_grid_fileid_recent;	

	virtual BOOL RowResultDeal_Grid_Idle_Recent();
	virtual BOOL RowResultDeal_Grid_Dedicated_Recent();
	virtual BOOL RowResultDeal_Grid_Gprs_Recent();

private:
	BOOL GetTime(const BYTE* const pData, const int nCount,int& ioffset);
	BOOL GetFileList(const BYTE* const pData, const int nCount,int& ioffset);
  void DealTestTypeGsm(int testtype, std::string strtb, char* tm_file);
	void DealFileList(void);
	void DealGridEventRecent(void);
	BOOL GetAreaCoverRecentSearchAreaInfo(const BYTE* const pData, const int nCount,int& ioffset);

private:
  STRU_SEARCH_TIMESPAN tm_Time_Span;
	STRU_SEARCH_AREA tm_Search_Area;
	int m_Itemp;
};

class CSearchSock_AreaEvent : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);

private:
  BOOL GetTime(const BYTE* const pData, const int nCount,int& ioffset);
	BOOL GetFileList(const BYTE* const pData, const int nCount,int& ioffset);
  void DealFileList(void);
	BOOL GetAreaEventSearchAreaInfo(const BYTE* const pData, const int nCount,int& ioffset);

private:
	STRU_SEARCH_TIMESPAN tm_Time_Span;
  STRU_SEARCH_AREA tm_Search_Area;
	char tm_event_list[255];
};

class CSearchSock_AreaAntennaRev : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);

private:
	BOOL GetTime(const BYTE* const pData, const int nCount,int& ioffset);
	BOOL GetFileList(const BYTE* const pData, const int nCount,int& ioffset);
	void DealTestTypeData(int testtype, std::string strlog, std::string strtb, char* tm_file);
	void DealTestTypeOther(int testtype, std::string strlog, std::string strtb, char* tm_file);
	BOOL GetAreaAntennaRevSearchAreaInfo(const BYTE* const pData, const int nCount,int& ioffset);

private:
	STRU_SEARCH_TIMESPAN tm_Time_Span;
	STRU_SEARCH_AREA tm_Search_Area;
	int m_Itemp;
};

class CSearchSock_SiteSimuSample : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
private:
	BOOL GetSiteSimuSampleSearchAreaInfo(const BYTE* const pData, const int nCount,int& ioffset);
	BOOL GetSiteSimuSampleTimeSpan(const BYTE* const pData, const int nCount,int& ioffset);

private:
	STRU_SEARCH_TIMESPAN tm_Time_Span;
	STRU_SEARCH_AREA tm_Search_Area;
};


#endif
