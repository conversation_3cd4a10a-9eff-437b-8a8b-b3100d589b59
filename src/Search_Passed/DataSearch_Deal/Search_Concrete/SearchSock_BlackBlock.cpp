// SearchSock_BlackBlock.cpp: implementation of the CSearchSock_BlackBlock class.
//
//////////////////////////////////////////////////////////////////////

#include "SearchSock_BlackBlock.h"

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

/************************************************************************
CSearchSock_BlackBlockInfo                                         
************************************************************************/
BOOL  
CSearchSock_BlackBlockInfo::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
   	int itime;
	PPM_OS::memcpy(&itime,pData + ioffset,4);
	itime = MAKEINT_NETSEQ1(itime);
	MOVENEXT(ioffset,4,nCount);

	char tm_sql[1024];
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec mc_sp_blackblock_info_get %d" ,
		itime);
	
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_BLACKBLOCK_INFO;
	if(NULL != m_pDataBaseDeal_)
	{
		m_pDataBaseDeal_->SearchSql(tm_sql,this, m_iRowType_);
	}
	Clear();
	
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec mc_sp_blackblock_event_get %d" ,
		itime);
	
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_BLACKBLOCK_EVENT;
	if(NULL != m_pDataBaseDeal_)
	{
		m_pDataBaseDeal_->SearchSql(tm_sql,this, m_iRowType_);
	}
	Clear();
	
	//处理结束
	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
	
	return TRUE;
}

/************************************************************************
CSearchSock_BlackBlockWcdmaInfo                                         
************************************************************************/
BOOL
CSearchSock_BlackBlockWcdmaInfo::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	int itime;
	PPM_OS::memcpy(&itime,pData + ioffset,4);
	itime = MAKEINT_NETSEQ1(itime);
	MOVENEXT(ioffset,4,nCount);

	int itype;
	PPM_OS::memcpy(&itype,pData + ioffset,4);
	itype = MAKEINT_NETSEQ1(itype);
	MOVENEXT(ioffset,4,nCount);

	char tm_sql[1024];
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec mc_sp_blackblock_w_info_get %d, %d" ,
		itime, itype);

	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_BLACKBLOCK_INFO;
	SEARCHSQL(m_iRowType_, tm_sql);

	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec mc_sp_blackblock_w_event_get %d, %d" ,
		itime, itype);

	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_BLACKBLOCK_EVENT;
	SEARCHSQL(m_iRowType_, tm_sql);

	//处理结束
	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);

	return TRUE;

}

/************************************************************************
CSearchSock_BlackBlockWcdmaIDToInfo                                         
************************************************************************/
BOOL
CSearchSock_BlackBlockWcdmaIDToInfo::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	int iid;
	PPM_OS::memcpy(&iid,pData + ioffset,4);
	iid = MAKEINT_NETSEQ1(iid);
	MOVENEXT(ioffset,4,nCount);

	int itype;
	PPM_OS::memcpy(&itype,pData + ioffset,4);
	itype = MAKEINT_NETSEQ1(itype);
	MOVENEXT(ioffset,4,nCount);

	char tm_sql[1024];
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec mc_sp_blackblock_w_id2info_get %d,%d" ,
		iid,itype);

	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_BLACKBLOCK_INFO;
	SEARCHSQL(m_iRowType_, tm_sql);	

	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec mc_sp_blackblock_w_id2event_get %d,%d" ,
		iid,itype);

	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_BLACKBLOCK_EVENT;
	SEARCHSQL(m_iRowType_, tm_sql);	

	//处理结束
	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);	
	return TRUE;	
}

/************************************************************************
CSearchSock_BlackBlockEdit                                         
************************************************************************/
BOOL  
CSearchSock_BlackBlockEdit::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
   	int iid;
	PPM_OS::memcpy(&iid,pData + ioffset,4);
	iid = MAKEINT_NETSEQ1(iid);
	MOVENEXT(ioffset,4,nCount);
	
	char tm_name[255];
	PPM_OS::memset(&tm_name, 0, sizeof(tm_name));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_name,sizeof(tm_name)));
	
	char tm_reason[2000];
	PPM_OS::memset(&tm_reason, 0, sizeof(tm_reason));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_reason,sizeof(tm_reason)));
	
	char tm_sql[2300];
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec mc_sp_blackblock_info_edit %d ,'%s','%s'" ,
		iid, &tm_name, &tm_reason);
	
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_TEMP_INT;
	if(NULL != m_pDataBaseDeal_)
	{
		m_pDataBaseDeal_->SearchSql(tm_sql,this, m_iRowType_);
	}
	Clear();
	
	//处理结束
	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);	
	return TRUE;
	
}


/************************************************************************
CSearchSock_BlackBlockTdInfo                                        
************************************************************************/
BOOL
CSearchSock_BlackBlockTdInfo::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
   	int itime = 0;
	PPM_OS::memcpy(&itime,pData + ioffset,4);
	itime = MAKEINT_NETSEQ1(itime);
	MOVENEXT(ioffset,4,nCount);
	
	int itype;
	PPM_OS::memcpy(&itype,pData + ioffset,4);
	itype = MAKEINT_NETSEQ1(itype);
	MOVENEXT(ioffset,4,nCount);
	
	char tm_sql[1024];
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec mc_sp_blackblock_td_info_get %d, %d" ,
		itime, itype);
	
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_BLACKBLOCK_INFO;
	if(NULL != m_pDataBaseDeal_)
	{
		m_pDataBaseDeal_->SearchSql(tm_sql,this, m_iRowType_);
	}
	Clear();
	
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec mc_sp_blackblock_td_event_get %d, %d" ,
		itime, itype);
	
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_BLACKBLOCK_EVENT;
	if(NULL != m_pDataBaseDeal_)
	{
		m_pDataBaseDeal_->SearchSql(tm_sql,this, m_iRowType_);
	}
	Clear();
	
	//处理结束
	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
	
	return TRUE;
	
}

/************************************************************************
CSearchSock_BlackBlockTdEdit                                        
************************************************************************/
BOOL  
CSearchSock_BlackBlockTdEdit::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
   	int iid;
	PPM_OS::memcpy(&iid,pData + ioffset,4);
	iid = MAKEINT_NETSEQ1(iid);
	MOVENEXT(ioffset,4,nCount);
	
	char tm_name1[255];
	PPM_OS::memset(&tm_name1, 0, sizeof(tm_name1));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_name1,sizeof(tm_name1)));
	
	char tm_reason1[2000];
	PPM_OS::memset(&tm_reason1, 0, sizeof(tm_reason1));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_reason1,sizeof(tm_reason1)));
	
	char tm_sql[2300];
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec mc_sp_blackblock_td_info_edit %d ,'%s','%s'" ,
		iid, &tm_name1, &tm_reason1);
	
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_TEMP_INT;
	if(NULL != m_pDataBaseDeal_)
	{
		m_pDataBaseDeal_->SearchSql(tm_sql,this, m_iRowType_);
	}
	Clear();
	
	//处理结束
	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);	
	return TRUE;	
}

/************************************************************************
CSearchSock_BlackBlockIdToInfo                                        
************************************************************************/
BOOL
CSearchSock_BlackBlockIdToInfo::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	int iid;
	PPM_OS::memcpy(&iid,pData + ioffset,4);
	iid = MAKEINT_NETSEQ1(iid);
	MOVENEXT(ioffset,4,nCount);

	char tm_sql[1024];
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec mc_sp_blackblock_id2info_get %d " ,
		iid);

	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_BLACKBLOCK_INFO;
	if(NULL != m_pDataBaseDeal_)
	{
		m_pDataBaseDeal_->SearchSql(tm_sql,this, m_iRowType_);
	}
	Clear();

	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec mc_sp_blackblock_id2event_get %d " ,
		iid);

	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_BLACKBLOCK_EVENT;
	if(NULL != m_pDataBaseDeal_)
	{
		m_pDataBaseDeal_->SearchSql(tm_sql,this, m_iRowType_);
	}
	Clear();

	//处理结束
	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);	
	return TRUE;		
}

/************************************************************************
CSearchSock_BlackBlockIdToInfo                                        
************************************************************************/
BOOL
CSearchSock_NewBlackBlockTokenInfo::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	int iid;
	PPM_OS::memcpy(&iid,pData + ioffset,4);
	iid = MAKEINT_NETSEQ1(iid);
	MOVENEXT(ioffset,4,nCount);

	char token[255];
	PPM_OS::memset(&token, 0, sizeof(token));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,token,sizeof(token)));

	char tm_sql[1024];
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec mc_sp_newblackblock_id2info_get %s,%d " ,
		token,
		iid);

	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_BLACKBLOCK_INFO;
	if(NULL != m_pDataBaseDeal_)
	{
		m_pDataBaseDeal_->SearchSql(tm_sql,this, m_iRowType_);
	}
	Clear();

	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec mc_sp_newblackblock_id2event_get %s,%d " ,
		token,
		iid);

	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_BLACKBLOCK_EVENT;
	if(NULL != m_pDataBaseDeal_)
	{
		m_pDataBaseDeal_->SearchSql(tm_sql,this, m_iRowType_);
	}
	Clear();

	//处理结束
	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);	
	return TRUE;		
}

/************************************************************************
CSearchSock_BlackBlockTdIdToInfo                                        
************************************************************************/
BOOL
CSearchSock_BlackBlockTdIdToInfo::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
   	int iid;
	PPM_OS::memcpy(&iid,pData + ioffset,4);
	iid = MAKEINT_NETSEQ1(iid);
	MOVENEXT(ioffset,4,nCount);
	
	int itype;
	PPM_OS::memcpy(&itype,pData + ioffset,4);
	itype = MAKEINT_NETSEQ1(itype);
	MOVENEXT(ioffset,4,nCount);

	char tm_sql[1024];
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec mc_sp_blackblock_td_id2info_get %d, %d" ,
		iid, itype);
	
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_BLACKBLOCK_INFO;
	if(NULL != m_pDataBaseDeal_)
	{
		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
	}	
	Clear();
	
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec mc_sp_blackblock_td_id2event_get %d, %d" ,
		iid, itype);
	
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_BLACKBLOCK_EVENT;
	if(NULL != m_pDataBaseDeal_)
	{
		m_pDataBaseDeal_->SearchSql(tm_sql,this, m_iRowType_);
	}
	Clear();
	
	//处理结束
	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);	
	return TRUE;	
}


/************************************************************************
CSearchSock_ExcelBlockInfo                                        
************************************************************************/
BOOL
CSearchSock_ExcelBlockInfo::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	char tm_sql[8000];

	PPM_OS::memset(tm_sql, 0, sizeof(tm_sql));
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec mc_sp_excelblock_get ");

	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_ExcelBLOCK_INFO;
	if(NULL != m_pDataBaseDeal_)
	{
		m_pDataBaseDeal_->SearchSql(tm_sql,this, m_iRowType_);
	}
	Clear();

	PPM_OS::memset(tm_sql, 0, sizeof(tm_sql));	
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec mc_sp_excelblock_item_get ");

	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_ExcelBLOCK_Item_INFO;
	if(NULL != m_pDataBaseDeal_)
	{
		m_pDataBaseDeal_->SearchSql(tm_sql,this, m_iRowType_);
	}
	Clear();


	//处理结束
	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
	return true;
}


/************************************************************************
CSearchSock_ExcelBlockLocalInfo                                        
************************************************************************/
BOOL
CSearchSock_ExcelBlockLocalInfo::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	char tm_sql[8000];

	PPM_OS::memset(tm_sql, 0, sizeof(tm_sql));
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec mc_sp_excelblock_local_get ");

	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_ExcelBLOCK_Local_INFO;
	if(NULL != m_pDataBaseDeal_)
	{
		m_pDataBaseDeal_->SearchSql(tm_sql,this, m_iRowType_);
	}
	Clear();

	PPM_OS::memset(tm_sql, 0, sizeof(tm_sql));	
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec mc_sp_excelblock_local_item_get ");

	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_ExcelBLOCK_Local_Item_INFO;
	if(NULL != m_pDataBaseDeal_)
	{
		m_pDataBaseDeal_->SearchSql(tm_sql,this, m_iRowType_);
	}
	Clear();

	//处理结束
	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
	return true;
}

/************************************************************************
CSearchSock_NewBlockInfo                                        
************************************************************************/
BOOL
CSearchSock_NewBlockInfo::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	char tm_sql[8000];	
	PPM_OS::memset(tm_sql, 0, sizeof(tm_sql));
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"select block_id,new_block_id from tb_complain_item_newblock_ana ");	

	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_NEW_BLOCK_ID;
	SEARCHSQL(m_iRowType_, tm_sql);

	PPM_OS::memset(tm_sql, 0, sizeof(tm_sql));
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"select new_block_id,week_monday,hot_status,black_status,hot_intending,black_intending,\
user_count,bill_count,areaDes,jdDes,reasonDes,solutionDes,HotWeekScale,MainOldBlock from tb_complain_report_ana ");	
	
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_NEW_BLOCK_INFO;
	SEARCHSQL(m_iRowType_, tm_sql);

	//处理结束
	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
	return true;
}


















