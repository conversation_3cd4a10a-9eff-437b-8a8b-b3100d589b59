// SearchSock_BlackBlock.h: interface for the CSearchSock_BlackBlock class.
//
//////////////////////////////////////////////////////////////////////

#ifndef __SEARCHSOCK_BLACKBLOCK_H__
#define __SEARCHSOCK_BLACKBLOCK_H__

#include "./SearchSock.h"


class CSearchSock_BlackBlockInfo : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};


class CSearchSock_BlackBlockEdit : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};


class CSearchSock_BlackBlockTdInfo : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};

class CSearchSock_BlackBlockWcdmaInfo : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};

class CSearchSock_BlackBlockWcdmaIDToInfo : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};

class CSearchSock_NewBlackBlockTokenInfo : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};


class CSearchSock_BlackBlockTdEdit : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};


class CSearchSock_BlackBlockIdToInfo : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};

class CSearchSock_BlackBlockTdIdToInfo : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};

class CSearchSock_ExcelBlockInfo : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};

class CSearchSock_ExcelBlockLocalInfo : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};

class CSearchSock_NewBlockInfo : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};

#endif
