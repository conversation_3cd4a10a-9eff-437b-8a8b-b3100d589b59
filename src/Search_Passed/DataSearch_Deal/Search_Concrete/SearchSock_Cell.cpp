// SearchSock_Cell.cpp: implementation of the CSearchSock_Cell class.
//
//////////////////////////////////////////////////////////////////////

#include "SearchSock_Cell.h"

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

/************************************************************************/
/*  CSearchSock_CellCover                                               */
/************************************************************************/
CSearchSock_CellCover::CSearchSock_CellCover()
{
	for (int i = 0; i < 256; i++)
	{
		pfunc_TestTypeData[i] = &CSearchSock_CellCover::DealTestTypeDataDefault;
		pfunc_ServiceTypeGsm[i] = &CSearchSock_CellCover::DealServiceTypeGsmDefault;
		pfunc_ServiceTypeScan[i] = &CSearchSock_CellCover::DealServiceTypeScanDefault;
    pfunc_ServiceTypeCdma[i] = &CSearchSock_CellCover::DealServiceTypeCdmaDefault;
		pfunc_ServiceTypeTdscdma[i] = &CSearchSock_CellCover::DealServiceTypeTdscdmaDefault;
		pfunc_ServiceTypeWcdma[i] = &CSearchSock_CellCover::DealServiceTypeWcdmaDefault;
	}
	
	//test type
	pfunc_TestTypeData[CEnumDef::T_GSM_DT] = &CSearchSock_CellCover::DealTestTypeDataGsm;
	pfunc_TestTypeData[CEnumDef::T_GSM_CQT] = &CSearchSock_CellCover::DealTestTypeDataGsm;
	pfunc_TestTypeData[CEnumDef::T_AUTODT] = &CSearchSock_CellCover::DealTestTypeDataGsm;
	pfunc_TestTypeData[CEnumDef::T_AUTODT_TA] = &CSearchSock_CellCover::DealTestTypeDataGsm;
	pfunc_TestTypeData[CEnumDef::T_AUTOCQT] = &CSearchSock_CellCover::DealTestTypeDataGsm;

	pfunc_TestTypeData[CEnumDef::T_SCANTEST] = &CSearchSock_CellCover::DealTestTypeDataScan;

	pfunc_TestTypeData[CEnumDef::T_CDMA_DT] = &CSearchSock_CellCover::DealTestTypeDataCdma;
	pfunc_TestTypeData[CEnumDef::T_CDMA_CQT] = &CSearchSock_CellCover::DealTestTypeDataCdma;

	pfunc_TestTypeData[CEnumDef::T_TDSCDMA_DT] = &CSearchSock_CellCover::DealTestTypeDataTdscdma;
	pfunc_TestTypeData[CEnumDef::T_TDSCDMA_CQT] = &CSearchSock_CellCover::DealTestTypeDataTdscdma;

	pfunc_TestTypeData[CEnumDef::T_WCDMA_DT] = &CSearchSock_CellCover::DealTestTypeDataWcdma;
	pfunc_TestTypeData[CEnumDef::T_WCDMA_CQT] = &CSearchSock_CellCover::DealTestTypeDataWcdma;

    //service type
	pfunc_ServiceTypeGsm[CEnumDef::Ser_GSM_VOICE] = &CSearchSock_CellCover::DealServiceTypeGsmVoice;
	pfunc_ServiceTypeGsm[CEnumDef::Ser_GSM_IDLE] = &CSearchSock_CellCover::DealServiceTypeGsmVoice;
	pfunc_ServiceTypeGsm[CEnumDef::Ser_GPRS_DATA] = &CSearchSock_CellCover::DealServiceTypeGsmData;
	pfunc_ServiceTypeGsm[CEnumDef::Ser_EDGE_DATA] = &CSearchSock_CellCover::DealServiceTypeGsmData;

	pfunc_ServiceTypeScan[CEnumDef::Ser_SCAN] = &CSearchSock_CellCover::DealServiceTypeScanNormal;
	pfunc_ServiceTypeScan[CEnumDef::Ser_SCAN_TD] = &CSearchSock_CellCover::DealServiceTypeScanTd;

	pfunc_ServiceTypeCdma[CEnumDef::Ser_CDMA_VOICE] = &CSearchSock_CellCover::DealServiceTypeCdmaVoice;
	pfunc_ServiceTypeCdma[CEnumDef::Ser_CDMA1X_DATA] = &CSearchSock_CellCover::DealServiceTypeCdmaData;

	pfunc_ServiceTypeTdscdma[CEnumDef::Ser_TDSCDMA_VIDEO] = &CSearchSock_CellCover::DealServiceTypeTdscdmaVoice;
	pfunc_ServiceTypeTdscdma[CEnumDef::Ser_TDSCDMA_VOICE] = &CSearchSock_CellCover::DealServiceTypeTdscdmaVoice;
	pfunc_ServiceTypeTdscdma[CEnumDef::Ser_TDSCDMA_IDLE] = &CSearchSock_CellCover::DealServiceTypeTdscdmaVoice;
	pfunc_ServiceTypeTdscdma[CEnumDef::Ser_TDSCDMA_DATA] = &CSearchSock_CellCover::DealServiceTypeTdscdmaData;
	pfunc_ServiceTypeTdscdma[CEnumDef::Ser_TDSCDMA_HSDPA] = &CSearchSock_CellCover::DealServiceTypeTdscdmaData;

	pfunc_ServiceTypeWcdma[CEnumDef::Ser_WCDMA_VIDEO] = &CSearchSock_CellCover::DealServiceTypeWcdmaVoice;
	pfunc_ServiceTypeWcdma[CEnumDef::Ser_WCDMA_VOICE] = &CSearchSock_CellCover::DealServiceTypeWcdmaVoice;
	pfunc_ServiceTypeWcdma[CEnumDef::Ser_WCDMA_DATA] = &CSearchSock_CellCover::DealServiceTypeWcdmaData;
	pfunc_ServiceTypeWcdma[CEnumDef::Ser_WCDMA_HSDPA] = &CSearchSock_CellCover::DealServiceTypeWcdmaData;


}
CSearchSock_CellCover::~CSearchSock_CellCover()
{

}
/************************************************************************
基本查询条件
＋
小区查询条件
1)	iLAC(4BYTES) : int
2)	wRAC(2BYTES) : smallint
3)	iCI(4BYTES) : int
＋
1)	覆盖查询类型（1BYTE）：0:概要，1:详细，2:基于栅格指标查询
************************************************************************/
BOOL  
CSearchSock_CellCover::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	//time
	if (!GetTimeSpan(pData, nCount, ioffset))
	{
		return FALSE;
	}
	
	if (!GetBaseFileList(pData, nCount, ioffset))
	{
		return FALSE;
	}
	
	if(0 >= m_map_filelist2.size())	//no data
	{
		//处理结束
		FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
		return TRUE;
	}

  auto p_map = m_map_filelist2.begin();   
  char tm_file[8000];
	int bOnce;		
  int cutnum = 600;         //ado对提交的sql语句限制为8000字节以内

  for (; p_map != m_map_filelist2.end(); p_map++)   
	{
		auto p_vec = p_map->second.begin();	
		PPM_OS::memset(&tm_file, 0, sizeof(tm_file));	
		bOnce = 0;	
		size_t allcount=1;	

		for (int count=1; p_vec != p_map->second.end(); p_vec++, ++allcount, ++count)   
		{   
			if(0 == bOnce)
			{
				PPM_OS::snprintf(tm_file,sizeof(tm_file),"%d",*p_vec); 
				bOnce = 1;
			}
			else
			{
					PPM_OS::snprintf(tm_file,sizeof(tm_file),"%s,%d",tm_file,*p_vec); 
			}

			if(cutnum == count || allcount == p_map->second.size())
			{ 
				//获取头文件信息
				SearchLogFileSInfo(p_map->first.strlog.c_str(),  p_map->first.testtype, tm_file, 0);

				(this->*pfunc_TestTypeData[p_map->first.testtype])(p_map->first, tm_file);
												
				PPM_OS::memset(tm_file, 0, sizeof(tm_file));
				count = 0;
				bOnce = 0;
			}
		}         
	}     

	//处理结束
	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
	return TRUE;	
}

BOOL CSearchSock_CellCover::GetTimeSpan(const BYTE* const pData, const int nCount,int& ioffset)
{
	PPM_OS::memcpy(&tm_Time_Span.istime,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Time_Span.ietime,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	tm_Time_Span.ConvertToNetSeq();	
    return TRUE;
}
BOOL CSearchSock_CellCover::GetAreaInfo(const BYTE* const pData, const int nCount,int& ioffset)
{
	//longitude and latitude
	PPM_OS::memcpy(&tm_Search_Area.itllongitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Search_Area.itllatitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Search_Area.ibrlongitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Search_Area.ibrlatitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);

	tm_Search_Area.ConvertToNetSeq();

	return TRUE;
}
BOOL CSearchSock_CellCover::GetCellInfo(const BYTE* const pData, const int nCount,int& ioffset)
{
	PPM_OS::memcpy(&tm_Search_Cell.iLAC,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Search_Cell.wRAC,pData + ioffset,2);
	MOVENEXT(ioffset,2,nCount);
	PPM_OS::memcpy(&tm_Search_Cell.iCI,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);

	tm_Search_Cell.ConvertToNetSeq();
    return TRUE;
}
BOOL CSearchSock_CellCover::GetBaseFileList(const BYTE* const pData, const int nCount,int& ioffset)
{
	//type
	char tm_sql_part[1024];
	PPM_OS::memset(tm_sql_part, 0, sizeof(tm_sql_part));
	if(FALSE == CSearch_PI::GetTypeSql(pData,nCount,ioffset,tm_sql_part)){
		return FALSE;
	}

	if (!GetAreaInfo(pData, nCount, ioffset))
	{
		return FALSE;
	}

	if (!GetCellInfo(pData, nCount, ioffset))
	{
		return FALSE;
	}

	BYTE btemp;
	PPM_OS::memcpy(&btemp,pData + ioffset,1);
	m_Itemp = btemp & 0x000000ff;

	char tm_sql[9000];	
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_cell_cover_get %d,%d, %d,%d,%d,%d, %d,%d,%d, '%s',%d",
		tm_Time_Span.istime, 
		tm_Time_Span.ietime,
		tm_Search_Area.itllongitude,
		tm_Search_Area.itllatitude,
		tm_Search_Area.ibrlongitude,
		tm_Search_Area.ibrlatitude,
		tm_Search_Cell.iLAC,
		tm_Search_Cell.wRAC,
		tm_Search_Cell.iCI,
		tm_sql_part,
		m_Itemp);

	m_map_filelist2.clear();
	m_iRowType_ = TYPE_VEC_FILE_LIST_SAMPLE2;
  SEARCHSQL(m_iRowType_, tm_sql);

	return TRUE;
}

void CSearchSock_CellCover::DealTestTypeDataDefault(STRU_FILE_LIST2 stru_filelist, char* tm_file)
{

}
void CSearchSock_CellCover::DealTestTypeDataGsm(STRU_FILE_LIST2 stru_filelist, char* tm_file)
{
    (this->*pfunc_ServiceTypeGsm[stru_filelist.servicetype])(stru_filelist, tm_file);
}
void CSearchSock_CellCover::DealTestTypeDataScan(STRU_FILE_LIST2 stru_filelist, char* tm_file)
{
    (this->*pfunc_ServiceTypeScan[stru_filelist.servicetype])(stru_filelist, tm_file);
}
void CSearchSock_CellCover::DealTestTypeDataCdma(STRU_FILE_LIST2 stru_filelist, char* tm_file)
{
    (this->*pfunc_ServiceTypeCdma[stru_filelist.servicetype])(stru_filelist, tm_file);
}
void CSearchSock_CellCover::DealTestTypeDataTdscdma(STRU_FILE_LIST2 stru_filelist, char* tm_file)
{
    (this->*pfunc_ServiceTypeTdscdma[stru_filelist.servicetype])(stru_filelist, tm_file);
}
void CSearchSock_CellCover::DealTestTypeDataWcdma(STRU_FILE_LIST2 stru_filelist, char* tm_file)
{
    (this->*pfunc_ServiceTypeWcdma[stru_filelist.servicetype])(stru_filelist, tm_file);
}

void CSearchSock_CellCover::DealServiceTypeGsmDefault(STRU_FILE_LIST2 stru_filelist, char* tm_file)
{

}
void CSearchSock_CellCover::DealServiceTypeGsmVoice(STRU_FILE_LIST2 stru_filelist, char* tm_file)
{
	char tm_sql[9000];
	PPM_OS::memset(tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec mc_sp_cell_cover_gsm_dt_get %d,%d, %d,%d,%d, '%s','%s',%d" ,
		tm_Time_Span.istime, 
		tm_Time_Span.ietime,
		tm_Search_Cell.iLAC,
		tm_Search_Cell.wRAC,
		tm_Search_Cell.iCI,
		stru_filelist.strtb.c_str(),
		tm_file,
		m_Itemp);

	if(0 == m_Itemp)
	{				
		m_iRowType_ = TYPE_VEC_NORMAL;
		m_bResponseType_ = RESTYPE_CELL_COVER_SUMMARY;
		SEARCHSQL(m_iRowType_, tm_sql);
	}
	else if(1 == m_Itemp)
	{							
		m_iRowType_ = TYPE_VEC_NORMAL;
		m_bResponseType_ = RESTYPE_AREA_COVER_DETAIL;
		SEARCHSQL(m_iRowType_, tm_sql);
	}
	else if(2 == m_Itemp)
	{
		PPM_OS::memset(tm_sql, 0, sizeof(tm_sql));
		PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
			"exec mc_sp_cell_cover_gsm_grid_get %d,%d, %d,%d,%d, '%s','%s'" ,
			tm_Time_Span.istime, 
			tm_Time_Span.ietime,
			tm_Search_Cell.iLAC,
			tm_Search_Cell.wRAC,
			tm_Search_Cell.iCI,
			stru_filelist.strtb.c_str(),
			tm_file);

		char tm_sql2[8000];
		PPM_OS::memset(tm_sql2, 0, sizeof(tm_sql2));
		PPM_OS::snprintf(tm_sql2,sizeof(tm_sql2),"%s,%d",tm_sql,1);
		m_iRowType_ = TYPE_VEC_NORMAL;
		m_bResponseType_ = RESTYPE_CELL_COVER_GRID_IDLE;
    SEARCHSQL(m_iRowType_, tm_sql2);

		PPM_OS::memset(tm_sql2, 0, sizeof(tm_sql2));
		PPM_OS::snprintf(tm_sql2,sizeof(tm_sql2),"%s,%d",tm_sql,2);
		m_iRowType_ = TYPE_VEC_NORMAL;
		m_bResponseType_ = RESTYPE_CELL_COVER_GRID_DEDICATED;
		SEARCHSQL(m_iRowType_, tm_sql2);
	}
}
void CSearchSock_CellCover::DealServiceTypeGsmData(STRU_FILE_LIST2 stru_filelist, char* tm_file)
{
	char tm_sql[9000];
	PPM_OS::memset(tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec mc_sp_cell_cover_gsm_d_dt_get %d,%d, %d,%d,%d, '%s','%s',%d" ,
		tm_Time_Span.istime, 
		tm_Time_Span.ietime,
		tm_Search_Cell.iLAC,
		tm_Search_Cell.wRAC,
		tm_Search_Cell.iCI,
		stru_filelist.strtb.c_str(),
		tm_file,
		m_Itemp);

	if(0 == m_Itemp)
	{				
		m_iRowType_ = TYPE_VEC_REVIEW_DATA_SUMMARY;
		m_bResponseType_ = RESTYPE_CELL_COVER_GPRS_SUMMARY;
		SEARCHSQL(m_iRowType_, tm_sql);
	}
	else if(1 == m_Itemp)
	{							

	}
	else if(2 == m_Itemp)
	{
		PPM_OS::memset(tm_sql, 0, sizeof(tm_sql));
		PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
			"exec mc_sp_cell_cover_gsm_grid_get %d,%d, %d,%d,%d, '%s','%s'" ,
			tm_Time_Span.istime, 
			tm_Time_Span.ietime,
			tm_Search_Cell.iLAC,
			tm_Search_Cell.wRAC,
			tm_Search_Cell.iCI,
			stru_filelist.strtb.c_str(),
			tm_file);

		char tm_sql2[8000];									 
		PPM_OS::memset(tm_sql2, 0, sizeof(tm_sql2));
		PPM_OS::snprintf(tm_sql2,sizeof(tm_sql2),"%s,%d",tm_sql,3);
		m_iRowType_ = TYPE_VEC_GIS_DATA;
		m_bResponseType_ = RESTYPE_CELL_COVER_GRID_GPRS;
		SEARCHSQL(m_iRowType_, tm_sql2);
	}
}

void CSearchSock_CellCover::DealServiceTypeScanDefault(STRU_FILE_LIST2 stru_filelist, char* tm_file)
{

}
void CSearchSock_CellCover::DealServiceTypeScanNormal(STRU_FILE_LIST2 stru_filelist, char* tm_file)
{
	char tm_sql[9000];
	PPM_OS::memset(tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec mc_sp_cell_cover_scan_get %d,%d, %d,%d,%d, '%s', '%s','%s',%d" ,
		tm_Time_Span.istime, 
		tm_Time_Span.ietime,
		tm_Search_Cell.iLAC,
		tm_Search_Cell.wRAC,
		tm_Search_Cell.iCI,
		stru_filelist.strlog.c_str(),
		stru_filelist.strtb.c_str(),
		tm_file,
		m_Itemp);

	if(0 == m_Itemp)
	{
		m_iRowType_ = TYPE_VEC_NORMAL;
		m_bResponseType_ = RESTYPE_AREA_COVER_SCAN_SUMMARY;
		SEARCHSQL(m_iRowType_, tm_sql);
	}
	else if(1 == m_Itemp)
	{
		m_iRowType_ = TYPE_VEC_NORMAL;
		m_bResponseType_ = RESTYPE_AREA_COVER_SCAN_DETAIL;
		SEARCHSQL(m_iRowType_, tm_sql);
	}
	else if(2 == m_Itemp)
	{
		m_iRowType_ = TYPE_VEC_NORMAL;
		m_bResponseType_ = RESTYPE_AREA_COVER_GRID_SCAN;
		SEARCHSQL(m_iRowType_, tm_sql);
	}
}
void CSearchSock_CellCover::DealServiceTypeScanTd(STRU_FILE_LIST2 stru_filelist, char* tm_file)
{
	char tm_sql[9000];
	PPM_OS::memset(tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec mc_sp_cell_cover_scan_td_get %d,%d, %d,%d,%d, '%s', '%s','%s'" ,
		tm_Time_Span.istime, 
		tm_Time_Span.ietime,
		tm_Search_Cell.iLAC,
		tm_Search_Cell.wRAC,
		tm_Search_Cell.iCI,
		stru_filelist.strlog.c_str(),
		stru_filelist.strtb.c_str(),
		tm_file);

	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_CELL_COVER_SCAN_TD;
    SEARCHSQL(m_iRowType_, tm_sql);
}

void CSearchSock_CellCover::DealServiceTypeCdmaDefault(STRU_FILE_LIST2 stru_filelist, char* tm_file)
{

}
void CSearchSock_CellCover::DealServiceTypeCdmaVoice(STRU_FILE_LIST2 stru_filelist, char* tm_file)
{
	char tm_sql[9000];
	PPM_OS::memset(tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec mc_sp_cell_cover_cdma_dt_get %d,%d, %d,%d,%d, '%s','%s', %d" ,
		tm_Time_Span.istime, 
		tm_Time_Span.ietime,
		tm_Search_Cell.iLAC,
		tm_Search_Cell.wRAC,
		tm_Search_Cell.iCI,
		stru_filelist.strtb.c_str(),
		tm_file, 
		m_Itemp);

	if(0 == m_Itemp)
	{	
		m_bResponseType_ = RESTYPE_CELL_COVER_CDMA_SUMMARY;
		m_iRowType_ = TYPE_VEC_NORMAL;
		SEARCHSQL(m_iRowType_, tm_sql);
	}
	if(1 == m_Itemp)
	{	

	}
	else if(2 == m_Itemp)
	{
		PPM_OS::memset(tm_sql, 0, sizeof(tm_sql));
		PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
			"exec mc_sp_cell_cover_cdma_grid_get %d,%d, %d,%d,%d, %d,'%s','%s','%s'" ,
			tm_Time_Span.istime, 
			tm_Time_Span.ietime,
			tm_Search_Cell.iLAC,
			tm_Search_Cell.wRAC,
			tm_Search_Cell.iCI,
			stru_filelist.testtype,
			stru_filelist.strlog.c_str(),
			stru_filelist.strtb.c_str(),
			tm_file);

		char tm_sql2[8000];
		PPM_OS::memset(tm_sql2, 0, sizeof(tm_sql2));
		PPM_OS::snprintf(tm_sql2,sizeof(tm_sql2),"%s,%d",tm_sql,1);
		m_iRowType_ = TYPE_VEC_NORMAL;
		m_bResponseType_ = RESTYPE_CELL_COVER_GRID_CDMA;
    SEARCHSQL(m_iRowType_, tm_sql2);
	}
}
void CSearchSock_CellCover::DealServiceTypeCdmaData(STRU_FILE_LIST2 stru_filelist, char* tm_file)
{
	char tm_sql[9000];
	PPM_OS::memset(tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec mc_sp_cell_cover_cdma_dt_get %d,%d, %d,%d,%d, '%s','%s', %d" ,
		tm_Time_Span.istime, 
		tm_Time_Span.ietime,
		tm_Search_Cell.iLAC,
		tm_Search_Cell.wRAC,
		tm_Search_Cell.iCI,
		stru_filelist.strtb.c_str(),
		tm_file, 
		m_Itemp);

	if(0 == m_Itemp)
	{	
		m_iRowType_ = TYPE_VEC_NORMAL;
		m_bResponseType_ = RESTYPE_CELL_COVER_CDMA_DATA_SUMMARY;
		SEARCHSQL(m_iRowType_, tm_sql);
	}
	if(1 == m_Itemp){	

	}
	else if(2 == m_Itemp)
	{
		PPM_OS::memset(tm_sql, 0, sizeof(tm_sql));
		PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
            "exec mc_sp_cell_cover_cdma_grid_get %d,%d, %d,%d,%d, %d,'%s','%s','%s'",
			tm_Time_Span.istime, tm_Time_Span.ietime,
			tm_Search_Cell.iLAC,
			tm_Search_Cell.wRAC,
			tm_Search_Cell.iCI,
			stru_filelist.testtype,
			stru_filelist.strlog.c_str(),
			stru_filelist.strtb.c_str(),
			tm_file);

		char tm_sql2[8000];
		PPM_OS::memset(tm_sql2, 0, sizeof(tm_sql2));
		PPM_OS::snprintf(tm_sql2,sizeof(tm_sql2),"%s,%d",tm_sql,2);
		m_iRowType_ = TYPE_VEC_GIS_DATA;
		m_bResponseType_ = RESTYPE_CELL_COVER_GRID_CDMA_DATA;
		SEARCHSQL(m_iRowType_, tm_sql2);
	}
}

void CSearchSock_CellCover::DealServiceTypeTdscdmaDefault(STRU_FILE_LIST2 stru_filelist, char* tm_file)
{

}
void CSearchSock_CellCover::DealServiceTypeTdscdmaVoice(STRU_FILE_LIST2 stru_filelist, char* tm_file)
{
	char tm_sql[9000];
	PPM_OS::memset(tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec mc_sp_cell_cover_tdscdma_dt_get %d,%d, %d,%d,%d, '%s','%s',%d" ,
		tm_Time_Span.istime, 
		tm_Time_Span.ietime,
		tm_Search_Cell.iLAC,
		tm_Search_Cell.wRAC,
		tm_Search_Cell.iCI,
		stru_filelist.strtb.c_str(),
		tm_file,
		m_Itemp);

	if(0 == m_Itemp)
	{
		m_iRowType_ = TYPE_VEC_NORMAL;
		m_bResponseType_ = RESTYPE_CELL_COVER_TDSCDMA_SUMMARY;
		SEARCHSQL(m_iRowType_, tm_sql);
	}
	else if(1 == m_Itemp)
	{
		m_iRowType_ = TYPE_VEC_NORMAL;
		m_bResponseType_ = RESTYPE_CELL_COVER_TDSCDMA_NORMAL;
		SEARCHSQL(m_iRowType_, tm_sql);
	}
	else if(2 == m_Itemp)
	{
		PPM_OS::memset(tm_sql, 0, sizeof(tm_sql));
		PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
			"exec mc_sp_cell_cover_tdscdma_grid_get %d,%d, %d,%d,%d, '%s','%s'" ,
			tm_Time_Span.istime, 
			tm_Time_Span.ietime,
			tm_Search_Cell.iLAC,
			tm_Search_Cell.wRAC,
			tm_Search_Cell.iCI,
			stru_filelist.strtb.c_str(),
			tm_file);

		m_bResponseType_ = RESTYPE_CELL_COVER_GRID_TDSCDMA_AMR;
		m_iRowType_ = TYPE_VEC_NORMAL;
		char tm_sql2[8000];
		PPM_OS::memset(tm_sql2, 0, sizeof(tm_sql2));
		PPM_OS::snprintf(tm_sql2, sizeof(tm_sql2), "%s,%d", tm_sql, 1);
		SEARCHSQL(m_iRowType_, tm_sql2);

		m_iRowType_ = TYPE_VEC_NORMAL;
		m_bResponseType_ = RESTYPE_CELL_COVER_GRID_TDSCDMA_VP;
		PPM_OS::memset(tm_sql2, 0, sizeof(tm_sql2));
		PPM_OS::snprintf(tm_sql2, sizeof(tm_sql2), "%s,%d", tm_sql, 3);
		SEARCHSQL(m_iRowType_, tm_sql2);
	}
}
void CSearchSock_CellCover::DealServiceTypeTdscdmaData(STRU_FILE_LIST2 stru_filelist, char* tm_file)
{
	char tm_sql[9000];
	PPM_OS::memset(tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec mc_sp_cell_cover_tdscdma_d_dt_get %d,%d, %d,%d,%d, '%s','%s',%d" ,
		tm_Time_Span.istime, 
		tm_Time_Span.ietime,
		tm_Search_Cell.iLAC,
		tm_Search_Cell.wRAC,
		tm_Search_Cell.iCI,
		stru_filelist.strtb.c_str(),
		tm_file,
		m_Itemp);

	if(0 == m_Itemp)
	{
		m_iRowType_ = TYPE_VEC_NORMAL;
		m_bResponseType_ = RESTYPE_CELL_COVER_TDSCDMA_DATA_SUMMARY;
		SEARCHSQL(m_iRowType_, tm_sql);
	}
	else if(1 == m_Itemp)
	{

	}
	else if(2 == m_Itemp)
	{
		PPM_OS::memset(tm_sql, 0, sizeof(tm_sql));
		PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
			"exec mc_sp_cell_cover_tdscdma_grid_get %d,%d, %d,%d,%d, '%s','%s'" ,
			tm_Time_Span.istime, 
			tm_Time_Span.ietime,
			tm_Search_Cell.iLAC,
			tm_Search_Cell.wRAC,
			tm_Search_Cell.iCI,
			stru_filelist.strtb.c_str(),
			tm_file);

		m_iRowType_ = TYPE_VEC_GIS_DATA;
		m_bResponseType_ = RESTYPE_CELL_COVER_GRID_TDSCDMA_PS;
		char tm_sql2[8000];
		PPM_OS::memset(&tm_sql2, 0, sizeof(tm_sql2));
		PPM_OS::snprintf(tm_sql2, sizeof(tm_sql2), "%s,%d", tm_sql, 2);
    SEARCHSQL(m_iRowType_, tm_sql2);
	}
}

void CSearchSock_CellCover::DealServiceTypeWcdmaDefault(STRU_FILE_LIST2 stru_filelist, char* tm_file)
{

}
void CSearchSock_CellCover::DealServiceTypeWcdmaVoice(STRU_FILE_LIST2 stru_filelist, char* tm_file)
{
	char tm_sql[9000];
	//WCDMA Voice
	PPM_OS::memset(tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec mc_sp_cell_cover_wcdma_dt_get %d,%d, %d,%d,%d, '%s','%s',%d" ,
		tm_Time_Span.istime, 
		tm_Time_Span.ietime,
		tm_Search_Cell.iLAC,
		tm_Search_Cell.wRAC,
		tm_Search_Cell.iCI,
		stru_filelist.strtb.c_str(),
		tm_file,
		m_Itemp);

	if (0 == m_Itemp)
	{			  
		m_bResponseType_ = RESTYPE_CELL_COVER_WCDMA_SUMMARY;
		m_iRowType_ = TYPE_VEC_NORMAL;
		SEARCHSQL(m_iRowType_, tm_sql);
	}
	else if (1 == m_Itemp)
	{

	}
	else if (2 == m_Itemp)
	{
		PPM_OS::memset(tm_sql, 0, sizeof(tm_sql));
		PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
            "exec mc_sp_cell_cover_wcdma_grid_get %d,%d, %d,%d,%d, '%s','%s'" ,
            tm_Time_Span.istime, 
            tm_Time_Span.ietime,
            tm_Search_Cell.iLAC, tm_Search_Cell.wRAC,
            tm_Search_Cell.iCI,
            stru_filelist.strtb.c_str(),
            tm_file);

		//amr
		m_iRowType_ = TYPE_VEC_NORMAL;
		m_bResponseType_ = RESTYPE_CELL_COVER_GRID_WCDMA_AMR;

		char tm_sql2[8000];
		PPM_OS::memset(tm_sql2, 0, sizeof(tm_sql2));
		PPM_OS::snprintf(tm_sql2, sizeof(tm_sql2), "%s,%d", tm_sql, 1);
    SEARCHSQL(m_iRowType_, tm_sql2);

		//vp
		m_iRowType_ = TYPE_VEC_NORMAL;
		m_bResponseType_ = RESTYPE_CELL_COVER_GRID_WCDMA_VP;
		PPM_OS::memset(tm_sql2, 0, sizeof(tm_sql2));
		PPM_OS::snprintf(tm_sql2, sizeof(tm_sql2), "%s,%d", tm_sql, 3);
		SEARCHSQL(m_iRowType_, tm_sql2);
	}
}
void CSearchSock_CellCover::DealServiceTypeWcdmaData(STRU_FILE_LIST2 stru_filelist, char* tm_file)
{
	char tm_sql[9000];
	//WCDMA DATA
	PPM_OS::memset(tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec mc_sp_cell_cover_wcdma_d_dt_get %d,%d, %d,%d,%d, '%s','%s',%d" ,
		tm_Time_Span.istime, 
		tm_Time_Span.ietime,
		tm_Search_Cell.iLAC,
		tm_Search_Cell.wRAC,
		tm_Search_Cell.iCI,
		stru_filelist.strtb.c_str(),
		tm_file,
		m_Itemp);

	if (0 == m_Itemp)
	{			  
		m_iRowType_ = TYPE_VEC_NORMAL;
		m_bResponseType_ = RESTYPE_CELL_COVER_WCDMA_DATA_SUMMARY;
		SEARCHSQL(m_iRowType_, tm_sql);
	}
	else if (1 == m_Itemp)
	{

	}
	else if (2 == m_Itemp)
	{
		PPM_OS::memset(tm_sql, 0, sizeof(tm_sql));
		PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
			"exec mc_sp_cell_cover_wcdma_grid_get %d,%d, %d,%d,%d, '%s','%s'",
			tm_Time_Span.istime, 
			tm_Time_Span.ietime,
			tm_Search_Cell.iLAC,
			tm_Search_Cell.wRAC,
			tm_Search_Cell.iCI,
			stru_filelist.strtb.c_str(),
			tm_file);

		//ps
		m_iRowType_ = TYPE_VEC_GIS_DATA;
		m_bResponseType_ = RESTYPE_CELL_COVER_GRID_WCDMA_PS;
		
		char tm_sql2[8000];
		PPM_OS::memset(tm_sql2, 0, sizeof(tm_sql2));
		PPM_OS::snprintf(tm_sql2, sizeof(tm_sql2), "%s,%d", tm_sql, 2);
    SEARCHSQL(m_iRowType_, tm_sql2);

		//pshs
		m_iRowType_ = TYPE_VEC_GIS_DATA;
		m_bResponseType_ = RESTYPE_CELL_COVER_GRID_WCDMA_PSHS;
		PPM_OS::memset(tm_sql2, 0, sizeof(tm_sql2));
		PPM_OS::snprintf(tm_sql2, sizeof(tm_sql2), "%s,%d", tm_sql, 4);
		SEARCHSQL(m_iRowType_, tm_sql2);
	}
}


/************************************************************************/
/*  CSearchSock_CellEvent                                              */
/************************************************************************/
/************************************************************************
基本查询条件＋小区查询条件+事件列表
************************************************************************/
BOOL  
CSearchSock_CellEvent::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	//time
	if (!GetTime(pData, nCount, ioffset))
	{
		return FALSE;
	}
	
	if (!GetFileList(pData, nCount, ioffset))
	{
		return FALSE;
	}
	
	if(0 >= m_map_filelist.size())	//no data
	{
		//处理结束
		FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
		return TRUE;
	}
	
	DealFileList();

	//处理结束
	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
	return TRUE;	
}

BOOL CSearchSock_CellEvent::GetTime(const BYTE* const pData, const int nCount,int& ioffset)
{
	PPM_OS::memcpy(&tm_Time_Span.istime,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Time_Span.ietime,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	tm_Time_Span.ConvertToNetSeq();	
	return TRUE;
}
BOOL CSearchSock_CellEvent::GetArea(const BYTE* const pData, const int nCount,int& ioffset)
{
	PPM_OS::memcpy(&tm_Search_Area.itllongitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Search_Area.itllatitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Search_Area.ibrlongitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Search_Area.ibrlatitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	tm_Search_Area.ConvertToNetSeq();
	return TRUE;
}
BOOL CSearchSock_CellEvent::GetCell(const BYTE* const pData, const int nCount,int& ioffset)
{
	PPM_OS::memcpy(&tm_Search_Cell.iLAC,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Search_Cell.wRAC,pData + ioffset,2);
	MOVENEXT(ioffset,2,nCount);
	PPM_OS::memcpy(&tm_Search_Cell.iCI,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);

	tm_Search_Cell.ConvertToNetSeq();
	return TRUE;
}
BOOL CSearchSock_CellEvent::GetFileList(const BYTE* const pData, const int nCount,int& ioffset)
{
	//type
	char tm_sql_part[1024];
	PPM_OS::memset(&tm_sql_part, 0, sizeof(tm_sql_part));
	if(FALSE == CSearch_PI::GetTypeSql(pData,nCount,ioffset,tm_sql_part)){
		return FALSE;
	}

	//longitude and latitude
	if (!GetArea(pData, nCount, ioffset)){
		return FALSE;
	}

	if (!GetCell(pData, nCount, ioffset)){
		return FALSE;
	}

	PPM_OS::memset(&tm_event_list , 0, sizeof(tm_event_list));

	int itemp;
	PPM_OS::memcpy(&itemp,pData + ioffset,4);

	itemp = MAKEINT_NETSEQ1(itemp);

	if(-1 == itemp){
		MOVENEXT(ioffset,4,nCount)
	}
	else{
		if(FALSE == CSearch_PI::GetTextValue_int(pData,nCount,ioffset,tm_event_list)){
			return FALSE;
		}
	}

	char tm_sql[9000];
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec mc_sp_area_event_get %d,%d, %d,%d,%d,%d, '%s'" ,
		tm_Time_Span.istime, 
		tm_Time_Span.ietime,
		tm_Search_Area.itllongitude,
		tm_Search_Area.itllatitude,
		tm_Search_Area.ibrlongitude,
		tm_Search_Area.ibrlatitude,
		tm_sql_part);

	m_map_filelist.clear();
	m_iRowType_ = TYPE_VEC_FILE_LIST_SAMPLE;             //文件id获取 +　采样点数目获取
	SEARCHSQL(m_iRowType_, tm_sql);
    return TRUE;
}
void CSearchSock_CellEvent::DealFileList(void)
{
	char tm_file[8000];
	int bOnce;
	int cutnum = 600;         //ado对提交的sql语句限制为8000字节以内
	auto p_map = m_map_filelist.begin();   

	for (; p_map != m_map_filelist.end(); p_map++)   
	{
		auto p_vec = p_map->second.begin();	
		PPM_OS::memset(&tm_file, 0, sizeof(tm_file));	
		bOnce = 0;	
		size_t allcount=1;	

		for (int count=1; p_vec != p_map->second.end(); ++count, p_vec++, ++allcount)   
		{   
			if(0 == bOnce){
				PPM_OS::snprintf(tm_file,sizeof(tm_file),"%d",*p_vec); 
				bOnce = 1;
			}
			else{
				PPM_OS::snprintf(tm_file,sizeof(tm_file),"%s,%d",tm_file,*p_vec); 
			}	
			if(cutnum == count || allcount == p_map->second.size())
			{
				//获取头文件信息
				SearchLogFileSInfo(p_map->first.strlog.c_str(), p_map->first.testtype, tm_file, 0);
                char tm_sql[9000];
				PPM_OS::memset(tm_sql, 0, sizeof(tm_sql));
				PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
					"exec mc_sp_cell_event_get_1 %d,%d,%d, '%s', '%s','%s'",
					tm_Search_Cell.iLAC,
					tm_Search_Cell.wRAC,
					tm_Search_Cell.iCI,
					tm_event_list,
					p_map->first.strtb.c_str(),
					tm_file);

				m_iRowType_ = TYPE_VEC_NORMAL;
				m_bResponseType_ = RESTYPE_CELL_EVENT;
				SEARCHSQL(m_iRowType_, tm_sql);

				PPM_OS::memset(tm_file, 0, sizeof(tm_file));
				count = 0;
				bOnce = 0;
			}
		}
	}
}

/************************************************************************/
/*  CSearchSock_CellPropertyInfo                                        */
/************************************************************************/
//小区性能指标查询
BOOL   
CSearchSock_CellPropertyInfo::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{

	char strCellCode[255];
	PPM_OS::memset(&strCellCode, 0, sizeof(strCellCode));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,strCellCode,sizeof(strCellCode)));

	STRU_SEARCH_TIMESPAN tm_Time_Span;
	//time
	PPM_OS::memcpy(&tm_Time_Span.istime,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Time_Span.ietime,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	tm_Time_Span.ConvertToNetSeq();	

	int imark1;
	PPM_OS::memcpy(&imark1,pData + ioffset,4);
	imark1 = MAKEINT_NETSEQ1(imark1);
	MOVENEXT(ioffset,4,nCount); 

	int imark2;
	PPM_OS::memcpy(&imark2,pData + ioffset,4);
	imark2 = MAKEINT_NETSEQ1(imark2);
	MOVENEXT(ioffset,4,nCount);    

	int imark3;
	PPM_OS::memcpy(&imark3,pData + ioffset,4);
	imark3 = MAKEINT_NETSEQ1(imark3);
	MOVENEXT(ioffset,4,nCount);    

	char tm_sql[512];

	if (1 == imark1)
	{
		PPM_OS::memset(tm_sql, 0, sizeof(tm_sql));
		PPM_OS::snprintf(tm_sql,sizeof(tm_sql),"exec spx_cell_property_get '%s',%d,%d,%d",
			strCellCode,tm_Time_Span.istime,tm_Time_Span.ietime,1);

		m_iRowType_ = TYPE_VEC_NORMAL;
		m_bResponseType_ = RESTYPE_CELL_PROPERTY_CELL_INFO;

		if(NULL != m_pDataBaseDeal_){
			m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
		}
		Clear();
	}

	if (1 == imark2)
	{
		PPM_OS::memset(tm_sql, 0, sizeof(tm_sql));
		PPM_OS::snprintf(tm_sql,sizeof(tm_sql),"exec spx_cell_property_get '%s',%d,%d,%d",
			strCellCode,tm_Time_Span.istime,tm_Time_Span.ietime,2);

		m_iRowType_ = TYPE_VEC_NORMAL;
		m_bResponseType_ = RESTYPE_CELL_PROPERTY_GPRS_INFO;

		if(NULL != m_pDataBaseDeal_){
			m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
		}
		Clear();
	}

	if (1 == imark3)
	{
		PPM_OS::memset(tm_sql, 0, sizeof(tm_sql));
		PPM_OS::snprintf(tm_sql,sizeof(tm_sql),"exec spx_cell_property_get '%s',%d,%d,%d",
			strCellCode,tm_Time_Span.istime,tm_Time_Span.ietime,3);

		m_iRowType_ = TYPE_VEC_NORMAL;
		m_bResponseType_ = RESTYPE_CELL_PROPERTY_TRAFFIC_INFO;

		if(NULL != m_pDataBaseDeal_){
			m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
		}
		Clear();
	}

	//处理结束
	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
	return TRUE;
}


/************************************************************************/
/*  CSearchSock_CellCoverDisInfo                                        */
/************************************************************************/
BOOL 
CSearchSock_CellCoverDisInfo::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	int ifileid;
	PPM_OS::memcpy(&ifileid,pData + ioffset,4);
	ifileid = MAKEINT_NETSEQ1(ifileid);
    MOVENEXT(ioffset,4,nCount);
	
	char tm_logtbname[255];
	PPM_OS::memset(tm_logtbname, 0, sizeof(tm_logtbname));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_logtbname,sizeof(tm_logtbname)));	
	
	char tm_sql[8000];
	PPM_OS::memset(tm_sql, 0, sizeof(tm_sql));
	
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec mc_sp_cell_cover_distance_get %d, '%s' " ,
		ifileid, &tm_logtbname);
	
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_CELL_COVER_DISTANCE;
	if(NULL != m_pDataBaseDeal_)
	{
		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
	}
	Clear();
	
	//处理结束
	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
    return true;
	
}

/************************************************************************/
/*  CSearchSock_CellsimuGridLog                                        */
/************************************************************************/
BOOL 
CSearchSock_CellsimuGridLog::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	STRU_SEARCH_TIMESPAN tm_Time_Span;
	//time
	PPM_OS::memcpy(&tm_Time_Span.istime,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Time_Span.ietime,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	tm_Time_Span.ConvertToNetSeq();	
	
	char tm_sql[9000];
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec sp_cellsimu_grid_log_get %d,%d " ,
		tm_Time_Span.istime, 
		tm_Time_Span.ietime);
	
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_CELLSIMU_GRID_LOG;
	
	if(NULL != m_pDataBaseDeal_)
	{
		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
	}
	Clear();
	
	//处理结束
	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);	
	return TRUE;
	
}

/************************************************************************/
/*  CSearchSock_CellsimuGridInfo                                        */
/************************************************************************/
BOOL 
CSearchSock_CellsimuGridInfo::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{

	STRU_SEARCH_AREA tm_Search_Area;
	//longitude and latitude
	PPM_OS::memcpy(&tm_Search_Area.itllongitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Search_Area.itllatitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Search_Area.ibrlongitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Search_Area.ibrlatitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);

	char strtbname[255];
	PPM_OS::memset(strtbname, 0, sizeof(strtbname));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,strtbname,sizeof(strtbname)));

	int type;
	PPM_OS::memcpy(&type,pData + ioffset,4);
	type = MAKEINT_NETSEQ1(type);
	MOVENEXT(ioffset,4,nCount);

	tm_Search_Area.ConvertToNetSeq();

	char tm_sql[9000];
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec sp_cellsimu_grid_get %d,%d,%d,%d " ,
		tm_Search_Area.itllongitude, 
		tm_Search_Area.itllatitude,
		tm_Search_Area.ibrlongitude,
		tm_Search_Area.ibrlatitude
		);

	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_CELLSIMU_GRID;

	if(NULL != m_pDataBaseDeal_)
	{
		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
	}
	Clear();

	if (1 == type)
	{
		PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
		PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
			"exec sp_cellsimu_grid_info_get %d,%d,%d,%d,'%s' " ,
			tm_Search_Area.itllongitude, 
			tm_Search_Area.itllatitude,
			tm_Search_Area.ibrlongitude,
			tm_Search_Area.ibrlatitude,
			strtbname
			);

		m_iRowType_ = TYPE_VEC_NORMAL;
		m_bResponseType_ = RESTYPE_CELLSIMU_GRID_INFO;

		if(NULL != m_pDataBaseDeal_)
		{
			m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
		}
		Clear();
	}

	//处理结束
	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);	
	return TRUE;
}

/************************************************************************/
/*  CSearchSock_CellsimuCellGrid                                        */
/************************************************************************/
BOOL
CSearchSock_CellsimuCellGrid::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	int cellId;
	PPM_OS::memcpy(&cellId,pData + ioffset,4);
	cellId = MAKEINT_NETSEQ1(cellId);
	MOVENEXT(ioffset,4,nCount);
	
	char strtbname[255];
	PPM_OS::memset(strtbname, 0, sizeof(strtbname));
    CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,strtbname,sizeof(strtbname)));
	
	char tm_sql[9000];
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec sp_cellsimu_cell_grid_get %d, '%s' " ,
		cellId,
		&strtbname
		);
	
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_CELLSIMU_CEll_GRID;
	
	if(NULL != m_pDataBaseDeal_)
	{
		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
	}
	Clear();
	
	//处理结束
	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);	
    return TRUE;
}

/************************************************************************
CSearchSock_CellOutServiceInfo                                    
************************************************************************/
BOOL   
CSearchSock_CellOutServiceInfo::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	int cell_id;
	PPM_OS::memcpy(&cell_id,pData + ioffset,4);
	cell_id = MAKEINT_NETSEQ1(cell_id);
	MOVENEXT(ioffset,4,nCount);
	
	int alarm_id;
	PPM_OS::memcpy(&alarm_id,pData + ioffset,4);
	alarm_id = MAKEINT_NETSEQ1(alarm_id);
	MOVENEXT(ioffset,4,nCount);
	
	STRU_SEARCH_TIMESPAN tm_Time_Span;
	//time
	PPM_OS::memcpy(&tm_Time_Span.istime,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Time_Span.ietime,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	tm_Time_Span.ConvertToNetSeq();	 
	
	char tm_sql[512];
	PPM_OS::memset(tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec spx_cell_outservice_get %d,%d,%d,%d",
		cell_id,
		alarm_id,
		tm_Time_Span.istime,
		tm_Time_Span.ietime
		);
	
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_CELL_OUTSERVICE_INFO;
	
	if(NULL != m_pDataBaseDeal_){
		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
		//处理结束
		FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
	}
	Clear();
	return TRUE;
}

BOOL 
CSearchSock_CellOutServiceInfo::RowResultDeal_Normal()
{
	if(m_vecColumn.size() != CResultDef::Instance()->GetVecResultSize(CResultDef::SEARCHTYPE_CELL_OUTSERVICE_INFO)){
		return FALSE;
	} 
	if(NULL != m_pServerDeal_){
		PPM_OS::memset(m_pSendBuf_,0,SEND_BUF_MAXSIZE);
		
		int tm_ioffset = 0;
		m_pSendBuf_[tm_ioffset++] = CMD1_SEARCH;
		m_pSendBuf_[tm_ioffset++] = CMD2_RESPONSE;
		m_pSendBuf_[tm_ioffset++] = m_bResponseType_;
		//
		
		char szDesc[1024];
		PPM_OS::memset(&szDesc, 0, sizeof(szDesc));
		
		for(size_t i = 0; i < m_vecColumn.size(); i++)
		{
			if(i >= 7 && i <= 14)
			{
				Deal_NormalBetween7To14(i, szDesc, tm_ioffset);
			}
			else
			{
				(this->*pfunc_AddSendValue[m_vecColumn[i].iColType])(tm_ioffset,i);
			}
		}
		if(0 > m_pServerDeal_->SendData(m_pSendBuf_,tm_ioffset)){
			return -1;
		}
	}
	return TRUE;
}

void CSearchSock_CellOutServiceInfo::Deal_NormalBetween7To14(unsigned int i, char* szDesc, int& tm_ioffset)
{
	int tm_len = PPM_OS::strlen(((char*)m_vecColumn[i].pColValue));
	char sztmp[255];
	PPM_OS::memset(&sztmp, 0, sizeof(sztmp));
	PPM_OS::memcpy(sztmp,m_vecColumn[i].pColValue,tm_len);
	PPM_OS::snprintf(szDesc,1024,"%s%s",szDesc,sztmp);

	if(i == 14)
	{
		WORD tm_total = PPM_OS::strlen(szDesc);
		WORD tm_lensave = MAKEWORD_NETSEQ1(tm_total);
		PPM_OS::memcpy(m_pSendBuf_ + tm_ioffset,&tm_lensave,2);
		PPM_OS::memcpy(m_pSendBuf_ + tm_ioffset + 2,szDesc,tm_total);

		tm_ioffset += tm_total+2;
		PPM_OS::memset(&szDesc, 0, sizeof(szDesc));
	}
}
/************************************************************************/
/*  CSearchSock_CellAlarm                                               */
/************************************************************************/
BOOL
CSearchSock_CellAlarmInfo::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
    PPM_DEBUG((LM_ERROR, "正在执行小区告警！"));

	int alarm_status;
	PPM_OS::memcpy(&alarm_status,pData + ioffset,4);
	alarm_status = MAKEINT_NETSEQ1(alarm_status);
	MOVENEXT(ioffset,4,nCount);	

	STRU_SEARCH_TIMESPAN tm_Time_Span;
	//time
	PPM_OS::memcpy(&tm_Time_Span.istime,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Time_Span.ietime,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	tm_Time_Span.ConvertToNetSeq();	 

	//当前时间
	time_t now;
	time(&now);
	int itime = time(&now);

	char tm_sql[512];
	PPM_OS::memset(tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec spx_cell_alarm_get %d,%d,%d,%d,1",
		alarm_status,
		tm_Time_Span.istime,
		tm_Time_Span.ietime,
		itime
		);

	PPM_OS::memset(tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec spx_cell_alarm_get %d,%d,%d,%d,2",
		alarm_status,
		tm_Time_Span.istime,
		tm_Time_Span.ietime,
		itime
		);

	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_CELL_ALARM_INFO;

	if(NULL != m_pDataBaseDeal_){
		PPM_DEBUG((LM_ERROR, "正在执行小区告警查询！"));

		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
		//处理结束

		PPM_DEBUG((LM_ERROR, "执行小区告警查询完毕！"));
		FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
	}
    Clear();

	return TRUE;
}


BOOL 
CSearchSock_CellAlarmInfo::RowResultDeal_Normal()
{
	if(m_vecColumn.size() != CResultDef::Instance()->GetVecResultSize(CResultDef::SEARCHTYPE_CELL_ALARM_INFO)){
		return FALSE;
	} 
	if(NULL != m_pServerDeal_){
		PPM_OS::memset(m_pSendBuf_,0,SEND_BUF_MAXSIZE);

		int tm_ioffset = 0;
		m_pSendBuf_[tm_ioffset++] = CMD1_SEARCH;
		m_pSendBuf_[tm_ioffset++] = CMD2_RESPONSE;
		m_pSendBuf_[tm_ioffset++] = m_bResponseType_;
		//

		char szDesc[1024];
		PPM_OS::memset(&szDesc, 0, sizeof(szDesc));

		for(size_t i = 0; i < m_vecColumn.size(); i++)
		{
			if(i >= 8 && i <= 15)
			{
				Deal_NormalBetween8To15(i, szDesc, tm_ioffset);
			}
			else
			{
				(this->*pfunc_AddSendValue[m_vecColumn[i].iColType])(tm_ioffset,i);
			}
		}
		if(0 > m_pServerDeal_->SendData(m_pSendBuf_,tm_ioffset)){
			return -1;
		}
	}
	return TRUE;
}

void CSearchSock_CellAlarmInfo::Deal_NormalBetween8To15(unsigned int i, char* szDesc, int& tm_ioffset)
{
	int tm_len = PPM_OS::strlen(((char*)m_vecColumn[i].pColValue));
    char sztmp[255];
	PPM_OS::memset(&sztmp, 0, sizeof(sztmp));
	PPM_OS::memcpy(sztmp,m_vecColumn[i].pColValue,tm_len);
	PPM_OS::snprintf(szDesc,1024,"%s%s",szDesc,sztmp);

	if(i == 15)
	{
		WORD tm_total = PPM_OS::strlen(szDesc);
		WORD tm_lensave = MAKEWORD_NETSEQ1(tm_total);
		PPM_OS::memcpy(m_pSendBuf_ + tm_ioffset,&tm_lensave,2);
		PPM_OS::memcpy(m_pSendBuf_ + tm_ioffset + 2,szDesc,tm_total);

		tm_ioffset += tm_total+2;
		PPM_OS::memset(&szDesc, 0, sizeof(szDesc));
	}
}

/************************************************************************/
/*  CSearchSock_CellPropertyLNInfo                                       */
/************************************************************************/
BOOL
CSearchSock_CellPropertyLNInfo::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	PPM_DEBUG((LM_ERROR, "正在执行小区性能！"));

	int lac;
	PPM_OS::memcpy(&lac,pData + ioffset,4);
	lac = MAKEINT_NETSEQ1(lac);
	MOVENEXT(ioffset,4,nCount);	

	int ci;
	PPM_OS::memcpy(&ci,pData + ioffset,4);
	ci = MAKEINT_NETSEQ1(ci);
	MOVENEXT(ioffset,4,nCount);	

	STRU_SEARCH_TIMESPAN tm_Time_Span;
	//time
	PPM_OS::memcpy(&tm_Time_Span.istime,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Time_Span.ietime,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	tm_Time_Span.ConvertToNetSeq();	 

	//当前时间
	time_t now;
	time(&now);
	int itime = time(&now);

	char tm_sql[512];
	PPM_OS::memset(tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec spx_cell_property_ln_get %d,%d,%d,%d,%d,1",
		lac,
		ci,
		tm_Time_Span.istime,
		tm_Time_Span.ietime,
		itime
		);

	PPM_OS::memset(tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec spx_cell_property_ln_get %d,%d,%d,%d,%d,2",
		lac,
		ci,
		tm_Time_Span.istime,
		tm_Time_Span.ietime,
		itime
		);
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_CELL_PROPERTY_LN_INFO;

	if(NULL != m_pDataBaseDeal_){
		PPM_DEBUG((LM_ERROR, "正在执行小区性能查询！"));

		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);

		PPM_DEBUG((LM_ERROR, "执行小区性能查询完毕！"));
		//处理结束
		FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
	}
	Clear();

	return TRUE;
}


/************************************************************************/
/*  CSearchSock_CellsimuNBCellInfo                                       */
/************************************************************************/
BOOL
CSearchSock_CellsimuNBCellInfo::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	int cellId;
	PPM_OS::memcpy(&cellId,pData + ioffset,4);
	cellId = MAKEINT_NETSEQ1(cellId);
	MOVENEXT(ioffset,4,nCount);

	int itime;
	PPM_OS::memcpy(&itime,pData + ioffset,4);
	itime = MAKEINT_NETSEQ1(itime);
	MOVENEXT(ioffset,4,nCount);

	char tm_sql[9000];
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec sp_cellsimu_nbcell_grid_get %d,%d " ,
		cellId,
		itime
		);

	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_CELLSIMU_NBCELL_INFO;

	if(NULL != m_pDataBaseDeal_)
	{
		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
	}
	Clear();


	//处理结束
	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);	
	return TRUE;
}


BOOL
CSearchSock_CellTraffic::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	MOVENEXTTEST(ioffset,17,nCount);	
	STRU_SEARCH_TIMESPAN tm_Time_Span;

	//time
	PPM_OS::memcpy(&tm_Time_Span.istime,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Time_Span.ietime,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	tm_Time_Span.ConvertToNetSeq();	

	short len =pData[ioffset] * 256 + pData[ioffset+1];
	MOVENEXT(ioffset,2,nCount);

	//type
	char tm_sql_part[1024];
	PPM_OS::memcpy(&tm_sql_part, pData + ioffset, len);


	char tm_sql[8000];
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	tm_sql_part[len] = 0;
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec mc_sp_cell_traffic_get %d,%d,'%s'" ,
		tm_Time_Span.istime, 
		tm_Time_Span.ietime,
		tm_sql_part);
   
	m_iRowType_ = TYPE_VEC_NORMAL;             //文件id获取 +　采样点数目获取
	m_bResponseType_ = RESTYPE_CELL_TRAFFIC;

	SEARCHSQL(m_iRowType_, tm_sql);

	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND); 
	return TRUE;
}
