// SearchSock_Cell.h: interface for the CSearchSock_Cell class.
//
//////////////////////////////////////////////////////////////////////

#ifndef __SEARCHSOCK_CELL_H__
#define __SEARCHSOCK_CELL_H__

#include "./SearchSock.h"
   
class CSearchSock_CellCover : public CSearchSock
{
public:
  CSearchSock_CellCover();
	virtual ~CSearchSock_CellCover();

	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);

private:
	BOOL GetTimeSpan(const BYTE* const pData, const int nCount,int& ioffset);
	BOOL GetAreaInfo(const BYTE* const pData, const int nCount,int& ioffset);
	BOOL GetCellInfo(const BYTE* const pData, const int nCount,int& ioffset);
	BOOL GetBaseFileList(const BYTE* const pData, const int nCount,int& ioffset);

	void (CSearchSock_CellCover::*pfunc_TestTypeData[256])(STRU_FILE_LIST2 stru_filelist, char* tm_file);
	void DealTestTypeDataDefault(STRU_FILE_LIST2 stru_filelist, char* tm_file);
	void DealTestTypeDataGsm(STRU_FILE_LIST2 stru_filelist, char* tm_file);
	void DealTestTypeDataScan(STRU_FILE_LIST2 stru_filelist, char* tm_file);
	void DealTestTypeDataCdma(STRU_FILE_LIST2 stru_filelist, char* tm_file);
	void DealTestTypeDataTdscdma(STRU_FILE_LIST2 stru_filelist, char* tm_file);
	void DealTestTypeDataWcdma(STRU_FILE_LIST2 stru_filelist, char* tm_file);

	void (CSearchSock_CellCover::*pfunc_ServiceTypeGsm[256])(STRU_FILE_LIST2 stru_filelist, char* tm_file);
	void DealServiceTypeGsmDefault(STRU_FILE_LIST2 stru_filelist, char* tm_file);
	void DealServiceTypeGsmVoice(STRU_FILE_LIST2 stru_filelist, char* tm_file);
	void DealServiceTypeGsmData(STRU_FILE_LIST2 stru_filelist, char* tm_file);

	void (CSearchSock_CellCover::*pfunc_ServiceTypeScan[256])(STRU_FILE_LIST2 stru_filelist, char* tm_file);
	void DealServiceTypeScanDefault(STRU_FILE_LIST2 stru_filelist, char* tm_file);
	void DealServiceTypeScanNormal(STRU_FILE_LIST2 stru_filelist, char* tm_file);
  void DealServiceTypeScanTd(STRU_FILE_LIST2 stru_filelist, char* tm_file);
    
	void (CSearchSock_CellCover::*pfunc_ServiceTypeCdma[256])(STRU_FILE_LIST2 stru_filelist, char* tm_file);
	void DealServiceTypeCdmaDefault(STRU_FILE_LIST2 stru_filelist, char* tm_file);
	void DealServiceTypeCdmaVoice(STRU_FILE_LIST2 stru_filelist, char* tm_file);
	void DealServiceTypeCdmaData(STRU_FILE_LIST2 stru_filelist, char* tm_file);

	void (CSearchSock_CellCover::*pfunc_ServiceTypeTdscdma[256])(STRU_FILE_LIST2 stru_filelist, char* tm_file);
  void DealServiceTypeTdscdmaDefault(STRU_FILE_LIST2 stru_filelist, char* tm_file);
	void DealServiceTypeTdscdmaVoice(STRU_FILE_LIST2 stru_filelist, char* tm_file);
	void DealServiceTypeTdscdmaData(STRU_FILE_LIST2 stru_filelist, char* tm_file);

	void (CSearchSock_CellCover::*pfunc_ServiceTypeWcdma[256])(STRU_FILE_LIST2 stru_filelist, char* tm_file);
	void DealServiceTypeWcdmaDefault(STRU_FILE_LIST2 stru_filelist, char* tm_file);
	void DealServiceTypeWcdmaVoice(STRU_FILE_LIST2 stru_filelist, char* tm_file);
	void DealServiceTypeWcdmaData(STRU_FILE_LIST2 stru_filelist, char* tm_file);


private:
	STRU_SEARCH_TIMESPAN tm_Time_Span;
	STRU_SEARCH_AREA tm_Search_Area;
	STRU_SEARCH_CELL tm_Search_Cell;
	int m_Itemp;
};

class CSearchSock_CellEvent : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);

private:
	BOOL GetTime(const BYTE* const pData, const int nCount,int& ioffset);
  BOOL GetArea(const BYTE* const pData, const int nCount,int& ioffset);
	BOOL GetCell(const BYTE* const pData, const int nCount,int& ioffset);
	BOOL GetFileList(const BYTE* const pData, const int nCount,int& ioffset);
	void DealFileList(void);

private:
	STRU_SEARCH_TIMESPAN tm_Time_Span;
	STRU_SEARCH_AREA tm_Search_Area;
	STRU_SEARCH_CELL tm_Search_Cell;
	char tm_event_list[255];
};

class CSearchSock_CellPropertyInfo : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};

class CSearchSock_CellCoverDisInfo : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};


class CSearchSock_CellsimuGridLog : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};


class CSearchSock_CellsimuGridInfo : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};


class CSearchSock_CellsimuCellGrid : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};


class CSearchSock_CellOutServiceInfo : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);

private: 
	virtual BOOL RowResultDeal_Normal();
	void Deal_NormalBetween7To14(unsigned int i, char* szDesc, int& tm_ioffset);
};

class CSearchSock_CellAlarmInfo : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);

private: 
	virtual BOOL RowResultDeal_Normal();
	void Deal_NormalBetween8To15(unsigned int i, char* szDesc, int& tm_ioffset);
};

class CSearchSock_CellPropertyLNInfo : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};

class CSearchSock_CellsimuNBCellInfo : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};

class CSearchSock_CellTraffic : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};






#endif
