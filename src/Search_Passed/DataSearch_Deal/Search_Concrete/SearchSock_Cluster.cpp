// SearchSock_Cluster.cpp: implementation of the CSearchSock_Cluster class.
//
//////////////////////////////////////////////////////////////////////

#include "SearchSock_Cluster.h"

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

/************************************************************************
CSearchSock_ClusterInfo                                 
************************************************************************/
BOOL  
CSearchSock_ClusterInfo::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{   
	tClusterInfo stru_ClusterInfo;
	stru_ClusterInfo.Clear();
	PPM_OS::memcpy(&stru_ClusterInfo.iyear,pData + ioffset,4);
	stru_ClusterInfo.iyear = MAKEINT_NETSEQ1(stru_ClusterInfo.iyear);
	MOVENEXT(ioffset,4,nCount);

	PPM_OS::memcpy(&stru_ClusterInfo.itesttype,pData + ioffset,4);
	stru_ClusterInfo.itesttype = MAKEINT_NETSEQ1(stru_ClusterInfo.itesttype);
	MOVENEXT(ioffset,4,nCount);

	PPM_OS::memcpy(&stru_ClusterInfo.ibatch,pData + ioffset,4);
	stru_ClusterInfo.ibatch = MAKEINT_NETSEQ1(stru_ClusterInfo.ibatch);
	MOVENEXT(ioffset,4,nCount);
	
	//返回clique
	char tm_sql[255];
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec sp_cluster_clique_get %d,%d,%d" ,
		stru_ClusterInfo.iyear,
		stru_ClusterInfo.itesttype,
        stru_ClusterInfo.ibatch);
	
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_CLUSTER_CLIQUE_INFO;
	SEARCHSQL(m_iRowType_, tm_sql);

	//返回cluster
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec sp_cluster_info_get %d,%d,%d" ,
		stru_ClusterInfo.iyear,
		stru_ClusterInfo.itesttype,
        stru_ClusterInfo.ibatch);
	
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_CLUSTER_INFO;
	SEARCHSQL(m_iRowType_, tm_sql);

    //返回邻频小区对
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec sp_cluster_adjfreq_get %d,%d,%d" ,
		stru_ClusterInfo.iyear,
		stru_ClusterInfo.itesttype,
        stru_ClusterInfo.ibatch);
	
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_CLUSTER_ADJFREQ_INFO;
	SEARCHSQL(m_iRowType_, tm_sql);

    //返回相关统计结果
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec sp_cluster_area_freqNumAvg_get %d ", stru_ClusterInfo.itesttype);
	
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_CLUSTER_AREA_FREQNUMAVG;
	SEARCHSQL(m_iRowType_, tm_sql);

	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec sp_cluster_area_freqVariance_get %d", stru_ClusterInfo.itesttype);
	
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_CLUSTER_AREA_FREQVARIANCE;
	
	if(NULL != m_pDataBaseDeal_){
		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
	}
	Clear();

	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec sp_cluster_area_weakCovCellNum_get %d", stru_ClusterInfo.itesttype);
	
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_CLUSTER_AREA_WEAKCOVCELLNUM;
	
	if(NULL != m_pDataBaseDeal_){
		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
	}
	Clear();

	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec sp_cluster_area_carrierUseAvg_get %d", stru_ClusterInfo.itesttype);
	
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_CLUSTER_AREA_CARRIERUSEAVG;
	
	if(NULL != m_pDataBaseDeal_){
		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
	}
	Clear();

	//返回过覆盖小区
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec sp_cluster_cell_weakcov_get %d,%d,%d" ,
		stru_ClusterInfo.iyear,
		stru_ClusterInfo.itesttype,
        stru_ClusterInfo.ibatch);
	
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_CLUSTER_CELL_WEAKCOVER;
	
	if(NULL != m_pDataBaseDeal_){
		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
	}
	Clear();

	//返回小区无线利用率
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec sp_cluster_cell_wirelessrate_get %d,%d,%d" ,
		stru_ClusterInfo.iyear,
		stru_ClusterInfo.itesttype,
        stru_ClusterInfo.ibatch);
	
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_CLUSTER_CELL_WIRELESSRATE;
	
	if(NULL != m_pDataBaseDeal_){
		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
	}
	Clear();

	//返回区域渗透率
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec sp_cluster_area_injectrate_get %d", stru_ClusterInfo.itesttype );
	
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_CLUSTER_AREA_INJECTRATE;
	
	if(NULL != m_pDataBaseDeal_){
		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
	}
	Clear();

	//返回有效小区占比
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec sp_cluster_cell_availrate_get %d", stru_ClusterInfo.itesttype );

	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_CLUSTER_CELL_AVAILRATE;

	SEARCHSQL(m_iRowType_, tm_sql);

	//返回同频小区对
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec sp_cluster_cofreq_get %d,%d,%d" ,
		stru_ClusterInfo.iyear,
		stru_ClusterInfo.itesttype,
        stru_ClusterInfo.ibatch);
	
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_CLUSTER_COFREQ_INFO;

    SEARCHSQL(m_iRowType_, tm_sql);

   //处理结束
	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);	
	return TRUE;
}

/************************************************************************
CSearchSock_ClusterLog                                     
************************************************************************/
BOOL  
CSearchSock_ClusterLog::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{  
	int itesttype;
	PPM_OS::memcpy(&itesttype,pData + ioffset,4);
	itesttype = MAKEINT_NETSEQ1(itesttype);
	MOVENEXT(ioffset,4,nCount);
	
	STRU_SEARCH_TIMESPAN tm_Time_Span;
	//time
	PPM_OS::memcpy(&tm_Time_Span.istime,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Time_Span.ietime,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	tm_Time_Span.ConvertToNetSeq();	
	
	char tm_sql[255];
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec sp_cluster_log_get %d,%d,%d" ,
        itesttype,
		tm_Time_Span.istime, 
		tm_Time_Span.ietime);
	
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_CLUSTER_LOG;
	
	if(NULL != m_pDataBaseDeal_){
		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
	}
	Clear();

	//处理结束
	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
	return TRUE;
}

/************************************************************************
CSearchSock_ClusterCSL                                     
************************************************************************/
BOOL  
CSearchSock_ClusterCSL::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{  
	
	STRU_SEARCH_TIMESPAN tm_Time_Span;
	//time
	PPM_OS::memcpy(&tm_Time_Span.istime,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Time_Span.ietime,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	tm_Time_Span.ConvertToNetSeq();	
	
	char tm_sql[255];
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec sp_cluster_clique_CSL_get %d,%d" ,
		tm_Time_Span.istime, 
		tm_Time_Span.ietime);
	
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_CLUSTER_CSL_GET;
	
	if(NULL != m_pDataBaseDeal_){
		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
		//处理结束
		FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
	}
	Clear();


	return TRUE;
}

/************************************************************************
CSearchSock_ClusterCSLInfo                                     
************************************************************************/
BOOL  
CSearchSock_ClusterCSLInfo::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{  	
	int itime;
	PPM_OS::memcpy(&itime,pData + ioffset,4);
	itime = MAKEINT_NETSEQ1(itime);
	MOVENEXT(ioffset,4,nCount);
	
	char tm_sql[255];
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec sp_cluster_clique_CSL_info_get %d" ,
        itime);
	
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_CLUSTER_CSL_INFO_GET;
	
	if(NULL != m_pDataBaseDeal_){
		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
		//处理结束
		FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
	}
	Clear();
	return TRUE;
}

/************************************************************************
CSearchSock_ClusterCellCover                                     
************************************************************************/
BOOL 
CSearchSock_ClusterCellCover::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	int iyear;
	PPM_OS::memcpy(&iyear,pData + ioffset,4);
	iyear = MAKEINT_NETSEQ1(iyear);
	MOVENEXT(ioffset,4,nCount);
	
	int itesttype;
	PPM_OS::memcpy(&itesttype,pData + ioffset,4);
	itesttype = MAKEINT_NETSEQ1(itesttype);
	MOVENEXT(ioffset,4,nCount);
	
	int ibatch;
	PPM_OS::memcpy(&ibatch,pData + ioffset,4);
	ibatch = MAKEINT_NETSEQ1(ibatch);
	MOVENEXT(ioffset,4,nCount);
	
	char tm_cellname[255];
	PPM_OS::memset(&tm_cellname, 0, sizeof(tm_cellname));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_cellname,sizeof(tm_cellname)));
	
	char tm_sql[255];
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec sp_cluster_cell_grid_get %d,%d,%d,'%s'" ,
		iyear,
        itesttype,
		ibatch,
		&tm_cellname);
	
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_CLUSTER_CELL_COVER;
	
	if(NULL != m_pDataBaseDeal_){
		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
	}
	Clear();	
	//处理结束
	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
	return TRUE;
}

/************************************************************************
CSearchSock_ClusterAllCliqueInfo                                     
************************************************************************/
BOOL 
CSearchSock_ClusterAllCliqueInfo::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	int iyear;
	PPM_OS::memcpy(&iyear,pData + ioffset,4);
	iyear = MAKEINT_NETSEQ1(iyear);
	MOVENEXT(ioffset,4,nCount);
	
	int itesttype;
	PPM_OS::memcpy(&itesttype,pData + ioffset,4);
	itesttype = MAKEINT_NETSEQ1(itesttype);
	MOVENEXT(ioffset,4,nCount);
	
	int ibatch;
	PPM_OS::memcpy(&ibatch,pData + ioffset,4);
	ibatch = MAKEINT_NETSEQ1(ibatch);
	MOVENEXT(ioffset,4,nCount);
	
	//返回clique
	char tm_sql[255];
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec sp_cluster_allclique_get %d,%d,%d" ,
		iyear,
		itesttype,
        ibatch);
	
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_CLUSTER_CLIQUE_INFO;
	
	if(NULL != m_pDataBaseDeal_){
		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
	}
	Clear();	

    //返回邻频小区对
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec sp_cluster_adjfreq_get %d,%d,%d" ,
		iyear,
		itesttype,
        ibatch);
	
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_CLUSTER_ADJFREQ_INFO;
	
	if(NULL != m_pDataBaseDeal_){
		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
	}
	Clear();
	
	//返回同频小区对
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec sp_cluster_cofreq_get %d,%d,%d" ,
		iyear,
		itesttype,
        ibatch);
	
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_CLUSTER_COFREQ_INFO;		
	
	if(NULL != m_pDataBaseDeal_){
		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
	}
	Clear();
	
    //处理结束
	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);	
	return TRUE;
}


/************************************************************************
CSearchSock_OverCoverLog                                     
************************************************************************/
BOOL  
CSearchSock_OverCoverLog::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{ 
	int iservicetype;
	PPM_OS::memcpy(&iservicetype,pData + ioffset,4);
	iservicetype = MAKEINT_NETSEQ1(iservicetype);
	MOVENEXT(ioffset,4,nCount);
	
	STRU_SEARCH_TIMESPAN tm_Time_Span;
	//time
	PPM_OS::memcpy(&tm_Time_Span.istime,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Time_Span.ietime,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	tm_Time_Span.ConvertToNetSeq();	
	
	char tm_sql[255];
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec sp_overcover_round_get %d,%d,%d" ,
        iservicetype,
		tm_Time_Span.istime, 
		tm_Time_Span.ietime);
	
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_CLUSTER_LOG;
	
	if(NULL != m_pDataBaseDeal_){
		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
	}
	Clear();

	//处理结束
	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
	return TRUE;
}

/************************************************************************
CSearchSock_OverCoverInfo                                     
************************************************************************/
BOOL  
CSearchSock_OverCoverInfo::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{   
	int iservicetype;
	PPM_OS::memcpy(&iservicetype,pData + ioffset,4);
	iservicetype = MAKEINT_NETSEQ1(iservicetype);
	MOVENEXT(ioffset,4,nCount);

	int iround;
	PPM_OS::memcpy(&iround,pData + ioffset,4);
	iround = MAKEINT_NETSEQ1(iround);
	MOVENEXT(ioffset,4,nCount);

	//返回过覆盖小区个数
	char tm_sql[255];
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec sp_overcover_area_weakcellnum_get %d ", iservicetype);
	
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_CLUSTER_AREA_WEAKCOVCELLNUM;
	
	if(NULL != m_pDataBaseDeal_){
		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
	}
	Clear();



	//返回过覆盖小区
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec sp_overcover_cell_get %d,%d" ,
		iservicetype,
        iround);
	
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_CLUSTER_CELL_WEAKCOVER;

	if(NULL != m_pDataBaseDeal_){
		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
	}
	Clear();



	//返回小区无线利用率
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec sp_overcover_cell_wirelessrate_get %d,%d" ,
		iservicetype,
        iround);
	
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_CLUSTER_CELL_WIRELESSRATE;
	SearchSqlResultData(tm_sql);

	//返回区域渗透率
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec sp_overcover_area_injectrate_get %d", iservicetype );
	
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_CLUSTER_AREA_INJECTRATE;

	SearchSqlResultData(tm_sql);

	//返回有效小区占比
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec sp_overcover_cell_injrate_get %d", iservicetype );

	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_CLUSTER_CELL_AVAILRATE;
	
	if(NULL != m_pDataBaseDeal_){
		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);	
	}
	Clear();

    //处理结束
	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);	
	return TRUE;
}

void CSearchSock_OverCoverInfo::SearchSqlResultData(char* tm_sql)
{
	if(NULL != m_pDataBaseDeal_){
		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
	}
	Clear();
}

/************************************************************************
CSearchSock_OverCoverCellCover                                     
************************************************************************/
BOOL 
CSearchSock_OverCoverCellCover::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	int iservicetype;
	PPM_OS::memcpy(&iservicetype,pData + ioffset,4);
	iservicetype = MAKEINT_NETSEQ1(iservicetype);
	MOVENEXT(ioffset,4,nCount);
	
	int iround;
	PPM_OS::memcpy(&iround,pData + ioffset,4);
	iround = MAKEINT_NETSEQ1(iround);
	MOVENEXT(ioffset,4,nCount);
	
	char tm_cellname[255];
	PPM_OS::memset(&tm_cellname, 0, sizeof(tm_cellname));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_cellname,sizeof(tm_cellname)));
	
	char tm_sql[255];
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec sp_overcover_cell_grid_get %d,%d,'%s'" ,
		iservicetype,
        iround,
		&tm_cellname);
	
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_OVERCOVER_CELL_COVER;
	
	if(NULL != m_pDataBaseDeal_){
		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
	}
	Clear();
	
	//处理结束
	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
	return TRUE;
}



