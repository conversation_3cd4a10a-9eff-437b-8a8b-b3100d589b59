// SearchSock_Cluster.h: interface for the CSearchSock_Cluster class.
//
//////////////////////////////////////////////////////////////////////

#ifndef __SEARCHSOCK_CLUSTER_H__
#define __SEARCHSOCK_CLUSTER_H__

#include "./SearchSock.h"
   
class CSearchSock_ClusterInfo : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);

private:
	typedef struct Stru_ClusterInfo
	{
		void Clear(void)
		{
            iyear = 0;
			itesttype = 0;
			ibatch = 0;
		};

		int iyear;
		int itesttype;
		int ibatch;
	}tClusterInfo;
};

class CSearchSock_ClusterLog : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};


class CSearchSock_ClusterCSL : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};


class CSearchSock_ClusterCSLInfo : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};


class CSearchSock_ClusterCellCover : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};


class CSearchSock_ClusterAllCliqueInfo : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};


class CSearchSock_OverCoverLog : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};


class CSearchSock_OverCoverInfo : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);

private:
	void SearchSqlResultData(char* tm_sql);

};


class CSearchSock_OverCoverCellCover : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};

#endif
