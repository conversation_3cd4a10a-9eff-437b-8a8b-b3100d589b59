// SearchSock_ComplainBlock.cpp: implementation of the CSearchSock_ComplainBlock class.
//
//////////////////////////////////////////////////////////////////////

#include "SearchSock_ComplainBlock.h"

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////


/************************************************************************
CSearchSock_ComplainBlockGrid                                 
************************************************************************/
BOOL
CSearchSock_ComplainBlockGrid::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	char tm_sql[255];
	
	//清空，获取Block_ID
	m_vec_temp_int.clear();
	
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec sp_complain_block_grid_get 1,1 ");
	
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_COMPLAIN_BLOCK_GRID;
	if(NULL != m_pDataBaseDeal_)
	{
		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
	}	
	Clear();
	
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec sp_complain_block_grid_log_get 1,1");
	
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_COMPLAIN_BLOCK_GRID_LOG;
	if(NULL != m_pDataBaseDeal_)
	{
		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
	}	
	Clear();
	
	
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec sp_complain_block_get 1,1 ");
	
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_COMPLAIN_BLOCK;
	SEARCHSQL(m_iRowType_, tm_sql);
	
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec sp_complain_item_get 1,0,0,1 ");
	
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_COMPLAIN_ITEM;

	SEARCHSQL(m_iRowType_, tm_sql);
	
	//处理结束
	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);	
	return TRUE;	
}

/************************************************************************
CSearchSock_ComplainBlockInfo                                 
************************************************************************/
BOOL
CSearchSock_ComplainBlockInfo::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	STRU_SEARCH_TIMESPAN tm_Time_Span;
	//time
	PPM_OS::memcpy(&tm_Time_Span.istime,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Time_Span.ietime,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	tm_Time_Span.ConvertToNetSeq();	
	
	char tm_sql[1024];
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec sp_complain_block_get 1,1 ");
	
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_COMPLAIN_BLOCK;
	if(NULL != m_pDataBaseDeal_)
	{
		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
	}
	Clear();
	
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec sp_complain_item_get 1,%d, %d,1" ,
		tm_Time_Span.istime, tm_Time_Span.ietime);
	
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_COMPLAIN_ITEM;
	if(NULL != m_pDataBaseDeal_)
	{
		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
	}
	Clear();
	
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec sp_complain_report_get");
	
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_COMPLAIN_REPORT;
	if(NULL != m_pDataBaseDeal_)
	{
		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
	}
	Clear();
	
	//处理结束
	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);	
	return TRUE;	
}


/************************************************************************
CSearchSock_ComplainBlockIdToInfo                                 
************************************************************************/
BOOL
CSearchSock_ComplainBlockIdToInfo::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	
	int itemp;
	PPM_OS::memcpy(&itemp,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	
	itemp = MAKEINT_NETSEQ1(itemp);	
	
	char tm_sql[255];
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec sp_complain_block_id2info_get %d", itemp);
	
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_COMPLAIN_BLOCK;
	if(NULL != m_pDataBaseDeal_)
	{
		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
	}	
	Clear();
	
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec sp_complain_item_id2info_get %d" ,itemp);
	
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_COMPLAIN_ITEM;
	if(NULL != m_pDataBaseDeal_)
	{
		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
	}
	Clear();
	
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec sp_complain_report_id2info_get %d", itemp);
	
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_COMPLAIN_REPORT;
	if(NULL != m_pDataBaseDeal_)
	{
		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
	}
	Clear();
	
	//处理结束
	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);	
	return TRUE;	
}

/************************************************************************
CSearchSock_ComplainBlockGridRecent                                 
************************************************************************/
BOOL
CSearchSock_ComplainBlockGridRecent::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	char tm_sql[255];
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec sp_complain_block_grid_recent_get ");
	
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_COMPLAIN_BLOCK_GRID_RECENT;
	if(NULL != m_pDataBaseDeal_)
	{
		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
	}	
	Clear();
	
	//处理结束
	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);	
	return TRUE;	
}

/************************************************************************
CSearchSock_ComplainBlockGridToInfo                                 
************************************************************************/
BOOL
CSearchSock_ComplainBlockGridToInfo::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	int igridid;
	PPM_OS::memcpy(&igridid,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	igridid = MAKEINT_NETSEQ1(igridid);	
	
	m_vec_temp_int.clear();
	char tm_sql[255];
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec sp_complain_block_grid_get 2,%d ", igridid);
	
	m_iRowType_ = TYPE_VEC_COMPLAIN_GRID;
	m_bResponseType_ = RESTYPE_COMPLAIN_BLOCK_GRID;
	if(NULL != m_pDataBaseDeal_)
	{
		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
	}
	Clear();
	
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec sp_complain_block_grid_log_get 2,%d", igridid);
	
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_COMPLAIN_BLOCK_GRID_LOG;
	if(NULL != m_pDataBaseDeal_)
	{
		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
	}	
	Clear();
	
	//make block_id str
	char block_ids[512];
	PPM_OS::memset(&block_ids, 0, sizeof(block_ids));
	bool bOnce = true;
    
	for(auto p_vec = m_vec_temp_int.begin(); p_vec != m_vec_temp_int.end(); p_vec++)
	{
		if(bOnce)
		{
			PPM_OS::snprintf(block_ids,sizeof(block_ids),"%d",*p_vec); 
			bOnce = false;
		}
		else
		{
			PPM_OS::snprintf(block_ids,sizeof(block_ids),"%s,%d",&block_ids,*p_vec); 
		}	  
	}
	
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec sp_complain_block_get 2,'%s'", &block_ids);
	
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_COMPLAIN_BLOCK;
	if(NULL != m_pDataBaseDeal_)
	{
		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
	}	
	Clear();
	
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec sp_complain_item_get 2, 0, 0, '%s'", &block_ids);
	
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_COMPLAIN_ITEM;
	if(NULL != m_pDataBaseDeal_)
	{
		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
	}
	Clear();
	
	//处理结束
	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);	
	return TRUE;	
}



















