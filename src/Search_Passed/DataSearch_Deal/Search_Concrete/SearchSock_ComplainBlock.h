// SearchSock_ComplainBlock.h: interface for the CSearchSock_ComplainBlock class.
//
//////////////////////////////////////////////////////////////////////

#ifndef __SEARCHSOCK_COMPLAINBLOCK_H__
#define __SEARCHSOCK_COMPLAINBLOCK_H__

#include "./SearchSock.h"
   
class CSearchSock_ComplainBlockGrid : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};


class CSearchSock_ComplainBlockInfo : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};


class CSearchSock_ComplainBlockIdToInfo : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};


class CSearchSock_ComplainBlockGridRecent : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};


class CSearchSock_ComplainBlockGridToInfo : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};


#endif
