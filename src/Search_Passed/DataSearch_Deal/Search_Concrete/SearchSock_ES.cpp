// SearchSock_ES.cpp: implementation of the CSearchSock_ES class.
//
//////////////////////////////////////////////////////////////////////

#include "SearchSock_ES.h"
//#include "ColumnDef.h"
#include "../../stdclass/StdCommond.h"

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

/************************************************************************/
/* CSearchSock_ESEventInfo                                              */
/************************************************************************/
BOOL  
CSearchSock_ESEventInfo::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{	
	if (!GetESEventTimeSpanInfo(pData, nCount, ioffset))
	{
		return FALSE;
	}
	
	//type
	char tm_sql_part[1024];
	PPM_OS::memset(&tm_sql_part, 0, sizeof(tm_sql_part));
	if(FALSE == CSearch_PI::GetTypeSql(pData,nCount,ioffset,tm_sql_part)){
		return FALSE;
	}
	
	if (!GetESEventSearchAreaInfo(pData, nCount, ioffset))
	{
		return FALSE;
	}
	
	//event
	char tm_events[255];
	PPM_OS::memset(&tm_events, 0, sizeof(tm_events));
	if(FALSE == CSearch_PI::GetEventIdList(pData,nCount,ioffset,tm_events)){
		return FALSE;
	}

	//strfilename
	char strfilename[8000];
	PPM_OS::memset(&strfilename, 0, sizeof(strfilename));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,strfilename,sizeof(strfilename)));

	std::string sql = " 1=1 ";
	std::vector<std::string> vec_strName;
	vec_strName = CStdCommond::str_slipt(std::string(strfilename), "{,}");

	for (size_t i=0; i<vec_strName.size(); ++i)
	{
		if (0 == i)
		{			
			sql = " strfilename like ''%" + vec_strName[i] + "%'' ";
		}
		else 
		{
			sql += " or strfilename like ''%" + vec_strName[i] + "%'' ";
		}     
	}

	sql = " (" + sql + ") ";
	
	char tm_sql[8000];
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec mc_sp_es_area_event_get %d, %d, %d,%d,%d,%d, '%s', '%s','%s'" ,
		tm_Time_Span.istime, 
		tm_Time_Span.ietime,
		tm_Search_Area.itllongitude,
		tm_Search_Area.itllatitude,
		tm_Search_Area.ibrlongitude,
		tm_Search_Area.ibrlatitude,
		tm_sql_part,
		tm_events,
		sql.c_str());
	
	m_iRowType_ = TYPE_VEC_ES_AREA_EVENT;
	m_bResponseType_ = RESTYPE_ES_EVENT_INFO;
	
	if(NULL != m_pDataBaseDeal_)
	{
		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
	}
	Clear();
	
	//处理结束
	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);	
	return TRUE;	
}

BOOL  
CSearchSock_ESEventInfo::GetESEventTimeSpanInfo(const BYTE* const pData, const int nCount,int& ioffset)
{
	//time
	PPM_OS::memcpy(&tm_Time_Span.istime,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Time_Span.ietime,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	tm_Time_Span.ConvertToNetSeq();	
    return TRUE;
}

BOOL  
CSearchSock_ESEventInfo::GetESEventSearchAreaInfo(const BYTE* const pData, const int nCount,int& ioffset)
{
	//longitude and latitude
	PPM_OS::memcpy(&tm_Search_Area.itllongitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Search_Area.itllatitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Search_Area.ibrlongitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Search_Area.ibrlatitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);

	tm_Search_Area.ConvertToNetSeq();
	return TRUE;
}

/************************************************************************/
/* CSearchSock_ESEventTime                                              */
/************************************************************************/
BOOL  
CSearchSock_ESEventTime::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{	
	int itime;
	PPM_OS::memcpy(&itime,pData + ioffset,4);
	itime = MAKEINT_NETSEQ1(itime);
	MOVENEXT(ioffset,4,nCount);
	
	int iprojecttype;
	PPM_OS::memcpy(&iprojecttype,pData + ioffset,4);
	iprojecttype = MAKEINT_NETSEQ1(iprojecttype);
	MOVENEXT(ioffset,4,nCount);
	
	int ifileid;
	PPM_OS::memcpy(&ifileid,pData + ioffset,4);
	ifileid = MAKEINT_NETSEQ1(ifileid);
	MOVENEXT(ioffset,4,nCount);
	
	int event_id;
	PPM_OS::memcpy(&event_id,pData + ioffset,4);
	event_id = MAKEINT_NETSEQ1(event_id);
	MOVENEXT(ioffset,4,nCount);
	
	char tm_table[255];
	PPM_OS::memset(&tm_table, 0, sizeof(tm_table));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_table,sizeof(tm_table)));
	
	char tm_sql[9000];
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec mc_sp_es_event_time_get %d,%d,%d,%d, '%s' " ,
		itime, 
		iprojecttype,
		ifileid,
		event_id, 
		&tm_table);
	
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_ES_EVENT_TIME;
	
	if(NULL != m_pDataBaseDeal_)
	{
		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
	}
	Clear();
	
	//处理结束
	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);	
	return TRUE;	
}



/************************************************************************/
/* CSearchSock_ESEventReportEdit                                        */
/************************************************************************/
BOOL 
CSearchSock_ESEventReportEdit::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	int ifileid;
	PPM_OS::memcpy(&ifileid,pData + ioffset,4);
	ifileid = MAKEINT_NETSEQ1(ifileid);
	MOVENEXT(ioffset,4,nCount);

	int iprojecttype;
	PPM_OS::memcpy(&iprojecttype,pData + ioffset,4);
	iprojecttype = MAKEINT_NETSEQ1(iprojecttype);
	MOVENEXT(ioffset,4,nCount);

	int iseqid;
	PPM_OS::memcpy(&iseqid,pData + ioffset,4);
	iseqid = MAKEINT_NETSEQ1(iseqid);
	MOVENEXT(ioffset,4,nCount);

	int itime;
	PPM_OS::memcpy(&itime,pData + ioffset,4);
	itime = MAKEINT_NETSEQ1(itime);
	MOVENEXT(ioffset,4,nCount);

	int wtimems;
	PPM_OS::memcpy(&wtimems,pData + ioffset,4);
	wtimems = MAKEINT_NETSEQ1(wtimems);
	MOVENEXT(ioffset,4,nCount);

	int bms;
	PPM_OS::memcpy(&bms,pData + ioffset,4);
	bms = MAKEINT_NETSEQ1(bms);
	MOVENEXT(ioffset,4,nCount);

	int iEventID;
	PPM_OS::memcpy(&iEventID,pData + ioffset,4);
	iEventID = MAKEINT_NETSEQ1(iEventID);
	MOVENEXT(ioffset,4,nCount);

	int ilongitude;
	PPM_OS::memcpy(&ilongitude,pData + ioffset,4);
	ilongitude = MAKEINT_NETSEQ1(ilongitude);
	MOVENEXT(ioffset,4,nCount);

	int ilatitude;
	PPM_OS::memcpy(&ilatitude,pData + ioffset,4);
	ilatitude = MAKEINT_NETSEQ1(ilatitude);
	MOVENEXT(ioffset,4,nCount);

	int icqtposid;
	PPM_OS::memcpy(&icqtposid,pData + ioffset,4);
	icqtposid = MAKEINT_NETSEQ1(icqtposid);
	MOVENEXT(ioffset,4,nCount);

	int iLAC;
	PPM_OS::memcpy(&iLAC,pData + ioffset,4);
	iLAC = MAKEINT_NETSEQ1(iLAC);
	MOVENEXT(ioffset,4,nCount);

	int wRAC;
	PPM_OS::memcpy(&wRAC,pData + ioffset,4);
	wRAC = MAKEINT_NETSEQ1(wRAC);
	MOVENEXT(ioffset,4,nCount);

	int iCI;
	PPM_OS::memcpy(&iCI,pData + ioffset,4);
	iCI = MAKEINT_NETSEQ1(iCI);
	MOVENEXT(ioffset,4,nCount);

	int iTargetLAC;
	PPM_OS::memcpy(&iTargetLAC,pData + ioffset,4);
	iTargetLAC = MAKEINT_NETSEQ1(iTargetLAC);
	MOVENEXT(ioffset,4,nCount);

	int wTargetRAC;
	PPM_OS::memcpy(&wTargetRAC,pData + ioffset,4);
	wTargetRAC = MAKEINT_NETSEQ1(wTargetRAC);
	MOVENEXT(ioffset,4,nCount);

	int iTargetCI;
	PPM_OS::memcpy(&iTargetCI,pData + ioffset,4);
	iTargetCI = MAKEINT_NETSEQ1(iTargetCI);
	MOVENEXT(ioffset,4,nCount);

	int ianatime;
	PPM_OS::memcpy(&ianatime,pData + ioffset,4);
	ianatime = MAKEINT_NETSEQ1(ianatime);
	MOVENEXT(ioffset,4,nCount);

	char pretype_desc[1000];
	PPM_OS::memset(&pretype_desc, 0, sizeof(pretype_desc));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,pretype_desc,sizeof(pretype_desc)));

	char reason_desc[1000];
	PPM_OS::memset(&reason_desc, 0, sizeof(reason_desc));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,reason_desc,sizeof(reason_desc)));

	int iimporttime;
	PPM_OS::memcpy(&iimporttime,pData + ioffset,4);
	iimporttime = MAKEINT_NETSEQ1(iimporttime);
	MOVENEXT(ioffset,4,nCount);

	char cellname[50];
	PPM_OS::memset(&cellname, 0, sizeof(cellname));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,cellname,sizeof(cellname)));

	char areaname[50];
	PPM_OS::memset(&areaname, 0, sizeof(areaname));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,areaname,sizeof(areaname)));

	char roadname[200];
	PPM_OS::memset(&roadname, 0, sizeof(roadname));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,roadname,sizeof(roadname)));

	char strfilename[255];
	PPM_OS::memset(&strfilename, 0, sizeof(strfilename));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,strfilename,sizeof(strfilename)));

	char strsavepath[255];
	PPM_OS::memset(&strsavepath, 0, sizeof(strsavepath));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,strsavepath,sizeof(strsavepath)));

	char strmethod[255];
	PPM_OS::memset(&strmethod, 0, sizeof(strmethod));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,strmethod,sizeof(strmethod)));

	char strsolution[1000];
	PPM_OS::memset(&strsolution, 0, sizeof(strsolution));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,strsolution,sizeof(strsolution)));

	char strdwusername[50];
	PPM_OS::memset(&strdwusername, 0, sizeof(strdwusername));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,strdwusername,sizeof(strdwusername)));

	int itimedw;
	PPM_OS::memcpy(&itimedw,pData + ioffset,4);
	itimedw = MAKEINT_NETSEQ1(itimedw);
	MOVENEXT(ioffset,4,nCount);

	char strnote[1000];
	PPM_OS::memset(&strnote, 0, sizeof(strnote));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,strnote,sizeof(strnote)));

	char strauditor[50];
	PPM_OS::memset(&strauditor, 0, sizeof(strauditor));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,strauditor,sizeof(strauditor)));

	int itimeaudit;
	PPM_OS::memcpy(&itimeaudit,pData + ioffset,4);
	itimeaudit = MAKEINT_NETSEQ1(itimeaudit);
	MOVENEXT(ioffset,4,nCount);

	int status;
	PPM_OS::memcpy(&status,pData + ioffset,4);
	status = MAKEINT_NETSEQ1(status);
	MOVENEXT(ioffset,4,nCount);

	char strbak[1000];
	PPM_OS::memset(&strbak, 0, sizeof(strbak));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,strbak,sizeof(strbak)));

	char stropteffect[255];
	PPM_OS::memset(&stropteffect, 0, sizeof(stropteffect));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,stropteffect,sizeof(stropteffect)));

	char tm_sql[9000];
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec mc_sp_es_event_report_edit %d,%d,%d,%d,%d, %d,%d,%d,%d,%d, %d,%d,%d,%d,%d, %d,%d,  \
		'%s','%s', \
		%d,  \
		'%s','%s','%s','%s','%s','%s','%s','%s',  \
		%d,  \
		'%s','%s',  \
		%d,%d, \
		'%s','%s' " ,
		ifileid,
		iprojecttype,
		iseqid,
		itime,
		wtimems,
		bms,
		iEventID,
		ilongitude,
		ilatitude,
		icqtposid,
		iLAC,
		wRAC,
		iCI,
		iTargetLAC,
		wTargetRAC,
		iTargetCI,
		ianatime,
		&pretype_desc,
		&reason_desc,
		iimporttime,
		&cellname,
		&areaname,
		&roadname,
		&strfilename,
		&strsavepath,
		&strmethod,
		&strsolution,
		&strdwusername,
		itimedw,
		&strnote,
		&strauditor,
		itimeaudit,
		status,
		&strbak,
		&stropteffect);

	if(NULL != m_pDataBaseDeal_){
		int ret = m_pDataBaseDeal_->ExecSql(tm_sql);
		if (0 > ret)
		{
			return FALSE;
		}
	}

	//处理结束
	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
	return TRUE;

}

/************************************************************************/
/* CSearchSock_ESEventAheadTime                                        */
/************************************************************************/
BOOL 
CSearchSock_ESEventAheadTime::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	int tarEvtId;
	PPM_OS::memcpy(&tarEvtId,pData + ioffset,4);
	tarEvtId = MAKEINT_NETSEQ1(tarEvtId);
	MOVENEXT(ioffset,4,nCount);
	
	int fileid;
	PPM_OS::memcpy(&fileid,pData + ioffset,4);
	fileid = MAKEINT_NETSEQ1(fileid);
	MOVENEXT(ioffset,4,nCount);
	
	int projectid;
	PPM_OS::memcpy(&projectid,pData + ioffset,4);
	projectid = MAKEINT_NETSEQ1(projectid);
	MOVENEXT(ioffset,4,nCount);
	
	int timevalue;
	PPM_OS::memcpy(&timevalue,pData + ioffset,4);
	timevalue = MAKEINT_NETSEQ1(timevalue);
	MOVENEXT(ioffset,4,nCount);
	
	char tbnameEvent[255];
	PPM_OS::memset(&tbnameEvent, 0, sizeof(tbnameEvent));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tbnameEvent,sizeof(tbnameEvent)));
	
	
	char tm_sql[8000];
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec mc_sp_es_event_ahead_time_get %d,%d,%d,%d,'%s' " ,
		tarEvtId, 
		fileid,
		projectid,
		timevalue,
		&tbnameEvent);
	
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_ES_EVENT_AHEAD_TIME;
	
	if(NULL != m_pDataBaseDeal_)
	{
		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
	}
	Clear();
	
	//处理结束
	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);	
	return TRUE;
}

/************************************************************************/
/* CSearchSock_ESEventMsg                                         */
/************************************************************************/
BOOL 
CSearchSock_ESEventMsg::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	STRU_SEARCH_TIMESPAN tm_Time_Span;
	//time
	PPM_OS::memcpy(&tm_Time_Span.istime,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Time_Span.ietime,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	tm_Time_Span.ConvertToNetSeq();	
	
	int tarFileid;
	PPM_OS::memcpy(&tarFileid,pData + ioffset,4);
	tarFileid = MAKEINT_NETSEQ1(tarFileid);
	MOVENEXT(ioffset,4,nCount);
	
	int msgId;
	PPM_OS::memcpy(&msgId,pData + ioffset,4);
	msgId = MAKEINT_NETSEQ1(msgId);
	MOVENEXT(ioffset,4,nCount);
	
	char tbname_msg[255];
	PPM_OS::memset(&tbname_msg, 0, sizeof(tbname_msg));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tbname_msg,sizeof(tbname_msg)));
	
	int type;
	PPM_OS::memcpy(&type,pData + ioffset,4);
	type = MAKEINT_NETSEQ1(type);
	MOVENEXT(ioffset,4,nCount);
	
	char tm_sql[8000];
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec mc_sp_es_event_msg_get %d,%d,%d,%d,'%s',%d " ,
		tm_Time_Span.istime, 
		tm_Time_Span.ietime,
		tarFileid,
		msgId,
		&tbname_msg,
		type);
	
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_ES_EVENT_MSG;
	
	if(NULL != m_pDataBaseDeal_)
	{
		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
	}
	Clear();
	
	//处理结束
	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);	
	return TRUE;
}

/************************************************************************/
/* CSearchSock_ESEventReport                                         */
/************************************************************************/
BOOL 
CSearchSock_ESEventReport::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	STRU_SEARCH_TIMESPAN tm_Time_Span;
	//time
	PPM_OS::memcpy(&tm_Time_Span.istime,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Time_Span.ietime,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	tm_Time_Span.ConvertToNetSeq();	

	//type
	char tm_sql_part[1024];
	PPM_OS::memset(&tm_sql_part, 0, sizeof(tm_sql_part));
	if(FALSE == CSearch_PI::GetTypeSql(pData,nCount,ioffset,tm_sql_part)){
		return FALSE;
	}

	char tm_event_list[255];
	PPM_OS::memset(&tm_event_list , 0, sizeof(tm_event_list));

	int itemp;
	PPM_OS::memcpy(&itemp,pData + ioffset,4);

	itemp = MAKEINT_NETSEQ1(itemp);

	if(-1 == itemp)
	{
		MOVENEXT(ioffset,4,nCount)
	}
	else
	{
		if(FALSE == CSearch_PI::GetTextValue_int(pData,nCount,ioffset,tm_event_list)){
			return FALSE;
		}
	}

	char tm_sql[8000];

	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec mc_sp_es_event_report_get %d,%d, '%s', '%s' " ,
		tm_Time_Span.istime, 
		tm_Time_Span.ietime,
		tm_event_list,
		tm_sql_part);

	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_ES_EVENT_REPORT;
	if(NULL != m_pDataBaseDeal_)
	{
		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
	}
	Clear();

	//处理结束
	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);	
	return TRUE;
}



BOOL 
CSearchSock_ESEventReport::RowResultDeal()
{
	if(m_vecColumn.size() != CResultDef::Instance()->GetVecResultSize(CResultDef::SEARCHTYPE_ES_EVENT_REPORT)){
		return FALSE;
	} 
	if(NULL != m_pServerDeal_){
		PPM_OS::memset(m_pSendBuf_,0,SEND_BUF_MAXSIZE);

		int tm_ioffset = 0;
		m_pSendBuf_[tm_ioffset++] = CMD1_SEARCH;
		m_pSendBuf_[tm_ioffset++] = CMD2_RESPONSE;
		m_pSendBuf_[tm_ioffset++] = m_bResponseType_;
		//

		char szDesc[1024];
		PPM_OS::memset(&szDesc, 0, sizeof(szDesc));

		for(size_t i = 0; i < m_vecColumn.size(); i++)
		{
			if(IsDealESEventSendDataNeedNum(i))
			{
				DealESEventSendData(i, szDesc, tm_ioffset);
			}
			else
			{
				(this->*pfunc_AddSendValue[m_vecColumn[i].iColType])(tm_ioffset,i);
			}
		}
		if(0 > m_pServerDeal_->SendData(m_pSendBuf_,tm_ioffset)){
			return -1;
		}
	}
	return TRUE;
}

bool CSearchSock_ESEventReport::IsDealESEventSendDataNeedNum(unsigned int num)
{
    unsigned int szNum[4][2] = 
	{
		{17, 32},
	    {40, 47},
	    {50, 57},
	    {61, 68}
	};

	bool IsNeed = false;
	for (int i = 0; i < 4; i++)
	{
		if (num >= szNum[i][0] && num <= szNum[i][1])
		{
			IsNeed = true;
			break;
		}
	}

	return IsNeed;
}

bool CSearchSock_ESEventReport::IsESEventReportNeedNum(unsigned int num)
{
   unsigned int numvalue[5] = {24, 32, 47, 57, 68};
   bool ret = false;
   for (BYTE i = 0; i < 5; i++)
   {
	   if (num == numvalue[i])
	   {
		   ret = true;
	   }
   }

   return ret;
}
void CSearchSock_ESEventReport::DealESEventSendData(unsigned int i, char* szDesc, int& tm_ioffset)
{
	int tm_len = PPM_OS::strlen(((char*)m_vecColumn[i].pColValue));
	char sztmp[255];
	PPM_OS::memset(&sztmp, 0, sizeof(sztmp));
	PPM_OS::memcpy(sztmp,m_vecColumn[i].pColValue,tm_len);
	PPM_OS::snprintf(szDesc,1024,"%s%s",szDesc,sztmp);

	if(IsESEventReportNeedNum(i))
	{
		WORD tm_total = PPM_OS::strlen(szDesc);
		WORD tm_lensave = MAKEWORD_NETSEQ1(tm_total);
		PPM_OS::memcpy(m_pSendBuf_ + tm_ioffset,&tm_lensave,2);
		PPM_OS::memcpy(m_pSendBuf_ + tm_ioffset + 2,szDesc,tm_total);

		tm_ioffset += tm_total+2;
		PPM_OS::memset(&szDesc, 0, sizeof(szDesc));
	}
}


/************************************************************************/
/* CSearchSock_ESGridCell                                        */
/************************************************************************/
BOOL 
CSearchSock_ESGridCell::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	int gridid;
	PPM_OS::memcpy(&gridid,pData + ioffset,4);
	gridid = MAKEINT_NETSEQ1(gridid);
	MOVENEXT(ioffset,4,nCount);

	char tm_sql[8000];
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec mc_sp_es_scancell_at_grid_get %d " ,
		gridid);

	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_ES_GRID_CELL;

	if(NULL != m_pDataBaseDeal_)
	{
		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
	}
	Clear();

	//处理结束
	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);	
	return TRUE;
}






