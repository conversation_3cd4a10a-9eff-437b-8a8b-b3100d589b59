// SearchSock_ES.h: interface for the CSearchSock_ES class.
//
//////////////////////////////////////////////////////////////////////

#ifndef __SEARCHSOCK_ES_H__
#define __SEARCHSOCK_ES_H__

#include "./SearchSock.h"
   
class CSearchSock_ESEventInfo : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);

private:
	BOOL GetESEventTimeSpanInfo(const BYTE* const pData, const int nCount,int& ioffset);
	BOOL GetESEventSearchAreaInfo(const BYTE* const pData, const int nCount,int& ioffset);

	STRU_SEARCH_TIMESPAN tm_Time_Span;
	STRU_SEARCH_AREA tm_Search_Area;
};


class CSearchSock_ESEventTime : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};


 class CSearchSock_ESEventReport : public CSearchSock
 {
 public:
 	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
 private:
 	virtual BOOL RowResultDeal();
	bool IsESEventReportNeedNum(unsigned int num);
	void DealESEventSendData(unsigned int i, char* szDesc, int& tm_ioffset);
	bool IsDealESEventSendDataNeedNum(unsigned int num);
 };


class CSearchSock_ESEventReportEdit : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};


class CSearchSock_ESEventAheadTime : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};


class CSearchSock_ESEventMsg : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};

class CSearchSock_ESGridCell : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};



#endif
