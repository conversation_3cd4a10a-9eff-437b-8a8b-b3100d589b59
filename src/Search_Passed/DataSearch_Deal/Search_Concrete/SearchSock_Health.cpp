// SearchSock_Health.cpp: implementation of the CSearchSock_Health class.
//
//////////////////////////////////////////////////////////////////////

#include "SearchSock_Health.h"

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

/************************************************************************
CSearchSock_HealthGSM
************************************************************************/
BOOL  
CSearchSock_HealthGSM::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	MOVENEXTTEST(ioffset,17,nCount);

	if (!GetHealthGSMTimeSpan(pData, nCount, ioffset))
	{
		return FALSE;
	}

	//type
	char tm_sql_part[1024];
	PPM_OS::memset(&tm_sql_part, 0, sizeof(tm_sql_part));
	if(FALSE == CSearch_PI::GetTypeSql(pData,nCount,ioffset,tm_sql_part)){
		return FALSE;
	}
	
	if (!GetHealthGSMSearchAreaInfo(pData, nCount, ioffset))
	{
		return FALSE;
	}

	char tm_sql[2048];
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
				"exec sp_health_gsm_get %d,%d, %d,%d,%d,%d, '%s'" ,
						   tm_Time_Span.istime, 
						   tm_Time_Span.ietime,
						   tm_Search_Area.itllongitude,
						   tm_Search_Area.itllatitude,
						   tm_Search_Area.ibrlongitude,
						   tm_Search_Area.ibrlatitude,
						   tm_sql_part);


	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_HEALTH_GSM;
	if(NULL != m_pDataBaseDeal_)
	{
		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
	}
	Clear();

	//处理结束
	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
	
	return TRUE;
}

BOOL  
CSearchSock_HealthGSM::GetHealthGSMTimeSpan(const BYTE* const pData, const int nCount,int& ioffset)
{
	//time
	PPM_OS::memcpy(&tm_Time_Span.istime,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Time_Span.ietime,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	tm_Time_Span.ConvertToNetSeq();	
    return TRUE;
}

BOOL  
CSearchSock_HealthGSM::GetHealthGSMSearchAreaInfo(const BYTE* const pData, const int nCount,int& ioffset)
{
	//longitude and latitude
	PPM_OS::memcpy(&tm_Search_Area.itllongitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Search_Area.itllatitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Search_Area.ibrlongitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Search_Area.ibrlatitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);

	tm_Search_Area.ConvertToNetSeq();	
    return TRUE;
}
/************************************************************************
CSearchSock_HealthScan
************************************************************************/
BOOL  
CSearchSock_HealthScan::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	m_scan_count = 0;
	m_scan_offset = 0;

	MOVENEXTTEST(ioffset,28,nCount);

	if (!GetHealthScanTimeSpan(pData, nCount, ioffset))
	{
		return FALSE;
	}

	if (!GetHealthScanSearchAreaInfo(pData, nCount, ioffset))
	{
		return FALSE;
	}

	int itemp = 0;
	PPM_OS::memcpy(&itemp,pData + ioffset,4);
	itemp = MAKEINT_NETSEQ1(itemp);	//rxlev


	char tm_sql[1024];
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec sp_health_scan_get %d,%d,  %d,%d,%d,%d,  %d",
		tm_Time_Span.istime, 
		tm_Time_Span.ietime,
		tm_Search_Area.itllongitude,
		tm_Search_Area.itllatitude,
		tm_Search_Area.ibrlongitude,
		tm_Search_Area.ibrlatitude,
		itemp);

	m_bResponseType_ = RESTYPE_HEALTH_SCAN;
	m_scan_count = 0;
	m_scan_offset = 0;
	SEARCHSQL(m_iRowType_, tm_sql);

	//send the last package
	if((NULL != m_pServerDeal_) && (m_scan_offset != 0))
	{
		m_pServerDeal_->SendData(m_pSendBuf_,m_scan_offset);
	}

	//处理结束
	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);

	return TRUE;
}

BOOL  
CSearchSock_HealthScan::GetHealthScanTimeSpan(const BYTE* const pData, const int nCount,int& ioffset)
{
	//time
	PPM_OS::memcpy(&tm_Time_Span.istime,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Time_Span.ietime,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	tm_Time_Span.ConvertToNetSeq();	
    return TRUE;
}

BOOL  
CSearchSock_HealthScan::GetHealthScanSearchAreaInfo(const BYTE* const pData, const int nCount,int& ioffset)
{
	//longitude and latitude
	PPM_OS::memcpy(&tm_Search_Area.itllongitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Search_Area.itllatitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Search_Area.ibrlongitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Search_Area.ibrlatitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);

	tm_Search_Area.ConvertToNetSeq();	
    return TRUE;
}

BOOL
CSearchSock_HealthScan::RowResultDeal(int nQType)
{
	if(m_vecColumn.size() != CResultDef::Instance()->GetVecResultSize(CResultDef::SEARCHTYPE_HEALTH_SCAN)){
		return FALSE;
	} 
	if(NULL != m_pServerDeal_)
	{
		if(0 == m_scan_count)
		{
			PPM_OS::memset(m_pSendBuf_,0,SEND_BUF_MAXSIZE);
			m_scan_offset = 0;

			//构造信息发送
			m_pSendBuf_[m_scan_offset++] = CMD1_SEARCH;//命令字
			m_pSendBuf_[m_scan_offset++] = CMD2_RESPONSE;//子命令字
			m_pSendBuf_[m_scan_offset++] = m_bResponseType_;//响应类型
			m_pSendBuf_[m_scan_offset++] = 0;//数量

		}

		//
		for(size_t i = 0; i < m_vecColumn.size(); i++){
			(this->*pfunc_AddSendValue[m_vecColumn[i].iColType])(m_scan_offset,i);
		}

		m_scan_count++;
		m_pSendBuf_[3] = m_scan_count;

		if(m_scan_count == 10)
		{
			m_scan_count = 0;	
			if(0 > m_pServerDeal_->SendData(m_pSendBuf_,m_scan_offset))
			{
				return -1;
			}
		}
	}
	return TRUE;
}

/************************************************************************
CSearchSock_HealthScan_Cqt
************************************************************************/
BOOL  
CSearchSock_HealthScan_Cqt::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	MOVENEXTTEST(ioffset,12,nCount);

	STRU_SEARCH_TIMESPAN tm_Time_Span;
	//time
	PPM_OS::memcpy(&tm_Time_Span.istime,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Time_Span.ietime,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	tm_Time_Span.ConvertToNetSeq();	

	char tm_area_list[255];
	PPM_OS::memset(&tm_area_list , 0, sizeof(tm_area_list));
	
	int itemp;
	PPM_OS::memcpy(&itemp,pData + ioffset,4);
	itemp = MAKEINT_NETSEQ1(itemp);
	
	if(-1 == itemp)
	{
		MOVENEXT(ioffset,4,nCount)
	}
	else
	{
		if(FALSE == CSearch_PI::GetTextValue_int(pData,nCount,ioffset,tm_area_list)){
			return FALSE;
		}
	}	

	PPM_OS::memcpy(&itemp,pData + ioffset,4);
	itemp = MAKEINT_NETSEQ1(itemp);	//rxlev
	MOVENEXT(ioffset,4,nCount);

	char tm_sql[512];
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec sp_health_scan_cqt_get %d,%d,  '%s',  %d",
		tm_Time_Span.istime, 
		tm_Time_Span.ietime,
		tm_area_list,
		itemp);

	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_HEALTH_SCAN_CQT;

	m_scan_count = 0;
	m_scan_offset = 0;

	time_t now; // 变量声明 
	time(&now); // 取得现在的日期时间 
	PPM_DEBUG((LM_ERROR,"%s\n",ctime(&now)));


	if(NULL != m_pDataBaseDeal_)
	{
		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
	}
	Clear();
	//send the last package
	if((NULL != m_pServerDeal_) && (m_scan_offset != 0))
	{
		m_pServerDeal_->SendData(m_pSendBuf_,m_scan_offset);
	}

	time(&now); // 取得现在的日期时间 
	PPM_DEBUG((LM_ERROR,"%s\n",ctime(&now)));

	//处理结束
	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);

	return TRUE;
}

BOOL 
CSearchSock_HealthScan_Cqt::RowResultDeal(int nQType)
{
	return CSearchSock::RowResultDeal(nQType);
}
