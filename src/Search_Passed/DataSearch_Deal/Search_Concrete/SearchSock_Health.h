// SearchSock_Health.h: interface for the CSearchSock_Health class.
//
//////////////////////////////////////////////////////////////////////

#ifndef __SEARCHSOCK_HEALTH_H__
#define __SEARCHSOCK_HEALTH_H__

#include "./SearchSock.h"
   
class CSearchSock_HealthGSM : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);

private:
	BOOL GetHealthGSMTimeSpan(const BYTE* const pData, const int nCount,int& ioffset);
	BOOL GetHealthGSMSearchAreaInfo(const BYTE* const pData, const int nCount,int& ioffset);

private:
	STRU_SEARCH_TIMESPAN tm_Time_Span;
	STRU_SEARCH_AREA tm_Search_Area;
};


class CSearchSock_HealthScan : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);

	virtual BOOL RowResultDeal(int nQType);

protected:
	int m_scan_count;
	int m_scan_offset;

private:
	BOOL GetHealthScanTimeSpan(const BYTE* const pData, const int nCount,int& ioffset);
	BOOL GetHealthScanSearchAreaInfo(const BYTE* const pData, const int nCount,int& ioffset);

private:
	STRU_SEARCH_TIMESPAN tm_Time_Span;
	STRU_SEARCH_AREA tm_Search_Area;
};


class CSearchSock_HealthScan_Cqt : public CSearchSock_HealthScan
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);

protected:
   virtual BOOL RowResultDeal(int nQType);
};

#endif
