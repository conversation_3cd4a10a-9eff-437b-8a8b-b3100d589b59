// SearchSock_LogFile.cpp: implementation of the CSearchSock_LogFile class.
//
//////////////////////////////////////////////////////////////////////

#include "SearchSock_LogFile.h"


//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////


/************************************************************************/
/* Search log file                                                      */
/************************************************************************/

BOOL 
CSearchSock_LogFile::DealData(const BYTE* const pData, const int nCount, int& ioffset)
{
	if (!GetLogFileTimeSpan(pData, nCount, ioffset))
	{
		return FALSE;
	}
	
	//type
	char tm_sql_part[1024];
	PPM_OS::memset(&tm_sql_part, 0, sizeof(tm_sql_part));
	
	if(FALSE == CSearch_PI::GetTypeSql(pData,nCount,ioffset,tm_sql_part)){
		return FALSE;
	}
	
	if (!GetLogFileSearchAreaInfo(pData, nCount, ioffset))
	{
		return FALSE;
	}
	
	char tm_file[255];
	PPM_OS::memset(&tm_file, 0, sizeof(tm_file));
	
    //filename
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_file,
		sizeof(tm_file)));
    
	std::string strFileName(tm_file);
	std::string makestr = "";
	bool flag = true;
    if (strFileName.find(" and ") != std::string::npos)
    {
		MakeStr1(strFileName, flag);
    }
	else if (strFileName.find(" or ") != std::string::npos )
	{
		MakeStr2(strFileName, flag);
	}
    
	if (makestr.length() == 0)
	{
		makestr = " and strfilename like ''%" + (std::string)tm_file + "%''";
	}
	
	char tm_sql[2048];		
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_log_get %d,%d,%d,%d,%d,%d,'%s','%s'",
		tm_Time_Span.istime, 
		tm_Time_Span.ietime,
		tm_Search_Area.itllongitude,
		tm_Search_Area.itllatitude,
		tm_Search_Area.ibrlongitude,
		tm_Search_Area.ibrlatitude,
		tm_sql_part,
		makestr.c_str()//tm_file
  		);

	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_LOG_SEARCH;
	if(NULL != m_pDataBaseDeal_){
		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
		//处理结束
		FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
	}
	Clear();
	
	return TRUE;
}

BOOL 
CSearchSock_LogFile::GetLogFileTimeSpan(const BYTE* const pData, const int nCount, int& ioffset)
{
	//time
	PPM_OS::memcpy(&tm_Time_Span.istime,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Time_Span.ietime,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	tm_Time_Span.ConvertToNetSeq();	
    return TRUE;
}

BOOL 
CSearchSock_LogFile::GetLogFileSearchAreaInfo(const BYTE* const pData, const int nCount, int& ioffset)
{
	//longitude and latitude
	PPM_OS::memcpy(&tm_Search_Area.itllongitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Search_Area.itllatitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Search_Area.ibrlongitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Search_Area.ibrlatitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);

	tm_Search_Area.ConvertToNetSeq();
    return TRUE;
}

void CSearchSock_LogFile::MakeStr1(std::string& strFileName, bool& flag)
{
	while(1)                
	{               
		int pos = strFileName.find(" and ");     
		if( pos < 0)
		{
			makestr += " and strfilename like ''%" + strFileName + "%'') ";
			break;
		}       
		std::string word = strFileName.substr(0, pos);
		strFileName = strFileName.substr(pos+5);
		if (flag)
		{
			makestr += " and (strfilename like ''%" + word + "%'' ";
			flag = false;
		}
		else 
		{
			makestr += " and strfilename like ''%" + word + "%''  ";
		}

	}
}
void CSearchSock_LogFile::MakeStr2(std::string& strFileName, bool& flag)
{
	while(1)                
	{               
		int pos = strFileName.find(" or ");     
		if( pos < 0)
		{
			makestr += " or strfilename like ''%" + strFileName + "%'') ";
			break;
		}       
		std::string word = strFileName.substr(0, pos);
		strFileName = strFileName.substr(pos+4);          
		if (flag)
		{
			makestr += "  and (strfilename like ''%" + word + "%'' ";
			flag = false;
		}
		else 
		{
			makestr += " or strfilename like ''%" + word + "%'' ";
		}
	}
}

/************************************************************************/
/* Search log file LN                                                   */
/************************************************************************/

BOOL  
CSearchSock_LogFile_LN::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	//time
	if (!GetTime(pData, nCount, ioffset))
	{
		return FALSE;
	}
	
	//type
	char tm_sql_part[1024];
	PPM_OS::memset(&tm_sql_part, 0, sizeof(tm_sql_part));
    
	m_map_area_list.clear();
	if(FALSE == CSearch_PI::GetTypeSql_LN(pData,nCount,ioffset,tm_sql_part, &m_map_area_list)){
		return FALSE;
	}
	
	if (!GetArea(pData, nCount, ioffset))
	{
		return FALSE;
	}
	
	char tm_file[255];
	PPM_OS::memset(&tm_file, 0, sizeof(tm_file));
	
    //filename
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_file,
		sizeof(tm_file)));
    
	std::string strFileName(tm_file);
	std::string makestr = "";
	bool flag = true;
    if (strFileName.find(" and ") != std::string::npos)
    {
		MakeStr1(strFileName, flag);
    }
	else if (strFileName.find(" or ") != std::string::npos )
	{
		MakeStr2(strFileName, flag);
	}
    
	if (makestr.length() == 0)
	{
		makestr = " and strfilename like ''%" + (std::string)tm_file + "%'' ";
	}
	
	if(0 >= m_map_area_list.size())
	{
		DealAreaListEmpty(tm_sql_part);
	}
	else
	{
		DealAreaListNomal(tm_sql_part);
	}
	
	return TRUE;
}

BOOL CSearchSock_LogFile_LN::GetTime(const BYTE* const pData, const int nCount,int& ioffset)
{
	PPM_OS::memcpy(&tm_Time_Span.istime,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Time_Span.ietime,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	tm_Time_Span.ConvertToNetSeq();	
	return TRUE;
}
BOOL CSearchSock_LogFile_LN::GetArea(const BYTE* const pData, const int nCount,int& ioffset)
{
	//longitude and latitude
	PPM_OS::memcpy(&tm_Search_Area.itllongitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Search_Area.itllatitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Search_Area.ibrlongitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Search_Area.ibrlatitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);

	tm_Search_Area.ConvertToNetSeq();
	return TRUE;
}

void CSearchSock_LogFile_LN::MakeStr1(std::string& strFileName, bool& flag)
{
	while(1)                
	{               
		int pos = strFileName.find(" and ");     
		if( pos < 0)
		{
			makestr += " and strfilename like ''%" + strFileName + "%'') ";
			break;
		}       
		std::string word = strFileName.substr(0, pos);
		strFileName = strFileName.substr(pos+5);
		if (flag)
		{
			makestr += " and (strfilename like ''%" + word + "%'' ";
			flag = false;
		}
		else 
		{
			makestr += " and strfilename like ''%" + word + "%''  ";
		}
	}
}

void CSearchSock_LogFile_LN::MakeStr2(std::string& strFileName, bool& flag)
{
	while(1)                
	{               
		int pos = strFileName.find(" or ");     
		if( pos < 0)
		{
			makestr += " or strfilename like ''%" + strFileName + "%'') ";
			break;
		}       
		std::string word = strFileName.substr(0, pos);
		strFileName = strFileName.substr(pos+4);          
		if (flag)
		{
			makestr += "  and (strfilename like ''%" + word + "%'' ";
			flag = false;
		}
		else 
		{
			makestr += " or strfilename like ''%" + word + "%'' ";
		}
	}
}

void CSearchSock_LogFile_LN::DealAreaListEmpty(char* tm_sql_part)
{
	char tm_sql[9000];
	PPM_OS::memset(tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_log_get %d,%d,%d,%d,%d,%d,'%s','%s'",
		tm_Time_Span.istime, 
		tm_Time_Span.ietime,
		tm_Search_Area.itllongitude,
		tm_Search_Area.itllatitude,
		tm_Search_Area.ibrlongitude,
		tm_Search_Area.ibrlatitude,
		tm_sql_part,
		makestr.c_str()//tm_file
		);

	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_LOG_SEARCH;

	if(NULL != m_pDataBaseDeal_)
	{
		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
		//处理结束
		FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
	}
	Clear();
}
void CSearchSock_LogFile_LN::DealAreaListNomal(char* tm_sql_part)
{
	char tm_sql[9000];
	PPM_OS::memset(tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_log_get_ln %d,%d,%d,%d,%d,%d,'%s','%s'",
		tm_Time_Span.istime, 
		tm_Time_Span.ietime,
		tm_Search_Area.itllongitude,
		tm_Search_Area.itllatitude,
		tm_Search_Area.ibrlongitude,
		tm_Search_Area.ibrlatitude,	
		tm_sql_part,
		makestr.c_str()//tm_file
		);

	char tm_sql2[4096];
	char szarea[255];
	int bOnce = 0;
	auto p_map = m_map_area_list.begin();   
	for (; p_map != m_map_area_list.end(); p_map++)   
	{
		memset(tm_sql2, 0, sizeof(tm_sql2));
		memset(szarea, 0, sizeof(szarea));
		bOnce = 0;	

		auto p_vec = p_map->second.begin();	

		for (; p_vec != p_map->second.end(); p_vec++)   
		{
			if(!MakeSzArea((*p_vec), szarea, bOnce))
			{
				break;
			}
		}

		PPM_OS::snprintf(tm_sql2, sizeof(tm_sql2), "%s, ' and iareatype = %d '" ,
			tm_sql,
			p_map->first);

		if(0 < strlen(szarea))
		{
			PPM_OS::snprintf(tm_sql2, sizeof(tm_sql2), "%s,' and iareaid in(%s)'" , tm_sql2, szarea);
		}
		else
		{
			PPM_OS::snprintf(tm_sql2, sizeof(tm_sql2), "%s, ''" ,
				tm_sql2
				/*, p_map->first*/);
		}

		m_iRowType_ = TYPE_VEC_NORMAL;
		m_bResponseType_ = RESTYPE_LOG_SEARCH;
		SEARCHSQL(m_iRowType_, tm_sql2);
	}

	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
}

bool CSearchSock_LogFile_LN::MakeSzArea(int areaid, char* szarea, int& bOnce)
{
	if(-1 == areaid)	// if the areaid = -1 ,search all the areaid 
	{
		return false;
	}
	else
	{
		if(0 == bOnce)
		{
			PPM_OS::snprintf(szarea,sizeof(szarea),"%s%d", szarea, areaid); 
			bOnce = 1;
		}
		else
		{
			PPM_OS::snprintf(szarea,sizeof(szarea),"%s,%d",szarea, areaid); 
		}	
	}
	return true;
}
/************************************************************************/
/* Search log file EVENT                                                */
/************************************************************************/

BOOL  
CSearchSock_LogEvent::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	MOVENEXTTEST(ioffset,9,nCount);
	
	int ifileID;
	PPM_OS::memcpy(&ifileID,pData + ioffset,4);
	ifileID = MAKEINT_NETSEQ1(ifileID);
	MOVENEXT(ioffset,4,nCount);
	
	BYTE btemp;
	PPM_OS::memcpy(&btemp,pData + ioffset,1);
	MOVENEXT(ioffset,1,nCount);
	
	int iprojecttype;
	iprojecttype = btemp & 0x000000ff;		
	
	char tm_event_list[255];
	PPM_OS::memset(&tm_event_list , 0, sizeof(tm_event_list));
	
	int itemp;
	PPM_OS::memcpy(&itemp,pData + ioffset,4);
	
	itemp = MAKEINT_NETSEQ1(itemp);
	
	if(-1 == itemp)
	{
		MOVENEXT(ioffset,4,nCount)
	}
	else
	{
		if(FALSE == CSearch_PI::GetTextValue_int(pData,nCount,ioffset,tm_event_list)){
			return FALSE;
		}
	}
	
	char tm_logtbname[255];
	PPM_OS::memset(&tm_logtbname, 0, sizeof(tm_logtbname));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_logtbname,sizeof(tm_logtbname)));
	
    //获取头文件信息
    SearchLogFileInfo(tm_logtbname, iprojecttype, ifileID, 0);
	
	char tm_sql[1024];
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec mc_sp_log_event_get %d,%d, '%s', '%s'" ,
		ifileID, 
		iprojecttype,
		tm_event_list,
		tm_logtbname);
	
	
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_LOG_EVENT;
	if(NULL != m_pDataBaseDeal_)
	{
		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
	}
	Clear();
	
	//处理结束
	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
	
	return TRUE;

}

/************************************************************************/
/* Search log file By Ifileid                                           */
/************************************************************************/

BOOL  
CSearchSock_LogFileByFileid::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	int ifileID;
	PPM_OS::memcpy(&ifileID,pData + ioffset,4);
	ifileID = MAKEINT_NETSEQ1(ifileID);
	MOVENEXT(ioffset,4,nCount);
	
	char tm_logname[255];
	PPM_OS::memset(&tm_logname, 0, sizeof(tm_logname));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_logname,sizeof(tm_logname)));
	
	char tm_sql[2048];		
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_log_get_1 %d, '%s' ",
		ifileID, //tm_file
		&tm_logname
		);
	
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_LOG_SEARCH_BYFILEID;
	if(NULL != m_pDataBaseDeal_){
		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
		//处理结束
		FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
	}
	Clear();
	
	return TRUE;
}


/************************************************************************/
/* Search log file Error                                                */
/************************************************************************/

BOOL  
CSearchSock_LogFileError::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	if (!GetLogFileErrorTimeSpan(pData, nCount, ioffset))
	{
		return FALSE;
	}
	
	//type
	char tm_sql_part[1024];
	PPM_OS::memset(&tm_sql_part, 0, sizeof(tm_sql_part));
	if(FALSE == CSearch_PI::GetTypeSql(pData,nCount,ioffset,tm_sql_part)){
		return FALSE;
	}
	
	if (!GetLogFileErrorSearchAreaInfo(pData, nCount, ioffset))
	{
		return FALSE;
	}
	
	char tm_file[255];
	PPM_OS::memset(&tm_file, 0, sizeof(tm_file));
	
	//filename
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_file,
		sizeof(tm_file)));
	
	char tm_sql[1024];
	PPM_OS::memset(tm_sql, 0 ,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_log_error_get %d,%d,%d,%d,%d,%d,'%s','%s'",
		tm_Time_Span.istime, 
		tm_Time_Span.ietime,
		tm_Search_Area.itllongitude,
		tm_Search_Area.itllatitude,
		tm_Search_Area.ibrlongitude,
		tm_Search_Area.ibrlatitude,
		tm_sql_part,
		tm_file
		);
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_LOG_SEARCH;
	
	if(NULL != m_pDataBaseDeal_){
		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
		//处理结束
		FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
	}
	Clear();
	return TRUE;
}

BOOL  
CSearchSock_LogFileError::GetLogFileErrorTimeSpan(const BYTE* const pData, const int nCount,int& ioffset)
{
	//time
	PPM_OS::memcpy(&tm_Time_Span.istime,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Time_Span.ietime,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	tm_Time_Span.ConvertToNetSeq();	
    return TRUE;
}

BOOL  
CSearchSock_LogFileError::GetLogFileErrorSearchAreaInfo(const BYTE* const pData, const int nCount,int& ioffset)
{
	//longitude and latitude
	PPM_OS::memcpy(&tm_Search_Area.itllongitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Search_Area.itllatitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Search_Area.ibrlongitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Search_Area.ibrlatitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);

	tm_Search_Area.ConvertToNetSeq();
    return TRUE;
}
/************************************************************************/
/* Search log file Error                                                */
/************************************************************************/

BOOL 
CSearchSock_File_Delete::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	int ifileid;
	PPM_OS::memcpy(&ifileid,pData + ioffset,4);
	ifileid = MAKEINT_NETSEQ1(ifileid);
	MOVENEXT(ioffset,4,nCount);
	
	int iprojecttype;
	PPM_OS::memcpy(&iprojecttype,pData + ioffset,4);
	iprojecttype = MAKEINT_NETSEQ1(iprojecttype);
	MOVENEXT(ioffset,4,nCount);
	
	char tm_logtbname[255];
	PPM_OS::memset(&tm_logtbname, 0, sizeof(tm_logtbname));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_logtbname,sizeof(tm_logtbname)));
	
	char tm_sql[256];	
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_log_info_delete %d, %d, '%s'", 
		ifileid,iprojecttype,tm_logtbname);
	
	if(NULL != m_pDataBaseDeal_){
		int ret = m_pDataBaseDeal_->ExecSql(tm_sql);
		if (0 > ret)
		{
			return FALSE;
		}
	}
	
	//处理结束
	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
	return TRUE;	
}


