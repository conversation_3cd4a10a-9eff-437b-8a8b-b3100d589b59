// SearchSock_LogFile.h: interface for the CSearchSock_LogFile class.
//
//////////////////////////////////////////////////////////////////////



#ifndef __SEARCHSOCK_LOGFILE_H__
#define __SEARCHSOCK_LOGFILE_H__


#include "./SearchDIY.h"
   
class CSearchSock_LogFile : public CSearchSock
{

public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);

private:
	void MakeStr1(std::string& strFileName, bool& flag);
	void MakeStr2(std::string& strFileName, bool& flag);

	BOOL GetLogFileTimeSpan(const BYTE* const pData, const int nCount, int& ioffset);
	BOOL GetLogFileSearchAreaInfo(const BYTE* const pData, const int nCount, int& ioffset);

private:
	std::string makestr;
  STRU_SEARCH_TIMESPAN tm_Time_Span;
	STRU_SEARCH_AREA tm_Search_Area;
};

class CSearchSock_LogFile_Test : public CSearchSock
{
	
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};


class CSearchSock_LogFile_LN : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);

private:
	std::map<int, std::vector<int> > m_map_area_list; 

private:
	BOOL GetTime(const BYTE* const pData, const int nCount,int& ioffset);
	BOOL GetArea(const BYTE* const pData, const int nCount,int& ioffset);
	void MakeStr1(std::string& strFileName, bool& flag);
	void MakeStr2(std::string& strFileName, bool& flag);
	void DealAreaListEmpty(char* tm_sql_part);
	void DealAreaListNomal(char* tm_sql_part);
	bool MakeSzArea(int areaid, char* szarea, int& bOnce);

private:
	STRU_SEARCH_TIMESPAN tm_Time_Span;
	STRU_SEARCH_AREA tm_Search_Area;
	std::string makestr;
};

class CSearchSock_LogEvent : public CSearchSock
{	
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};

class CSearchSock_LogFileByFileid : public CSearchSock
{	
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};

class CSearchSock_LogFileError : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);

private:
	BOOL GetLogFileErrorTimeSpan(const BYTE* const pData, const int nCount,int& ioffset);
	BOOL GetLogFileErrorSearchAreaInfo(const BYTE* const pData, const int nCount,int& ioffset);

private:
	STRU_SEARCH_TIMESPAN tm_Time_Span;
	STRU_SEARCH_AREA tm_Search_Area;

};


class CSearchSock_File_Delete : public CSearchSock
{	
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};

#endif
