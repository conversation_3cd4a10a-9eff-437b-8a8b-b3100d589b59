// SearchSock_LogReview.cpp: implementation of the CSearchSock_LogReview class.
//
//////////////////////////////////////////////////////////////////////

#include "SearchSock_LogReview.h"

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

/************************************************************************
1)	测试类型（1BYTE）:dt/cqt/autodt/autocqt/扫频测试等；
2)	文件ID（4BYTES）
3)	文件回放类型（1BYTE）：0:概要，1:详细，2:深度
4)	文件回放起始时间（4BYTES）：如果全部回放，起始时间设置为0即可
5)	文件结束起始时间（4BYTES）
************************************************************************/
CSearchSock_ReviewLogFile::CSearchSock_ReviewLogFile()
{
	for (int i = 0; i < 256; i++)
	{
		pFuncTestTypeData[i] = &CSearchSock_ReviewLogFile::DealTestType_Default;
		pFuncServerType_Gsm[i] = &CSearchSock_ReviewLogFile::ReviewLogDefault;
		pFuncServerType_Scan[i] = &CSearchSock_ReviewLogFile::ReviewLogDefault;
		pFuncServerType_Cdma[i] = &CSearchSock_ReviewLogFile::ReviewLogDefault;
		pFuncServerType_Cdma2000[i] = &CSearchSock_ReviewLogFile::ReviewLogDefault;
		pFuncServerType_Tdscdma[i] = &CSearchSock_ReviewLogFile::ReviewLogDefault;
		pFuncServerType_Wcdma[i] = &CSearchSock_ReviewLogFile::ReviewLogDefault;

	}
	//testtype
	pFuncTestTypeData[CEnumDef::T_GSM_DT] = &CSearchSock_ReviewLogFile::DealTestType_Gsm;
	pFuncTestTypeData[CEnumDef::T_GSM_CQT] = &CSearchSock_ReviewLogFile::DealTestType_Gsm;
	pFuncTestTypeData[CEnumDef::T_AUTODT] = &CSearchSock_ReviewLogFile::DealTestType_Gsm;
	pFuncTestTypeData[CEnumDef::T_AUTODT_TA] = &CSearchSock_ReviewLogFile::DealTestType_Gsm;
	pFuncTestTypeData[CEnumDef::T_AUTOCQT] = &CSearchSock_ReviewLogFile::DealTestType_Gsm;

	pFuncTestTypeData[CEnumDef::T_SCANTEST] = &CSearchSock_ReviewLogFile::DealTestType_Scan;

	pFuncTestTypeData[CEnumDef::T_CDMA_DT] = &CSearchSock_ReviewLogFile::DealTestType_Cdma;
	pFuncTestTypeData[CEnumDef::T_CDMA_CQT] = &CSearchSock_ReviewLogFile::DealTestType_Cdma;

	pFuncTestTypeData[CEnumDef::T_CDMA2000_DT] = &CSearchSock_ReviewLogFile::DealTestType_Cdma2000;
	pFuncTestTypeData[CEnumDef::T_CDMA2000_CQT] = &CSearchSock_ReviewLogFile::DealTestType_Cdma2000;

	pFuncTestTypeData[CEnumDef::T_TDSCDMA_DT] = &CSearchSock_ReviewLogFile::DealTestType_Tdscdma;
	pFuncTestTypeData[CEnumDef::T_TDSCDMA_CQT] = &CSearchSock_ReviewLogFile::DealTestType_Tdscdma;

	pFuncTestTypeData[CEnumDef::T_WCDMA_DT] = &CSearchSock_ReviewLogFile::DealTestType_Wcdma;
	pFuncTestTypeData[CEnumDef::T_WCDMA_CQT] = &CSearchSock_ReviewLogFile::DealTestType_Wcdma;

    //service type
	pFuncServerType_Gsm[CEnumDef::Ser_GSM_VOICE] = &CSearchSock_ReviewLogFile::ReviewLogGSM;
	pFuncServerType_Gsm[CEnumDef::Ser_GSM_IDLE] = &CSearchSock_ReviewLogFile::ReviewLogGSM;
	pFuncServerType_Gsm[CEnumDef::Ser_GPRS_DATA] = &CSearchSock_ReviewLogFile::ReviewLogData;
	pFuncServerType_Gsm[CEnumDef::Ser_EDGE_DATA] = &CSearchSock_ReviewLogFile::ReviewLogData;
	pFuncServerType_Gsm[CEnumDef::Ser_GSM_UPLINK] = &CSearchSock_ReviewLogFile::ReviewGSMUpLink;

	pFuncServerType_Scan[CEnumDef::Ser_SCAN] = &CSearchSock_ReviewLogFile::ReviewLogScan;
	pFuncServerType_Scan[CEnumDef::Ser_SCAN_WCDMA] = &CSearchSock_ReviewLogFile::ReviewLogScanWcdma;

	pFuncServerType_Cdma[CEnumDef::Ser_CDMA_VOICE] = &CSearchSock_ReviewLogFile::ReviewLogCDMA;
	pFuncServerType_Cdma[CEnumDef::Ser_CDMA1X_DATA] = &CSearchSock_ReviewLogFile::ReviewLogCDMAData;

	pFuncServerType_Cdma2000[CEnumDef::Ser_CDMA2000_VOICE] = &CSearchSock_ReviewLogFile::ReviewLogDefault;
	pFuncServerType_Cdma2000[CEnumDef::Ser_CDMA2000_DATA] = &CSearchSock_ReviewLogFile::ReviewLogCDMA2000Data;

	pFuncServerType_Tdscdma[CEnumDef::Ser_TDSCDMA_VOICE] = &CSearchSock_ReviewLogFile::ReviewLogTDSCDMA;
	pFuncServerType_Tdscdma[CEnumDef::Ser_TDSCDMA_VIDEO] = &CSearchSock_ReviewLogFile::ReviewLogTDSCDMA;
	pFuncServerType_Tdscdma[CEnumDef::Ser_TDSCDMA_IDLE] = &CSearchSock_ReviewLogFile::ReviewLogTDSCDMA;
	pFuncServerType_Tdscdma[CEnumDef::Ser_TDSCDMA_DATA] = &CSearchSock_ReviewLogFile::ReviewLogTDSCDMAData;
	pFuncServerType_Tdscdma[CEnumDef::Ser_TDSCDMA_HSDPA] = &CSearchSock_ReviewLogFile::ReviewLogTDSCDMAData;

	pFuncServerType_Wcdma[CEnumDef::Ser_WCDMA_VOICE] = &CSearchSock_ReviewLogFile::ReviewLogWCDMA;
	pFuncServerType_Wcdma[CEnumDef::Ser_WCDMA_VIDEO] = &CSearchSock_ReviewLogFile::ReviewLogWCDMA;
	pFuncServerType_Wcdma[CEnumDef::Ser_WCDMA_DATA] = &CSearchSock_ReviewLogFile::ReviewLogWCDMAData;
	pFuncServerType_Wcdma[CEnumDef::Ser_WCDMA_HSDPA] = &CSearchSock_ReviewLogFile::ReviewLogWCDMAData;


}

CSearchSock_ReviewLogFile:: ~CSearchSock_ReviewLogFile()
{

}

BOOL  
CSearchSock_ReviewLogFile::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	MOVENEXTTEST(ioffset,15,nCount);
	STRU_LOG_REVIEW tm_Log_Review;
	
	BYTE btemp = 0xff;
	int itemp = 0;
	
	//==================== projecttype ====================
	PPM_OS::memcpy(&btemp,pData + ioffset,1);
	itemp = btemp & 0x000000ff;		
	tm_Log_Review.iprojecttype = itemp;
	MOVENEXT(ioffset,1,nCount);
	
	//==================== testtype ====================
	btemp = 0xff;
	PPM_OS::memcpy(&btemp,pData + ioffset,1);
	itemp = btemp & 0x000000ff;		
	tm_Log_Review.itesttype = itemp;
	MOVENEXT(ioffset,1,nCount);

	//==================== fileid ====================
	PPM_OS::memcpy(&tm_Log_Review.ifileID,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	
	//==================== reviewtype ====================
	btemp = 0xff;
	PPM_OS::memcpy(&btemp,pData + ioffset,1);
	itemp = btemp & 0x000000ff;
	tm_Log_Review.ireviewtype = itemp;
	MOVENEXT(ioffset,1,nCount);
	
	//==================== time ====================
	PPM_OS::memcpy(&tm_Log_Review.istime,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Log_Review.ietime,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	
	tm_Log_Review.ConvertToNetSeq();

	//==================== logtbname =======================
	char tm_logtbname[255];
	PPM_OS::memset(tm_logtbname, 0, sizeof(tm_logtbname));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_logtbname,sizeof(tm_logtbname)));	

	//==================== get data type ====================
	char tm_sql[1024];
	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_review_log_get %d, %d,'%s'", 
		tm_Log_Review.ifileID,
		tm_Log_Review.iprojecttype,
		tm_logtbname
		);

	m_iRowType_ = TYPE_VEC_TB_INFO;

	if(NULL != m_pDataBaseDeal_)
	{
		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
	}
	Clear();

	if(0 == m_tb_info.fileid)
	{
		//处理结束
		FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
		return TRUE;
	}

	//==================== file head info ====================
    if (!SearchLogFileInfo(tm_logtbname, tm_Log_Review.iprojecttype, tm_Log_Review.ifileID, 0))
    {
		//处理结束
		FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
		return TRUE;
    }

	(this->*pFuncTestTypeData[m_tb_info.nettype])(m_tb_info, tm_Log_Review);

	//处理结束
	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
	return TRUE;
}

void CSearchSock_ReviewLogFile::DealTestType_Default(STRU_TB_INFO m_tb_info, STRU_LOG_REVIEW tm_Log_Review)
{

}
void CSearchSock_ReviewLogFile::DealTestType_Gsm(STRU_TB_INFO m_tb_info, STRU_LOG_REVIEW tm_Log_Review)
{
	(this->*pFuncServerType_Gsm[m_tb_info.servicetype])(m_tb_info, tm_Log_Review);
}
void CSearchSock_ReviewLogFile::DealTestType_Scan(STRU_TB_INFO m_tb_info, STRU_LOG_REVIEW tm_Log_Review)
{
	(this->*pFuncServerType_Scan[m_tb_info.servicetype])(m_tb_info, tm_Log_Review);	
}
void CSearchSock_ReviewLogFile::DealTestType_Cdma(STRU_TB_INFO m_tb_info, STRU_LOG_REVIEW tm_Log_Review)
{
	(this->*pFuncServerType_Cdma[m_tb_info.servicetype])(m_tb_info, tm_Log_Review);
}
void CSearchSock_ReviewLogFile::DealTestType_Cdma2000(STRU_TB_INFO m_tb_info, STRU_LOG_REVIEW tm_Log_Review)
{
    (this->*pFuncServerType_Cdma2000[m_tb_info.servicetype])(m_tb_info, tm_Log_Review);
}
void CSearchSock_ReviewLogFile::DealTestType_Tdscdma(STRU_TB_INFO m_tb_info, STRU_LOG_REVIEW tm_Log_Review)
{
    (this->*pFuncServerType_Tdscdma[m_tb_info.servicetype])(m_tb_info, tm_Log_Review);
}
void CSearchSock_ReviewLogFile::DealTestType_Wcdma(STRU_TB_INFO m_tb_info, STRU_LOG_REVIEW tm_Log_Review)
{
    (this->*pFuncServerType_Wcdma[m_tb_info.servicetype])(m_tb_info, tm_Log_Review);
}

void 
CSearchSock_ReviewLogFile::DealLogSearch_Msg_Detail(STRU_TB_INFO st_tb_info, STRU_LOG_REVIEW st_log_review)
{
	char tm_sql[1024];

	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_review_msg_detail %d ,'%s', %d, %d", 
		st_log_review.ifileID,
		st_tb_info.strmsg.c_str(),
		st_log_review.istime,
		st_log_review.ietime);

	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_LOG_REVIEW_MSG_DETAIL;
	if(NULL != m_pDataBaseDeal_){
		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
	}
	Clear();
}

BOOL 
CSearchSock_ReviewLogFile::ReviewLogData(STRU_TB_INFO st_tb_info, STRU_LOG_REVIEW st_log_review)
{
	char tm_sql[1024];
	
	//==================== event ====================
	DealLogSearch_Event(st_tb_info, st_log_review);
	
	//==================== summary review ====================
	if(0 == st_log_review.ireviewtype)
	{
		PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
		PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
			"exec mc_sp_review_data_sample_summary %d ,'%s', %d, %d", 
			st_log_review.ifileID,
			st_tb_info.strsample.c_str(),
			st_log_review.istime,
			st_log_review.ietime);
		
		m_iRowType_ = TYPE_VEC_REVIEW_DATA_SUMMARY;
		m_bResponseType_ = RESTYPE_LOG_REVIEW_DATA_SAMPLE_SUMMARY;
		if(NULL != m_pDataBaseDeal_){
			m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
		}
		Clear();
	}
	//==================== detail review ====================
	else if(1 == st_log_review.ireviewtype)
	{
		DealLogSearch_Msg_Detail(st_tb_info, st_log_review);
		
		PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
		PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
			"exec mc_sp_review_data_sample_normal %d ,'%s', %d, %d", 
			st_log_review.ifileID,
			st_tb_info.strsample.c_str(),
			st_log_review.istime,
			st_log_review.ietime);
		
		m_iRowType_ = TYPE_VEC_REVIEW_DATA_NORMAL;
		m_bResponseType_ = RESTYPE_LOG_REVIEW_DATA_SAMPLE_NORMAL;
		if(NULL != m_pDataBaseDeal_){
			m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
		}
		Clear();
	}
	//==================== depth review ====================
	else if(2 == st_log_review.ireviewtype)
	{	
		DealLogSearch_Msg_Depth(st_tb_info, st_log_review);

		PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
		PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
			"exec mc_sp_review_data_sample_normal %d ,'%s', %d, %d", 
			st_log_review.ifileID,
			st_tb_info.strsample.c_str(),
			st_log_review.istime,
			st_log_review.ietime);
		
		m_iRowType_ = TYPE_VEC_REVIEW_DATA_NORMAL;
		m_bResponseType_ = RESTYPE_LOG_REVIEW_DATA_SAMPLE_NORMAL;
		if(NULL != m_pDataBaseDeal_)
		{
			m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
		}
		Clear();
	}
	 return TRUE;
}

void 
CSearchSock_ReviewLogFile::DealLogSearch_Msg_Depth(STRU_TB_INFO st_tb_info, STRU_LOG_REVIEW st_log_review)
{
	char tm_sql[1024];

	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_review_msg_depth %d ,'%s', %d, %d", 
		st_log_review.ifileID,
		st_tb_info.strmsg.c_str(),
		st_log_review.istime,
		st_log_review.ietime);

	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_LOG_REVIEW_MSG_DEPTH;
	if(NULL != m_pDataBaseDeal_){
		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
	}
	Clear();
}

void 
CSearchSock_ReviewLogFile::DealLogSearch_Event(STRU_TB_INFO st_tb_info, STRU_LOG_REVIEW st_log_review)
{
	char tm_sql[1024];

	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_review_event %d ,'%s', %d, %d", 
		st_log_review.ifileID,
		st_tb_info.strevent.c_str(),
		st_log_review.istime,
		st_log_review.ietime);

	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_LOG_REVIEW_EVENT;
	if(NULL != m_pDataBaseDeal_)
	{
		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
	}
	Clear();
}

BOOL 
CSearchSock_ReviewLogFile::ReviewLogTDSCDMAData(STRU_TB_INFO st_tb_info, STRU_LOG_REVIEW st_log_review)
{
	char tm_sql[1024];
	
	//==================== event ====================
	DealLogSearch_Event(st_tb_info, st_log_review);
	
	//==================== summary review ====================
	if(0 == st_log_review.ireviewtype)
	{
		PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
		PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
			"exec mc_sp_review_tdscdma_data_sample_summary %d ,'%s', %d, %d", 
			st_log_review.ifileID,
			st_tb_info.strsample.c_str(),
			st_log_review.istime,
			st_log_review.ietime);
		
		m_iRowType_ = TYPE_VEC_REVIEW_TDSCDMA_DATA_SUMMARY;
		m_bResponseType_ = RESTYPE_LOG_REVIEW_TD_DATA_SAMPLE_SUMMARY;
		if(NULL != m_pDataBaseDeal_){
			m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
		}
		Clear();
	}
	//==================== detail review ====================
	else if(1 == st_log_review.ireviewtype)
	{
		DealLogSearch_Msg_Detail(st_tb_info, st_log_review);
		
		PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
		PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
			"exec mc_sp_review_tdscdma_data_sample_normal %d ,'%s', %d, %d", 
			st_log_review.ifileID,
			st_tb_info.strsample.c_str(),
			st_log_review.istime,
			st_log_review.ietime);
		
		m_iRowType_ = TYPE_VEC_REVIEW_TDSCDMA_DATA_NORMAL;
		m_bResponseType_ = RESTYPE_LOG_REVIEW_TD_DATA_SAMPLE_NORMAL;
		if(NULL != m_pDataBaseDeal_){
			m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
		}
		Clear();
	}
	//==================== depth review ====================
	else if(2 == st_log_review.ireviewtype)
	{	
		DealLogSearch_Msg_Depth(st_tb_info, st_log_review);

		PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
		PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
			"exec mc_sp_review_tdscdma_data_sample_normal %d ,'%s', %d, %d", 
			st_log_review.ifileID,
			st_tb_info.strsample.c_str(),
			st_log_review.istime,
			st_log_review.ietime);
		
		m_iRowType_ = TYPE_VEC_REVIEW_TDSCDMA_DATA_NORMAL;
		m_bResponseType_ = RESTYPE_LOG_REVIEW_TD_DATA_SAMPLE_NORMAL;
		if(NULL != m_pDataBaseDeal_)
		{
			m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
		}
		Clear();
	}
	 return TRUE;
}

BOOL 
CSearchSock_ReviewLogFile::ReviewLogWCDMAData(STRU_TB_INFO st_tb_info, STRU_LOG_REVIEW st_log_review)
{
	char tm_sql[1024];
	
	//==================== event ====================
	DealLogSearch_Event(st_tb_info, st_log_review);
	
	//==================== summary review ====================
	if(0 == st_log_review.ireviewtype)
	{
		PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
		PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
			"exec mc_sp_review_wcdma_data_sample_summary %d ,'%s', %d, %d", 
			st_log_review.ifileID,
			st_tb_info.strsample.c_str(),
			st_log_review.istime,
			st_log_review.ietime);
		
		m_iRowType_ = TYPE_VEC_REVIEW_WCDMA_DATA_SUMMARY;
		m_bResponseType_ = RESTYPE_LOG_REVIEW_W_DATA_SAMPLE_SUMMARY;
		if(NULL != m_pDataBaseDeal_){
			m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
		}
		Clear();
	}
	//==================== detail review ====================
	else if(1 == st_log_review.ireviewtype)
	{
		DealLogSearch_Msg_Detail(st_tb_info, st_log_review);
		
		PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
		PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
			"exec mc_sp_review_wcdma_data_sample_normal %d ,'%s', %d, %d", 
			st_log_review.ifileID,
			st_tb_info.strsample.c_str(),
			st_log_review.istime,
			st_log_review.ietime);
		
		m_iRowType_ = TYPE_VEC_REVIEW_WCDMA_DATA_NORMAL;
		m_bResponseType_ = RESTYPE_LOG_REVIEW_W_DATA_SAMPLE_NORMAL;
		if(NULL != m_pDataBaseDeal_){
			m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
		}
		Clear();
	}
	//==================== depth review ====================
	else if(2 == st_log_review.ireviewtype)
	{	
		DealLogSearch_Msg_Depth(st_tb_info, st_log_review);

		PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
		PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
			"exec mc_sp_review_wcdma_data_sample_normal %d ,'%s', %d, %d", 
			st_log_review.ifileID,
			st_tb_info.strsample.c_str(),
			st_log_review.istime,
			st_log_review.ietime);
		
		m_iRowType_ = TYPE_VEC_REVIEW_WCDMA_DATA_NORMAL;
		m_bResponseType_ = RESTYPE_LOG_REVIEW_W_DATA_SAMPLE_NORMAL;
		if(NULL != m_pDataBaseDeal_)
		{
			m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
		}
		Clear();
	}
	 return TRUE;
}


BOOL 
CSearchSock_ReviewLogFile::ReviewLogGSM(STRU_TB_INFO st_tb_info, STRU_LOG_REVIEW st_log_review)
{
	char tm_sql[1024];

	//==================== event ====================
	DealLogSearch_Event(st_tb_info, st_log_review);

	//==================== summary review ====================
	if(0 == st_log_review.ireviewtype)
	{
		PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
		PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
			"exec mc_sp_review_gsm_sample_summary %d ,'%s', %d, %d", 
			st_log_review.ifileID,
			st_tb_info.strsample.c_str(),
			st_log_review.istime,
			st_log_review.ietime);
		
		m_iRowType_ = TYPE_VEC_NORMAL;
		m_bResponseType_ = RESTYPE_LOG_REVIEW_SAMPLE_SUMMARY;
		SEARCHSQL(m_iRowType_, tm_sql);
	}
	//==================== detail review ====================
	else if(1 == st_log_review.ireviewtype)
	{
		PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
		PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
			"exec mc_sp_review_gsm_sample_normal %d ,'%s', %d, %d", 
			st_log_review.ifileID,
			st_tb_info.strsample.c_str(),
			st_log_review.istime,
			st_log_review.ietime);
		
		m_iRowType_ = TYPE_VEC_NORMAL;
		m_bResponseType_ = RESTYPE_LOG_REVIEW_SAMPLE_NORMAL;
		SEARCHSQL(m_iRowType_, tm_sql);
		//++++++++++++++++++++++++++++++++++++++++++++++++
		DealLogSearch_Msg_Detail(st_tb_info, st_log_review);
	}
	//==================== depth review ====================
	else if(2 == st_log_review.ireviewtype)
	{
		PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
		PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
			"exec mc_sp_review_gsm_sample_normal %d ,'%s', %d, %d", 
			st_log_review.ifileID,
			st_tb_info.strsample.c_str(),
			st_log_review.istime,
			st_log_review.ietime);
		
		m_iRowType_ = TYPE_VEC_NORMAL;
		m_bResponseType_ = RESTYPE_LOG_REVIEW_SAMPLE_NORMAL;
		SEARCHSQL(m_iRowType_, tm_sql);
		//++++++++++++++++++++++++++++++++++++++++++++++++
		PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
		PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
			"exec mc_sp_review_gsm_sample_abnormal %d ,'%s', %d, %d", 
			st_log_review.ifileID,
			st_tb_info.strsample2.c_str(),
			st_log_review.istime,
			st_log_review.ietime);
		
		m_iRowType_ = TYPE_VEC_NORMAL;
		m_bResponseType_ = RESTYPE_LOG_REVIEW_SAMPLE_ABNORMAL;
		SEARCHSQL(m_iRowType_, tm_sql);
		//++++++++++++++++++++++++++++++++++++++++++++++++
		DealLogSearch_Msg_Depth(st_tb_info, st_log_review);
	}

    return TRUE;
}

BOOL 
CSearchSock_ReviewLogFile::ReviewLogScan(STRU_TB_INFO st_tb_info, STRU_LOG_REVIEW st_log_review)
{
	char tm_sql[1024];

	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_review_scan %d ,'%s', %d, %d, %d",
		st_log_review.ifileID,
		st_tb_info.strsuffix.c_str(),
		st_log_review.istime,
		st_log_review.ietime,
		st_log_review.ireviewtype
		);
	
	if(0 == st_log_review.ireviewtype)
	{
		m_iRowType_ = TYPE_VEC_NORMAL;
		m_bResponseType_ = RESTYPE_LOG_REVIEW_SCAN_SUMMARY;
		
		if(NULL != m_pDataBaseDeal_){
			m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
		}
		Clear();
	}
	else if(1 == st_log_review.ireviewtype)
	{
		m_iRowType_ = TYPE_VEC_NORMAL;
		m_bResponseType_ = RESTYPE_LOG_REVIEW_SCAN_DETAIL;
		
		if(NULL != m_pDataBaseDeal_){
			m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
		}
		Clear();
	}
	else if(2 == st_log_review.ireviewtype)
	{
		m_iRowType_ = TYPE_VEC_NORMAL;
		m_bResponseType_ = RESTYPE_LOG_REVIEW_SCAN_DEPTH;
		
		if(NULL != m_pDataBaseDeal_){
			m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
		}
		Clear();
	}
    return TRUE;
}

BOOL 
CSearchSock_ReviewLogFile::ReviewLogCDMA(STRU_TB_INFO st_tb_info, STRU_LOG_REVIEW st_log_review)
{

	char tm_sql[1024];
	
	//==================== event ====================
	DealLogSearch_Event(st_tb_info, st_log_review);

	//==================== summary review ====================
	if(0 == st_log_review.ireviewtype)
	{
		PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
		PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
			"exec mc_sp_review_cdma_sample_summary %d ,'%s', %d, %d", 
			st_log_review.ifileID,
			st_tb_info.strsample.c_str(),
			st_log_review.istime, st_log_review.ietime);
		
		m_iRowType_ = TYPE_VEC_NORMAL;
		m_bResponseType_ = RESTYPE_LOG_REVIEW_CDMA_SAMPLE_SUMMARY;
		SEARCHSQL(m_iRowType_, tm_sql);
	}

	//==================== detail review ====================
	else if(1 == st_log_review.ireviewtype)
	{
		DealLogSearch_Msg_Detail(st_tb_info, st_log_review);
		
		PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
		PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
			"exec mc_sp_review_cdma_sample_normal %d ,'%s', %d, %d", 
			st_log_review.ifileID,
			st_tb_info.strsample.c_str(),
			st_log_review.istime, st_log_review.ietime);
		
		m_iRowType_ = TYPE_VEC_REVIEW_CDMA_NORMAL;
		m_bResponseType_ = RESTYPE_LOG_REVIEW_CDMA_SAMPLE_NORMAL;
		SEARCHSQL(m_iRowType_, tm_sql);
	}

    //==================== depth review ====================
	else if(2 == st_log_review.ireviewtype)
	{
		DealLogSearch_Msg_Depth(st_tb_info, st_log_review);

		PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
		PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
			"exec mc_sp_review_cdma_sample_normal %d ,'%s', %d, %d", 
			st_log_review.ifileID,
			st_tb_info.strsample.c_str(),
			st_log_review.istime, st_log_review.ietime);
		
		m_iRowType_ = TYPE_VEC_REVIEW_CDMA_NORMAL;
		m_bResponseType_ = RESTYPE_LOG_REVIEW_CDMA_SAMPLE_NORMAL;
		SEARCHSQL(m_iRowType_, tm_sql);
	}
    return TRUE;
}

BOOL 
CSearchSock_ReviewLogFile::ReviewLogCDMAData(STRU_TB_INFO st_tb_info, STRU_LOG_REVIEW st_log_review)
{

	char tm_sql[1024];
	
	//==================== event ====================
	DealLogSearch_Event(st_tb_info, st_log_review);

	//==================== summary review ====================
	if(0 == st_log_review.ireviewtype)
	{
		PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
		PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
			"exec mc_sp_review_cdma_sample_summary %d ,'%s', %d, %d", 
			st_log_review.ifileID,
			st_tb_info.strsample.c_str(),
			st_log_review.istime,
			st_log_review.ietime);
		
		m_iRowType_ = TYPE_VEC_NORMAL;
		m_bResponseType_ = RESTYPE_LOG_REVIEW_CDMA_DATA_SAMPLE_SUMMARY;
		SEARCHSQL(m_iRowType_, tm_sql);
	}

	//==================== detail review ====================
	else if(1 == st_log_review.ireviewtype)
	{
		DealLogSearch_Msg_Detail(st_tb_info, st_log_review);
		
		PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
		PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
			"exec mc_sp_review_cdma_sample_normal %d ,'%s', %d, %d", 
			st_log_review.ifileID,
			st_tb_info.strsample.c_str(),
			st_log_review.istime,
			st_log_review.ietime);
		
		m_iRowType_ = TYPE_VEC_REVIEW_CDMA_NORMAL;
		m_bResponseType_ = RESTYPE_LOG_REVIEW_CDMA_DATA_SAMPLE_NORMAL;
		SEARCHSQL(m_iRowType_, tm_sql);
	}

    //==================== depth review ====================
	else if(2 == st_log_review.ireviewtype)
	{
		DealLogSearch_Msg_Depth(st_tb_info, st_log_review);

		PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
		PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
			"exec mc_sp_review_cdma_sample_normal %d ,'%s', %d, %d", 
			st_log_review.ifileID,
			st_tb_info.strsample.c_str(),
			st_log_review.istime,
			st_log_review.ietime);
		
		m_iRowType_ = TYPE_VEC_REVIEW_CDMA_NORMAL;
		m_bResponseType_ = RESTYPE_LOG_REVIEW_CDMA_DATA_SAMPLE_NORMAL;
		SEARCHSQL(m_iRowType_, tm_sql);
	}
    return TRUE;
}

BOOL 
CSearchSock_ReviewLogFile::ReviewLogCDMA2000Data(STRU_TB_INFO st_tb_info, STRU_LOG_REVIEW st_log_review)
{

	char tm_sql[1024];
	
	//==================== event ====================
	DealLogSearch_Event(st_tb_info, st_log_review);

	//==================== summary review ====================
	if(0 == st_log_review.ireviewtype)
	{
		PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
		PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
			"exec mc_sp_review_cdma2000_data_sample_summary %d ,'%s', %d, %d", 
			st_log_review.ifileID,
			st_tb_info.strsample.c_str(),
			st_log_review.istime,
			st_log_review.ietime);
		
		m_iRowType_ = TYPE_VEC_NORMAL;
		m_bResponseType_ = RESTYPE_LOG_REVIEW_CDMA2000_DATA_SAMPLE_SUMMARY;
		if(NULL != m_pDataBaseDeal_){
			m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
		}
		Clear();
	}

	//==================== detail review ====================
	else if(1 == st_log_review.ireviewtype)
	{
		DealLogSearch_Msg_Detail(st_tb_info, st_log_review);
		
		PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
		PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
			"exec mc_sp_review_cdma2000_data_sample_normal %d ,'%s', %d, %d", 
			st_log_review.ifileID,
			st_tb_info.strsample.c_str(),
			st_log_review.istime,
			st_log_review.ietime);
		
		m_iRowType_ = TYPE_VEC_REVIEW_CDMA_NORMAL;
		m_bResponseType_ = RESTYPE_LOG_REVIEW_CDMA2000_DATA_SAMPLE_NORMAL;
		if(NULL != m_pDataBaseDeal_){
			m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
		}
		Clear();
	}

    //==================== depth review ====================
	else if(2 == st_log_review.ireviewtype)
	{
		DealLogSearch_Msg_Depth(st_tb_info, st_log_review);

		PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
		PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
			"exec mc_sp_review_cdma2000_data_sample_normal %d ,'%s', %d, %d", 
			st_log_review.ifileID,
			st_tb_info.strsample.c_str(),
			st_log_review.istime,
			st_log_review.ietime);
		
		m_iRowType_ = TYPE_VEC_REVIEW_CDMA_NORMAL;
		m_bResponseType_ = RESTYPE_LOG_REVIEW_CDMA2000_DATA_SAMPLE_NORMAL;
		if(NULL != m_pDataBaseDeal_){
			m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
		}
		Clear();
	}
    return TRUE;
}

BOOL 
CSearchSock_ReviewLogFile::ReviewLogTDSCDMA(STRU_TB_INFO st_tb_info, STRU_LOG_REVIEW st_log_review)
{
	char tm_sql[1024];
	
	//==================== event ====================
	DealLogSearch_Event(st_tb_info, st_log_review);
	
	//==================== summary review ====================
	if(0 == st_log_review.ireviewtype)
	{
		PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
		PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
			"exec mc_sp_review_tdscdma_sample_summary %d ,'%s', %d, %d", 
			st_log_review.ifileID,
			st_tb_info.strsample.c_str(),
			st_log_review.istime,
			st_log_review.ietime);
		
		m_iRowType_ = TYPE_VEC_NORMAL;
		m_bResponseType_ = RESTYPE_LOG_REVIEW_TDSCDMA_SAMPLE_SUMMARY;
		if(NULL != m_pDataBaseDeal_){
			m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
		}
		Clear();
	}
	//==================== detail review ====================
	else if(1 == st_log_review.ireviewtype)
	{
		DealLogSearch_Msg_Detail(st_tb_info, st_log_review);
		
		PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
		PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
			"exec mc_sp_review_tdscdma_sample_normal %d ,'%s', %d, %d", 
			st_log_review.ifileID,
			st_tb_info.strsample.c_str(),
			st_log_review.istime,
			st_log_review.ietime);
		
		m_iRowType_ = TYPE_VEC_NORMAL;
		m_bResponseType_ = RESTYPE_LOG_REVIEW_TDSCDMA_SAMPLE_NORMAL;
		if(NULL != m_pDataBaseDeal_){
			m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
		}
		Clear();
	}
	//==================== depth review ====================
	else if(2 == st_log_review.ireviewtype)
	{	
		DealLogSearch_Msg_Depth(st_tb_info, st_log_review);

		PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
		PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
			"exec mc_sp_review_tdscdma_sample_normal %d ,'%s', %d, %d", 
			st_log_review.ifileID,
			st_tb_info.strsample.c_str(),
			st_log_review.istime,
			st_log_review.ietime);
		
		m_iRowType_ = TYPE_VEC_NORMAL;
		m_bResponseType_ = RESTYPE_LOG_REVIEW_TDSCDMA_SAMPLE_NORMAL;
		if(NULL != m_pDataBaseDeal_)
		{
			m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
		}
		Clear();
	}
	 return TRUE;
}


BOOL
CSearchSock_ReviewLogFile::ReviewLogWCDMA(STRU_TB_INFO st_tb_info, STRU_LOG_REVIEW st_log_review)
{
	char tm_sql[255];

	//==================== event ====================
	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_review_event %d ,'%s', %d, %d", 
		st_log_review.ifileID,
		st_tb_info.strevent.c_str(),
		st_log_review.istime,
		st_log_review.ietime);
	
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_LOG_REVIEW_EVENT;
	if(NULL != m_pDataBaseDeal_)
	{
		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
	}
	Clear();

	//==================== summary review ====================
	if(0 == st_log_review.ireviewtype)
	{
		PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
		PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
			"exec mc_sp_review_wcdma_sample_summary %d ,'%s', %d, %d", 
			st_log_review.ifileID,
			st_tb_info.strsample.c_str(),
			st_log_review.istime,
			st_log_review.ietime);
		
		m_iRowType_ = TYPE_VEC_NORMAL;
		m_bResponseType_ = RESTYPE_LOG_REVIEW_WCDMA_SAMPLE_SUMMARY;
		if(NULL != m_pDataBaseDeal_)
		{
			m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
		}
		Clear();
	}
	//==================== detail review ====================
	else if(1 == st_log_review.ireviewtype)
	{
		PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
		PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
			"exec mc_sp_review_wcdma_sample_normal %d ,'%s', %d, %d", 
			st_log_review.ifileID,
			st_tb_info.strsample.c_str(),
			st_log_review.istime,
			st_log_review.ietime);
		
		m_iRowType_ = TYPE_VEC_REVIEW_WCDMA_DATA_NORMAL;
		m_bResponseType_ = RESTYPE_LOG_REVIEW_WCDMA_SAMPLE_NORMAL;
		if(NULL != m_pDataBaseDeal_)
		{
			m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
		}
		Clear();
		//++++++++++++++++++++++++++++++++++++++++++++++++
		PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
		PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
			"exec mc_sp_review_msg_detail %d ,'%s', %d, %d", 
			st_log_review.ifileID,
			st_tb_info.strmsg.c_str(),
			st_log_review.istime,
			st_log_review.ietime);
		
		m_iRowType_ = TYPE_VEC_NORMAL;
		m_bResponseType_ = RESTYPE_LOG_REVIEW_MSG_DETAIL;
		if(NULL != m_pDataBaseDeal_)
		{
			m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
		}
		Clear();
	}
	//==================== depth review ====================
	else if(2 == st_log_review.ireviewtype)
	{
		PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
		PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
			"exec mc_sp_review_msg_depth %d ,'%s', %d, %d",
			st_log_review.ifileID,
			st_tb_info.strmsg.c_str(),
			st_log_review.istime,
			st_log_review.ietime);
		
		m_iRowType_ = TYPE_VEC_NORMAL;
		m_bResponseType_ = RESTYPE_LOG_REVIEW_MSG_DEPTH;
		if(NULL != m_pDataBaseDeal_){
			m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
		}
		Clear();
		
		PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
		PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
			"exec mc_sp_review_wcdma_sample_normal %d ,'%s', %d, %d",
			st_log_review.ifileID,
			st_tb_info.strsample.c_str(),
			st_log_review.istime,
			st_log_review.ietime);
		
		m_iRowType_ = TYPE_VEC_REVIEW_WCDMA_DATA_NORMAL;
		m_bResponseType_ = RESTYPE_LOG_REVIEW_WCDMA_SAMPLE_NORMAL;
		if(NULL != m_pDataBaseDeal_)
		{
			m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
		}
		Clear();
	}
    return TRUE;
}


BOOL
CSearchSock_ReviewLogFile::ReviewGSMUpLink(STRU_TB_INFO st_tb_info, STRU_LOG_REVIEW st_log_review)
{
	char tm_sql[1024];

	//==================== summary review ====================
	if(0 == st_log_review.ireviewtype)
	{

	}
	//==================== detail review ====================
	else if(1 == st_log_review.ireviewtype)
	{

	}
	//==================== depth review ====================
	else if(2 == st_log_review.ireviewtype)
	{	
		DealLogSearch_Msg_Depth(st_tb_info, st_log_review);

		PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
		PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
			"exec mc_sp_review_ulgsm_sample_normal %d ,'%s', %d, %d", 
			st_log_review.ifileID,
			st_tb_info.strsample.c_str(),
			st_log_review.istime,
			st_log_review.ietime);

		m_iRowType_ = TYPE_VEC_REVIEW_ULGSM_NORMAL;
		m_bResponseType_ = RESTYPE_LOG_REVIEW_ULGSM_NORMAL;
		SEARCHSQL(m_iRowType_, tm_sql);
	}
	return TRUE;
}

BOOL 
CSearchSock_ReviewLogFile::ReviewLogScanWcdma(STRU_TB_INFO st_tb_info, STRU_LOG_REVIEW st_log_review)
{
	char tm_sql[1024];

	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_review_scan_wcdma %d ,'%s', %d, %d, %d",
		st_log_review.ifileID,
		st_tb_info.strsample.c_str(),
		st_log_review.istime,
		st_log_review.ietime,
		st_log_review.ireviewtype
		);

	if(0 == st_log_review.ireviewtype)
	{
		m_iRowType_ = TYPE_VEC_REVIEW_SCAN_WCDMA;
		m_bResponseType_ = RESTYPE_LOG_REVIEW_SCAN_WCDMA;
		SEARCHSQL(m_iRowType_, tm_sql);
	}

	return TRUE;
}

BOOL CSearchSock_ReviewLogFile::ReviewLogDefault(STRU_TB_INFO st_tb_info, STRU_LOG_REVIEW st_log_review)
{
	return TRUE;
}













