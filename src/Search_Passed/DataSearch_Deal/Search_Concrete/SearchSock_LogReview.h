// SearchSock_LogReview.h: interface for the CSearchSock_LogReview class.
//
//////////////////////////////////////////////////////////////////////

#ifndef __SEARCHSOCK_LOGREVIEW_H__
#define __SEARCHSOCK_LOGREVIEW_H__

#include "./SearchSock.h"

class CSearchSock_ReviewLogFile : public CSearchSock
{
public:
    CSearchSock_ReviewLogFile();
    virtual ~CSearchSock_ReviewLogFile();

	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);

protected:
	void (CSearchSock_ReviewLogFile::*pFuncTestTypeData[256])(STRU_TB_INFO m_tb_info, STRU_LOG_REVIEW tm_Log_Review);
	BOOL (CSearchSock_ReviewLogFile::*pFuncServerType_Gsm[256])(STRU_TB_INFO st_tb_info, STRU_LOG_REVIEW st_log_review);
	BOOL (CSearchSock_ReviewLogFile::*pFuncServerType_Scan[256])(STRU_TB_INFO st_tb_info, STRU_LOG_REVIEW st_log_review);
	BOOL (CSearchSock_ReviewLogFile::*pFuncServerType_Cdma[256])(STRU_TB_INFO st_tb_info, STRU_LOG_REVIEW st_log_review);
	BOOL (CSearchSock_ReviewLogFile::*pFuncServerType_Cdma2000[256])(STRU_TB_INFO st_tb_info, STRU_LOG_REVIEW st_log_review);
	BOOL (CSearchSock_ReviewLogFile::*pFuncServerType_Tdscdma[256])(STRU_TB_INFO st_tb_info, STRU_LOG_REVIEW st_log_review);
	BOOL (CSearchSock_ReviewLogFile::*pFuncServerType_Wcdma[256])(STRU_TB_INFO st_tb_info, STRU_LOG_REVIEW st_log_review);

	void DealTestType_Default(STRU_TB_INFO m_tb_info, STRU_LOG_REVIEW tm_Log_Review);
	void DealTestType_Gsm(STRU_TB_INFO m_tb_info, STRU_LOG_REVIEW tm_Log_Review);
	void DealTestType_Scan(STRU_TB_INFO m_tb_info, STRU_LOG_REVIEW tm_Log_Review);
	void DealTestType_Cdma(STRU_TB_INFO m_tb_info, STRU_LOG_REVIEW tm_Log_Review);
	void DealTestType_Cdma2000(STRU_TB_INFO m_tb_info, STRU_LOG_REVIEW tm_Log_Review);
	void DealTestType_Tdscdma(STRU_TB_INFO m_tb_info, STRU_LOG_REVIEW tm_Log_Review);
	void DealTestType_Wcdma(STRU_TB_INFO m_tb_info, STRU_LOG_REVIEW tm_Log_Review);


	BOOL ReviewLogData(STRU_TB_INFO st_tb_info, STRU_LOG_REVIEW st_log_review);
	BOOL ReviewLogGSM(STRU_TB_INFO st_tb_info, STRU_LOG_REVIEW st_log_review);
	BOOL ReviewLogScan(STRU_TB_INFO st_tb_info, STRU_LOG_REVIEW st_log_review);
	BOOL ReviewLogCDMA(STRU_TB_INFO st_tb_info, STRU_LOG_REVIEW st_log_review);
	BOOL ReviewLogCDMAData(STRU_TB_INFO st_tb_info, STRU_LOG_REVIEW st_log_review);
	BOOL ReviewLogCDMA2000Data(STRU_TB_INFO st_tb_info, STRU_LOG_REVIEW st_log_review);
	BOOL ReviewLogTDSCDMA(STRU_TB_INFO st_tb_info, STRU_LOG_REVIEW st_log_review);
	BOOL ReviewLogTDSCDMAData(STRU_TB_INFO st_tb_info, STRU_LOG_REVIEW st_log_review);
	BOOL ReviewLogWCDMA(STRU_TB_INFO st_tb_info, STRU_LOG_REVIEW st_log_review);
	BOOL ReviewLogWCDMAData(STRU_TB_INFO st_tb_info, STRU_LOG_REVIEW st_log_review);
	BOOL ReviewGSMUpLink(STRU_TB_INFO st_tb_info, STRU_LOG_REVIEW st_log_review);
	BOOL ReviewLogScanWcdma(STRU_TB_INFO st_tb_info, STRU_LOG_REVIEW st_log_review);
	BOOL ReviewLogDefault(STRU_TB_INFO st_tb_info, STRU_LOG_REVIEW st_log_review);
	void DealLogSearch_Event(STRU_TB_INFO st_tb_info, STRU_LOG_REVIEW st_log_review);
	void DealLogSearch_Msg_Detail(STRU_TB_INFO st_tb_info, STRU_LOG_REVIEW st_log_review);
	void DealLogSearch_Msg_Depth(STRU_TB_INFO st_tb_info, STRU_LOG_REVIEW st_log_review);

};

#endif
