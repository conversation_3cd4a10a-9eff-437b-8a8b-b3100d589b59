// SearchSock_Other.cpp: implementation of the CSearchSock_Other class.
//
//////////////////////////////////////////////////////////////////////

#include "SearchSock_Other.h"

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

/************************************************************************
CSearchSock_RoadRate
************************************************************************/
BOOL  
CSearchSock_RoadRate::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	MOVENEXTTEST(ioffset,17,nCount);
	
	if (!GetRoadRateTimeSpan(pData, nCount, ioffset))
	{
		return FALSE;
	}
	
	//type
	char tm_sql_part[1024];
	PPM_OS::memset(&tm_sql_part, 0, sizeof(tm_sql_part));
	if(FALSE == CSearch_PI::GetTypeSql(pData,nCount,ioffset,tm_sql_part)){
		return FALSE;
	}
	
	if (!GetRoadRateSearchAreaInfo(pData, nCount, ioffset))
	{
		return FALSE;
	}
	
	char tm_sql[2048];
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
        "exec sp_road_rate_get %d,%d, %d,%d,%d,%d, '%s'",
		tm_Time_Span.istime, 
		tm_Time_Span.ietime,
		tm_Search_Area.itllongitude,
		tm_Search_Area.itllatitude,
		tm_Search_Area.ibrlongitude,
		tm_Search_Area.ibrlatitude,
		tm_sql_part);
	
	
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_ROAD_RATE;
	if(NULL != m_pDataBaseDeal_)
	{
		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
	}
	Clear();
	
	//处理结束
	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
	
	return TRUE;
}

BOOL  
CSearchSock_RoadRate::GetRoadRateTimeSpan(const BYTE* const pData, const int nCount,int& ioffset)
{
	//time
	PPM_OS::memcpy(&tm_Time_Span.istime,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Time_Span.ietime,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	tm_Time_Span.ConvertToNetSeq();	
    return TRUE;
}

BOOL  
CSearchSock_RoadRate::GetRoadRateSearchAreaInfo(const BYTE* const pData, const int nCount,int& ioffset)
{
	//longitude and latitude
	PPM_OS::memcpy(&tm_Search_Area.itllongitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Search_Area.itllatitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Search_Area.ibrlongitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Search_Area.ibrlatitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);

	tm_Search_Area.ConvertToNetSeq();
    return TRUE;
}
/************************************************************************
CSearchSock_ProMaxTime
************************************************************************/
BOOL  
CSearchSock_ProMaxTime::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	char tm_sql[256];	
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec sp_project_maxtime_get");
	
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_PROJECT_MAXTIME;
	
	if(NULL != m_pDataBaseDeal_){
		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
	}
	Clear();
	
	//处理结束
	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
	return TRUE;	
}


/************************************************************************
CSearchSock_RoadMonitInfo
************************************************************************/
BOOL  
CSearchSock_RoadMonitInfo::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
   	int itime;
	PPM_OS::memcpy(&itime,pData + ioffset,4);
	itime = MAKEINT_NETSEQ1(itime);
	MOVENEXT(ioffset,4,nCount);
	
	char tm_sql[1024];
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec mc_sp_roadmonit_info_get %d" ,
		itime);
	
	m_vec_temp_int.clear();
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_ROADMONIT_INFO;
	if(NULL != m_pDataBaseDeal_)
	{
		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
	}
	Clear();
    
	if (0 == m_vec_temp_int.size())
	{
		//处理结束
		FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
		return TRUE;
	}
	
    char id_list[1024];
	PPM_OS::memset(id_list, 0, sizeof(id_list));
	for (size_t i=0; i<m_vec_temp_int.size(); i++)
	{
		if (0 == i)
		{
			PPM_OS::sprintf(id_list, "%d", m_vec_temp_int[i]);
		}
		else 
		{
			PPM_OS::sprintf(id_list, "%s,%d", &id_list, m_vec_temp_int[i]);
		}
	}
	
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec mc_sp_roadmonit_event_get '%s'" ,
		&id_list);
	
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_ROADMONIT_EVENT;
	if(NULL != m_pDataBaseDeal_)
	{
		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
	}
	Clear();
	
	//处理结束
	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
	return TRUE;
	
}

/************************************************************************
CSearchSock_CompBenchUnitInfo
************************************************************************/
BOOL  
CSearchSock_CompBenchUnitInfo::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{  
	//time
	if (!GetUnitInfoTimeSpan(pData, nCount, ioffset))
	{
		return FALSE;
	}
	
    //longitude and latitude
	if (!GetUnitInfoSearchAreaInfo(pData, nCount, ioffset))
	{
		return FALSE;
	}
	
	char tm_sql[8000];
	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec sp_compbench_date_get %d,%d",
		tm_Time_Span.istime,
		tm_Time_Span.ietime
		);
	
	m_vec_temp_int.clear();
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_COMPBENCH_DATE;
	if(NULL != m_pDataBaseDeal_)
	{
		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
	}
	Clear();
   	
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec sp_compbench_unit_get %d,%d,%d,%d" ,
		tm_Search_Area.itllongitude,     
		tm_Search_Area.itllatitude,
		tm_Search_Area.ibrlongitude,
		tm_Search_Area.ibrlatitude
		);
	
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_COMPBENCH_UNIT_INFO;
	if(NULL != m_pDataBaseDeal_)
	{
		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
	}
	Clear();
				
	//处理结束
	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
	return TRUE;
	
}

BOOL  
CSearchSock_CompBenchUnitInfo::GetUnitInfoTimeSpan(const BYTE* const pData, const int nCount,int& ioffset)
{
	STRU_SEARCH_TIMESPAN tm_Time_Span;
	//time
	PPM_OS::memcpy(&tm_Time_Span.istime,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Time_Span.ietime,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	tm_Time_Span.ConvertToNetSeq();	
    return TRUE;
}

BOOL  
CSearchSock_CompBenchUnitInfo::GetUnitInfoSearchAreaInfo(const BYTE* const pData, const int nCount,int& ioffset)
{
	PPM_OS::memcpy(&tm_Search_Area.itllongitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Search_Area.itllatitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Search_Area.ibrlongitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Search_Area.ibrlatitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);

	tm_Search_Area.ConvertToNetSeq();
    return TRUE;
}
/************************************************************************
CSearchSock_TDSCDMANetRate
************************************************************************/
//td网络占有率
BOOL  
CSearchSock_TDSCDMANetRate::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	//time
	if (!GetTime(pData, nCount, ioffset))
	{
	  return FALSE;
	}

	if (!GetFileList(pData, nCount, ioffset))
	{
	  return FALSE;
	}

	if(0 >= m_map_filelist.size())	//no data
	{
	  //处理结束
	  FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
	  return TRUE;
	}

	int bOnce;
	int cutnum = 600;         //ado对提交的sql语句限制为8000字节以内
	char tm_file[8000];
	auto p_map = m_map_filelist.begin();   
	  
	for (; p_map != m_map_filelist.end(); p_map++)   
	{  
		auto p_vec = p_map->second.begin();	
		PPM_OS::memset(&tm_file, 0, sizeof(tm_file));	
		bOnce = 0;	
		size_t allcount=1;	

		for (int count=1; p_vec != p_map->second.end(); ++count, ++allcount, p_vec++)   
		{   
			if(0 == bOnce)
			{
				PPM_OS::snprintf(tm_file,sizeof(tm_file),"%d",*p_vec); 
				bOnce = 1;
			}
			else{
				PPM_OS::snprintf(tm_file,sizeof(tm_file),"%s,%d",tm_file,*p_vec); 
			}	
			if(cutnum == count || allcount == p_map->second.size())
			{  
				DealTestTypeTdscdma(p_map->first.testtype, p_map->first.strlog,  p_map->first.strtb, tm_file);

				PPM_OS::memset(tm_file, 0, sizeof(tm_file));
				count = 0;
				bOnce = 0;
			}
		}         
	} 
	//处理结束
	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);

	return TRUE;
}

BOOL CSearchSock_TDSCDMANetRate::GetTime(const BYTE* const pData, const int nCount,int& ioffset)
{
	PPM_OS::memcpy(&tm_Time_Span.istime,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Time_Span.ietime,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	tm_Time_Span.ConvertToNetSeq();
	return TRUE;
}
BOOL CSearchSock_TDSCDMANetRate::GetFileList(const BYTE* const pData, const int nCount,int& ioffset)
{
	//type
	char tm_sql_part[1024];
	PPM_OS::memset(tm_sql_part, 0, sizeof(tm_sql_part));
	if(FALSE == CSearch_PI::GetTypeSql(pData,nCount,ioffset,tm_sql_part)){
		return FALSE;
	}

	//longitude and latitude
	if (!GetTdscdmaNetRateSearchAreaInfo(pData, nCount, ioffset))
	{
		return FALSE;
	}

	char tm_sql[9000];
	PPM_OS::memset(tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec mc_sp_area_cover_get %d,%d, %d,%d,%d,%d, '%s',%d" ,
		tm_Time_Span.istime, 
		tm_Time_Span.ietime,
		tm_Search_Area.itllongitude,     
		tm_Search_Area.itllatitude,
		tm_Search_Area.ibrlongitude,
		tm_Search_Area.ibrlatitude,
		tm_sql_part,
		2);

	m_iRowType_ = TYPE_VEC_FILE_LIST_SAMPLE;//文件id获取 +　采样点数目获取
    SEARCHSQL(m_iRowType_, tm_sql);
	return TRUE;
}

BOOL CSearchSock_TDSCDMANetRate::GetTdscdmaNetRateSearchAreaInfo(const BYTE* const pData, const int nCount,int& ioffset)
{
	PPM_OS::memcpy(&tm_Search_Area.itllongitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Search_Area.itllatitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Search_Area.ibrlongitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Search_Area.ibrlatitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);

	tm_Search_Area.ConvertToNetSeq();	
    return TRUE;
}

void CSearchSock_TDSCDMANetRate::DealTestTypeTdscdma(int testtype, std::string strlog, std::string strtb, char* tm_file)
{
	if(CEnumDef::T_TDSCDMA_DT == testtype)  //TDSCDMA
	{
		char tm_sql[9000];
		PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
		PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
			"exec mc_sp_area_cover_tdscdma_netrate_image_get %d,%d, %d,%d,%d,%d, %d,'%s','%s','%s'" ,
			tm_Time_Span.istime, 
			tm_Time_Span.ietime,
			tm_Search_Area.itllongitude,
			tm_Search_Area.itllatitude,
			tm_Search_Area.ibrlongitude,
			tm_Search_Area.ibrlatitude,
			testtype,
			strlog.c_str(),
			strtb.c_str(),
			tm_file);

		m_iRowType_ = TYPE_VEC_NORMAL;
		m_bResponseType_ = RESTYPE_TDSCDMA_NETRATE_IMAGE;
		SEARCHSQL(m_iRowType_, tm_sql);
	}
}
/************************************************************************
CSearchSock_DocDownLoadInfo
************************************************************************/
//测试报告信息查询
BOOL   
CSearchSock_DocDownLoadInfo::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	char projecttype[1024];
	PPM_OS::memset(projecttype,0,sizeof(projecttype));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,projecttype,sizeof(projecttype)));
	
	int iyear;
	PPM_OS::memcpy(&iyear,pData + ioffset,4);
	iyear = MAKEINT_NETSEQ1(iyear);
	MOVENEXT(ioffset,4,nCount);
	
	int ibatch;
	PPM_OS::memcpy(&ibatch,pData + ioffset,4);
	ibatch = MAKEINT_NETSEQ1(ibatch);
	MOVENEXT(ioffset,4,nCount);
	
	char tm_sql[512];
	PPM_OS::memset(tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),"exec mc_sp_log_getDocInfo '%s',%d,%d",
		projecttype,iyear,ibatch
		);
	
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RSQTYPE_DOWNLOADINFO;
	
	if(NULL != m_pDataBaseDeal_){
		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
		//处理结束
		FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
	}
	Clear();
	return TRUE;
}


/************************************************************************
CSearchSock_CompareCmccCu
************************************************************************/
BOOL 
CSearchSock_CompareCmccCu::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	MOVENEXTTEST(ioffset,17,nCount);
	//time
	if (!GetCompareCmccCuTimeSpan(pData, nCount, ioffset))
	{
		return FALSE;
	}
	
	if (!GetCompareCmccCuSearchAreaInfo(pData, nCount, ioffset))
	{
		return FALSE;
	}
	
	BYTE btemp = 0xff;
	PPM_OS::memcpy(&btemp,pData + ioffset,1);
	int itemp = btemp & 0x000000ff;
	
	char tm_sql[9000];
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec mc_sp_area_cover_grid_get_vs %d,%d, %d,%d,%d,%d ,%d" ,
		tm_Time_Span.istime, 
		tm_Time_Span.ietime,
		tm_Search_Area.itllongitude,     
		tm_Search_Area.itllatitude,
		tm_Search_Area.ibrlongitude,
		tm_Search_Area.ibrlatitude,
		itemp);
	
	m_map_filelist.clear();
	m_iRowType_ = TYPE_VEC_FILE_LIST;             //文件id获取 +　采样点数目获取
	
	if(NULL != m_pDataBaseDeal_)
	{
		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
	}
	Clear();
	
	if(0 >= m_map_filelist.size())	//no data
	{
		//处理结束
		FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
		return TRUE;
	}
	
	char tm_file[8000];//ado对提交的sql语句限制为8000字节以内
  int cutnum = 600;         
	auto p_map = m_map_filelist.begin();   
	int bOnce;
    for (; p_map != m_map_filelist.end(); p_map++)   
	{
		PPM_OS::memset(&tm_file, 0, sizeof(tm_file));	
		bOnce = 0;	
		size_t allcount=1;	
		auto p_vec = p_map->second.begin();	
		
		for (int count=1; p_vec != p_map->second.end(); p_vec++, ++count, ++allcount)   
		{   
			if(0 == bOnce){
				PPM_OS::snprintf(tm_file,sizeof(tm_file),"%d",*p_vec); 
				bOnce = 1;
			}
			else
			{
				PPM_OS::snprintf(tm_file,sizeof(tm_file),"%s,%d",tm_file,*p_vec); 
			}	
			if(cutnum == count || allcount == p_map->second.size())
			{ 
				//获取头文件信息
                SearchLogFileSInfo(p_map->first.strlog.c_str(),  p_map->first.testtype, tm_file, 0);

				PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
				PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
					"exec mc_sp_area_cover_grid_get %d,%d, %d,%d,%d,%d, '%s','%s'" ,
					tm_Time_Span.istime, 
					tm_Time_Span.ietime,
					tm_Search_Area.itllongitude,
					tm_Search_Area.itllatitude,
					tm_Search_Area.ibrlongitude,
					tm_Search_Area.ibrlatitude,
					p_map->first.strtb.c_str(),
					tm_file);
				
				char tm_sql2[8000];
				
				PPM_OS::memset(&tm_sql2, 0, sizeof(tm_sql2));
				PPM_OS::snprintf(tm_sql2,sizeof(tm_sql2),"%s,%d",tm_sql,1);
				m_iRowType_ = TYPE_VEC_NORMAL;
				m_bResponseType_ = RESTYPE_AREA_COVER_GRID_IDLE;
				SEARCHSQL(m_iRowType_, tm_sql2);
				
				PPM_OS::memset(&tm_sql2, 0, sizeof(tm_sql2));
				PPM_OS::snprintf(tm_sql2,sizeof(tm_sql2),"%s,%d",tm_sql,2);
				m_iRowType_ = TYPE_VEC_NORMAL;
				m_bResponseType_ = RESTYPE_AREA_COVER_GRID_DEDICATED;
				SEARCHSQL(m_iRowType_, tm_sql2);
				
				PPM_OS::memset(&tm_sql2, 0, sizeof(tm_sql2));
				PPM_OS::snprintf(tm_sql2,sizeof(tm_sql2),"%s,%d",tm_sql,3);
				m_iRowType_ = TYPE_VEC_NORMAL;
				m_bResponseType_ = RESTYPE_AREA_COVER_GRID_GPRS;
				SEARCHSQL(m_iRowType_, tm_sql2);

				PPM_OS::memset(tm_file, 0, sizeof(tm_file));
				count = 0;
				bOnce = 0;
			}
		}
	} 
	//处理结束
	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
	return TRUE;	
}

BOOL 
CSearchSock_CompareCmccCu::GetCompareCmccCuTimeSpan(const BYTE* const pData, const int nCount,int& ioffset)
{
	//time
	PPM_OS::memcpy(&tm_Time_Span.istime,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Time_Span.ietime,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	tm_Time_Span.ConvertToNetSeq();	
    return TRUE;
}

BOOL 
CSearchSock_CompareCmccCu::GetCompareCmccCuSearchAreaInfo(const BYTE* const pData, const int nCount,int& ioffset)
{
	//longitude and latitude
	PPM_OS::memcpy(&tm_Search_Area.itllongitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Search_Area.itllatitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Search_Area.ibrlongitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Search_Area.ibrlatitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);

	tm_Search_Area.ConvertToNetSeq();	
    return TRUE;
}
/************************************************************************
CSearchSock_CQTMngInfo
************************************************************************/
//CQT地点信息
BOOL   
CSearchSock_CQTMngInfo::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{	
	STRU_SEARCH_TIMESPAN tm_Time_Span;
	//time
	PPM_OS::memcpy(&tm_Time_Span.istime,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Time_Span.ietime,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	tm_Time_Span.ConvertToNetSeq();	


	STRU_SEARCH_AREA tm_Search_Area;
	//longitude and latitude
	PPM_OS::memcpy(&tm_Search_Area.itllongitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Search_Area.itllatitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Search_Area.ibrlongitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Search_Area.ibrlatitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	tm_Search_Area.ConvertToNetSeq();

	char tm_sql[512];

	PPM_OS::memset(tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_cqtmng_place_get %d,%d, %d,%d,%d,%d",
		tm_Time_Span.istime,
		tm_Time_Span.ietime,
		tm_Search_Area.itllongitude,
		tm_Search_Area.itllatitude,
		tm_Search_Area.ibrlongitude,
		tm_Search_Area.ibrlatitude
		);

	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_CQTMNG_PLACE_INFO;

	if(NULL != m_pDataBaseDeal_){
		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
	}
	Clear();


	PPM_OS::memset(tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_cqtmng_placesub_get %d,%d, %d,%d,%d,%d",
		tm_Time_Span.istime,
		tm_Time_Span.ietime,
		tm_Search_Area.itllongitude,
		tm_Search_Area.itllatitude,
		tm_Search_Area.ibrlongitude,
		tm_Search_Area.ibrlatitude
		);

	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_CQTMNG_PLACESUB_INFO;

	if(NULL != m_pDataBaseDeal_){
		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
	}
	Clear();

	PPM_OS::memset(tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_cqtmng_cdma_get %d,%d, %d,%d,%d,%d",
		tm_Time_Span.istime,
		tm_Time_Span.ietime,
		tm_Search_Area.itllongitude,
		tm_Search_Area.itllatitude,
		tm_Search_Area.ibrlongitude,
		tm_Search_Area.ibrlatitude
		);

	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_CQTMNG_CDMA_INFO;

	if(NULL != m_pDataBaseDeal_){
		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
	}
	Clear();

	PPM_OS::memset(tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_cqtmng_gsm_get %d,%d, %d,%d,%d,%d",
		tm_Time_Span.istime,
		tm_Time_Span.ietime,
		tm_Search_Area.itllongitude,
		tm_Search_Area.itllatitude,
		tm_Search_Area.ibrlongitude,
		tm_Search_Area.ibrlatitude
		);

	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_CQTMNG_GSM_INFO;

	if(NULL != m_pDataBaseDeal_){
		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
	}
	Clear();
 
	PPM_OS::memset(tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_cqtmng_tdscdma_get %d,%d, %d,%d,%d,%d",
		tm_Time_Span.istime,
		tm_Time_Span.ietime,
		tm_Search_Area.itllongitude,
		tm_Search_Area.itllatitude,
		tm_Search_Area.ibrlongitude,
		tm_Search_Area.ibrlatitude
		);

	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_CQTMNG_TDAMR_INFO;

	if(NULL != m_pDataBaseDeal_){
		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
	}
	Clear();

	PPM_OS::memset(tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_cqtmng_log_get %d,%d, %d,%d,%d,%d",
		tm_Time_Span.istime,
		tm_Time_Span.ietime,
		tm_Search_Area.itllongitude,
		tm_Search_Area.itllatitude,
		tm_Search_Area.ibrlongitude,
		tm_Search_Area.ibrlatitude
		);

	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_CQTMNG_LOG_INFO;

	if(NULL != m_pDataBaseDeal_){
		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);

	}
	Clear();

	//处理结束
	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
	return TRUE;
}



/************************************************************************
CSearchSock_KPITable
************************************************************************/
BOOL 
CSearchSock_KPITable::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	int idbid;
	PPM_OS::memcpy(&idbid,pData + ioffset,4);
	idbid = MAKEINT_NETSEQ1(idbid);
	MOVENEXT(ioffset,4,nCount);

	char tm_tbname[255];
	PPM_OS::memset(tm_tbname, 0, sizeof(tm_tbname));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_tbname,sizeof(tm_tbname)));	

	char tm_sql[8000];	
	PPM_OS::memset(tm_sql, 0, sizeof(tm_sql));
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"use %s select icoltype from tb_popkpi_entry where strtablename = '%s'  order by iidx asc ",
		CConfigSetting::Instance()->m_stru_MainDBSetting_.pchDBName, tm_tbname);	
	m_iRowType_ = TYPE_VEC_TALBE_COLUMN;

	Clear();
	m_vec_temp_int.clear();

  // STRU_ParaResult* p_newSTRU_ParaResult = STRU_ParaResult::SwitchParaResult(TYPE_CS_INT);

	// m_vec_Result_.push_back(p_newSTRU_ParaResult);

	if(NULL != m_pDataBaseDeal_)
	{
		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
	}

	Clear();

	if (0 == m_vec_temp_int.size())
	{
		return FALSE;
	}

	//通过m_vec_columntype构造返回结构
	// m_vec_Result_.clear();

	// for (auto p_vec_int = m_vec_temp_int.begin(); p_vec_int != m_vec_temp_int.end(); ++p_vec_int)
	// {
	// 	STRU_ParaResult* p_newSTRU_ParaResult = STRU_ParaResult::SwitchParaResult(*p_vec_int); 
	// 	m_vec_Result_.push_back(p_newSTRU_ParaResult);
	// }

	if (-1 == idbid)
	{
		PPM_OS::memset(tm_sql, 0, sizeof(tm_sql));	
		PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
		PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
			"use %s  select * from %s ", CConfigSetting::Instance()->m_stru_MainDBSetting_.pchDBName, tm_tbname, idbid);
	}
	else 
	{
		PPM_OS::memset(tm_sql, 0, sizeof(tm_sql));	
		PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
		PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
			"use %s   select * from %s where dbid = %d ", CConfigSetting::Instance()->m_stru_MainDBSetting_.pchDBName, tm_tbname, idbid);
	}


	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_TABLE_DATA_INFO;
	if(NULL != m_pDataBaseDeal_)
	{
		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
	}
    Clear();

	//处理结束
	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
	return true;
}









