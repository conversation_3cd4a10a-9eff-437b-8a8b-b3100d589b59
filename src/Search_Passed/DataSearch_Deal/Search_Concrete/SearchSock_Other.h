// SearchSock_Other.h: interface for the CSearchSock_Other class.
//
//////////////////////////////////////////////////////////////////////

#ifndef __SEARCHSOCK_OTHER_H__
#define __SEARCHSOCK_OTHER_H__

#include "./SearchSock.h"
   
class CSearchSock_RoadRate : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);

private:
	BOOL GetRoadRateTimeSpan(const BYTE* const pData, const int nCount,int& ioffset);
	BOOL GetRoadRateSearchAreaInfo(const BYTE* const pData, const int nCount,int& ioffset);

private:
	STRU_SEARCH_TIMESPAN tm_Time_Span;
  STRU_SEARCH_AREA tm_Search_Area;
};


class CSearchSock_ProMaxTime : public CSearchSock
{

public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};


class CSearchSock_RoadMonitInfo : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};


class CSearchSock_CompBenchUnitInfo : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);

private:
	BOOL GetUnitInfoTimeSpan(const BYTE* const pData, const int nCount,int& ioffset);
	BOOL GetUnitInfoSearchAreaInfo(const BYTE* const pData, const int nCount,int& ioffset);

private:
	STRU_SEARCH_TIMESPAN tm_Time_Span;
	STRU_SEARCH_AREA tm_Search_Area;
};


class CSearchSock_TDSCDMANetRate : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);

private:
  BOOL GetTime(const BYTE* const pData, const int nCount,int& ioffset);
	BOOL GetFileList(const BYTE* const pData, const int nCount,int& ioffset);
  void DealTestTypeTdscdma(int testtype, std::string strlog, std::string strtb, char* tm_file);
	BOOL GetTdscdmaNetRateSearchAreaInfo(const BYTE* const pData, const int nCount,int& ioffset);

private:
	STRU_SEARCH_TIMESPAN tm_Time_Span;
	STRU_SEARCH_AREA tm_Search_Area;
};


class CSearchSock_DocDownLoadInfo : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};


class CSearchSock_CompareCmccCu : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);

private:
	BOOL GetCompareCmccCuTimeSpan(const BYTE* const pData, const int nCount,int& ioffset);
	BOOL GetCompareCmccCuSearchAreaInfo(const BYTE* const pData, const int nCount,int& ioffset);

private:
	STRU_SEARCH_TIMESPAN tm_Time_Span;
	STRU_SEARCH_AREA tm_Search_Area;
};

class CSearchSock_CQTMngInfo : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};

class CSearchSock_KPITable : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};








#endif
