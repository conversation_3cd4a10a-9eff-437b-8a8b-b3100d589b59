// ComDef_Search.h: interface for the CComDef_Search class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_COMDEF_SEARCH_H__9184F54B_E0B0_4DBF_A8D9_C465FAAAA4D1__INCLUDED_)
#define AFX_COMDEF_SEARCH_H__9184F54B_E0B0_4DBF_A8D9_C465FAAAA4D1__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

#define REQTYPE_COMPBENCH_UNIT_INFO             0x61	//REQUEST
#define RESTYPE_COMPBENCH_DATE                  0x61
#define RESTYPE_COMPBENCH_UNIT_INFO             0x62

#define RESTYPE_FILE_INFO                       0x01   //RESTYPE返回文件头信息
#define RESTYPE_FILE_INFO_MORE                  0x02   //RESTYPE返回文件头信息

#define REQTYPE_LOG_SEARCH_LN					0x79	//REQUEST

#define REQTYPE_LOG_SEARCH						0x80	//REQUEST
#define RESTYPE_LOG_SEARCH						0x80

#define REQTYPE_LOG_SEARCH_BYFILEID				0x82	//REQUEST
#define RESTYPE_LOG_SEARCH_BYFILEID				0x82

#define REQTYPE_LOG_Error_SEARCH                0x81    //REQUEST
#define RESTYPE_LOG_Error_SEARCH                0x81   

#define REQTYPE_LOG_REVIEW						0x90	//REQUEST
#define RESTYPE_LOG_REVIEW_MSG_DETAIL			0x90
#define RESTYPE_LOG_REVIEW_MSG_DEPTH			0x91
#define RESTYPE_LOG_REVIEW_EVENT				0x92
#define RESTYPE_LOG_REVIEW_SAMPLE_SUMMARY		0x93
#define RESTYPE_LOG_REVIEW_SAMPLE_NORMAL		0x94
#define RESTYPE_LOG_REVIEW_SAMPLE_ABNORMAL		0x95

#define RESTYPE_LOG_REVIEW_TDSCDMA_SAMPLE_SUMMARY		0x73
#define RESTYPE_LOG_REVIEW_TDSCDMA_SAMPLE_NORMAL		0x74
#define RESTYPE_LOG_REVIEW_TDSCDMA_SAMPLE_ABNORMAL		0x75

#define RESTYPE_LOG_REVIEW_WCDMA_SAMPLE_SUMMARY		    0x76
#define RESTYPE_LOG_REVIEW_WCDMA_SAMPLE_NORMAL		    0x77
#define RESTYPE_LOG_REVIEW_WCDMA_SAMPLE_ABNORMAL		0x78

#define RESTYPE_LOG_REVIEW_CDMA_SAMPLE_SUMMARY          0x81
#define RESTYPE_LOG_REVIEW_CDMA_SAMPLE_NORMAL           0x82

#define RESTYPE_LOG_REVIEW_DATA_SAMPLE_SUMMARY          0x84
#define RESTYPE_LOG_REVIEW_DATA_SAMPLE_NORMAL           0x85

#define RESTYPE_LOG_REVIEW_TD_DATA_SAMPLE_SUMMARY       0x86
#define RESTYPE_LOG_REVIEW_TD_DATA_SAMPLE_NORMAL        0x87

#define RESTYPE_LOG_REVIEW_W_DATA_SAMPLE_SUMMARY        0x88
#define RESTYPE_LOG_REVIEW_W_DATA_SAMPLE_NORMAL         0x89

#define RESTYPE_LOG_REVIEW_CDMA_DATA_SAMPLE_SUMMARY     0x8a
#define RESTYPE_LOG_REVIEW_CDMA_DATA_SAMPLE_NORMAL      0x8b

#define RESTYPE_LOG_REVIEW_CDMA2000_DATA_SAMPLE_SUMMARY     0x8c
#define RESTYPE_LOG_REVIEW_CDMA2000_DATA_SAMPLE_NORMAL      0x8d

#define RESTYPE_LOG_REVIEW_ULGSM_NORMAL                0x8e


#define REQTYPE_AREA_COVER						0xa0	//REQUEST
#define REQTYPE_AREA_COVER_RECENT               0xa1    //REQUEST
#define RESTYPE_AREA_COVER_SUMMARY				0x93
#define RESTYPE_AREA_COVER_DETAIL				0x94
#define	RESTYPE_AREA_COVER_GRID_IDLE			0xa0
#define	RESTYPE_AREA_COVER_GRID_DEDICATED		0xa1
#define	RESTYPE_AREA_COVER_GRID_GPRS			0xa2
#define	RESTYPE_AREA_COVER_GRID_SCAN			0xa3
#define	RESTYPE_AREA_COVER_GRID_TDSCDMA_AMR		0xa4
#define	RESTYPE_AREA_COVER_GRID_TDSCDMA_PS		0xa5
#define	RESTYPE_AREA_COVER_GRID_TDSCDMA_VP		0xa6
#define	RESTYPE_AREA_COVER_GRID_WCDMA_AMR		0xa7
#define	RESTYPE_AREA_COVER_GRID_WCDMA_PS		0xa8
#define	RESTYPE_AREA_COVER_GRID_WCDMA_VP		0xa9
#define	RESTYPE_AREA_COVER_GRID_WCDMA_PSHS		0xaa

#define RESTYPE_AREA_COVER_GPRS_SUMMARY         0xab
#define RESTYPE_AREA_COVER_GPRS_NORMAL          0xac
#define RESTYPE_AREA_COVER_CDMA_V_SUMMARY       0xad
#define RESTYPE_AREA_COVER_CDMA_D_SUMMARY       0xae
#define RESTYPE_AREA_COVER_CDMA_V_GRID          0xaf
#define RESTYPE_AREA_COVER_CDMA_D_GRID          0xb0
#define RESTYPE_AREA_COVER_TDSCDMA_D_SUMMARY    0xb1
#define RESTYPE_AREA_COVER_CDMA2000_D_SUMMARY   0xb2
#define RESTYPE_AREA_COVER_CDMA2000_D_GRID      0xba

#define RESTYPE_AREA_COVER_TDSCDMA_SUMMARY      0x73
#define RESTYPE_AREA_COVER_TDSCDMA_NORMAL       0x74
#define RESTYPE_AERA_COVER_WCDMA_V_SUMMARY      0x76
#define RESTYPE_AERA_COVER_WCDMA_D_SUMMARY      0x79


#define	RESTYPE_AREAI_COVER_GRID_WCDMA_AMR		0xc1
#define	RESTYPE_AREAI_COVER_GRID_WCDMA_VP		0xc2
#define RESTYPE_AREAI_COVER_CDMA_V_GRID         0xc3
#define	RESTYPE_AREAI_COVER_GRID_WCDMA_PS		0xc4
#define	RESTYPE_AREAI_COVER_GRID_WCDMA_PSHS		0xc5
#define RESTYPE_AREAI_COVER_GRID_TDSCDMA_PS     0x15
#define RESTYPE_AREAI_COVER_GRID_TDSCDMA_AMR	0x16
#define RESTYPE_AREAI_COVER_GRID_TDSCDMA_VP	    0x17
#define RESTYPE_AREAI_COVER_GRID_GSM_UPLINK	    0x18


#define REQTYPE_COMPARE_CMCC_CU                 0xa2    //REQUEST

#define REQTYPE_CELL_COVER						0xb0	//REQUEST
#define RESTYPE_CELL_COVER_SUMMARY				0x93
#define RESTYPE_CELL_COVER_DETAIL				0x94
#define	RESTYPE_CELL_COVER_GRID_IDLE			0xa0
#define	RESTYPE_CELL_COVER_GRID_DEDICATED		0xa1
#define	RESTYPE_CELL_COVER_GRID_GPRS			0xa2
#define	RESTYPE_CELL_COVER_GRID_SCAN			0xb3
#define	RESTYPE_CELL_COVER_GRID_TDSCDMA_AMR		0xa4
#define	RESTYPE_CELL_COVER_GRID_TDSCDMA_PS		0xa5
#define	RESTYPE_CELL_COVER_GRID_TDSCDMA_VP		0xa6
#define RESTYPE_CELL_COVER_TDSCDMA_SUMMARY      0x73
#define RESTYPE_CELL_COVER_TDSCDMA_NORMAL       0x74

#define RESTYPE_CELL_COVER_GPRS_SUMMARY			0xa7
#define RESTYPE_CELL_COVER_CDMA_SUMMARY         0x81
#define RESTYPE_CELL_COVER_GRID_CDMA			0xa8
#define RESTYPE_CELL_COVER_GRID_CDMA_DATA	    0xa9
#define RESTYPE_CELL_COVER_CDMA_DATA_SUMMARY    0xaa
#define RESTYPE_CELL_COVER_TDSCDMA_DATA_SUMMARY 0xab
#define RESTYPE_CELL_COVER_WCDMA_SUMMARY        0xac
#define RESTYPE_CELL_COVER_GRID_WCDMA_AMR       0xad
#define RESTYPE_CELL_COVER_GRID_WCDMA_VP        0xae
#define RESTYPE_CELL_COVER_WCDMA_DATA_SUMMARY   0xaf
#define RESTYPE_CELL_COVER_GRID_WCDMA_PS        0xb0
#define RESTYPE_CELL_COVER_GRID_WCDMA_PSHS      0xb1

#define	RESTYPE_LOG_REVIEW_SCAN_SUMMARY			0x97	//REQUEST    //重复定义
#define	RESTYPE_LOG_REVIEW_SCAN_DETAIL			0x97
#define	RESTYPE_LOG_REVIEW_SCAN_DEPTH			0x97
#define RESTYPE_LOG_REVIEW_SCAN_WCDMA           0x98

#define	RESTYPE_AREA_COVER_SCAN_SUMMARY			0x97
#define	RESTYPE_AREA_COVER_SCAN_DETAIL			0x97

#define	RESTYPE_CELL_COVER_SCAN_SUMMARY			0x97
#define	RESTYPE_CELL_COVER_SCAN_DETAIL			0x97

#define RESTYPE_CELL_COVER_SCAN_TD              0x99


#define	REQTYPE_AREA_EVENT						0xc0	//REQUEST
#define	RESTYPE_AREA_EVENT						0x92

#define REQTYPE_CELL_EVENT						0xd0	//REQUEST
#define RESTYPE_CELL_EVENT						0x92

#define REQTYPE_HEALTH_GSM						0xd1   //REQUEST
#define RESTYPE_HEALTH_GSM						0xd1
#define REQTYPE_HEALTH_SCAN						0xd2   //REQUEST
#define RESTYPE_HEALTH_SCAN						0xd2
#define REQTYPE_ROAD_RATE						0xd3   //REQUEST
#define RESTYPE_ROAD_RATE						0xd3
#define REQTYPE_LOG_EVENT						0xd4   //REQUEST
#define RESTYPE_LOG_EVENT						0x92
#define REQTYPE_HEALTH_SCAN_CQT					0xd5   //REQUEST
#define RESTYPE_HEALTH_SCAN_CQT					0xd2
#define REQTYPE_PROJECT_MAXTIME					0xd6   //REQUEST
#define RESTYPE_PROJECT_MAXTIME					0xd6

#define REQTYPE_BLACKBLOCK_INFO                 0xd7   //REQUEST
#define RESTYPE_BLACKBLOCK_INFO                 0xd7
#define RESTYPE_BLACKBLOCK_EVENT                0xd8   
#define REQTYPE_BLACKBLOCK_EDIT                 0xd9   //REQUEST
#define REQTYPE_BLACKBLOCK_TD_INFO              0xda   //REQUEST
#define REQTYPE_BLACKBLOCK_TD_EDIT              0xdb   //REQUEST
#define REQTYPE_BLACKBLOCK_IDTOINFO             0xdc   //REQUEST
#define REQTYPE_BLACKBLOCK_TD_IDTOINFO          0xdd   //REQUEST
#define REQTYPE_BLACKBLOCK_W_INFO               0xde   //REQUEST
#define REQTYPE_BLACKBLOCK_W_IDTOINFO           0xdf   //REQUEST
#define REQTYPE_NEWBLACKBLOCK_INFO				0xe0   //REQUEST

 
#define REQTYPE_ROADMONIT_INFO                  0xc1   //REQUEST
#define RESTYPE_ROADMONIT_INFO                  0xc1  
#define RESTYPE_ROADMONIT_EVENT                 0xc2

#define REQTYPE_AREA_COVERLEAK                  0xa3    //REQUEST

#define REQTYPE_AREA_ANTENNA_REV                0xa4    //REQUEST
#define RESTYPE_ANTENNA_REV_IDLE                0xa4
#define RESTYPE_ANTENNA_REV_DEDICATE            0xa5
#define RESTYPE_ANTENNA_REV_GPRS                0xa6

#define REQTYPE_COMPLAIN_BLOCK_INFO             0xa5    //REQUEST
#define RESTYPE_COMPLAIN_BLOCK                  0x30   
#define RESTYPE_COMPLAIN_ITEM                   0x31  
#define RESTYPE_COMPLAIN_REPORT					0x32  
#define REQTYPE_COMPLAIN_BLOCK_IDTOINFO         0xa6    //REQUEST
#define REQTYPE_COMPLAIN_BLOCK_GRID             0xa7    //REQUEST 
#define RESTYPE_COMPLAIN_BLOCK_GRID             0xa7
#define RESTYPE_COMPLAIN_BLOCK_GRID_LOG         0xa8
#define REQTYPE_COMPLAIN_BLOCK_GRIDTOINFO       0xa8    //REQUEST 

#define REQTYPE_OVERCOVER_LOG					0x51    //REQUEST
#define REQTYPE_OVERCOVER_INFO					0x52    //REQUEST
#define REQTYPE_OVERCOVER_CELL_COVER            0x50    //REQUEST
#define RESTYPE_OVERCOVER_CELL_COVER			0x71   

#define REQTYPE_CLUSTER_INFO					0x53    //REQUEST
#define RESTYPE_CLUSTER_CLIQUE_INFO				0x53
#define RESTYPE_CLUSTER_INFO                    0x54
#define RESTYPE_CLUSTER_ADJFREQ_INFO            0x55
#define RESTYPE_CLUSTER_AREA_FREQNUMAVG         0x56
#define RESTYPE_CLUSTER_AREA_FREQVARIANCE       0x57
#define RESTYPE_CLUSTER_AREA_WEAKCOVCELLNUM     0x58
#define RESTYPE_CLUSTER_AREA_CARRIERUSEAVG      0x59
#define RESTYPE_CLUSTER_CELL_WEAKCOVER          0X6A
#define RESTYPE_CLUSTER_CELL_WIRELESSRATE       0X6B
#define RESTYPE_CLUSTER_AREA_INJECTRATE         0X6C
#define RESTYPE_CLUSTER_CELL_AVAILRATE          0X6D
#define RESTYPE_CLUSTER_COFREQ_INFO             0x70

#define REQTYPE_CLUSTER_LOG                     0x54    //REQUEST
#define RESTYPE_CLUSTER_LOG					    0x54

#define REQTYPE_CLUSTER_ALLCLIQUE_INFO          0x5b    //REQUEST

#define REQTYPE_CLUSTER_CELL_COVER              0x71    //REQUEST
#define RESTYPE_CLUSTER_CELL_COVER				0x71

#define REQTYPE_ES_EVENT_INFO                   0x75    //REQUEST
#define RESTYPE_ES_EVENT_INFO			    	0x75

#define REQTYPE_ES_EVENT_TIME                   0x76     //REQUEST 
#define RESTYPE_ES_EVENT_TIME			    	0x76 

#define REQTYPE_TDSCDMA_NETRATE                 0x55    //REQUEST       //TD网占比
#define RESTYPE_TDSCDMA_NETRATE			    	0x55  
#define RESTYPE_TDSCDMA_NETRATE_IMAGE			0x56  

#define REQTYPE_CLUSTER_CSL_GET                 0x56    //REQUEST
#define RESTYPE_CLUSTER_CSL_GET					0x56   
#define REQTYPE_CLUSTER_CSL_INFO_GET            0x57    //REQUEST
#define RESTYPE_CLUSTER_CSL_INFO_GET			0x57  

#define REQTYPE_DOWNLOADINFO                    0x58    //REQUEST    返回测试报告信息
#define RSQTYPE_DOWNLOADINFO                    0x58  


#define REQTYPE_COMPLAIN_BLOCK_GRID_RECENT      0x59    //REQUEST    
#define RESTYPE_COMPLAIN_BLOCK_GRID_RECENT      0x59 

#define REQTYPE_CELL_OUTSERVICE_INFO            0x5c    //REQUEST 
#define RESTYPE_CELL_OUTSERVICE_INFO            0x5c 

#define REQTYPE_CELLSIMU_GRID_LOG               0x62    //REQUEST 
#define RESTYPE_CELLSIMU_GRID_LOG               0x62
#define REQTYPE_CELLSIMU_GRID_INFO              0x63    //REQUEST 
#define RESTYPE_CELLSIMU_GRID                   0x63
#define RESTYPE_CELLSIMU_GRID_INFO              0x64

#define REQTYPE_CELL_COVER_DISTANCE             0x64     //REQUEST 
#define RESTYPE_CELL_COVER_DISTANCE             0x64

#define REQTYPE_ES_EVENT_REPORT                 0x65     //REQUEST 
#define RESTYPE_ES_EVENT_REPORT                 0x65

#define REQTYPE_ES_EVENT_REPORT_EDIT            0x66     //REQUEST 

#define REQTYPE_ES_EVENT_AHEAD_TIME             0x67     //REQUEST 
#define RESTYPE_ES_EVENT_AHEAD_TIME             0x67

#define REQTYPE_ES_EVENT_MSG                    0x68     //REQUEST 
#define RESTYPE_ES_EVENT_MSG                    0x68

#define REQTYPE_CELLSIMU_CEll_GRID              0x69     //REQUEST 
#define RESTYPE_CELLSIMU_CEll_GRID              0x69

#define  RESTYPE_TEMP_INT                       0xde

//area 
#define RESTYPE_AREASEARCH_ERROR				0xE1     //区域查询采样点过多不予查询



#define REQTYPE_CELL_PROPERTY_INFO              0x5a    //REQUEST 
#define RESTYPE_CELL_PROPERTY_CELL_INFO         0x5a  
#define RESTYPE_CELL_PROPERTY_GPRS_INFO         0x5b
#define RESTYPE_CELL_PROPERTY_TRAFFIC_INFO      0x5c

#define REQTYPE_ES_GRID_CELL                    0x6b     //REQUEST 
#define RESTYPE_ES_GRID_CELL                    0x6b

#define REQTYPE_CELLSIMU_NBCELL_INFO            0x6a     //REQUEST 
#define RESTYPE_CELLSIMU_NBCELL_INFO            0x6a

#define REQTYPE_CQTMNG_PLACE_INFO               0x6c     //REQUEST 
#define RESTYPE_CQTMNG_PLACE_INFO               0x6c
#define RESTYPE_CQTMNG_PLACESUB_INFO            0x6d
#define RESTYPE_CQTMNG_GSM_INFO                 0x6e
#define RESTYPE_CQTMNG_CDMA_INFO                0x6f
#define RESTYPE_CQTMNG_LOG_INFO                 0x70
#define RESTYPE_CQTMNG_TDAMR_INFO               0x71

#define REQTYPE_CELL_ALARM_INFO                 0x6d     //REQUEST 
#define RESTYPE_CELL_ALARM_INFO                 0x6d
#define REQTYPE_CELL_PROPERTY_LN_INFO           0x6f    //REQUEST 
#define RESTYPE_CELL_PROPERTY_LN_INFO           0x6f    

#define REQTYPE_ExcelBLOCK_INFO                 0x77    //REQUEST 
#define RESTYPE_ExcelBLOCK_INFO                 0x77 
#define RESTYPE_ExcelBLOCK_Item_INFO            0x78  
#define REQTYPE_ExcelBLOCK_Local_INFO           0x78    //REQUEST 
#define RESTYPE_ExcelBLOCK_Local_INFO           0x79  
#define RESTYPE_ExcelBLOCK_Local_Item_INFO      0x80  

#define REQTYPE_TABLE_DATA_INFO                 0x83  //REQUEST 
#define RESTYPE_TABLE_DATA_INFO                 0x83  

//new
#define REQTYPE_SITESIMU_SAMPLE_INFO			0x84  //REQUEST REQTYPE_CELLSIMU_BY_DTDATA
#define RESTYPE_SITESIMU_SAMPLE_INFO			0x84   

#define REQTYPE_CELL_TRAFFIC       			    0x85  //REQUEST 
#define RESTYPE_CELL_TRAFFIC 			        0x85 

#define REQTYPE_NEW_BLOCK_INFO       	    	0x86  //REQUEST 
#define RESTYPE_NEW_BLOCK_ID    			    0x86 
#define RESTYPE_NEW_BLOCK_INFO    			    0x87   



#endif // !defined(AFX_COMDEF_SEARCH_H__9184F54B_E0B0_4DBF_A8D9_C465FAAAA4D1__INCLUDED_)
