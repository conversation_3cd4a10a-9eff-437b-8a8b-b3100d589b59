// SockSearchDeal.cpp: implementation of the CSockSearchDeal class.
//
//////////////////////////////////////////////////////////////////////



#include "./SockSearchDeal.h"



//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CSockSearchDeal::CSockSearchDeal()
:m_mapSearchPos()
{
	/************************************************************************/
	/* SearchSock_LogFile                                                   */
	/************************************************************************/
    m_mapSearchPos.insert(make_pair(REQTYPE_LOG_SEARCH, &CSockSearchDeal::SearchSock_LogFile));
    m_mapSearchPos.insert(make_pair(REQTYPE_LOG_SEARCH_LN, &CSockSearchDeal::SearchSock_LogFile_LN));
    m_mapSearchPos.insert(make_pair(REQTYPE_LOG_SEARCH_BYFILEID, &CSockSearchDeal::SearchSock_LogFileByFileid));
    m_mapSearchPos.insert(make_pair(REQTYPE_LOG_Error_SEARCH, &CSockSearchDeal::SearchSock_LogFileError));

	/************************************************************************/
	/* SearchSock_LogReview                                                 */
	/************************************************************************/
    m_mapSearchPos.insert(make_pair(REQTYPE_LOG_REVIEW, &CSockSearchDeal::SearchSock_ReviewLogFile));

	/************************************************************************/
	/* SearchSock_Area                                                      */
	/************************************************************************/
    m_mapSearchPos.insert(make_pair(REQTYPE_AREA_COVER, &CSockSearchDeal::SearchSock_AreaCover));
    m_mapSearchPos.insert(make_pair(REQTYPE_AREA_COVERLEAK, &CSockSearchDeal::SearchSock_AreaCoverLeak));
    m_mapSearchPos.insert(make_pair(REQTYPE_AREA_COVER_RECENT, &CSockSearchDeal::SearchSock_AreaCoverRecent));
    m_mapSearchPos.insert(make_pair(REQTYPE_AREA_EVENT, &CSockSearchDeal::SearchSock_AreaEvent));
    m_mapSearchPos.insert(make_pair(REQTYPE_AREA_ANTENNA_REV, &CSockSearchDeal::SearchSock_AreaAntennaRev));
    m_mapSearchPos.insert(make_pair(REQTYPE_SITESIMU_SAMPLE_INFO, &CSockSearchDeal::SearchSock_SiteSimuSample));


	/************************************************************************/
	/* SearchSock_Cell                                                      */
	/************************************************************************/
    m_mapSearchPos.insert(make_pair(REQTYPE_CELL_COVER, &CSockSearchDeal::SearchSock_CellCover));
    m_mapSearchPos.insert(make_pair(REQTYPE_CELL_EVENT, &CSockSearchDeal::SearchSock_CellEvent));
    m_mapSearchPos.insert(make_pair(REQTYPE_CELL_PROPERTY_INFO, &CSockSearchDeal::SearchSock_CellPropertyInfo));
    m_mapSearchPos.insert(make_pair(REQTYPE_CELL_COVER_DISTANCE, &CSockSearchDeal::SearchSock_CellCoverDisInfo));
    m_mapSearchPos.insert(make_pair(REQTYPE_CELLSIMU_GRID_LOG, &CSockSearchDeal::SearchSock_CellsimuGridLog));
    m_mapSearchPos.insert(make_pair(REQTYPE_CELLSIMU_GRID_INFO, &CSockSearchDeal::SearchSock_CellsimuGridInfo));
    m_mapSearchPos.insert(make_pair(REQTYPE_CELLSIMU_CEll_GRID, &CSockSearchDeal::SearchSock_CellsimuCellGrid));
    m_mapSearchPos.insert(make_pair(REQTYPE_CELL_OUTSERVICE_INFO, &CSockSearchDeal::SearchSock_CellOutServiceInfo));
    m_mapSearchPos.insert(make_pair(REQTYPE_CELL_TRAFFIC, &CSockSearchDeal::SearchSock_CellTraffic));
    m_mapSearchPos.insert(make_pair(REQTYPE_CELL_ALARM_INFO, &CSockSearchDeal::SearchSock_CellAlarmInfo));
    m_mapSearchPos.insert(make_pair(REQTYPE_CELL_PROPERTY_LN_INFO, &CSockSearchDeal::SearchSock_CellPropertyLNInfo));
	/************************************************************************/
	/* SearchSock_ES                                                        */
	/************************************************************************/
    m_mapSearchPos.insert(make_pair(REQTYPE_ES_EVENT_INFO, &CSockSearchDeal::SearchSock_ESEventInfo));
    m_mapSearchPos.insert(make_pair(REQTYPE_ES_EVENT_TIME, &CSockSearchDeal::SearchSock_ESEventTime));
    m_mapSearchPos.insert(make_pair(REQTYPE_ES_EVENT_REPORT, &CSockSearchDeal::SearchSock_ESEventReport));
    m_mapSearchPos.insert(make_pair(REQTYPE_ES_EVENT_REPORT_EDIT, &CSockSearchDeal::SearchSock_ESEventReportEdit));
    m_mapSearchPos.insert(make_pair(REQTYPE_ES_EVENT_AHEAD_TIME, &CSockSearchDeal::SearchSock_ESEventAheadTime));
    m_mapSearchPos.insert(make_pair(REQTYPE_ES_EVENT_MSG, &CSockSearchDeal::SearchSock_ESEventMsg));

	/************************************************************************/
	/* SearchSock_Health                                                    */
	/************************************************************************/
    m_mapSearchPos.insert(make_pair(REQTYPE_HEALTH_GSM, &CSockSearchDeal::SearchSock_HealthGSM));
    m_mapSearchPos.insert(make_pair(REQTYPE_HEALTH_SCAN, &CSockSearchDeal::SearchSock_HealthScan));
    m_mapSearchPos.insert(make_pair(REQTYPE_HEALTH_SCAN_CQT, &CSockSearchDeal::SearchSock_HealthScan_Cqt));

	/************************************************************************/
	/* SearchSock_BlackBock                                                 */
	/************************************************************************/
    m_mapSearchPos.insert(make_pair(REQTYPE_BLACKBLOCK_INFO, &CSockSearchDeal::SearchSock_BlackBlockInfo));
    m_mapSearchPos.insert(make_pair(REQTYPE_BLACKBLOCK_EDIT, &CSockSearchDeal::SearchSock_BlackBlockEdit));
    m_mapSearchPos.insert(make_pair(REQTYPE_BLACKBLOCK_TD_INFO, &CSockSearchDeal::SearchSock_BlackBlockTdInfo));
    m_mapSearchPos.insert(make_pair(REQTYPE_BLACKBLOCK_TD_EDIT, &CSockSearchDeal::SearchSock_BlackBlockTdEdit));
    m_mapSearchPos.insert(make_pair(REQTYPE_BLACKBLOCK_IDTOINFO, &CSockSearchDeal::SearchSock_BlackBlockIdToInfo));
    m_mapSearchPos.insert(make_pair(REQTYPE_BLACKBLOCK_TD_IDTOINFO, &CSockSearchDeal::SearchSock_BlackBlockTdIdToInfo));
    m_mapSearchPos.insert(make_pair(REQTYPE_NEW_BLOCK_INFO, &CSockSearchDeal::SearchSock_NewBlockInfo));
    m_mapSearchPos.insert(make_pair(REQTYPE_BLACKBLOCK_W_INFO, &CSockSearchDeal::SearchSock_BlackBlockWcdmaInfo));
    m_mapSearchPos.insert(make_pair(REQTYPE_BLACKBLOCK_W_IDTOINFO, &CSockSearchDeal::SearchSock_BlackBlockWcdmaIDToInfo));
    m_mapSearchPos.insert(make_pair(REQTYPE_NEWBLACKBLOCK_INFO, &CSockSearchDeal::SearchSock_NewBlackBlockTokenInfo));
    m_mapSearchPos.insert(make_pair(REQTYPE_ExcelBLOCK_INFO, &CSockSearchDeal::SearchSock_ExcelBlockInfo));
    m_mapSearchPos.insert(make_pair(REQTYPE_ExcelBLOCK_Local_INFO, &CSockSearchDeal::SearchSock_ExcelBlockLocalInfo));

    /************************************************************************/
    /* SearchSock_Cluster                                                   */
    /************************************************************************/
    m_mapSearchPos.insert(make_pair(REQTYPE_CLUSTER_INFO, &CSockSearchDeal::SearchSock_ClusterInfo));
    m_mapSearchPos.insert(make_pair(REQTYPE_CLUSTER_LOG, &CSockSearchDeal::SearchSock_ClusterLog));
    m_mapSearchPos.insert(make_pair(REQTYPE_CLUSTER_CSL_GET, &CSockSearchDeal::SearchSock_ClusterCSL));
    m_mapSearchPos.insert(make_pair(REQTYPE_CLUSTER_CSL_INFO_GET, &CSockSearchDeal::SearchSock_ClusterCSLInfo));
    m_mapSearchPos.insert(make_pair(REQTYPE_CLUSTER_CELL_COVER, &CSockSearchDeal::SearchSock_ClusterCellCover));
    m_mapSearchPos.insert(make_pair(REQTYPE_CLUSTER_ALLCLIQUE_INFO, &CSockSearchDeal::SearchSock_ClusterAllCliqueInfo));
    m_mapSearchPos.insert(make_pair(REQTYPE_OVERCOVER_LOG, &CSockSearchDeal::SearchSock_OverCoverLog));
    m_mapSearchPos.insert(make_pair(REQTYPE_OVERCOVER_INFO, &CSockSearchDeal::SearchSock_OverCoverInfo));
    m_mapSearchPos.insert(make_pair(REQTYPE_OVERCOVER_CELL_COVER, &CSockSearchDeal::SearchSock_OverCoverCellCover));

    /************************************************************************/
    /* SearchSock_ComplainBlock                                             */
    /************************************************************************/
    m_mapSearchPos.insert(make_pair(REQTYPE_COMPLAIN_BLOCK_GRID, &CSockSearchDeal::SearchSock_ComplainBlockGrid));
    m_mapSearchPos.insert(make_pair(REQTYPE_COMPLAIN_BLOCK_INFO, &CSockSearchDeal::SearchSock_ComplainBlockInfo));
    m_mapSearchPos.insert(make_pair(REQTYPE_COMPLAIN_BLOCK_IDTOINFO, &CSockSearchDeal::SearchSock_ComplainBlockIdToInfo));
    m_mapSearchPos.insert(make_pair(REQTYPE_COMPLAIN_BLOCK_GRID_RECENT, &CSockSearchDeal::SearchSock_ComplainBlockGridRecent));
    m_mapSearchPos.insert(make_pair(REQTYPE_COMPLAIN_BLOCK_GRIDTOINFO, &CSockSearchDeal::SearchSock_ComplainBlockGridToInfo));

    /************************************************************************/
    /* SearchSock_Other                                                     */
    /************************************************************************/
    m_mapSearchPos.insert(make_pair(REQTYPE_ROAD_RATE, &CSockSearchDeal::SearchSock_RoadRate));
    m_mapSearchPos.insert(make_pair(REQTYPE_PROJECT_MAXTIME, &CSockSearchDeal::SearchSock_ProMaxTime));
    m_mapSearchPos.insert(make_pair(REQTYPE_ROADMONIT_INFO, &CSockSearchDeal::SearchSock_RoadMonitInfo));
    m_mapSearchPos.insert(make_pair(REQTYPE_COMPBENCH_UNIT_INFO, &CSockSearchDeal::SearchSock_CompBenchUnitInfo));
    m_mapSearchPos.insert(make_pair(REQTYPE_TDSCDMA_NETRATE, &CSockSearchDeal::SearchSock_TDSCDMANetRate));
    //测试报告信息
    m_mapSearchPos.insert(make_pair(REQTYPE_DOWNLOADINFO, &CSockSearchDeal::SearchSock_DocDownLoadInfo));
    m_mapSearchPos.insert(make_pair(REQTYPE_COMPARE_CMCC_CU, &CSockSearchDeal::SearchSock_CompareCmccCu));
    m_mapSearchPos.insert(make_pair(REQTYPE_TABLE_DATA_INFO, &CSockSearchDeal::SearchSock_KPITable));
    m_mapSearchPos.insert(make_pair(REQTYPE_CQTMNG_PLACE_INFO, &CSockSearchDeal::SearchSock_CQTMngInfo));
    m_mapSearchPos.insert(make_pair(REQTYPE_ROAD_RATE, &CSockSearchDeal::SearchSock_RoadRate));
    m_mapSearchPos.insert(make_pair(REQTYPE_ROAD_RATE, &CSockSearchDeal::SearchSock_RoadRate));

}

CSockSearchDeal::~CSockSearchDeal()
{
    m_mapSearchPos.clear();
}

BOOL
CSockSearchDeal::DealSock(const BYTE* const pData, const int nCount,int& ioffset, const STRU_SockInfo& sockInfo)
{
	char tm_addr[512];
	sockInfo.pServerDeal_->m_ptcpclient_->GetInetAddr()->addr_to_string(tm_addr, sizeof(tm_addr), 1);
	std::string strUserInfo = "[" + std::string(tm_addr) + "]";

	if(CMD2_REQUEST == pData[ioffset])//请求
	{
		MOVENEXT(ioffset,1,nCount)

		std::map<unsigned int, SearchDeal>::iterator it;
        it = m_mapSearchPos.find(pData[ioffset++]);

		if (it != m_mapSearchPos.end())
		{
			(this->*(it->second))(pData, nCount, ioffset, sockInfo, strUserInfo);
		}
		else
		{
            PPM_DEBUG((LM_ERROR,"unknown search request type:%d\n",pData[ioffset - 1]));
            return FALSE;
		}
	}
	else //响应 
	{
		PPM_DEBUG((LM_ERROR,"should not be search data management response:%d\n",pData[ioffset+1]));
		return FALSE;
	}
	return TRUE;
}

//SearchSock_LogFile
void CSockSearchDeal::SearchSock_LogFile(const BYTE* const pData, const int nCount,int& ioffset, 
                                         const STRU_SockInfo& sockInfo, std::string userinfo)
{
    DEALDATA2(CSearchSock_LogFile, userinfo);			
}
void CSockSearchDeal::SearchSock_LogFile_LN(const BYTE* const pData, const int nCount,int& ioffset, 
                                            const STRU_SockInfo& sockInfo, std::string userinfo)
{
    DEALDATA2(CSearchSock_LogFile_LN, userinfo);
}
void CSockSearchDeal::SearchSock_LogFileByFileid(const BYTE* const pData, const int nCount,int& ioffset, 
                                                 const STRU_SockInfo& sockInfo, std::string userinfo)
{
    DEALDATA2(CSearchSock_LogFileByFileid, userinfo);
}
void CSockSearchDeal::SearchSock_LogFileError(const BYTE* const pData, const int nCount,int& ioffset, 
                                              const STRU_SockInfo& sockInfo, std::string userinfo)
{
    DEALDATA2(CSearchSock_LogFileError, userinfo);
}

//SearchSock_LogReview
void CSockSearchDeal::SearchSock_ReviewLogFile(const BYTE* const pData, const int nCount,int& ioffset, 
                                               const STRU_SockInfo& sockInfo, std::string userinfo)
{
    DEALDATA2(CSearchSock_ReviewLogFile, userinfo);
}

//SearchSock_Area 
void CSockSearchDeal::SearchSock_AreaCover(const BYTE* const pData, const int nCount,int& ioffset, 
                                           const STRU_SockInfo& sockInfo, std::string userinfo)
{
    DEALDATA2(CSearchSock_AreaCover, userinfo);
}
void CSockSearchDeal::SearchSock_AreaCoverLeak(const BYTE* const pData, const int nCount,int& ioffset, 
                                               const STRU_SockInfo& sockInfo, std::string userinfo)
{
    DEALDATA2(CSearchSock_AreaCoverLeak, userinfo);
}
void CSockSearchDeal::SearchSock_AreaCoverRecent(const BYTE* const pData, const int nCount,int& ioffset, 
                                                 const STRU_SockInfo& sockInfo, std::string userinfo)
{
    DEALDATA2(CSearchSock_AreaCoverRecent, userinfo);
}
void CSockSearchDeal::SearchSock_AreaEvent(const BYTE* const pData, const int nCount,int& ioffset, 
                                           const STRU_SockInfo& sockInfo, std::string userinfo)
{
    DEALDATA2(CSearchSock_AreaEvent, userinfo);
}
void CSockSearchDeal::SearchSock_AreaAntennaRev(const BYTE* const pData, const int nCount,int& ioffset, 
                                                const STRU_SockInfo& sockInfo, std::string userinfo)
{
    DEALDATA2(CSearchSock_AreaAntennaRev, userinfo);
}
void CSockSearchDeal::SearchSock_SiteSimuSample(const BYTE* const pData, const int nCount,int& ioffset, 
                                                const STRU_SockInfo& sockInfo, std::string userinfo)
{
    DEALDATA2(CSearchSock_SiteSimuSample, userinfo);
}

//SearchSock_Cell
void CSockSearchDeal::SearchSock_CellCover(const BYTE* const pData, const int nCount,int& ioffset, 
                                           const STRU_SockInfo& sockInfo, std::string userinfo)
{
    DEALDATA2(CSearchSock_CellCover, userinfo);
}
void CSockSearchDeal::SearchSock_CellEvent(const BYTE* const pData, const int nCount,int& ioffset, 
                                           const STRU_SockInfo& sockInfo, std::string userinfo)
{
    DEALDATA2(CSearchSock_CellEvent, userinfo);
}
void CSockSearchDeal::SearchSock_CellPropertyInfo(const BYTE* const pData, const int nCount,int& ioffset, 
                                                  const STRU_SockInfo& sockInfo, std::string userinfo)
{
    DEALDATA2(CSearchSock_CellPropertyInfo, userinfo);
}
void CSockSearchDeal::SearchSock_CellCoverDisInfo(const BYTE* const pData, const int nCount,int& ioffset, 
                                                  const STRU_SockInfo& sockInfo, std::string userinfo)
{
    DEALDATA2(CSearchSock_CellCoverDisInfo, userinfo);
}
void CSockSearchDeal::SearchSock_CellsimuGridLog(const BYTE* const pData, const int nCount,int& ioffset, 
                                                 const STRU_SockInfo& sockInfo, std::string userinfo)
{
    DEALDATA2(CSearchSock_CellsimuGridLog, userinfo);
}
void CSockSearchDeal::SearchSock_CellsimuGridInfo(const BYTE* const pData, const int nCount,int& ioffset, 
                                                  const STRU_SockInfo& sockInfo, std::string userinfo)
{
    DEALDATA2(CSearchSock_CellsimuGridInfo, userinfo);
}
void CSockSearchDeal::SearchSock_CellsimuCellGrid(const BYTE* const pData, const int nCount,int& ioffset, 
                                                  const STRU_SockInfo& sockInfo, std::string userinfo)
{
    DEALDATA2(CSearchSock_CellsimuCellGrid, userinfo);
}
void CSockSearchDeal::SearchSock_CellOutServiceInfo(const BYTE* const pData, const int nCount,int& ioffset, 
                                                    const STRU_SockInfo& sockInfo, std::string userinfo)
{
    DEALDATA2(CSearchSock_CellOutServiceInfo, userinfo);
}
void CSockSearchDeal::SearchSock_CellTraffic(const BYTE* const pData, const int nCount,int& ioffset, 
                                             const STRU_SockInfo& sockInfo, std::string userinfo)
{
    DEALDATA2(CSearchSock_CellTraffic, userinfo);
}
void CSockSearchDeal::SearchSock_CellAlarmInfo(const BYTE* const pData, const int nCount,int& ioffset, 
                                               const STRU_SockInfo& sockInfo, std::string userinfo)
{
    DEALDATA2(CSearchSock_CellAlarmInfo, userinfo);
}
void CSockSearchDeal::SearchSock_CellPropertyLNInfo(const BYTE* const pData, const int nCount,int& ioffset, 
                                                    const STRU_SockInfo& sockInfo, std::string userinfo)
{
    DEALDATA2(CSearchSock_CellPropertyLNInfo, userinfo);
}

//SearchSock_ES
void CSockSearchDeal::SearchSock_ESEventInfo(const BYTE* const pData, const int nCount,int& ioffset, 
                                             const STRU_SockInfo& sockInfo, std::string userinfo)
{
    DEALDATA2(CSearchSock_ESEventInfo, userinfo);
}
void CSockSearchDeal::SearchSock_ESEventTime(const BYTE* const pData, const int nCount,int& ioffset, 
                                             const STRU_SockInfo& sockInfo, std::string userinfo)
{
    DEALDATA2(CSearchSock_ESEventTime, userinfo);
}
void CSockSearchDeal::SearchSock_ESEventReport(const BYTE* const pData, const int nCount,int& ioffset, 
                                               const STRU_SockInfo& sockInfo, std::string userinfo)
{
    DEALDATA2(CSearchSock_ESEventReport, userinfo);
}
void CSockSearchDeal::SearchSock_ESEventReportEdit(const BYTE* const pData, const int nCount,int& ioffset, 
                                                   const STRU_SockInfo& sockInfo, std::string userinfo)
{
    DEALDATA2(CSearchSock_ESEventReportEdit, userinfo);
}
void CSockSearchDeal::SearchSock_ESEventAheadTime(const BYTE* const pData, const int nCount,int& ioffset, 
                                                  const STRU_SockInfo& sockInfo, std::string userinfo)
{
    DEALDATA2(CSearchSock_ESEventAheadTime, userinfo);
}
void CSockSearchDeal::SearchSock_ESEventMsg(const BYTE* const pData, const int nCount,int& ioffset, 
                                            const STRU_SockInfo& sockInfo, std::string userinfo)
{
    DEALDATA2(CSearchSock_ESEventMsg, userinfo);
}

//SearchSock_Health
void CSockSearchDeal::SearchSock_HealthGSM(const BYTE* const pData, const int nCount,int& ioffset, 
                                           const STRU_SockInfo& sockInfo, std::string userinfo)
{
    DEALDATA2(CSearchSock_HealthGSM, userinfo);
}
void CSockSearchDeal::SearchSock_HealthScan(const BYTE* const pData, const int nCount,int& ioffset, 
                                            const STRU_SockInfo& sockInfo, std::string userinfo)
{
    DEALDATA2(CSearchSock_HealthScan, userinfo);
}
void CSockSearchDeal::SearchSock_HealthScan_Cqt(const BYTE* const pData, const int nCount,int& ioffset, 
                                                const STRU_SockInfo& sockInfo, std::string userinfo)
{
    DEALDATA2(CSearchSock_HealthScan_Cqt, userinfo);
}

//SearchSock_BlackBock
void CSockSearchDeal::SearchSock_BlackBlockInfo(const BYTE* const pData, const int nCount,int& ioffset, 
                                                const STRU_SockInfo& sockInfo, std::string userinfo)
{
    DEALDATA2(CSearchSock_BlackBlockInfo, userinfo);
}
void CSockSearchDeal::SearchSock_BlackBlockEdit(const BYTE* const pData, const int nCount,int& ioffset, 
                                                const STRU_SockInfo& sockInfo, std::string userinfo)
{
    DEALDATA2(CSearchSock_BlackBlockEdit, userinfo);
}
void CSockSearchDeal::SearchSock_BlackBlockTdInfo(const BYTE* const pData, const int nCount,int& ioffset, 
                                                  const STRU_SockInfo& sockInfo, std::string userinfo)
{
    DEALDATA2(CSearchSock_BlackBlockTdInfo, userinfo);
}
void CSockSearchDeal::SearchSock_BlackBlockTdEdit(const BYTE* const pData, const int nCount,int& ioffset, 
                                                  const STRU_SockInfo& sockInfo, std::string userinfo)
{
    DEALDATA2(CSearchSock_BlackBlockTdEdit, userinfo);
}
void CSockSearchDeal::SearchSock_BlackBlockIdToInfo(const BYTE* const pData, const int nCount,int& ioffset, 
                                                    const STRU_SockInfo& sockInfo, std::string userinfo)
{
    DEALDATA2(CSearchSock_BlackBlockIdToInfo, userinfo);
}
void CSockSearchDeal::SearchSock_BlackBlockTdIdToInfo(const BYTE* const pData, const int nCount,int& ioffset, 
                                                      const STRU_SockInfo& sockInfo, std::string userinfo)
{
    DEALDATA2(CSearchSock_BlackBlockTdIdToInfo, userinfo);
}
void CSockSearchDeal::SearchSock_NewBlockInfo(const BYTE* const pData, const int nCount,int& ioffset, 
                                              const STRU_SockInfo& sockInfo, std::string userinfo)
{
    DEALDATA2(CSearchSock_NewBlockInfo, userinfo);
}
void CSockSearchDeal::SearchSock_BlackBlockWcdmaInfo(const BYTE* const pData, const int nCount,int& ioffset, 
                                                     const STRU_SockInfo& sockInfo, std::string userinfo)
{
    DEALDATA2(CSearchSock_BlackBlockWcdmaInfo, userinfo);
}
void CSockSearchDeal::SearchSock_BlackBlockWcdmaIDToInfo(const BYTE* const pData, const int nCount,int& ioffset, 
                                                         const STRU_SockInfo& sockInfo, std::string userinfo)
{
    DEALDATA2(CSearchSock_BlackBlockWcdmaIDToInfo, userinfo);
}
void CSockSearchDeal::SearchSock_NewBlackBlockTokenInfo(const BYTE* const pData, const int nCount,int& ioffset, 
                                                        const STRU_SockInfo& sockInfo, std::string userinfo)
{
    DEALDATA2(CSearchSock_NewBlackBlockTokenInfo, userinfo);
}
void CSockSearchDeal::SearchSock_ExcelBlockInfo(const BYTE* const pData, const int nCount,int& ioffset, 
                                                const STRU_SockInfo& sockInfo, std::string userinfo)
{
    DEALDATA2(CSearchSock_ExcelBlockInfo, userinfo);
}
void CSockSearchDeal::SearchSock_ExcelBlockLocalInfo(const BYTE* const pData, const int nCount,int& ioffset, 
                                                     const STRU_SockInfo& sockInfo, std::string userinfo)
{
    DEALDATA2(CSearchSock_ExcelBlockLocalInfo, userinfo);
}

//SearchSock_Cluster
void CSockSearchDeal::SearchSock_ClusterInfo(const BYTE* const pData, const int nCount,int& ioffset, 
                                             const STRU_SockInfo& sockInfo, std::string userinfo)
{
    DEALDATA2(CSearchSock_ClusterInfo, userinfo);
}
void CSockSearchDeal::SearchSock_ClusterLog(const BYTE* const pData, const int nCount,int& ioffset, 
                                            const STRU_SockInfo& sockInfo, std::string userinfo)
{
    DEALDATA2(CSearchSock_ClusterLog, userinfo);
}
void CSockSearchDeal::SearchSock_ClusterCSL(const BYTE* const pData, const int nCount,int& ioffset, 
                                            const STRU_SockInfo& sockInfo, std::string userinfo)
{
    DEALDATA2(CSearchSock_ClusterCSL, userinfo);
}
void CSockSearchDeal::SearchSock_ClusterCSLInfo(const BYTE* const pData, const int nCount,int& ioffset, 
                                                const STRU_SockInfo& sockInfo, std::string userinfo)
{
    DEALDATA2(CSearchSock_ClusterCSLInfo, userinfo);
}
void CSockSearchDeal::SearchSock_ClusterCellCover(const BYTE* const pData, const int nCount,int& ioffset, 
                                                  const STRU_SockInfo& sockInfo, std::string userinfo)
{
    DEALDATA2(CSearchSock_ClusterCellCover, userinfo);
}
void CSockSearchDeal::SearchSock_ClusterAllCliqueInfo(const BYTE* const pData, const int nCount,int& ioffset, 
                                                      const STRU_SockInfo& sockInfo, std::string userinfo)
{
    DEALDATA2(CSearchSock_ClusterAllCliqueInfo, userinfo);
}
void CSockSearchDeal::SearchSock_OverCoverLog(const BYTE* const pData, const int nCount,int& ioffset, 
                                              const STRU_SockInfo& sockInfo, std::string userinfo)
{
    DEALDATA2(CSearchSock_OverCoverLog, userinfo);
}
void CSockSearchDeal::SearchSock_OverCoverInfo(const BYTE* const pData, const int nCount,int& ioffset, 
                                               const STRU_SockInfo& sockInfo, std::string userinfo)
{
    DEALDATA2(CSearchSock_OverCoverInfo, userinfo);
}
void CSockSearchDeal::SearchSock_OverCoverCellCover(const BYTE* const pData, const int nCount,int& ioffset, 
                                                    const STRU_SockInfo& sockInfo, std::string userinfo)
{
    DEALDATA2(CSearchSock_OverCoverCellCover, userinfo);
}

//SearchSock_ComplainBlock
void CSockSearchDeal::SearchSock_ComplainBlockGrid(const BYTE* const pData, const int nCount,int& ioffset, 
                                                   const STRU_SockInfo& sockInfo, std::string userinfo)
{
    DEALDATA2(CSearchSock_ComplainBlockGrid, userinfo);
}
void CSockSearchDeal::SearchSock_ComplainBlockInfo(const BYTE* const pData, const int nCount,int& ioffset, 
                                                   const STRU_SockInfo& sockInfo, std::string userinfo)
{
    DEALDATA2(CSearchSock_ComplainBlockInfo, userinfo);
}
void CSockSearchDeal::SearchSock_ComplainBlockIdToInfo(const BYTE* const pData, const int nCount,int& ioffset, 
                                                       const STRU_SockInfo& sockInfo, std::string userinfo)
{
    DEALDATA2(CSearchSock_ComplainBlockIdToInfo, userinfo);
}
void CSockSearchDeal::SearchSock_ComplainBlockGridRecent(const BYTE* const pData, const int nCount,int& ioffset, 
                                                         const STRU_SockInfo& sockInfo, std::string userinfo)
{
    DEALDATA2(CSearchSock_ComplainBlockGridRecent, userinfo);
}
void CSockSearchDeal::SearchSock_ComplainBlockGridToInfo(const BYTE* const pData, const int nCount,int& ioffset, 
                                                         const STRU_SockInfo& sockInfo, std::string userinfo)
{
    DEALDATA2(CSearchSock_ComplainBlockGridToInfo, userinfo);
}

//SearchSock_Other
void CSockSearchDeal::SearchSock_RoadRate(const BYTE* const pData, const int nCount,int& ioffset, 
                                          const STRU_SockInfo& sockInfo, std::string userinfo)
{
    DEALDATA2(CSearchSock_RoadRate, userinfo);
}
void CSockSearchDeal::SearchSock_ProMaxTime(const BYTE* const pData, const int nCount,int& ioffset, 
                                            const STRU_SockInfo& sockInfo, std::string userinfo)
{
    DEALDATA2(CSearchSock_ProMaxTime, userinfo);
}
void CSockSearchDeal::SearchSock_RoadMonitInfo(const BYTE* const pData, const int nCount,int& ioffset, 
                                               const STRU_SockInfo& sockInfo, std::string userinfo)
{
    DEALDATA2(CSearchSock_RoadMonitInfo, userinfo);
}
void CSockSearchDeal::SearchSock_CompBenchUnitInfo(const BYTE* const pData, const int nCount,int& ioffset, 
                                                   const STRU_SockInfo& sockInfo, std::string userinfo)
{
    DEALDATA2(CSearchSock_CompBenchUnitInfo, userinfo);
}
void CSockSearchDeal::SearchSock_TDSCDMANetRate(const BYTE* const pData, const int nCount,int& ioffset, 
                                                const STRU_SockInfo& sockInfo, std::string userinfo)
{
    DEALDATA2(CSearchSock_TDSCDMANetRate, userinfo);
}
//测试报告信息
void CSockSearchDeal::SearchSock_DocDownLoadInfo(const BYTE* const pData, const int nCount,int& ioffset, 
                                                 const STRU_SockInfo& sockInfo, std::string userinfo)
{
    DEALDATA2(CSearchSock_DocDownLoadInfo, userinfo);
}
void CSockSearchDeal::SearchSock_CompareCmccCu(const BYTE* const pData, const int nCount,int& ioffset, 
                                               const STRU_SockInfo& sockInfo, std::string userinfo)
{
    DEALDATA2(CSearchSock_CompareCmccCu, userinfo);
}
void CSockSearchDeal::SearchSock_KPITable(const BYTE* const pData, const int nCount,int& ioffset, 
                                          const STRU_SockInfo& sockInfo, std::string userinfo)
{
    DEALDATA2(CSearchSock_KPITable, userinfo);
}
void CSockSearchDeal::SearchSock_CQTMngInfo(const BYTE* const pData, const int nCount,int& ioffset, 
                                            const STRU_SockInfo& sockInfo, std::string userinfo)
{
    DEALDATA2(CSearchSock_CQTMngInfo, userinfo);
}
