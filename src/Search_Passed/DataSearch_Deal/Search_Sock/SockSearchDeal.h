// SockSearchDeal.h: interface for the CSockSearchDeal class.
//
//////////////////////////////////////////////////////////////////////

#ifndef __SOCKSEARCHDEAL_H__
#define __SOCKSEARCHDEAL_H__

#include "./SockFactory.h"

#include "./SearchSock_LogFile.h"
#include "./SearchSock_LogReview.h"
#include "./SearchSock_Area.h"
#include "./SearchSock_Cell.h"
#include "./SearchSock_ES.h"
#include "./SearchSock_Health.h"
#include "./SearchSock_BlackBlock.h"
#include "./SearchSock_Cluster.h"
#include "./SearchSock_ComplainBlock.h"
#include "./SearchSock_Other.h"

class CSockSearchDeal : public CSockFactory, public Singleton<CSockSearchDeal>
{
public:
	CSockSearchDeal();
	virtual ~CSockSearchDeal();

public:
    virtual BOOL DealSock(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo);

    typedef void (CSockSearchDeal::*SearchDeal)(const BYTE* const pData, const int nCount,int& ioffset, 
                                                const STRU_SockInfo& sockInfo, std::string userinfo);
	std::map<unsigned int, SearchDeal> m_mapSearchPos;

    void SearchSock_LogFile(const BYTE* const pData, const int nCount,int& ioffset, const STRU_SockInfo& sockInfo, std::string userinfo);
    void SearchSock_LogFile_LN(const BYTE* const pData, const int nCount,int& ioffset, const STRU_SockInfo& sockInfo, std::string userinfo);
    void SearchSock_LogFileByFileid(const BYTE* const pData, const int nCount,int& ioffset, const STRU_SockInfo& sockInfo, std::string userinfo);
    void SearchSock_LogFileError(const BYTE* const pData, const int nCount,int& ioffset, const STRU_SockInfo& sockInfo, std::string userinfo);

	//SearchSock_LogReview
    void SearchSock_ReviewLogFile(const BYTE* const pData, const int nCount,int& ioffset, const STRU_SockInfo& sockInfo, std::string userinfo);

	//SearchSock_Area 
    void SearchSock_AreaCover(const BYTE* const pData, const int nCount,int& ioffset, const STRU_SockInfo& sockInfo, std::string userinfo);
    void SearchSock_AreaCoverLeak(const BYTE* const pData, const int nCount,int& ioffset, const STRU_SockInfo& sockInfo, std::string userinfo);
    void SearchSock_AreaCoverRecent(const BYTE* const pData, const int nCount,int& ioffset, const STRU_SockInfo& sockInfo, std::string userinfo);
    void SearchSock_AreaEvent(const BYTE* const pData, const int nCount,int& ioffset, const STRU_SockInfo& sockInfo, std::string userinfo);
    void SearchSock_AreaAntennaRev(const BYTE* const pData, const int nCount,int& ioffset, const STRU_SockInfo& sockInfo, std::string userinfo);
    void SearchSock_SiteSimuSample(const BYTE* const pData, const int nCount,int& ioffset, const STRU_SockInfo& sockInfo, std::string userinfo);

	//SearchSock_Cell
    void SearchSock_CellCover(const BYTE* const pData, const int nCount,int& ioffset, const STRU_SockInfo& sockInfo, std::string userinfo);
    void SearchSock_CellEvent(const BYTE* const pData, const int nCount,int& ioffset, const STRU_SockInfo& sockInfo, std::string userinfo);
    void SearchSock_CellPropertyInfo(const BYTE* const pData, const int nCount,int& ioffset, const STRU_SockInfo& sockInfo, std::string userinfo);
    void SearchSock_CellCoverDisInfo(const BYTE* const pData, const int nCount,int& ioffset, const STRU_SockInfo& sockInfo, std::string userinfo);
    void SearchSock_CellsimuGridLog(const BYTE* const pData, const int nCount,int& ioffset, const STRU_SockInfo& sockInfo, std::string userinfo);
    void SearchSock_CellsimuGridInfo(const BYTE* const pData, const int nCount,int& ioffset, const STRU_SockInfo& sockInfo, std::string userinfo);
    void SearchSock_CellsimuCellGrid(const BYTE* const pData, const int nCount,int& ioffset, const STRU_SockInfo& sockInfo, std::string userinfo);
    void SearchSock_CellOutServiceInfo(const BYTE* const pData, const int nCount,int& ioffset, const STRU_SockInfo& sockInfo, std::string userinfo);
    void SearchSock_CellTraffic(const BYTE* const pData, const int nCount,int& ioffset, const STRU_SockInfo& sockInfo, std::string userinfo);
    void SearchSock_CellAlarmInfo(const BYTE* const pData, const int nCount,int& ioffset, const STRU_SockInfo& sockInfo, std::string userinfo);
    void SearchSock_CellPropertyLNInfo(const BYTE* const pData, const int nCount,int& ioffset, const STRU_SockInfo& sockInfo, std::string userinfo);

	//SearchSock_ES
    void SearchSock_ESEventInfo(const BYTE* const pData, const int nCount,int& ioffset, const STRU_SockInfo& sockInfo, std::string userinfo);
    void SearchSock_ESEventTime(const BYTE* const pData, const int nCount,int& ioffset, const STRU_SockInfo& sockInfo, std::string userinfo);
    void SearchSock_ESEventReport(const BYTE* const pData, const int nCount,int& ioffset, const STRU_SockInfo& sockInfo, std::string userinfo);
    void SearchSock_ESEventReportEdit(const BYTE* const pData, const int nCount,int& ioffset, const STRU_SockInfo& sockInfo, std::string userinfo);
    void SearchSock_ESEventAheadTime(const BYTE* const pData, const int nCount,int& ioffset, const STRU_SockInfo& sockInfo, std::string userinfo);
    void SearchSock_ESEventMsg(const BYTE* const pData, const int nCount,int& ioffset, const STRU_SockInfo& sockInfo, std::string userinfo);

	//SearchSock_Health
    void SearchSock_HealthGSM(const BYTE* const pData, const int nCount,int& ioffset, const STRU_SockInfo& sockInfo, std::string userinfo);
    void SearchSock_HealthScan(const BYTE* const pData, const int nCount,int& ioffset, const STRU_SockInfo& sockInfo, std::string userinfo);
    void SearchSock_HealthScan_Cqt(const BYTE* const pData, const int nCount,int& ioffset, const STRU_SockInfo& sockInfo, std::string userinfo);

	//SearchSock_BlackBock
    void SearchSock_BlackBlockInfo(const BYTE* const pData, const int nCount,int& ioffset, const STRU_SockInfo& sockInfo, std::string userinfo);
    void SearchSock_BlackBlockEdit(const BYTE* const pData, const int nCount,int& ioffset, const STRU_SockInfo& sockInfo, std::string userinfo);
    void SearchSock_BlackBlockTdInfo(const BYTE* const pData, const int nCount,int& ioffset, const STRU_SockInfo& sockInfo, std::string userinfo);
    void SearchSock_BlackBlockTdEdit(const BYTE* const pData, const int nCount,int& ioffset, const STRU_SockInfo& sockInfo, std::string userinfo);
    void SearchSock_BlackBlockIdToInfo(const BYTE* const pData, const int nCount,int& ioffset, const STRU_SockInfo& sockInfo, std::string userinfo);
    void SearchSock_BlackBlockTdIdToInfo(const BYTE* const pData, const int nCount,int& ioffset, const STRU_SockInfo& sockInfo, std::string userinfo);
    void SearchSock_NewBlockInfo(const BYTE* const pData, const int nCount,int& ioffset, const STRU_SockInfo& sockInfo, std::string userinfo);
    void SearchSock_BlackBlockWcdmaInfo(const BYTE* const pData, const int nCount,int& ioffset, const STRU_SockInfo& sockInfo, std::string userinfo);
    void SearchSock_BlackBlockWcdmaIDToInfo(const BYTE* const pData, const int nCount,int& ioffset, const STRU_SockInfo& sockInfo, std::string userinfo);
    void SearchSock_NewBlackBlockTokenInfo(const BYTE* const pData, const int nCount,int& ioffset, const STRU_SockInfo& sockInfo, std::string userinfo);
    void SearchSock_ExcelBlockInfo(const BYTE* const pData, const int nCount,int& ioffset, const STRU_SockInfo& sockInfo, std::string userinfo);
    void SearchSock_ExcelBlockLocalInfo(const BYTE* const pData, const int nCount,int& ioffset, const STRU_SockInfo& sockInfo, std::string userinfo);

	//SearchSock_Cluster
    void SearchSock_ClusterInfo(const BYTE* const pData, const int nCount,int& ioffset, const STRU_SockInfo& sockInfo, std::string userinfo);
    void SearchSock_ClusterLog(const BYTE* const pData, const int nCount,int& ioffset, const STRU_SockInfo& sockInfo, std::string userinfo);
    void SearchSock_ClusterCSL(const BYTE* const pData, const int nCount,int& ioffset, const STRU_SockInfo& sockInfo, std::string userinfo);
    void SearchSock_ClusterCSLInfo(const BYTE* const pData, const int nCount,int& ioffset, const STRU_SockInfo& sockInfo, std::string userinfo);
    void SearchSock_ClusterCellCover(const BYTE* const pData, const int nCount,int& ioffset, const STRU_SockInfo& sockInfo, std::string userinfo);
    void SearchSock_ClusterAllCliqueInfo(const BYTE* const pData, const int nCount,int& ioffset, const STRU_SockInfo& sockInfo, std::string userinfo);
    void SearchSock_OverCoverLog(const BYTE* const pData, const int nCount,int& ioffset, const STRU_SockInfo& sockInfo, std::string userinfo);
    void SearchSock_OverCoverInfo(const BYTE* const pData, const int nCount,int& ioffset, const STRU_SockInfo& sockInfo, std::string userinfo);
    void SearchSock_OverCoverCellCover(const BYTE* const pData, const int nCount,int& ioffset, const STRU_SockInfo& sockInfo, std::string userinfo);

	//SearchSock_ComplainBlock
    void SearchSock_ComplainBlockGrid(const BYTE* const pData, const int nCount,int& ioffset, const STRU_SockInfo& sockInfo, std::string userinfo);
    void SearchSock_ComplainBlockInfo(const BYTE* const pData, const int nCount,int& ioffset, const STRU_SockInfo& sockInfo, std::string userinfo);
    void SearchSock_ComplainBlockIdToInfo(const BYTE* const pData, const int nCount,int& ioffset, const STRU_SockInfo& sockInfo, std::string userinfo);
    void SearchSock_ComplainBlockGridRecent(const BYTE* const pData, const int nCount,int& ioffset, const STRU_SockInfo& sockInfo, std::string userinfo);
    void SearchSock_ComplainBlockGridToInfo(const BYTE* const pData, const int nCount,int& ioffset, const STRU_SockInfo& sockInfo, std::string userinfo);

	//SearchSock_Other
    void SearchSock_RoadRate(const BYTE* const pData, const int nCount,int& ioffset, const STRU_SockInfo& sockInfo, std::string userinfo);
    void SearchSock_ProMaxTime(const BYTE* const pData, const int nCount,int& ioffset, const STRU_SockInfo& sockInfo, std::string userinfo);
    void SearchSock_RoadMonitInfo(const BYTE* const pData, const int nCount,int& ioffset, const STRU_SockInfo& sockInfo, std::string userinfo);
    void SearchSock_CompBenchUnitInfo(const BYTE* const pData, const int nCount,int& ioffset, const STRU_SockInfo& sockInfo, std::string userinfo);
    void SearchSock_TDSCDMANetRate(const BYTE* const pData, const int nCount,int& ioffset, const STRU_SockInfo& sockInfo, std::string userinfo);
	//测试报告信息
    void SearchSock_DocDownLoadInfo(const BYTE* const pData, const int nCount,int& ioffset, const STRU_SockInfo& sockInfo, std::string userinfo);
    void SearchSock_CompareCmccCu(const BYTE* const pData, const int nCount,int& ioffset, const STRU_SockInfo& sockInfo, std::string userinfo);
    void SearchSock_KPITable(const BYTE* const pData, const int nCount,int& ioffset, const STRU_SockInfo& sockInfo, std::string userinfo);
    void SearchSock_CQTMngInfo(const BYTE* const pData, const int nCount,int& ioffset, const STRU_SockInfo& sockInfo, std::string userinfo);

};

#endif
