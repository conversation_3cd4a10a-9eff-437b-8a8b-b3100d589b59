// StatisticSock_Area.cpp: implementation of the CStatisticSock_Area class.
//
//////////////////////////////////////////////////////////////////////

#include "StatisticSock_Area.h"

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

/************************************************************************
CStatisticSock_AreaLog
************************************************************************/
CStatisticSock_AreaLog::CStatisticSock_AreaLog()
{
	for(int i = 0; i < 256; i++)
	{
		pfunc_TestTypeData[i] = &CStatisticSock_AreaLog::DealTestType_Default;
	}

	pfunc_TestTypeData[CEnumDef::T_GSM_DT] = &CStatisticSock_AreaLog::DealTestType_Gsm;
	pfunc_TestTypeData[CEnumDef::T_GSM_CQT] = &CStatisticSock_AreaLog::DealTestType_Gsm;
	pfunc_TestTypeData[CEnumDef::T_AUTODT] = &CStatisticSock_AreaLog::DealTestType_Gsm;
	pfunc_TestTypeData[CEnumDef::T_AUTODT_TA] = &CStatisticSock_AreaLog::DealTestType_Gsm;
	pfunc_TestTypeData[CEnumDef::T_AUTOCQT] = &CStatisticSock_AreaLog::DealTestType_Gsm;

	pfunc_TestTypeData[CEnumDef::T_SCANTEST] = &CStatisticSock_AreaLog::DealTestType_Scan;

	pfunc_TestTypeData[CEnumDef::T_CDMA_DT] = &CStatisticSock_AreaLog::DealTestType_Cdma;
	pfunc_TestTypeData[CEnumDef::T_CDMA_CQT] = &CStatisticSock_AreaLog::DealTestType_Cdma;

	pfunc_TestTypeData[CEnumDef::T_TDSCDMA_DT] = &CStatisticSock_AreaLog::DealTestType_Tdscdma;
	pfunc_TestTypeData[CEnumDef::T_TDSCDMA_CQT] = &CStatisticSock_AreaLog::DealTestType_Tdscdma;

}

CStatisticSock_AreaLog::~CStatisticSock_AreaLog()
{

}
/************************************************************************
基于区域的LOG统计
基本查询条件
+区域查询条件
************************************************************************/
BOOL  
CStatisticSock_AreaLog::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	MOVENEXTTEST(ioffset,29,nCount);

	//time
    if (!GetTimeSpan(pData, nCount, ioffset))
    {
		return FALSE;
    }
	
	if (!GetBaseFileList(pData, nCount, ioffset))
	{
		return FALSE;
	}

	if(0 >= m_map_filelist.size()){//no data
		//处理结束
		FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
		return TRUE;
	}
	
  char tm_file[8000];//ado对提交的sql语句限制为8000字节以内
	int bOnce;		
  int cutnum = 600;         
	auto p_map = m_map_filelist.begin();   
  for (; p_map != m_map_filelist.end(); p_map++){
		auto p_vec = p_map->second.begin();	
		PPM_OS::memset(&tm_file, 0, sizeof(tm_file));	
		bOnce = 0;
		
		size_t allcount=1;	
		
		for (int count=1; p_vec != p_map->second.end(); p_vec++, ++count, ++allcount){   		
			if(0 == bOnce){
				PPM_OS::snprintf(tm_file,sizeof(tm_file),"%d",*p_vec); 
				bOnce = 1;
			}
			else{
				PPM_OS::snprintf(tm_file,sizeof(tm_file),"%s,%d",
                    tm_file,*p_vec); 
			}
			
			if(cutnum == count || allcount == p_map->second.size()){   
				//获取头文件信息
			    SearchLogFileSInfo(p_map->first.strlog.c_str(), p_map->first.testtype, tm_file, 0);

				DealAreaLogFileStat(p_map->first.strlog, p_map->first.strtb, p_map->first.testtype, tm_file);

				PPM_OS::memset(tm_file, 0, sizeof(tm_file));
				count = 0;
				bOnce = 0;
			}
		}
	}

	//处理结束		
	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
	return TRUE;	
}

BOOL CStatisticSock_AreaLog::GetTimeSpan(const BYTE* const pData, const int nCount,int& ioffset)
{	
	PPM_OS::memcpy(&tm_Time_Span.istime,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Time_Span.ietime,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	tm_Time_Span.ConvertToNetSeq();	

	return TRUE;
}

BOOL CStatisticSock_AreaLog::GetBaseFileList(const BYTE* const pData, const int nCount,int& ioffset)
{
	//type
	char tm_sql_part[1024];
	PPM_OS::memset(&tm_sql_part, 0, sizeof(tm_sql_part));
	if(FALSE == CSearch_PI::GetTypeSql4(pData,nCount,ioffset,tm_sql_part)){
		//处理结束
		FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
		return FALSE;
	}

	//longitude and latitude
	PPM_OS::memcpy(&tm_Search_Area.itllongitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Search_Area.itllatitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Search_Area.ibrlongitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Search_Area.ibrlatitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);

	tm_Search_Area.ConvertToNetSeq();

	//filename
	char  szfilename[255];
	PPM_OS::memset(szfilename,0,sizeof(szfilename));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,szfilename,sizeof(szfilename)));

	char tm_sql[9000];	
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),"exec mc_sp_stat_area_log_get %d,%d, %d,%d,%d,%d, '%s','%s'",
		tm_Time_Span.istime, 
		tm_Time_Span.ietime,
		tm_Search_Area.itllongitude,
		tm_Search_Area.itllatitude,
		tm_Search_Area.ibrlongitude,
		tm_Search_Area.ibrlatitude,
		tm_sql_part,
		szfilename
		);

	m_map_filelist.clear();

	m_iRowType_ = TYPE_VEC_FILE_LIST;             //文件id获取 +　采样点数目获取
    SEARCHSQL(m_iRowType_, tm_sql);

	return TRUE;
}

void CStatisticSock_AreaLog::DealTestType_Default(char* tm_file)
{

}

void CStatisticSock_AreaLog::DealTestType_Gsm(char* tm_file)
{
	//second recordset
	char tm_sql[9000];	
	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),"exec mc_sp_stat_area_log_get_2 '%s'",tm_file);

	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_STAT_AREA_LOG_PARA_1;
	SEARCHSQL(m_iRowType_, tm_sql);

	//third recordset
	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),"exec mc_sp_stat_area_log_get_3 '%s'",tm_file);

	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_STAT_AREA_LOG_PARA_2;
	SEARCHSQL(m_iRowType_, tm_sql);

#ifdef _NEWSTAT_
	//fourth recordset
	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),"exec mc_sp_stat_area_log_get_4 '%s'",tm_file);

	m_iRowType_ = TYPE_VEC_GIS_DATA;
	m_bResponseType_ = RESTYPE_STAT_AREA_LOG_PARA_3;
	SEARCHSQL(m_iRowType_, tm_sql);
#endif
}

void CStatisticSock_AreaLog::DealTestType_Scan(char* tm_file)
{
	//sixth recordset
	char tm_sql[9000];
	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),"exec mc_sp_stat_area_log_get_6 '%s'",tm_file);

	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_STAT_AREA_LOG_SCAN_PARA;
    SEARCHSQL(m_iRowType_, tm_sql);
}

void CStatisticSock_AreaLog::DealTestType_Cdma(char* tm_file)
{
	//fifth recordset
	char tm_sql[9000];
	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),"exec mc_sp_stat_area_log_get_5 '%s'",tm_file);

	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_STAT_AREA_LOG_CDMA_PARA;

	if(NULL != m_pDataBaseDeal_){
	}
	Clear();

}

void CStatisticSock_AreaLog::DealTestType_Tdscdma(char* tm_file)
{
	//TDSCDMA 9-11
	char tm_sql[9000];
	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),"exec mc_sp_stat_area_log_get_9 '%s'",tm_file);

	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_STAT_AREA_LOG_TDSCDMA_AMR;
	SEARCHSQL(m_iRowType_, tm_sql);

#ifdef _NEWSTAT_
	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),"exec mc_sp_stat_area_log_get_10 '%s'",tm_file);
	m_iRowType_ = TYPE_VEC_GIS_DATA;
	m_bResponseType_ = RESTYPE_STAT_AREA_LOG_TDSCDMA_PS;
	SEARCHSQL(m_iRowType_, tm_sql);
#endif

	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),"exec mc_sp_stat_area_log_get_11 '%s'",tm_file);

	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_STAT_AREA_LOG_TDSCDMA_VP;
	SEARCHSQL(m_iRowType_, tm_sql);

	//TDSCDMA EVENT KPI
	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),"exec mc_sp_stat_area_log_get_12 '%s'",tm_file);

	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_STAT_TDSCDMA_EVENT;
    SEARCHSQL(m_iRowType_, tm_sql);
}

void CStatisticSock_AreaLog::DealAreaLogFileStat(std::string strlog, std::string strtb, int testtype, char* tm_file)
{
	if (strtb.size() < 12){
		//first recordset
        char tm_sql[9000];
		PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
		PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
			"exec mc_sp_stat_area_log_get_1 '%s', '%s'",tm_file, strlog.c_str());

		m_iRowType_ = TYPE_VEC_NORMAL;
		m_bResponseType_ = RESTYPE_STAT_AREA_LOG_FILE;
		SEARCHSQL(m_iRowType_, tm_sql);

		(this->*pfunc_TestTypeData[testtype])(tm_file);
	}
	else
	{
		//事件统计 只做gsm
		DealGsmStat(testtype, strtb, tm_file);
	}
}

void CStatisticSock_AreaLog::DealGsmStat(int testtype, std::string strtb, char* tm_file)
{
	//seventh recordset
	char tm_sql[9000];
	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_stat_area_log_get_7 %d,%d,%d,%d, %d,'%s','%s'",
		tm_Search_Area.itllongitude,			
		tm_Search_Area.itllatitude,
		tm_Search_Area.ibrlongitude,
		tm_Search_Area.ibrlatitude,
		testtype,
		strtb.c_str(),
		tm_file);

	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_STAT_AREA_LOG_GSM_EVENT;
	SEARCHSQL(m_iRowType_, tm_sql);

	//eighth recordset
	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_stat_area_log_get_8 %d,%d,%d,%d, %d,'%s','%s'",
		tm_Search_Area.itllongitude,			
		tm_Search_Area.itllatitude,
		tm_Search_Area.ibrlongitude,
		tm_Search_Area.ibrlatitude,
		testtype,
		strtb.c_str(),
		tm_file);

	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_STAT_AREA_LOG_GPRS_EVENT;
    SEARCHSQL(m_iRowType_, tm_sql);
}
/************************************************************************
CStatisticSock_AreaLogKpi
************************************************************************/
CStatisticSock_AreaLogKpi::CStatisticSock_AreaLogKpi()
{
	for(int i = 0; i < 256; i++)
	{
		pfunc_TestTypeData[i] = &CStatisticSock_AreaLogKpi::DealTestType_Default;
	}

	pfunc_TestTypeData[CEnumDef::T_GSM_DT] = &CStatisticSock_AreaLogKpi::DealTestType_Gsm;
	pfunc_TestTypeData[CEnumDef::T_GSM_CQT] = &CStatisticSock_AreaLogKpi::DealTestType_Gsm;
	pfunc_TestTypeData[CEnumDef::T_AUTODT] = &CStatisticSock_AreaLogKpi::DealTestType_Gsm;
	pfunc_TestTypeData[CEnumDef::T_AUTODT_TA] = &CStatisticSock_AreaLogKpi::DealTestType_Gsm;
	pfunc_TestTypeData[CEnumDef::T_AUTOCQT] = &CStatisticSock_AreaLogKpi::DealTestType_Gsm;

	pfunc_TestTypeData[CEnumDef::T_SCANTEST] = &CStatisticSock_AreaLogKpi::DealTestType_Scan;

	pfunc_TestTypeData[CEnumDef::T_CDMA_DT] = &CStatisticSock_AreaLogKpi::DealTestType_Cdma;
	pfunc_TestTypeData[CEnumDef::T_CDMA_CQT] = &CStatisticSock_AreaLogKpi::DealTestType_Cdma;

	pfunc_TestTypeData[CEnumDef::T_TDSCDMA_DT] = &CStatisticSock_AreaLogKpi::DealTestType_Tdscdma;
	pfunc_TestTypeData[CEnumDef::T_TDSCDMA_CQT] = &CStatisticSock_AreaLogKpi::DealTestType_Tdscdma;

	pfunc_TestTypeData[CEnumDef::T_WCDMA_DT] = &CStatisticSock_AreaLogKpi::DealTestType_Wcdma;
	pfunc_TestTypeData[CEnumDef::T_WCDMA_CQT] = &CStatisticSock_AreaLogKpi::DealTestType_Wcdma;
}
CStatisticSock_AreaLogKpi::~CStatisticSock_AreaLogKpi()
{

}

BOOL 
CStatisticSock_AreaLogKpi::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	MOVENEXT(ioffset,4,nCount);

	//time
	if (!GetTimeSpan(pData, nCount, ioffset))
	{
		return FALSE;
	}

	if (!GetBaseFileList(pData, nCount, ioffset))
	{
		return FALSE;
	}

	if(0 >= m_map_filelist.size()){	//no data
		//处理结束
		FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
		return TRUE;
	}

	char tm_file[8000];
	int cutnum = 600;         //ado对提交的sql语句限制为8000字节以内
	int bOnce;		
	auto p_map = m_map_filelist.begin();   
	for (; p_map != m_map_filelist.end(); p_map++)
	{
		auto p_vec = p_map->second.begin();	
		PPM_OS::memset(&tm_file, 0, sizeof(tm_file));	
		bOnce = 0;

		size_t allcount=1;	

		for (int count=1; p_vec != p_map->second.end(); ++allcount, p_vec++, ++count){   		
			if(0 == bOnce){
				PPM_OS::snprintf(tm_file,sizeof(tm_file),"%d",*p_vec); 
				bOnce = 1;
			}
			else{
				PPM_OS::snprintf(tm_file,sizeof(tm_file),"%s,%d",tm_file,*p_vec); 
			}

			if(cutnum == count 
				|| allcount == p_map->second.size()){   
				//获取头文件信息
                SearchLogFileSInfo(p_map->first.strlog.c_str(), p_map->first.testtype, tm_file, 0);

                DealTestTypeData(p_map->first.testtype, p_map->first.strtb, tm_file);

				PPM_OS::memset(tm_file, 0, sizeof(tm_file));
				count = 0;
				bOnce = 0;
			}
		}
	}

	//处理结束		
	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
	return TRUE;

}

void CStatisticSock_AreaLogKpi::DealTestTypeData(int testtype, std::string strtb, char* tm_file)
{
	if (strtb.size() < 12)
	{
		(this->*pfunc_TestTypeData[testtype])(tm_file);
	}
	else
	{
		DealStat(strtb, tm_file);
	}
}

BOOL CStatisticSock_AreaLogKpi::GetTimeSpan(const BYTE* const pData, const int nCount,int& ioffset)
{	
	PPM_OS::memcpy(&tm_Time_Span.istime,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Time_Span.ietime,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	tm_Time_Span.ConvertToNetSeq();	

	return TRUE;
}

BOOL CStatisticSock_AreaLogKpi::GetBaseFileList(const BYTE* const pData, const int nCount,int& ioffset)
{
	char tm_sql_part[1024];
	PPM_OS::memset(&tm_sql_part, 0, sizeof(tm_sql_part));
	if(FALSE == CSearch_PI::GetTypeSql(pData,nCount,ioffset,tm_sql_part)){
		return FALSE;
	}

	//longitude and latitude
	//默认经纬度获取为0，代表全网查询
	if (!GetAreaLogKpiSearchAreaInfo(pData, nCount, ioffset))
	{
		return FALSE;
	}

	char szfile[255];
	PPM_OS::memset(&szfile, 0, sizeof(szfile));

	//filename
    CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,szfile,sizeof(szfile)));

    char tm_sql[9000];	
    PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
        "exec mc_sp_stat_area_log_get %d,%d, %d,%d,%d,%d, '%s','%s'",
		tm_Time_Span.istime, 
		tm_Time_Span.ietime,
		tm_Search_Area.itllongitude,
		tm_Search_Area.itllatitude,
		tm_Search_Area.ibrlongitude,
		tm_Search_Area.ibrlatitude,
		tm_sql_part,
		szfile
		);

	m_map_filelist.clear();
	m_iRowType_ = TYPE_VEC_FILE_LIST;             //文件id获取 +　采样点数目获取
    SEARCHSQL(m_iRowType_, tm_sql);
    return TRUE;
}

BOOL CStatisticSock_AreaLogKpi::GetAreaLogKpiSearchAreaInfo(const BYTE* const pData, const int nCount,int& ioffset)
{
	PPM_OS::memcpy(&tm_Search_Area.itllongitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Search_Area.itllatitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Search_Area.ibrlongitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Search_Area.ibrlatitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	tm_Search_Area.ConvertToNetSeq();
    return TRUE;
}
void CStatisticSock_AreaLogKpi::DealTestType_Default(char* tm_file)
{

}
void CStatisticSock_AreaLogKpi::DealTestType_Gsm(char* tm_file)
{
	//second recordset
    char tm_sql[9000];
	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),"exec mc_sp_stat_area_log_get_2 '%s'", tm_file);

	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_STAT_AREA_LOG_KPI_PARA_1;
	SEARCHSQL(m_iRowType_, tm_sql);

	//third recordset
	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),"exec mc_sp_stat_area_log_get_3 '%s'", tm_file);

	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_STAT_AREA_LOG_KPI_PARA_2;
	SEARCHSQL(m_iRowType_, tm_sql);

	//fourth recordset
	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),"exec mc_sp_stat_area_log_get_4 '%s'", tm_file);

	m_iRowType_ = TYPE_VEC_GIS_DATA;
	m_bResponseType_ = RESTYPE_STAT_AREA_LOG_KPI_PARA_3;
    SEARCHSQL(m_iRowType_, tm_sql);
}
void CStatisticSock_AreaLogKpi::DealTestType_Scan(char* tm_file)
{
	//sixth recordset
	char tm_sql[9000];
	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),"exec mc_sp_stat_area_log_get_6 '%s'", tm_file);

	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_STAT_AREA_LOG_KPI_SCAN_PARA;
    SEARCHSQL(m_iRowType_, tm_sql);
}
void CStatisticSock_AreaLogKpi::DealTestType_Cdma(char* tm_file)
{
	//fifth recordset
	char tm_sql[9000];
	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), "exec mc_sp_stat_area_log_get_5 '%s'",tm_file);

	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_STAT_AREA_LOG_KPI_CDMA_PARA;
    SEARCHSQL(m_iRowType_, tm_sql);
}
void CStatisticSock_AreaLogKpi::DealTestType_Tdscdma(char* tm_file)
{
	//TDSCDMA 9-11
	char tm_sql[9000];
	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),"exec mc_sp_stat_area_log_get_9 '%s'",tm_file);

	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_STAT_AREA_LOG_KPI_TDSCDMA_AMR;
	SEARCHSQL(m_iRowType_, tm_sql);

	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),"exec mc_sp_stat_area_log_get_10 '%s'",tm_file);
	m_iRowType_ = TYPE_VEC_GIS_DATA;
	m_bResponseType_ = RESTYPE_STAT_AREA_LOG_KPI_TDSCDMA_PS;
	SEARCHSQL(m_iRowType_, tm_sql);

	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),"exec mc_sp_stat_area_log_get_11 '%s'",tm_file);

	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_STAT_AREA_LOG_KPI_TDSCDMA_VP;
    SEARCHSQL(m_iRowType_, tm_sql);
}

void CStatisticSock_AreaLogKpi::DealTestType_Wcdma(char* tm_file)
{
	//wcdma image amr 
	char tm_sql[9000];
	char strtbname[255];
	char strdata[1024];
	PPM_OS::memset(strtbname, 0, sizeof(strtbname));
	PPM_OS::snprintf(strtbname, sizeof(tm_sql), "tb_wcdma_stati_amr_log");
	PPM_OS::memset(strdata, 0, sizeof(strdata));
	PPM_OS::snprintf(strdata, sizeof(strdata), 
"''BaseInfo'',''CoverRate'',''Rscp'',''Ecio'',''Rxpower'',''Txpower'',''Bler'',''Sir'',''FreqJam'',''ActiveSet''");
	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),"exec mc_sp_stati_log_get  '%s', '%s', '%s'",tm_file, strtbname, strdata);
	m_iRowType_ = TYPE_VEC_SEARCH_DATA_IMAGE;
	m_bResponseType_ = RESTYPE_STATI_LOG_KPI_WCDMA_AMR;
  SEARCHSQL(m_iRowType_, tm_sql);

	//wcdma image vp 
	PPM_OS::memset(strtbname, 0, sizeof(strtbname));
	PPM_OS::snprintf(strtbname, sizeof(tm_sql), "tb_wcdma_stati_vp_log");
	PPM_OS::memset(strdata, 0, sizeof(strdata));
	PPM_OS::snprintf(strdata, sizeof(strdata), 
"''BaseInfo'',''CoverRate'',''Rscp'',''Ecio'',''Rxpower'',''Txpower'',''Bler'',''Sir'',''FreqJam'',''ActiveSet''");

	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),"exec mc_sp_stati_log_get  '%s', '%s', '%s'",tm_file, strtbname, strdata);
	m_iRowType_ = TYPE_VEC_SEARCH_DATA_IMAGE;
	m_bResponseType_ = RESTYPE_STATI_LOG_KPI_WCDMA_VP;
  SEARCHSQL(m_iRowType_, tm_sql);

	//wcdma image ps 
	PPM_OS::memset(strtbname, 0, sizeof(strtbname));
	PPM_OS::snprintf(strtbname, sizeof(tm_sql), "tb_wcdma_stati_ps_log");
	PPM_OS::memset(strdata, 0, sizeof(strdata));
	PPM_OS::snprintf(strdata, sizeof(strdata), 
"''BaseInfo'',''CoverRate'',''Rscp'',''Ecio'',''Rxpower'',''Txpower'',''Bler'',''Sir'',''ActiveSet'',''RLC'',''APP'',''FreqJam'',''PDUSDU'',''CQI''");
	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),"exec mc_sp_stati_log_get  '%s', '%s', '%s'",tm_file, strtbname, strdata);
	m_iRowType_ = TYPE_VEC_SEARCH_DATA_IMAGE;
	m_bResponseType_ = RESTYPE_STATI_LOG_KPI_WCDMA_PS;
  SEARCHSQL(m_iRowType_, tm_sql);
	//wcdma image pshs
	PPM_OS::memset(strtbname, 0, sizeof(strtbname));
	PPM_OS::snprintf(strtbname, sizeof(tm_sql), "tb_wcdma_stati_pshs_log");
	PPM_OS::memset(strdata, 0, sizeof(strdata));
	PPM_OS::snprintf(strdata, sizeof(strdata), 
"''BaseInfo'',''CoverRate'',''Rscp'',''Ecio'',''Rxpower'',''Txpower'',''Bler'',''Sir'',''ActiveSet'',''RLC'',''APP'',''FreqJam'',''PDUSDU'',''CQI''");
	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),"exec mc_sp_stati_log_get  '%s', '%s', '%s'",tm_file, strtbname, strdata);
	m_iRowType_ = TYPE_VEC_SEARCH_DATA_IMAGE;
	m_bResponseType_ = RESTYPE_STATI_LOG_KPI_WCDMA_PSHS;
  SEARCHSQL(m_iRowType_, tm_sql);
}
void CStatisticSock_AreaLogKpi::DealStat(std::string strtb, char* tm_file)
{
    char tm_sql[9000];
	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),"exec mc_sp_stat_area_kpi_get_7  %d,%d,%d,%d, '%s','%s'",
		tm_Search_Area.itllongitude,
		tm_Search_Area.itllatitude,
		tm_Search_Area.ibrlongitude,
		tm_Search_Area.ibrlatitude,
		strtb.c_str(),
		tm_file);
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_STAT_AREA_LOG_KPI_EVENT;
    SEARCHSQL(m_iRowType_, tm_sql);
}
/************************************************************************
CStatisticSock_AreaKPI
************************************************************************/
CStatisticSock_AreaKPI::CStatisticSock_AreaKPI()
{
	for(int i = 0; i < 256; i++)
	{
		pfunc_TestTypeData[i] = &CStatisticSock_AreaKPI::DealTestType_Default;
	}

	pfunc_TestTypeData[CEnumDef::T_GSM_DT] = &CStatisticSock_AreaKPI::DealTestType_Gsm;
	pfunc_TestTypeData[CEnumDef::T_GSM_CQT] = &CStatisticSock_AreaKPI::DealTestType_Gsm;
	pfunc_TestTypeData[CEnumDef::T_AUTODT] = &CStatisticSock_AreaKPI::DealTestType_Gsm;
	pfunc_TestTypeData[CEnumDef::T_AUTODT_TA] = &CStatisticSock_AreaKPI::DealTestType_Gsm;
	pfunc_TestTypeData[CEnumDef::T_AUTOCQT] = &CStatisticSock_AreaKPI::DealTestType_Gsm;

	pfunc_TestTypeData[CEnumDef::T_SCANTEST] = &CStatisticSock_AreaKPI::DealTestType_Scan;

	pfunc_TestTypeData[CEnumDef::T_CDMA_DT] = &CStatisticSock_AreaKPI::DealTestType_Cdma;
	pfunc_TestTypeData[CEnumDef::T_CDMA_CQT] = &CStatisticSock_AreaKPI::DealTestType_Cdma;

	pfunc_TestTypeData[CEnumDef::T_CDMA2000_DT] = &CStatisticSock_AreaKPI::DealTestType_Cdma2000;

	pfunc_TestTypeData[CEnumDef::T_TDSCDMA_DT] = &CStatisticSock_AreaKPI::DealTestType_Tdscdma;
	pfunc_TestTypeData[CEnumDef::T_TDSCDMA_CQT] = &CStatisticSock_AreaKPI::DealTestType_Tdscdma;

	pfunc_TestTypeData[CEnumDef::T_WCDMA_DT] = &CStatisticSock_AreaKPI::DealTestType_Wcdma;
	pfunc_TestTypeData[CEnumDef::T_WCDMA_CQT] = &CStatisticSock_AreaKPI::DealTestType_Wcdma;

}
CStatisticSock_AreaKPI::~CStatisticSock_AreaKPI()
{

}

BOOL
CStatisticSock_AreaKPI::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	MOVENEXTTEST(ioffset,28,nCount);
	//stattype
	MOVENEXT(ioffset,4,nCount);

	//time
	if (!GetTimeSpan(pData, nCount, ioffset))
	{
		return FALSE;
	}

	if (!GetBaseFileList(pData, nCount, ioffset))
	{
		return FALSE;
	}

	if(0 >= m_map_filelist.size()){//no data
		//处理结束
		FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
		return TRUE;
	}

	auto p_map = m_map_filelist.begin();   
	char tm_file[8000];
	int bOnce;		
	int cutnum = 600;         //ado对提交的sql语句限制为8000字节以内

	for (; p_map != m_map_filelist.end(); p_map++){
		bOnce = 0;
		PPM_OS::memset(&tm_file, 0, sizeof(tm_file));	
		auto p_vec = p_map->second.begin();	

		size_t allcount=1;	

		for (int count=1; p_vec != p_map->second.end(); p_vec++, ++count, ++allcount){   		
			if(0 == bOnce){
				PPM_OS::snprintf(tm_file,sizeof(tm_file),"%d",*p_vec); 
				bOnce = 1;
			}
			else{
				PPM_OS::snprintf(tm_file,sizeof(tm_file),"%s,%d",tm_file,*p_vec); 
			}

			if(cutnum == count || allcount == p_map->second.size()){ 
				//获取头文件信息
				SearchLogFileSInfo(p_map->first.strlog.c_str(), p_map->first.testtype, tm_file, 0);

				DealTestTypeData(p_map->first.testtype, p_map->first.strtb, tm_file);

				PPM_OS::memset(tm_file, 0, sizeof(tm_file));
				count = 0;
				bOnce = 0;
			}
		}
	}
	//处理结束
	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
	return TRUE;
}

void CStatisticSock_AreaKPI::DealTestTypeData(int testtype, std::string strtb, char* tm_file)
{
	if (strtb.size() < 12)
	{
		(this->*pfunc_TestTypeData[testtype])(strtb, tm_file);
	}
	else//seventh recordset
	{	
		DealStatKpi(strtb, tm_file);
	}
}

BOOL CStatisticSock_AreaKPI::GetTimeSpan(const BYTE* const pData, const int nCount,int& ioffset)
{
	PPM_OS::memcpy(&tm_Time_Span.istime,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Time_Span.ietime,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	tm_Time_Span.ConvertToNetSeq();	

	return TRUE;
}
BOOL CStatisticSock_AreaKPI::GetBaseFileList(const BYTE* const pData, const int nCount,int& ioffset)
{
	//type
	char tm_sql_part[1024];
	PPM_OS::memset(&tm_sql_part, 0, sizeof(tm_sql_part));
	if(FALSE == CSearch_PI::GetTypeSql(pData, nCount, ioffset, tm_sql_part)){
		return FALSE;
	}

	//longitude and latitude	
	if (!GetAreaKpiSearchAreaInfo(pData, nCount, ioffset))
	{
		return TRUE;
	}

	char sz_file[255];
	PPM_OS::memset(&sz_file, 0, sizeof(sz_file));

	//filename
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,sz_file,sizeof(sz_file)));

	std::string strFileName(sz_file);
	std::string makestr = "";
	bool flag = true;
	if (strFileName.find(" and ") != std::string::npos){
		MakeAndStr(strFileName, flag);
	}
	else if (strFileName.find(" or ") != std::string::npos ){
		MakeOrStr(strFileName, flag);
	}

	if (makestr.length() == 0){
		makestr = " and strfilename like ''%" + (std::string)sz_file + "%''";
	}

	char tm_sql[9000];	
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),"exec mc_sp_stat_area_kpi_get %d,%d, %d,%d,%d,%d, '%s', '%s'",
		tm_Time_Span.istime, 
		tm_Time_Span.ietime,
		tm_Search_Area.itllongitude,
		tm_Search_Area.itllatitude,
		tm_Search_Area.ibrlongitude,
		tm_Search_Area.ibrlatitude,
		tm_sql_part,
		makestr.c_str()//tm_file
		);

	m_map_filelist.clear();
	m_iRowType_ = TYPE_VEC_FILE_LIST;             //文件id获取 +　采样点数目获取
    SEARCHSQL(m_iRowType_, tm_sql);

	return TRUE;
}

BOOL CStatisticSock_AreaKPI::GetAreaKpiSearchAreaInfo(const BYTE* const pData, const int nCount,int& ioffset)
{
	PPM_OS::memcpy(&tm_Search_Area.itllongitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Search_Area.itllatitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Search_Area.ibrlongitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Search_Area.ibrlatitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	tm_Search_Area.ConvertToNetSeq();
    return TRUE;
}

void CStatisticSock_AreaKPI::MakeAndStr(std::string& strFileName, bool& flag)
{
	while(1){               
		int pos = strFileName.find(" and ");     
		if( pos < 0){
			makestr += " and strfilename like ''%" + strFileName + "%'') ";
			break;
		}       
		std::string word = strFileName.substr(0, pos);
		strFileName = strFileName.substr(pos+5);
		if (flag){
			makestr += " and (strfilename like ''%" + word + "%'' ";
			flag = false;
		}
		else{
			makestr += " and strfilename like ''%" + word + "%''  ";
		}
	}
}

void CStatisticSock_AreaKPI::MakeOrStr(std::string& strFileName, bool& flag)
{
	while(1){               
		int pos = strFileName.find(" or ");     
		if( pos < 0){
			makestr += " or strfilename like ''%" + strFileName + "%'') ";
			break;
		}       
		std::string word = strFileName.substr(0, pos);
		strFileName = strFileName.substr(pos+4);          
		if (flag){
			makestr += "  and (strfilename like ''%" + word + "%'' ";
			flag = false;
		}
		else {
			makestr += " or strfilename like ''%" + word + "%'' ";
		}
	}
}

void CStatisticSock_AreaKPI::DealTestType_Default(std::string strtb, char* tm_file)
{

}
void CStatisticSock_AreaKPI::DealTestType_Gsm(std::string strtb, char* tm_file)
{
    //second recordset
    char tm_sql[9000];
    PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
    PPM_OS::snprintf(tm_sql,sizeof(tm_sql),"exec mc_sp_stat_area_kpi_get_2  %d,%d,%d,%d, '%s','%s'",
        tm_Search_Area.itllongitude,
        tm_Search_Area.itllatitude,
        tm_Search_Area.ibrlongitude,
        tm_Search_Area.ibrlatitude,
        strtb.c_str(),
        tm_file);
    m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_STAT_AREA_KPI_PARA_1;
	SEARCHSQL(m_iRowType_, tm_sql);

	//third recordset
	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),"exec mc_sp_stat_area_kpi_get_3  %d,%d,%d,%d, '%s','%s'",
		tm_Search_Area.itllongitude,
		tm_Search_Area.itllatitude,
		tm_Search_Area.ibrlongitude,
		tm_Search_Area.ibrlatitude,
		strtb.c_str(),
		tm_file);
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_STAT_AREA_KPI_PARA_2;
	SEARCHSQL(m_iRowType_, tm_sql);

	//fourth recordset
	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),"exec mc_sp_stat_area_kpi_get_4  %d,%d,%d,%d, '%s','%s'",
		tm_Search_Area.itllongitude,
		tm_Search_Area.itllatitude,
		tm_Search_Area.ibrlongitude,
		tm_Search_Area.ibrlatitude,
		strtb.c_str(),
		tm_file);
	m_iRowType_ = TYPE_VEC_GIS_DATA;
	m_bResponseType_ = RESTYPE_STAT_AREA_KPI_PARA_3;
	SEARCHSQL(m_iRowType_, tm_sql);

	char strtbname[255];
	char strdata[1024];
	//GSM image Voice
	PPM_OS::memset(strtbname, 0, sizeof(strtbname));
	PPM_OS::snprintf(strtbname, sizeof(tm_sql), "tb_gsm_stati_voice_grid_%s", strtb.c_str());
	PPM_OS::memset(strdata, 0, sizeof(strdata));
	PPM_OS::snprintf(strdata, sizeof(strdata), "''BaseInfo'',''Rxlev'',''Rxqual'',\
''RxlevFull'',''RxlevSub'',''RxlevBcch'',''RxqualFull'',''RxqualSub'',''CiBcch'',''CiTch'',''TA'',''VoiceCode'',\
''Txpower'',''BcchFreq'',''RLTCur'',''RLTMax'',''PesqLQ'',''PesqScore''");

	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),"exec mc_sp_stati_grid_get  0,0, %d,%d,%d,%d, '%s', '%s', '%s'",
		tm_Search_Area.itllongitude,
		tm_Search_Area.itllatitude,
		tm_Search_Area.ibrlongitude,
		tm_Search_Area.ibrlatitude,
		tm_file,
		strtbname,
		strdata
		);
	m_iRowType_ = TYPE_VEC_SEARCH_DATA_IMAGE;
	m_bResponseType_ = RESTYPE_STATI_AREA_KPI_GSM_VOICE;
	SEARCHSQL(m_iRowType_, tm_sql);

	//GSM image Data
	PPM_OS::memset(strtbname, 0, sizeof(strtbname));
	PPM_OS::snprintf(strtbname, sizeof(tm_sql), "tb_gsm_stati_data_grid_%s", strtb.c_str());
	PPM_OS::memset(strdata, 0, sizeof(strdata));
	PPM_OS::snprintf(strdata, sizeof(strdata), "''BaseInfo'',''Rxlev'',''Rxqual'',\
''RxlevFull'',''RxlevSub'',''RxlevBcch'',''RxqualFull'',''RxqualSub'',''CiBcch'',\
''CiTch'',''TA'',''VoiceCode'',''Txpower'',''BcchFreq'', ''RLTCur'',''RLTMax'',\
''PesqLQ'',''PesqScore'',''RLC'',''APP'',''MCS'', ''FTPSlot'',''Bler''");

	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),"exec mc_sp_stati_grid_get  0,0, %d,%d,%d,%d, '%s', '%s', '%s'",
		tm_Search_Area.itllongitude,
		tm_Search_Area.itllatitude,
		tm_Search_Area.ibrlongitude,
		tm_Search_Area.ibrlatitude,
		tm_file,
		strtbname,
		strdata);

	m_iRowType_ = TYPE_VEC_SEARCH_DATA_IMAGE;
	m_bResponseType_ = RESTYPE_STATI_AREA_KPI_GSM_DATA;
	SEARCHSQL(m_iRowType_, tm_sql);

	//GSM UPLINK  DATA
	PPM_OS::memset(strtbname, 0, sizeof(strtbname));
	PPM_OS::snprintf(strtbname, sizeof(tm_sql), "tb_gsm_stati_mtr_grid_%s", strtb.c_str());
	PPM_OS::memset(strdata, 0, sizeof(strdata));
	PPM_OS::snprintf(strdata, sizeof(strdata), "''BaseInfo'',''RxlevDL'',''RxqualDL'',''RxlevUL'',''RxqualUL'',''Ta''");

	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),"exec mc_sp_stati_grid_get  0,0, %d,%d,%d,%d, '%s', '%s', '%s'",
		tm_Search_Area.itllongitude,
		tm_Search_Area.itllatitude,
		tm_Search_Area.ibrlongitude,
		tm_Search_Area.ibrlatitude,
		tm_file,
		strtbname,
		strdata);
	m_iRowType_ = TYPE_VEC_SEARCH_DATA_IMAGE;
	m_bResponseType_ = RESTYPE_STATI_AREA_KPI_GSM_UPLINK;
	SEARCHSQL(m_iRowType_, tm_sql);

}

void CStatisticSock_AreaKPI::DealTestType_Scan(std::string strtb, char* tm_file)
{
	//sixth recordset
	char tm_sql[9000];
	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),"exec mc_sp_stat_area_kpi_get_6  %d,%d,%d,%d, '%s','%s'",
		tm_Search_Area.itllongitude,
		tm_Search_Area.itllatitude,
		tm_Search_Area.ibrlongitude,
		tm_Search_Area.ibrlatitude,
		strtb.c_str(),
		tm_file);
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_STAT_AREA_KPI_SCAN_PARA;
	SEARCHSQL(m_iRowType_, tm_sql);
}
void CStatisticSock_AreaKPI::DealTestType_Cdma(std::string strtb, char* tm_file)
{
	//fifth recordset
	//cdma voice
	char tm_sql[9000];
	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),"exec mc_sp_stat_area_kpi_get_5  %d,%d,%d,%d, '%s','%s'",
		tm_Search_Area.itllongitude,
		tm_Search_Area.itllatitude,
		tm_Search_Area.ibrlongitude,
		tm_Search_Area.ibrlatitude,
		strtb.c_str(),
		tm_file);
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_STAT_AREA_KPI_CDMA_PARA;
	SEARCHSQL(m_iRowType_, tm_sql);

	//cdma 1x
	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),"exec mc_sp_stat_area_kpi_get_15  %d,%d,%d,%d, '%s','%s'",
		tm_Search_Area.itllongitude,
		tm_Search_Area.itllatitude,
		tm_Search_Area.ibrlongitude,
		tm_Search_Area.ibrlatitude,
		strtb.c_str(),
		tm_file);
	m_iRowType_ = TYPE_VEC_GIS_DATA;
	m_bResponseType_ = RESTYPE_STAT_AREA_KPI_CDMA_DATA;
	SEARCHSQL(m_iRowType_, tm_sql);

	//cdma image voice
	char strtbname[255];
	char strdata[1024];
	PPM_OS::memset(strtbname, 0, sizeof(strtbname));
	PPM_OS::snprintf(strtbname, sizeof(tm_sql), "tb_cdma_stati_v_grid_%s", strtb.c_str());
	PPM_OS::memset(strdata, 0, sizeof(strdata));
	PPM_OS::snprintf(strdata, sizeof(strdata), "''BaseInfo'',''CoverRate'',''FFER'',\
''Ecio'',''Rxpower'',''Txpower'',''Pesq''");

	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),"exec mc_sp_stati_grid_get  0,0, %d,%d,%d,%d, '%s', '%s', '%s'",
		tm_Search_Area.itllongitude,
		tm_Search_Area.itllatitude,
		tm_Search_Area.ibrlongitude,
		tm_Search_Area.ibrlatitude,
		tm_file,
		strtbname,
		strdata);

	m_iRowType_ = TYPE_VEC_SEARCH_DATA_IMAGE;
	m_bResponseType_ = RESTYPE_STATI_AREA_KPI_CDMA_V;
	SEARCHSQL(m_iRowType_, tm_sql);
}
void CStatisticSock_AreaKPI::DealTestType_Cdma2000(std::string strtb, char* tm_file)
{
	//evdo cdma2000数据
    char tm_sql[9000];
	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),"exec mc_sp_stat_area_kpi_get_16  %d,%d,%d,%d, '%s','%s'",
		tm_Search_Area.itllongitude,
		tm_Search_Area.itllatitude,
		tm_Search_Area.ibrlongitude,
		tm_Search_Area.ibrlatitude,
		strtb.c_str(),
		tm_file);
	m_iRowType_ = TYPE_VEC_GIS_DATA;
	m_bResponseType_ = RESTYPE_STAT_AREA_KPI_CDMA2000_DATA;
	SEARCHSQL(m_iRowType_, tm_sql);
}
void CStatisticSock_AreaKPI::DealTestType_Tdscdma(std::string strtb, char* tm_file)
{
	//TDSCDMA 8-10
	char tm_sql[9000];
	//eighth recordset
	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),"exec mc_sp_stat_area_kpi_get_8  %d,%d,%d,%d, '%s','%s'",
		tm_Search_Area.itllongitude,
		tm_Search_Area.itllatitude,
		tm_Search_Area.ibrlongitude,
		tm_Search_Area.ibrlatitude,
		strtb.c_str(),
		tm_file);
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_STAT_AREA_KPI_TDSCDMA_AMR;
	SEARCHSQL(m_iRowType_, tm_sql);

	//ninth recordset
	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),"exec mc_sp_stat_area_kpi_get_9  %d,%d,%d,%d, '%s','%s'",
		tm_Search_Area.itllongitude,
		tm_Search_Area.itllatitude,
		tm_Search_Area.ibrlongitude,
		tm_Search_Area.ibrlatitude,
		strtb.c_str(),
		tm_file);
	m_iRowType_ = TYPE_VEC_GIS_DATA;
	m_bResponseType_ = RESTYPE_STAT_AREA_KPI_TDSCDMA_PS;
	SEARCHSQL(m_iRowType_, tm_sql);

	//tenth recordset
	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),"exec mc_sp_stat_area_kpi_get_10  %d,%d,%d,%d, '%s','%s'",
		tm_Search_Area.itllongitude,
		tm_Search_Area.itllatitude,
		tm_Search_Area.ibrlongitude,
		tm_Search_Area.ibrlatitude,
		strtb.c_str(),
		tm_file);
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_STAT_AREA_KPI_TDSCDMA_VP;
	SEARCHSQL(m_iRowType_, tm_sql);

	char strtbname[255];
	char strdata[1024];
	//TDSCDMA image ps
	PPM_OS::memset(strtbname, 0, sizeof(strtbname));
	PPM_OS::snprintf(strtbname, sizeof(tm_sql), "tb_tdscdma_stati_ps_grid_%s", strtb.c_str());
	PPM_OS::memset(strdata, 0, sizeof(strdata));
	PPM_OS::snprintf(strdata, sizeof(strdata), "''BaseInfo'',''Pccpch'',''Dpch'',\
''Rxlev'',''RLC'',''APP'',''MCS'',''FTPSlot'',''Bler_Data'',''Bru'',''FreqJam''");

	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_stati_grid_get  0,0, %d,%d,%d,%d, '%s', '%s', '%s'",
		tm_Search_Area.itllongitude,
		tm_Search_Area.itllatitude,
		tm_Search_Area.ibrlongitude,
		tm_Search_Area.ibrlatitude,
		tm_file,
		strtbname,
		strdata
		);
	m_iRowType_ = TYPE_VEC_SEARCH_DATA_IMAGE;
	m_bResponseType_ = RESTYPE_STATI_AREA_KPI_TDSCDMA_PS;
	SEARCHSQL(m_iRowType_, tm_sql);

	//TDSCDMA image amr
	PPM_OS::memset(strtbname, 0, sizeof(strtbname));
	PPM_OS::snprintf(strtbname, sizeof(tm_sql), "tb_tdscdma_stati_amr_grid_%s", strtb.c_str());
	PPM_OS::memset(strdata, 0, sizeof(strdata));
	PPM_OS::snprintf(strdata, sizeof(strdata), "''BaseInfo'',''Pccpch'',''Dpch'',\
''Bler'',''Mos'',''Rxlev'',''FreqJam''");

	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_stati_grid_get  0,0, %d,%d,%d,%d, '%s', '%s', '%s'",
		tm_Search_Area.itllongitude,
		tm_Search_Area.itllatitude,
		tm_Search_Area.ibrlongitude,
		tm_Search_Area.ibrlatitude,
		tm_file, strtbname, strdata
		);
	m_iRowType_ = TYPE_VEC_SEARCH_DATA_IMAGE;
	m_bResponseType_ = RESTYPE_STATI_AREA_KPI_TDSCDMA_AMR;
	SEARCHSQL(m_iRowType_, tm_sql);

	//TDSCDMA image VP
	PPM_OS::memset(strtbname, 0, sizeof(strtbname));
	PPM_OS::snprintf(strtbname, sizeof(tm_sql), "tb_tdscdma_stati_vp_grid_%s", strtb.c_str());
	PPM_OS::memset(strdata, 0, sizeof(strdata));
	PPM_OS::snprintf(strdata, sizeof(strdata), "''BaseInfo'',''Pccpch'',''Dpch'',\
''Bler'',''Mos'',''Rxlev'',''FreqJam''");

	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_stati_grid_get  0,0, %d,%d,%d,%d, '%s', '%s', '%s'",
		tm_Search_Area.itllongitude,
		tm_Search_Area.itllatitude,
		tm_Search_Area.ibrlongitude,
		tm_Search_Area.ibrlatitude,
		tm_file,
		strtbname,
		strdata
		);
	m_iRowType_ = TYPE_VEC_SEARCH_DATA_IMAGE;
	m_bResponseType_ = RESTYPE_STATI_AREA_KPI_TDSCDMA_VP;
	SEARCHSQL(m_iRowType_, tm_sql);
}
void CStatisticSock_AreaKPI::DealTestType_Wcdma(std::string strtb, char* tm_file)
{
	//WCDMA
	char tm_sql[9000];
	//11 recordset
	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_stat_area_kpi_get_11  %d,%d,%d,%d, '%s','%s'",
		tm_Search_Area.itllongitude,
		tm_Search_Area.itllatitude,
		tm_Search_Area.ibrlongitude,
		tm_Search_Area.ibrlatitude,
		strtb.c_str(),
		tm_file);
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_STAT_AREA_KPI_WCDMA_AMR;
	SEARCHSQL(m_iRowType_, tm_sql);

	//12 recordset
	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_stat_area_kpi_get_12  %d,%d,%d,%d, '%s','%s'",
		tm_Search_Area.itllongitude,
		tm_Search_Area.itllatitude,
		tm_Search_Area.ibrlongitude,
		tm_Search_Area.ibrlatitude,
		strtb.c_str(),
		tm_file);
	m_iRowType_ = TYPE_VEC_GIS_DATA;
	m_bResponseType_ = RESTYPE_STAT_AREA_KPI_WCDMA_PS;
	SEARCHSQL(m_iRowType_, tm_sql);

	//14 recordset
	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_stat_area_kpi_get_14  %d,%d,%d,%d, '%s','%s'",
		tm_Search_Area.itllongitude,
		tm_Search_Area.itllatitude,
		tm_Search_Area.ibrlongitude,
		tm_Search_Area.ibrlatitude,
		strtb.c_str(),
		tm_file);
	m_iRowType_ = TYPE_VEC_GIS_DATA;
	m_bResponseType_ = RESTYPE_STAT_AREA_KPI_WCDMA_PSHS;
	SEARCHSQL(m_iRowType_, tm_sql);

	//13 recordset
	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_stat_area_kpi_get_13  %d,%d,%d,%d, '%s','%s'",
		tm_Search_Area.itllongitude,
		tm_Search_Area.itllatitude,
		tm_Search_Area.ibrlongitude,
		tm_Search_Area.ibrlatitude,
		strtb.c_str(),
		tm_file);
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_STAT_AREA_KPI_WCDMA_VP;
	SEARCHSQL(m_iRowType_, tm_sql);

	char strtbname[255];
	char strdata[1024];
	//wcdma image amr
	PPM_OS::memset(strtbname, 0, sizeof(strtbname));
	PPM_OS::snprintf(strtbname, sizeof(tm_sql), "tb_wcdma_stati_amr_grid_%s", strtb.c_str());
	PPM_OS::memset(strdata, 0, sizeof(strdata));
	PPM_OS::snprintf(strdata, sizeof(strdata), "''BaseInfo'',  ''CoverRate'', ''Rscp'',\
''Ecio'', ''Rxpower'', ''Txpower'', ''Bler'', ''Sir'', ''ActiveSet'', ''FreqJam'',\
''Rxlev'',''BcchCI'',''Rxqual''");

	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_stati_grid_get  0,0, %d,%d,%d,%d, '%s', '%s', '%s'",
		tm_Search_Area.itllongitude,
		tm_Search_Area.itllatitude,
		tm_Search_Area.ibrlongitude,
		tm_Search_Area.ibrlatitude,
		tm_file,
		strtbname,
		strdata
		);
	m_iRowType_ = TYPE_VEC_SEARCH_DATA_IMAGE;
	m_bResponseType_ = RESTYPE_STATI_AREA_KPI_WCDMA_AMR;
	SEARCHSQL(m_iRowType_, tm_sql);

	//wcdma image vp
	PPM_OS::memset(strtbname, 0, sizeof(strtbname));
	PPM_OS::snprintf(strtbname, sizeof(tm_sql), "tb_wcdma_stati_vp_grid_%s", strtb.c_str());
	PPM_OS::memset(strdata, 0, sizeof(strdata));
	PPM_OS::snprintf(strdata, sizeof(strdata), "''BaseInfo'',''CoverRate'',''Rscp'',\
''Ecio'',''Rxpower'',''Txpower'',''Bler'', ''Sir'',''ActiveSet'', ''FreqJam'',\
''Rxlev'',''BcchCI'',''Rxqual''");

	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_stati_grid_get  0,0, %d,%d,%d,%d, '%s', '%s', '%s'",
		tm_Search_Area.itllongitude,
		tm_Search_Area.itllatitude,
		tm_Search_Area.ibrlongitude,
		tm_Search_Area.ibrlatitude,
		tm_file,
		strtbname,
		strdata
		);
	m_iRowType_ = TYPE_VEC_SEARCH_DATA_IMAGE;
	m_bResponseType_ = RESTYPE_STATI_AREA_KPI_WCDMA_VP;
	SEARCHSQL(m_iRowType_, tm_sql);

	//wcdma image ps
	PPM_OS::memset(strtbname, 0, sizeof(strtbname));
	PPM_OS::snprintf(strtbname, sizeof(tm_sql), "tb_wcdma_stati_ps_grid_%s", strtb.c_str());
	PPM_OS::memset(strdata, 0, sizeof(strdata));
	PPM_OS::snprintf(strdata, sizeof(strdata), "''BaseInfo'',''CoverRate'',''Rscp'',\
''Ecio'',''Rxpower'',''Txpower'',''Bler'',''Sir'',''ActiveSet'',''RLC'',''APP'',''PDUSDU'',''CQI'',''Rxlev'', ''BcchCI'',''Rxqual''");

	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_stati_grid_get  0,0, %d,%d,%d,%d, '%s', '%s', '%s'",
		tm_Search_Area.itllongitude,
		tm_Search_Area.itllatitude,
		tm_Search_Area.ibrlongitude,
		tm_Search_Area.ibrlatitude,
		tm_file, strtbname,
		strdata
		);
	m_iRowType_ = TYPE_VEC_SEARCH_DATA_IMAGE;
	m_bResponseType_ = RESTYPE_STATI_AREA_KPI_WCDMA_PS;
	SEARCHSQL(m_iRowType_, tm_sql);

	//wcdma image pshs
	PPM_OS::memset(strtbname, 0, sizeof(strtbname));
	PPM_OS::snprintf(strtbname, sizeof(tm_sql), "tb_wcdma_stati_pshs_grid_%s", strtb.c_str());
	PPM_OS::memset(strdata, 0, sizeof(strdata));
	PPM_OS::snprintf(strdata, sizeof(strdata), 
		"''BaseInfo'',''CoverRate'',''Rscp'',''Ecio'',''Rxpower'',''Txpower'',''Bler'',''Sir'',\
''ActiveSet'',''RLC'',''APP'',''PDUSDU'',''CQI'',''Rxlev'',''BcchCI'',''Rxqual''");

	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_stati_grid_get  0,0, %d,%d,%d,%d, '%s', '%s', '%s'",
		tm_Search_Area.itllongitude,
		tm_Search_Area.itllatitude,
		tm_Search_Area.ibrlongitude,
		tm_Search_Area.ibrlatitude,
		tm_file,
		strtbname,
		strdata
		);
	m_iRowType_ = TYPE_VEC_SEARCH_DATA_IMAGE;
	m_bResponseType_ = RESTYPE_STATI_AREA_KPI_WCDMA_PSHS;
	SEARCHSQL(m_iRowType_, tm_sql);
}

void CStatisticSock_AreaKPI::DealStatKpi(std::string strtb, char* tm_file)
{
	char tm_sql[9000];
	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_stat_area_kpi_get_7  %d,%d,%d,%d, '%s','%s'",
		tm_Search_Area.itllongitude,
		tm_Search_Area.itllatitude,
		tm_Search_Area.ibrlongitude,
		tm_Search_Area.ibrlatitude,
		strtb.c_str(),
		tm_file);
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_STAT_AREA_KPI_EVENT;
	SEARCHSQL(m_iRowType_, tm_sql);
}

/************************************************************************
CStatisticSock_AreaCellKPI
************************************************************************/
CStatisticSock_AreaCellKPI::CStatisticSock_AreaCellKPI()
{
	for(int i = 0; i < 256; i++)
	{
		pfunc_TestTypeData[i] = &CStatisticSock_AreaCellKPI::DealTestType_Default;
	}

	pfunc_TestTypeData[CEnumDef::T_GSM_DT] = &CStatisticSock_AreaCellKPI::DealTestType_Gsm;
	pfunc_TestTypeData[CEnumDef::T_GSM_CQT] = &CStatisticSock_AreaCellKPI::DealTestType_Gsm;
	pfunc_TestTypeData[CEnumDef::T_AUTODT] = &CStatisticSock_AreaCellKPI::DealTestType_Gsm;
	pfunc_TestTypeData[CEnumDef::T_AUTODT_TA] = &CStatisticSock_AreaCellKPI::DealTestType_Gsm;
	pfunc_TestTypeData[CEnumDef::T_AUTOCQT] = &CStatisticSock_AreaCellKPI::DealTestType_Gsm;

	pfunc_TestTypeData[CEnumDef::T_SCANTEST] = &CStatisticSock_AreaCellKPI::DealTestType_Scan;

	pfunc_TestTypeData[CEnumDef::T_CDMA_DT] = &CStatisticSock_AreaCellKPI::DealTestType_Cdma;
	pfunc_TestTypeData[CEnumDef::T_CDMA_CQT] = &CStatisticSock_AreaCellKPI::DealTestType_Cdma;

	pfunc_TestTypeData[CEnumDef::T_TDSCDMA_DT] = &CStatisticSock_AreaCellKPI::DealTestType_Tdscdma;
	pfunc_TestTypeData[CEnumDef::T_TDSCDMA_CQT] = &CStatisticSock_AreaCellKPI::DealTestType_Tdscdma;
}
CStatisticSock_AreaCellKPI::~CStatisticSock_AreaCellKPI()
{

}

BOOL  
CStatisticSock_AreaCellKPI::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
    //time
	if (!GetTimeSpan(pData, nCount, ioffset))
	{
		return FALSE;
	}
	
	if (!GetBaseFileList(pData, nCount, ioffset))
	{
		return FALSE;
	}
    //no data
	if(0 >= m_map_filelist.size()){
		//处理结束
		FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
		return TRUE;
	}
	
  auto p_map = m_map_filelist.begin();   
  char tm_file[8000];
	int bOnce;		
  int cutnum = 600;         //ado对提交的sql语句限制为8000字节以内

  for (; p_map != m_map_filelist.end(); p_map++)   
	{ 
		auto p_vec = p_map->second.begin();	
		PPM_OS::memset(&tm_file, 0, sizeof(tm_file));	
		size_t allcount=1;	
		bOnce = 0;

		for (int count=1; p_vec != p_map->second.end(); ++allcount, ++count, p_vec++)   
        {   		
	     	if(0 == bOnce)	
			{
				PPM_OS::snprintf(tm_file,sizeof(tm_file),"%d",*p_vec); 
				bOnce = 1;
			}
			else{
				PPM_OS::snprintf(tm_file,sizeof(tm_file),"%s,%d",tm_file,*p_vec); 
			}
    
		   if(cutnum == count || allcount == p_map->second.size())	
		   { 
			   //获取头文件信息
			   SearchLogFileSInfo(p_map->first.strlog.c_str(), p_map->first.testtype, tm_file, 0);   

			   DealTestTypeData(p_map->first.testtype, p_map->first.strtb, tm_file);

			   count = 0;
               PPM_OS::memset(tm_file, 0, sizeof(tm_file));
               bOnce = 0;
		   }
       }	         
	}
	//处理结束
    FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
	return TRUE;
}
void CStatisticSock_AreaCellKPI::DealTestTypeData(int testtype, std::string strtb, char* tm_file)
{
	if (strtb.size() < 12)  
	{
		(this->*pfunc_TestTypeData[testtype])(strtb, tm_file);
	}
	else
	{
		DealCellKpi(strtb, tm_file);
	}
}
BOOL CStatisticSock_AreaCellKPI::GetTimeSpan(const BYTE* const pData, const int nCount,int& ioffset)
{
	PPM_OS::memcpy(&tm_Time_Span.istime,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Time_Span.ietime,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	tm_Time_Span.ConvertToNetSeq();	

	return TRUE;
}

BOOL CStatisticSock_AreaCellKPI::GetBaseFileList(const BYTE* const pData, const int nCount,int& ioffset)
{
	//type
	char tm_sql_part[1024];
	PPM_OS::memset(&tm_sql_part, 0, sizeof(tm_sql_part));
	if(FALSE == CSearch_PI::GetTypeSql(pData,nCount,ioffset,tm_sql_part)){
		return FALSE;
	}

	char tm_sql[9000];	
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_stat_area_kpi_get %d,%d, %d,%d,%d,%d, '%s', 'NULL'",
		tm_Time_Span.istime, 
		tm_Time_Span.ietime,
		0,//tm_Search_Area.itllongitude,
		0,//tm_Search_Area.itllatitude,
		0,//tm_Search_Area.ibrlongitude,
		0,//tm_Search_Area.ibrlatitude,
		tm_sql_part		
		);

	m_map_filelist.clear();
	m_iRowType_ = TYPE_VEC_FILE_LIST;             //文件id获取 +　采样点数目获取
	SEARCHSQL(m_iRowType_, tm_sql);

	return TRUE;
}

void CStatisticSock_AreaCellKPI::DealTestType_Default(std::string strtb, char* tm_file)
{

}
void CStatisticSock_AreaCellKPI::DealTestType_Gsm(std::string strtb, char* tm_file)
{
	//second recordset
	char tm_sql[9000];	
	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_stat_area_cell_kpi_get_2  '%s','%s'",
		strtb.c_str(),
		tm_file);
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_STAT_CELL_KPI_PARA_1;
	SEARCHSQL(m_iRowType_, tm_sql);

	//third recordset
	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_stat_area_cell_kpi_get_3  '%s','%s'",
		strtb.c_str(),
		tm_file);
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_STAT_CELL_KPI_PARA_2;
	SEARCHSQL(m_iRowType_, tm_sql);

#ifdef _NEWSTAT_
	//fourth recordset
	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_stat_area_cell_kpi_get_4  '%s','%s'",
		strtb.c_str(),
		tm_file);
	m_iRowType_ = TYPE_VEC_GIS_DATA;
	m_bResponseType_ = RESTYPE_STAT_CELL_KPI_PARA_3;
	SEARCHSQL(m_iRowType_, tm_sql);
#endif
}
void CStatisticSock_AreaCellKPI::DealTestType_Scan(std::string strtb, char* tm_file)
{
	//sixth recordset
	char tm_sql[9000];
	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_stat_area_cell_kpi_get_6  '%s','%s'",
		strtb.c_str(),
		tm_file);
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_STAT_CELL_KPI_SCAN_PARA;
    SEARCHSQL(m_iRowType_, tm_sql);
}
void CStatisticSock_AreaCellKPI::DealTestType_Cdma(std::string strtb, char* tm_file)
{
	//fifth recordset
	char tm_sql[9000];
	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_stat_area_cell_kpi_get_5  '%s','%s'",
		strtb.c_str(),
		tm_file);
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_STAT_CELL_KPI_CDMA_PARA;
	if(NULL != m_pDataBaseDeal_){
	}
	Clear();
}
void CStatisticSock_AreaCellKPI::DealTestType_Tdscdma(std::string strtb, char* tm_file)
{
	//TDSCDMA 8~10
	char tm_sql[9000];
	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_stat_area_cell_kpi_get_8 '%s','%s'",
		strtb.c_str(),
		tm_file);
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_STAT_CELL_KPI_TDSCDMA_AMR;
	SEARCHSQL(m_iRowType_, tm_sql);

#ifdef _NEWSTAT_
	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_stat_area_cell_kpi_get_9 '%s','%s'",
		strtb.c_str(),
		tm_file);
	m_iRowType_ = TYPE_VEC_GIS_DATA;
	m_bResponseType_ = RESTYPE_STAT_CELL_KPI_TDSCDMA_PS;
	SEARCHSQL(m_iRowType_, tm_sql);
#endif

	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_stat_area_cell_kpi_get_10 '%s','%s'",
		strtb.c_str(),
		tm_file);
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_STAT_CELL_KPI_TDSCDMA_VP;
	SEARCHSQL(m_iRowType_, tm_sql);
}

void CStatisticSock_AreaCellKPI::DealCellKpi(std::string strtb, char* tm_file)
{
	//seventh recordset
    char tm_sql[9000];
	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_stat_area_cell_kpi_get_7  '%s','%s'",
		strtb.c_str(),
		tm_file);
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_STAT_CELL_KPI_EVENT;
    SEARCHSQL(m_iRowType_, tm_sql);
}

/************************************************************************
CStatisticSock_AreaStatOld
************************************************************************/
CStatisticSock_AreaStatOld::CStatisticSock_AreaStatOld()
{
	for(int i = 0; i < 256; i++)
	{
		pfunc_TestTypeData[i] = &CStatisticSock_AreaStatOld::DealTestType_Default;
	}

	pfunc_TestTypeData[CEnumDef::T_GSM_DT] = &CStatisticSock_AreaStatOld::DealTestType_Gsm;
	pfunc_TestTypeData[CEnumDef::T_GSM_CQT] = &CStatisticSock_AreaStatOld::DealTestType_Gsm;
	pfunc_TestTypeData[CEnumDef::T_AUTODT] = &CStatisticSock_AreaStatOld::DealTestType_Gsm;
	pfunc_TestTypeData[CEnumDef::T_AUTODT_TA] = &CStatisticSock_AreaStatOld::DealTestType_Gsm;
	pfunc_TestTypeData[CEnumDef::T_AUTOCQT] = &CStatisticSock_AreaStatOld::DealTestType_Gsm;

	pfunc_TestTypeData[CEnumDef::T_TDSCDMA_DT] = &CStatisticSock_AreaStatOld::DealTestType_Tdscdma;
	pfunc_TestTypeData[CEnumDef::T_TDSCDMA_CQT] = &CStatisticSock_AreaStatOld::DealTestType_Tdscdma;

}

CStatisticSock_AreaStatOld::~CStatisticSock_AreaStatOld()
{

}
/************************************************************************
基于非GIS的统计,add by wj 20100721
************************************************************************/
BOOL  
CStatisticSock_AreaStatOld::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	MOVENEXTTEST(ioffset,24,nCount);

	STRU_STAT_NOGIS tm_Search_NOGIS;
	memset(&tm_Search_NOGIS, 0, sizeof(tm_Search_NOGIS));

	//areatypeid
	PPM_OS::memcpy(&tm_Search_NOGIS.iareatypeid,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);

	int itemp;
	PPM_OS::memcpy(&itemp,pData + ioffset,4);
	itemp = MAKEINT_NETSEQ1(itemp);

	if(-1 == itemp)
	{
		MOVENEXT(ioffset,4,nCount)
	}
	else
	{
		if(FALSE == CSearch_PI::GetTextValue_int(pData,nCount,ioffset,tm_Search_NOGIS.szarea)){
			return FALSE;
		}
	}

	//projecttype
	PPM_OS::memcpy(&tm_Search_NOGIS.iprojecttype,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);

	//timetype
	PPM_OS::memcpy(&itemp,pData + ioffset,4);
	itemp = MAKEINT_NETSEQ1(itemp);
	MOVENEXT(ioffset,4,nCount);

	int year = 0;
	int month = 0;
	PPM_OS::memcpy(&year,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	year = MAKEINT_NETSEQ1(year);

	PPM_OS::memcpy(&month,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	month = MAKEINT_NETSEQ1(month);

	switch (itemp)
	{
	case 0:
		{
			PPM_OS::snprintf(tm_Search_NOGIS.sztimetype,sizeof(tm_Search_NOGIS.sztimetype),
				" and datepart(yy,dateadd(ss,istime,''1970-1-1 8:00:00'')) = %d ", year);	
		}
		break;
	case 1:
		{
			PPM_OS::snprintf(tm_Search_NOGIS.sztimetype,sizeof(tm_Search_NOGIS.sztimetype),
				" and datepart(yy,dateadd(ss,istime,''1970-1-1 8:00:00'')) = %d  \
				and datepart(mm,dateadd(ss,istime,''1970-1-1 8:00:00'')) = %d ",
				year, month);	
		}
		break;
	case 2:
		{
			PPM_OS::snprintf(tm_Search_NOGIS.sztimetype,sizeof(tm_Search_NOGIS.sztimetype),
				" and iyear = %d ", year);	
		}
		break;
	case 3:
		{
			PPM_OS::snprintf(tm_Search_NOGIS.sztimetype,sizeof(tm_Search_NOGIS.sztimetype),
				" and iyear  = %d and ibatch = %d ", year, month);	
		}
		break;
	default:
		break;
	}

	tm_Search_NOGIS.ConvertToNetSeq();

	SearchStatAreaDetail(&tm_Search_NOGIS);

	//处理结束
	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
	return TRUE;
}

BOOL  
CStatisticSock_AreaStatOld::SearchStatAreaDetail(STRU_STAT_NOGIS *tm_Search_NOGIS)
{
	char tm_sql[9000];

	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),"exec mc_sp_stat_area_stat_get %d,'%s'",
		tm_Search_NOGIS->iprojecttype, 
		tm_Search_NOGIS->sztimetype);

	m_map_filelist.clear();
	m_iRowType_ = TYPE_VEC_FILE_LIST;
	SEARCHSQL(m_iRowType_, tm_sql);

	if(0 >= m_map_filelist.size())	//no data
	{
		return TRUE;
	}

	char tm_file[8000];
	int bOnce;
	int cutnum = 600;         //ado对提交的sql语句限制为8000字节以内		
	auto p_map = m_map_filelist.begin();   

	for (; p_map != m_map_filelist.end(); p_map++)   
	{
		auto p_vec = p_map->second.begin();	
		PPM_OS::memset(&tm_file, 0, sizeof(tm_file));	
		bOnce = 0;

		size_t allcount=1;	

		for (int count=1; p_vec != p_map->second.end();  ++count, ++allcount, p_vec++)   
		{   		
			if(0 == bOnce){
				PPM_OS::snprintf(tm_file,sizeof(tm_file),"%d",*p_vec); 
				bOnce = 1;
			}
			else
			{
				PPM_OS::snprintf(tm_file,sizeof(tm_file),"%s,%d",tm_file,*p_vec); 
			}

			if(cutnum == count || allcount == p_map->second.size())
			{ 
				//获取头文件信息
				SearchLogFileSInfo(p_map->first.strlog.c_str(), p_map->first.testtype, tm_file, 1);

				(this->*pfunc_TestTypeData[p_map->first.testtype])(tm_Search_NOGIS, p_map->first.strtb, tm_file);

				PPM_OS::memset(tm_file, 0, sizeof(tm_file));
				count = 0;
				bOnce = 0;
			}

		}
	}

	return TRUE;
}

void CStatisticSock_AreaStatOld::DealTestType_Default(STRU_STAT_NOGIS *tm_Search_NOGIS, std::string strtb, char* tm_file)
{

}
void CStatisticSock_AreaStatOld::DealTestType_Gsm(STRU_STAT_NOGIS *tm_Search_NOGIS, std::string strtb, char* tm_file)
{
	char tm_sql[9000];
	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_stat_area_stat_get_1  %d,'%s', '%s','%s'",
		tm_Search_NOGIS->iareatypeid,
		tm_Search_NOGIS->szarea,
		strtb.c_str(),
		tm_file);

	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_STAT_NOGIS_V_GSM_IDLE;
	SEARCHSQL(m_iRowType_, tm_sql);

	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_stat_area_stat_get_2 %d,'%s', '%s','%s'",
		tm_Search_NOGIS->iareatypeid,
		tm_Search_NOGIS->szarea,
		strtb.c_str(),
		tm_file);

	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_STAT_NOGIS_V_GSM_DEDICATED;
	SEARCHSQL(m_iRowType_, tm_sql);

	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_stat_area_stat_get_3 %d,'%s', '%s','%s'",
		tm_Search_NOGIS->iareatypeid,
		tm_Search_NOGIS->szarea,
		strtb.c_str(),
		tm_file);

	m_iRowType_ = TYPE_VEC_STAT_AREA_DATA;
	m_bResponseType_ = RESTYPE_STAT_NOGIS_D_GPRS;
	SEARCHSQL(m_iRowType_, tm_sql);

	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_stat_area_stat_get_4 %d,'%s', '%s','%s'",
		tm_Search_NOGIS->iareatypeid,
		tm_Search_NOGIS->szarea,
		strtb.c_str(),
		tm_file);

	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_STAT_AREA_STAT_EVENT;
    SEARCHSQL(m_iRowType_, tm_sql);
}
void CStatisticSock_AreaStatOld::DealTestType_Tdscdma(STRU_STAT_NOGIS *tm_Search_NOGIS, std::string strtb, char* tm_file)
{
	char tm_sql[9000];
	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_stat_area_stat_get_5  %d,'%s', '%s','%s'",
		tm_Search_NOGIS->iareatypeid,
		tm_Search_NOGIS->szarea,
		strtb.c_str(),
		tm_file);

	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_STAT_NOGIS_TDSCDMA_AMR;
	SEARCHSQL(m_iRowType_, tm_sql);

	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_stat_area_stat_get_6 %d,'%s', '%s','%s'",
		tm_Search_NOGIS->iareatypeid,
		tm_Search_NOGIS->szarea,
		strtb.c_str(),
		tm_file);

	m_iRowType_ = TYPE_VEC_STAT_AREA_DATA;
	m_bResponseType_ = RESTYPE_STAT_NOGIS_TDSCDMA_PS;
	SEARCHSQL(m_iRowType_, tm_sql);

	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_stat_area_stat_get_7 %d,'%s', '%s','%s'",
		tm_Search_NOGIS->iareatypeid,
		tm_Search_NOGIS->szarea,
		strtb.c_str(),
		tm_file);

	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_STAT_NOGIS_TDSCDMA_VP;
	SEARCHSQL(m_iRowType_, tm_sql);

	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_stat_area_stat_get_4 %d,'%s', '%s','%s'",
		tm_Search_NOGIS->iareatypeid,
		tm_Search_NOGIS->szarea,
		strtb.c_str(),
		tm_file);

	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_STAT_AREA_STAT_EVENT;
    SEARCHSQL(m_iRowType_, tm_sql);
}

/************************************************************************
CStatisticSock_AreaStat
************************************************************************/
CStatisticSock_AreaStat::CStatisticSock_AreaStat()
{
    for (int i = 0; i < 256; i++)
    {
		pfunc_TestTypeData[i] = &CStatisticSock_AreaStat::DealTestType_Default;
    }

	pfunc_TestTypeData[CEnumDef::T_GSM_DT] = &CStatisticSock_AreaStat::DealTestType_Gsm;
	pfunc_TestTypeData[CEnumDef::T_GSM_CQT] = &CStatisticSock_AreaStat::DealTestType_Gsm;
	pfunc_TestTypeData[CEnumDef::T_AUTODT] = &CStatisticSock_AreaStat::DealTestType_Gsm;
	pfunc_TestTypeData[CEnumDef::T_AUTODT_TA] = &CStatisticSock_AreaStat::DealTestType_Gsm;
	pfunc_TestTypeData[CEnumDef::T_AUTOCQT] = &CStatisticSock_AreaStat::DealTestType_Gsm;

	pfunc_TestTypeData[CEnumDef::T_TDSCDMA_DT] = &CStatisticSock_AreaStat::DealTestType_Tdscdma;
	pfunc_TestTypeData[CEnumDef::T_TDSCDMA_CQT] = &CStatisticSock_AreaStat::DealTestType_Tdscdma;

}
CStatisticSock_AreaStat::~CStatisticSock_AreaStat()
{

}

BOOL  
CStatisticSock_AreaStat::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	if (!GetAreaStat(pData, nCount, ioffset))
	{
		return FALSE;
	}

	if (!GetFileList(pData, nCount, ioffset))
	{
		return FALSE;
	}

	if(0 >= m_map_filelist.size())	//no data
	{
		//处理结束
		FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
		return TRUE;
	}

	int bOnce;
	char tm_file[8000];//ado对提交的sql语句限制为8000字节以内
    int cutnum = 600;
	auto p_map = m_map_filelist.begin();   

	for (; p_map != m_map_filelist.end(); p_map++)   
	{
		auto p_vec = p_map->second.begin();	
		size_t allcount=1;	
		PPM_OS::memset(&tm_file, 0, sizeof(tm_file));	
		bOnce = 0;

		for (int count=1; p_vec != p_map->second.end(); p_vec++, ++count, ++allcount)   
		{   		
			if(0 == bOnce){
				bOnce = 1;
				PPM_OS::snprintf(tm_file,sizeof(tm_file),"%d",*p_vec); 
			}
			else
			{
				PPM_OS::snprintf(tm_file,sizeof(tm_file),"%s,%d",tm_file,*p_vec); 
			}

			if(cutnum == count || allcount == p_map->second.size())
			{ 
				//获取头文件信息
				SearchLogFileSInfo(p_map->first.strlog.c_str(), p_map->first.testtype, tm_file, 1);

				(this->*pfunc_TestTypeData[p_map->first.testtype])(p_map->first.strtb, tm_file);

				PPM_OS::memset(tm_file, 0, sizeof(tm_file));
				count = 0;
				bOnce = 0;
			}

		}
	}

	//处理结束
	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
	return TRUE;
}

BOOL CStatisticSock_AreaStat::GetAreaStat(const BYTE* const pData, const int nCount,int& ioffset)
{
	tAreaStat.Clear();
	//timetype
	PPM_OS::memcpy(&tAreaStat.timetype,pData + ioffset,4);
	tAreaStat.timetype = MAKEINT_NETSEQ1(tAreaStat.timetype);
	MOVENEXT(ioffset,4,nCount);

	PPM_OS::memcpy(&tAreaStat.time1,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	tAreaStat.time1 = MAKEINT_NETSEQ1(tAreaStat.time1);

	PPM_OS::memcpy(&tAreaStat.time2,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount)
		tAreaStat.time2 = MAKEINT_NETSEQ1(tAreaStat.time2);

	//areatype
	PPM_OS::memcpy(&tAreaStat.iareatypeid,pData + ioffset,4);
	tAreaStat.iareatypeid = MAKEINT_NETSEQ1(tAreaStat.iareatypeid);
	MOVENEXT(ioffset,4,nCount);

	//areaid
	PPM_OS::memset(tAreaStat.szareaid, 0, sizeof(tAreaStat.szareaid));

	int itemp;
	PPM_OS::memcpy(&itemp,pData + ioffset,4);
	itemp = MAKEINT_NETSEQ1(itemp);

	if(-1 == itemp)
	{
		MOVENEXT(ioffset,4,nCount)
	}
	else
	{
		if(FALSE == CSearch_PI::GetTextValue_int(pData,nCount,ioffset,tAreaStat.szareaid)){
			return FALSE;
		}
	}
    return TRUE;
}

BOOL CStatisticSock_AreaStat::GetFileList(const BYTE* const pData, const int nCount,int& ioffset)
{
	//type
	char tm_sql_part[1024];
	PPM_OS::memset(&tm_sql_part, 0, sizeof(tm_sql_part));
	if(FALSE == CSearch_PI::GetTypeSql(pData,nCount,ioffset,tm_sql_part)){
		return FALSE;
	}

	char tm_sql[9000];

	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_stat_area_stat_get '%s',%d,%d,%d",
		tm_sql_part,
		tAreaStat.timetype,
		tAreaStat.time1,
		tAreaStat.time2);

	m_map_filelist.clear();
	m_iRowType_ = TYPE_VEC_FILE_LIST;
    SEARCHSQL(m_iRowType_, tm_sql);
	return TRUE;
}

void CStatisticSock_AreaStat::DealTestType_Default(std::string strtb, char* tm_file)
{

}

void CStatisticSock_AreaStat::DealTestType_Gsm(std::string strtb, char* tm_file)
{
	char tm_sql[9000];
	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_stat_area_stat_get_1  %d,'%s', '%s','%s'",
		tAreaStat.iareatypeid,
		tAreaStat.szareaid,
		strtb.c_str(),
		tm_file);

	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_STAT_NOGIS_V_GSM_IDLE;
    SEARCHSQL(m_iRowType_, tm_sql);

	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_stat_area_stat_get_2 %d,'%s', '%s','%s'",
		tAreaStat.iareatypeid,
		tAreaStat.szareaid,
		strtb.c_str(),
		tm_file);

	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_STAT_NOGIS_V_GSM_DEDICATED;
    SEARCHSQL(m_iRowType_, tm_sql);

	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_stat_area_stat_get_3 %d,'%s', '%s','%s'",
		tAreaStat.iareatypeid,
		tAreaStat.szareaid,
		strtb.c_str(),
		tm_file);

	m_iRowType_ = TYPE_VEC_STAT_AREA_DATA;
	m_bResponseType_ = RESTYPE_STAT_NOGIS_D_GPRS;
	SEARCHSQL(m_iRowType_, tm_sql);

	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_stat_area_stat_get_4 %d,'%s', '%s','%s'",
		tAreaStat.iareatypeid,
		tAreaStat.szareaid,
		strtb.c_str(),
		tm_file);

	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_STAT_AREA_STAT_EVENT;
    SEARCHSQL(m_iRowType_, tm_sql);
}
void CStatisticSock_AreaStat::DealTestType_Tdscdma(std::string strtb, char* tm_file)
{
	char tm_sql[9000];
	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_stat_area_stat_get_5  %d,'%s', '%s','%s'",
		tAreaStat.iareatypeid,
		tAreaStat.szareaid,
		strtb.c_str(),
		tm_file);

	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_STAT_NOGIS_TDSCDMA_AMR;
	SEARCHSQL(m_iRowType_, tm_sql);

	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_stat_area_stat_get_6 %d,'%s', '%s','%s'",
		tAreaStat.iareatypeid,
		tAreaStat.szareaid,
		strtb.c_str(),
		tm_file);

	m_iRowType_ = TYPE_VEC_STAT_AREA_DATA;
	m_bResponseType_ = RESTYPE_STAT_NOGIS_TDSCDMA_PS;
	SEARCHSQL(m_iRowType_, tm_sql);

	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_stat_area_stat_get_7 %d,'%s', '%s','%s'",
		tAreaStat.iareatypeid,
		tAreaStat.szareaid,
		strtb.c_str(),
		tm_file);

	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_STAT_NOGIS_TDSCDMA_VP;
	SEARCHSQL(m_iRowType_, tm_sql);

	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_stat_area_stat_get_4 %d,'%s', '%s','%s'",
		tAreaStat.iareatypeid,
		tAreaStat.szareaid,
		strtb.c_str(),
		tm_file);

	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_STAT_AREA_STAT_EVENT;
    SEARCHSQL(m_iRowType_, tm_sql);
}
/************************************************************************/
/* CStatisticSock_WorkKPI                                              */
/************************************************************************/
CStatisticSock_WorkKPI::CStatisticSock_WorkKPI()
{
	for(int i = 0; i < 256; i++)
	{
		pfunc_TestTypeData[i] = &CStatisticSock_WorkKPI::DealTestType_Default;
	}

	pfunc_TestTypeData[CEnumDef::T_GSM_DT] = &CStatisticSock_WorkKPI::DealTestType_Gsm;
	pfunc_TestTypeData[CEnumDef::T_GSM_CQT] = &CStatisticSock_WorkKPI::DealTestType_Gsm;
	pfunc_TestTypeData[CEnumDef::T_AUTODT] = &CStatisticSock_WorkKPI::DealTestType_Gsm;
	pfunc_TestTypeData[CEnumDef::T_AUTODT_TA] = &CStatisticSock_WorkKPI::DealTestType_Gsm;
	pfunc_TestTypeData[CEnumDef::T_AUTOCQT] = &CStatisticSock_WorkKPI::DealTestType_Gsm;

	pfunc_TestTypeData[CEnumDef::T_SCANTEST] = &CStatisticSock_WorkKPI::DealTestType_Scan;

	pfunc_TestTypeData[CEnumDef::T_CDMA_DT] = &CStatisticSock_WorkKPI::DealTestType_Cdma;
	pfunc_TestTypeData[CEnumDef::T_CDMA_CQT] = &CStatisticSock_WorkKPI::DealTestType_Cdma;

	pfunc_TestTypeData[CEnumDef::T_CDMA2000_DT] = &CStatisticSock_WorkKPI::DealTestType_Cdma2000;

	pfunc_TestTypeData[CEnumDef::T_TDSCDMA_DT] = &CStatisticSock_WorkKPI::DealTestType_Tdscdma;
	pfunc_TestTypeData[CEnumDef::T_TDSCDMA_CQT] = &CStatisticSock_WorkKPI::DealTestType_Tdscdma;

	pfunc_TestTypeData[CEnumDef::T_WCDMA_DT] = &CStatisticSock_WorkKPI::DealTestType_Wcdma;
	pfunc_TestTypeData[CEnumDef::T_WCDMA_CQT] = &CStatisticSock_WorkKPI::DealTestType_Wcdma;

}

CStatisticSock_WorkKPI::~CStatisticSock_WorkKPI()
{

}

BOOL
CStatisticSock_WorkKPI::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	MOVENEXTTEST(ioffset,13,nCount);

	//time
	if (!GetTimeSpan(pData, nCount, ioffset))
	{
		return FALSE;
	}

	if (!GetBaseFileList(pData, nCount, ioffset))
	{
		return FALSE;
	}

	if(0 >= m_map_filelist.size())	//no data
	{
		//处理结束
		FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
		return TRUE;
	}

	auto p_map = m_map_filelist.begin();   
	char tm_file[8000];
	int bOnce;		
	int cutnum = 600;         //ado对提交的sql语句限制为8000字节以内

	for (; p_map != m_map_filelist.end(); p_map++)   
	{
		auto p_vec = p_map->second.begin();	
		PPM_OS::memset(&tm_file, 0, sizeof(tm_file));	
		bOnce = 0;

		size_t allcount=1;	

		for (int count=1; p_vec != p_map->second.end(); p_vec++, ++count, ++allcount)   
		{   		
			if(0 != bOnce){
				PPM_OS::snprintf(tm_file,sizeof(tm_file),"%s,%d",tm_file,*p_vec); 
			}
			else
			{
				PPM_OS::snprintf(tm_file,sizeof(tm_file),"%d",*p_vec); 
				bOnce = 1;
			}

			if(cutnum == count || allcount == p_map->second.size())
			{ 
				DealWorkKpi(p_map->first.testtype, p_map->first.strlog, p_map->first.strtb, tm_file);

				PPM_OS::memset(tm_file, 0, sizeof(tm_file));
				count = 0;
				bOnce = 0;
			}
		}
	}
	//处理结束
	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
	return TRUE;
}

BOOL CStatisticSock_WorkKPI::GetTimeSpan(const BYTE* const pData, const int nCount,int& ioffset)
{
	STRU_SEARCH_TIMESPAN tm_Time_Span;	
	PPM_OS::memcpy(&tm_Time_Span.istime,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Time_Span.ietime,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	tm_Time_Span.ConvertToNetSeq();	
    return TRUE;
}

BOOL CStatisticSock_WorkKPI::GetBaseFileList(const BYTE* const pData, const int nCount,int& ioffset)
{
	//type
	char tm_sql_part[1024];
	PPM_OS::memset(&tm_sql_part, 0, sizeof(tm_sql_part));
	if(FALSE == CSearch_PI::GetTypeSql(pData,nCount,ioffset,tm_sql_part)){
		return FALSE;
	}

	//filename
	char szfile[255];
	PPM_OS::memset(&szfile, 0, sizeof(szfile));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,szfile,
		sizeof(szfile)));

	char tm_sql[9000];	
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),"exec mc_sp_stat_work_kpi_get %d,%d,'%s','%s'",
		tm_Time_Span.istime, 
		tm_Time_Span.ietime,
		tm_sql_part,
		szfile);

	m_map_filelist.clear();
	m_iRowType_ = TYPE_VEC_FILE_LIST;             //文件id获取 +　采样点数目获取
    SEARCHSQL(m_iRowType_, tm_sql);

    return TRUE;
}

void CStatisticSock_WorkKPI::DealTestType_Default(char* tm_file)
{

}
void CStatisticSock_WorkKPI::DealTestType_Gsm(char* tm_file)
{
	char tm_sql[9000];
	//second recordset
	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_stat_work_kpi_get_gsm_idle '%s'",
		tm_file);
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_STAT_LOG_KPI_PARA_1;
	SEARCHSQL(m_iRowType_, tm_sql);

	//third recordset
	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_stat_work_kpi_get_gsm_dedi  '%s'",
		tm_file);
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_STAT_LOG_KPI_PARA_2;
    SEARCHSQL(m_iRowType_, tm_sql);

	//fourth recordset
	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_stat_work_kpi_get_gsm_data  '%s'",
		tm_file);
	m_iRowType_ = TYPE_VEC_GIS_DATA;
	m_bResponseType_ = RESTYPE_STAT_LOG_KPI_PARA_3;
    SEARCHSQL(m_iRowType_, tm_sql);
}
void CStatisticSock_WorkKPI::DealTestType_Scan(char* tm_file)
{
	char tm_sql[9000];
	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_stat_work_kpi_get_scan  '%s'",
		tm_file);
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_STAT_LOG_KPI_SCAN_PARA;
    SEARCHSQL(m_iRowType_, tm_sql);
}
void CStatisticSock_WorkKPI::DealTestType_Cdma(char* tm_file)
{
	char tm_sql[9000];
	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_stat_work_kpi_get_cdma_voice  '%s'",
		tm_file);
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_STAT_LOG_KPI_CDMA_PARA;
	SEARCHSQL(m_iRowType_, tm_sql);

	//cdma 1x
	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_stat_work_kpi_get_cdma_data  '%s'",
		tm_file);
	m_iRowType_ = TYPE_VEC_GIS_DATA;
	m_bResponseType_ = RESTYPE_STAT_LOG_KPI_CDMA_DATA;
    SEARCHSQL(m_iRowType_, tm_sql);
}
void CStatisticSock_WorkKPI::DealTestType_Cdma2000(char* tm_file)
{
	char tm_sql[9000];
	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_stat_work_kpi_get_cdma2000  '%s'",
		tm_file);
	m_iRowType_ = TYPE_VEC_GIS_DATA;
	m_bResponseType_ = RESTYPE_STAT_LOG_KPI_CDMA2000_DATA;
    SEARCHSQL(m_iRowType_, tm_sql);
}
void CStatisticSock_WorkKPI::DealTestType_Tdscdma(char* tm_file)
{
	char tm_sql[9000];
	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_stat_work_kpi_get_td_amr  '%s'",
		tm_file);
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_STAT_LOG_KPI_TDSCDMA_AMR;
	SEARCHSQL(m_iRowType_, tm_sql);

	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_stat_work_kpi_get_td_ps  '%s'",
		tm_file);
	m_iRowType_ = TYPE_VEC_GIS_DATA;
	m_bResponseType_ = RESTYPE_STAT_LOG_KPI_TDSCDMA_PS;
	SEARCHSQL(m_iRowType_, tm_sql);

	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_stat_work_kpi_get_td_vp  '%s'",
		tm_file);
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_STAT_LOG_KPI_TDSCDMA_VP;
    SEARCHSQL(m_iRowType_, tm_sql);
}
void CStatisticSock_WorkKPI::DealTestType_Wcdma(char* tm_file)
{
	char tm_sql[9000];
	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_stat_work_kpi_get_w_amr  '%s'",
		tm_file);
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_STAT_LOG_KPI_WCDMA_AMR;
    SEARCHSQL(m_iRowType_, tm_sql);

	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_stat_work_kpi_get_w_ps '%s'",
		tm_file);
	m_iRowType_ = TYPE_VEC_GIS_DATA;
	m_bResponseType_ = RESTYPE_STAT_LOG_KPI_WCDMA_PS;
    SEARCHSQL(m_iRowType_, tm_sql);

	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_stat_work_kpi_get_w_vp  '%s'",
		tm_file);
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_STAT_LOG_KPI_WCDMA_VP;
    SEARCHSQL(m_iRowType_, tm_sql);

	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_stat_work_kpi_get_w_pshs  '%s'",
		tm_file);
	m_iRowType_ = TYPE_VEC_GIS_DATA;
	m_bResponseType_ = RESTYPE_STAT_LOG_KPI_WCDMA_PSHS;
    SEARCHSQL(m_iRowType_, tm_sql);
}

void CStatisticSock_WorkKPI::DealTestTypeKpi(int testtype, std::string strlog, char* tm_file)
{
	char tm_sql[9000];
	//first recordset
	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_stat_work_kpi_get_file_info '%s', '%s'",
		tm_file,
		strlog.c_str());

	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_STAT_LOG_KPI_FILE;
	SEARCHSQL(m_iRowType_, tm_sql);

	(this->*pfunc_TestTypeData[testtype])(tm_file);
}

void CStatisticSock_WorkKPI::DealEventKpi(std::string strtb, char* tm_file)
{
	char tm_sql[9000];
	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_stat_work_kpi_get_event  '%s','%s'",
		strtb.c_str(),
		tm_file);
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_STAT_LOG_KPI_EVENT;
    SEARCHSQL(m_iRowType_, tm_sql);
}

void CStatisticSock_WorkKPI::DealWorkKpi(int testtype, std::string strlog, std::string strtb, char* tm_file)
{
	//获取头文件信息
	SearchLogFileSInfo(strlog.c_str(), testtype, tm_file, 0);

	if (strtb.size() < 12)  
	{
		DealTestTypeKpi(testtype, strlog, tm_file);
	}
	//event
	else if (strtb.size() >= 12)
	{	
		DealEventKpi(strtb, tm_file);
	}              
}



