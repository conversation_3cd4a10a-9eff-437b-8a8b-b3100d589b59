// StatisticSock_Area.h: interface for the CStatisticSock_Area class.
//
//////////////////////////////////////////////////////////////////////

#ifndef __STATISTICSOCK_AREA_H__
#define __STATISTICSOCK_AREA_H__

#include "./SearchSock.h"
   

class CStatisticSock_AreaLog : public CSearchSock
{
public:
	CStatisticSock_AreaLog();
	virtual ~CStatisticSock_AreaLog();

	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);

private:
  BOOL GetTimeSpan(const BYTE* const pData, const int nCount,int& ioffset);
  BOOL GetBaseFileList(const BYTE* const pData, const int nCount,int& ioffset);

	void (CStatisticSock_AreaLog::*pfunc_TestTypeData[256])(char* tm_file);
	void DealTestType_Default(char* tm_file);
  void DealTestType_Gsm(char* tm_file);
	void DealTestType_Scan(char* tm_file);
	void DealTestType_Cdma(char* tm_file);
	void DealTestType_Tdscdma(char* tm_file);

	void DealAreaLogFileStat(std::string strlog, std::string strtb, int testtype, char* tm_file);
  void DealGsmStat(int testtype, std::string strtb, char* tm_file);

	STRU_SEARCH_TIMESPAN tm_Time_Span;
	STRU_SEARCH_AREA tm_Search_Area;
};


class CStatisticSock_AreaLogKpi : public CSearchSock
{
public:
	CStatisticSock_AreaLogKpi();
	virtual ~CStatisticSock_AreaLogKpi();

	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
private:
	BOOL GetTimeSpan(const BYTE* const pData, const int nCount,int& ioffset);
	BOOL GetBaseFileList(const BYTE* const pData, const int nCount,int& ioffset);

	void (CStatisticSock_AreaLogKpi::*pfunc_TestTypeData[256])(char* tm_file);
	void DealTestTypeData(int testtype, std::string strtb, char* tm_file);
	void DealTestType_Default(char* tm_file);
	void DealTestType_Gsm(char* tm_file);
	void DealTestType_Scan(char* tm_file);
	void DealTestType_Cdma(char* tm_file);
	void DealTestType_Tdscdma(char* tm_file);
	void DealTestType_Wcdma(char* tm_file);
    void DealStat(std::string strtb, char* tm_file);
	BOOL GetAreaLogKpiSearchAreaInfo(const BYTE* const pData, const int nCount,int& ioffset);

private:
	STRU_SEARCH_TIMESPAN tm_Time_Span;
	STRU_SEARCH_AREA tm_Search_Area;
};

class CStatisticSock_AreaKPI : public CSearchSock
{
public:
	CStatisticSock_AreaKPI();
	virtual ~CStatisticSock_AreaKPI();

	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);

private:
	BOOL GetTimeSpan(const BYTE* const pData, const int nCount,int& ioffset);
	BOOL GetBaseFileList(const BYTE* const pData, const int nCount,int& ioffset);

	void (CStatisticSock_AreaKPI::*pfunc_TestTypeData[256])(std::string strtb, char* tm_file);
	void DealTestTypeData(int testtype, std::string strtb, char* tm_file);
	void DealTestType_Default(std::string strtb, char* tm_file);
	void DealTestType_Gsm(std::string strtb, char* tm_file);
	void DealTestType_Scan(std::string strtb, char* tm_file);
	void DealTestType_Cdma(std::string strtb, char* tm_file);
	void DealTestType_Cdma2000(std::string strtb, char* tm_file);
	void DealTestType_Tdscdma(std::string strtb, char* tm_file);
	void DealTestType_Wcdma(std::string strtb, char* tm_file);
    void DealStatKpi(std::string strtb, char* tm_file);
	BOOL GetAreaKpiSearchAreaInfo(const BYTE* const pData, const int nCount,int& ioffset);

	void MakeAndStr(std::string& strFileName, bool& flag);
	void MakeOrStr(std::string& strFileName, bool& flag);
private:
	STRU_SEARCH_TIMESPAN tm_Time_Span;
	STRU_SEARCH_AREA tm_Search_Area;
	std::string makestr;
};


class CStatisticSock_AreaCellKPI : public CSearchSock
{
public:
	CStatisticSock_AreaCellKPI();
	virtual ~CStatisticSock_AreaCellKPI();

	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);

private:
	BOOL GetTimeSpan(const BYTE* const pData, const int nCount,int& ioffset);
	BOOL GetBaseFileList(const BYTE* const pData, const int nCount,int& ioffset);

	void (CStatisticSock_AreaCellKPI::*pfunc_TestTypeData[256])(std::string strtb, char* tm_file);
	void DealTestTypeData(int testtype, std::string strtb, char* tm_file);
	void DealTestType_Default(std::string strtb, char* tm_file);
	void DealTestType_Gsm(std::string strtb, char* tm_file);
	void DealTestType_Scan(std::string strtb, char* tm_file);
	void DealTestType_Cdma(std::string strtb, char* tm_file);
	void DealTestType_Tdscdma(std::string strtb, char* tm_file);
    void DealCellKpi(std::string strtb, char* tm_file);

private:
	STRU_SEARCH_TIMESPAN tm_Time_Span;
};


class CStatisticSock_AreaStatOld : public CSearchSock
{
public:
	CStatisticSock_AreaStatOld();
	virtual ~CStatisticSock_AreaStatOld();

	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
	
private:
	BOOL SearchStatAreaDetail(STRU_STAT_NOGIS *tm_Search_NOGIS);

private:
    void (CStatisticSock_AreaStatOld::*pfunc_TestTypeData[256])(STRU_STAT_NOGIS *tm_Search_NOGIS, std::string strtb, char* tm_file);
    void DealTestType_Default(STRU_STAT_NOGIS *tm_Search_NOGIS, std::string strtb, char* tm_file);
    void DealTestType_Gsm(STRU_STAT_NOGIS *tm_Search_NOGIS, std::string strtb, char* tm_file);
    void DealTestType_Tdscdma(STRU_STAT_NOGIS *tm_Search_NOGIS, std::string strtb, char* tm_file);

};


class CStatisticSock_AreaStat : public CSearchSock
{
public:
	CStatisticSock_AreaStat();
	virtual ~CStatisticSock_AreaStat();

	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);

private:
    struct Stru_AreaStat
	{
		void Clear(void)
		{
			timetype = 0;
			time1 = 0;
			time2 = 0;
			iareatypeid = 0;
			memset(szareaid, 0, sizeof(szareaid));
		};

        int timetype;
		int time1;
		int time2;
		int iareatypeid;
		char szareaid[255];
	}tAreaStat;

private:
	BOOL GetAreaStat(const BYTE* const pData, const int nCount,int& ioffset);
	BOOL GetFileList(const BYTE* const pData, const int nCount,int& ioffset);

	void (CStatisticSock_AreaStat::*pfunc_TestTypeData[256])(std::string strtb, char* tm_file);
	void DealTestType_Default(std::string strtb, char* tm_file);
	void DealTestType_Gsm(std::string strtb, char* tm_file);
	void DealTestType_Tdscdma(std::string strtb, char* tm_file);

};

class CStatisticSock_WorkKPI : public CSearchSock
{
public:
	CStatisticSock_WorkKPI();
	virtual ~CStatisticSock_WorkKPI();

	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);

private:
	BOOL GetTimeSpan(const BYTE* const pData, const int nCount,int& ioffset);
	BOOL GetBaseFileList(const BYTE* const pData, const int nCount,int& ioffset);

	void (CStatisticSock_WorkKPI::*pfunc_TestTypeData[256])(char* tm_file);
	void DealTestType_Default(char* tm_file);
	void DealTestType_Gsm(char* tm_file);
	void DealTestType_Scan(char* tm_file);
	void DealTestType_Cdma(char* tm_file);
	void DealTestType_Cdma2000(char* tm_file);
	void DealTestType_Tdscdma(char* tm_file);
	void DealTestType_Wcdma(char* tm_file);

	void DealTestTypeKpi(int testtype, std::string strlog, char* tm_file);
	void DealEventKpi(std::string strtb, char* tm_file);
	void DealWorkKpi(int testtype, std::string strlog, std::string strtb, char* tm_file);

private:
	STRU_SEARCH_TIMESPAN tm_Time_Span;
};


#endif
