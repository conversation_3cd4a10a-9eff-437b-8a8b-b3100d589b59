// StatisticSock_Cell.cpp: implementation of the CStatisticSock_Cell class.
//
//////////////////////////////////////////////////////////////////////

#include "StatisticSock_Cell.h"

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////
CStatisticSock_CellKPI::CStatisticSock_CellKPI()
{
	for (int i = 0; i < 256; i++)
	{
		pfunc_TestTypeData[i] = &CStatisticSock_CellKPI::DealTestType_Default;
	}

	pfunc_TestTypeData[CEnumDef::T_GSM_DT] = &CStatisticSock_CellKPI::DealTestType_Gsm;
	pfunc_TestTypeData[CEnumDef::T_GSM_CQT] = &CStatisticSock_CellKPI::DealTestType_Gsm;
	pfunc_TestTypeData[CEnumDef::T_AUTODT] = &CStatisticSock_CellKPI::DealTestType_Gsm;
	pfunc_TestTypeData[CEnumDef::T_AUTODT_TA] = &CStatisticSock_CellKPI::DealTestType_Gsm;
	pfunc_TestTypeData[CEnumDef::T_AUTOCQT] = &CStatisticSock_CellKPI::DealTestType_Gsm;

	pfunc_TestTypeData[CEnumDef::T_SCANTEST] = &CStatisticSock_CellKPI::DealTestType_Scan;

	pfunc_TestTypeData[CEnumDef::T_CDMA_DT] = &CStatisticSock_CellKPI::DealTestType_Cdma;
	pfunc_TestTypeData[CEnumDef::T_CDMA_CQT] = &CStatisticSock_CellKPI::DealTestType_Cdma;

	pfunc_TestTypeData[CEnumDef::T_TDSCDMA_DT] = &CStatisticSock_CellKPI::DealTestType_Tdscdma;
	pfunc_TestTypeData[CEnumDef::T_TDSCDMA_CQT] = &CStatisticSock_CellKPI::DealTestType_Tdscdma;

	pfunc_TestTypeData[CEnumDef::T_WCDMA_CQT] = &CStatisticSock_CellKPI::DealTestType_Wcdma;
	pfunc_TestTypeData[CEnumDef::T_WCDMA_DT] = &CStatisticSock_CellKPI::DealTestType_Wcdma;

}
CStatisticSock_CellKPI::~CStatisticSock_CellKPI()
{

}
/************************************************************************
CStatisticSock_CellKPI
************************************************************************/
BOOL  
CStatisticSock_CellKPI::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	MOVENEXTTEST(ioffset,23,nCount);

	if (!GetTime(pData, nCount, ioffset))
	{
		return FALSE;
	}

	if (!GetFileList(pData, nCount, ioffset))
	{
		return FALSE;
	}

	if(0 >= m_map_filelist.size())	//no data
	{
		//处理结束
		FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
		return TRUE;
	}

	auto p_map = m_map_filelist.begin();   
	char tm_file[8000];
	int bOnce;		
	int cutnum = 600;         //ado对提交的sql语句限制为8000字节以内

	for (; p_map != m_map_filelist.end(); p_map++)   
	{

		auto p_vec = p_map->second.begin();
		bOnce = 0;
		PPM_OS::memset(&tm_file, 0, sizeof(tm_file));	
		size_t allcount=1;	

		for (int count=1; p_vec != p_map->second.end(); p_vec++, ++count, ++allcount)   
		{   		
			if(0 == bOnce)
			{
				PPM_OS::snprintf(tm_file,sizeof(tm_file),"%d",*p_vec); 
				bOnce = 1;
			}
			else
			{
				PPM_OS::snprintf(tm_file,sizeof(tm_file),"%s,%d",tm_file,*p_vec); 
			}

			if(cutnum == count 
				|| allcount == p_map->second.size())
			{ 
				//获取头文件信息
				SearchLogFileSInfo(p_map->first.strlog.c_str(), p_map->first.testtype, tm_file, 0);   

				DealTestType_Data(p_map->first.testtype, p_map->first.strtb, tm_file);

				PPM_OS::memset(tm_file, 0, sizeof(tm_file));
				count = 0;
				bOnce = 0;
			}
		}	         
	}
	//处理结束
	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
	return TRUE;
}

BOOL CStatisticSock_CellKPI::GetTime(const BYTE* const pData, const int nCount,int& ioffset)
{
	//time
	PPM_OS::memcpy(&tm_Time_Span.istime,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Time_Span.ietime,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	tm_Time_Span.ConvertToNetSeq();	
    return TRUE;
}

BOOL CStatisticSock_CellKPI::GetArea(const BYTE* const pData, const int nCount,int& ioffset)
{
	//longitude and latitude
	PPM_OS::memcpy(&tm_Search_Area.itllongitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Search_Area.itllatitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Search_Area.ibrlongitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Search_Area.ibrlatitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);

	tm_Search_Area.ConvertToNetSeq();	
    return TRUE;
}
BOOL CStatisticSock_CellKPI::GetCell(const BYTE* const pData, const int nCount,int& ioffset)
{
	PPM_OS::memcpy(&tm_Search_Cell.iLAC,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Search_Cell.wRAC,pData + ioffset,2);
	MOVENEXT(ioffset,2,nCount);
	PPM_OS::memcpy(&tm_Search_Cell.iCI,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);

	tm_Search_Cell.ConvertToNetSeq();
    return TRUE;
}
BOOL CStatisticSock_CellKPI::GetFileList(const BYTE* const pData, const int nCount,int& ioffset)
{
	//type
	char tm_sql_part[1024];
	PPM_OS::memset(&tm_sql_part, 0, sizeof(tm_sql_part));
	if(FALSE == CSearch_PI::GetTypeSql(pData,nCount,ioffset,tm_sql_part)){
		return FALSE;
	}

	if (!GetArea(pData, nCount, ioffset))
	{
		return FALSE;
	}

	if (!GetCell(pData, nCount, ioffset))
	{
		return FALSE;
	}

	char tm_sql[9000];	
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_stat_area_kpi_get %d,%d, %d,%d,%d,%d, '%s', 'NULL'",
		tm_Time_Span.istime, 
		tm_Time_Span.ietime,
		tm_Search_Area.itllongitude,
		tm_Search_Area.itllatitude,
		tm_Search_Area.ibrlongitude,
		tm_Search_Area.ibrlatitude,
		tm_sql_part);

	m_map_filelist.clear();
	m_iRowType_ = TYPE_VEC_FILE_LIST;
    SEARCHSQL(m_iRowType_, tm_sql);
    return TRUE;
}

void CStatisticSock_CellKPI::DealTestType_Data(int testtype, std::string strtb, char* tm_file)
{
	if (strtb.size() < 12)  
	{
		(this->*pfunc_TestTypeData[testtype])(strtb, tm_file);
	}
	else if (strtb.size() >= 12)  
	{
		DealStatGis_Event(strtb, tm_file);
	}
}

void CStatisticSock_CellKPI::DealTestType_Default(std::string strtb, char* tm_file)
{

}
void CStatisticSock_CellKPI::DealTestType_Gsm(std::string strtb, char* tm_file)
{
	char tm_sql[9000];
	//second recordset
	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_stat_cell_kpi_get_2  %d,%d,%d, '%s','%s'",
		tm_Search_Cell.iLAC,
		tm_Search_Cell.wRAC,
		tm_Search_Cell.iCI,
		strtb.c_str(),
		tm_file);
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_STAT_CELL_KPI_PARA_1;
	SEARCHSQL(m_iRowType_, tm_sql);

	//third recordset
	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_stat_cell_kpi_get_3  %d,%d,%d, '%s','%s'",
		tm_Search_Cell.iLAC,
		tm_Search_Cell.wRAC,
		tm_Search_Cell.iCI,
		strtb.c_str(),
		tm_file);
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_STAT_CELL_KPI_PARA_2;
	SEARCHSQL(m_iRowType_, tm_sql);

	//fourth recordset
	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_stat_cell_kpi_get_4  %d,%d,%d, '%s','%s'",
		tm_Search_Cell.iLAC,
		tm_Search_Cell.wRAC,
		tm_Search_Cell.iCI,
		strtb.c_str(),
		tm_file);
	m_iRowType_ = TYPE_VEC_GIS_DATA;
	m_bResponseType_ = RESTYPE_STAT_CELL_KPI_PARA_3;
	SEARCHSQL(m_iRowType_, tm_sql);
}
void CStatisticSock_CellKPI::DealTestType_Scan(std::string strtb, char* tm_file)
{
	char tm_sql[9000];
	//sixth recordset
	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_stat_cell_kpi_get_6  %d,%d,%d, '%s','%s'",
		tm_Search_Cell.iLAC,
		tm_Search_Cell.wRAC,
		tm_Search_Cell.iCI,
		strtb.c_str(),
		tm_file);
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_STAT_CELL_KPI_SCAN_PARA;
	SEARCHSQL(m_iRowType_, tm_sql);
}
void CStatisticSock_CellKPI::DealTestType_Cdma(std::string strtb, char* tm_file)
{
	char tm_sql[9000];
	//fifth recordset
	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_stat_cell_kpi_get_5  %d,%d,%d, '%s','%s'",
		tm_Search_Cell.iLAC,
		tm_Search_Cell.wRAC,
		tm_Search_Cell.iCI,
		strtb.c_str(),
		tm_file);
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_STAT_CELL_KPI_CDMA_PARA;
	SEARCHSQL(m_iRowType_, tm_sql);
}
void CStatisticSock_CellKPI::DealTestType_Tdscdma(std::string strtb, char* tm_file)
{
	char tm_sql[9000];
	//TDSCDMA 8~10
	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_stat_cell_kpi_get_8  %d,%d,%d, '%s','%s'",
		tm_Search_Cell.iLAC,
		tm_Search_Cell.wRAC,
		tm_Search_Cell.iCI,
		strtb.c_str(),
		tm_file);
	m_iRowType_ = TYPE_VEC_GIS_DATA;
	m_bResponseType_ = RESTYPE_STAT_CELL_KPI_TDSCDMA_AMR;
	SEARCHSQL(m_iRowType_, tm_sql);

	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_stat_cell_kpi_get_9  %d,%d,%d, '%s','%s'",
		tm_Search_Cell.iLAC,
		tm_Search_Cell.wRAC,
		tm_Search_Cell.iCI,
		strtb.c_str(),
		tm_file);
	m_iRowType_ = TYPE_VEC_GIS_DATA;
	m_bResponseType_ = RESTYPE_STAT_CELL_KPI_TDSCDMA_PS;
	SEARCHSQL(m_iRowType_, tm_sql);

	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_stat_cell_kpi_get_10  %d,%d,%d, '%s','%s'",
		tm_Search_Cell.iLAC,
		tm_Search_Cell.wRAC,
		tm_Search_Cell.iCI,
		strtb.c_str(),
		tm_file);
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_STAT_CELL_KPI_TDSCDMA_VP;
	SEARCHSQL(m_iRowType_, tm_sql);
}
void CStatisticSock_CellKPI::DealTestType_Wcdma(std::string strtb, char* tm_file)
{
	char tm_sql[9000];
	char strtbname[255];
	char strdata[1024];
	PPM_OS::memset(strtbname, 0, sizeof(strtbname));
	PPM_OS::snprintf(strtbname, sizeof(tm_sql), "tb_wcdma_stati_amr_cell_%s", strtb.c_str());
	PPM_OS::memset(strdata, 0, sizeof(strdata));
	PPM_OS::snprintf(strdata, sizeof(strdata), 
		"''BaseInfo'',''CoverRate'',''Rscp'',''Ecio'', ''Rxpower'', ''Txpower'',''Bler'',''Sir''");

	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_stati_cell_get  %d,%d,%d, '%s', '%s', '%s'",
		tm_Search_Cell.iLAC,
		tm_Search_Cell.wRAC,
		tm_Search_Cell.iCI,
		tm_file,
		strtbname,
		strdata);
	m_iRowType_ = TYPE_VEC_SEARCH_DATA_IMAGE;
	m_bResponseType_ = RESTYPE_STATI_CELL_KPI_WCDMA_AMR;
	SEARCHSQL(m_iRowType_, tm_sql);

	PPM_OS::memset(strtbname, 0, sizeof(strtbname));
	PPM_OS::snprintf(strtbname, sizeof(tm_sql), "tb_wcdma_stati_vp_cell_%s", strtb.c_str());
	PPM_OS::memset(strdata, 0, sizeof(strdata));
	PPM_OS::snprintf(strdata, sizeof(strdata), 
		"''BaseInfo'', ''CoverRate'', ''Rscp'', ''Ecio'',''Rxpower'',''Txpower'',''Bler'',''Sir''");

	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_stati_cell_get  %d,%d,%d, '%s', '%s', '%s'",
		tm_Search_Cell.iLAC,
		tm_Search_Cell.wRAC,
		tm_Search_Cell.iCI,
		tm_file,
		strtbname,
		strdata);
	m_iRowType_ = TYPE_VEC_SEARCH_DATA_IMAGE;
	m_bResponseType_ = RESTYPE_STATI_CELL_KPI_WCDMA_VP;
	SEARCHSQL(m_iRowType_, tm_sql);

	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_stat_cell_kpi_get_11  %d,%d,%d, '%s','%s'",
		tm_Search_Cell.iLAC,
		tm_Search_Cell.wRAC,
		tm_Search_Cell.iCI,
		strtb.c_str(),
		tm_file);
	m_iRowType_ = TYPE_VEC_GIS_DATA;
	m_bResponseType_ = RESTYPE_STAT_CELL_KPI_WCDMA_PS;
	SEARCHSQL(m_iRowType_, tm_sql);

	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_stat_cell_kpi_get_12  %d,%d,%d, '%s','%s'",
		tm_Search_Cell.iLAC,
		tm_Search_Cell.wRAC,
		tm_Search_Cell.iCI,
		strtb.c_str(),
		tm_file);
	m_iRowType_ = TYPE_VEC_GIS_DATA;
	m_bResponseType_ = RESTYPE_STAT_CELL_KPI_WCDMA_PSHS;
	SEARCHSQL(m_iRowType_, tm_sql);
}
void CStatisticSock_CellKPI::DealStatGis_Event(std::string strtb, char* tm_file)
{
	char tm_sql[9000];
	//seventh recordset
	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_stat_cell_kpi_get_7  %d,%d,%d, '%s','%s'",
		tm_Search_Cell.iLAC,
		tm_Search_Cell.wRAC,
		tm_Search_Cell.iCI,
		strtb.c_str(),
		tm_file);
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_STAT_CELL_KPI_EVENT;
	SEARCHSQL(m_iRowType_, tm_sql);
}









