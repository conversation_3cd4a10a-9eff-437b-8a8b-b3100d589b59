// StatisticSock_Cell.h: interface for the CStatisticSock_Cell class.
//
//////////////////////////////////////////////////////////////////////

#ifndef __STATISTICSOCK_CELL_H__
#define __STATISTICSOCK_CELL_H__

#include "./SearchSock.h"
   
class CStatisticSock_CellKPI : public CSearchSock
{
public:
    CStatisticSock_CellKPI();
	virtual ~CStatisticSock_CellKPI();

	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);

private:
	BOOL GetTime(const BYTE* const pData, const int nCount,int& ioffset);
	BOOL GetArea(const BYTE* const pData, const int nCount,int& ioffset);
	BOOL GetCell(const BYTE* const pData, const int nCount,int& ioffset);
	BOOL GetFileList(const BYTE* const pData, const int nCount,int& ioffset);
	void DealTestType_Data(int testtype, std::string strtb, char* tm_file);

	void (CStatisticSock_CellKPI::*pfunc_TestTypeData[256])(std::string strtb, char* tm_file);
	void DealTestType_Default(std::string strtb, char* tm_file);
	void DealTestType_Gsm(std::string strtb, char* tm_file);
	void DealTestType_Scan(std::string strtb, char* tm_file);
	void DealTestType_Cdma(std::string strtb, char* tm_file);
	void DealTestType_Tdscdma(std::string strtb, char* tm_file);
	void DealTestType_Wcdma(std::string strtb, char* tm_file);
	void DealStatGis_Event(std::string strtb, char* tm_file);

private:
	STRU_SEARCH_TIMESPAN tm_Time_Span;
	STRU_SEARCH_AREA tm_Search_Area;
	STRU_SEARCH_CELL tm_Search_Cell;
};


#endif
