// StatisticSock_Log.cpp: implementation of the CStatisticSock_Log class.
//
//////////////////////////////////////////////////////////////////////

#include "StatisticSock_Log.h"
#include "./DataBaseInfo.h"


//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

/************************************************************************
CStatisticSock_LogKPI
************************************************************************/
/************************************************************************
1)	测试类型（1BYTE）:dt/cqt/autodt/autocqt/扫频测试等；
2)	文件ID（4BYTES）
************************************************************************/                 


BOOL  
CStatisticSock_LogKPI::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	MOVENEXTTEST(ioffset,12,nCount);

	if (!SetLogKpi(pData, nCount, ioffset))
	{
		return FALSE;
	}

	if (!Set_TmSql(pData, nCount, ioffset))
	{
		return FALSE;
	}

	//文件信息
	DealSearchFileInfo();

    //GSM
    DealSearchGsmKpi();
	
	//cdma_voice
	DealSearchCdmaVoiceKpi();

	//Scan
	DealSearchScanKpi();

	//Event
    DealSearchEventKpi();

	//tdscdma 8~10
    DealSearchTdscdmaKpi();

	//wcdma
	DealSearchWcdmaKpi();

	//cdma cdma2000 data
	DealSearchCdma_Cdma2000DataKpi();
    
    //wcdma image
	DealSearchWcdmaImageKpi();

	//Cdma Image
    DealSearchCdmaImageKpi();
    
	//Tdscdma image
    DealSearchTdscdmaImageKpi();

	//GSM image
    DealSearchGsmImageKpi();

	//处理结束
	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
	return TRUE;
}

BOOL CStatisticSock_LogKPI::SetLogKpi(const BYTE* const pData, const int nCount,int& ioffset)
{
	STRU_SEARCH_LOG_KPI tm_Search_Log_KPI;

	PPM_OS::memcpy(&tm_Search_Log_KPI.iprojecttype,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);

	PPM_OS::memcpy(&tm_Search_Log_KPI.itesttype,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);

	PPM_OS::memcpy(&tm_Search_Log_KPI.ifileID,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);

	tm_Search_Log_KPI.ConvertToNetSeq();

	return TRUE;
}

BOOL CStatisticSock_LogKPI::Set_TmSql(const BYTE* const pData, const int nCount,int& ioffset)
{
	int tm_itesttype = CDataBaseInfo::Instance()->GetTestTypeByProjectID(tm_Search_Log_KPI.iprojecttype);

	//logtbname
	char  tm_logtbname[255];
	PPM_OS::memset(tm_logtbname,0,sizeof(tm_logtbname));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_logtbname,sizeof(tm_logtbname)));
    PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_stat_log_kpi_get %d,%d,%d, '%s'",
		tm_Search_Log_KPI.iprojecttype, 
		tm_itesttype, 
		tm_Search_Log_KPI.ifileID,
		tm_logtbname
		);

	return TRUE;
}

void CStatisticSock_LogKPI::DealSearchFileInfo(void)
{
	//first recordset 
	char tm_sql2[8000];
	PPM_OS::memset(tm_sql2,0,sizeof(tm_sql2));
	PPM_OS::snprintf(tm_sql2,sizeof(tm_sql2),"%s,%d",tm_sql,1);
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_STAT_LOG_KPI_FILE;
	SEARCHSQL(m_iRowType_,tm_sql2);
}

void CStatisticSock_LogKPI::DealSearchGsmKpi(void)
{
	char tm_sql2[8000];
	//second recordset
	PPM_OS::memset(tm_sql2,0,sizeof(tm_sql2));
	PPM_OS::snprintf(tm_sql2,sizeof(tm_sql2),"%s,%d",tm_sql,2);
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_STAT_LOG_KPI_PARA_1;
	SEARCHSQL(m_iRowType_,tm_sql2);

	//third recordset
	PPM_OS::memset(tm_sql2,0,sizeof(tm_sql2));
	PPM_OS::snprintf(tm_sql2,sizeof(tm_sql2),"%s,%d",tm_sql,3);
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_STAT_LOG_KPI_PARA_2;
	SEARCHSQL(m_iRowType_,tm_sql2);

	//fourth recordset
	PPM_OS::memset(tm_sql2,0,sizeof(tm_sql2));
	PPM_OS::snprintf(tm_sql2,sizeof(tm_sql2),"%s,%d",tm_sql,4);
	m_iRowType_ = TYPE_VEC_GIS_DATA;
	m_bResponseType_ = RESTYPE_STAT_LOG_KPI_PARA_3;
	SEARCHSQL(m_iRowType_,tm_sql2);
}

void CStatisticSock_LogKPI::DealSearchCdmaVoiceKpi(void)
{
	char tm_sql2[8000];
	//fifth recordset
	PPM_OS::memset(tm_sql2,0,sizeof(tm_sql2));
	PPM_OS::snprintf(tm_sql2,sizeof(tm_sql2),"%s,%d",tm_sql,5);
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_STAT_LOG_KPI_CDMA_PARA;
	SEARCHSQL(m_iRowType_,tm_sql2);
}

void CStatisticSock_LogKPI::DealSearchScanKpi(void)
{
	char tm_sql2[8000];
	//sixth recordset
	PPM_OS::memset(tm_sql2,0,sizeof(tm_sql2));
	PPM_OS::snprintf(tm_sql2,sizeof(tm_sql2),"%s,%d",tm_sql,6);
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_STAT_LOG_KPI_SCAN_PARA;
	SEARCHSQL(m_iRowType_,tm_sql2);
}

void CStatisticSock_LogKPI::DealSearchEventKpi(void)
{
	char tm_sql2[8000];
	//seventh recordset
	PPM_OS::memset(tm_sql2,0,sizeof(tm_sql2));
	PPM_OS::snprintf(tm_sql2,sizeof(tm_sql2),"%s,%d",tm_sql,7);
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_STAT_LOG_KPI_EVENT;
	SEARCHSQL(m_iRowType_,tm_sql2);
}

void CStatisticSock_LogKPI::DealSearchTdscdmaKpi(void)
{
	char tm_sql2[8000];
	//eighth recordset
	PPM_OS::memset(tm_sql2,0,sizeof(tm_sql2));
	PPM_OS::snprintf(tm_sql2,sizeof(tm_sql2),"%s,%d",tm_sql,8);
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_STAT_LOG_KPI_TDSCDMA_AMR;
	SEARCHSQL(m_iRowType_,tm_sql2);

	//ninth recordset
	PPM_OS::memset(tm_sql2,0,sizeof(tm_sql2));
	PPM_OS::snprintf(tm_sql2,sizeof(tm_sql2),"%s,%d",tm_sql,9);
	m_iRowType_ = TYPE_VEC_GIS_DATA;
	m_bResponseType_ = RESTYPE_STAT_LOG_KPI_TDSCDMA_PS;
	SEARCHSQL(m_iRowType_,tm_sql2);

	//tenth recordset
	PPM_OS::memset(tm_sql2,0,sizeof(tm_sql2));
	PPM_OS::snprintf(tm_sql2,sizeof(tm_sql2),"%s,%d",tm_sql,10);
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_STAT_LOG_KPI_TDSCDMA_VP;
	SEARCHSQL(m_iRowType_,tm_sql2);
}

void CStatisticSock_LogKPI::DealSearchWcdmaKpi(void)
{
	char tm_sql2[8000];
	//11 recordset
    PPM_OS::memset(tm_sql2,0,sizeof(tm_sql2));
    PPM_OS::snprintf(tm_sql2,sizeof(tm_sql2),"%s,%d",tm_sql,11);
    m_iRowType_ = TYPE_VEC_NORMAL;
    m_bResponseType_ = RESTYPE_STAT_LOG_KPI_WCDMA_AMR;
    SEARCHSQL(m_iRowType_,tm_sql2);

    //12 recordset
    PPM_OS::memset(tm_sql2,0,sizeof(tm_sql2));
    PPM_OS::snprintf(tm_sql2,sizeof(tm_sql2),"%s,%d",tm_sql,12);
    m_iRowType_ = TYPE_VEC_GIS_DATA;
    m_bResponseType_ = RESTYPE_STAT_LOG_KPI_WCDMA_PS;
    SEARCHSQL(m_iRowType_,tm_sql2);

    //13 recordset
    PPM_OS::memset(tm_sql2,0,sizeof(tm_sql2));
    PPM_OS::snprintf(tm_sql2,sizeof(tm_sql2),"%s,%d",tm_sql,13);
    m_iRowType_ = TYPE_VEC_NORMAL;
    m_bResponseType_ = RESTYPE_STAT_LOG_KPI_WCDMA_VP;
    SEARCHSQL(m_iRowType_,tm_sql2);

    //14 recordset
    PPM_OS::memset(tm_sql2,0,sizeof(tm_sql2));
    PPM_OS::snprintf(tm_sql2,sizeof(tm_sql2),"%s,%d",tm_sql,14);
    m_iRowType_ = TYPE_VEC_GIS_DATA;
    m_bResponseType_ = RESTYPE_STAT_LOG_KPI_WCDMA_PSHS;
    SEARCHSQL(m_iRowType_,tm_sql2);
}

void CStatisticSock_LogKPI::DealSearchCdma_Cdma2000DataKpi(void)
{
	char tm_sql2[8000];
	//15 recordset
	PPM_OS::memset(tm_sql2,0,sizeof(tm_sql2));
	PPM_OS::snprintf(tm_sql2,sizeof(tm_sql2),"%s,%d",tm_sql,15);
	m_iRowType_ = TYPE_VEC_GIS_DATA;  
	m_bResponseType_ = RESTYPE_STAT_LOG_KPI_CDMA_DATA;
	SEARCHSQL(m_iRowType_,tm_sql2);

	//16 recordset 
	//cdma2000 evdo
	PPM_OS::memset(tm_sql2,0,sizeof(tm_sql2));
	PPM_OS::snprintf(tm_sql2,sizeof(tm_sql2),"%s,%d",tm_sql,16);
	m_iRowType_ = TYPE_VEC_GIS_DATA;  
	m_bResponseType_ = RESTYPE_STAT_LOG_KPI_CDMA2000_DATA;
	SEARCHSQL(m_iRowType_,tm_sql2);
}


void CStatisticSock_LogKPI::DealSearchWcdmaImageKpi(void)
{
	char strtbname[255];
	char strdata[2024];
    PPM_OS::memset(strtbname, 0, sizeof(strtbname));
    PPM_OS::snprintf(strtbname, sizeof(strtbname), "tb_wcdma_stati_amr_log");
    PPM_OS::memset(strdata, 0, sizeof(strdata));
    PPM_OS::snprintf(strdata, sizeof(strdata), "''BaseInfo'',''CoverRate'',''Rscp'',''Ecio'',''Rxpower'',''Txpower'',\
''Bler'',''Sir'',''FreqJam'',''ActiveSet'',''Rxlev'',''BcchCI'',''Rxqual''");

    PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
    PPM_OS::snprintf(tm_sql,sizeof(tm_sql),"exec mc_sp_stati_log_get  '%d', '%s', '%s'",
        tm_Search_Log_KPI.ifileID,
        strtbname,
        strdata);
    m_iRowType_ = TYPE_VEC_SEARCH_DATA_IMAGE;
    m_bResponseType_ = RESTYPE_STATI_LOG_KPI_WCDMA_AMR;
    SEARCHSQL(m_iRowType_,tm_sql);

    //wcdma image vp 
    PPM_OS::memset(strtbname, 0, sizeof(strtbname));
    PPM_OS::snprintf(strtbname, sizeof(strtbname), "tb_wcdma_stati_vp_log");
    PPM_OS::memset(strdata, 0, sizeof(strdata));
    PPM_OS::snprintf(strdata, sizeof(strdata), "''BaseInfo'',''CoverRate'',''Rscp'',''Ecio'',''Rxpower'',\
''Txpower'',''Bler'',''Sir'',''FreqJam'',''ActiveSet'',''Rxlev'',''BcchCI'',''Rxqual''");

    PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
    PPM_OS::snprintf(tm_sql,sizeof(tm_sql),"exec mc_sp_stati_log_get  '%d', '%s', '%s'",
        tm_Search_Log_KPI.ifileID,
        strtbname,
        strdata);
    m_iRowType_ = TYPE_VEC_SEARCH_DATA_IMAGE;
    m_bResponseType_ = RESTYPE_STATI_LOG_KPI_WCDMA_VP;
    SEARCHSQL(m_iRowType_,tm_sql);

    //wcdma image ps 
    PPM_OS::memset(strtbname, 0, sizeof(strtbname));
    PPM_OS::snprintf(strtbname, sizeof(strtbname), "tb_wcdma_stati_ps_log");
    PPM_OS::memset(strdata, 0, sizeof(strdata));
    PPM_OS::snprintf(strdata, sizeof(strdata), "''BaseInfo'',''CoverRate'',''Rscp'',''Ecio'',''Rxpower'',\
''Txpower'',''Bler'',''Sir'',''ActiveSet'',''RLC'',''APP'',''FreqJam'',''PDUSDU'',''CQI'',''Rxlev'',''BcchCI'',''Rxqual''");

    PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
    PPM_OS::snprintf(tm_sql,sizeof(tm_sql),"exec mc_sp_stati_log_get  '%d', '%s', '%s'",
        tm_Search_Log_KPI.ifileID,
        strtbname,
        strdata);
    m_iRowType_ = TYPE_VEC_SEARCH_DATA_IMAGE;
    m_bResponseType_ = RESTYPE_STATI_LOG_KPI_WCDMA_PS;
    SEARCHSQL(m_iRowType_,tm_sql);

    //wcdma image pshs
    PPM_OS::memset(strtbname, 0, sizeof(strtbname));
    PPM_OS::snprintf(strtbname, sizeof(strtbname), "tb_wcdma_stati_pshs_log");
    PPM_OS::memset(strdata, 0, sizeof(strdata));
    PPM_OS::snprintf(strdata, sizeof(strdata), "''BaseInfo'',''CoverRate'',''Rscp'',''Ecio'',''Rxpower'',\
''Txpower'',''Bler'',''Sir'',''ActiveSet'',''RLC'',''APP'',''FreqJam'',''PDUSDU'',''CQI'',''Rxlev'',''BcchCI'',''Rxqual''");

    PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
    PPM_OS::snprintf(tm_sql,sizeof(tm_sql),"exec mc_sp_stati_log_get  '%d', '%s', '%s'",
        tm_Search_Log_KPI.ifileID,
        strtbname,
        strdata);
    m_iRowType_ = TYPE_VEC_SEARCH_DATA_IMAGE;
    m_bResponseType_ = RESTYPE_STATI_LOG_KPI_WCDMA_PSHS;
    SEARCHSQL(m_iRowType_,tm_sql);
}

void CStatisticSock_LogKPI::DealSearchCdmaImageKpi(void)
{
	char strtbname[255];
	char strdata[2024];
	//cdma image voice
	PPM_OS::memset(strtbname, 0, sizeof(strtbname));
	PPM_OS::snprintf(strtbname, sizeof(strtbname), "tb_cdma_stati_voice_log");
	PPM_OS::memset(strdata, 0, sizeof(strdata));
	PPM_OS::snprintf(strdata, sizeof(strdata), "''BaseInfo'',''ReferEcio'',''TotalEcio'',''TotalEc'',''FFER'',\
''CoverRate'',''RxAGC'',''Rxpower'',''Txpower'',''TxGAIN'',''TxAGC'',''Pesq''");
	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
        "exec mc_sp_stati_log_get  '%d', '%s', '%s'",
        tm_Search_Log_KPI.ifileID,
        strtbname,
        strdata);
	m_iRowType_ = TYPE_VEC_SEARCH_DATA_IMAGE;
	m_bResponseType_ = RESTYPE_STATI_LOG_KPI_CDMA_V;
	SEARCHSQL(m_iRowType_,tm_sql);

	//cdma image voice
	PPM_OS::memset(strtbname, 0, sizeof(strtbname));
	PPM_OS::snprintf(strtbname, sizeof(strtbname), "tb_cdma_stati_data_log");
	PPM_OS::memset(strdata, 0, sizeof(strdata));
	PPM_OS::snprintf(strdata, sizeof(strdata), "''BaseInfo'',''ReferEcio'',''TotalEcio'',''TotalEc'',''FFER'',\
''CoverRate'',''RxAGC'',''Rxpower'',''Txpower'',''TxGAIN'',''TxAGC'',''RLC'',''App'',''Bler''");
	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),"exec mc_sp_stati_log_get  '%d', '%s', '%s'",
        tm_Search_Log_KPI.ifileID,
        strtbname,
        strdata);
	m_iRowType_ = TYPE_VEC_SEARCH_DATA_IMAGE;
	m_bResponseType_ = RESTYPE_STATI_LOG_KPI_CDMA_D;
	SEARCHSQL(m_iRowType_,tm_sql);
}

void CStatisticSock_LogKPI::DealSearchTdscdmaImageKpi(void)
{
	char strtbname[255];
	char strdata[2024];
	//tdscdma image voice
	PPM_OS::memset(strtbname, 0, sizeof(strtbname));
	PPM_OS::snprintf(strtbname, sizeof(strtbname), "tb_tdscdma_stati_amr_log");
	PPM_OS::memset(strdata, 0, sizeof(strdata));
	PPM_OS::snprintf(strdata, sizeof(strdata), "''BaseInfo'',''Pccpch'',''Dpch'',''Bler'',''Mos'',''Rxlev'',''FreqJam''");

	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),"exec mc_sp_stati_log_get  '%d', '%s', '%s'",
        tm_Search_Log_KPI.ifileID,
        strtbname,
        strdata);
	m_iRowType_ = TYPE_VEC_SEARCH_DATA_IMAGE;
	m_bResponseType_ = RESTYPE_STATI_LOG_KPI_TDSCDMA_AMR;
	SEARCHSQL(m_iRowType_,tm_sql);

	//tdscdma image VP
	PPM_OS::memset(strtbname, 0, sizeof(strtbname));
	PPM_OS::snprintf(strtbname, sizeof(strtbname), "tb_tdscdma_stati_vp_log");
	PPM_OS::memset(strdata, 0, sizeof(strdata));
	PPM_OS::snprintf(strdata, sizeof(strdata), "''BaseInfo'',''Pccpch'',''Dpch'',''Bler'',''Mos'',''Rxlev'',''FreqJam''");

	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),"exec mc_sp_stati_log_get  '%d', '%s', '%s'",
        tm_Search_Log_KPI.ifileID,
        strtbname,
        strdata);
	m_iRowType_ = TYPE_VEC_SEARCH_DATA_IMAGE;
	m_bResponseType_ = RESTYPE_STATI_LOG_KPI_TDSCDMA_VP;
	SEARCHSQL(m_iRowType_,tm_sql);

	//tdscdma image PS
	PPM_OS::memset(strtbname, 0, sizeof(strtbname));
	PPM_OS::snprintf(strtbname, sizeof(strtbname), "tb_tdscdma_stati_ps_log");
	PPM_OS::memset(strdata, 0, sizeof(strdata));
	PPM_OS::snprintf(strdata, sizeof(strdata), "''BaseInfo'',''Rscp'',''Bler'',''Mos'',''RLC'',''APP'',''MCS'',\
''FTPSlot'',''Bler_Data'',''Bru'',''FreqJam''");

	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),"exec mc_sp_stati_log_get  '%d', '%s', '%s'",
        tm_Search_Log_KPI.ifileID,
        strtbname,
        strdata);
	m_iRowType_ = TYPE_VEC_SEARCH_DATA_IMAGE;
	m_bResponseType_ = RESTYPE_STATI_LOG_KPI_TDSCDMA_PS;
	SEARCHSQL(m_iRowType_,tm_sql);
}

void CStatisticSock_LogKPI::DealSearchGsmImageKpi(void)
{
	char strtbname[255];
	char strdata[2024];
	//GSM image Voice
	PPM_OS::memset(strtbname, 0, sizeof(strtbname));
	PPM_OS::snprintf(strtbname, sizeof(strtbname), "tb_gsm_stati_voice_log");
	PPM_OS::memset(strdata, 0, sizeof(strdata));
	PPM_OS::snprintf(strdata, sizeof(strdata), "''BaseInfo'',''Rxlev'',''Rxqual'',''RxlevFull'',''RxlevSub'',\
''RxlevBcch'',''RxqualFull'',''RxqualSub'',''CiBcch'',''CiTch'',''TA'',''VoiceCode'',''Txpower'',''BcchFreq'',\
''RLTCur'',''RLTMax'',''PesqLQ'',''PesqScore''");

	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),"exec mc_sp_stati_log_get  '%d', '%s', '%s'",
        tm_Search_Log_KPI.ifileID,
        strtbname,
        strdata);
	m_iRowType_ = TYPE_VEC_SEARCH_DATA_IMAGE;
	m_bResponseType_ = RESTYPE_STATI_LOG_KPI_GSM_VOICE;
	SEARCHSQL(m_iRowType_,tm_sql);

	//GSM image Data
	PPM_OS::memset(strtbname, 0, sizeof(strtbname));
	PPM_OS::snprintf(strtbname, sizeof(strtbname), "tb_gsm_stati_data_log");
	PPM_OS::memset(strdata, 0, sizeof(strdata));
	PPM_OS::snprintf(strdata, sizeof(strdata), "''BaseInfo'',''Rxlev'',''Rxqual'',''RxlevFull'',''RxlevSub'',\
''RxlevBcch'',''RxqualFull'',''RxqualSub'',''CiBcch'',''CiTch'',''TA'',''VoiceCode'',''Txpower'',''BcchFreq'',\
''RLTCur'',''RLTMax'',''PesqLQ'',''PesqScore'',''RLC'',''APP'',''MCS'',''FTPSlot'',''Bler''");

	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),"exec mc_sp_stati_log_get  '%d', '%s', '%s'",
        tm_Search_Log_KPI.ifileID,
        strtbname,
        strdata);
	m_iRowType_ = TYPE_VEC_SEARCH_DATA_IMAGE;
	m_bResponseType_ = RESTYPE_STATI_LOG_KPI_GSM_DATA;
	SEARCHSQL(m_iRowType_,tm_sql);

	//GSM MTR image
	PPM_OS::memset(strtbname, 0, sizeof(strtbname));
	PPM_OS::snprintf(strtbname, sizeof(strtbname), "tb_gsm_stati_mtr_log");
	PPM_OS::memset(strdata, 0, sizeof(strdata));
	PPM_OS::snprintf(strdata, sizeof(strdata), "''BaseInfo'',''RxlevDL'',''RxqualDL'',''RxlevUL'',''RxqualUL'',''Ta''");

	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),"exec mc_sp_stati_log_get  '%d', '%s', '%s'",
        tm_Search_Log_KPI.ifileID,
        strtbname,
        strdata);
	m_iRowType_ = TYPE_VEC_SEARCH_DATA_IMAGE;
	m_bResponseType_ = RESTYPE_STATI_LOG_KPI_GSM_UPLINK;
	SEARCHSQL(m_iRowType_,tm_sql);
}

/************************************************************************/
/* CStatisticSock_LogKPILN                                              */
/************************************************************************/

BOOL  
CStatisticSock_LogKPILN::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	int itesttype = 0;
	PPM_OS::memcpy(&itesttype,pData + ioffset,4);
	itesttype = MAKEINT_NETSEQ1(itesttype);
	MOVENEXT(ioffset,4,nCount);

	int ifileid = 0;
	PPM_OS::memcpy(&ifileid,pData + ioffset,4);
	ifileid = MAKEINT_NETSEQ1(ifileid);
	MOVENEXT(ioffset,4,nCount);

	char tm_fileid[255];
	PPM_OS::memset(tm_fileid, 0, sizeof(tm_fileid));
	PPM_OS::snprintf(tm_fileid, sizeof(tm_fileid),  "%d", ifileid);

	//logtbname
	char  tm_logtbname[255];
	PPM_OS::memset(tm_logtbname,0,sizeof(tm_logtbname));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_logtbname,sizeof(tm_logtbname)));

	SearchLogFileSInfo(tm_logtbname, itesttype, tm_fileid, 0);

	char tm_sql[512];
	PPM_OS::memset(tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_stat_log_kpi_get_LN %d,%d, '%s'",
		itesttype, 
		ifileid,
		tm_logtbname
		);

	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_STAT_LOG_KPI_LN;

	if(NULL != m_pDataBaseDeal_){
		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
	}
	Clear();

	PPM_OS::memset(tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_stat_log_kpi_get_LN_TD_Amr %d,%d, '%s'",
		itesttype, 
		ifileid,
		tm_logtbname
		);

	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_STAT_LOG_KPI_LN_TD_AMR;

	if(NULL != m_pDataBaseDeal_){
		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
	}
	Clear();

	PPM_OS::memset(tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_stat_log_kpi_get_LN_TD_Ps %d,%d, '%s'",
		itesttype, 
		ifileid,
		tm_logtbname
		);

	m_iRowType_ = TYPE_VEC_STAT_LN_DATA;
	m_bResponseType_ = RESTYPE_STAT_LOG_KPI_LN_TD_PS;

	if(NULL != m_pDataBaseDeal_){
		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
	}
	Clear();

	PPM_OS::memset(tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_stat_log_kpi_get_LN_GSMV %d,%d, '%s'",
		itesttype, 
		ifileid,
		tm_logtbname
		);

	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_STAT_LOG_KPI_LN_GSMV;

	if(NULL != m_pDataBaseDeal_){
		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
	}
	Clear();

	PPM_OS::memset(tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_stat_log_kpi_get_LN_GSMD %d,%d, '%s'",
		itesttype, 
		ifileid,
		tm_logtbname
		);

	m_iRowType_ = TYPE_VEC_STAT_LN_DATA;
	m_bResponseType_ = RESTYPE_STAT_LOG_KPI_LN_GSMD;

	if(NULL != m_pDataBaseDeal_){
		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
	}
	Clear();


	//处理结束
	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
	return TRUE;
}

BOOL
CStatisticSock_LogKPILN::RowResultDeal_Data_LN()
{
	if(m_vecColumn.size() != CResultDef::Instance()->GetVecResultSize(CResultDef::STATTYPE_KPI_LOG_LN_TD_Ps)){
		return FALSE;
	} 
	if(NULL != m_pServerDeal_){
		PPM_OS::memset(m_pSendBuf_,0,SEND_BUF_MAXSIZE);

		int tm_ioffset = 0;
		
		m_pSendBuf_[tm_ioffset++] = CMD1_SEARCH;
		m_pSendBuf_[tm_ioffset++] = CMD2_RESPONSE;
		m_pSendBuf_[tm_ioffset++] = m_bResponseType_;
		//

		byte szDesc[2000];
		PPM_OS::memset(&szDesc, 0, sizeof(szDesc));

		WORD szDesc_ioffset = 0;
		WORD tm_total = 0;

		for(size_t i = 0; i < m_vecColumn.size(); i++)
		{
			if((i >= 11) && (i <= 26))
			{
				RowResultDeal_Data_LN(i, szDesc, szDesc_ioffset, tm_total, tm_ioffset);
			}
			else
			{
				(this->*pfunc_AddSendValue[m_vecColumn[i].iColType])(tm_ioffset,i);
			}
		}
		if(0 > m_pServerDeal_->SendData(m_pSendBuf_,tm_ioffset)){
			return -1;
		}
	}
	return TRUE;
}
void CStatisticSock_LogKPILN::RowResultDeal_Data_LN(int i, byte* szDesc, WORD& szDesc_ioffset, WORD& tm_total, int& tm_ioffset)
{
	byte sztmp[260];
	WORD tm_len = 0;
	PPM_OS::memcpy(&tm_len,m_vecColumn[i].pColValue,2);
	tm_len = REVERTWORD_NETSEQ1(tm_len);

	if (tm_len > 1)
	{
		PPM_OS::memset(&sztmp, 0, sizeof(sztmp));
		PPM_OS::memcpy(sztmp,m_vecColumn[i].pColValue + 2,tm_len);
		PPM_OS::memcpy(szDesc + szDesc_ioffset, sztmp, tm_len);
		szDesc_ioffset += tm_len;
		tm_total += tm_len;
	}

	if(i == 18 || i == 26 )
	{
		WORD tm_lensave = MAKEWORD_NETSEQ1(tm_total);
		PPM_OS::memcpy(m_pSendBuf_ + tm_ioffset,&tm_lensave,2);
		PPM_OS::memcpy(m_pSendBuf_ + tm_ioffset + 2,szDesc,tm_total);	
		tm_ioffset += tm_total+2;

		PPM_OS::memset(&szDesc, 0, sizeof(szDesc));
		szDesc_ioffset = 0;
		tm_total = 0;
	}
}

BOOL 
CStatisticSock_LogKPILN::RowResultDeal(int nQType)
{
	switch(m_iRowType_)
	{
	case TYPE_VEC_STAT_LN_DATA:
		{
		   return RowResultDeal_Data_LN();
		}
        break;

	default:
		{
			return CSearchSock::RowResultDeal(nQType);
		}
	}
	return FALSE;
}

