// StatisticSock_Log.h: interface for the CStatisticSock_Log class.
//
//////////////////////////////////////////////////////////////////////

#ifndef __STATISTICSOCK_LOG_H__
#define __STATISTICSOCK_LOG_H__

#include "./SearchSock.h"

#define TYPE_VEC_STAT_LN_DATA                  0x60
   
class CStatisticSock_LogKPI : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);

private:
  BOOL SetLogKpi(const BYTE* const pData, const int nCount,int& ioffset);
  BOOL Set_TmSql(const BYTE* const pData, const int nCount,int& ioffset);

	void DealSearchFileInfo(void);
	void DealSearchGsmKpi(void);
	void DealSearchCdmaVoiceKpi(void);
	void DealSearchScanKpi(void);
	void DealSearchEventKpi(void);
	void DealSearchTdscdmaKpi(void);
	void DealSearchWcdmaKpi(void);
	void DealSearchCdma_Cdma2000DataKpi(void);
	void DealSearchWcdmaImageKpi(void);
	void DealSearchCdmaImageKpi(void);
	void DealSearchTdscdmaImageKpi(void);
	void DealSearchGsmImageKpi(void);

private:
	STRU_SEARCH_LOG_KPI tm_Search_Log_KPI;
	char tm_sql[8000];

};

class CStatisticSock_LogKPILN : public CSearchSock
{

public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);

private:
	virtual BOOL RowResultDeal(int nQType);
	BOOL RowResultDeal_Data_LN();
	void RowResultDeal_Data_LN(int i, byte* szDesc, WORD& szDesc_ioffset, WORD& tm_total, int& tm_ioffset);

};

#endif
