// StatisticSock_Other.cpp: implementation of the CStatisticSock_Other class.
//
//////////////////////////////////////////////////////////////////////

#include "StatisticSock_Other.h"

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

/************************************************************************
CStatisticSock_GIS
************************************************************************/
CStatisticSock_GIS::CStatisticSock_GIS()
{
    for (int i = 0; i < 256; i++)
    {
		pfunc_TestTypeData[i] = &CStatisticSock_GIS::DealTestType_Default;
    }

	pfunc_TestTypeData[CEnumDef::T_GSM_DT] = &CStatisticSock_GIS::DealTestType_Gsm;
	pfunc_TestTypeData[CEnumDef::T_GSM_CQT] = &CStatisticSock_GIS::DealTestType_Gsm;
	pfunc_TestTypeData[CEnumDef::T_AUTODT] = &CStatisticSock_GIS::DealTestType_Gsm;
	pfunc_TestTypeData[CEnumDef::T_AUTODT_TA] = &CStatisticSock_GIS::DealTestType_Gsm;
	pfunc_TestTypeData[CEnumDef::T_AUTOCQT] = &CStatisticSock_GIS::DealTestType_Gsm;

	pfunc_TestTypeData[CEnumDef::T_SCANTEST] = &CStatisticSock_GIS::DealTestType_Scan;

	pfunc_TestTypeData[CEnumDef::T_TDSCDMA_DT] = &CStatisticSock_GIS::DealTestType_Tdscdma;
	pfunc_TestTypeData[CEnumDef::T_TDSCDMA_CQT] = &CStatisticSock_GIS::DealTestType_Tdscdma;

}
CStatisticSock_GIS::~CStatisticSock_GIS()
{

}
/************************************************************************
基于GIS的统计
************************************************************************/
BOOL  
CStatisticSock_GIS::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	MOVENEXTTEST(ioffset,32,nCount);

	if (!GetStatGIS(pData, nCount, ioffset))
	{
		return FALSE;
	}
	//get filelist
	if (!GetFileList(pData, nCount, ioffset))
	{
		return FALSE;
	}
	
	if(0 >= m_map_filelist.size())	//no data
	{
		//处理结束
		FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
		return TRUE;
	}

    char tm_file[8000];
	int bOnce = 0;
	int cutnum = 600;

	auto p_map = m_map_filelist.begin();   

    for (; p_map != m_map_filelist.end(); p_map++)   
	{
				 
	    auto p_vec = p_map->second.begin();	
		PPM_OS::memset(&tm_file, 0, sizeof(tm_file));	
		size_t allcount=1;	
		bOnce = 0;

		for (int count=1; p_vec != p_map->second.end(); p_vec++, ++count, ++allcount)   
        {   		
			if(0 == bOnce)
			{
				bOnce = 1;
			 	PPM_OS::snprintf(tm_file,sizeof(tm_file),"%d",*p_vec); 
			}
			else
			{
				PPM_OS::snprintf(tm_file,sizeof(tm_file),"%s,%d",tm_file,*p_vec); 
			}

            if(cutnum == count || allcount == p_map->second.size())
		    { 
			    //获取头文件信息
			    SearchLogFileSInfo(p_map->first.strlog.c_str(), p_map->first.testtype, tm_file, 0);  

			    DealTestType_Data(p_map->first.testtype, p_map->first.strtb, tm_file);

                PPM_OS::memset(tm_file, 0, sizeof(tm_file));
				count = 0;
			    bOnce = 0;
		    }
        }
	}

    FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND); 
	return TRUE;
}

BOOL CStatisticSock_GIS::GetStatGIS(const BYTE* const pData, const int nCount,int& ioffset)
{
	memset(&tm_Search_GIS, 0, sizeof(tm_Search_GIS));

	//longitude and latitude
	PPM_OS::memcpy(&tm_Search_GIS.itllongitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Search_GIS.itllatitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Search_GIS.ibrlongitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Search_GIS.ibrlatitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);

	//iprojecttype
	PPM_OS::memcpy(&tm_Search_GIS.iprojecttype,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);

	//timetype
	PPM_OS::memcpy(&tm_Search_GIS.itimetype,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);

	//year
	PPM_OS::memcpy(&tm_Search_GIS.iyear,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);

	//batch
	PPM_OS::memcpy(&tm_Search_GIS.ibatch,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);

	tm_Search_GIS.ConvertToNetSeq();

	GetTimeType();
    
	return TRUE;
}

void CStatisticSock_GIS::GetTimeType(void)
{
	PPM_OS::memset(tm_Search_GIS.sztimetype, 0, sizeof(tm_Search_GIS.sztimetype));
	if (tm_Search_GIS.itimetype == 0)
	{
		PPM_OS::snprintf(tm_Search_GIS.sztimetype,sizeof(tm_Search_GIS.sztimetype),
			" and datepart(yy,dateadd(ss,istime,''1970-1-1 8:00:00'')) = %d ", tm_Search_GIS.iyear);	
	}
	else if (tm_Search_GIS.itimetype == 1)
	{
		PPM_OS::snprintf(tm_Search_GIS.sztimetype,sizeof(tm_Search_GIS.sztimetype),
			" and datepart(yy,dateadd(ss,istime,''1970-1-1 8:00:00'')) = %d  \
			and datepart(mm,dateadd(ss,istime,''1970-1-1 8:00:00'')) = %d ",
			tm_Search_GIS.iyear, tm_Search_GIS.ibatch);	
	}
	else if (tm_Search_GIS.itimetype == 2)
	{
		PPM_OS::snprintf(tm_Search_GIS.sztimetype,sizeof(tm_Search_GIS.sztimetype),
			" and iyear = %d ", tm_Search_GIS.iyear);	
	}
	else if (tm_Search_GIS.itimetype == 3)
	{
		PPM_OS::snprintf(tm_Search_GIS.sztimetype,sizeof(tm_Search_GIS.sztimetype),
			" and iyear  = %d and ibatch = %d ", tm_Search_GIS.iyear, tm_Search_GIS.ibatch);	
	}
	else
	{
		//do nothing...
	}
}

BOOL CStatisticSock_GIS::GetFileList(const BYTE* const pData, const int nCount,int& ioffset)
{
	char tm_sql[9000];
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec mc_sp_stat_gis_get %d,%d,%d,%d, %d,'%s'" ,
		tm_Search_GIS.itllongitude, 
		tm_Search_GIS.itllatitude,
		tm_Search_GIS.ibrlongitude,
		tm_Search_GIS.ibrlatitude,
		tm_Search_GIS.iprojecttype,
		tm_Search_GIS.sztimetype
		);

	m_map_filelist.clear();
	m_iRowType_ = TYPE_VEC_FILE_LIST;             //文件id获取 +　采样点数目获取
    SEARCHSQL(m_iRowType_, tm_sql);

	return TRUE;
}

void CStatisticSock_GIS::DealTestType_Data(int testtype, std::string strtb, char* tm_file)
{
	if (strtb.size() < 10)
	{
		(this->*pfunc_TestTypeData[testtype])(strtb, tm_file);
	}
	else
	{	   
		DealStatGis_Event(strtb, tm_file);
	}               
}

void CStatisticSock_GIS::DealTestType_Default(std::string strtb, char* tm_file)
{
}

void CStatisticSock_GIS::DealTestType_Gsm(std::string strtb, char* tm_file)
{
	char tm_sql[9000];
	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_stat_gis_get_1  %d,%d,%d,%d, '%s','%s'",
		tm_Search_GIS.itllongitude,
		tm_Search_GIS.itllatitude,
		tm_Search_GIS.ibrlongitude,
		tm_Search_GIS.ibrlatitude,
		strtb.c_str(),
		tm_file);

	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_STAT_GIS_V_GSM_IDLE;
	SEARCHSQL(m_iRowType_, tm_sql);

	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_stat_gis_get_2  %d,%d,%d,%d, '%s','%s'",
		tm_Search_GIS.itllongitude,
		tm_Search_GIS.itllatitude,
		tm_Search_GIS.ibrlongitude,
		tm_Search_GIS.ibrlatitude,
		strtb.c_str(),
		tm_file);

	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_STAT_GIS_V_GSM_DEDICATED;
	SEARCHSQL(m_iRowType_, tm_sql);

	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_stat_gis_get_3  %d,%d,%d,%d, '%s','%s'",
		tm_Search_GIS.itllongitude,
		tm_Search_GIS.itllatitude,
		tm_Search_GIS.ibrlongitude,
		tm_Search_GIS.ibrlatitude,
		strtb.c_str(),
		tm_file);

	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_STAT_GIS_D_GPRS;
	SEARCHSQL(m_iRowType_, tm_sql);
}
void CStatisticSock_GIS::DealTestType_Scan(std::string strtb, char* tm_file)
{
	char tm_sql[9000];
	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_stat_gis_get_5  %d,%d,%d,%d, '%s','%s'",
		tm_Search_GIS.itllongitude,
		tm_Search_GIS.itllatitude,
		tm_Search_GIS.ibrlongitude,
		tm_Search_GIS.ibrlatitude,
		strtb.c_str(),
		tm_file);

	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_STAT_GIS_SCAN;
    SEARCHSQL(m_iRowType_, tm_sql);
}
void CStatisticSock_GIS::DealTestType_Tdscdma(std::string strtb, char* tm_file)
{
	char tm_sql[9000];
	//TDSCDMA_AMR
	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_stat_gis_get_6  %d,%d,%d,%d, '%s','%s'",
		tm_Search_GIS.itllongitude,
		tm_Search_GIS.itllatitude,
		tm_Search_GIS.ibrlongitude,
		tm_Search_GIS.ibrlatitude,
		strtb.c_str(),
		tm_file);

	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_STAT_GIS_TDSCDMA_AMR;
	SEARCHSQL(m_iRowType_, tm_sql);

	//TDSCDMA_PS
	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_stat_gis_get_7  %d,%d,%d,%d, '%s','%s'",
		tm_Search_GIS.itllongitude,
		tm_Search_GIS.itllatitude,
		tm_Search_GIS.ibrlongitude,
		tm_Search_GIS.ibrlatitude,
		strtb.c_str(),
		tm_file);

	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_STAT_GIS_TDSCDMA_PS;
	SEARCHSQL(m_iRowType_, tm_sql);

	//TDSCDMA_VP
	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_stat_gis_get_8  %d,%d,%d,%d, '%s','%s'",
		tm_Search_GIS.itllongitude,
		tm_Search_GIS.itllatitude,
		tm_Search_GIS.ibrlongitude,
		tm_Search_GIS.ibrlatitude,
		strtb.c_str(),
		tm_file);

	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_STAT_GIS_TDSCDMA_VP;
    SEARCHSQL(m_iRowType_, tm_sql);
}
void CStatisticSock_GIS::DealStatGis_Event(std::string strtb, char* tm_file)
{
	char tm_sql[9000];
	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_stat_gis_get_4  %d,%d,%d,%d, '%s','%s'",
		tm_Search_GIS.itllongitude,
		tm_Search_GIS.itllatitude,
		tm_Search_GIS.ibrlongitude,
		tm_Search_GIS.ibrlatitude,
		strtb.c_str(),
		tm_file);

	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_STAT_GIS_EVENT;
    SEARCHSQL(m_iRowType_, tm_sql);
}
/************************************************************************
CStatisticSock_GISVS
************************************************************************/
BOOL  
CStatisticSock_GISVS::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
    if (!GetStatGIS(pData, nCount, ioffset))
    {
		return FALSE;
    }
    if (!GetFileList(pData, nCount, ioffset))
    {
		return FALSE;
    }
	
	if(0 >= m_map_filelist.size())	//no data
	{
		//处理结束
		FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
		return TRUE;
	}

    char tm_file[8000];
	int cutnum = 600;
	int bOnce = 0;
	auto p_map = m_map_filelist.begin();   

    for (; p_map != m_map_filelist.end(); p_map++)   
	{	 
	    auto p_vec = p_map->second.begin();	
		size_t allcount=1;	
		PPM_OS::memset(&tm_file, 0, sizeof(tm_file));	
		bOnce = 0;

		for (int count=1; p_vec != p_map->second.end(); p_vec++, ++count, ++allcount)   
        {   		
			if(0 == bOnce)
			{
			 	PPM_OS::snprintf(tm_file,sizeof(tm_file),"%d",*p_vec); 
				bOnce = 1;
			}
			else
			{
				PPM_OS::snprintf(tm_file,sizeof(tm_file),"%s,%d",tm_file,*p_vec); 
			}

            if(cutnum == count || allcount == p_map->second.size())
		    { 
                DealSearchGisStat(p_map->first, tm_file);
                PPM_OS::memset(tm_file, 0, sizeof(tm_file));
                count = 0;
                bOnce = 0;
		    }
        }
	}

    FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND); 
	return TRUE;
}

BOOL CStatisticSock_GISVS::GetStatGIS(const BYTE* const pData, const int nCount,int& ioffset)
{
	memset(&tm_Search_GIS, 0, sizeof(tm_Search_GIS));
	//longitude and latitude
	PPM_OS::memcpy(&tm_Search_GIS.itllongitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Search_GIS.itllatitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Search_GIS.ibrlongitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_Search_GIS.ibrlatitude,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);

	tm_Search_GIS.ConvertToNetSeq();
	return TRUE;
}
BOOL CStatisticSock_GISVS::GetFileList(const BYTE* const pData, const int nCount,int& ioffset)
{
	//starttime
	int istime;
	PPM_OS::memcpy(&istime,pData + ioffset,4);
	istime = MAKEINT_NETSEQ1(istime);
	MOVENEXT(ioffset,4,nCount);

	//endtime
	int ietime;
	PPM_OS::memcpy(&ietime,pData + ioffset,4);
	ietime = MAKEINT_NETSEQ1(ietime);
	MOVENEXT(ioffset,4,nCount);

	int icarriertype;
	PPM_OS::memcpy(&icarriertype,pData + ioffset,4);
	icarriertype = MAKEINT_NETSEQ1(icarriertype);
	MOVENEXT(ioffset,4,nCount);

	//get filelist
	char tm_sql[9000];
	PPM_OS::memset(&tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql), 
		"exec mc_sp_stat_gis_get_vs %d,%d,%d,%d, %d,%d ,%d" ,
		tm_Search_GIS.itllongitude, 
		tm_Search_GIS.itllatitude,
		tm_Search_GIS.ibrlongitude,
		tm_Search_GIS.ibrlatitude,
		istime,
		ietime,
		icarriertype);

	m_map_filelist.clear();
	m_iRowType_ = TYPE_VEC_FILE_LIST;             //文件id获取 +　采样点数目获取
    SEARCHSQL(m_iRowType_, tm_sql);

	return TRUE;
}

void CStatisticSock_GISVS::DealSearchGisStat(STRU_FILE_LIST stru_filelist, char* tm_file)
{
	if (stru_filelist.strtb.size() < 10)
	{
		//获取头文件信息
		SearchLogFileSInfo(stru_filelist.strlog.c_str(), stru_filelist.testtype, tm_file, 0);

		DealSearchGisGsm(stru_filelist.strtb, tm_file);
	}
	else
	{	   
		DealSearchGisEvent(stru_filelist.strtb, tm_file);
	}               
}

void CStatisticSock_GISVS::DealSearchGisGsm(std::string strtb, char* tm_file)
{
	char tm_sql[9000];
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_STAT_GIS_V_GSM_IDLE;
	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_stat_gis_get_1  %d,%d,%d,%d, '%s','%s'",
		tm_Search_GIS.itllongitude,
		tm_Search_GIS.itllatitude,
		tm_Search_GIS.ibrlongitude,
		tm_Search_GIS.ibrlatitude,
		strtb.c_str(),
		tm_file);

    SEARCHSQL(m_iRowType_, tm_sql);

	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_STAT_GIS_V_GSM_DEDICATED;
	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_stat_gis_get_2  %d,%d,%d,%d, '%s','%s'",
		tm_Search_GIS.itllongitude,
		tm_Search_GIS.itllatitude,
		tm_Search_GIS.ibrlongitude,
		tm_Search_GIS.ibrlatitude,
		strtb.c_str(),
		tm_file);

    SEARCHSQL(m_iRowType_, tm_sql);

	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_STAT_GIS_D_GPRS;
	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_stat_gis_get_3  %d,%d,%d,%d, '%s','%s'",
		tm_Search_GIS.itllongitude,
		tm_Search_GIS.itllatitude,
		tm_Search_GIS.ibrlongitude,
		tm_Search_GIS.ibrlatitude,
		strtb.c_str(),
		tm_file);

    SEARCHSQL(m_iRowType_, tm_sql);
}

void CStatisticSock_GISVS::DealSearchGisEvent(std::string strtb, char* tm_file)
{
	char tm_sql[9000];
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_STAT_GIS_EVENT;
	PPM_OS::memset(tm_sql,0,sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec mc_sp_stat_gis_get_4  %d,%d,%d,%d, '%s','%s'",
		tm_Search_GIS.itllongitude,
		tm_Search_GIS.itllatitude,
		tm_Search_GIS.ibrlongitude,
		tm_Search_GIS.ibrlatitude,
		strtb.c_str(),
		tm_file);

    SEARCHSQL(m_iRowType_, tm_sql);
}



