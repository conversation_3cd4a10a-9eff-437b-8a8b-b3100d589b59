// StatisticSock_Other.h: interface for the CStatisticSock_Other class.
//
//////////////////////////////////////////////////////////////////////

#ifndef __STATISTICSOCK_OTHER_H__
#define __STATISTICSOCK_OTHER_H__

#include "./SearchSock.h"
   
class CStatisticSock_GIS : public CSearchSock
{
public:
    CStatisticSock_GIS();
	virtual ~CStatisticSock_GIS();

	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);

private:
	BOOL GetStatGIS(const BYTE* const pData, const int nCount,int& ioffset);
	void GetTimeType(void);
	BOOL GetFileList(const BYTE* const pData, const int nCount,int& ioffset);
	void DealTestType_Data(int testtype, std::string strtb, char* tm_file);

	void (CStatisticSock_GIS::*pfunc_TestTypeData[256])(std::string strtb, char* tm_file);
	void DealTestType_Default(std::string strtb, char* tm_file);
	void DealTestType_Gsm(std::string strtb, char* tm_file);
  void DealTestType_Scan(std::string strtb, char* tm_file);
  void DealTestType_Tdscdma(std::string strtb, char* tm_file);
	void DealStatGis_Event(std::string strtb, char* tm_file);

private:
	STRU_STAT_GIS tm_Search_GIS;
};


class CStatisticSock_GISVS : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);

private:
  BOOL GetStatGIS(const BYTE* const pData, const int nCount,int& ioffset);
  BOOL GetFileList(const BYTE* const pData, const int nCount,int& ioffset);
	void DealSearchGisStat(STRU_FILE_LIST stru_filelist, char* tm_file);
  void DealSearchGisGsm(std::string strtb, char* tm_file);
	void DealSearchGisEvent(std::string strtb, char* tm_file);

private:
	STRU_STAT_GIS tm_Search_GIS;
};


#endif
