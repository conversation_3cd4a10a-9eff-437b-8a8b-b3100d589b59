// ComDef_Stat.h: interface for the CComDef_Stat class.
//
//////////////////////////////////////////////////////////////////////

#pragma once

#define RESTYPE_FILE_INFO                       0x01   //RESTYPE返回文件头信息
#define RESTYPE_FILE_INFO_MORE                  0x02   //RESTYPE返回文件头信息

#define REQTYPE_STAT_AREA_LOG					0x70	//REQUEST
#define RESTYPE_STAT_AREA_LOG_FILE				0x70
#define RESTYPE_STAT_AREA_LOG_PARA_1			0x71
#define RESTYPE_STAT_AREA_LOG_PARA_2			0x72
#define RESTYPE_STAT_AREA_LOG_PARA_3			0x73
#define RESTYPE_STAT_AREA_LOG_CDMA_PARA			0x74
#define RESTYPE_STAT_AREA_LOG_SCAN_PARA			0x75
#define RESTYPE_STAT_AREA_LOG_GSM_EVENT			0x76
#define RESTYPE_STAT_AREA_LOG_GPRS_EVENT		0x77
#define RESTYPE_STAT_AREA_LOG_TDSCDMA_AMR		0x78
#define RESTYPE_STAT_AREA_LOG_TDSCDMA_PS		0x79
#define RESTYPE_STAT_AREA_LOG_TDSCDMA_VP		0x7a

#define RESTYPE_STAT_TDSCDMA_EVENT              0x7b   

#define REQTYPE_STAT_AREA_LOG_KPI				0x71	//REQUEST
#define RESTYPE_STAT_AREA_LOG_KPI_FILE				0x90
#define RESTYPE_STAT_AREA_LOG_KPI_PARA_1			0x91
#define RESTYPE_STAT_AREA_LOG_KPI_PARA_2			0x92
#define RESTYPE_STAT_AREA_LOG_KPI_PARA_3			0x93
#define RESTYPE_STAT_AREA_LOG_KPI_CDMA_PARA			0x94
#define RESTYPE_STAT_AREA_LOG_KPI_SCAN_PARA			0x95
#define RESTYPE_STAT_AREA_LOG_KPI_EVENT				0x96
#define RESTYPE_STAT_AREA_LOG_KPI_TDSCDMA_AMR		0x97
#define RESTYPE_STAT_AREA_LOG_KPI_TDSCDMA_PS		0x98
#define RESTYPE_STAT_AREA_LOG_KPI_TDSCDMA_VP		0x99
#define RESTYPE_STAT_AREA_LOG_KPI_WCDMA_AMR		    0x9a
#define RESTYPE_STAT_AREA_LOG_KPI_WCDMA_PS	    	0x9b
#define RESTYPE_STAT_AREA_LOG_KPI_WCDMA_VP		    0x9c
#define RESTYPE_STAT_AREA_LOG_KPI_WCDMA_PSHS		0x9d

#define REQTYPE_STAT_LOG_KPI					0x80	//REQUEST
#define RESTYPE_STAT_LOG_KPI_FILE				0x80
#define RESTYPE_STAT_LOG_KPI_PARA_1				0x81
#define RESTYPE_STAT_LOG_KPI_PARA_2				0x82
#define RESTYPE_STAT_LOG_KPI_PARA_3				0x83
#define RESTYPE_STAT_LOG_KPI_CDMA_PARA			0x84
#define RESTYPE_STAT_LOG_KPI_SCAN_PARA			0x85
#define RESTYPE_STAT_LOG_KPI_EVENT				0x86
#define RESTYPE_STAT_LOG_KPI_TDSCDMA_AMR		0x87
#define RESTYPE_STAT_LOG_KPI_TDSCDMA_PS	    	0x88
#define RESTYPE_STAT_LOG_KPI_TDSCDMA_VP	    	0x89
#define RESTYPE_STAT_LOG_KPI_WCDMA_AMR	    	0x8a
#define RESTYPE_STAT_LOG_KPI_WCDMA_PS	    	0x8b
#define RESTYPE_STAT_LOG_KPI_WCDMA_VP	    	0x8c
#define RESTYPE_STAT_LOG_KPI_WCDMA_PSHS         0x8d
#define RESTYPE_STAT_LOG_KPI_CDMA_DATA          0x8e
#define RESTYPE_STAT_LOG_KPI_CDMA2000_DATA      0x8f

#define RESTYPE_STATI_LOG_KPI_WCDMA_AMR	    	0x10//
#define RESTYPE_STATI_LOG_KPI_WCDMA_VP	    	0x11//
#define RESTYPE_STATI_LOG_KPI_CDMA_V	    	0x12//
#define RESTYPE_STATI_LOG_KPI_TDSCDMA_AMR       0x13//
#define RESTYPE_STATI_LOG_KPI_TDSCDMA_VP        0x14//
#define RESTYPE_STATI_LOG_KPI_WCDMA_PS	    	0x15//
#define RESTYPE_STATI_LOG_KPI_WCDMA_PSHS	    0x16//
#define RESTYPE_STATI_LOG_KPI_TDSCDMA_PS 	    0x17//
#define RESTYPE_STATI_LOG_KPI_GSM_VOICE	        0x18
#define RESTYPE_STATI_LOG_KPI_GSM_DATA 	        0x19
#define RESTYPE_STATI_LOG_KPI_GSM_UPLINK 	    0x1a
#define RESTYPE_STATI_LOG_KPI_CDMA_D	    	0x1b//

#define REQTYPE_STAT_AREA_KPI					0x90	//REQUEST
#define RESTYPE_STAT_AREA_KPI_FILE				0x90
#define RESTYPE_STAT_AREA_KPI_PARA_1			0x91
#define RESTYPE_STAT_AREA_KPI_PARA_2			0x92
#define RESTYPE_STAT_AREA_KPI_PARA_3			0x93
#define RESTYPE_STAT_AREA_KPI_CDMA_PARA			0x94
#define RESTYPE_STAT_AREA_KPI_SCAN_PARA			0x95
#define RESTYPE_STAT_AREA_KPI_EVENT				0x96
#define RESTYPE_STAT_AREA_KPI_TDSCDMA_AMR		0x97
#define RESTYPE_STAT_AREA_KPI_TDSCDMA_PS		0x98
#define RESTYPE_STAT_AREA_KPI_TDSCDMA_VP		0x99
#define RESTYPE_STAT_AREA_KPI_WCDMA_AMR		    0x9a
#define RESTYPE_STAT_AREA_KPI_WCDMA_PS	    	0x9b
#define RESTYPE_STAT_AREA_KPI_WCDMA_VP		    0x9c
#define RESTYPE_STAT_AREA_KPI_WCDMA_PSHS		0x9d
#define RESTYPE_STAT_AREA_KPI_CDMA_DATA			0x9e
#define RESTYPE_STAT_AREA_KPI_CDMA2000_DATA		0x9f

#define RESTYPE_STATI_AREA_KPI_WCDMA_AMR		0x10//
#define RESTYPE_STATI_AREA_KPI_WCDMA_VP		    0x11//
#define RESTYPE_STATI_AREA_KPI_CDMA_V	    	0x12//
#define RESTYPE_STATI_AREA_KPI_WCDMA_PS	        0x13//
#define RESTYPE_STATI_AREA_KPI_WCDMA_PSHS	    0x14//
#define RESTYPE_STATI_AREA_KPI_TDSCDMA_PS	    0x15//
#define RESTYPE_STATI_AREA_KPI_TDSCDMA_AMR	    0x16//
#define RESTYPE_STATI_AREA_KPI_TDSCDMA_VP	    0x17//
#define RESTYPE_STATI_AREA_KPI_GSM_VOICE	    0x18
#define RESTYPE_STATI_AREA_KPI_GSM_DATA 	    0x19
#define RESTYPE_STATI_AREA_KPI_GSM_UPLINK 	    0x1a

#define REQTYPE_STAT_CELL_KPI					0xa0	//REQUEST
#define RESTYPE_STAT_CELL_KPI_FILE				0xa0
#define RESTYPE_STAT_CELL_KPI_PARA_1			0xa1
#define RESTYPE_STAT_CELL_KPI_PARA_2			0xa2
#define RESTYPE_STAT_CELL_KPI_PARA_3			0xa3
#define RESTYPE_STAT_CELL_KPI_CDMA_PARA			0xa4
#define RESTYPE_STAT_CELL_KPI_SCAN_PARA			0xa5
#define RESTYPE_STAT_CELL_KPI_EVENT				0xa6
#define RESTYPE_STAT_CELL_KPI_TDSCDMA_AMR		0xa7
#define RESTYPE_STAT_CELL_KPI_TDSCDMA_PS		0xa8
#define RESTYPE_STAT_CELL_KPI_TDSCDMA_VP		0xa9

#define REQTYPE_STAT_AREA_CELL_KPI			    0xa1	//REQUEST
#define RESTYPE_STAT_CELL_KPI_FILE				0xa0
#define RESTYPE_STAT_CELL_KPI_PARA_1			0xa1
#define RESTYPE_STAT_CELL_KPI_PARA_2			0xa2
#define RESTYPE_STAT_CELL_KPI_PARA_3			0xa3
#define RESTYPE_STAT_CELL_KPI_CDMA_PARA			0xa4
#define RESTYPE_STAT_CELL_KPI_SCAN_PARA			0xa5
#define RESTYPE_STAT_CELL_KPI_EVENT				0xa6
#define RESTYPE_STAT_CELL_KPI_TDSCDMA_AMR		0xa7
#define RESTYPE_STAT_CELL_KPI_TDSCDMA_PS		0xa8
#define RESTYPE_STAT_CELL_KPI_TDSCDMA_VP		0xa9

#define RESTYPE_STATI_CELL_KPI_WCDMA_AMR		0xaa
#define RESTYPE_STATI_CELL_KPI_WCDMA_VP	    	0xab
#define RESTYPE_STAT_CELL_KPI_WCDMA_PS	        0xac
#define RESTYPE_STAT_CELL_KPI_WCDMA_PSHS		0xad


#define	REQTYPE_STAT_NOGIS						0xb0	//非GIS统计
#define	RESTYPE_STAT_NOGIS_V_GSM_IDLE			0xb0
#define	RESTYPE_STAT_NOGIS_V_GSM_DEDICATED		0xb1
#define	RESTYPE_STAT_NOGIS_D_GPRS				0xb2
#define	RESTYPE_STAT_NOGIS_V_GSM_EVENT			0xb3
#define	RESTYPE_STAT_NOGIS_D_GPRS_EVENT			0xb4
#define	RESTYPE_STAT_NOGIS_SCAN					0xb5
#define RESTYPE_STAT_NOGIS_TDSCDMA_AMR          0xb6
#define RESTYPE_STAT_NOGIS_TDSCDMA_PS           0xb7
#define RESTYPE_STAT_NOGIS_TDSCDMA_VP           0xb8
#define RESTYPE_STAT_NOGIS_TDSCDMA_EVENT        0xb9

#define	REQTYPE_STAT_GIS						0xc0	//GIS统计
#define	RESTYPE_STAT_GIS_V_GSM_IDLE				0xc0
#define	RESTYPE_STAT_GIS_V_GSM_DEDICATED		0xc1
#define	RESTYPE_STAT_GIS_D_GPRS					0xc2
#define	RESTYPE_STAT_GIS_EVENT					0xc3
#define	RESTYPE_STAT_GIS_SCAN					0xc4
#define RESTYPE_STAT_GIS_TDSCDMA_AMR            0xc5
#define RESTYPE_STAT_GIS_TDSCDMA_PS             0xc6
#define RESTYPE_STAT_GIS_TDSCDMA_VP             0xc7
#define	REQTYPE_STAT_GIS_VS                     0xc1   //REQUEST

//add by wj，对AreaStat处理后的数据进行查询，用于系统的报表查询
#define REQTYPE_STAT_AREA_STAT_OLD			    0xe0
#define RESTYPE_STAT_AREA_STAT					0xe0
#define	RESTYPE_STAT_AREA_STAT_EVENT			0xe1

#define REQTYPE_STAT_AREA_STAT					0xe1
#define RESTYPE_STAT_AREA_STAT					0xe0
#define	RESTYPE_STAT_AREA_STAT_EVENT			0xe1

//add by wj，对工作量进行统计
#define REQTYPE_STAT_WORK_KPI					0xf0

//new 

#define REQTYPE_STAT_LOG_KPI_LN					0x81	//REQUEST
#define RESTYPE_STAT_LOG_KPI_LN  				0x81
#define RESTYPE_STAT_LOG_KPI_LN_TD_AMR          0x82  
#define RESTYPE_STAT_LOG_KPI_LN_TD_PS           0x83  
#define RESTYPE_STAT_LOG_KPI_LN_GSMV            0x84  
#define RESTYPE_STAT_LOG_KPI_LN_GSMD            0x85 

#define RESTYPE_STATI_CELL_KPI_WCDMA_AMR		0xaa
#define RESTYPE_STATI_CELL_KPI_WCDMA_VP	    	0xab
#define RESTYPE_STAT_CELL_KPI_WCDMA_PS	        0xac
#define RESTYPE_STAT_CELL_KPI_WCDMA_PSHS		0xad



