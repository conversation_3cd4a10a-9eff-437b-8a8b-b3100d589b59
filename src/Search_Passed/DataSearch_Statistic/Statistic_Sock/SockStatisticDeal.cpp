// SockStatisticDeal.cpp: implementation of the CSockStatisticDeal class.
//
//////////////////////////////////////////////////////////////////////

#include "./SockStatisticDeal.h"

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CSockStatisticDeal::CSockStatisticDeal()
:m_mapReqTypePos()
{
	InitReqTypeMap();
}

CSockStatisticDeal::~CSockStatisticDeal()
{
	m_mapReqTypePos.clear();
}

BOOL
CSockStatisticDeal::DealSock(const BYTE* const pData, const int nCount,int& ioffset, const STRU_SockInfo& sockInfo)
{
	char tm_addr[512];
	sockInfo.pServerDeal_->m_ptcpclient_->GetInetAddr()->addr_to_string(tm_addr, sizeof(tm_addr), 1);
	std::string strUserInfo = "[" + std::string(tm_addr) + "]";

	if(CMD2_REQUEST == pData[ioffset])//请求
	{
		MOVENEXT(ioffset,1,nCount)

		auto it = m_mapReqTypePos.find(pData[ioffset++]);

		if (it != m_mapReqTypePos.end())
		{
			(this->*(it->second))(pData, nCount, ioffset, sockInfo, strUserInfo);
		}
		else
		{
			PPM_DEBUG((LM_ERROR, "unknown search request type:%d\n", pData[ioffset - 1]));
			return FALSE;
		}
	}
	else //响应 
	{
		PPM_DEBUG((LM_ERROR,"should not be statistic data management response:%d\n",pData[ioffset+1]));
		return FALSE;
	}
	return TRUE;
}

void 
CSockStatisticDeal::
Deal_Reqtype_Stat_Area_Log(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo)
{
	DEALDATA2(CStatisticSock_AreaLog, strUserInfo);
}
void 
CSockStatisticDeal::
Deal_Reqtype_Stat_Area_Log_Kpi(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo)
{
	DEALDATA2(CStatisticSock_AreaLogKpi, strUserInfo);
}
void 
CSockStatisticDeal::
Deal_Reqtype_Stat_Area_Kpi(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo)
{
	DEALDATA2(CStatisticSock_AreaKPI, strUserInfo);
}
void 
CSockStatisticDeal::
Deal_Reqtype_Stat_Area_Cell_Kpi(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo)
{
	DEALDATA2(CStatisticSock_AreaCellKPI, strUserInfo);
}
void 
CSockStatisticDeal::
Deal_Reqtype_Stat_Area_Stat_Old(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo)
{
	DEALDATA2(CStatisticSock_AreaStatOld, strUserInfo);
}
void 
CSockStatisticDeal::
Deal_Reqtype_Stat_Area_Stat(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo)
{
	DEALDATA2(CStatisticSock_AreaStat, strUserInfo);
}
void 
CSockStatisticDeal::
Deal_Reqtype_Stat_Log_Kpi(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo)
{
	DEALDATA2(CStatisticSock_LogKPI, strUserInfo);
}
void 
CSockStatisticDeal::
Deal_Reqtype_Stat_Log_Kpi_LN(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo)
{
	DEALDATA2(CStatisticSock_LogKPILN, strUserInfo);
}
void 
CSockStatisticDeal::
Deal_Reqtype_Stat_Cell_Kpi(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo)
{
	DEALDATA2(CStatisticSock_CellKPI, strUserInfo);
}
void 
CSockStatisticDeal::
Deal_Reqtype_Stat_Gis(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo)
{
	DEALDATA2(CStatisticSock_GIS, strUserInfo);
}
void 
CSockStatisticDeal::
Deal_Reqtype_Stat_Gis_Vs(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo)
{
	DEALDATA2(CStatisticSock_GISVS, strUserInfo);
}
void 
CSockStatisticDeal::
Deal_Reqtype_Stat_Work_Kpi(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo)
{
	DEALDATA2(CStatisticSock_WorkKPI, strUserInfo);
}

void CSockStatisticDeal::InitReqTypeMap(void)
{
	m_mapReqTypePos.insert(make_pair(REQTYPE_STAT_AREA_LOG, &CSockStatisticDeal::Deal_Reqtype_Stat_Area_Log));
	m_mapReqTypePos.insert(make_pair(REQTYPE_STAT_AREA_LOG_KPI, &CSockStatisticDeal::Deal_Reqtype_Stat_Area_Log_Kpi));
	m_mapReqTypePos.insert(make_pair(REQTYPE_STAT_AREA_KPI, &CSockStatisticDeal::Deal_Reqtype_Stat_Area_Kpi));
	m_mapReqTypePos.insert(make_pair(REQTYPE_STAT_AREA_CELL_KPI, &CSockStatisticDeal::Deal_Reqtype_Stat_Area_Cell_Kpi));
	m_mapReqTypePos.insert(make_pair(REQTYPE_STAT_AREA_STAT_OLD, &CSockStatisticDeal::Deal_Reqtype_Stat_Area_Stat_Old));
	m_mapReqTypePos.insert(make_pair(REQTYPE_STAT_AREA_STAT, &CSockStatisticDeal::Deal_Reqtype_Stat_Area_Stat));
	m_mapReqTypePos.insert(make_pair(REQTYPE_STAT_LOG_KPI, &CSockStatisticDeal::Deal_Reqtype_Stat_Log_Kpi));
	m_mapReqTypePos.insert(make_pair(REQTYPE_STAT_LOG_KPI_LN, &CSockStatisticDeal::Deal_Reqtype_Stat_Log_Kpi_LN));
	m_mapReqTypePos.insert(make_pair(REQTYPE_STAT_CELL_KPI, &CSockStatisticDeal::Deal_Reqtype_Stat_Cell_Kpi));
	m_mapReqTypePos.insert(make_pair(REQTYPE_STAT_GIS, &CSockStatisticDeal::Deal_Reqtype_Stat_Gis));
	m_mapReqTypePos.insert(make_pair(REQTYPE_STAT_GIS_VS, &CSockStatisticDeal::Deal_Reqtype_Stat_Gis_Vs));
	m_mapReqTypePos.insert(make_pair(REQTYPE_STAT_WORK_KPI, &CSockStatisticDeal::Deal_Reqtype_Stat_Work_Kpi));

}














