// SockStatisticDeal.h: interface for the CSockStatisticDeal class.
//
//////////////////////////////////////////////////////////////////////

#ifndef __SOCKSTATISTICDEAL_H__
#define __SOCKSTATISTICDEAL_H__

#include "./SockFactory.h"

#include "./StatisticSock_Area.h"
#include "./StatisticSock_Cell.h"
#include "./StatisticSock_Log.h"
#include "./StatisticSock_Other.h"

class CSockStatisticDeal : public CSockFactory, public Singleton<CSockStatisticDeal>
{
public:
	CSockStatisticDeal();
	virtual ~CSockStatisticDeal();
	
public:
    virtual BOOL DealSock(const BYTE* const pData, const int nCount,int& ioffset, const STRU_SockInfo& sockInfo);
	
private:
	typedef void (CSockStatisticDeal::*DealReqTypeData)(const BYTE* const pData, const int nCount, int& ioffset,
                                                        const STRU_SockInfo& sockInfo, std::string strUserInfo);
	std::map<BYTE, DealReqTypeData> m_mapReqTypePos;

    void Deal_Reqtype_Stat_Area_Log(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo);
    void Deal_Reqtype_Stat_Area_Log_Kpi(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo);
    void Deal_Reqtype_Stat_Area_Kpi(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo);
    void Deal_Reqtype_Stat_Area_Cell_Kpi(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo);
    void Deal_Reqtype_Stat_Area_Stat_Old(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo);
    void Deal_Reqtype_Stat_Area_Stat(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo);
    void Deal_Reqtype_Stat_Log_Kpi(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo);
    void Deal_Reqtype_Stat_Log_Kpi_LN(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo);
    void Deal_Reqtype_Stat_Cell_Kpi(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo);
    void Deal_Reqtype_Stat_Gis(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo);
    void Deal_Reqtype_Stat_Gis_Vs(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo);
    void Deal_Reqtype_Stat_Work_Kpi(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo);

	void InitReqTypeMap(void);
};


#endif
