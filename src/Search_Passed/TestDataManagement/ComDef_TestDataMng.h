// ComDef_TestDataMng.h: interface for the CComDef_TestDataMng class.
//
//////////////////////////////////////////////////////////////////////

#ifndef __COMDEF_TESTDATAMNG_H__
#define __COMDEF_TESTDATAMNG_H__

//测试文件下载
#define REQTYPE_FILEDELETE						0x0A	//REQUEST	//文件删除请求
#define RESTYPE_FILEDELETE						0x06				//文件删除响应

#define REQTYPE_FILEDOWNLOAD					0x0B	//REQUEST	//文件下载请求
#define REQTYPE_FILEDOWNLOADByProject           0x0E    //REQUEST	//文件下载请求
#define REQTYPE_FILEDOWNLOADByPath				0xAD	//REQUEST	//文件下载请求
#define RESTYPE_FILEDOWNLOAD					0x07				//文件下载响应

#define REQTYPE_DOCDOWNLOAD_DIY					0x0C	//REQUEST	//自定义下载(相对路径+文件名)
#define REQTYPE_FILEDISTRIBUTE					0x0F	//REQUEST	//分发文件

#define REQTYPE_DOCDOWNLOAD                     0x0D    //REQUEST    //文本（测试报告）文件下载

#define REQTYPE_DBCONVERT						0x0C	//数据转换
#define RESTYPE_DBCONVERT						0x08

#define REQTYPE_DOCUPLOAD                       0x10    //上传命令字
#define RESTYPE_DOCUPLOAD_SUCCESS               0x10    //上传完毕命令字
#define RESTYPE_DOCUPLOAD_ERROR                 0x11    //上传完毕命令字

#define RESTYPE_FILEDISTRIBUTE_SUCCESS			0x12	//分发成功命令字
#define RESTYPE_FILEDISTRIBUTE_FAIL				0x13	//分发失败命令字

#define REQTYPE_SENDFILE_BLACKBLOCK_BEGIN       0xa1    //REQUEST     //黑点解决方案上传
#define REQTYPE_SENDFILE_BLACKBLOCK             0xa2    //REQUEST
#define REQTYPE_SENDFILE_BLACKBLOCK_END         0xa3    //REQUEST
#define REQTYPE_FILEDOWNLOAD_BLACKBLOCK         0xa4     //黑点解决方案下载

#define REQTYPE_FILEUPLOAD_BLACKBLOCK_DELETE    0xa5    //REQUEST
#define RESTYPE_CHECK_FILEDOWNLOAD_BLACKBLOCK   0xa6    //REQUEST

#define REQTYPE_FILEUPLOAD_BLACKBLOCK           0xa7    //REQUEST

#define REQTYPE_FILEUPLOAD_BLACKBLOCK_UPDATE    0xa8    //REQUEST

#define REQTYPE_FILEDOWNLOAD_CQTImage           0xa9    //CQTImage下载

#define REQTYPE_FILEUPLOAD_XLSX                 0xaa    //REQUEST     //XLSX上传

#define REQTYPE_MCFILETIME						0xab	//MC文件最近修改时间
#define RESTYPE_MCFILETIME						0xab

#define REQTYPE_MCCFILE							0xac	//MCC文件下载
#define RESTYPE_MCCFILE							0xac


//文件传输类型
#define FILETRANSFER_BEGIN 0//文件开始，后面会带有50个BYTES的文件名称
#define FILETRANSFER_CONTINUE 1//文件中间
#define FILETRANSFER_END 2//文件结束
#define FILETRANSFER_ERROR 0xff//文件结束

#define FILETRANSFER_LENTH 3 //文件大小

//CQT_Picture_Operate
#define SQLOPERATOR_INSERT 1
#define SQLOPERATOR_UPDATEWITHIMG 2
#define SQLOPERATOR_UPDATEWITHOUTIMG 3
#define SQLOPERATOR_DELETE 4

#endif
