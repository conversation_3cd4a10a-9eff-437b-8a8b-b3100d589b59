// TestDataMngSock_Other.cpp: implementation of the CTestDataMngSock_Other class.
//
//////////////////////////////////////////////////////////////////////

#include "TestDataMngSock_Other.h"
#include "./DtSockServer.h"
#include "./DtdrvApp_Public_Var.h"
#include "./ConfigSetting.h"
#include "./StdCommond.h"

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

/************************************************************************
Comm_DownLoad                                                      
************************************************************************/
BOOL 
Comm_DownLoad::RowResultDeal(int nQType)
{
	BOOL res = TRUE;
	//获取文件路径
	if(NULL != m_pServerDeal_){
		if(m_vecColumn.size() <= 0){
        return res;
		}

		std::string fname((char*)(m_vecColumn[0].pColValue));

		int ftype = *(int*)(m_vecColumn[1].pColValue);

		res = SendFileToClient(fname, ftype);
		if(FALSE == res){
			BYTE tm_buf[10];
			tm_buf[0] = RESTYPE_FILEDOWNLOAD;
			tm_buf[1] = CMD2_RESPONSE;
			tm_buf[2] = FILETRANSFER_ERROR;
			m_pServerDeal_->SendData(tm_buf,3);
		}
	}
	return res;
}


BOOL
Comm_DownLoad::IsZipRaRFileExist(std::string& strfilename)
{
    CStdFile file;

	std::string filename_backup = strfilename;

	if (strfilename.length() > 4 && 
		(strfilename.substr(strfilename.size()-4, 4) == ".zip" ||
		strfilename.substr(strfilename.size()-4, 4) == ".ZIP" ||
		strfilename.substr(strfilename.size()-4, 4) == ".rar" ||
		strfilename.substr(strfilename.size()-4, 4) == ".RAR"))
	{
		std::string outputstr = "尝试查找文件： " + strfilename + " \n";
		PPM_APPLOG((LM_ERROR,  outputstr.c_str() ));

		if (TRUE == file.IsExist(strfilename.c_str()))
		{
			return TRUE;
		}
		else 
		{
			return FALSE;
		}
	}
	else 
	{
		std::string outputstr = "尝试查找文件： " + strfilename + " \n";
		PPM_APPLOG((LM_ERROR,  outputstr.c_str() ));

        if (TRUE == file.IsExist(strfilename.c_str()))
        {
			return TRUE;
        }

        strfilename += ".zip";
        if (TRUE == file.IsExist(strfilename.c_str()))
        {
			return TRUE;
        }
		else 
		{
            strfilename = filename_backup;
		}

		strfilename += ".rar";
		if (TRUE == file.IsExist(strfilename.c_str()))
		{
			return TRUE;
		}
		else 
		{
			strfilename = filename_backup;
			return FALSE;
		}
	}
    return FALSE;
}

BOOL 
Comm_DownLoad::CheckFileName1(std::string& strfilename)
{
	//检测扩展名后的扩展端口
	int nLen = strfilename.length();

	if (strfilename.find_last_of(".") != std::string::npos)
	{
		std::string strDotPort = strfilename.substr(strfilename.find_last_of("."), nLen);
		if (strDotPort.find("(") != std::string::npos
			&& strDotPort.find(")") != std::string::npos)
		{
			// *.mtf(02)
			strDotPort = strDotPort.substr(strDotPort.find("("));
			nLen -= strDotPort.length();
		}
		if (nLen > 0)
		{
			strfilename = strfilename.substr(0, nLen);
		}
	}
	else 
	{
		//没有扩展名
		std::string strTemp;
		if (strfilename.find_last_of("(") != std::string::npos
			&& strfilename.find_last_of(")") != std::string::npos)
		{
			// mtf(02)
			strTemp = strfilename.substr(strfilename.find_last_of("("));
			nLen -= strTemp.length();
		}
		if (nLen > 0)
		{
			strfilename = strfilename.substr(0, nLen);
		}
	}

    if (IsZipRaRFileExist(strfilename))
    {
		return TRUE;
    }
	return FALSE;
}

BOOL 
Comm_DownLoad::CheckFileName2(std::string& strfilename)
{
	//检测扩展名前的扩展端口
	int step = 1;
	std::string testfilename = strfilename;
	BOOL bFindFile = FALSE;
	while(step <= 3)
	{
		if (TRUE == IsZipRaRFileExist(testfilename))
		{
			bFindFile = TRUE;
			break;
		}

		//输出sql语句
		OUTPUT_ERR(std::string("下载检测过程中，找不到文件： "  + testfilename).c_str());

		int dianPos = testfilename.find_last_of(".");
		if (-1 == dianPos)
		{
			bFindFile = FALSE;
			break;
		}

		testfilename = testfilename.substr(0, dianPos-0-1) + testfilename.substr(dianPos, testfilename.length()-dianPos+1);

		step++;
	}
	if (TRUE == bFindFile)
	{
		strfilename = testfilename;
	}
	
	return bFindFile;
}

BOOL
Comm_DownLoad::CheckFile(std::string& strfilename)
{
	if (CheckFileName1(strfilename))
	{
		return TRUE;
	}
	else if (CheckFileName2(strfilename))
	{
		return TRUE;
	}
	else 
	{
		return FALSE;
	}
    return TRUE;
}

//发送指定的文件给客户端
BOOL 
Comm_DownLoad::SendFileToClient(std::string strfilename, int fileType)
{
	CStdFile tm_file;

	if(NULL == m_pServerDeal_)
	{
		PPM_DEBUG((LM_ERROR,"m_pServerDeal_ is null!%l\n"));
		return FALSE;
	}


	//尝试去获取本地文件，如果不能就去远程访问文件
	if (CheckFile((strfilename)))
	{
	}
	else if (!CheckRemoteFile(strfilename))
	{
		return FALSE;
	}

	if(!tm_file.Open(strfilename.c_str(),"rb"))
	{
		PPM_DEBUG((LM_ERROR,"cannot open file %s,%m\n",strfilename.c_str()));
		tm_file.Close();	
		return FALSE;
	}
	
  PPM_OS::memset(m_pSendBuf_,0,SEND_BUF_MAXSIZE);

	int tm_ioffset = 0;
	int tm_pos1,tm_pos2;
	int tm_sendfileno;
	tm_pos1 = strfilename.rfind("/");
	tm_pos2 = strfilename.rfind("\\");
	tm_ioffset = (tm_pos1 > tm_pos2 ? tm_pos1 : tm_pos2);
	char tm_filename[255];
	PPM_OS::snprintf(tm_filename,sizeof(tm_filename),"%s",strfilename.substr(tm_ioffset + 1,strfilename.size()).c_str());
	tm_ioffset = 0;

	std::string gbk_tm_filename = CUtility::Utf8ToGBK(tm_filename);

	WORD tm_filenamelen = gbk_tm_filename.length();
	tm_filenamelen = MAKEWORD_NETSEQ1(tm_filenamelen);

	m_pSendBuf_[tm_ioffset++] = RESTYPE_FILEDOWNLOAD; //下载响应
	m_pSendBuf_[tm_ioffset++] = CMD2_RESPONSE; //响应类型
	m_pSendBuf_[tm_ioffset++] = FILETRANSFER_BEGIN;//文件传输类型
	PPM_OS::memcpy(m_pSendBuf_ + tm_ioffset,&tm_filenamelen,2); //文件名称长度
	tm_ioffset += 2;
	tm_filenamelen = MAKEWORD_NETSEQ1(tm_filenamelen);
	PPM_OS::memcpy(m_pSendBuf_ + tm_ioffset, gbk_tm_filename.c_str(), tm_filenamelen); //文件名称
	tm_ioffset += tm_filenamelen;	
	tm_pos1 = 1;
	tm_sendfileno = MAKEINT_NETSEQ1(tm_pos1);
	
	PPM_OS::memcpy(m_pSendBuf_ + tm_ioffset,&tm_sendfileno,4); //文件体包号
	tm_file.SeekToBegin();
	int ireadlen = SEND_BUF_MAXSIZE - tm_ioffset - 100;
	tm_pos2 = tm_file.Read(m_pSendBuf_ + tm_ioffset + 8,ireadlen); //文件体
	int isendlen = tm_ioffset + 8 + tm_pos2;
	tm_pos2 = MAKEINT_NETSEQ1(tm_pos2);
	PPM_OS::memcpy(m_pSendBuf_ + tm_ioffset + 4, &tm_pos2, 4);//文件体长度
 	m_pServerDeal_->SendData(m_pSendBuf_,isendlen);


	if(tm_file.IsEof())
	{
		//发送传输文件结束
		m_pSendBuf_[2] = FILETRANSFER_END;
		PPM_OS::memset(m_pSendBuf_ + tm_ioffset,0,8);
 		m_pServerDeal_->SendData(m_pSendBuf_,tm_ioffset + 8);
	}
	else{
		m_pSendBuf_[2] = FILETRANSFER_CONTINUE;
		while(!tm_file.IsEof())
		{	
			tm_pos1++;
			tm_sendfileno = MAKEINT_NETSEQ1(tm_pos1);
			PPM_OS::memcpy(m_pSendBuf_ + tm_ioffset,&tm_sendfileno,4); //文件体包号
			tm_pos2 = tm_file.Read(m_pSendBuf_ + tm_ioffset + 8,ireadlen); //文件体
			isendlen = tm_ioffset + 8 + tm_pos2;
			tm_pos2 = MAKEINT_NETSEQ1(tm_pos2);
			PPM_OS::memcpy(m_pSendBuf_ + tm_ioffset + 4,&tm_pos2,4) ;//文件体长度
			if(tm_file.IsEof()){
				m_pSendBuf_[2] = FILETRANSFER_END;
			}
 			m_pServerDeal_->SendData(m_pSendBuf_,isendlen);	
		}
	}

	tm_file.Close();
	return TRUE;
}

BOOL Comm_DownLoad::CheckRemoteFile(std::string& strfilename)
{
	BOOL bFindFile = FALSE;
	for (size_t i = 0; i < CConfigSetting::Instance()->m_vec_FileDownLoadPath.size();++i)
	{
		std::string filepath = CConfigSetting::Instance()->m_vec_FileDownLoadPath[i].GetFileRemotePath(strfilename);
		if (filepath.length() == 0)
		{
			continue;
		}

		std::string outputstr = "尝试查找远程文件： " + filepath + " \n";
		PPM_APPLOG((LM_ERROR,  outputstr.c_str() ));

		if (CheckFile(filepath))
		{
			strfilename = filepath;
			bFindFile = TRUE;
			break;
		}
	}
	if (!bFindFile)
	{
		std::string outputstr = "找不到下载文件，文件名称为： " + strfilename + " \n";
		PPM_APPLOG((LM_ERROR,  outputstr.c_str() ));
		return FALSE;
	}
    return TRUE;
}
/************************************************************************
TestDataMngSock_FileDownLoad                                                            
************************************************************************/
/**
文件下载处理,所传输过来的信息为：文件ID,测试类型
*/
BOOL   
TestDataMngSock_FileDownLoad::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	//获取文件ID信息
	int tm_fileid;
	MOVENEXTTEST(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_fileid,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	tm_fileid = MAKEINT_NETSEQ1(tm_fileid);

	int tm_testtype;
	MOVENEXTTEST(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_testtype,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	tm_testtype = MAKEINT_NETSEQ1(tm_testtype);

	char tm_logtbname[255];
	PPM_OS::memset(&tm_logtbname, 0, sizeof(tm_logtbname));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_logtbname,sizeof(tm_logtbname)));

	std::string unix_tm_logtbname = CUtility::GBKToUtf8(tm_logtbname);

	//构造查询语句
	//首先查询信息在那些表中
	char tm_sql[255];
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),"exec mc_sp_log_getfilepath %d,%d, '%s'",
		tm_fileid,tm_testtype,unix_tm_logtbname.c_str()
		);
	
	m_iRowType_ = TYPE_VEC_FILEDOWNLOAD;

	if(NULL != m_pDataBaseDeal_){
		int ret = m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
		if(0 > ret){
			Clear();
			return FALSE;
		}
	}
	Clear();
	return TRUE;
}



/************************************************************************
TestDataMngSock_FileDownLoadByProjct                                                            
************************************************************************/
/**
文件下载处理,所传输过来的信息为：文件ID,项目类型
*/
BOOL   
TestDataMngSock_FileDownLoadByProjct::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	//获取文件ID信息
	int tm_fileid;
	MOVENEXTTEST(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_fileid,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	tm_fileid = MAKEINT_NETSEQ1(tm_fileid);

	int tm_projecttype;
	MOVENEXTTEST(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_projecttype,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	tm_projecttype = MAKEINT_NETSEQ1(tm_projecttype);

	char tm_logtbname[255];
	PPM_OS::memset(&tm_logtbname, 0, sizeof(tm_logtbname));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_logtbname,sizeof(tm_logtbname)));

	std::string unix_logtbname = CUtility::GBKToUtf8(tm_logtbname);

	//构造查询语句
	//首先查询信息在那些表中
	char tm_sql[255];
	#ifdef USE_MYSQL
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),"call mc_sp_log_getfilepath_byproject (%d, %d, '%s');",
	tm_fileid,tm_projecttype,unix_logtbname.c_str());
	#else
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),"exec mc_sp_log_getfilepath_byproject %d,%d, '%s'",
	tm_fileid,tm_projecttype,unix_logtbname.c_str());
	#endif
	
	m_iRowType_ = TYPE_VEC_FILEDOWNLOAD;

	if(NULL != m_pDataBaseDeal_)
	{
		int ret = m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
		if(0 > ret){
			Clear();
			return FALSE;
		}
	}
	Clear();
	return TRUE;
}

BOOL
TestDataMngSock_FileDownLoadByPath::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{	
	 char tm_filePath[255];
	 PPM_OS::memset(&tm_filePath, 0, sizeof(tm_filePath));
	 CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_filePath,sizeof(tm_filePath)));

	m_iRowType_ = TYPE_VEC_FILEDOWNLOAD;

	memcpy(m_vecColumn[0].pColValue, tm_filePath, strlen(tm_filePath));
	char type[1];
	PPM_OS::memset(&type, 0, sizeof(type));
	memcpy(m_vecColumn[1].pColValue, type, strlen(type));

	if(!this->RowResultDeal(m_iRowType_))
	{
		Clear();
		return FALSE;
	}

	Clear();
	return TRUE;
}

/************************************************************************
TestDataMngSock_DocDownLoad                                                            
************************************************************************/
/**
文本文件下载处理,所传输过来的信息为：文件路径,文件名称
*/
BOOL   
TestDataMngSock_DocDownLoad::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	char tm_filepath[1024];
	PPM_OS::memset(&tm_filepath, 0, sizeof(tm_filepath));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_filepath,sizeof(tm_filepath)));

	//获取文件名信息
	char tm_filename[1024];
	PPM_OS::memset(&tm_filename, 0, sizeof(tm_filename));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_filename,sizeof(tm_filename)));

	PPM_OS::snprintf(tm_filepath, sizeof(tm_filepath), "%s%s", &tm_filepath, &tm_filename);

	BOOL res = TRUE;
	if(NULL != m_pServerDeal_){
		res = SendFileToClient(tm_filepath, 0);
		if(FALSE == res){
			BYTE tm_buf[10];
			tm_buf[0] = RESTYPE_FILEDOWNLOAD;
			tm_buf[1] = CMD2_RESPONSE;
			tm_buf[2] = FILETRANSFER_ERROR;
			m_pServerDeal_->SendData(tm_buf,3);
		}
	}
	return res;
}

/************************************************************************
TestDataMngSock_DocDownLoad_DIY                                                            
************************************************************************/
/**
文本文件下载处理,所传输过来的信息为：文件相对路径,文件名称
*/
BOOL   
TestDataMngSock_DocDownLoad_DIY::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	char tm_filepath[1024];
	PPM_OS::memset(&tm_filepath, 0, sizeof(tm_filepath));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_filepath,sizeof(tm_filepath)));

	std::string unix_tm_filepath = CUtility::GBKToUtf8(tm_filepath);

	//获取文件名信息
	char tm_filename[1024];
	PPM_OS::memset(&tm_filename, 0, sizeof(tm_filename));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_filename,sizeof(tm_filename)));

	std::string unix_tm_filename = CUtility::GBKToUtf8(tm_filename);

	char cFilePath[1024];
	PPM_OS::snprintf(cFilePath, sizeof(cFilePath), "%s%s%s", &(CConfigSetting::Instance()->szPath), unix_tm_filepath.c_str(), unix_tm_filename.c_str());

	BOOL res = TRUE;
	if(NULL != m_pServerDeal_){
		res = SendFileToClient(cFilePath, 0);
		if(FALSE == res){
			BYTE tm_buf[10];
			tm_buf[0] = RESTYPE_FILEDOWNLOAD;
			tm_buf[1] = CMD2_RESPONSE;
			tm_buf[2] = FILETRANSFER_ERROR;
			m_pServerDeal_->SendData(tm_buf,3);
		}
	}
	return res;
}


/************************************************************************
TestDataMngSock_BlackBlockDocDownLoad                                                            
************************************************************************/
/**
文本文件下载处理,所传输过来的信息为：黑点ID,文件名称
*/
BOOL   
TestDataMngSock_BlackBlockDocDownLoad::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	//黑点ID
	int blackblockID;
	PPM_OS::memcpy(&blackblockID,pData + ioffset,4);
	blackblockID = MAKEINT_NETSEQ1(blackblockID);
	MOVENEXT(ioffset,4,nCount);

	//获取文件名信息
	char tm_filename[1024];
	PPM_OS::memset(&tm_filename, 0, sizeof(tm_filename));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_filename,sizeof(tm_filename)));

	char tm_filepath[1024];
	PPM_OS::memset(tm_filepath, 0, sizeof(tm_filepath));
	CStdCommond::GetProcessPath(tm_filepath);
	CStdCommond::CreateDirec(tm_filepath);
	PPM_OS::snprintf(tm_filepath, sizeof(tm_filepath), "%sBlackBlock/(%d)%s", &tm_filepath, blackblockID, &tm_filename);

    SendFileSize(tm_filepath);

	BOOL res = TRUE;
	if(NULL != m_pServerDeal_){
		res = SendFileToClient(tm_filepath, 0);
		if(FALSE == res){
			BYTE tm_buf[10];
			tm_buf[0] = RESTYPE_FILEDOWNLOAD;
			tm_buf[1] = CMD2_RESPONSE;
			tm_buf[2] = FILETRANSFER_ERROR;
			m_pServerDeal_->SendData(tm_buf,3);
		}
	}
	return res;
}

BOOL   
TestDataMngSock_BlackBlockDocDownLoad::SendFileSize(const char* pFilePath)
{
	//获取文件大小
	CStdFile tm_file;
	if(!tm_file.Open(pFilePath,"ab"))
	{
		PPM_DEBUG((LM_ERROR,"cannot open file %s,%m\n",pFilePath));
		tm_file.Close();	
		return FALSE;
	}
	int fileSize = tm_file.GetLength();
	//发送文件的大小
	PPM_OS::memset(m_pSendBuf_,0,sizeof(m_pSendBuf_));

	//构造信息发送
	int tm_ioffset = 0;
	
	m_pSendBuf_[tm_ioffset++] = CMD1_CONFIG_MNG;//命令字
	m_pSendBuf_[tm_ioffset++] = CMD2_RESPONSE;//子命令字
	m_pSendBuf_[tm_ioffset++] = FILETRANSFER_LENTH;//响应类型
	//
	CSearch_PI::AddIntToBuf(m_pSendBuf_, tm_ioffset, fileSize, SEND_BUF_MAXSIZE);

	if(0 > m_pServerDeal_->SendData(m_pSendBuf_,tm_ioffset)){
		return FALSE;
	}

	return TRUE;
}


/************************************************************************
TestDataMngSock_CQTImageDocDownLoad                                                            
************************************************************************/
/**
CQT文件下载处理,所传输过来的信息为：文件名称
*/
BOOL   
TestDataMngSock_CQTImageDocDownLoad::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	//获取文件名信息
	char tm_filename[1024];
	PPM_OS::memset(&tm_filename, 0, sizeof(tm_filename));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_filename,sizeof(tm_filename)));
	char dir[1024];
	PPM_OS::memset(&dir,0,sizeof(dir));
	int k = 0;
	for (size_t i = 0;i < sizeof(tm_filename);i++)
	{
		if (tm_filename[i] == '#')
		{
			k++;
		}
		if (k == 2)
		{
			break;
		}
		dir[i] = tm_filename[i];
	}

	char tm_filepathFG[1024];
	PPM_OS::memset(tm_filepathFG, 0, sizeof(tm_filepathFG));
	PPM_OS::snprintf(tm_filepathFG, sizeof(tm_filepathFG), "%sCqtImageTemper/%s/%s.fp", 
		CConfigSetting::Instance()->m_pchCQTImagePath_, &dir, &tm_filename);

	char tm_filepathJPG[1024];
	PPM_OS::memset(tm_filepathJPG, 0, sizeof(tm_filepathJPG));
	PPM_OS::snprintf(tm_filepathJPG, sizeof(tm_filepathJPG), "%sCqtImageTemper/%s/%s.jpg", 
		CConfigSetting::Instance()->m_pchCQTImagePath_, &dir, &tm_filename);

	char tm_filepathTAB[1024];
	PPM_OS::memset(tm_filepathTAB, 0, sizeof(tm_filepathTAB));
	PPM_OS::snprintf(tm_filepathTAB, sizeof(tm_filepathTAB), "%sCqtImageTemper/%s/%s.tab", 
		CConfigSetting::Instance()->m_pchCQTImagePath_, &dir, &tm_filename);

	CStdFile file;
	BOOL res = TRUE;
	if (TRUE == file.IsExist(tm_filepathFG) &&TRUE == file.IsExist(tm_filepathJPG) && TRUE == file.IsExist(tm_filepathTAB))
	{
		if (!DownLoadFile(tm_filepathFG)){
			return FALSE;
		}

		if (!DownLoadFile(tm_filepathJPG)){
			return FALSE;
		}

		if (!DownLoadFile(tm_filepathTAB)){
			return FALSE;
		}
	}
	else
	{
		res = FALSE;
		BYTE tm_buf[10];
		tm_buf[0] = RESTYPE_FILEDOWNLOAD;
		tm_buf[1] = CMD2_RESPONSE;
		tm_buf[2] = FILETRANSFER_ERROR;
		m_pServerDeal_->SendData(tm_buf,3);
	}
	return res;
}

BOOL TestDataMngSock_CQTImageDocDownLoad::DownLoadFile(char* c_filename)
{
	SendFileSize(c_filename);

	BOOL res = TRUE;
	if(NULL != m_pServerDeal_){
		res = SendFileToClient(c_filename, 0);
		if(FALSE == res){
			BYTE tm_buf[10];
			tm_buf[0] = RESTYPE_FILEDOWNLOAD;
			tm_buf[1] = CMD2_RESPONSE;
			tm_buf[2] = FILETRANSFER_ERROR;
			m_pServerDeal_->SendData(tm_buf,3);
			return res;
		}
	}
	return res;
}

BOOL   
TestDataMngSock_CQTImageDocDownLoad::SendFileSize(const char* pFilePath)
{
	//获取文件大小
	CStdFile tm_file;
	if(!tm_file.Open(pFilePath,"ab"))
	{
		PPM_DEBUG((LM_ERROR,"cannot open file %s,%m\n",pFilePath));
		tm_file.Close();	
		return FALSE;
	}
	int fileSize = tm_file.GetLength();
	//发送文件的大小
	PPM_OS::memset(m_pSendBuf_,0,sizeof(m_pSendBuf_));

	//构造信息发送
	int tm_ioffset = 0;
	m_pSendBuf_[tm_ioffset++] = CMD1_CONFIG_MNG;//命令字
	m_pSendBuf_[tm_ioffset++] = CMD2_RESPONSE;//子命令字
	m_pSendBuf_[tm_ioffset++] = FILETRANSFER_LENTH;//响应类型
	//
	CSearch_PI::AddIntToBuf(m_pSendBuf_, tm_ioffset, fileSize, SEND_BUF_MAXSIZE);

	if(0 > m_pServerDeal_->SendData(m_pSendBuf_,tm_ioffset)){
		return FALSE;
	}

	return TRUE;
}

/************************************************************************
TestDataMngSock_CheckBlackBlockDoc                                                            
************************************************************************/
//判断文件是否存在
BOOL   
TestDataMngSock_BlackBlockDocDelete::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	//黑点ID
	int blackblockID;
	PPM_OS::memcpy(&blackblockID,pData + ioffset,4);
	blackblockID = MAKEINT_NETSEQ1(blackblockID);
	MOVENEXT(ioffset,4,nCount);

	//获取文件名信息
	char tm_filename[1024];
	PPM_OS::memset(&tm_filename, 0, sizeof(tm_filename));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_filename,sizeof(tm_filename)));

	char tm_filepath[1024];
	PPM_OS::memset(tm_filepath, 0, sizeof(tm_filepath));
	CStdCommond::GetProcessPath(tm_filepath);
	PPM_OS::snprintf(tm_filepath, sizeof(tm_filepath), "%sBlackBlock/(%d)%s", &tm_filepath, blackblockID, &tm_filename);

	if (CStdFile::IsExist(tm_filepath))
	{
		CStdFile::Remove(tm_filepath);
	}

	char tm_sql[1024];
	PPM_OS::memset(tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql, sizeof(tm_sql), "update tb_black_block set attfiledesc = substring(attfiledesc, 0, charindex('%s',attfiledesc)-1)\
+ substring(attfiledesc, charindex('%s',attfiledesc) + len('%s'), len(attfiledesc)-(charindex('%s',attfiledesc) + len('%s'))+1) \
from tb_black_block where id = %d ",&tm_filename, &tm_filename, &tm_filename, &tm_filename, &tm_filename, blackblockID);

	if(NULL != m_pDataBaseDeal_){
		m_pDataBaseDeal_->ExecSql(tm_sql);
	}	

	//处理结束
	FeedBackOver(CMD1_CONFIG_MNG,RESTYPE_SEARCHEND);
	return TRUE;
}


/************************************************************************
TestDataMngSock_CheckBlackBlockDoc                                                            
************************************************************************/
//判断文件是否存在
BOOL   
TestDataMngSock_BlackBlockDocUpdate::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	//黑点ID
	int blackblockID;
	PPM_OS::memcpy(&blackblockID,pData + ioffset,4);
	blackblockID = MAKEINT_NETSEQ1(blackblockID);
	MOVENEXT(ioffset,4,nCount);

	//获取文件名信息
	char tm_filename[1024];
	PPM_OS::memset(&tm_filename, 0, sizeof(tm_filename));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_filename,sizeof(tm_filename)));

	char tm_sql[1024];
	PPM_OS::memset(tm_sql, 0, sizeof(tm_sql));
	PPM_OS::snprintf(tm_sql, sizeof(tm_sql), "update tb_black_block set attfiledesc = attfiledesc + '|%s' where id = %d ", &tm_filename, blackblockID);
    
	if(NULL != m_pDataBaseDeal_){
		m_pDataBaseDeal_->ExecSql(tm_sql);
	}	

	//处理结束
	FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
	return TRUE;
}


/************************************************************************
TestDataMngSock_FileUpLoad_BlackBlock                                                            
************************************************************************/
//下载是增长式增加，每次下载都需要读取文件然后在后面增加
BOOL 
TestDataMngSock_FileUpLoad_BlackBlock::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	//黑点ID
	int blackblockID;
	PPM_OS::memcpy(&blackblockID,pData + ioffset,4);
	blackblockID = MAKEINT_NETSEQ1(blackblockID);
	MOVENEXT(ioffset,4,nCount);

	//获取文件名信息
	char tm_filename[1024];
	PPM_OS::memset(&tm_filename, 0, sizeof(tm_filename));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_filename,sizeof(tm_filename)));

	char tm_filepath[1024];
	PPM_OS::memset(tm_filepath, 0, sizeof(tm_filepath));
	CStdCommond::GetProcessPath(tm_filepath);
	PPM_OS::snprintf(tm_filepath,sizeof(tm_filepath),"%sBlackBlock/(%d)%s", &tm_filepath, blackblockID, &tm_filename);

	if(NULL == m_pServerDeal_)
	{
		PPM_DEBUG((LM_ERROR,"m_pServerDeal_ is null!%l\n"));
		return FALSE;
	}

	char tm_folderpath[1024];
	PPM_OS::memset(tm_folderpath, 0, sizeof(tm_folderpath));
	CStdCommond::GetProcessPath(tm_folderpath);
	PPM_OS::snprintf(tm_folderpath,sizeof(tm_folderpath),"%sBlackBlock/", &tm_folderpath);

	// if (GetFileAttributes(tm_folderpath) != FILE_ATTRIBUTE_DIRECTORY)
	// {
	// 	BOOL ret = CreateDirectory(tm_folderpath,NULL);
	// 	if(!ret)
	// 	{
	// 		FeedBackOver(CMD1_CONFIG_MNG,RESTYPE_SEARCHEND);
	// 		PPM_DEBUG((LM_ERROR,"Create BlackBlock Directory fail!%l\n"));
	// 		return FALSE;
	// 	}
	// }

	CStdFile tm_file;

	if(!tm_file.Open(tm_filepath,"ab")){
		PPM_DEBUG((LM_ERROR,"cannot open file %s,%m\n",tm_filepath));
		tm_file.Close();	
		return FALSE;
	}

	//接收数据
	//包号
	int packNum;
	int receiveBufferLenth;

	PPM_OS::memcpy(&packNum,pData + ioffset,4);
	packNum = MAKEINT_NETSEQ1(packNum);
	MOVENEXT(ioffset,4,nCount);
    //文件体长度
	PPM_OS::memcpy(&receiveBufferLenth,pData + ioffset,4);
	receiveBufferLenth = MAKEINT_NETSEQ1(receiveBufferLenth);
	MOVENEXT(ioffset,4,nCount);

    if (receiveBufferLenth > SEND_BUF_MAXSIZE - 8){
		//处理结束
		FeedBackOver(CMD1_CONFIG_MNG,RESTYPE_SEARCHEND);
		return TRUE;
    }

	//文件体
	BYTE byteBuffer[SEND_BUF_MAXSIZE];
	PPM_OS::memset(&byteBuffer, 0, sizeof(byteBuffer));
	PPM_OS::memcpy(byteBuffer, pData+ioffset, receiveBufferLenth);

    tm_file.SeekToEnd();
    tm_file.Write(byteBuffer, receiveBufferLenth);
	tm_file.Flush();
	tm_file.Close();

	//处理结束
	FeedBackOver(CMD1_CONFIG_MNG,RESTYPE_SEARCHEND);
	return TRUE;
}

/************************************************************************
TestDataMngSock_FileUpLoad_XLSX                                                            
************************************************************************/
//下载是增长式增加，每次下载都需要读取文件然后在后面增加
BOOL 
TestDataMngSock_FileUpLoad_XLSX::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	//获取文件名信息
	char tm_filename[1024];
	PPM_OS::memset(&tm_filename, 0, sizeof(tm_filename));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_filename,sizeof(tm_filename)));

	char tm_filepath[1024];
	PPM_OS::memset(&tm_filepath, 0, sizeof(tm_filepath));
	PPM_OS::snprintf(tm_filepath, sizeof(tm_filepath), "%sXLSXTemper/%s", CConfigSetting::Instance()->m_pchXLSXPath_, &tm_filename);

	if(NULL == m_pServerDeal_)
	{
		PPM_DEBUG((LM_ERROR,"m_pServerDeal_ is null!%l\n"));
		return FALSE;
	}

	char tm_folderpath[1024];
	PPM_OS::memset(tm_folderpath, 0, sizeof(tm_folderpath));
	PPM_OS::snprintf(tm_folderpath, sizeof(tm_folderpath), "%sXLSXTemper/", CConfigSetting::Instance()->m_pchXLSXPath_);

	// if (GetFileAttributes(tm_folderpath) != FILE_ATTRIBUTE_DIRECTORY)
	// {
	// 	BOOL ret = CreateDirectory(tm_folderpath,NULL);
	// 	if(!ret)
	// 	{
	// 		FeedBackOver(CMD1_CONFIG_MNG,RESTYPE_SEARCHEND);
	// 		PPM_DEBUG((LM_ERROR,"Create XLSXTemper Directory fail!%l\n"));
	// 		return FALSE;
	// 	}
	// }

	CStdFile tm_file;

	if(!tm_file.Open(tm_filepath,"ab"))
	{
		PPM_DEBUG((LM_ERROR,"cannot open file %s,%m\n",tm_filepath));
		tm_file.Close();	
		return FALSE;
	}

	//接收数据
	//包号
	int packNum;
	PPM_OS::memcpy(&packNum,pData + ioffset,4);
	packNum = MAKEINT_NETSEQ1(packNum);
	MOVENEXT(ioffset,4,nCount);
	//文件体长度
	int receiveBufferLenth;
	PPM_OS::memcpy(&receiveBufferLenth,pData + ioffset,4);
	receiveBufferLenth = MAKEINT_NETSEQ1(receiveBufferLenth);
	MOVENEXT(ioffset,4,nCount);

	if (receiveBufferLenth > SEND_BUF_MAXSIZE - 8)
	{
		//处理结束
		FeedBackOver(CMD1_CONFIG_MNG,RESTYPE_SEARCHEND);
		return TRUE;
	}

	//文件体
	BYTE byteBuffer[SEND_BUF_MAXSIZE];
	PPM_OS::memset(&byteBuffer, 0, sizeof(byteBuffer));
	PPM_OS::memcpy(byteBuffer, pData+ioffset, receiveBufferLenth);

	tm_file.SeekToEnd();
	tm_file.Write(byteBuffer, receiveBufferLenth);
	tm_file.Flush();
	tm_file.Close();

	//处理结束
	FeedBackOver(CMD1_CONFIG_MNG,RESTYPE_SEARCHEND);
	return TRUE;
}

/************************************************************************
TestDataMngSock_FileUpLoad_DOC                                                            
************************************************************************/
//下载是增长式增加，每次下载都需要读取文件然后在后面增加
BOOL 
TestDataMngSock_FileUpLoad_DOC::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	MOVENEXTTEST(ioffset, 4 * 3, nCount);
	//上传状态
	int uploadstatus;//1.首包数据，2.中间包数据 3.最后一包数据 4.删除数据
	PPM_OS::memcpy(&uploadstatus,pData + ioffset,4);
	uploadstatus = MAKEINT_NETSEQ1(uploadstatus);
	MOVENEXT(ioffset,4,nCount);

	//上传类型
	int uploadtype;//1是绝对目录，2是相对目录
	PPM_OS::memcpy(&uploadtype,pData + ioffset,4);
	uploadtype = MAKEINT_NETSEQ1(uploadtype);
	MOVENEXT(ioffset,4,nCount);

	//上传时间
	int time;
	PPM_OS::memcpy(&time,pData + ioffset,4);
	time = MAKEINT_NETSEQ1(time);
	MOVENEXT(ioffset,4,nCount);

	//文件业务类型
	int filetype;
	PPM_OS::memcpy(&filetype,pData + ioffset,4);
	filetype = MAKEINT_NETSEQ1(filetype);
	MOVENEXT(ioffset,4,nCount);

	//上传文件路径，是相对路径，在服务端的根目录下Document下面。如：filepath:myfile/a/ 那么目录就是： Document/myfile/a/
	char filepath[255];
	PPM_OS::memset(&filepath, 0, sizeof(filepath));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,filepath,sizeof(filepath)));

	//文件名
	char filename[255];
	PPM_OS::memset(&filename, 0, sizeof(filename));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,filename,sizeof(filename)));

	char tm_folderpath[1024];
	PPM_OS::memset(tm_folderpath, 0, sizeof(tm_folderpath));

	if (!GetFolderPath(uploadtype, filepath, tm_folderpath))
	{
		return FALSE;
	}

	//删除数据
	if (uploadstatus == 4)
	{
		return DeleteFileInfoData(filepath, filename, tm_folderpath);
	}

	//首包数据，文件存在就删除
	if (uploadstatus == 1)
	{
		RemoveFile(tm_folderpath);
	}

	//检查目录生成文件
	// if (GetFileAttributes(tm_folderpath) != FILE_ATTRIBUTE_DIRECTORY)
	// {
	// 	BOOL ret = CreateDirectory(tm_folderpath, NULL);
	// 	if (!ret)
	// 	{
	// 		FeedBackOver(CMD1_CONFIG_MNG, RESTYPE_DOCUPLOAD_ERROR);
	// 		PPM_DEBUG((LM_ERROR, "Create Directory fail!%l\n"));
	// 		return FALSE;
	// 	}
	// }

	PPM_OS::snprintf(tm_folderpath, sizeof(tm_folderpath), "%s%s", tm_folderpath, filename);

	CStdFile tm_file;
	if (!tm_file.Open(tm_folderpath, "ab"))
	{
		FeedBackOver(CMD1_CONFIG_MNG, RESTYPE_DOCUPLOAD_ERROR);
		PPM_DEBUG((LM_ERROR, "cannot open file %s,%m\n", tm_folderpath));
		tm_file.Close();
		return FALSE;
	}

	//接收数据
	//包号
	int packNum;
	PPM_OS::memcpy(&packNum, pData + ioffset, 4);
	packNum = MAKEINT_NETSEQ1(packNum);
	MOVENEXT(ioffset, 4, nCount);
	//文件体长度
	int receiveBufferLenth;
	PPM_OS::memcpy(&receiveBufferLenth, pData + ioffset, 4);
	receiveBufferLenth = MAKEINT_NETSEQ1(receiveBufferLenth);
	MOVENEXT(ioffset, 4, nCount);

	if (receiveBufferLenth > SEND_BUF_MAXSIZE - 100)
	{
		//处理结束
		FeedBackOver(CMD1_CONFIG_MNG, RESTYPE_DOCUPLOAD_ERROR);
		return FALSE;
	}

	//文件体
	BYTE byteBuffer[SEND_BUF_MAXSIZE];
	PPM_OS::memset(&byteBuffer, 0, sizeof(byteBuffer));
	PPM_OS::memcpy(byteBuffer, pData + ioffset, receiveBufferLenth);

	tm_file.SeekToEnd();
	tm_file.Write(byteBuffer, receiveBufferLenth);
	tm_file.Flush();
	tm_file.Close();

	//如果是结束包，就需要插入表记录
	if (uploadstatus == 3)
	{
		char strsql[255];
		PPM_OS::memset(&strsql, 0, sizeof(strsql));
		PPM_OS::snprintf(strsql, sizeof(strsql), "insert into tb_cfg_document values(%d,%d,%d,%s,%s,%s)",
			&filetype,
			&time,
			&uploadtype,
			&filepath,
			&filename,
			"");
		if (!InsertLogInfo(strsql))
		{
			FeedBackOver(CMD1_CONFIG_MNG, RESTYPE_DOCUPLOAD_ERROR);
			PPM_DEBUG((LM_ERROR, "Insert tb_cfg_document fail!%l\n"));
		}
	}

	//处理结束
	FeedBackOver(CMD1_CONFIG_MNG, RESTYPE_DOCUPLOAD_SUCCESS);
	return TRUE;
}

BOOL TestDataMngSock_FileUpLoad_DOC::GetFolderPath(int uploadtype, char* filepath, char* tm_folderpath)
{
	switch (uploadtype)
	{
    case 1:
        PPM_OS::snprintf(tm_folderpath, sizeof(tm_folderpath), "%s", tm_folderpath, filepath);
        break;
    case 2:
        CStdCommond::GetProcessPath(tm_folderpath);
        PPM_OS::snprintf(tm_folderpath, sizeof(tm_folderpath), "%sDocument/%s", tm_folderpath, filepath);
        break;
    default:
        FeedBackOver(CMD1_CONFIG_MNG, RESTYPE_DOCUPLOAD_ERROR);
        PPM_DEBUG((LM_ERROR, "UpLoadType Wrong!%l\n"));
        return FALSE;
	}

	return TRUE;
}

BOOL TestDataMngSock_FileUpLoad_DOC::DeleteFileInfoData(char* filepath, char* filename, char* tm_folderpath)
{
	char strsql[255];
	PPM_OS::memset(strsql, 0, sizeof(strsql));
	PPM_OS::snprintf(strsql, sizeof(strsql), "delete from tb_cfg_document where filepath + filename = '%s%s'",
		filepath,
		filename);
	if(NULL != m_pDataBaseDeal_)
	{
		if(0 > m_pDataBaseDeal_->ExecSql(strsql))
		{
			FeedBackOver(CMD1_CONFIG_MNG,RESTYPE_DOCUPLOAD_ERROR);
			return FALSE;
		}
	}
	else
	{
		FeedBackOver(CMD1_CONFIG_MNG,RESTYPE_DOCUPLOAD_ERROR);
		return FALSE;
	}

	RemoveFile(tm_folderpath);

	FeedBackOver(CMD1_CONFIG_MNG,RESTYPE_DOCUPLOAD_SUCCESS);
	return TRUE;
}

void TestDataMngSock_FileUpLoad_DOC::RemoveFile(const char* tm_folderpath)
{
	bool isexist = CStdFile::IsExist(tm_folderpath);
	if (isexist)
	{
		CStdFile::Remove(tm_folderpath);
	}
}

BOOL
TestDataMngSock_FileUpLoad_DOC::InsertLogInfo(const char* strsql)
{
	if(NULL != m_pDataBaseDeal_)
	{
		if(0 > m_pDataBaseDeal_->ExecSql(strsql))
		{
			return FALSE;
		}
		return TRUE;
	}
	return FALSE;
}

/************************************************************************
MCCFile_Send                                                            
************************************************************************/
//发送MCC文件
BOOL 
MCCFile_Send::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	char tm_cityName[32];
	PPM_OS::memset(tm_cityName, 0, sizeof(tm_cityName));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData, nCount, ioffset, tm_cityName, sizeof(tm_cityName)));

	char fileRule[512];
	PPM_OS::memset(fileRule, 0, sizeof(fileRule));
	PPM_OS::sprintf(fileRule, "%sCellDatas *.%s.mcc.zip", CConfigSetting::Instance()->m_pchMCCPath_, tm_cityName);

	// WIN32_FIND_DATA data;
	// HANDLE hFind;
	// hFind = FindFirstFile(fileRule, &data);

	// char* fileName[20] = {0};
	// int idx = 0;
	// char tempName[512];
	// while (hFind != INVALID_HANDLE_VALUE)
	// {
	// 	PPM_OS::memset(tempName, 0, sizeof(tempName));
	// 	PPM_OS::strcpy(tempName, data.cFileName);
	// 	size_t len = PPM_OS::strlen(tempName);
	// 	fileName[idx] = new char[len + 1];
	// 	PPM_OS::memcpy(fileName[idx], tempName, len + 1);
	// 	++idx;

	// 	if(!FindNextFile(hFind, &data)){
	// 		hFind = INVALID_HANDLE_VALUE;
	// 	}
	// }
	// CloseHandle(hFind);

	// int maxtime = 0;
	// for (idx = 0; idx < 20; ++idx)
	// {
	// 	if(fileName[idx] == NULL){
	// 		break;
	// 	}

	// 	//CellDatas 20151014.鸡西市.mcc.zip
	// 	string strName = string(fileName[idx]);
	// 	strName = strName.substr(strlen("CellDatas "), 8);

	// 	int time = atoi(strName.c_str());

	// 	if(time > maxtime)
	// 	{
	// 		PPM_OS::memset(tempName, 0, sizeof(tempName));
	// 		PPM_OS::memcpy(tempName, fileName[idx], PPM_OS::strlen(fileName[idx]));
	// 		maxtime = time; 
	// 	}
	// 	delete fileName[idx];
	// }

	// PPM_OS::sprintf(fileRule, "%s%s", CConfigSetting::Instance()->m_pchMCCPath_, tempName);

	// if(NULL == m_pServerDeal_){
	// 	return TRUE;
	// }

	// if(PPM_OS::strlen(tempName) == 0 || SendFileToClient(fileRule, 0) == FALSE){
	// 	BYTE tm_buf[10];
	// 	tm_buf[0] = RESTYPE_FILEDOWNLOAD;
	// 	tm_buf[1] = CMD2_RESPONSE;
	// 	tm_buf[2] = FILETRANSFER_ERROR;
	// 	m_pServerDeal_->SendData(tm_buf,3);
	// }

	return TRUE;
}

/************************************************************************
MCFileTime_Send                                                            
************************************************************************/
//发送MC文件最近修改时间
BOOL 
MCFileTime_Send::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{

	for (size_t i = 0; i < CConfigSetting::Instance()->m_vec_mcTimePath.size(); ++i)
	{
		PPM_OS::memset(m_pSendBuf_,0,SEND_BUF_MAXSIZE);
		int tm_ioffset = 0;
		m_pSendBuf_[tm_ioffset++] = RESTYPE_MCFILETIME; //下载响应
		m_pSendBuf_[tm_ioffset++] = CMD2_RESPONSE; //响应类型
		m_pSendBuf_[tm_ioffset++] = FILETRANSFER_CONTINUE;//文件传输类型

		std::string cfilename = CUtility::Utf8ToGBK(CConfigSetting::Instance()->m_vec_mcTimePath[i].cFileName);
		WORD tm_filenamelen = cfilename.length();
		WORD tm_filenamelen_NET = MAKEWORD_NETSEQ1(tm_filenamelen);

		PPM_OS::memcpy(m_pSendBuf_ + tm_ioffset,&tm_filenamelen_NET,2); //文件名称长度
		tm_ioffset += 2;
		PPM_OS::memcpy(m_pSendBuf_ + tm_ioffset, cfilename.c_str(),tm_filenamelen); //文件名称
		tm_ioffset += tm_filenamelen;

		std::string ctime = CUtility::Utf8ToGBK(CConfigSetting::Instance()->m_vec_mcTimePath[i].cTime);
		WORD tm_timelen = ctime.length();
		WORD tm_timelen_NET = MAKEWORD_NETSEQ1(tm_timelen);

		PPM_OS::memcpy(m_pSendBuf_ + tm_ioffset,&tm_timelen_NET,2); //文件名称长度
		tm_ioffset += 2;
		PPM_OS::memcpy(m_pSendBuf_ + tm_ioffset, ctime.c_str(), tm_timelen); //文件名称
		tm_ioffset += tm_timelen;

		m_pServerDeal_->SendData(m_pSendBuf_,tm_ioffset + 8);
	}
	m_pSendBuf_[0] = RESTYPE_MCFILETIME;
	m_pSendBuf_[1] = CMD2_RESPONSE;
	m_pSendBuf_[2] = FILETRANSFER_END;
	m_pServerDeal_->SendData(m_pSendBuf_,3);
	return true;
}

/************************************************************************
FileDistribute                                                            
************************************************************************/
//进行文件分发
BOOL 
FileDistribute::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	//获取文件ID信息
	int tm_fileid;
	MOVENEXTTEST(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_fileid,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	tm_fileid = MAKEINT_NETSEQ1(tm_fileid);

	int tm_projecttype;
	MOVENEXTTEST(ioffset,4,nCount);
	PPM_OS::memcpy(&tm_projecttype,pData + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	tm_projecttype = MAKEINT_NETSEQ1(tm_projecttype);

	char tm_logtbname[255];
	PPM_OS::memset(&tm_logtbname, 0, sizeof(tm_logtbname));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_logtbname,sizeof(tm_logtbname)));

	//目标路径
	PPM_OS::memset(&targetDir, 0, sizeof(targetDir));
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,targetDir,sizeof(targetDir)));

	//构造查询语句
	//首先查询信息在那些表中
	char tm_sql[255];
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),"exec mc_sp_log_getfilepath_byproject %d,%d, '%s'",
		tm_fileid,tm_projecttype,tm_logtbname
		);

	m_iRowType_ = TYPE_VEC_FILEDOWNLOAD;

	if(NULL != m_pDataBaseDeal_){
		int ret = m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
		if(0 > ret){
			Clear();
			return FALSE;
		}
	}
	Clear();
	return TRUE;
}

//复制文件到目标地
BOOL 
FileDistribute::RowResultDeal()
{
	BOOL res = TRUE;
	//获取文件路径
	if(NULL != m_pServerDeal_){
		if(m_vecColumn.size() <= 0){
            return res;
		}

		res = CopyData((char*)m_vecColumn[0].pColValue);
		if(FALSE == res){
			BYTE tm_buf[10];
			tm_buf[0] = CMD1_TESTDATAMANAGEMENT;
			tm_buf[1] = CMD2_RESPONSE;
			tm_buf[2] = RESTYPE_FILEDISTRIBUTE_FAIL;
			m_pServerDeal_->SendData(tm_buf,3);
		}
	}
	return res;
}

BOOL
FileDistribute::CopyData(std::string strfilename)
{
	CStdFile tm_file;

	if(NULL == m_pServerDeal_)
	{
		PPM_DEBUG((LM_ERROR,"m_pServerDeal_ is null!%l\n"));
		return FALSE;
	}


	//尝试去获取本地文件，如果不能就去远程访问文件
	if (CheckFile((strfilename)))
	{
	}
	else if (!CheckRemoteFile(strfilename))
	{
		return FALSE;
	}

	std::string name = CUtility::GetFileNameWithExtension(strfilename);

	// if (GetFileAttributes(targetDir) != FILE_ATTRIBUTE_DIRECTORY)
	// {
	// 	std::string strDir = targetDir;
	// 	std::vector<std::string> vec;
	// 	vec = CStdStrOpHelper::Slipt(strDir, "\\");
	// 	std::string tmp = "";
	// 	for (std::vector<std::string>::iterator it = vec.begin(); it != vec.end(); ++it)
	// 	{
	// 		tmp += *it;
	// 		tmp += '/';
	// 		CreateDirectory(tmp.c_str(), NULL);
	// 	}
	// }

	// if(!CopyFile(strfilename.c_str(), strcat(targetDir, name.c_str()), false))
	// {
	// 	return FALSE;
	// }
	FeedBackOver(CMD1_TESTDATAMANAGEMENT,RESTYPE_FILEDISTRIBUTE_SUCCESS);

	return TRUE;
}

BOOL
FileDistribute::CheckRemoteFile(std::string& strfilename)
{
	BOOL bFindFile = FALSE;
	for (size_t i = 0; i < CConfigSetting::Instance()->m_vec_FileDownLoadPath.size();++i)
	{
		std::string filepath = CConfigSetting::Instance()->m_vec_FileDownLoadPath[i].GetFileRemotePath(strfilename);
		if (filepath.length() == 0)
		{
			continue;
		}
		if (CheckFile(filepath))
		{
			strfilename = filepath;
			bFindFile = TRUE;
			break;
		}
	}
	if (!bFindFile)
	{
		std::string outputstr = "找不到文件，文件名称为： " + strfilename;
		PPM_APPLOG((LM_ERROR,  outputstr.c_str() ));
		return FALSE;
	}
    
	return TRUE;
}

