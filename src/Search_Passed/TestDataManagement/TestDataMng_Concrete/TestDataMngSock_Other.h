// TestDataMngSock_Other.h: interface for the CTestDataMngSock_Other class.
//
//////////////////////////////////////////////////////////////////////

#ifndef __TESTDATAMNGSOCK_OTHER_H__
#define __TESTDATAMNGSOCK_OTHER_H__

#include "./SearchSock.h"


#define  TYPE_VEC_FILEDOWNLOAD 0x01
#define  TYPE_VEC_FILEDOWNLOAD_BLACKBLOCK 0x02

#define  TYPE_VEC_CHECK_BLACKBLOCK_DOC 0x03

class Comm_DownLoad : public CSearchSock
{
public:
	virtual BOOL RowResultDeal(int nQType);
	
public:
	BOOL SendFileToClient(std::string strfilename, int filetype);
	BOOL IsZipRaRFileExist(std::string& strfilename);
	BOOL CheckFile(std::string& strfilename);
	BOOL CheckFileName1(std::string& strfilename);
	BOOL CheckFileName2(std::string& strfilename);
	BOOL CheckRemoteFile(std::string& strfilename);
};
   
class TestDataMngSock_FileDownLoad : public Comm_DownLoad
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};

class TestDataMngSock_FileDownLoadByProjct : public Comm_DownLoad
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};

class TestDataMngSock_FileDownLoadByPath : public Comm_DownLoad
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};

class TestDataMngSock_CQTImageDocDownLoad : public Comm_DownLoad
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);	

private:
	BOOL DownLoadFile(char* c_filename);
	BOOL SendFileSize(const char* pFilePath);
};

//黑点文件
class TestDataMngSock_BlackBlockDocDownLoad : public Comm_DownLoad
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);	

private:
	BOOL SendFileSize(const char* pFilePath);
};

class TestDataMngSock_BlackBlockDocDelete : public Comm_DownLoad
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);	
};

class TestDataMngSock_BlackBlockDocUpdate : public Comm_DownLoad
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);	
};

class TestDataMngSock_FileUpLoad_BlackBlock : public Comm_DownLoad
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};

class TestDataMngSock_FileUpLoad_XLSX : public Comm_DownLoad
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};

//上传文件
class TestDataMngSock_FileUpLoad_DOC : public Comm_DownLoad
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
private:
	BOOL InsertLogInfo(const char* pFilePath);
	BOOL DeleteFileInfoData(char* filepath, char* filename, char* tm_folderpath);
	BOOL GetFolderPath(int uploadtype, char* filepath, char* tm_folderpath);
	void RemoveFile(const char* tm_folderpath);
};

class TestDataMngSock_DocDownLoad : public Comm_DownLoad
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);	
};

class MCCFile_Send : public Comm_DownLoad
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};

class MCFileTime_Send : public Comm_DownLoad
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);	
};
class TestDataMngSock_DocDownLoad_DIY : public Comm_DownLoad
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);	
};
class FileDistribute : public Comm_DownLoad
{
public:
	char targetDir[512];
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
	virtual BOOL RowResultDeal();
	virtual BOOL CopyData(std::string strfilename);
	BOOL CheckRemoteFile(std::string& strfilename);
};

#endif
