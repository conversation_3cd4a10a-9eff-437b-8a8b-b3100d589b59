// SockTestDataMngDeal.cpp: implementation of the CSockTestDataMngDeal class.
//
//////////////////////////////////////////////////////////////////////

#include "./SockTestDataMngDeal.h"

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CSockTestDataMngDeal::CSockTestDataMngDeal()
:m_mapStdmdReqTypePos()
{
	InitReqTypeMap();
}

CSockTestDataMngDeal::~CSockTestDataMngDeal()
{
	m_mapStdmdReqTypePos.clear();
}


BOOL
CSockTestDataMngDeal::DealSock(const BYTE* const pData, const int nCount,int& ioffset, const STRU_SockInfo& sockInfo)
{
	char tm_addr[512];
	sockInfo.pServerDeal_->m_ptcpclient_->GetInetAddr()->addr_to_string(tm_addr, sizeof(tm_addr), 1);
	std::string strUserInfo = "[" + std::string(tm_addr) + "]";
	
	if(CMD2_REQUEST == pData[ioffset])//请求
	{
		MOVENEXT(ioffset,1,nCount)

		std::map<BYTE, DealReqTypeData>::iterator it;
		it = m_mapStdmdReqTypePos.find(pData[ioffset++]);

		if (it != m_mapStdmdReqTypePos.end())
		{
			(this->*(it->second))(pData, nCount, ioffset, sockInfo, strUserInfo);
		}
		else
		{
			PPM_DEBUG((LM_ERROR, "known test data management request:%d\n", pData[ioffset - 1]));
			return FALSE;
		}
	}
	else //响应 
	{
		PPM_DEBUG((LM_ERROR,"should not be test data management response:%d\n",pData[ioffset+1]));
		return FALSE;
	}

	DEALDATA2(TestDataMngSock_FileDownLoad, strUserInfo);
	return TRUE;
}

//数据下载请求
void 
CSockTestDataMngDeal::
Deal_Reqtype_FileDownload(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo)
{
	DEALDATA2(TestDataMngSock_FileDownLoad, strUserInfo);
}
//数据下载请求通过项目类型
void 
CSockTestDataMngDeal::
Deal_Reqtype_FileDownloadByProject(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo)
{
	DEALDATA2(TestDataMngSock_FileDownLoadByProjct, strUserInfo);
}
//数据下载请求通过文件路径
void 
CSockTestDataMngDeal::
Deal_Reqtype_FileDownloadByPath(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo)
{
	DEALDATA2(TestDataMngSock_FileDownLoadByPath, strUserInfo);
}
//上传
void 
CSockTestDataMngDeal::
Deal_Reqtype_DocUpload(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo)
{
	DEALDATA2(TestDataMngSock_FileUpLoad_DOC, strUserInfo);
}
//文本文件下载请求
void 
CSockTestDataMngDeal::
Deal_Reqtype_DocDownload(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo)
{
	DEALDATA2(TestDataMngSock_DocDownLoad, strUserInfo);
}
//黑点解决方案下载请求
void 
CSockTestDataMngDeal::
Deal_Reqtype_FileDownload_BlackBlock(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo)
{
	DEALDATA2(TestDataMngSock_BlackBlockDocDownLoad, strUserInfo);
}
//CQTImage下载请求
void 
CSockTestDataMngDeal::
Deal_Reqtype_FileDownload_CQTImage(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo)
{
	DEALDATA2(TestDataMngSock_CQTImageDocDownLoad, strUserInfo);
}
//检查黑点上传文件
void 
CSockTestDataMngDeal::
Deal_Reqtype_FileUpload_BlackBlock_Delete(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo)
{
	DEALDATA2(TestDataMngSock_BlackBlockDocDelete, strUserInfo);
}
void 
CSockTestDataMngDeal::
Deal_Reqtype_FileUpload_BlackBlock(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo)
{
	DEALDATA2(TestDataMngSock_FileUpLoad_BlackBlock, strUserInfo);
}
void 
CSockTestDataMngDeal::
Deal_Reqtype_FileUpload_Xlsx(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo)
{
	DEALDATA2(TestDataMngSock_FileUpLoad_XLSX, strUserInfo);
}
void 
CSockTestDataMngDeal::
Deal_Reqtype_FileUpload_BlackBlock_Update(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo)
{
	DEALDATA2(TestDataMngSock_BlackBlockDocUpdate, strUserInfo);
}
void 
CSockTestDataMngDeal::
Deal_Reqtype_MCFileTime(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo)
{
	DEALDATA2(MCFileTime_Send, strUserInfo);
}
void 
CSockTestDataMngDeal::
Deal_Reqtype_MCCFile(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo)
{
	DEALDATA2(MCCFile_Send, strUserInfo);
}
void 
CSockTestDataMngDeal::
Deal_Reqtype_DocDownload_DIY(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo)
{
	DEALDATA2(TestDataMngSock_DocDownLoad_DIY, strUserInfo);
}
void 
CSockTestDataMngDeal::
Deal_Reqtype_FileDistribute(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo)
{
	DEALDATA2(FileDistribute, strUserInfo);
}

void CSockTestDataMngDeal::InitReqTypeMap(void)
{
    m_mapStdmdReqTypePos.insert(make_pair(REQTYPE_FILEDOWNLOAD, &CSockTestDataMngDeal::Deal_Reqtype_FileDownload));
    m_mapStdmdReqTypePos.insert(make_pair(REQTYPE_FILEDOWNLOADByProject, &CSockTestDataMngDeal::Deal_Reqtype_FileDownloadByProject));
    m_mapStdmdReqTypePos.insert(make_pair(REQTYPE_FILEDOWNLOADByPath, &CSockTestDataMngDeal::Deal_Reqtype_FileDownloadByPath));
    m_mapStdmdReqTypePos.insert(make_pair(REQTYPE_DOCUPLOAD, &CSockTestDataMngDeal::Deal_Reqtype_DocUpload));
    m_mapStdmdReqTypePos.insert(make_pair(REQTYPE_DOCDOWNLOAD, &CSockTestDataMngDeal::Deal_Reqtype_DocDownload));
    m_mapStdmdReqTypePos.insert(make_pair(REQTYPE_FILEDOWNLOAD_BLACKBLOCK, &CSockTestDataMngDeal::Deal_Reqtype_FileDownload_BlackBlock));
    m_mapStdmdReqTypePos.insert(make_pair(REQTYPE_FILEDOWNLOAD_CQTImage, &CSockTestDataMngDeal::Deal_Reqtype_FileDownload_CQTImage));
    m_mapStdmdReqTypePos.insert(make_pair(REQTYPE_FILEUPLOAD_BLACKBLOCK_DELETE, &CSockTestDataMngDeal::Deal_Reqtype_FileUpload_BlackBlock_Delete));
    m_mapStdmdReqTypePos.insert(make_pair(REQTYPE_FILEUPLOAD_BLACKBLOCK, &CSockTestDataMngDeal::Deal_Reqtype_FileUpload_BlackBlock));
    m_mapStdmdReqTypePos.insert(make_pair(REQTYPE_FILEUPLOAD_XLSX, &CSockTestDataMngDeal::Deal_Reqtype_FileUpload_Xlsx));
    m_mapStdmdReqTypePos.insert(make_pair(REQTYPE_FILEUPLOAD_BLACKBLOCK_UPDATE, &CSockTestDataMngDeal::Deal_Reqtype_FileUpload_BlackBlock_Update));
    m_mapStdmdReqTypePos.insert(make_pair(REQTYPE_MCFILETIME, &CSockTestDataMngDeal::Deal_Reqtype_MCFileTime));
    m_mapStdmdReqTypePos.insert(make_pair(REQTYPE_MCCFILE, &CSockTestDataMngDeal::Deal_Reqtype_MCCFile));
    m_mapStdmdReqTypePos.insert(make_pair(REQTYPE_DOCDOWNLOAD_DIY, &CSockTestDataMngDeal::Deal_Reqtype_DocDownload_DIY));
    m_mapStdmdReqTypePos.insert(make_pair(REQTYPE_FILEDISTRIBUTE, &CSockTestDataMngDeal::Deal_Reqtype_FileDistribute));

}



