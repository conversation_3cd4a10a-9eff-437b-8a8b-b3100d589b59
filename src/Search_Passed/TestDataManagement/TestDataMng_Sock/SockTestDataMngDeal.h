// SockTestDataMngDeal.h: interface for the CSockTestDataMngDeal class.
//
//////////////////////////////////////////////////////////////////////

#ifndef __SOCKTESTDATAMNGDEAL_H__
#define __SOCKTESTDATAMNGDEAL_H__

#include "./SockFactory.h"
#include "./TestDataMngSock_Other.h"


class CSockTestDataMngDeal : public CSockFactory, public Singleton<CSockTestDataMngDeal>
{
public:
	CSockTestDataMngDeal();
	virtual ~CSockTestDataMngDeal();
	
public:
    virtual BOOL DealSock(const BYTE* const pData, const int nCount,int& ioffset, const STRU_SockInfo& sockInfo);

private:
	typedef void (CSockTestDataMngDeal::*DealReqTypeData)(const BYTE* const pData, const int nCount, int& ioffset, 
                                                          const STRU_SockInfo& sockInfo, std::string strUserInfo);
	std::map<BYTE, DealReqTypeData> m_mapStdmdReqTypePos;

    void Deal_Reqtype_FileDownload(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo);
    void Deal_Reqtype_FileDownloadByProject(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo);
    void Deal_Reqtype_FileDownloadByPath(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo);
    void Deal_Reqtype_DocUpload(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo);
    void Deal_Reqtype_DocDownload(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo);
    void Deal_Reqtype_FileDownload_BlackBlock(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo);
    void Deal_Reqtype_FileDownload_CQTImage(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo);
    void Deal_Reqtype_FileUpload_BlackBlock_Delete(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo);
    void Deal_Reqtype_FileUpload_BlackBlock(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo);
    void Deal_Reqtype_FileUpload_Xlsx(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo);
    void Deal_Reqtype_FileUpload_BlackBlock_Update(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo);
    void Deal_Reqtype_MCFileTime(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo);
    void Deal_Reqtype_MCCFile(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo);
    void Deal_Reqtype_DocDownload_DIY(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo);
    void Deal_Reqtype_FileDistribute(const BYTE* const pData, const int nCount, int& ioffset, const STRU_SockInfo& sockInfo, std::string strUserInfo);
	void InitReqTypeMap(void);
};

#endif
