// ComDef_UserMng.h: interface for the CComDef_UserMng class.
//
//////////////////////////////////////////////////////////////////////

#ifndef __COMDEF_USERMNG_H__
#define __COMDEF_USERMNG_H__

//user management command
#define REQTYPE_USER_MNG						0xd0	//REQUEST	
#define	RESTYPE_USER_MNG_FUNC					0xd0
#define	RESTYPE_USER_MNG_SUBFUNC				0xd1
#define	RESTYPE_USER_MNG_ROLE					0xd2
#define	RESTYPE_USER_MNG_ROLE_FUNC				0xd3
#define	RESTYPE_USER_MNG_USER_ROLE				0xd4


#define REQTYPE_USERMNG_USER_ADD				0xea	//REQUEST	
#define RESTYPE_USERMNG_USER_ADD				0xea
#define REQTYPE_USERMNG_USER_DEL				0xe1   //REQUEST
#define RESTYPE_USERMNG_USER_DEL				0xe1
#define REQTYPE_USERMNG_USER_UPDATE				0xe2   //REQUEST
#define RESTYPE_USERMNG_USER_UPDATE				0xe2

#define REQTYPE_USERMNG_ROLE_ADD				0xe3	//REQUEST	
#define RESTYPE_USERMNG_ROLE_ADD				0xe3
#define REQTYPE_USERMNG_ROLE_DEL				0xe4   //REQUEST
#define REQTYPE_USERMNG_ROLE_UPDATE				0xe5   //REQUEST

#define REQTYPE_USERMNG_USER_ROLE_ADD			0xe6	//REQUEST	
#define REQTYPE_USERMNG_USER_ROLE_CLEAR			0xe7   //REQUEST

#define REQTYPE_USERMNG_ROLE_FUNC_ADD			0xe8	//REQUEST	
#define REQTYPE_USERMNG_ROLE_FUNC_CLEAR			0xe9   //REQUEST

#define REQTYPE_USERMNG_PERSONAL_UPDATE			0xea	//REQUEST

#endif
