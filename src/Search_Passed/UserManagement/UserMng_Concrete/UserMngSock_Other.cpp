// UserMngSock_Other.cpp: implementation of the CUserMngSock_Other class.
//
//////////////////////////////////////////////////////////////////////

#include "UserMngSock_Other.h"

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

/************************************************************************
CUserMngSock_UserConfig                                                 
************************************************************************/
BOOL  
CUserMngSock_UserConfig::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{

	char tm_sql[512];	
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec sp_usermng_get");
	
	char tm_sql2[512];
	
	//func group 
	PPM_OS::memset(tm_sql2,0,sizeof(tm_sql2));
	PPM_OS::snprintf(tm_sql2,sizeof(tm_sql2),"%s %d",tm_sql,1);
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_USER_MNG_FUNC;
	
	if(NULL != m_pDataBaseDeal_){
		m_pDataBaseDeal_->SearchSql(tm_sql2,this,m_iRowType_);
	}
	Clear();
	
	//subfunc
	PPM_OS::memset(tm_sql2,0,sizeof(tm_sql2));
	PPM_OS::snprintf(tm_sql2,sizeof(tm_sql2),"%s %d",tm_sql,2);
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_USER_MNG_SUBFUNC;
	
	if(NULL != m_pDataBaseDeal_){
		m_pDataBaseDeal_->SearchSql(tm_sql2,this,m_iRowType_);
	}
	Clear();
	
	//role
	PPM_OS::memset(tm_sql2,0,sizeof(tm_sql2));
	PPM_OS::snprintf(tm_sql2,sizeof(tm_sql2),"%s %d",tm_sql,3);
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_USER_MNG_ROLE;
	
	if(NULL != m_pDataBaseDeal_){
		m_pDataBaseDeal_->SearchSql(tm_sql2,this,m_iRowType_);
	}
	Clear();
	
	//role_func
	PPM_OS::memset(tm_sql2,0,sizeof(tm_sql2));
	PPM_OS::snprintf(tm_sql2,sizeof(tm_sql2),"%s %d",tm_sql,4);
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_USER_MNG_ROLE_FUNC;
	
	if(NULL != m_pDataBaseDeal_){
		m_pDataBaseDeal_->SearchSql(tm_sql2,this,m_iRowType_);
	}
	Clear();
	
	//role_user
	PPM_OS::memset(tm_sql2,0,sizeof(tm_sql2));
	PPM_OS::snprintf(tm_sql2,sizeof(tm_sql2),"%s %d",tm_sql,5);
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_USER_MNG_USER_ROLE;
	
	if(NULL != m_pDataBaseDeal_){
		m_pDataBaseDeal_->SearchSql(tm_sql2,this,m_iRowType_);
		//处理结束
		FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
	}
	Clear();
	
	return TRUE;	
}


/************************************************************************
CUserMngSock_UserAdd                                                 
************************************************************************/
BOOL  
CUserMngSock_UserAdd::DealData(const BYTE* const pData, const int nCount,int& ioffset)
{
	MOVENEXTTEST(ioffset,5,nCount);
	
	STRU_USER_INFO tm_User_Info;
	
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_User_Info.szusername,
		sizeof(tm_User_Info.szusername)));
	
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_User_Info.szlogon_code,
		sizeof(tm_User_Info.szlogon_code)));
	
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_User_Info.szlogon_pwd,
		sizeof(tm_User_Info.szlogon_pwd)));
	
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_User_Info.szphone,
		sizeof(tm_User_Info.szphone)));
	
	CHECLFALSE(CSearch_PI::GetTextValue_word(pData,nCount,ioffset,tm_User_Info.szcomment,
		sizeof(tm_User_Info.szcomment)));
	
	m_iRowType_ = TYPE_VEC_NORMAL;
	m_bResponseType_ = RESTYPE_USER_MNG_FUNC;
	
	char tm_sql[512];	
	PPM_OS::snprintf(tm_sql,sizeof(tm_sql),
		"exec sp_usermng_user_add '%s','%s','%s','%s','%s'",
		tm_User_Info.szusername, 
		tm_User_Info.szlogon_code,
		tm_User_Info.szlogon_pwd,
		tm_User_Info.szphone,
		tm_User_Info.szcomment
		);
	
	if(NULL != m_pDataBaseDeal_){
		m_pDataBaseDeal_->SearchSql(tm_sql,this,m_iRowType_);
		//处理结束
		FeedBackOver(CMD1_SEARCH,RESTYPE_SEARCHEND);
	}
	Clear();
	
	return TRUE;	
}






