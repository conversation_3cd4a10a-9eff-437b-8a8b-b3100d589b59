// UserMngSock_Other.h: interface for the CUserMngSock_Other class.
//
//////////////////////////////////////////////////////////////////////

#ifndef __USERMNGSOCK_OTHER_H__
#define __USERMNGSOCK_OTHER_H__

#include "./SearchSock.h"
   
class CUserMngSock_UserConfig : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};


class CUserMngSock_UserAdd : public CSearchSock
{
public:
	virtual BOOL DealData(const BYTE* const pData, const int nCount,int& ioffset);
};


#endif
