// SockUserMngDeal.cpp: implementation of the CSockUserMngDeal class.
//
//////////////////////////////////////////////////////////////////////

#include "./SockUserMngDeal.h"

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CSockUserMngDeal::CSockUserMngDeal()
{
}

CSockUserMngDeal::~CSockUserMngDeal()
{

}

BOOL
CSockUserMngDeal::DealSock(const BYTE* const pData, const int nCount,int& ioffset, const STRU_SockInfo& sockInfo)
{
	char tm_addr[512];
	sockInfo.pServerDeal_->m_ptcpclient_->GetInetAddr()->addr_to_string(tm_addr, sizeof(tm_addr), 1);
	std::string strUserInfo = "[" + std::string(tm_addr) + "]";

	if(CMD2_REQUEST == pData[ioffset])//请求
	{
		MOVENEXT(ioffset,1,nCount)
		switch(pData[ioffset++]){
		case REQTYPE_USER_MNG:
			{
				DEALDATA2(CUserMngSock_UserConfig, strUserInfo);	
			}
			break;
		case REQTYPE_USERMNG_USER_ADD:
			{
				DEALDATA2(CUserMngSock_UserAdd, strUserInfo);	
			}
			break;
			
		default: 
			return FALSE;
			break;
		}
	}
	else //响应 
	{
	}
	return TRUE;
}

