// SockUserMngDeal.h: interface for the CSockUserMngDeal class.
//
//////////////////////////////////////////////////////////////////////

#ifndef __SOCKUSERMNGDEAL_H__
#define __SOCKUSERMNGDEAL_H__

#include "./SockFactory.h"

#include "./UserMngSock_Other.h"

class CSockUserMngDeal : public CSockFactory, public Singleton<CSockUserMngDeal>
{
public:
	CSockUserMngDeal();
	virtual ~CSockUserMngDeal();
	
public:
    virtual BOOL DealSock(const BYTE* const pData, const int nCount,int& ioffset, const STRU_SockInfo& sockInfo);
	
};

#endif
