#include "./Utility.h"
#include "./linux_api.h"
#include "Strutils.h"
#include "DESCrypto.h"
#include "md5.h"
#include <iconv.h>

const double _DX = 40075360.0; //赤道长度，米
const double _DY = 39940670.0; //子午线长度，米
const double PI = 3.14159;
const char ModeType[7][8] = {"NO", "IDLE", "DEDIC", "LIMIT", "SCAN", "PACKET", "PACKETI"};

const double CI[255] = 
{
	-6,-6,-6,-6,-6,-6,-6,-6,-6,-6,
	-4.9,-4.4,-3.9,-3.5,-3.1,-2.7,-2.3,-2,-1.7,-1.4,
	-1.1,-0.9,-0.6,-0.4,-0.2,0.1,0.3,0.5,0.7,0.9,
	1.1,1.25,1.42,1.59,1.75,1.91,2.06,2.21,2.35,2.5,
	2.64,2.77,2.9,3.03,3.15,3.25,3.39,3.51,3.62,3.74,
	3.85,3.95,4.06,4.16,4.26,4.37,4.46,4.56,4.65,4.75,
	4.84,4.93,5.02,5.1,5.19,5.27,5.36,5.44,5.52,5.6,
	5.68,5.76,5.83,5.9,5.98,6.05,6.12,6.19,6.26,6.32,
	6.39,6.46,6.52,6.58,6.65,6.71,6.78,6.84,6.91,6.98,
	7.05,7.12,7.2,7.27,7.35,7.42,7.5,7.58,7.65,7.72,
	7.8,7.87,7.93,7.99,8.05,8.11,8.17,8.23,8.28,8.33,
	8.39,8.42,8.5,8.55,8.6,8.65,8.7,8.75,8.8,8.85,
	8.9,8.95,9.01,9.06,9.11,9.16,9.21,9.26,9.31,9.36,
	9.4,9.46,9.5,9.55,9.6,9.65,9.7,9.75,9.79,9.84,
	9.89,9.94,9.99,10.04,10.09,10.14,10.19,10.24,10.29,10.34,
	10.39,10.44,10.49,10.54,10.59,10.64,10.69,10.74,10.79,10.84,
	10.88,10.93,10.98,11.02,11.07,11.11,11.16,11.2,11.24,11.28,
	11.33,11.37,11.41,11.45,11.5,11.54,11.58,11.62,11.66,11.7,
	11.75,11.79,11.83,11.89,11.92,11.96,12,12.05,12.09,12.13,
	12.18,12.22,12.27,12.31,12.35,12.4,12.44,12.49,12.53,12.58,
	12.64,12.67,12.72,12.76,12.81,12.86,12.91,12.96,13.01,13.06,
	13.11,13.17,13.22,13.27,13.33,13.38,13.44,13.5,13.56,13.62,
	13.68,13.74,13.81,13.87,13.93,14,14.07,14.14,14.21,14.27,
	14.35,14.42,14.5,14.58,14.66,14.75,14.84,14.94,15.06,15.2,
	15.4,15.67,15.96,16.2,16.45,16.75,17.08,17.54,18.12,18.8,
	19.99,20,20,20,20
};

int CUtility::GetExeId(void)
{
	// --通过程序所在的路径，组合出唯一的标识
	int nCode = 0;
	
	char szProcessPath[MAX_PATH];
	memset(szProcessPath, 0, sizeof(szProcessPath));
	if (Clinux_api::get_executable_fullname(szProcessPath) != 0)
	{
		return -1;
	}

	for (size_t m = 0; m < strlen(szProcessPath); m++)
	{
		nCode = 31 * nCode + szProcessPath[m];
	}
	
	return nCode;
}

std::string CUtility::OutPutInfo(const char* fmt, ...)
{
	va_list marker;
	va_start( marker, fmt );
	
	char tmpBuff[2560] = {0};
	
	vsnprintf(tmpBuff, sizeof(tmpBuff), fmt, marker); 
	
	va_end( marker ); 
	
	return tmpBuff;
}

std::string CUtility::S_Printf(const char* fmt, ...)
{
	va_list marker;
	va_start( marker, fmt );
	
	char tmpBuff[2560] = {0};
	
	vsnprintf(tmpBuff, sizeof(tmpBuff), fmt, marker); 
	
	va_end( marker ); 
	
	return tmpBuff;
}

std::string CUtility::GetCurrTime(void)
{
	tSTRU_TIME_VAL tval = GetMilliseconds();

	return GetStrTime(tval.tv_sec, tval.tv_usec);
}

tSTRU_TIME_VAL CUtility::GetMilliseconds(void)
{
	struct timeval tv;
	struct timezone tz;

	gettimeofday(&tv, &tz);

	tSTRU_TIME_VAL tTemp;
	tTemp.tv_sec = tv.tv_sec;
	tTemp.tv_usec = tv.tv_usec / 1000;

	return tTemp;
}

// 2015-07-02 10:38:30.387
tSTRU_TIME_VAL CUtility::GetMilliseconds(const char* c_Time)
{
	std::string str(c_Time);
	struct tm tmCurr;
	char tmp[8];
	int npos = 0;
		
	tSTRU_TIME_VAL tTemp;
	tTemp.Clear();

	if (str.length() < 23)
	{
		return tTemp;
	}
	
	memset(tmp, 0, sizeof(tmp));
	memcpy(tmp, str.substr(npos, 4).c_str(), 4);
	tmCurr.tm_year = atoi(tmp) - 1900;
	npos += 5;
	
	memset(tmp, 0, sizeof(tmp));
	memcpy(tmp, str.substr(npos, 2).c_str(), 2);
	tmCurr.tm_mon = atoi(tmp) - 1;
	npos += 3;
	
	memset(tmp, 0, sizeof(tmp));
	memcpy(tmp, str.substr(npos, 2).c_str(), 2);
	tmCurr.tm_mday = atoi(tmp);
	npos += 3;
	
	memset(tmp, 0, sizeof(tmp));
	memcpy(tmp, str.substr(npos, 2).c_str(), 2);
	tmCurr.tm_hour = atoi(tmp);
	npos += 3;

	memset(tmp, 0, sizeof(tmp));
	memcpy(tmp, str.substr(npos, 2).c_str(), 2);
	tmCurr.tm_min = atoi(tmp);
	npos += 3;

	memset(tmp, 0, sizeof(tmp));
	memcpy(tmp, str.substr(npos, 2).c_str(), 2);
	tmCurr.tm_sec = atoi(tmp);
	npos += 3;

	tTemp.tv_sec = mktime(&tmCurr);

	memset(tmp, 0, sizeof(tmp));
	memcpy(tmp, str.substr(npos, 3).c_str(), 3);
	tTemp.tv_usec= atoi(tmp);

	return tTemp;
}

time_t CUtility::GetXMonthTime(int nMonthBefor)
{
	time_t nSec = time(nullptr);
	struct tm when = *localtime(&nSec);
	when.tm_mon -= nMonthBefor;
	
	return mktime(&when);
}

std::string CUtility::Stat_GsmTCH(BYTE iMode, BYTE TCHCount, int* pTCH)
{
	// char strValue[100];

	// memset(strValue, 0, sizeof(strValue));

	std::string strValue = "";

	if (TCHCount == 1)
	{
		strValue = OutPutInfo("%04X%04X", 1, (WORD)pTCH[0]);
		// sprintf(strValue, "%04X%04X", 1, (WORD)pTCH[0]);	
	}
	else if (TCHCount > 1)
	{
		if (TCHCount > 16)
		{
			TCHCount = 16;
		}

		strValue = OutPutInfo("%04X", TCHCount-1);
		// sprintf(strValue, "%04X", TCHCount-1);
		for(int i = 1; i < TCHCount; i++)
		{
			strValue = OutPutInfo("%s%04X", strValue, (WORD)pTCH[i]);
			// sprintf(strValue, "%s%04X", strValue, (WORD)pTCH[i]);	
		}
	}
	
	return (std::string)strValue;
}

std::string CUtility::GetStrTime(time_t nSec, int nMsec)
{
	char szlog[64] = "";

	struct tm when = *localtime(&nSec);

	sprintf(szlog, "%d-%02d-%02d %02d:%02d:%02d.%03d",
		when.tm_year - 100 + 2000,
		when.tm_mon + 1,
		when.tm_mday,
		when.tm_hour,
		when.tm_min,
		when.tm_sec,
		nMsec);

	return std::string(szlog);
}

std::string CUtility::GetStrTime(tSTRU_TIME_VAL tTimeVal)
{
	if (tTimeVal.tv_sec < 0)
	{
		return " ";
	}

	return GetStrTime(tTimeVal.tv_sec, tTimeVal.tv_usec);
}

bool CUtility::GetStruTM(char* const szTimeInfo, tm& when, BYTE* const pDate)
{
	memset(&when, 0, sizeof(when));

	// 2015-02-23 13:18:09
	if (strlen(szTimeInfo) < 19)
	{
		return false;
	}

	std::string str(szTimeInfo);

	int i = 0;
	int nPos = 0;
	char tmp[8];
	memset(tmp, 0, sizeof(tmp));
	memcpy(tmp, str.substr(nPos, 4).c_str(), 4);
	when.tm_year = atoi(tmp) - 1900;
	pDate[i++] = (when.tm_year - 100);
	nPos += 5;
	
	memset(tmp, 0, sizeof(tmp));
	memcpy(tmp, str.substr(nPos, 2).c_str(), 2);
	when.tm_mon = atoi(tmp) - 1;
	pDate[i++] = (when.tm_mon + 1);
	nPos += 3;

	memset(tmp, 0, sizeof(tmp));
	memcpy(tmp, str.substr(nPos, 2).c_str(), 2);
	when.tm_mday = atoi(tmp);
	pDate[i++] = (when.tm_mday);
	nPos += 3;
	
	memset(tmp, 0, sizeof(tmp));
	memcpy(tmp, str.substr(nPos, 2).c_str(), 2);
	when.tm_hour = atoi(tmp);
	pDate[i++] = (when.tm_hour);
	nPos += 3;

	memset(tmp, 0, sizeof(tmp));
	memcpy(tmp, str.substr(nPos, 2).c_str(), 2);
	when.tm_min = atoi(tmp);
	pDate[i++] = (when.tm_min);
	nPos += 3;

	memset(tmp, 0, sizeof(tmp));
	memcpy(tmp, str.substr(nPos, 2).c_str(), 2);
	when.tm_sec = atoi(tmp);
	pDate[i++] = (when.tm_sec);
	nPos += 3;

	mktime(&when);

	return true;
}

std::string CUtility::GetStrDataForm(const time_t nTime, const std::string strToken)
{
	time_t totalsec = nTime;

	if (totalsec == 0)
	{
		time(&totalsec);
	}

	struct tm tmCurr = *localtime(&totalsec);
	
	char szData[MAX_PATH];
	
	if (strToken.length() > 0)
	{
		sprintf(szData, "%4d%s%02d%s%02d",
			tmCurr.tm_year + 1900,
			strToken.c_str(),
			tmCurr.tm_mon + 1, 
			strToken.c_str(),
			tmCurr.tm_mday);
	}
	else
	{
		sprintf(szData, "%4d%02d%02d",
			tmCurr.tm_year + 1900, 
			tmCurr.tm_mon + 1, 
			tmCurr.tm_mday);
	}
	
	return ((std::string)szData);
}

std::string CUtility::GBKToUtf8(char* pSrc)
{	
	char dest[1024];
	memset(dest, 0, sizeof(dest));

	iconv_t cd = iconv_open("utf-8","GBK");

	if(cd ==0)
	{
		return "";
	}
	
	size_t srclen = strlen(pSrc);
	
	size_t dstlen = sizeof(dest) - 1;
	char* s = pSrc;
	char* d = dest;
	iconv(cd, (char**)(&s), &srclen, &d ,&dstlen);
	
	iconv_close(cd);

	return ((std::string)dest);
}

std::string CUtility::Utf8ToGBK(char* pSrc)
{	
	char dest[1024];
	memset(dest, 0, sizeof(dest));

	iconv_t cd = iconv_open("GBK", "utf-8");

	if(cd ==0)
	{
		return "";
	}
	
	size_t srclen = strlen(pSrc);
	
	size_t dstlen = 1024;
	char* s = pSrc;
	char* d = dest;
	iconv(cd, (char**)(&s), &srclen, &d ,&dstlen);
	
	iconv_close(cd);

	return ((std::string)dest);
}

bool CUtility::Rename(std::string strOrgFilePath, std::string strNewFilePath)
{
	if (CStdFile::IsExist(strOrgFilePath.c_str()))
	{
		if (CStdFile::Rename(strOrgFilePath.c_str(), strNewFilePath.c_str()) != 0)
		{
			CStdFile::Remove(strNewFilePath.c_str());
			
			CStdFile::Rename(strOrgFilePath.c_str(), strNewFilePath.c_str());
		}
	}
	
	if (CStdFile::IsExist(strNewFilePath.c_str()))
	{
		return true;
	}
	
	return false;
}

std::string CUtility::TrimNumStrLeftZero(std::string strnum)
{
	std::string strRet = strnum;
	int num = atoi(strnum.c_str());
	if (num < 10 && num > 0)
	{
		strRet = strnum.substr(1);
	}

	return strRet;
}

void CUtility::ReplaceCommaMark(char* const pStr, std::string strNewToken)
{
	int nLen = strlen(pStr);
	
	int nNewLen = nLen;
	// 把字符串中为逗号的分隔符替换为分号
	// 如：UE Radio Capability  'eutra,utra_1,geran_cs,geran_ps'
	for (int i = 0; i < nLen && pStr[i] != 0; i++)
	{
		if (pStr[i] == '\'')
		{
			i++;
			
			while (pStr[i] != '\'' && i < nLen && pStr[i] != 0)
			{
				if (pStr[i] == ',')
				{
					memmove(pStr + i + strNewToken.length(), pStr + i + 1, nNewLen - i);
					
					for (std::size_t j = 0; j < strNewToken.length(); j++)
					{
						pStr[i] = strNewToken.at(j);
						
						i++;
						nNewLen++;
					}
				}
				else
				{
					i++;
				}
			}
			
			if (pStr[i] =='\'')
			{
				i++;
			}
		}
	}
}

//从文件路径+文件名获取文件名
std::string CUtility::GetFileName(const std::string strSrcFileName)
{
	std::string strfile("");

	char dir[MAX_DIR];
	char fname[MAX_FNAME];
	char ext[MAX_EXT];

	split_name(strSrcFileName.c_str(), dir, fname, ext);

	strfile.assign(fname);
	
	return strfile;
}

//从文件路径+文件名获取文件名
std::string CUtility::GetFileNameWithExtension(const std::string strSrcFileName)
{
	std::string strfile("");

	char dir[MAX_DIR];
	char fname[MAX_FNAME];
	char ext[MAX_EXT];

	split_name(strSrcFileName.c_str(), dir, fname, ext);

	strfile.assign(fname);
	strfile += std::string(ext);
	
	return strfile;
}

void CUtility::split_name(const char *szfullfilename, char *szpathname, char *szfilename, char *szextname)
{
	int i, j;

	i = 0;
	while (szfullfilename[i] != '\0'){i++;}

	if (strchr(szfullfilename, '.') != nullptr)
	{
		while (szfullfilename[i] != '.'){i--;}
	}

	j = 0;
	// i++; // 要不要显示小圆点：例如：注释掉显示.dat， 放开显示dat
	while((szextname[j] = szfullfilename[i]) != '\0')
	{
		i++;
		j++;
	}

	i -= j;

	while (szfullfilename[i] != '/'){i--;}

	for (j = 0; j <= i; j++)
	{
			szpathname[j] = szfullfilename[j];
	}

	szpathname[j] = '\0';

	j = 0;
	i++;
	while((szfilename[j] = szfullfilename[i]) != '\0')
	{
		i++;
		j++;
	}
}


void CUtility::TrimMidBlank(char* const pStr, std::size_t nsize)
{
	std::string str("");

	const int nLen = strlen(pStr);

	for (int i = 0; i < nLen; i++)
	{
		if ((pStr[i] != ' ') 
			&& (pStr[i] != '\r')
			&& (pStr[i] != '\n'))
		{
			str += pStr[i];
		}
	}
	
	strcpy(pStr, str.c_str());

	if (str.length() >= (nsize - 2))
	{
		pStr[nsize - 2] = 0;
		pStr[nsize - 1] = 0;
	}
	else
	{
		pStr[str.length()] = 0;
		pStr[str.length() + 1] = 0;
	}
}

int CUtility::HexStrToByteWithoutMidBlank(char* const pStr, BYTE* const pBinary, BYTE btype)
{
	int nLen = strlen(pStr);

	if (nLen == 0)
	{
		return 0;
	}

	for (int j = nLen - 1; j >= 0; j--)
	{
		if ((pStr[j] == '\r') 
			|| (pStr[j] == '\n')
			|| (pStr[j] == ' '))
		{
			pStr[j] = 0;
		}
		else
		{
			break;
		}
	}

	nLen = strlen(pStr);

	if (btype == 0)
	{
		if (nLen > (8192 - 1) * 2)
		{
			nLen = (8192 - 1) * 2;
		}
		else
		{
			//do nothing...
		}
	}

	int nValue = -1;

	char szByte[3];

	for (int i = 0; i < nLen; i = i + 2)
	{
		szByte[0] = pStr[i];
		szByte[1] = pStr[i + 1];
		szByte[2] = 0;

		sscanf(szByte, "%X", &nValue);

		if (nValue == -1)
		{
			break;
		}

		pBinary[i / 2] = nValue;
	}
	
	return nLen / 2;
}

//参数：纬度1，经度1，纬度2，经度2
double CUtility::GetDistance(double lng1, double lat1, double lng2, double lat2)
{
	//计算y1, y2所在位置的纬度圈长度
	double dx1 = _DX * sin((90.0 - lat1) * 2 * PI / 360.0);
	double dx2 = _DX * sin((90.0 - lat2) * 2 * PI / 360.0);

	double dx = (dx1 + dx2) / 2.0 * (lng1 - lng2) / 360.0;
	double dy = _DY * (lat2 - lat1) / 360.0;

	return sqrt(dx*dx + dy*dy);
}

bool CUtility::DesDecrypt(char* pStrInfo, bool bCompatiblePlain)
{
	std::string strTemp(pStrInfo);
	
	// 解密密钥
	std::string strRst(CDESCrypto::Instance()->DES_Decrypt(strTemp.c_str()));
	
	sprintf(pStrInfo, "%s", strRst.c_str());
	
	if (bCompatiblePlain)
	{
		// 没加密
		if (strRst.length() == 0)
		{
			sprintf(pStrInfo, "%s", strTemp.c_str());
		}
	}
	else
	{
		// 没加密
		if (strRst.length() == 0)
		{						
			return false;
		}
	}
	
	return true;
}

std::string CUtility::DesEncrypt(char* pStrInfo)
{
	return CDESCrypto::Instance()->DES_Encrypt(pStrInfo);
}

std::string CUtility::Md5Encrypt(std::string pStrInfo)
{
	return md5(pStrInfo);
}

std::string CUtility::Md5Encrypt(const void* dat, size_t len)
{
	return md5(dat, len);
}

unsigned int CUtility::HashCode(const std::string strAbsolutePath, 
							   unsigned int &filesize, 
							   CStdLog* const pLog,
							   const bool bAloneIdEnable,
							   std::vector<int>* const pAloneIdProjects,
							   const std::string strRelativePath,
							   const int nProjectId)
{
	// 构造fileid
	CStdFile file;

	if (!file.Open(strAbsolutePath.c_str(), "rb"))
	{
		if (pLog != nullptr)
		{
			OUTPUT_LOG2((*pLog), "计算ifileid, 打开文件失败", strAbsolutePath.c_str());
		}

		return 0;
	}

	size_t j = 0;

	unsigned int nCode = 0;
	size_t nSize = 0;
	filesize = 0;

	BYTE Data[8192];

	memset(Data, 0, 8192);

	do
	{
		nSize = file.Read(Data, 8192);
		filesize += nSize;
		if (file.IsError())
		{
			file.Close();

			return 0;
		}

		for (j = 0; j < nSize; j++) 
		{
			nCode = 31 * nCode + Data[j];
		}
	}while(nSize > 0);

	file.Close();

	/** 广东省移动提的需求 [6/4/2015 zhs]
	 * 指定项目的文件不覆盖别的项目的文件
	 * 别的项目的文件也不覆盖此项目的文件
	*/
	bool bIsAloneFileId = false;
	std::string strTemp(CUtility::GetFileName(strAbsolutePath));

	if (bAloneIdEnable
		&& pAloneIdProjects != nullptr)
	{
		for (j = 0; j < pAloneIdProjects->size(); j++)
		{
			if (nProjectId == pAloneIdProjects->at(j))
			{
				bIsAloneFileId = true;
				
				// 带上相对的文件路径+文件名
				strTemp.assign(strRelativePath);
				
				break;
			}
		}
	}

	CStrutils::ToUpperString(strTemp);
	// 此类文件很小，单靠内容容易重复，所以加名称
	if ((strTemp.find("MTRFIL") != std::string::npos)
		|| (strTemp.find("CTRFIL") != std::string::npos)
		|| filesize <= 1048576
		|| bIsAloneFileId)
	{
		for (size_t m = 0; m < strTemp.length(); m++)
		{
			nCode = 31 * nCode + strTemp[m];
		}
	}

	// 由于开始是用了int保存filesize，故现在还得用int，否则文件大小超过2G值不一样。 [11/12/2015 zhs]
	// 作为引用的形参，不能直接强行转换数据类型，应该加上地址符，否则转出来的是地址。((int)(*(&filesize))) [11/12/2015 zhs]
	// 由于之前超过2G的文件大小的值为负的，故会把文件名作为ifileid的一部分了。现版本做了修正，故超过2G的文件重入id会不同。 [11/12/2015 zhs]
	return nCode * 31 + filesize;
}

bool CUtility::FileIsUsed(std::string fileFullName)
{	
	 
	bool result = false;
	
	//判断文件是否存在，如果不存在，直接返回 false
	if (!CStdFile::IsExist(fileFullName.c_str()))
	{
		result = false;
	}//end: 如果文件不存在的处理逻辑
	else
	{//如果文件存在，则继续判断文件是否已被其它程序使用
		//逻辑：尝试执行打开文件的操作，如果文件已经被其它程序使用，则打开失败，抛出异常，根据此类异常可以判断文件是否已被其它程序使用。
		/**System.IO.FileStream fileStream = null;*/
		try
		{			
			FILE* m_hFile = fopen(fileFullName.c_str(), "rb");
			if(nullptr == m_hFile)
			{
        //文件不可写
				result = true;
				
				OUTPUT_ERR(OutPutInfo("[Waring]: 文件正在被占用! [%s]", fileFullName.c_str()).c_str());
			}
			else
			{
				fclose(m_hFile);
			}
		}
		catch (...)
		{
			result = true;
		}
		
	}//end: 如果文件存在的处理逻辑
	//返回指示文件是否已被其它程序使用的值
	return result;
}//end method FileIsUsed

bool CUtility::FileIsExiset(std::string& strPathFile, std::string& strNewDot)
{
	bool bFileIsExiset = false;
	if (CStdFile::IsExist(strPathFile.c_str()))
	{
		bFileIsExiset = true;
	}
	else
	{
		std::string tmp = strPathFile + ".rar";
		
		if (CStdFile::IsExist(tmp.c_str()))
		{
			strNewDot = ".rar";
			
			strPathFile = tmp;
			
			bFileIsExiset = true;
		}
		else
		{
			tmp = strPathFile + ".zip";
			if (CStdFile::IsExist(tmp.c_str()))
			{
				strNewDot = ".zip";
				
				strPathFile = tmp;
				
				bFileIsExiset = true;
			}
		}
	}
	
	return bFileIsExiset;
}

int CUtility::pmkdir(const char *path)
{
    std::size_t len = strlen(path);
    if (PATH_MAX <= len) {
        return -3;
    }

    char name[PATH_MAX]{0};
    strcpy(name, path);
    len = strlen(name);

    if (name[len-1]!='/') {
        strcat(name, "/");
    }

    len = strlen(name);

    for (std::size_t i = 1 ; i < len ; i++) {
        if (name[i]=='/') {
            name[i] = 0;
            if ( access(name, 0) !=0 ) {
                if (mkdir(name, S_IRWXU | S_IRWXG | S_IRWXO) == -1) {
                    return -1;
                }
            }
            name[i] = '/';
        }
    }

    return 0;
}

int CUtility::UnZipFile(const char* unzip_file_in, char* unzip_path_out)
{
	if ((unzip_file_in == nullptr) 
		|| (unzip_file_in != nullptr && strlen(unzip_file_in) == 0))
	{
		return -1;
	}

	char zip_file_out_tmp[MAX_PATH]{};
	// create unzip path
	if ((unzip_path_out == nullptr) 
		|| (unzip_path_out != nullptr && strlen(unzip_path_out) == 0))
	{
		size_t ncount = strrchr(unzip_file_in, '/') - unzip_file_in + 1;
		strncpy(zip_file_out_tmp, unzip_file_in, ncount);

		if (unzip_path_out != nullptr)
		{
			strcpy(unzip_path_out, zip_file_out_tmp);
		}		
	}
	else
	{
		strcpy(zip_file_out_tmp, unzip_path_out);
	}

    int err = 0;
    char strerr[1024]{0};

    size_t len = strlen(zip_file_out_tmp);

    if (PATH_MAX <= len) {
        return -3;
    }

    char dest_path[ PATH_MAX ]{0};
    strcpy(dest_path, zip_file_out_tmp);

    if (dest_path[len-1]!='/') {
        strcat(dest_path, "/");
    }
 
    len = strlen(dest_path);
    
    struct zip *z = zip_open(unzip_file_in, ZIP_CREATE, &err);
    if (z != nullptr) {
        zip_int64_t i, c = zip_get_num_entries(z, ZIP_FL_UNCHANGED);
        for (i=0; i<c ; i++) {
            const char * name = zip_get_name(z, i, ZIP_FL_ENC_GUESS);

            size_t name_len = strlen(name);
            if (PATH_MAX <= (len + name_len)) {
                return -3;
            }
            // add dest path
            char temp_path[ PATH_MAX ]{0};
            strcpy(temp_path, dest_path);
            strcat(temp_path, name);

            char *d = strdup(temp_path);
            if (d != nullptr) {
                char *p = strrchr(d, '/');
                if(p != nullptr) {
                    *p = '\0';
                    if ((err = pmkdir(d)) != 0) {
                        free(d);
                        return err;
                    }
                }
                free(d);
 
                FILE *fp = fopen(temp_path, "w+b");
                struct zip_file *f = zip_fopen(z, name, 0);
                if (f != nullptr && fp != nullptr) {
                    zip_int64_t j, n = 0;
                    char buf[8192]{0};
                    while ((n = zip_fread(f, buf, sizeof(buf))) > 0) {
                        for (j=0;j<n;j++) {
                            putc(buf[j], fp);
                        }
                    }
                    fclose(fp);
                    zip_fclose(f);
                }
            } else {
                return -1;
            }
        }
 
        err = zip_close(z);
    } else {
        zip_error_to_str(strerr, 1024, err, errno);
        return -2;
    }
 
    return 1;
}
 
int CUtility::search_dir(struct zip *z, const char* org_path, const char* cur_path, const char * name )
{
    struct stat _stbuf;
    
    char tmp_path[ PATH_MAX ]{0};
    strcpy(tmp_path, cur_path);
    strncat( tmp_path, name, sizeof( tmp_path ));
 
    if( stat( tmp_path, &_stbuf ) == 0 ) {
 
        if( S_ISDIR( _stbuf.st_mode )) {
            DIR * _dir;
            struct dirent * _file;
            
            _dir = opendir( tmp_path );
 
            if( _dir ) { 
                strncat( tmp_path, "/", sizeof( tmp_path ) - 1);
 
                bool isEmptyPath = true;

                while(( _file = readdir( _dir )) != nullptr ) {
                    if( strncmp( _file->d_name, ".", 1 ) != 0 ) {
                        search_dir(z, org_path, tmp_path, _file->d_name);
                        isEmptyPath = false;
                    }
                }

                if (isEmptyPath) {
                    //Caution : no need to do this unless there is a empty folder
                    size_t name_offset = strlen(org_path);
                    char* name_ptr = tmp_path + name_offset;
                    zip_dir_add(z, name_ptr, ZIP_FL_ENC_GUESS); 
                }
 
                closedir( _dir );
            }
            else {
                return -1;
            }
        }
        else { 
            struct zip_source *s = zip_source_file(z, tmp_path, 0, -1);
            if(s != nullptr) {
                size_t name_offset = strlen(org_path);
                char* name_ptr = tmp_path + name_offset;

                zip_file_add(z, name_ptr, s, ZIP_FL_OVERWRITE|ZIP_FL_ENC_GUESS);
                //would be used and freed by zip_close(),
                //so don't free the zip_source here.
                //zip_source_free(s);
            } else {
                return -1;
            }
        }
    }
    else {
        return -2;
    }

		return 1;
}

int CUtility::ZipFile(const char * zip_file_in, char * zip_file_out)
{
		if ((zip_file_in == nullptr) 
			|| (zip_file_in != nullptr && strlen(zip_file_in) == 0))
		{
			return -1;
		}

		char zip_file_out_tmp[MAX_PATH];
		// create dest path
		if ((zip_file_out == nullptr) 
			|| (zip_file_out != nullptr && strlen(zip_file_out) == 0))
		{
			strcpy(zip_file_out_tmp, zip_file_in);
			strcat(zip_file_out_tmp, ".zip");

			if (zip_file_out != nullptr)
			{
				strcpy(zip_file_out, zip_file_out_tmp);
			}
		}
		else
		{
			strcpy(zip_file_out_tmp, zip_file_out);
			if (zip_file_out_tmp[strlen(zip_file_out_tmp) - 1] != '/') {
					strcat(zip_file_out_tmp, "/");
			}
				
			std::string fname = GetFileName(zip_file_in);
			strcat(zip_file_out_tmp, fname.c_str());
			strcat(zip_file_out_tmp, ".zip");
		}

    int err = 0;
    char strerr[1024];
    // create dest path
    char *d = strdup(zip_file_out_tmp);
    if (d != nullptr) {
        char *p = strrchr(d, '/');
        if(p != nullptr) {
            *p = '\0';
            if ((err = pmkdir(d)) != 0) {
                free(d);
                return err;
            }
        }
        free(d);
    } else {
        return -1;
    }

    // get org path
    size_t in_len = strlen(zip_file_in);
    int copy_len = 0;
    if (zip_file_in[in_len - 1] != '/') {
        const char * ptr = strrchr(zip_file_in, '/');
        copy_len = ptr - zip_file_in + 1;
    } else {
        char parent_path[PATH_MAX]{0};
        strncpy(parent_path, zip_file_in, in_len - 1);
        const char * ptr = strrchr(parent_path, '/');
        copy_len = ptr - zip_file_in + 1;
    }
    
    char org_path[PATH_MAX]{0};
    strncpy(org_path, zip_file_in, copy_len);
    
    // create dest zip file
    struct zip *z = zip_open(zip_file_out_tmp, ZIP_CREATE|ZIP_TRUNCATE, &err);

    if (z != nullptr) {
        search_dir(z, org_path, "", zip_file_in);
        err = zip_close(z);
    }else{
        return -2;
    }
    
    if (err != 0) {
        zip_error_to_str(strerr, 1024, err, errno);
        return -1;
    }

    return 1;
}

int CUtility::Hex_char_value(char c)
{   
    if(c >= '0' && c <= '9')   
	{
        return c - '0';   
	}
    else if(c >= 'a' && c <= 'f') 
	{
        return c - 'a' + 10;   
	}
    else if(c >= 'A' && c <= 'F') 
	{
        return c - 'A' + 10;  
	}

    return 0;   
}

int CUtility::BinaryStrToInt(const char* pStr)
{
	int nValue = 0;

	int nLen = strlen(pStr);

	for (int k = 0; k < nLen; k++)
	{
		nValue |= ((pStr[k] - '0') << (nLen - 1 - k));
	}

	if (nLen == 0)
	{
		nValue = -1;
	}

	return nValue;
}

void CUtility::ByteToBinary(const BYTE* const pByte, const int nLen, std::string& strBinary)
{
	strBinary.assign("");

	BYTE bitmask = 0;

	for (int i = 0; i < nLen; i++)
	{	
		bitmask = 0x80;		// binary: 10000000

		for (int j = 0; j < 8 ; j++)
		{
			strBinary += ((pByte[i] & (bitmask >> j)) ? '1' : '0');
		}
	}
}

//////////////////////////////////////////////////////////////////////////
// 获取时隙数：(nTs:时隙, nCount:时隙数)
//////////////////////////////////////////////////////////////////////////
void CUtility::GetTimeslot(BYTE nTs, BYTE& nPos, int& nCount)
{
	nCount = 0;
	if (nTs == 0xFF)
	{
		nPos = 0;
		nCount = 0;
		return;
	}
	
	BYTE nMask = 0x01;
	for (BYTE i = 0; i < 8; i++)
	{
		if (nTs & nMask)
		{
			nPos = i;
			nCount++;
			
			//Time Slot = (7 - nPos)
		}
		nMask = nMask << 1;
	}
}

bool CUtility::GetCol(std::vector<std::string>& vecCol, char* const pStr, char* const cSign, const BYTE nSign)
{
	vecCol.clear();

	if (strlen(pStr) <= 0)
	{
		return false;
	}
	
	int npos = 0;
	const WORD nLen = strlen(pStr);
	
	for (int i = 0; i <= nLen; i++)
	{
		for (int j = 0; j < nSign; j++)
		{
			if (pStr[i] == 0 && i < nLen)
			{
				continue;
			}

			if (pStr[i] == cSign[j])
			{
				pStr[i] = 0;
				vecCol.push_back(std::string(pStr + npos));
				npos = i + 1;
			}
			else if (pStr[i] == '\r')
			{
				pStr[i] = 0;
				vecCol.push_back(std::string(pStr + npos));
				break;
			}
			else if (pStr[i] == 0)
			{
				pStr[i] = 0;
				vecCol.push_back(std::string(pStr + npos));
				npos = i + 1;
			}

			if (npos >= nLen)
			{
				break;
			}
		}
	}
	
	if (vecCol.size() > 0)
	{
		return true;
	}

	return false;
}

/* szcomment根据Key值追加描述值信息 */
void CUtility::AddCommentForKey(char* strcomment, const char* strKey, const char* szValue)
{
	if ((strlen(strcomment) + strlen(szValue) + 1) < MAX_PATH)
	{
		char comment_tmp[MAX_PATH] = "";
		memcpy(comment_tmp, strcomment, strlen(strcomment));

		char* p = strstr(strcomment, strKey) + strlen(strKey) + 1;
		std::string str(p);
		if (p != nullptr)
		{
			strncpy(strcomment, comment_tmp, p - strcomment);
			strcomment[p - strcomment] = 0;
			sprintf(strcomment + strlen(strcomment), "%s,", szValue);
		}
		strcat(strcomment, str.c_str());
	}
}
/* strcomment:描述信息的保存变量；strKey：描述信息的key值；值描述格式：以逗号为分隔(",") */
void CUtility::AppendCommentInfo(char* strcomment, const char* strKey, const char* szValue)
{
	char strInfo[256] = { 0 };
	if (strstr(strcomment, strKey) == nullptr)
	{
		/* 检查是否超出缓存空间，超过则更新为最新的信息 */
		sprintf(strInfo, "<%s:%s> ", strKey, szValue);
		if ((strlen(strcomment) + strlen(strInfo)) < MAX_PATH)
		{
			sprintf(strcomment + strlen(strcomment), "%s", strInfo);
		}
		else
		{
			sprintf(strcomment, "%s", strInfo);
		}
	}
	else
	{
		/* 查找key值，看是否需要更加值 */
		memset(strInfo, 0, sizeof(strInfo));
		char* p1 = strstr(strcomment, strKey) + strlen(strKey) + 1;
		char* p2 = strstr(p1, ">");
		if ((p2 != nullptr) && ((p2 - p1) >= 0))
		{
			strncpy(strInfo, p1, p2 - p1);
			bool bIsHave = false;
			std::vector<std::string> vcl = CStrutils::Split(strInfo, ",");
			for (std::size_t i = 0; i < vcl.size(); i++)
			{
				if (vcl[i] == szValue)
				{
					bIsHave = true;
					break;
				}
			}
			if (!bIsHave)
			{
				AddCommentForKey(strcomment, strKey, szValue);
			}
		}
	}
}
/* 描述字段的填写格式统一为：
 * <关键字1:值描述>空格<关键字2:值描述>空格<关键字3:值描述>......
 * 值描述格式：以逗号为分隔(",")
 * 例子:<Decode Procedure:开始解码> <TestVersion:461> <MTDigit:15072324401,>
 */
void CUtility::AppendComment(char* strcomment, const char* strKey, const char* szValue)
{
	try
	{
		AppendCommentInfo(strcomment, strKey, szValue);
	}
	catch (...)
	{
		OUTPUT_ERR("AppendComment1:描述字段信息填写异常");
	}
}
void CUtility::AppendComment2(char* strcomment, const char* strKey, const char* szValue)
{
	try
	{
		AppendCommentInfo(strcomment, strKey, szValue);
	}
	catch (...)
	{
		OUTPUT_ERR("[Error]: AppendComment2:描述字段信息填写异常!");
	}
}

void CUtility::UTF_8ToString(const char* pstr, std::string& strRev)
{
	BYTE szUTF_8[3] = { 0xEF, 0xBB, 0xBF };
	if (memcmp(pstr, szUTF_8, 3) == 0)
	{
		strRev.assign(pstr + 3);
	}
}

/* 进制转换 */
int CUtility::DEC2OCT(int nConvert)
{
	int nRet = 0;
	int nTimes = 0;
	int nValue = nConvert;
	int radix = 8;
	
	while(nValue)
	{
		nRet += (nValue % radix) * pow(10.0, nTimes++);
		
		nValue = nValue / radix;
	}
	
	return nRet;
}

std::string CUtility::GetFileExtension(std::string strFileName)
{
	std::string strFileExtension = "";
	// 扩展名后加端口号
	if (strFileName.find_last_of(".") != std::string::npos)
	{
		strFileExtension = strFileName.substr(strFileName.find_last_of("."));
		if (strFileExtension.find("(") != std::string::npos
			&& strFileExtension.find(")") != std::string::npos)
		{
			// *.mtf(02)
			strFileExtension = strFileExtension.substr(0, strFileExtension.find("("));
		}
	}

	return strFileExtension;
}