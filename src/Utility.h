// Utility.h: interface for the CUtility class.
//
//////////////////////////////////////////////////////////////////////

#ifndef __UTILITY_H__
#define __UTILITY_H__

#include "StdHeader.h"
#include "StdFile.h"
#include "StdMonitor.h"
#include <zip.h>

#define REVERSE_INT(Ptr) ((int)((((UINT)((Ptr)[0])) << 24) + (((UINT)((Ptr)[1])) << 16) + (((UINT)((Ptr)[2])) << 8) + ((Ptr)[3])))
#define REVERSE_WORD(Ptr) (((Ptr)[0]) * 256 + ((Ptr)[1]))
#define REVERSE_INT24(Ptr) ((UINT)((((UINT)((Ptr)[0])) << 16) + (((UINT)((Ptr)[1])) << 8) + (((UINT)((Ptr)[2]))) ))
#define REVERSE_INT36(Ptr) ((UINT64)((((UINT64)((Ptr)[0])) << 28) + (((UINT64)((Ptr)[1])) << 20) + (((UINT64)((Ptr)[2])) << 12) + (((UINT64)((Ptr)[3])) << 4) + (((UINT64)((Ptr)[4])) >> 4) ))

typedef struct STRU_TIME_VAL
{
	long tv_sec; //秒
	long tv_usec; //微妙
	void Clear(void)
	{
		tv_sec = 0;
		tv_usec = 0;
	};
	bool operator== (const STRU_TIME_VAL& tval)
	{
		return ((this->tv_sec == tval.tv_sec) && (this->tv_usec == tval.tv_usec));
	};
	bool operator>= (const STRU_TIME_VAL& tval)
	{
		int nSpan = (this->tv_sec - tval.tv_sec) * 1000
			+ (this->tv_usec - tval.tv_usec);

		return (nSpan >= 0);
	};
	bool operator> (const STRU_TIME_VAL& tval)
	{
		int nSpan = (this->tv_sec - tval.tv_sec) * 1000
			+ (this->tv_usec - tval.tv_usec);

		return (nSpan > 0);
	};
	bool operator<= (const STRU_TIME_VAL& tval)
	{
		int nSpan = (this->tv_sec - tval.tv_sec) * 1000
			+ (this->tv_usec - tval.tv_usec);

		return (nSpan <= 0);
	};
	bool operator< (const STRU_TIME_VAL& tval)
	{
		int nSpan = (this->tv_sec - tval.tv_sec) * 1000
			+ (this->tv_usec - tval.tv_usec);

		return (nSpan < 0);
	};
	bool operator!= (const STRU_TIME_VAL& tval)
	{
		int nSpan = (this->tv_sec - tval.tv_sec) * 1000
			+ (this->tv_usec - tval.tv_usec);

		return (nSpan != 0);
	};
}tSTRU_TIME_VAL;

#define TOKEN_MT_SPLIT ("{#%MT%#}")

class CUtility  
{
public:
	static int GetExeId(void);
	static std::string OutPutInfo(const char* fmt, ...);
	static std::string S_Printf(const char* fmt, ...);
	static std::string GetCurrTime(void);
	static tSTRU_TIME_VAL GetMilliseconds(void);
	/**
	 * 功能描述：字符串时间转秒值
	 * @Param: c_Time,例子：2015-07-02 10:38:30.387
	 * @Return：tSTRU_TIME_VAL
	 */
	static tSTRU_TIME_VAL GetMilliseconds(const char* c_Time);
	/**
	 * 功能描述：获取N个月前的时间秒值
	 * @Param nMonthBefor: 多少月前

	 * @Return：秒值
	*/
	static time_t GetXMonthTime(int nMonthBefor);

	static std::string Stat_GsmTCH(BYTE iMode, BYTE TCHCount, int* pTCH);

	static std::string GetStrTime(time_t nSec, int nMsec);
	static std::string GetStrTime(tSTRU_TIME_VAL tTimeVal);
	static bool GetStruTM(char* const szTimeInfo, tm& when, BYTE* const pDate);
	static std::string GetStrDataForm(const time_t nTime = 0, const std::string strToken = "");
	// GBK转utf-8
	static std::string GBKToUtf8(char* pSrc);
	// utf-8转GBK
	static std::string Utf8ToGBK(char* pSrc);

	//重命名(返回是否修改成功)
	static bool Rename(std::string strOrgFilePath, std::string strNewFilePath);
	static std::string TrimNumStrLeftZero(std::string strnum);
	
	static void ReplaceCommaMark(char* const pStr, std::string strNewToken = TOKEN_MT_SPLIT);

	static std::string GetFileName(const std::string strSrcFileName);
	static std::string GetFileNameWithExtension(const std::string strSrcFileName);
	static void split_name(const char *szfullfilename, char *szpathname, char *szfilename, char *szextname);
	
	static void TrimMidBlank(char* const pStr, std::size_t nsize);
	static int HexStrToByteWithoutMidBlank(char* const pStr, BYTE* const pBinary, BYTE btype = 0); // btype = 0字符串长超过512 * 2的截断
	
	static double GetDistance(double lng1, double lat1, double lng2, double lat2);

	/**
	 * 功能描述：DES解密
	 * @Param strInfo: 加密的字符串（解密后会修改）
	 * @Param bCompatiblePlain: 是否兼容：如果传入的加密字符串是明文的话，不做修改

	 * @Return：成功与否
	*/
	static bool DesDecrypt(char* pStrInfo, bool bCompatiblePlain = true);

	/**
	 * 功能描述：DES加密
	 * @Param strInfo: 需要加密的字符串

	 * @Return：加密后的字符串
	*/
	static std::string DesEncrypt(char* pStrInfo);

	/**
	 * 功能描述：MD5加密
	 * @Param strInfo: 需要加密的字符串

	 * @Return：加密后的字符串
	*/
	static std::string Md5Encrypt(std::string pStrInfo);
	static std::string Md5Encrypt(const void* dat, size_t len);

	/**
	 * 功能描述：计算ifileid
	 * @param strAbsolutePath：绝对路径+文件名 
	 * @param filesize：计算返回文件大小
	 * @param pLog：输出日志（指针不为空则处理）
	 * @param bAloneIdEnable：配置开关，是否加上相对路径+文件名作为ifileid的一部分
	 * @param pAloneIdProjects：配置项目ID集，此项目加上相对路径+文件名作为ifileid的一部分
	 * @param strRelativePath：相对路径+文件名
	 * @param nProjectId：当前文件的项目id
	 * 
	 * @Return：ifileid
	*/
	static unsigned int HashCode(const std::string strAbsolutePath, 
						unsigned int &filesize, 
						CStdLog* const pLog = NULL,
						const bool bAloneIdEnable = false,
						std::vector<int>* const pAloneIdProjects = NULL,
						const std::string strRelativePath = "",
						const int nProjectId = -1);

	static bool FileIsUsed(std::string fileFullName);

	static bool FileIsExiset(std::string& strPathFile, std::string& strNewDot);

	// zip option
	static int UnZipFile(const char* unzip_file_in, char* unzip_path_out);
	static int ZipFile(const char * zip_file_in, char * zip_file_out);

	// 进制转换
	/* 十进制转八进制 */
	static int DEC2OCT(int nConvert);
	/* 16进制转10进制 */
	static int Hex_char_value(char c);

	template <typename T>
	static int FindMaxValIndex(T array[], int size)
	{
		T max = array[0];
		int index = 0;
		
		for(int i = 1; i < size; i++) {
			if(array[i] > max) {
				index = i;
				max = array[i];
			}
		}
		
		return index;  
	}

	static void RevArray(BYTE* pBuf, int len);
	static int SwapOptBytes(const void* Pltr, BYTE type);

	static int BinaryStrToInt(const char* pStr);
	static void ByteToBinary(const BYTE* const pByte, const int nLen, std::string& strBinary);
	/* 获取时隙数：(nTs:时隙, nCount:时隙数) */
	static void GetTimeslot(BYTE nTs, BYTE& nPos, int& nCount);

	static bool GetCol(std::vector<std::string>& vecCol, char* const pStr, char* const cSign, const BYTE nSign);

	//处理描述信息
	static void AppendComment(char* strcomment, const char* strKey, const char* szValue);
	static void AppendComment2(char* strcomment, const char* strKey, const char* szValue);
	static void AppendCommentInfo(char* strcomment, const char* strKey, const char* szValue);

	/**
	功能描述：对于UTF-8编码的文件，文件开始三个字节是EF BB BF，
             此函数功能是跳过这三个字符然后截取后面的字符串为新的字符串
	*/
	static void UTF_8ToString(const char* pstr, std::string& strRev);

	// 获取文件名的扩展名
	static std::string GetFileExtension(std::string strFileName);

private:
	// static int Get_NR_FGlobal_By_SSB_ARFCN(unsigned int nssbfreq);

	static int pmkdir(const char *path);
	static int search_dir (struct zip *z, const char* org_path, const char* cur_path, const char * name );

	static void AddCommentForKey(char* strcomment, const char* strKey, const char* szValue);
};

#endif // !defined(AFX_UTILITY_H__095B5F8A_1F5B_4856_94B2_A823F16B2E13__INCLUDED_)
