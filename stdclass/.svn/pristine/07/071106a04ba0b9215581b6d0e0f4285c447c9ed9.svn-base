#ifndef __STDTHREAD_H__
#define __STDTHREAD_H__

#include "StdHeader.h"

#define TRD_RETVAL void*

#define STACK_SIZE (2*1024*1024)

class CStdThreadActor
{
public:
	virtual ~CStdThreadActor(void){};
	virtual void ThreadMain(void) = 0;
};

class CStdThread : public CStdThreadActor
{
public:
	CStdThread(void);
	virtual ~CStdThread(void);

	bool CreateThread(const unsigned int nStackSize = STACK_SIZE, const CStdThreadActor* const pActor = NULL);
	
	void CloseThread(const int nmsec);

	bool IsAlive(const unsigned int nHeartBeatMaxSpan = 120)
	{
		if (!m_bRunflag)
		{
			return false;
		}

		return IsHeartBeat(nHeartBeatMaxSpan);
	};
	bool GetStopstatus(void)
	{
		return m_bStopflag;
	};
	void SetStop(void)
	{
		m_bRunflag = false;
		m_tHeartBeatTime = 0;
	};
	bool Runflag(void)
	{
		m_tHeartBeatTime = time(NULL);
		return m_bRunflag;
	};
	void Sleeping(unsigned int nSec);

	virtual void RunNow(void){m_nSleepStep = 0x5FFFFFFF;};

	DWORD GetThreadId(void) {
		return m_nThrdAddr;
	};

protected:
	time_t m_tThreadCreateTime;
	/**
	 * 增加线程的心跳时间，防止线程处理有问题，长时间被挂死。
	 */
	time_t m_tHeartBeatTime;

	/**线程心跳监控
	 * 线程挂死2分钟（nHeartBeatMaxSpan），则需要清理了。
	 */
	bool IsHeartBeat(const unsigned int nHeartBeatMaxSpan = 120);

private:
	typedef struct STRUCT_THREAD_INFO
	{
		CStdThread* pThread;
		CStdThreadActor* pActor;
	}tSTRUCT_THREAD_INFO;

	static TRD_RETVAL ThreadProc(void * lpParameter) // 线程函数
	{
		if (NULL != lpParameter)
		{
			tSTRUCT_THREAD_INFO* const pThreadInfo = (tSTRUCT_THREAD_INFO*)lpParameter;

			assert(pThreadInfo->pThread != NULL);
			assert(pThreadInfo->pActor != NULL);

			(pThreadInfo->pThread)->SetStopstatus(false);

			(pThreadInfo->pActor)->ThreadMain();

			(pThreadInfo->pThread)->SetStopstatus(true);

			(pThreadInfo->pThread)->EndThread();
		}

		return 0;
	};

	void EndThread(void);

	void SetStopstatus(const bool bflag)
	{
		m_bStopflag = bflag;
	};

	pthread_t m_nThread;

	bool m_bRunflag;
	bool m_bStopflag;
	unsigned int m_nThrdAddr;
	unsigned int m_nSleepStep;

	tSTRUCT_THREAD_INFO m_tTrdInfo;
};

#endif
