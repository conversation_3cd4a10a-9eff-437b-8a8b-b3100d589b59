
#include "StdCommond.h"
#include "StdFileFind.h"
#include "StdFile.h"
#include "Strutils.h"

bool CStdCommond::CreateDirec( const char * const pszPath)
{
	return (mkdir(pszPath, S_IRWXU | S_IRWXG | S_IRWXO) == 0);
}

void CStdCommond::CreateDirecA(const char * const pPath)
{
	if (0 == access(pPath, 0))
	{
			return;
	}

	size_t nLen = strlen(pPath);

	assert(nLen < MAX_PATH);

	char szPath[MAX_PATH] = "";

	strcpy(szPath, pPath);

	for (size_t i = 0; i < nLen; i++)
	{
		if ((szPath[i] == '\\')	|| (szPath[i] == '/'))
		{
			szPath[i] = 0;

			CreateDirec(szPath);

			szPath[i] = '/';
		}
	}

	CreateDirec(szPath);
}

void CStdCommond::GetProcessPath(char* const pszPath)
{
	if (pszPath == NULL)
	{
		return;
	}

	strcpy(pszPath, "");
	getcwd(pszPath, MAX_PATH);
	strcat(pszPath, "/");	
}

void CStdCommond::DeleteDirec(const char* const pszPath, bool bDelFile)
{
	if ((pszPath == NULL) || (strcmp(pszPath, "") == 0))
	{
		return;
	}

	char szPath[MAX_PATH] = "";
	strcpy(szPath, pszPath);

	size_t nLen = strlen(szPath);

	if ((szPath[nLen - 1] != '/') && 
		(szPath[nLen - 1] != '\\'))
	{
		strcat(szPath, "/");
	}

	CStdFileFind filefinder;

	bool bFinded = false;

	bFinded = filefinder.FindFile(szPath);

	while(bFinded)
	{
		bFinded = filefinder.FindNextFile();

		if (filefinder.IsDots())
		{
			continue;
		}
		else if (filefinder.IsDirectory())
		{
			DeleteDirec(filefinder.GetFilePath().c_str(), bDelFile);
		}
		else
		{
			if (bDelFile)
			{
				CStdFile::Remove(filefinder.GetFilePath().c_str());
			}
		}
	}
	filefinder.Close(); 

	rmdir(szPath);
}

void CStdCommond::ListFile(std::string strPath, std::vector<std::string>& vecFilePath)
{
	CStdFileFind filefinder;
	
	bool bFinded = filefinder.FindFile(strPath.c_str());
	
	std::string strFilePath("");
	
	while(bFinded)
	{
		bFinded = filefinder.FindNextFile();
		
		if (filefinder.IsDots())
		{
			continue;
		}
		else if (filefinder.IsDirectory())
		{
			ListFile(filefinder.GetFilePath(), vecFilePath);		
		}
		else
		{
			vecFilePath.push_back(filefinder.GetFilePath());
		}
	}
	
	filefinder.Close();
}

void CStdCommond::ListFile(std::string strPath, std::map<std::string, std::string>& mapFilePath)
{
	CStdFileFind filefinder;
	
	bool bFinded = filefinder.FindFile(strPath.c_str());
	
	std::string strFilePath("");
	
	while(bFinded)
	{
		bFinded = filefinder.FindNextFile();
		
		if (filefinder.IsDots())
		{
			continue;
		}
		else if (filefinder.IsDirectory())
		{
			ListFile(filefinder.GetFilePath(), mapFilePath);		
		}
		else
		{
			mapFilePath.insert(make_pair(filefinder.GetFileName(), filefinder.GetFilePath()));
		}
	}
	
	filefinder.Close();
}

void CStdCommond::Sleep( const int nMilsec)
{
	struct timeval sleeptime;
	sleeptime.tv_sec = (nMilsec / 1000) % 60;		
	sleeptime.tv_usec= (nMilsec % 1000) * 1000;
	select(0, NULL, NULL, NULL, &sleeptime);
}

char* CStdCommond::itoa(const int value, char * const string, const int radix)
{
	if(radix == 10)
	{
		sprintf(string , "%d" , value);
	}
	else if(radix == 16)
	{
		sprintf(string , "%x" , value);
	}
	
	return string;
}

std::string CStdCommond::itostr(const int value, const int radix)
{
	char datastr[255];

	itoa(value, datastr, radix);

	return std::string(datastr);
}

std::string CStdCommond::int64tostr(const INT64 value, const int radix)
{
	char datastr[255];

	if (radix == 10)
	{
		sprintf(datastr, "%lld", value);
	}
	else if (radix == 16)
	{
		sprintf(datastr, "%llX", value);
	}

	return std::string(datastr);
}

int CStdCommond::atoi(const std::string sourceStr, const int radix)
{
	int retInt = 0;
	std::string sourceStrTemp = StringToUpOrLow(sourceStr, 1);

	//16进制
	if (radix == 16 || radix == 8)
	{
		for (int i = 0; i<(int)sourceStrTemp.length(); ++i)
		{
			int level = int(sourceStrTemp.length() - i - 1);
			char ch = sourceStrTemp.at(i);

			if (ch >= 'a' && ch <= 'f')
			{
				retInt += (ch-'a' + 10) * (int)pow((float)radix, level);
			}
			else
			{
               retInt += (ch-'0') * (int)pow((float)radix, level);
			}
		}
	}
	else
	{
		retInt = ::atoi(sourceStrTemp.c_str());
	}
	return retInt;
}

short CStdCommond::strtosmallint(const std::string value)
{
   return (WORD)atoi(value.c_str());
}

BYTE CStdCommond::strtotinyint(const std::string value)
{
	return (BYTE)atoi(value.c_str());
}

int CStdCommond::GetErrno(void)
{
	return errno;
}

__int64 CStdCommond::_atoi64(const char *p64)
{
	if(::strlen(p64) > 19)
	{
		char tm_str[22];
		::strncpy(tm_str, p64, 19);
		if(tm_str[0] > '7')
		{
			tm_str[18] = 0;
		}
		return ::atoll(tm_str);
	}
	else
	{
		return ::atoll(p64);
	}
}


std::vector<std::string> CStdCommond::str_slipt(const std::string stringTemp, const std::string markFlag)
{
	std::vector<std::string> vec_return;

	std::string::size_type pos_old = 0;
	std::string::size_type pos_new = 0;
	std::string::size_type nPos = std::string::npos;

	while (pos_old < stringTemp.size())
	{
		pos_new = stringTemp.find(markFlag, pos_old) == nPos ? stringTemp.size():stringTemp.find(markFlag, pos_old);
		vec_return.push_back(stringTemp.substr(pos_old, pos_new - pos_old));
		pos_old = pos_new + markFlag.length();
	}

	return vec_return;
}

BOOL CStdCommond::vec_pushfront(void* p_item, std::vector<void*> vec_pItems)
{
	if (p_item == NULL)
	{
		return FALSE;
	}

    void* point = NULL;
    vec_pItems.push_back(point);

	for (std::vector<void*>::size_type i=vec_pItems.size()-1; i > 0; --i)
	{
        vec_pItems[i] = vec_pItems[i-1];
	}
  
    vec_pItems[0] = p_item;

	return TRUE;
}

std::string CStdCommond::str_trim(std::string sStr, char trim_char)
{
	int pos1 = 0;
	int pos2 = int(sStr.length()-1);

	while(pos1 < pos2)
	{
        if (sStr[pos1] == trim_char)
        {
			pos1++;
        }
		else 
		{
			break;
		}
	}

	while(pos1 < pos2)
	{
		if (sStr[pos2] == trim_char)
		{
			pos2--;
		}
		else 
		{
			break;
		}
	}

	return sStr.substr(pos1, pos2-pos1+1);
}

std::string CStdCommond::StringToUpOrLow(const std::string sourceStr, const int type)
{
	std::string targetStr(sourceStr);

	if (1 == type )
	{
		CStrutils::ToLowerString(targetStr);
	}
	else 
	{
		CStrutils::ToUpperString(targetStr);
	}
	return targetStr;
}

std::string CStdCommond::IPIntToStr(int ipInt)
{
	char szlog[64] = "";
	sprintf(szlog, "%d.%d.%d.%d", (ipInt&0X000000FF), (ipInt&0X0000FF00)>>8, (ipInt&0X00FF0000)>>2*8,(ipInt&0XFF000000)>>3*8);
	return std::string(szlog);
}

int CStdCommond::StrToIPInt(std::string ipStr)
{
	std::vector<std::string> strs = str_slipt(ipStr, ".");
	int ipInt = 0;
	for (size_t i=0; i<strs.size(); ++i)
	{
       ipInt += atoi(strs[i].c_str()) << i*8;
	}
	return ipInt;
}

BOOL CStdCommond::isValidHexNum(char ch1)
{
	if(( ch1 >= '0' && ch1 <= '9')  ||
		( ch1 >= 'a' && ch1 <= 'f')  || 
		( ch1 >= 'A' && ch1 <= 'F')
		)
	{
		return TRUE;
	}
	return FALSE;
}

char CStdCommond::toHex(char ch1)
{
	if (ch1 >= '0' && ch1 <= '9')
	{
		return ch1 - '0';
	}
	else if (ch1 >= 'a' && ch1 <= 'f')
	{
		return ch1 - 'a' + 10;
	}
	else if (ch1 >= 'A' && ch1 <= 'F')
	{
		return ch1 - 'A' + 10;
	}
	else
	{
		return 0;
	}
}

int CStdCommond::atox(char *const toHexChar, std::string& fromString)
{
	int nIndex = 0;
	for (size_t i=0; i<fromString.length(); i++)
	{
		char ch1 = 0, ch2 = 0;
		ch1 = fromString[i];
		while( !isValidHexNum(ch1)  && (i<fromString.length()) )
		{
			if(i == (fromString.length()-1) )
			{
				return nIndex;
			}

			i++;
			ch1 = fromString[i];
		}

		i++;
		if(i == (fromString.length()) )
		{
			return nIndex;
		}

		ch2 = fromString[i];
		while( !isValidHexNum(ch2)  && (i<fromString.length()) )
		{
			if(i == (fromString.length()-1) )
			{
				return nIndex;
			}

			i++;
			ch2 = fromString[i];
		}
		toHexChar[nIndex++] = toHex(ch1) * 16 + toHex(ch2);
	}

	return nIndex;
}


int CStdCommond::xtoa( const char *const fromHex, std::string& toString, const int nFromLen )
{
	if ( fromHex == NULL )
	{
		return 0;
	}
	BYTE b1, b2;
	char tempBuffer[10];
	for ( int i = 0; i < ( nFromLen ); i++ )
	{
		b2 = fromHex[i] & 0x0F ;
		b1 = ( fromHex[i] & 0xF0 ) >> 4 ;

		memset(tempBuffer, 0, sizeof(tempBuffer));
		if ( ( b1 == 0 ) && ( i == nFromLen - 1 ) )
		{
			sprintf(tempBuffer, "%X", b2);
		}
		else
		{
			sprintf(tempBuffer, "%X%X", b1, b2);
		}
		toString += std::string(tempBuffer);
	}
	return 1;
}

double CStdCommond::GetDistance(int point1_X, int point1_Y, int point2_X, int point2_Y)
{
	double dx1 = 40075360.0 * sin((90.0 - point1_Y/10000000.0) * 2 * 3.14159 / 360.0);
	double dx2 = 40075360.0 * sin((90.0 - point2_Y/10000000.0) * 2 * 3.14159 / 360.0);
	double dx = (dx1 + dx2) / 2.0 * (point1_X/10000000.0 - point2_X/10000000.0) / 360.0;
	double dy = 39940670.0 * (point2_Y/10000000.0 - point1_Y/10000000.0) / 360.0;

	dx = dx * dx + dy * dy;
	double distance = sqrt(dx);

	return distance;
}
int CStdCommond::replace(std::string& src_str, const std::string& old_str, const std::string& new_str) 
{ 
	int count = 0; 
	int old_str_len = old_str.length(), new_str_len = new_str.length(); 
	std::size_t pos = 0; 
	while((pos = src_str.find(old_str, pos)) != std::string::npos) 
	{ 
		src_str.replace(pos, old_str_len, new_str); 
		pos += new_str_len; 
		++count; 
	} 
	return count; 
} 

/// <summary>
/// 用于在输出窗口输出调试信息
/// </summary>
/// <param name="参数1名称">参数1意义</param>
/// <param name="参数2名称">参数2意义</param>
/// <returns>返回结果</returns>
void CStdCommond::OutputDebugPrintf(const char *format, ...)
{
	char buf[4096];
	char *p = buf;
	memset(buf, 0, sizeof(buf));
	va_list args;
	va_start(args, format);
  p += snprintf(buf, sizeof(buf) - 1, format, args);
	va_end(args);
	while ( p > buf  &&  isspace(p[-1]) )
		*--p = '\0';
	*p++ = '\r';
	*p++ = '\n';
	*p   = '\0';

  printf("%s\n", buf);
}





