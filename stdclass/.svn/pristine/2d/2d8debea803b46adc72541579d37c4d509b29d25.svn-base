#include "StdSqlOpHelper.h"
#include "StdNetBuffer.h"
#include "StdTime.h"
#include "Utility.h"

CStdSqlOpHelper::CStdSqlOpHelper(STRU_DBConnInfo* pConnInfo)
:m_DbConn(nullptr),
m_vecDBCol(),
m_mapDBDataByName(),
m_DataStatus(E_DataStatus_Error),
m_SqlLog("ms_execute_sql_log.txt")
{
	if(nullptr == pConnInfo)
  {
		return;
	}

	m_ConnInfo = *pConnInfo;

	m_DbConn = new CStdFreetds_Connection(&m_SqlLog);
}

CStdSqlOpHelper::~CStdSqlOpHelper(void)
{
	ClearData();

	m_DbConn->cleanup_conn();
}

size_t CStdSqlOpHelper::GetColumnCount(void)
{
	return m_vecDBCol.size();
}

STRU_DBData* CStdSqlOpHelper::GetColStruct(size_t i)
{
	if (m_DataStatus != E_DataStatus_ReadAble)
	{
		return nullptr;
	}
	if (i < m_vecDBDataByID.size())
	{
		return m_vecDBDataByID[i];
	}

	return nullptr;  
}

BOOL CStdSqlOpHelper::ExecuteNonQuery(const char* const pszSql)
{
	if (!m_DbConn->connect(m_ConnInfo.pchUserName, 
		m_ConnInfo.pchUserPassword, 
		m_ConnInfo.pchDBServer, 
		m_ConnInfo.pchDBName))
	{
		m_DataStatus = E_DataStatus_Error;
		return FALSE;
	}

	if (!m_DbConn->Execute(pszSql))
	{
		m_DataStatus = E_DataStatus_Error;
		m_DbConn->cleanup_conn();
		return FALSE;
	}

	if (m_DbConn->DbResults() == CStdFreetds_Connection::E_FAIL)
	{
		m_DataStatus = E_DataStatus_Error;
		m_DbConn->cleanup_conn();
		return FALSE;
	}

	if (m_ConnInfo.DisplaySql == 1)
	{
			OUTPUT_LOG(m_SqlLog, CUtility::OutPutInfo("[Exec SQL: ] %s", pszSql).c_str());
	}

	m_DataStatus = E_DataStatus_Error;//不能访问返回数据集
	return TRUE;
}

BOOL CStdSqlOpHelper::ExecuteReader(const char* const strSql)
{
  if (!m_DbConn->connect(m_ConnInfo.pchUserName, 
        m_ConnInfo.pchUserPassword, 
        m_ConnInfo.pchDBServer, 
        m_ConnInfo.pchDBName))
    {
			m_DataStatus = E_DataStatus_Error;
    	m_DbConn->cleanup_conn();
      return FALSE; 
    }
	
    if (!m_DbConn->Execute(strSql))
    {
			m_DataStatus = E_DataStatus_Error;
			m_DbConn->cleanup_conn();
			return FALSE;
    } 

    if (m_ConnInfo.DisplaySql == 1)
    {
        OUTPUT_LOG(m_SqlLog, CUtility::OutPutInfo("[Exec SQL: ] %s", strSql).c_str());
    }

		if (FALSE == Init())
		{
			m_DataStatus = E_DataStatus_Error;
			return FALSE;
		}

		m_DataStatus = E_DataStatus_ReadAble;//查询正确可以访问
    return TRUE;
}

BOOL CStdSqlOpHelper::Init()
{
	ClearData();

	do
	{
		m_DbConn->GetSelectColAttribute(m_vecDBCol);

		for (auto it = m_vecDBCol.begin(); it != m_vecDBCol.end(); ++it)
		{
			STRU_DBData* pSTRU_DBData = new STRU_DBData(it->columnName, it->columnType, it->colid);
			if (SqlType2DataObject(pSTRU_DBData) == FALSE) return FALSE;
			if (SqlType2BindType(pSTRU_DBData) == FALSE) return FALSE;
			if (CreateBindBuffer(pSTRU_DBData) == FALSE) return FALSE;

			m_mapDBDataByName[it->columnName] = pSTRU_DBData;   
			m_vecDBDataByID.push_back(pSTRU_DBData);
		}

		for (size_t i = 0; i < m_vecDBCol.size() ; ++i)
		{
			m_DbConn->BindCol(i + 1, m_vecDBDataByID[i]->bindType, m_vecDBDataByID[i]->bufferLenth, m_vecDBDataByID[i]->pBuffer);
		}
	}
	while (m_vecDBCol.size() == 0 && (m_DbConn->DbResults() == CStdFreetds_Connection::E_SUCCEED));

	if (m_vecDBCol.size() == 0)
	{
		ClearData();
		return FALSE;
	}

	if (m_vecDBCol.size() > 0 && m_DbConn->DbResults() == CStdFreetds_Connection::E_NO_MORE_RESULTS)
	{
		ClearData();
		return FALSE;
	}

	return TRUE;
}

void CStdSqlOpHelper::ClearData(void)
{
	for (auto it = m_vecDBDataByID.begin(); it != m_vecDBDataByID.end(); ++it)
	{
		delete *it;
	}
	m_mapDBDataByName.clear();
	m_vecDBDataByID.clear();
	m_vecDBCol.clear();
}

BOOL CStdSqlOpHelper::SqlType2DataObject(STRU_DBData* pSTRU_DBData)
{
	if (nullptr == pSTRU_DBData)
	{
		return FALSE;
	}

	pSTRU_DBData->dataType = CDataObject::E_DataType_Text;

	switch (pSTRU_DBData->sqlType)
	{
		case CSybDBType::E_SYB_SYBTEXT:
		case CSybDBType::E_SYB_SYBCHAR:
		case CSybDBType::E_SYB_SYBMSDATE:
		case CSybDBType::E_SYB_SYBMSTIME:
		case CSybDBType::E_SYB_SYBSQLVARIANT:
		case CSybDBType::E_SYB_SYBGEOGRAPHY:
		case CSybDBType::E_SYB_SYBXML:
			pSTRU_DBData->dataType = CDataObject::E_DataType_Text;
			break;
		case CSybDBType::E_SYB_SYBINT1:
			pSTRU_DBData->dataType = CDataObject::E_DataType_TinyInt;
			break;
		case CSybDBType::E_SYB_SYBINT2:
			pSTRU_DBData->dataType = CDataObject::E_DataType_SmallInt;
			break;
		case CSybDBType::E_SYB_SYBINT4:
		case CSybDBType::E_SYB_SYBMSDATETIMEOFFSET:
			pSTRU_DBData->dataType = CDataObject::E_DataType_Int;
			break;
		case CSybDBType::E_SYB_SYBREAL:
			pSTRU_DBData->dataType = CDataObject::E_DataType_Double;
			break;
		case CSybDBType::E_SYB_SYBDATETIME:
		case CSybDBType::E_SYB_SYBMSDATETIME2:
		case CSybDBType::E_SYB_SYBDATETIME4:
			pSTRU_DBData->dataType = CDataObject::E_DataType_DateTime;
			break;
		case CSybDBType::E_SYB_SYBFLT8:
		case CSybDBType::E_SYB_SYBMONEY:
		case CSybDBType::E_SYB_SYBMONEY4:
			pSTRU_DBData->dataType = CDataObject::E_DataType_Float;
			break;
		case CSybDBType::E_SYB_SYBBIT:			
			pSTRU_DBData->dataType = CDataObject::E_DataType_TinyInt;
			break;
		case CSybDBType::E_SYB_SYBNUMERIC:
			pSTRU_DBData->dataType = CDataObject::E_DataType_Numeric;
			break;		
		case CSybDBType::E_SYB_SYBDECIMAL:
		case CSybDBType::E_SYB_SYBINT8:
			pSTRU_DBData->dataType = CDataObject::E_DataType_Int64;
			break;
		case CSybDBType::E_SYB_SYBIMAGE: 
			pSTRU_DBData->dataType = CDataObject::E_DataType_Image;
			break;	
		case CSybDBType::E_SYB_SYBBINARY:
			pSTRU_DBData->dataType = CDataObject::E_DataType_Byte;
			break;
		
		default:
		    return FALSE;
	}
	return TRUE;
}

BOOL CStdSqlOpHelper::SqlType2BindType(STRU_DBData* pSTRU_DBData)
{
if (nullptr == pSTRU_DBData)
	{
		return FALSE;
	}

	switch (pSTRU_DBData->sqlType)
	{
		case CSybDBType::E_SYB_SYBCHAR:
			pSTRU_DBData->bindType = CHARBIND;
			break;
		case CSybDBType::E_SYB_SYBINT1:
			pSTRU_DBData->bindType = TINYBIND;
			break;
		case CSybDBType::E_SYB_SYBINT2:
			pSTRU_DBData->bindType = SMALLBIND;
			break;		
		case CSybDBType::E_SYB_SYBINT4:
			pSTRU_DBData->bindType = INTBIND;
			break;
		case CSybDBType::E_SYB_SYBINT8:
			pSTRU_DBData->bindType = BIGINTBIND;
			break;
		case CSybDBType::E_SYB_SYBIMAGE:
		case CSybDBType::E_SYB_SYBBINARY:
			pSTRU_DBData->bindType = BINARYBIND;
			break;
		case CSybDBType::E_SYB_SYBNUMERIC:
			pSTRU_DBData->bindType = NUMERICBIND;
			break;
		case CSybDBType::E_SYB_SYBTEXT:
		case CSybDBType::E_SYB_SYBMSDATE:
		case CSybDBType::E_SYB_SYBMSTIME:
		case CSybDBType::E_SYB_SYBSQLVARIANT:
		case CSybDBType::E_SYB_SYBGEOGRAPHY:
		case CSybDBType::E_SYB_SYBXML:
		case CSybDBType::E_SYB_SYBMSDATETIMEOFFSET:
		case CSybDBType::E_SYB_SYBREAL:
		case CSybDBType::E_SYB_SYBDATETIME:
		case CSybDBType::E_SYB_SYBMSDATETIME2:
		case CSybDBType::E_SYB_SYBDATETIME4:
		case CSybDBType::E_SYB_SYBFLT8:
		case CSybDBType::E_SYB_SYBMONEY:
		case CSybDBType::E_SYB_SYBMONEY4:
		case CSybDBType::E_SYB_SYBBIT:
		case CSybDBType::E_SYB_SYBDECIMAL:
		default:
		    return FALSE;
	}
	return TRUE;
}

BOOL CStdSqlOpHelper::CreateBindBuffer(STRU_DBData* pSTRU_DBData)
{
  pSTRU_DBData->bufferLenth = 0;
  switch(pSTRU_DBData->dataType)
  {
  case CDataObject::E_DataType_TinyInt:
	  {
		  pSTRU_DBData->pBuffer = new BYTE[1];
		  pSTRU_DBData->bufferLenth = 1;
		  memset(pSTRU_DBData->pBuffer, 0, pSTRU_DBData->bufferLenth);
	  }
	  break;
  case CDataObject::E_DataType_SmallInt:
	  {
		  pSTRU_DBData->pBuffer = new BYTE[2];
		  pSTRU_DBData->bufferLenth = 2;
		  memset(pSTRU_DBData->pBuffer, 0, pSTRU_DBData->bufferLenth);
	  }
	  break;
  case CDataObject::E_DataType_Int:
	  {
		  pSTRU_DBData->pBuffer = new BYTE[4];
		  pSTRU_DBData->bufferLenth = 4;
		  memset(pSTRU_DBData->pBuffer, 0, pSTRU_DBData->bufferLenth);
	  }
	  break;
  case CDataObject::E_DataType_Float:
  case CDataObject::E_DataType_Double:
  case CDataObject::E_DataType_Int64:
	  {
		  pSTRU_DBData->pBuffer = new BYTE[20];
		  pSTRU_DBData->bufferLenth = 20;
		  memset(pSTRU_DBData->pBuffer, 0, pSTRU_DBData->bufferLenth);
	  }
	  break;
  case CDataObject::E_DataType_Numeric:
  case CDataObject::E_DataType_DateTime:
	  {
		  pSTRU_DBData->pBuffer = new BYTE[100];
		  pSTRU_DBData->bufferLenth = 100;
		  memset(pSTRU_DBData->pBuffer, 0, pSTRU_DBData->bufferLenth);
	  }
	  break;
  case CDataObject::E_DataType_Text:
		{
		  pSTRU_DBData->pBuffer = new BYTE[MAX_DBDATA_BUFFERLENTH];
		  pSTRU_DBData->bufferLenth = 0;
		  memset(pSTRU_DBData->pBuffer, 0, MAX_DBDATA_BUFFERLENTH);
	  }
	  break;
  case CDataObject::E_DataType_Byte:
	case CDataObject::E_DataType_Image:
	  {
		  pSTRU_DBData->pBuffer = new BYTE[MAX_DBDATA_BUFFERLENTH];
		  pSTRU_DBData->bufferLenth = MAX_DBDATA_BUFFERLENTH;
		  memset(pSTRU_DBData->pBuffer, 0, MAX_DBDATA_BUFFERLENTH);
	  }
	  break;
  default:
	  {
		  return FALSE;
	  }
  }
  return TRUE;
}

BOOL CStdSqlOpHelper::Read(void)
{
	if (E_DataStatus_ReadAble != m_DataStatus)
	{
		return FALSE;
	}

	if (!subRead())
	{
			return FALSE;
	}
	return TRUE;
}

BOOL CStdSqlOpHelper::subRead(void)
{
	if (E_DataStatus_ReadAble != m_DataStatus)
	{
		return false;
	}

	//在获取数据之前先清空老数据，否则会有冗余
	for (int i = 0; i < (int)m_vecDBCol.size() ; ++i)
	{
		m_vecDBDataByID[i]->clear();
	}

	int csFetRt = m_DbConn->DbNextRow();

	return m_DataStatus == E_DataStatus_ReadAble &&  csFetRt == CStdFreetds_Connection::E_MORE_ROWS;
}

BOOL CStdSqlOpHelper::PushToBufferWihtNetSeq(BYTE* const pTarBuffer, int& ioffset, const int nCount, size_t sourStruIndex, int& sourBufferLenth)
{
	if (sourStruIndex > GetColumnCount())
	{
		return FALSE;
	}
	STRU_DBData* pSTRU_DBData = m_vecDBDataByID[sourStruIndex];

	if (pSTRU_DBData->dataType == CDataObject::E_DataType_Image)
	{
		if(!(CStdNetBufferWriter::PushSmallInt(pTarBuffer, ioffset, (int)pSTRU_DBData->dataLenth, nCount)))return false;
		if(!(CStdNetBufferWriter::PushBuffer(pTarBuffer, ioffset, (int)pSTRU_DBData->dataLenth, pSTRU_DBData->pBuffer, nCount)))return false;
		sourBufferLenth = (int)pSTRU_DBData->dataLenth+2;
	}
	else if (pSTRU_DBData->dataType  == CDataObject::E_DataType_Text)
	{
		WORD lenth = strlen(((char*)pSTRU_DBData->pBuffer));
		if(!CStdNetBufferWriter::PushSmallInt(pTarBuffer, ioffset, lenth, nCount))return false;
		if(!(CStdNetBufferWriter::PushBuffer(pTarBuffer, ioffset, lenth, pSTRU_DBData->pBuffer, nCount)))return false;       
		sourBufferLenth = lenth+2;
	}
	else if (pSTRU_DBData->dataType == CDataObject::E_DataType_Int)
	{
		int temp = GetInt(sourStruIndex);
		if(!(CStdNetBufferWriter::PushInt(pTarBuffer, ioffset, temp, nCount)))return false;
	}
	else if (pSTRU_DBData->dataType == CDataObject::E_DataType_SmallInt)
	{
		WORD temp = GetSmallInt(sourStruIndex);
		if(!(CStdNetBufferWriter::PushSmallInt(pTarBuffer, ioffset, temp, nCount)))return false;
	}
	else if (pSTRU_DBData->dataType == CDataObject::E_DataType_TinyInt)
	{

		BYTE temp = BYTE(sourStruIndex);
		if(!(CStdNetBufferWriter::PushByte(pTarBuffer, ioffset, temp, nCount)))return false;
	}
	else if (pSTRU_DBData->dataType == CDataObject::E_DataType_Float)
	{
		float temp = GetFloat(sourStruIndex);
		if(!(CStdNetBufferWriter::PushFloat(pTarBuffer, ioffset, temp, nCount)))return false;
	}
	else if (pSTRU_DBData->dataType == CDataObject::E_DataType_Double)
	{
		double temp = GetDouble(sourStruIndex);
		if(!(CStdNetBufferWriter::PushDouble(pTarBuffer, ioffset, temp, nCount)))return false;
	}
	else if (pSTRU_DBData->dataType == CDataObject::E_DataType_Int64)
	{	
		LONGLONG temp = GetInt64(sourStruIndex);
		if(!(CStdNetBufferWriter::PushInt64(pTarBuffer, ioffset, temp, nCount)))return false;
	}
	else if (pSTRU_DBData->dataType == CDataObject::E_DataType_DateTime)
	{	
		WORD lenth = sizeof(DBDATEREC);
		DBDATEREC timeStruct;
		memcpy(&timeStruct, pSTRU_DBData->pBuffer, lenth);

		CStdTime time;
		time.SetYear(timeStruct.year);
		time.SetMonth(timeStruct.month);
		time.SetDay(timeStruct.day);
		time.SetHour(timeStruct.hour);
		time.SetMinute(timeStruct.minute);
		time.SetSecond(timeStruct.second);
		if(!(CStdNetBufferWriter::PushInt(pTarBuffer, ioffset, time.GetAllSecond(), nCount)))return false;       
		sourBufferLenth = lenth;
	}
	else if (pSTRU_DBData->dataType == CDataObject::E_DataType_Numeric)
	{
		WORD lenth = strlen(((char*)pSTRU_DBData->pBuffer));
		if(!CStdNetBufferWriter::PushSmallInt(pTarBuffer, ioffset, lenth, nCount))return false;
		if(!(CStdNetBufferWriter::PushBuffer(pTarBuffer, ioffset, lenth, pSTRU_DBData->pBuffer, nCount)))return false;       
		sourBufferLenth = lenth+2;
	}
	return TRUE;
}

BOOL CStdSqlOpHelper::PushToBufferWihtNetSeq(BYTE* const pTarBuffer, int& ioffset, const int dataType, const int nCount, size_t sourStruIndex, int& sourBufferLenth)
{
	if (sourStruIndex > GetColumnCount())
	{
		return FALSE;
	}
	STRU_DBData* pSTRU_DBData = m_vecDBDataByID[sourStruIndex];

	if (pSTRU_DBData->dataType == CDataObject::E_DataType_Image)
	{
		if(!(CStdNetBufferWriter::PushSmallInt(pTarBuffer, ioffset, (int)pSTRU_DBData->dataLenth, nCount)))return false;
		if(!(CStdNetBufferWriter::PushBuffer(pTarBuffer, ioffset, (int)pSTRU_DBData->dataLenth, pSTRU_DBData->pBuffer, nCount)))return false;
		sourBufferLenth = (int)pSTRU_DBData->dataLenth+2;
	}
	else if (pSTRU_DBData->dataType  == CDataObject::E_DataType_Text)
	{
		std::string unix_str = CUtility::Utf8ToGBK((char*)pSTRU_DBData->pBuffer);
		int lenth = (int)unix_str.length();

		if(!CStdNetBufferWriter::PushSmallInt(pTarBuffer, ioffset, lenth, nCount)) return false;
		if(!(CStdNetBufferWriter::PushBuffer(pTarBuffer, ioffset, lenth, (BYTE*)(unix_str.c_str()), nCount))) return false;       
		sourBufferLenth = lenth+2;
	}
	else if (pSTRU_DBData->dataType == CDataObject::E_DataType_Int)
	{
		int temp = GetInt(sourStruIndex);
		if(!(CStdNetBufferWriter::PushInt(pTarBuffer, ioffset, temp, nCount)))return false;
	}
	else if (pSTRU_DBData->dataType == CDataObject::E_DataType_SmallInt)
	{
		WORD temp = GetSmallInt(sourStruIndex);

		if (dataType == TYPE_CS_INT)
		{
			int iv = (int)temp;
			if(!(CStdNetBufferWriter::PushInt(pTarBuffer, ioffset, iv, nCount)))return false;
		}
		else
			if(!(CStdNetBufferWriter::PushSmallInt(pTarBuffer, ioffset, temp, nCount)))return false;
	}
	else if (pSTRU_DBData->dataType == CDataObject::E_DataType_TinyInt)
	{
		BYTE temp = GetByte(sourStruIndex);

		if (dataType == TYPE_CS_INT)
		{
			int iv = (int)temp;
			if(!(CStdNetBufferWriter::PushInt(pTarBuffer, ioffset, iv, nCount)))return false;
		}
		else
			if(!(CStdNetBufferWriter::PushByte(pTarBuffer, ioffset, temp, nCount)))return false;
	}
	else if (pSTRU_DBData->dataType == CDataObject::E_DataType_Float)
	{
		float temp = GetFloat(sourStruIndex);
		if(!(CStdNetBufferWriter::PushFloat(pTarBuffer, ioffset, temp, nCount)))return false;
	}
	else if (pSTRU_DBData->dataType == CDataObject::E_DataType_Double)
	{
		double temp = GetDouble(sourStruIndex);
		if(!(CStdNetBufferWriter::PushDouble(pTarBuffer, ioffset, temp, nCount)))return false;
	}
	else if (pSTRU_DBData->dataType == CDataObject::E_DataType_Int64)
	{	
		LONGLONG temp = GetInt64(sourStruIndex);
		if(!(CStdNetBufferWriter::PushInt64(pTarBuffer, ioffset, temp, nCount)))return false;
	}
	else if (pSTRU_DBData->dataType == CDataObject::E_DataType_DateTime)
	{	
		WORD lenth = sizeof(DBDATEREC);
		DBDATEREC timeStruct;
		memcpy(&timeStruct, pSTRU_DBData->pBuffer, lenth);

		CStdTime time;
		time.SetYear(timeStruct.year);
		time.SetMonth(timeStruct.month);
		time.SetDay(timeStruct.day);
		time.SetHour(timeStruct.hour);
		time.SetMinute(timeStruct.minute);
		time.SetSecond(timeStruct.second);
		if(!(CStdNetBufferWriter::PushInt(pTarBuffer, ioffset, time.GetAllSecond(), nCount)))return false;       
		sourBufferLenth = lenth;
	}
	else if (pSTRU_DBData->dataType == CDataObject::E_DataType_Numeric)
	{
		WORD lenth = strlen(((char*)pSTRU_DBData->pBuffer));
		if(!CStdNetBufferWriter::PushSmallInt(pTarBuffer, ioffset, lenth, nCount))return false;
		if(!(CStdNetBufferWriter::PushBuffer(pTarBuffer, ioffset, lenth, pSTRU_DBData->pBuffer, nCount)))return false;       
		sourBufferLenth = lenth+2;
	}
	return TRUE;
}












