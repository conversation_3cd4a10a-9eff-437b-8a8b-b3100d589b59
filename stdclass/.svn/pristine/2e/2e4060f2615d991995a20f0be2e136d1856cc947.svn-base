// StdSocket.cpp: implementation of the CStdSocket class.
//
//////////////////////////////////////////////////////////////////////

#include "StdSocket.h"
bool CStdSocket::m_bInited = false;
UINT64 CStdSocket::uCounter = 0;
UINT64 CStdSocket::cStatus[10] = {0};

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////
CStdLog CStdSocket::m_socket_actor_log("tcp_socket_actor_log.txt");

CStdSocket::CStdSocket(void)
:m_hSocket(INVALID_SOCKET)
{
	CStdSocket::SocketEnvInit();
}

CStdSocket::~CStdSocket(void)
{
	try
	{
		Close();
	}
	catch (...)
	{
		
	}
}

bool CStdSocket::SocketEnvInit(void)
{
	if (!m_bInited)
	{
		m_bInited = true;
	}
	return true;
}

void CStdSocket::Close(void)
{
	if (m_hSocket != INVALID_SOCKET)
	{
		shutdown(m_hSocket, 2);	
		close(m_hSocket);
		m_hSocket = INVALID_SOCKET;
	}
}

void CStdSocket::Terminate(SOCKET hSocket)
{
	if (hSocket != INVALID_SOCKET)
	{
		shutdown(hSocket, 2);	
		close(hSocket);
		hSocket = INVALID_SOCKET;
	}
}

bool CStdSocket::Create(const int nSocketType)
{
	assert(m_hSocket == INVALID_SOCKET);

	m_hSocket = socket(AF_INET, nSocketType, 0);

	if (m_hSocket != INVALID_SOCKET)
	{
		return true;
	}
	return false;
}

bool CStdSocket::Bind(const unsigned short nSocketPort, const char* const lpszSocketAddress)
{
	assert(m_hSocket != INVALID_SOCKET);

	struct sockaddr_in addrin;
	memset(&addrin, 0, sizeof(addrin));

    addrin.sin_family = AF_INET;
	addrin.sin_port = htons(nSocketPort);

	if (lpszSocketAddress == NULL)
	{
		addrin.sin_addr.s_addr = htonl(INADDR_ANY);
	}
	else
	{
		const unsigned long lResult = inet_addr(lpszSocketAddress);
		if (lResult == INADDR_NONE)
		{
			return false;
		}

		addrin.sin_addr.s_addr = lResult;
	}

	return (bind(m_hSocket, (struct sockaddr*)&addrin, sizeof(addrin)) != -1);
}

bool CStdSocket::Accept(CStdSocket& rConnectedSocket)
{
	assert(rConnectedSocket.m_hSocket == INVALID_SOCKET);

	const SOCKET hTemp = Accept();
	rConnectedSocket.Attach(hTemp);
	return (hTemp != INVALID_SOCKET);
}

SOCKET CStdSocket::Accept(void)
{
	assert(m_hSocket != INVALID_SOCKET);

	struct sockaddr_in addrin;
	memset(&addrin, 0, sizeof(addrin));
	socklen_t nLen = sizeof(addrin);

	const SOCKET hTemp = accept(m_hSocket, (struct sockaddr*)&addrin, &nLen);
	return hTemp;
}

bool CStdSocket::Connect(const char* const lpszHostAddress, const unsigned short nHostPort)
{
	assert(lpszHostAddress != NULL);
	assert(m_hSocket != INVALID_SOCKET);

	struct sockaddr_in sockAddr;
	memset(&sockAddr, 0, sizeof(sockAddr));

	sockAddr.sin_family = AF_INET;
	sockAddr.sin_port = htons(nHostPort);
	sockAddr.sin_addr.s_addr = inet_addr(lpszHostAddress);

	if (sockAddr.sin_addr.s_addr == INADDR_NONE)
	{
		return false;
	}

	return (-1 != connect(m_hSocket, (struct sockaddr*)&sockAddr, sizeof(sockAddr)));
}

int CStdSocket::ReceiveFrom(void* const lpBuf, const int nBufLen, unsigned short& rSocketPort, char* const rSocketAddress)
{
	assert(m_hSocket != INVALID_SOCKET);

	struct sockaddr_in sockAddr;
	memset(&sockAddr, 0, sizeof(sockAddr));

	socklen_t nSockAddrLen = sizeof(sockAddr);
	const int nResult = recvfrom(m_hSocket, (char*)lpBuf, nBufLen, 0, (struct sockaddr*)&sockAddr, &nSockAddrLen);

	if(nResult != -1)
	{
		rSocketPort = ntohs(sockAddr.sin_port);
		strcpy(rSocketAddress, inet_ntoa(sockAddr.sin_addr));
	}
	return nResult;
}

int CStdSocket::SendTo(const void* const lpBuf, const int nBufLen, const unsigned short nHostPort, const char* const rSocketAddress)
{
	assert(m_hSocket != INVALID_SOCKET);

	struct sockaddr_in  sockAddr;
	memset(&sockAddr, 0, sizeof(sockAddr));

	sockAddr.sin_family = AF_INET;

	if (rSocketAddress == NULL)
	{
		sockAddr.sin_addr.s_addr = htonl(INADDR_BROADCAST);
	}
	else
	{
		sockAddr.sin_addr.s_addr = inet_addr(rSocketAddress);
		if (sockAddr.sin_addr.s_addr == INADDR_NONE)
		{
			return false;
		}
	}

	sockAddr.sin_port = htons(nHostPort);

	const int nSockAddrLen = sizeof(sockAddr);
	return sendto(m_hSocket, lpBuf, nBufLen, 0, (struct sockaddr*)&sockAddr, nSockAddrLen);
}

bool CStdSocket::GetPeerName(char* const rPeerAddress, unsigned short& rPeerPort)
{
	assert(m_hSocket != -1);

	struct sockaddr_in sockAddr;
	memset(&sockAddr, 0, sizeof(sockAddr));

	socklen_t nSockAddrLen = sizeof(sockAddr);
	const int bResult = getpeername(m_hSocket, (struct sockaddr*)&sockAddr, &nSockAddrLen);
	if (bResult == 0)
	{
		rPeerPort = ntohs(sockAddr.sin_port);
		strcpy(rPeerAddress, inet_ntoa(sockAddr.sin_addr));
		return true;
	}
	return false;
}

bool CStdSocket::GetPeerName(const SOCKET hSocket, char* const rPeerAddress, unsigned short& rPeerPort)
{
	assert(hSocket != INVALID_SOCKET);

	struct sockaddr_in sockAddr;
	memset(&sockAddr, 0, sizeof(sockAddr));

	socklen_t nSockAddrLen = sizeof(sockAddr);
	const int bResult = getpeername(hSocket, (struct sockaddr*)&sockAddr, &nSockAddrLen);
	if (bResult == 0)
	{
		rPeerPort = ntohs(sockAddr.sin_port);
		strcpy(rPeerAddress, inet_ntoa(sockAddr.sin_addr));
		return true;
	}
	return false;
}

bool CStdSocket::GetSockName(char* const pCltAddress, unsigned short& wCltPort)
{
	sockaddr_in sockAddr;
	memset(&sockAddr, 0, sizeof(sockAddr));
	socklen_t nLen = sizeof(sockAddr);

	int ret = getsockname(m_hSocket, (sockaddr*)&sockAddr, &nLen);

	if (ret != 0)
	{
		return false;
	}

	wCltPort = ntohs(sockAddr.sin_port);
	strcpy(pCltAddress, inet_ntoa(sockAddr.sin_addr));

	return true;
}

int CStdSocket::rSelect(const int nMilliSecond)
{
	if (m_hSocket == INVALID_SOCKET)
	{
		return -1;
	}

	fd_set rset;
	struct timeval wtime;
	wtime.tv_sec = nMilliSecond / 1000;
	wtime.tv_usec = ((nMilliSecond + 10) % 1000) * 1000;

	FD_ZERO(&rset);
	FD_SET(m_hSocket, &rset);

	int nfds = m_hSocket + 1;
	const int nRt = select(nfds, &rset, NULL, NULL, &wtime);
	// case -1:// 出错
	// case 0:// 超时或者对端网线拔掉, 对端网线拔掉select_r返回0, 这里不处理，交由测试包发现问题。
	// default:// 对端关闭或正常

	return nRt;
}

int CStdSocket::wSelect(const int nMilliSecond)
{
	if (m_hSocket == INVALID_SOCKET)
	{
		return -1;
	}

	fd_set wset;
	struct timeval wtime;
	wtime.tv_sec = nMilliSecond / 1000;
	wtime.tv_usec = ((nMilliSecond + 10) % 1000) * 1000;

	FD_ZERO(&wset);
	FD_SET(m_hSocket, &wset);

	int nfds = m_hSocket + 1;
	const int nRt = select(nfds, NULL, &wset, NULL, &wtime);
	if (nRt == 0)
	{
		int errcode = 0;
		unsigned int errlen = sizeof(int);
		getsockopt(m_hSocket, SOL_SOCKET,SO_ERROR, (char*)(&errcode), &errlen);
		if (errcode != 0)
		{
			return -1;
		}
	}

	//case -1:// 出错
	//case 0:// 超时,对端服务未启动
	//default:// 正常

	return nRt;
}

int CStdSocket::Select(const int nMilliSecond)
{
	if (m_hSocket == INVALID_SOCKET)
	{
		return -1;
	}

	struct timeval wtime;
	wtime.tv_sec = nMilliSecond / 1000;
	wtime.tv_usec = ((nMilliSecond + 10) % 1000) * 1000;

	fd_set wset;
	FD_ZERO(&wset);
	FD_SET(m_hSocket, &wset);

	fd_set rset;
	FD_ZERO(&rset);
	FD_SET(m_hSocket, &rset);

	int nfds = m_hSocket + 1;
	int nRt = select(nfds, &rset, &wset, NULL, &wtime);
	if (nRt == 0)
	{
		int errcode = 0;
		unsigned int errlen = sizeof(int);
		getsockopt(m_hSocket, SOL_SOCKET,SO_ERROR, (char*)(&errcode), &errlen);
		if (errcode != 0)
		{
			return -1;
		}
	}
	else if (nRt > 0)
	{
		nRt = 0;

		if (FD_ISSET(m_hSocket, &rset))
		{
			nRt = 0x01;
		}

		if (FD_ISSET(m_hSocket, &wset))
		{
			nRt += 0x02;
		}
	}

	return nRt;
}
