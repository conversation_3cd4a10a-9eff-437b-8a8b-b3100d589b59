// StdBuffer.cpp: implementation of the CStdBuffer class.
//
//////////////////////////////////////////////////////////////////////

#include "StdBuffer.h"
#include "./StdFileFind.h"
#include "./StdCommond.h"
//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////
#define MAX_NODE_COUNT 50000

CBufNode::CBufNode(int nBufNodeSize)
:CStdNode(nBufNodeSize)
{
	m_pNodeBuf = new BYTE[m_nBufNodeSize];
	assert(NULL != m_pNodeBuf);
	if (NULL != m_pNodeBuf)
	{
		memset(m_pNodeBuf, 0, m_nBufNodeSize);
		m_pNodeBuf[0] = (BYTE)(DATAPACKHEAD & 0xFF);
		m_pNodeBuf[1] = (BYTE)((DATAPACKHEAD & 0XFF00) >> 8);
	}
}

CFileNode::CFileNode(CBufNode* const prBuf, CBufNode* const pwBuf, const std::string& strName, CNodeMng* const pMng,const int nBufNodeSize)
:CStdNode(nBufNodeSize), m_wFile(), m_rFile(), 
m_strFile(strName), m_rBuf(prBuf), 
m_wBuf(pwBuf), m_nStartPos(0), m_pMng(pMng)
{
	Initialize();
}

CFileNode::~CFileNode(void)
{
	m_wFile.Close();
	m_rFile.Close();
}

void CFileNode::Initialize(void)
{
	CStdNode::SetFree();
	CStdNode::SetAutoDel(true);

	m_wFile.Close();
	m_rFile.Close();

	if (m_strFile.compare("") != 0)
	{
		if (m_wFile.Open(m_strFile.c_str(), "a+b"))
		{
			m_rBuf->SetFree();
			m_wBuf->SetFree();
		}
	}
}

void CFileNode::Release(void)
{
	CStdNode::SetFree();
	m_wFile.Close();
	m_rFile.Close();

	m_rBuf = NULL;
	m_wBuf = NULL;
	CStdFile::Remove(m_strFile.c_str());
	m_strFile.assign("");
	m_pMng = NULL;
}

void CFileNode::SetBusy(void)
{
	if (m_wBuf->GetNodeLen() > 0)
	{
		m_wFile.SuperWrite(m_wBuf->GetNodeBuf(), m_wBuf->GetTotalLen());
	}

	m_wBuf->SetFree();
	m_wFile.Close();

	CStdNode::SetBusy();
}

bool CFileNode::WriteData(const BYTE* const pBuf, const int nSize)
{
	if (NULL == pBuf)
	{
		return false;
	}

	if (!(m_wFile.IsOpen()))
	{
		return false;
	}

	if (m_wBuf->WriteData(pBuf, nSize))
	{
		// 第一包
		if (m_wBuf->GetNodeLen() == nSize)
		{
			SetNodeLen(m_wBuf->GetTotalLen());
		}
		else
		{
			SetNodeLen(nSize);
		}

		return true;
	}
	else
	{
		size_t nWriten = m_wFile.SuperWrite(m_wBuf->GetNodeBuf(), m_wBuf->GetTotalLen());
		if (nWriten != (size_t)m_wBuf->GetTotalLen())
		{
			return false;
		}

		m_wBuf->SetFree();
		// 文件只取节点长
		if (GetNodeLen() >= NODE_FIlE_SIZE)
		{
			return false;
		}
		else
		{
			m_wBuf->WriteData(pBuf, nSize);
			// 第一包
			SetNodeLen(m_wBuf->GetTotalLen());

			return true;
		}
	}

}
// 文件异常 文件结束 0，正常 总字节数
int CFileNode::ReadData(BYTE** const pBuf, int& nCount)
{
	nCount = 0;
	m_rBuf->SetFree();
	if (pBuf == NULL)
	{
		return -1;
	}
	*pBuf = NULL;

	if (!(m_rFile.IsOpen()))
	{
		if (!m_rFile.Open(m_strFile.c_str(), "rb"))
		{
			return -1;
		}
		else
		{
			m_rFile.Seek(m_nStartPos, CStdFile::begin);
		}
	}

	const long nPos = m_rFile.GetPosition();
	if (m_pMng != NULL)
	{
		m_pMng->WriteMapping(nPos, m_strFile);
	}

	// 到文件末尾但文件未关闭
	if (nPos == m_rFile.GetLength()) 
	{
		if (IsFreeNode())
		{
			return 0; // 继续读
		}
		else
		{
			return -1;// 关闭
		}
	}

	unsigned int nReadCount = 0;
	unsigned int nTotal = 0;
	BYTE* const pTmp = m_rBuf->GetNodeBuf();
	assert(pTmp != NULL);

	while( nTotal < PKG_HEADER_LEN )
	{
		nReadCount = (unsigned int)(m_rFile.Read(pTmp + nTotal, PKG_HEADER_LEN - nTotal));
		
		nTotal += nReadCount;

		if((nTotal == PKG_HEADER_LEN) || (m_rFile.IsError()))
		{
			break;
		}
		if ((nReadCount == 0) && (m_rFile.IsEof()) && (!IsFreeNode()))
		{
			break;
		}

		CStdCommond::Sleep(20);
	}
	// 文件结束了读取的长度不对
	if (nTotal != PKG_HEADER_LEN)
	{
		return -1;
	}

	const unsigned int nLen = pTmp[3] + pTmp[2] * 256;

	if ((int)nLen > (m_nBufNodeSize - PKG_HEADER_LEN))
	{
		return -1;
	}

	nTotal = 0;
	nReadCount = 0;
	while( nTotal < nLen )
	{
		nReadCount = (unsigned int)(m_rFile.Read(pTmp + PKG_HEADER_LEN + nTotal, nLen - nTotal));

		nTotal += nReadCount;

		if(( nTotal == nLen ) || m_rFile.IsError())
		{
			break;
		}
		if ((nReadCount == 0) && (m_rFile.IsEof()) && (!IsFreeNode()))
		{
			break;
		}
		CStdCommond::Sleep(20);
	}

	// 文件结束了读取的长度不对
	if (nTotal != nLen)
	{
		return -1;
	}

	nCount = nLen + PKG_HEADER_LEN;
	*pBuf = pTmp;

	return nCount;
}

/////////////////////////////////////////////////////////////

CNodeMng::CNodeMng(const int nMaxSize, 
				   const BYTE eNodeType,
				   const char* const pIpaddr, 
				   const int nPort,
				   const BYTE cDire,
				   const bool bReSendFlag,
				   const int nBufNodeSize)
:m_FreeBuf(), m_BusyBuf(), m_BusyLock(), m_bCurrType(BuffMode), 
m_eNodeType(eNodeType), m_rBuf(NULL), m_wBuf(NULL), 
m_strPath(""), m_nIndex(0), m_clsFileMap(pIpaddr, nPort, cDire),m_nBufNodeSize(nBufNodeSize)
{
	Initialize(nMaxSize, pIpaddr, nPort, bReSendFlag,nBufNodeSize);
}

CNodeMng::~CNodeMng(void)
{
	try
	{
		Release();
	}
	catch (...)
	{
		
	}
}

void CNodeMng::Initialize(const int nMaxSize, const char* const pIpaddr, 
						  const int nPort, const bool bReSendFlag,const int nBufNodeSize)
{
	if (CNodeMng::FileMode != m_eNodeType)
	{
		CBufNode* pNode = NULL;
		for(int i = 0; i < nMaxSize; i++)
		{
			pNode = new CBufNode(nBufNodeSize);
			if (NULL != pNode)
			{
				m_FreeBuf.push_back(pNode);
			}
		}
	}

	if (CNodeMng::BuffMode != m_eNodeType)
	{
		m_rBuf = new CBufNode(nBufNodeSize);
		m_wBuf = new CBufNode(nBufNodeSize);

		char szPath[MAX_PATH] = "";
		char szPort[32] = "";
		CStdCommond::GetProcessPath(szPath);
		if ((pIpaddr != NULL) && (nPort != 0))
		{
			strcat(szPath, pIpaddr);
			CreateDir(szPath);
			sprintf(szPort, "/%d/", nPort);
			strcat(szPath, szPort);
			CreateDir(szPath);
		}
		m_strPath.assign(szPath);

		// 加载忙节点（数据文件）
		GetHisFile(bReSendFlag);

		if (m_BusyBuf.size() > 0)
		{
			m_bCurrType = CNodeMng::FileMode;
		}
	}
}

void CNodeMng::GetHisFile(const bool bReSendFlag)
{
	tSTRUCT_BUFF_INFO tInfo;
	memset(&tInfo, 0, sizeof(tSTRUCT_BUFF_INFO));
	m_clsFileMap.ReadFileMap(&tInfo, sizeof(tSTRUCT_BUFF_INFO));

	bool bFinded = false;
	std::string strPath("");

	CFileNode* pNode = NULL;

	CStdAutoLock theLock(&m_BusyLock);

	CStdFileFind filefinder;
	bFinded = filefinder.FindFile(m_strPath.c_str());

	unsigned int nLen = 0;
	while(bFinded)
	{
		bFinded = filefinder.FindNextFile();

		if (filefinder.IsDirectory() || filefinder.IsDots())
		{
			continue;
		}

		strPath = filefinder.GetFilePath();

		if (strstr(strPath.c_str(), ".org") == NULL) 
		{
			continue;
		}

		int nRl = strPath.compare(tInfo.szFileName);
		if (bReSendFlag && (nRl >= 0))
		{
			pNode = new CFileNode(m_rBuf, m_wBuf, strPath, this,m_nBufNodeSize);

			pNode->SetBusy();

			CStdFile::GetStatus(strPath.c_str(), nLen);

			pNode->SetNodeLen((int)nLen);

			if (nRl == 0)
			{
				pNode->SetStartPos(tInfo.nValue);
			}

			m_BusyBuf.push_back(pNode);

		}
		else
		{
			CStdFile::Remove(strPath.c_str());
		}
	}

	filefinder.Close();

}

void CNodeMng::WriteMapping(const long lPos, const std::string& strFile)
{
	m_clsFileMap.WriteFileMap(lPos, strFile);
}

void CNodeMng::GetCurrentFile(char* const pName)
{
	time_t totalsec;
	struct tm tmCurr;
	time(&totalsec);

	tmCurr = *localtime_r(&totalsec, &tmCurr);

	char szName[48] = "";

	strftime(szName, 48, "%Y%m%d%H%M%S", &tmCurr);

	strcpy(pName, m_strPath.c_str());

	strcat(pName, szName);
	
	sprintf(szName, "_%03d.org", m_nIndex % 1000);

	strcat(pName, szName);

	m_nIndex++;
}

void CNodeMng::Release(void)
{
	// 增加一个空的让后继GetFreeNode操作都失败
	m_FreeBuf.push_front(NULL);

	CStdAutoLock theLock(&m_BusyLock);

	Node_Buffer::iterator it;

	for (it = m_BusyBuf.begin(); it != m_BusyBuf.end(); it++)
	{
		if ((*it) != NULL)
		{
			if ((*it)->IsAutoDel())
			{
				delete ((CFileNode*)(*it));
			}
		}
	}

	m_BusyBuf.clear();

	////////////////////////////

	CBufNode* pNode = NULL;

	for (it = m_FreeBuf.begin(); it != m_FreeBuf.end(); it++)
	{
		pNode = (CBufNode*)(*it);
		if (NULL != pNode)
		{
			delete pNode;
		}
	}

	m_FreeBuf.clear();

	////////////////////////////

	if (NULL != m_rBuf)
	{
		delete m_rBuf;
		m_rBuf = NULL;
	}
	if (NULL != m_wBuf)
	{
		delete m_wBuf;
		m_wBuf = NULL;
	}
}

CStdNode* CNodeMng::GetFreeNode(void)
{
	CStdNode* pNode = NULL;

	switch (m_eNodeType)
	{
	case CNodeMng::FileMode:
		NodeTypeSwitch(CNodeMng::FileMode);
		pNode = GetFileNode();
		break;
	case CNodeMng::BuffMode:
		NodeTypeSwitch(CNodeMng::BuffMode);
		pNode = GetBufNode();
		break;
	default:
		if (m_bCurrType == CNodeMng::FileMode)
		{
			 pNode = GetFileNode();
		}
		else
		{
			pNode = GetBufNode();
			if (pNode == NULL)
			{
				NodeTypeSwitch(CNodeMng::FileMode);

				pNode = GetFileNode();
			}
		}
	}

	return pNode;
}

CStdNode* CNodeMng::GetFileNode(void)
{
	char szName[MAX_PATH] = "";

	GetCurrentFile(szName);

	CFileNode* const pNode = new CFileNode(m_rBuf, m_wBuf, szName, this,m_nBufNodeSize);

	CStdAutoLock theLock(&m_BusyLock);
	// 节点太多,挤掉一个
	if (m_BusyBuf.size() >= MAX_NODE_COUNT)
	{
		SetFreeNode(*(m_BusyBuf.begin()));
		m_BusyBuf.pop_front();
	}
	m_BusyBuf.push_back(pNode);

	return (CStdNode*)pNode;
}

CStdNode* CNodeMng::GetBufNode(void)
{
	assert(!m_FreeBuf.empty());

	CBufNode* pNode = NULL;

	pNode = (CBufNode*)(*(m_FreeBuf.begin()));

	if (NULL != pNode)
	{
		for (int i = 0; i < 3; i++)
		{
			if (pNode->IsFreeNode())
			{
				return (CStdNode*)pNode;
			}

			CStdCommond::Sleep(10);
		}
	}

	return NULL;
}

// 只从list中将节点取出，发送完后再修改闲标志
CStdNode* CNodeMng::GetBusyNode(void)
{
	CStdNode* pNode = NULL;

	CStdAutoLock theLock(&m_BusyLock);

	if (!m_BusyBuf.empty())
	{
		pNode = (*(m_BusyBuf.begin()));

		m_BusyBuf.pop_front();
	}

	return pNode;
}

void CNodeMng::SetBusyNode(CStdNode* const pNode)
{
	if (NULL == pNode)
	{
		return;
	}

	if (m_bCurrType == CNodeMng::FileMode)
	{
		return;
	}

	if (!m_FreeBuf.empty())
	{
		m_FreeBuf.pop_front();
		m_FreeBuf.push_back(pNode);
	}

	CStdAutoLock theLock(&m_BusyLock);

	m_BusyBuf.push_back(pNode);
}

void CNodeMng::DumpBusyNode(CStdNode* const pNode)
{
	if (NULL == pNode)
	{
		return;
	}

	if (pNode->GetNodeLen() > 0)
	{
		pNode->SetBusy();

		SetBusyNode(pNode);
	}

	if (m_eNodeType != CNodeMng::FileMode)
	{
		NodeTypeSwitch(CNodeMng::BuffMode);
	}
}

bool CNodeMng::CreateDir( const char * const pstrPath) const
{
	int nRt = 0;

	nRt = mkdir(pstrPath, S_IRUSR | S_IWUSR |S_IXUSR | S_IRGRP |S_IWGRP |S_IXGRP);

	return nRt == 0 ? true : false;
}

/////////////////////////////////////////////////
CStdBuffer::CStdBuffer(const int nBufSize, const BYTE cBuffType, 
					   const char* const pIpaddr,
					   const int nPort,
					   const BYTE cDire,
					   const bool bReSendFlag,const int nBufNodeSize)
:m_NodeMng(nBufSize, cBuffType, pIpaddr, nPort, cDire, bReSendFlag,nBufNodeSize), m_pFreeNode(NULL), m_pBusyNode(NULL), m_wLock(), m_rLock(),m_nBufNodeSize(nBufNodeSize)
{

}

CStdBuffer::~CStdBuffer(void)
{
	m_pFreeNode = NULL;

	if ((m_pBusyNode != NULL)
		&& (m_pBusyNode->IsAutoDel()))
	{
		delete (CFileNode*)m_pBusyNode;
		m_pBusyNode = NULL;
	}
}

int CStdBuffer::Read(BYTE** const pBuf, int& nCount)
{
	nCount = 0;
	*pBuf = NULL;

	if (pBuf == NULL)
	{
		return 0;
	}

	CStdAutoLock theLock(&m_rLock);

	if (m_pBusyNode != NULL)
	{
		if ((m_pBusyNode->GetNodeLen() == 0) 
			&& (!m_pBusyNode->IsFreeNode()))
		{
			m_NodeMng.SetFreeNode(m_pBusyNode);
			m_pBusyNode = NULL;
		}
	}

	if (m_pBusyNode == NULL)
	{
		m_pBusyNode = m_NodeMng.GetBusyNode();
		if (m_pBusyNode == NULL)
		{
			// 当前无忙节点，可以切换为内存。
			Dump();
			return 0;
		}
	}

	const int nNum = m_pBusyNode->ReadData(pBuf, nCount);
	if (nNum == -1)// 只有文件
	{
		m_NodeMng.SetFreeNode(m_pBusyNode);
		m_pBusyNode = NULL;

		return -1;
	}
	// 读到当前正在写的文件末尾了，可以切换为内存。
	else if (nNum == 0)
	{
		Dump();
	}

	return nCount;
}

int CStdBuffer::Write(const BYTE* const pBuf, const int nCount)
{
	if ((NULL == pBuf) || (nCount > (m_nBufNodeSize - PKG_HEADER_LEN)))
	{
		return 0;
	}

	CStdAutoLock theLock(&m_wLock);
	if (m_pFreeNode == NULL)
	{
		m_pFreeNode = m_NodeMng.GetFreeNode();
		if (m_pFreeNode == NULL)
		{
			return 0;
		}
	}
	// 缓冲区满
	if (!m_pFreeNode->WriteData(pBuf, nCount))
	{
		m_pFreeNode->SetBusy();
		m_NodeMng.SetBusyNode(m_pFreeNode);
		m_pFreeNode = m_NodeMng.GetFreeNode();
		if (m_pFreeNode == NULL)
		{
			return 0;
		}

		if (!m_pFreeNode->WriteData(pBuf, nCount))
		{
			return 0;
		}
	}
	return nCount;
}

void CStdBuffer::Dump(void)
{
	CStdAutoLock theLock(&m_wLock);
	m_NodeMng.DumpBusyNode(m_pFreeNode);

	m_pFreeNode = NULL;
}

/////////////////////////////////////////////////////////////////
std::map<std::string, void*>CStdMultiFileMapping::m_mapUsed;
std::list<void*>CStdMultiFileMapping::m_lstUnused;
CStdLock CStdMultiFileMapping::m_clsLock;
int CStdMultiFileMapping::m_nSngLen = 0;

CStdMultiFileMapping::CStdMultiFileMapping(const char* const pMapName, const int nSngLen, const int nCount)
:CStdFileMapping(pMapName, nSngLen * nCount) 
{
	Initialize(nSngLen, nCount);
}

CStdMultiFileMapping::~CStdMultiFileMapping(void)
{
	try
	{
		m_mapUsed.clear();
	}
	catch (...)
	{
	}
	try
	{
		m_lstUnused.clear();
	}
	catch (...)
	{
	}
}

void* CStdMultiFileMapping::Mapping(const std::string& subkey)
{
	if (subkey.compare("") == 0)
	{
		return NULL;
	}
	CStdAutoLock theLock(&m_clsLock);

	void* pData = NULL;
	std::map<std::string, void*>::iterator it;
	it = m_mapUsed.find(subkey);
	if (it == m_mapUsed.end())
	{
		std::list<void*>::iterator iter;
		tSTRUCT_BUFF_INFO* pInfo = NULL;
		for (iter = m_lstUnused.begin(); iter != m_lstUnused.end(); )
		{
			pData = (*iter);
			pInfo = (tSTRUCT_BUFF_INFO*)pData;
			// 看看有没有被别的程序使用
			m_lstUnused.erase(iter);
			if (strlen(pInfo->szSubKey) == 0)
			{
				m_mapUsed.insert(std::map<std::string, void*>::value_type(subkey, pData));
				break;
			}
			else
			{
				m_mapUsed.insert(std::map<std::string, void*>::value_type(pInfo->szSubKey, pData));
				iter = m_lstUnused.begin();
				pData = NULL;
			}
		}
	}
	else
	{
		pData = it->second;
	}

	return pData;
}

void CStdMultiFileMapping::UnMapping(const std::string& subkey)
{
	CStdAutoLock theLock(&m_clsLock);

	void* const pData = m_mapUsed[subkey];

	if (NULL != pData)
	{
		memset(pData, 0, m_nSngLen);
		m_mapUsed.erase(subkey);
		m_lstUnused.push_back(pData);
	}
}

void CStdMultiFileMapping::Initialize(const int nSngLen, const int nCount)
{
	if (NULL == m_lpMapData)
	{
		return;
	}
	CStdAutoLock theLock(&m_clsLock);

	m_nSngLen = nSngLen;
	m_mapUsed.clear();

	void* pData = NULL;
	tSTRUCT_BUFF_INFO* pInfo = NULL;
	for (int i = 0; i < nCount; i++)
	{
		pData = (void*)((char*)m_lpMapData + (nSngLen * i));
		pInfo = (tSTRUCT_BUFF_INFO*)pData;
		if (strlen(pInfo->szSubKey) > 0)
		{
			m_mapUsed.insert(std::map<std::string, void*>::value_type(pInfo->szSubKey, pData));
		}
		else
		{
			m_lstUnused.push_back(pData);
		}
	}
}

//////////////////////////////////////////////////////////////////
// 一台机器上所有file buf共用该map, 一个连接会占用两个数据块(收/发)，MAX_MAPPING_COUNT不够时应该加大
CStdMultiFileMapping CStdAutoFileMapping::m_clsMFileMapping("STDBUFF_MAPPING", sizeof(tSTRUCT_BUFF_INFO), MAX_MAPPING_COUNT);

CStdAutoFileMapping::CStdAutoFileMapping(const char* const pIpaddr, const int nPort, const BYTE cDire)
:m_lpMapData(NULL), m_strName("")
{
	char szName[64] = "";
	sprintf(szName, "%s:%d:%d", pIpaddr, nPort, cDire);
	m_strName.assign(szName);
	if (m_strName.compare("") != 0)
	{
		m_lpMapData = m_clsMFileMapping.Mapping(m_strName);
	}
}

CStdAutoFileMapping::~CStdAutoFileMapping(void)
{
	m_clsMFileMapping.UnMapping(m_strName);
	m_lpMapData = NULL;
}

void CStdAutoFileMapping::ReadFileMap(void* const pMapData, const int nLen)
{
	if (NULL != m_lpMapData)
	{
		memcpy(pMapData, m_lpMapData, nLen);
	}
}

void CStdAutoFileMapping::WriteFileMap(const void* const pMapData, const int nLen)
{
	if (NULL != m_lpMapData)
	{
		memcpy(m_lpMapData, pMapData, nLen);
	}
}

void CStdAutoFileMapping::WriteFileMap(const long nPos, const std::string& strName)
{
	if (NULL != m_lpMapData)
	{
		tSTRUCT_BUFF_INFO tInfo;
		strcpy(tInfo.szSubKey, m_strName.c_str());
		strcpy(tInfo.szFileName, strName.c_str());
		tInfo.nValue = nPos;

		memcpy(m_lpMapData, &tInfo, sizeof(tInfo));
	}
}
