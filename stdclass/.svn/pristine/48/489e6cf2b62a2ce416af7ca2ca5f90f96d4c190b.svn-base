// StdMySql_Blk.cpp: implementation of the CStdMySql_Blk class.
//
//////////////////////////////////////////////////////////////////////

#include "StdMySql_Blk.h"

#define BIND_BUFFER_LEN MAX_PATH * 32

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////
CStdMySql_Blk::CStdMySql_Blk(MYSQL * _pmysql)
:m_pmysql(_pmysql),
m_mysqlbind(nullptr),
m_pstmt(nullptr),
m_vecColType(),
m_nCounter(0)
{

}

CStdMySql_Blk::~CStdMySql_Blk(void)
{
	m_pmysql = nullptr;
	
	for (size_t i = 0; i < m_vecColType.size(); i++)
	{
		if (m_mysqlbind == nullptr)
		{
			break;
		}

		if (m_mysqlbind[i].buffer != nullptr)
		{
			delete[] static_cast<BYTE*>(m_mysqlbind[i].buffer);
			m_mysqlbind[i].buffer = nullptr;
		}
	}

	if (m_mysqlbind != nullptr)
	{
		delete[] m_mysqlbind;
		m_mysqlbind = nullptr;
	}
	
	if (m_pstmt != nullptr)
	{
		m_pstmt = nullptr;
	}

	m_vecColType.clear();
}

bool CStdMySql_Blk::GetSqlColumnType(const char* const pTableName, std::vector<int> &coltypevec)
{
	if (nullptr == m_pmysql)
	{
		return false;
	}

	std::string strSql = "select * from " + std::string(pTableName) + " limit 0;";
	if (mysql_real_query(m_pmysql, strSql.c_str(), strSql.length()) != 0)
	{
		return false;
	}

	MYSQL_RES* presult = mysql_store_result(m_pmysql);
	if (presult == nullptr)
	{
		return false;
	}
	
	size_t numcols = mysql_num_fields(presult);
	if (numcols <= 0)
	{
		return false;
	}

	MYSQL_FIELD* pfields = mysql_fetch_fields(presult);
	for (size_t i = 0; i < numcols; i++)
	{
		int ntype = pfields[i].type;
		coltypevec.push_back(ntype);
		//printf("colnum = %d, colname = %s, coltype = %d\n", i, strColName.c_str(), tdbcol.columnType);
	}

	//释放结果集 
	if (presult != nullptr)
	{
		mysql_free_result(presult);
	}

	return true;
}

bool CStdMySql_Blk::Initilize(const char* const pTableName, const char* const pFile)
{
	if (nullptr == m_pmysql)
	{
		return false;
	}

	m_vecColType.clear();
	// 获取表字段属性
	if (!GetSqlColumnType(pTableName, m_vecColType))
	{
		return false;
	}

	// 构造绑定字段属性结构
	size_t colCount = m_vecColType.size();
	m_mysqlbind = new MYSQL_BIND[colCount];
	memset(m_mysqlbind, 0, sizeof(MYSQL_BIND) * colCount);

	std::string str_placeholder = "";

	for (size_t i = 0; i < colCount; i++)
	{
		m_mysqlbind[i].buffer = new BYTE[BIND_BUFFER_LEN]{0};
		m_mysqlbind[i].buffer_type = (enum_field_types)(m_vecColType[i]);
		m_mysqlbind[i].is_null = 0;
		m_mysqlbind[i].length = 0;

		str_placeholder += "?,";
	}
	
	// 准备预处理语句
	std::string stmt_str = "INSERT INTO " + 
							std::string(pTableName) + 
							" VALUES (" + 
							str_placeholder.substr(0, str_placeholder.length() - 1) + 
							");";
	m_pstmt = mysql_stmt_init(m_pmysql);
	if (mysql_stmt_prepare(m_pstmt, stmt_str.c_str(), stmt_str.length())) 
	{
		fprintf(stderr, "预处理错误: %s\n", mysql_stmt_error(m_pstmt));
		return false;
	}

	return true;
}

bool CStdMySql_Blk::Bind(const int nIndex, std::string strValue)
{
	if (m_mysqlbind == nullptr)
	{
		return false;
	}

	int nRet = true;

	// 清理缓存
	memset(m_mysqlbind[nIndex].buffer, 0, BIND_BUFFER_LEN);
	
	auto atype = m_mysqlbind[nIndex].buffer_type;
	switch (atype)
	{
	case MYSQL_TYPE_BIT:
	case MYSQL_TYPE_TINY:
	{
		int nval = atoi(strValue.c_str());
		memcpy(m_mysqlbind[nIndex].buffer, &nval, sizeof(BYTE));
		break;	
	}

	case MYSQL_TYPE_SHORT:
	{
		int nval = atoi(strValue.c_str());
		memcpy(m_mysqlbind[nIndex].buffer, &nval, sizeof(short));
		break;
	}

	case MYSQL_TYPE_LONG:
	{
		int nval = atoi(strValue.c_str());
		memcpy(m_mysqlbind[nIndex].buffer, &nval, sizeof(int));
		break;
	}

	case MYSQL_TYPE_DECIMAL:
	case MYSQL_TYPE_NEWDECIMAL:
	{
		INT64 nval = atoll(strValue.c_str());
		memcpy(m_mysqlbind[nIndex].buffer, &nval, sizeof(INT64));
		break;
	}

	case MYSQL_TYPE_FLOAT:
	{
		float nval = atof(strValue.c_str());
		memcpy(m_mysqlbind[nIndex].buffer, &nval, sizeof(float));
		break;
	}

	case MYSQL_TYPE_DOUBLE:
	{
		double nval = strtod(strValue.c_str(), NULL);
		memcpy(m_mysqlbind[nIndex].buffer, &nval, sizeof(double));
		break;
	}

	case MYSQL_TYPE_JSON:
	case MYSQL_TYPE_ENUM:
	case MYSQL_TYPE_STRING:
	case MYSQL_TYPE_VARCHAR:
	case MYSQL_TYPE_VAR_STRING:
	{
		size_t nlen = strValue.length();
		nlen = nlen >= BIND_BUFFER_LEN ? (BIND_BUFFER_LEN - 1) : nlen;
		memcpy(m_mysqlbind[nIndex].buffer, strValue.c_str(), nlen);
		m_mysqlbind[nIndex].buffer_length = nlen;
		break;
	}

	case MYSQL_TYPE_BLOB:
	case MYSQL_TYPE_TINY_BLOB:
	case MYSQL_TYPE_MEDIUM_BLOB:
	case MYSQL_TYPE_LONG_BLOB:
	{
		BYTE bData[BIND_BUFFER_LEN]{0};
		char cvalue[BIND_BUFFER_LEN]{0};
		int nlen = strValue.length();
		nlen = nlen > BIND_BUFFER_LEN ? BIND_BUFFER_LEN : nlen;
		memcpy(cvalue, strValue.c_str(), nlen);
		nlen = HexStrToByteWithoutMidBlank(cvalue, bData, 1);
		memcpy(m_mysqlbind[nIndex].buffer, bData, nlen);
		m_mysqlbind[nIndex].buffer_length = nlen;
		break;
	}

	case MYSQL_TYPE_DATETIME:
	case MYSQL_TYPE_DATETIME2:	
	default:
	{
		nRet = false;
		break;
	}

	}

	return nRet;
}

bool CStdMySql_Blk::Done(void)
{
	if (mysql_stmt_bind_param(m_pstmt, m_mysqlbind)) 
	{
		fprintf(stderr, "参数绑定错误: %s\n", mysql_stmt_error(m_pstmt));
		mysql_stmt_close(m_pstmt);
		return false;
	}

	// 执行语句
	if (mysql_stmt_execute(m_pstmt)) 
	{
		fprintf(stderr, "执行错误: %s\n", mysql_stmt_error(m_pstmt));
		return false;
	}
	
	return true;
}

bool CStdMySql_Blk::IsColNumEqual(size_t colnum)
{
	return m_vecColType.size() == colnum;
}

int CStdMySql_Blk::HexStrToByteWithoutMidBlank(char* const pStr, BYTE* const pBinary, BYTE btype)
{
	int nLen = strlen(pStr);

	if (nLen == 0)
	{
		return 0;
	}

	for (int j = nLen - 1; j >= 0; j--)
	{
		if ((pStr[j] == '\r') 
			|| (pStr[j] == '\n')
			|| (pStr[j] == ' '))
		{
			pStr[j] = 0;
		}
		else
		{
			break;
		}
	}

	nLen = strlen(pStr);

	if (btype == 0)
	{
		if (nLen > (8192 - 1) * 2)
		{
			nLen = (8192 - 1) * 2;
		}
		else
		{
			//do nothing...
		}
	}

	int nValue = -1;

	char szByte[3];

	for (int i = 0; i < nLen; i = i + 2)
	{
		szByte[0] = pStr[i];
		szByte[1] = pStr[i + 1];
		szByte[2] = 0;

		sscanf(szByte, "%X", &nValue);

		if (nValue == -1)
		{
			break;
		}

		pBinary[i / 2] = nValue;
	}
	
	return nLen / 2;
}