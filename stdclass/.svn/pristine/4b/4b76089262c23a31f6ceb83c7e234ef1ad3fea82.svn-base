#ifndef __STDDATAOBJECT_H__
#define __STDDATAOBJECT_H__

#include "./StdHeader.h"

//定义统一查询结果
//参数个数,参数类型,参数值
#define TYPE_CS_TINYINT   1  //UNSIGNED BYTE
#define TYPE_CS_SMALLINT  2  //short
#define TYPE_CS_USMALLINT 3  //ushort
#define TYPE_CS_FLOAT     5  //double
#define TYPE_CS_TEXT      6  //char
#define TYPE_CS_INT_FLOAT 7  //float*1000
#define TYPE_CS_INT       8  //int
#define TYPE_CS_VARYBIN  	9 // BIN、字符串

#define TYPE_CS_VARYBIN_MERGE 10    //2000长度字符
#define TYPE_CS_TEXT_MERGE    11    //2000长度字符
#define TYPE_CS_VARTEXT       12
#define TYPE_CS_UINT64        13    //8字节无符号整数
#define TYPE_CS_INT64         14    //8字节有符号整数

class CDataObject
{
public:
	enum E_DataType
	{
        E_DataType_Unknown = 0,
        E_DataType_Boolean,
		E_DataType_Char,
		E_DataType_Byte,
        E_DataType_SmallInt,
        E_DataType_Int,
        E_DataType_Int64,
		E_DataType_TinyInt,
		E_DataType_UInt16,
        E_DataType_UInt32,
		E_DataType_UInt64 = 10,
		E_DataType_Single,
        E_DataType_Double,
        E_DataType_Text,
        E_DataType_DateTime,
		E_DataType_TimePeriod,
		E_DataType_Color,
        E_DataType_Float,
        E_DataType_Object,
        E_DataType_Image,
		E_DataType_Numeric
	};
};

#endif

