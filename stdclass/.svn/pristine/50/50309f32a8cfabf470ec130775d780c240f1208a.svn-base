
#pragma once
#include "StdFile.h"
#include "StdLock.h"

#define BUF_SIZE 2*1024*1024
class CCycBuffer
{
public:
	<PERSON>yc<PERSON>uffer(void);
	CCycBuffer(const int nBufSize);
	~<PERSON>yc<PERSON>uffer(void);

	unsigned int ReadToFile(CStdFile &outputFile, const int nCount);
	unsigned int Read(BYTE * const pBuf, const int nCount);
	unsigned int Write(const BYTE * const pBuf, const int nCount);
	unsigned int Peek(BYTE * const pBuf, const int nCount);
	unsigned int GetSize();
	void Reset(void);

private:
	void Initialize(void);

	BYTE * m_pBuffer;
	int m_nBufSize;
 	CStdLock m_Lock;
	BYTE * m_pRdPos;
	BYTE * m_pWtPos;
	// true:read false:write
	bool m_bFlag; 
};
