#ifndef __STDTIME_H__
#define __STDTIME_H__

#include "./StdHeader.h"

class CStdTime
{
public:
	CStdTime(int nSec);
	CStdTime(const CStdTime& time);
	CStdTime(void);
  ~CStdTime(void);

  int GetYear(void)const;
	BOOL SetYear(int year);
	int GetMonth(void)const;
	BOOL SetMonth(int month);
	int GetDay(void)const;
	BOOL SetDay(int day);
	int GetHour(void)const;
	BOOL SetHour(int hour);
	int GetMinute(void)const;
	BOOL SetMinute(int minute);
	int GetSecond(void)const;
	BOOL SetSecond(int second);
	int GetAllSecond(void)const;
	BOOL SetAllSecond(int second);

	void AddSec(const int sec);
	void AddMinute(const int minute);
	void AddHour(const int hour);
	void AddDay(const int day);
	void AddMonth(const int month);
	void AddYear(const int year);

	int Compare(CStdTime tm_time)
	{
       return GetAllSecond() - tm_time.GetAllSecond(); 
	}
    
    void Copy(CStdTime& time)
	{
		m_nSec = time.m_nSec;
		fresh();
	}

private:
	time_t m_nSec;
	struct tm m_time;
	void fresh(void);

};

#endif
