
#include "./StdThread.h"
#include "StdCommond.h"
#include "StdMonitor.h"

CStdThread::CStdThread(void)
:m_tThreadCreateTime(time(NULL)),
m_tHeartBeatTime(time(NULL)),
m_nThread(0), 
m_bRunflag(true), 
m_bStopflag(true), 
m_nThrdAddr(0),
m_nSleepStep(0)
{
	memset(&m_tTrdInfo, 0, sizeof(m_tTrdInfo));
}

CStdThread::~CStdThread(void)
{
	m_nThread = 0;
	m_bRunflag = true;
	m_bStopflag = true;
	m_nThrdAddr = 0;
	m_tHeartBeatTime = 0;
	m_tThreadCreateTime = 0;
}

bool CStdThread::CreateThread(const unsigned int nStackSize, const CStdThreadActor* const pActor)
{
	m_bRunflag = true;
	m_bStopflag = false;
	m_nThread = 0;
	m_tHeartBeatTime = time(NULL);
	m_tThreadCreateTime = time(NULL);

	m_tTrdInfo.pThread = this;
	if (pActor == NULL)
	{
		m_tTrdInfo.pActor = this;
	}
	else
	{
		m_tTrdInfo.pActor = (CStdThreadActor*)pActor;
	}

	pthread_attr_t _attr;
	pthread_attr_init(&_attr);
	if (nStackSize > 0)
	{
		pthread_attr_setstacksize(&_attr, nStackSize);
	}
	pthread_attr_setdetachstate (&_attr, PTHREAD_CREATE_DETACHED); 
	pthread_create(&m_nThread, &_attr, ThreadProc, (void*)(&m_tTrdInfo));
	pthread_attr_destroy(&_attr);

	if (0 >= m_nThread)
	{
		return false;
	}
	return true;
}

void CStdThread::EndThread(void)
{
	if( 0 != m_nThread )
	{
		pthread_exit(&m_nThread);

		m_nThread = 0;
	}
}

void CStdThread::CloseThread(const int nmsec)
{
	m_bRunflag = false;
	m_tHeartBeatTime = 0;

	int nspan = 0;
	while ((!m_bStopflag) && (nmsec > nspan))
	{
		SetStop();
		nspan = nspan + 5;
		CStdCommond::Sleep(5);
	}

	if (!m_bStopflag)
	{
		pthread_kill(m_nThread, SIGQUIT);
	}
}

void CStdThread::Sleeping(unsigned int nSec)
{
	for (m_nSleepStep = 0; (m_nSleepStep < nSec) && Runflag(); m_nSleepStep++)
	{
		CStdCommond::Sleep(1000);
	}
}

/**线程心跳监控
 * 线程挂死2分钟（nHeartBeatMaxSpan），则需要清理了。
 */
bool CStdThread::IsHeartBeat(const unsigned int nHeartBeatMaxSpan/* = 120*/)
{
	unsigned int nSpan = (unsigned int)(time(NULL) - m_tHeartBeatTime);

	if (nSpan >= nHeartBeatMaxSpan)
	{
		char szLogInfo[MAX_PATH] = { 0 };

		struct tm when = *localtime(&m_tHeartBeatTime);

		sprintf(szLogInfo, "[CStdThread_Warning：] - 线程[Thread = 0x%08lX]心跳监控：Is No HeartBeat(Span=%d >= Max=%d)，ThreadCreateTime=%d-%02d-%02d %02d:%02d:%02d",
			GetThreadId(), nSpan, nHeartBeatMaxSpan, when.tm_year - 100 + 2000, when.tm_mon + 1, when.tm_mday, when.tm_hour, when.tm_min, when.tm_sec);

		OUTPUT_ERR(szLogInfo);

		return false;
	}

	return true;
};
