// StdMySql.cpp: implementation of the CStdMySql class.
//
//////////////////////////////////////////////////////////////////////

#include "./StdMySql.h"
// #include <iconv.h>

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////
CStdMySql::CStdMySql(void)
:m_mysql(nullptr),
m_result(nullptr),
m_row(nullptr),
m_fields(nullptr),
m_lengths(nullptr)
{
	memset(cDbName, 0, sizeof(cDbName));
}

CStdMySql::~CStdMySql(void)
{
	m_mysql = nullptr;
	m_result = nullptr;
	m_row = nullptr;
	m_fields = nullptr;
	m_lengths = nullptr;
}

void CStdMySql::Clear_Con(void)
{
	m_mysql = nullptr;
	m_result = nullptr;
	m_row = nullptr;
	m_fields = nullptr;
	m_lengths = nullptr;
	memset(cDbName, 0, sizeof(cDbName));
}

BOOL CStdMySql::IsAvailable(void)
{
	if (m_mysql == nullptr)
	{
		return FALSE;
	}

	if (strcmp(cDbName, "") == 0)
	{
		return FALSE;
	}

	return TRUE;
}

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////
CStdMySql_Connection::CStdMySql_Connection(void)
:m_mapTransform(),
m_pMySql(nullptr),
m_pBcpMySql(nullptr),
m_pBcpLog(nullptr)
{
	m_pMySql = new CStdMySql();

	m_mapTransform.insert(std::make_pair(MYSQL_TYPE_TINY, &CStdMySql_Connection::transform_tinyint_into_buffer));
	m_mapTransform.insert(std::make_pair(MYSQL_TYPE_SHORT, &CStdMySql_Connection::transform_short_into_buffer));
	m_mapTransform.insert(std::make_pair(MYSQL_TYPE_LONG, &CStdMySql_Connection::transform_int_into_buffer));
	m_mapTransform.insert(std::make_pair(MYSQL_TYPE_FLOAT, &CStdMySql_Connection::transform_float_into_buffer));
	m_mapTransform.insert(std::make_pair(MYSQL_TYPE_DOUBLE, &CStdMySql_Connection::transform_double_into_buffer));
	m_mapTransform.insert(std::make_pair(MYSQL_TYPE_LONGLONG, &CStdMySql_Connection::transform_int64_into_buffer));
	m_mapTransform.insert(std::make_pair(MYSQL_TYPE_VAR_STRING, &CStdMySql_Connection::transform_string_into_buffer));
	m_mapTransform.insert(std::make_pair(MYSQL_TYPE_DATETIME, &CStdMySql_Connection::transform_string_into_buffer));
}

CStdMySql_Connection::CStdMySql_Connection(CStdLog* _bcplog)
:m_mapTransform(),
m_pMySql(nullptr),
m_pBcpMySql(nullptr),
m_pBcpLog(_bcplog)
{
	m_pMySql = new CStdMySql();
	m_pBcpMySql = new CStdBcp_MySql(m_pBcpLog);

	m_mapTransform.insert(std::make_pair(MYSQL_TYPE_TINY, &CStdMySql_Connection::transform_tinyint_into_buffer));
	m_mapTransform.insert(std::make_pair(MYSQL_TYPE_SHORT, &CStdMySql_Connection::transform_short_into_buffer));
	m_mapTransform.insert(std::make_pair(MYSQL_TYPE_LONG, &CStdMySql_Connection::transform_int_into_buffer));
	m_mapTransform.insert(std::make_pair(MYSQL_TYPE_FLOAT, &CStdMySql_Connection::transform_float_into_buffer));
	m_mapTransform.insert(std::make_pair(MYSQL_TYPE_DOUBLE, &CStdMySql_Connection::transform_double_into_buffer));
	m_mapTransform.insert(std::make_pair(MYSQL_TYPE_LONGLONG, &CStdMySql_Connection::transform_int64_into_buffer));
	m_mapTransform.insert(std::make_pair(MYSQL_TYPE_VAR_STRING, &CStdMySql_Connection::transform_string_into_buffer));
	m_mapTransform.insert(std::make_pair(MYSQL_TYPE_DATETIME, &CStdMySql_Connection::transform_string_into_buffer));
}

CStdMySql_Connection::~CStdMySql_Connection(void)
{
	if (m_pMySql != nullptr)
	{
		cleanup_conn();
		delete m_pMySql;
		m_pMySql = nullptr;
	}

	if (m_pBcpMySql != nullptr)
	{
		delete m_pBcpMySql;
		m_pBcpMySql = nullptr;
	}
	
	m_pBcpLog = nullptr;
}

BOOL CStdMySql_Connection::connect(
				const char* const szUser,
				const char* const szPwd,
				const char* const szSvr,
				const char* const szDb,
				const int nPort)
{
	if (!m_pMySql->IsAvailable())
	{
		if (!setup_conn(szUser, szPwd, szSvr, szDb, nPort))
		{
			cleanup_conn();
			return FALSE;
		}
	}

	return TRUE;
}

BOOL CStdMySql_Connection::init_db(void)
{
	try
	{
		mysql_library_init(0, NULL, NULL);
	}
	catch(...)
	{
		return FALSE;
	}

	return TRUE;
}

BOOL CStdMySql_Connection::setup_conn(
				const char* const szUser,
				const char* const szPwd,
				const char* const szSvr,
				const char* const szDb,
				const int nPort)
{
	if (!init_db())
	{
		return FALSE;
	}

	if ((szUser == nullptr) || (strcmp(szUser, "") == 0) ||
		(szPwd == nullptr) || (strcmp(szPwd, "") == 0) ||
		(szSvr == nullptr) || (strcmp(szSvr, "") == 0) || nPort <= 1000)
	{
		return FALSE;
	}

	m_pMySql->m_mysql = mysql_init(nullptr); // 初始化MySQL连接

	if (m_pMySql->m_mysql == nullptr) 
	{
		OUTPUT_ERR("mysql_init() failed!...");
		return FALSE;
	}

	// 设置字符集为utf8mb4
	mysql_options(m_pMySql->m_mysql, MYSQL_SET_CHARSET_NAME, "utf8mb4");

	// 连接到MySQL数据库服务器
	auto mysql_ret = mysql_real_connect(m_pMySql->m_mysql, szSvr, szUser, szPwd, szDb, nPort, nullptr, 0);

	if (mysql_ret == nullptr) 
	{
		// std::string strer = mysql_error(m_pMySql->m_mysql);
		OUTPUT_ERR("mysql_real_connect() failed!...");
		cleanup_conn();
		return FALSE;
	}
	
  	strcpy(m_pMySql->cDbName, szDb);
	
	return TRUE;
}

void CStdMySql_Connection::cleanup_conn(void)
{
	if (!m_pMySql->IsAvailable())
	{
		return;
	}

	ClearResult();
	mysql_close(m_pMySql->m_mysql); // 关闭连接
	m_pMySql->Clear_Con();
	mysql_library_end();
}

BOOL CStdMySql_Connection::ClearResult(void)
{
	if (m_pMySql->m_result != nullptr)
	{
		mysql_free_result(m_pMySql->m_result);
	}

	return TRUE;
}

BOOL CStdMySql_Connection::BindCol(int ncolnum, BYTE* pbuff, int bufflen)
{
	if (m_pMySql->m_result == nullptr
		|| m_pMySql->m_row == nullptr)
	{
		return FALSE;
	}
	
	if (m_pMySql->m_result->field_count <= (unsigned int)ncolnum)
	{
		return FALSE;
	}
	
	if (pbuff == nullptr || bufflen <= 0)
	{
		return FALSE;
	}

	int nlen = bufflen;

	m_pMySql->m_lengths = mysql_fetch_lengths(m_pMySql->m_result);

	if (m_pMySql->m_lengths[ncolnum] < (unsigned long)bufflen)
	{
		nlen = m_pMySql->m_lengths[ncolnum];
	}
	
	transform_fieldValue_into_buffer(pbuff, ncolnum, m_pMySql->m_row[ncolnum], nlen);
	// memcpy(pbuff, m_pMySql->m_row[ncolnum], nlen);
	
	return TRUE;
}

BOOL CStdMySql_Connection::transform_int_into_buffer(BYTE* targetbuff, char* valuebuff, int valuebufflen)
{
	int nvalue = atoi(valuebuff);
	memcpy(targetbuff, &nvalue, sizeof(int));
	return TRUE;
}
BOOL CStdMySql_Connection::transform_string_into_buffer(BYTE* targetbuff, char* valuebuff, int valuebufflen)
{
	memcpy(targetbuff, valuebuff, valuebufflen);
	return TRUE;
}
BOOL CStdMySql_Connection::transform_tinyint_into_buffer(BYTE* targetbuff, char* valuebuff, int valuebufflen)
{
	BYTE nvalue = atoi(valuebuff);
	memcpy(targetbuff, &nvalue, sizeof(BYTE));
	return TRUE;
}
BOOL CStdMySql_Connection::transform_short_into_buffer(BYTE* targetbuff, char* valuebuff, int valuebufflen)
{
	int nvalue = atoi(valuebuff);
	memcpy(targetbuff, &nvalue, sizeof(short));
	return TRUE;
}
BOOL CStdMySql_Connection::transform_float_into_buffer(BYTE* targetbuff, char* valuebuff, int valuebufflen)
{
	float nvalue = atof(valuebuff);
	memcpy(targetbuff, &nvalue, sizeof(float));
	return TRUE;
}
BOOL CStdMySql_Connection::transform_double_into_buffer(BYTE* targetbuff, char* valuebuff, int valuebufflen)
{
	double nvalue = strtod(valuebuff, nullptr);
	memcpy(targetbuff, &nvalue, sizeof(double));
	return TRUE;
}
BOOL CStdMySql_Connection::transform_int64_into_buffer(BYTE* targetbuff, char* valuebuff, int valuebufflen)
{
	long long nvalue = atoll(valuebuff);
	memcpy(targetbuff, &nvalue, sizeof(INT64));
	return TRUE;
}

BOOL CStdMySql_Connection::transform_fieldValue_into_buffer(BYTE* targetbuff, int ncolnum, char* valuebuff, int valuebufflen)
{
	if (m_pMySql->m_fields == nullptr)
	{
		if (m_pMySql->m_result == nullptr)
		{
			return FALSE;
		}
		else
		{
			m_pMySql->m_fields = mysql_fetch_fields(m_pMySql->m_result);
		}
	}

	auto ntype = m_pMySql->m_fields[ncolnum].type;

	auto itTranser = m_mapTransform.find(ntype);

	if (itTranser != m_mapTransform.end())
	{
		(this->*(itTranser->second))(targetbuff, valuebuff, valuebufflen);
	}
	else
	{
		OUTPUT_ERR("unknown transform fieldValue datatype !...");
	}

	return TRUE;
}

BOOL CStdMySql_Connection::select_dbname(void)
{
	if (!m_pMySql->IsAvailable())
	{
		OUTPUT_ERR("[CStdMySql_Connection::select_dbname] Connect Info is Invalid!...");
		return FALSE;
	}

	// 连接数据库
	if(mysql_select_db(m_pMySql->m_mysql, m_pMySql->cDbName) != 0)
	{
		OUTPUT_ERR("[CStdMySql_Connection::select_dbname] Open data basename fail!...");
		return FALSE;
	}

	return TRUE;
}

int CStdMySql_Connection::Datalen(int colnum)
{
	if (m_pMySql->m_result == nullptr
		|| m_pMySql->m_row == nullptr)
	{
		return FALSE;
	}
	
	if (m_pMySql->m_result->field_count <= (u_int)colnum)
	{
		return FALSE;
	}

	return (int)(m_pMySql->m_lengths[colnum]);
}

BOOL CStdMySql_Connection::BcpData(const char* const pFile, const char* const pTable)
{
	if (!select_dbname())
	{
		return FALSE;
	}
	
	bool bRt = m_pBcpMySql->BcpData(m_pMySql->m_mysql, pFile, pTable);
	if (!bRt)
	{
		return FALSE;
	}

	return TRUE;
}

BOOL CStdMySql_Connection::Execute(const char* cpSql)
{
	if (!select_dbname())
	{
		return FALSE;
	}

	if (mysql_real_query(m_pMySql->m_mysql, cpSql, strlen(cpSql)) != 0)
	{
		OUTPUT_ERR("[CStdMySql_Connection::Execute] Query Sql Exec error!...");
		return FALSE;
	}

	m_pMySql->m_result = mysql_store_result(m_pMySql->m_mysql);

	return TRUE;
}

int CStdMySql_Connection::DbResults(void)
{
	if (m_pMySql->m_result == nullptr)
	{
		return -1;
	}
	
	return m_pMySql->m_result->row_count;
}

int CStdMySql_Connection::DbNextRow(void)
{
	if (m_pMySql->m_result == nullptr)
	{
		return -1;
	}

	m_pMySql->m_row = mysql_fetch_row(m_pMySql->m_result);
	
	if (m_pMySql->m_row == nullptr)
	{
		return -1;
	}

	return 0;
}

int CStdMySql_Connection::DbRowColNum(void)
{
	if (m_pMySql->m_result == nullptr)
	{
		return -1;
	}

	int num_fields = mysql_num_fields(m_pMySql->m_result);

	return num_fields;
}

BOOL CStdMySql_Connection::GetSelectColAttribute(const char* c_sql, std::vector<STRU_DBCOL>& vecColAttri)
{
	// 查询
	if (!Execute(c_sql))
    {
		cleanup_conn();
		return FALSE;
    }

	if (m_pMySql->m_result == nullptr)
	{
		return FALSE;
	}
	
	size_t numcols = mysql_num_fields(m_pMySql->m_result);
	if (numcols <= 0)
	{
		return FALSE;
	}

	m_pMySql->m_fields = mysql_fetch_fields(m_pMySql->m_result);
	// 查看命令执行的结果。
	for (size_t i = 0; i < numcols; i++)
	{		
		STRU_DBCOL tdbcol;
		std::string strColName(m_pMySql->m_fields[i].name);
		tdbcol.columnName = strColName;
		tdbcol.columnType = m_pMySql->m_fields[i].type;
		tdbcol.colid = i;
		vecColAttri.push_back(tdbcol);
		//printf("colnum = %d, colname = %s, coltype = %d\n", i, strColName.c_str(), tdbcol.columnType);
	}

	//关闭数据库连接  
	cleanup_conn();

	return TRUE;
}

BOOL CStdMySql_Connection::GetSelectColAttribute(std::vector<STRU_DBCOL>& vecColAttri)
{
	if (m_pMySql->m_result == nullptr)
	{
		return FALSE;
	}
	
	size_t numcols = mysql_num_fields(m_pMySql->m_result);
	if (numcols <= 0)
	{
		return FALSE;
	}

	m_pMySql->m_fields = mysql_fetch_fields(m_pMySql->m_result);
	// 查看命令执行的结果。
	for (size_t i = 0; i < numcols; i++)
	{		
		STRU_DBCOL tdbcol;
		std::string strColName(m_pMySql->m_fields[i].name);
		tdbcol.columnName = strColName;
		tdbcol.columnType = m_pMySql->m_fields[i].type;
		tdbcol.colid = i;
		vecColAttri.push_back(tdbcol);
		//printf("colnum = %d, colname = %s, coltype = %d\n", i, strColName.c_str(), tdbcol.columnType);
	}

	return TRUE;
}





