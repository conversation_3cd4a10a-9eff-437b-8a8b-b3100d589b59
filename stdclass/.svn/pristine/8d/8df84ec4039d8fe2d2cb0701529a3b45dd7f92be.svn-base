// StdIniFile.cpp: implementation of the CStdIniFile class.
//
//////////////////////////////////////////////////////////////////////

#include "StdProfile.h"
#include "StdCommond.h"

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////
CStdLock CStdProfile::m_StdLock;

CStdProfile::CStdProfile(const char* const pProfile)
:m_ProfileName(pProfile), m_strSec(""), m_Section()
{
	Initialize();
}

CStdProfile::~CStdProfile(void)
{
	try
	{
		m_Section.clear();
	}
	catch (...)
	{
		
	}
}

bool CStdProfile::GetProStrValue(const char* const pSec, const char* const pKey, char* const pValue, const char* const pDefault, const bool bCreat)
{
	std::string strKey("[");

	strKey.append(pSec);
	strKey.append("]::");
	strKey.append(pKey);
	if (strKey.compare("[]::") == 0)
	{
		return false;
	}

	std::map<std::string, tSTRUCT_INIINFO>::iterator it;
	it = m_Section.find(strKey);
	if (it != m_Section.end())
	{
		strcpy(pValue, it->second.strValue.c_str());
	}
	else
	{
		SetProStrValue(pSec, pKey, pDefault, bCreat);
		strcpy(pValue, pDefault);
	}

	return true;
}

bool CStdProfile::GetProIntValue(const char* const pSec, const char* const pKey, int& nValue, const char* const pDefault, const bool bCreat)
{
	char szValue[64] = "";
	if (GetProStrValue(pSec, pKey, szValue, pDefault, bCreat))
	{
		if (strcmp(szValue, "") != 0)
		{
			nValue = atoi(szValue);
		}
		else
		{
			nValue = atoi(pDefault);
		}
		return true;
	}

	return false;
}

void CStdProfile::Initialize(void)
{
	CStdFile iniFile;

	CStdAutoLock theLock(&m_StdLock);

	if (!iniFile.Open(m_ProfileName.c_str(), "rt"))
	{
		return;
	}

	char szContext[1024] = "";
	char* pCtx = NULL;
	char* pInfo = NULL;

	pCtx = iniFile.GetStr(szContext, sizeof(szContext));
	while(NULL != pCtx)
	{
		pInfo = NULL;
		pCtx = ClearContext(szContext, &pInfo); 

		GetContext(pCtx, pInfo);

		strcpy(szContext, "");
		pCtx = iniFile.GetStr(szContext, sizeof(szContext));
	}

	iniFile.Close();
}

void CStdProfile::GetContext(char* const pContxt, const char* const pInfo)
{
	if (pContxt == NULL)
	{
		return;
	}

	if (pContxt[0] == '[')
	{
		m_strSec.assign(pContxt);
	}
	else
	{
		MakeEntry(pContxt, pInfo);
	}
}

char* CStdProfile::ClearContext(char* pContxt, char** const pInfo)
{
	*pInfo = NULL;
	if (pContxt == NULL)
	{
		return NULL;
	}
	// 去掉注释
	char* pSub = strstr(pContxt, ";");
	if (pSub != NULL)
	{
		*pInfo = pSub + 1;
		pSub[0] = '\0';
	}

	pSub = strstr(pContxt, "#");
	if (pSub != NULL)
	{
		*pInfo = pSub + 1;
		pSub[0] = '\0';
	}

	size_t nLen = strlen(pContxt);
	// 去掉前导空格
	for (unsigned int i = 0; i < nLen; i++)
	{
		if ((pContxt[0] == ' ') || (pContxt[0] == '\t'))
		{
			pContxt++;
			continue;
		}
		break;
	}
	// 去掉后置空格
	nLen = strlen(pContxt);
	for (int j = ((int)nLen) - 1; j >= 0 ; j--)
	{
		if ((pContxt[j] == ' ') || 
			(pContxt[j] == '\t') ||
			(pContxt[j] == '\n') ||
			(pContxt[j] == '\r'))
		{
			pContxt[j] = '\0';
			continue;
		}
		break;
	}

	nLen = strlen(pContxt);
	if (nLen == 0)
	{
		return NULL;
	}
	return pContxt;
}

// [...]Entry, value, 认为=左右没有空格
// 对=左右的空格进行了处理。唐建中，2007-3-12。
void CStdProfile::MakeEntry(char* const pContxt, const char* const pInfo)
{
	std::string strKey(m_strSec);
	
	char* pSub = strstr(pContxt, "=");
	if (pSub != NULL)
	{
		pSub[0] = '\0';
		pSub++;
	}

    while (pSub[0] == ' ')
    {
        pSub++;
    }

	strKey.append("::");

	size_t length = strlen(pContxt);
	for (size_t i=length-1; pContxt[i]==' '; i--) 
	{
			pContxt[i] = 0;
	}

	strKey.append(pContxt);

	tSTRUCT_INIINFO tTmp;
	tTmp.strValue.assign(pSub);
	if (pInfo != NULL)
	{
		tTmp.strInfo.assign(pInfo);
	}
	else
	{
		tTmp.strInfo.assign("\n");
	}
	m_Section.insert(std::map<std::string, tSTRUCT_INIINFO>::value_type(strKey, tTmp));
}

void CStdProfile::SetProStrValue(const char* const pSec, const char* const pKey, const char* const pValue, const bool bCreat)
{
	std::string strKey("[");
	strKey.append(pSec);
	strKey.append("]::");
	strKey.append(pKey);

	std::map<std::string, tSTRUCT_INIINFO>::iterator it;
	it = m_Section.find(strKey);
	if (it != m_Section.end())
	{
		it->second.strValue.assign(pValue);
	}
	else
	{
		tSTRUCT_INIINFO tTmp;
		tTmp.strValue.assign(pValue);
		tTmp.strInfo.assign("\n");
		m_Section.insert(std::map<std::string, tSTRUCT_INIINFO>::value_type(strKey, tTmp));
	}

	if (bCreat)
	{
		UpdateProfile();
	}
}

void CStdProfile::SetProIntValue(const char* const pSec, const char* const pKey, const int nValue, const bool bCreat)
{
	char szVal[64];

	CStdCommond::itoa(nValue, szVal, 10);

	SetProStrValue(pSec, pKey, szVal, bCreat);
}

void CStdProfile::UpdateProfile(void)
{
	CStdAutoLock theLock(&m_StdLock);
	
	char szVal[1024];
	sprintf(szVal, "%s.bak", m_ProfileName.c_str());
	
	CStdFile::Remove(szVal);
	CStdFile::Rename(m_ProfileName.c_str(), szVal);

	CStdFile iniFile;

	if (!iniFile.Open(m_ProfileName.c_str(), "a+t"))
	{
		return;
	}

	char szContext[1024] = "";
	char* pSub;
	std::string strSec("");

	std::map<std::string, tSTRUCT_INIINFO>::iterator it;

	for(it = m_Section.begin(); it != m_Section.end(); it++)
	{
		strcpy(szContext, it->first.c_str());

		pSub = strstr(szContext, ":");
		if (pSub == NULL)
		{
			continue;
		}

		pSub[0] = '\0';
		pSub[1] = '\0';
		pSub = pSub + 2;

		if (strSec.compare(szContext) != 0)
		{
			strSec.assign(szContext);
			if (it != m_Section.begin())
			{
				iniFile.PutStr("\n");
			}

			strcpy(szVal, szContext);
			CorrectStr(szVal);
			iniFile.PutStr(szVal);
		}
		strcat(pSub, " = ");
		strcat(pSub, it->second.strValue.c_str());
		if (it->second.strInfo.compare("\n") != 0)
		{
			strcat(pSub, "\t\t;");
		}
		strcat(pSub, it->second.strInfo.c_str());

		strcpy(szVal, pSub);
		CorrectStr(szVal);
		iniFile.PutStr(szVal);
	}

	iniFile.Close();
}

void CStdProfile::CorrectStr(char* pStr)
{
	size_t len = strlen(pStr);

	while (len > 0)
	{
		if ((pStr[len - 1] == '\r') || (pStr[len - 1] == '\n'))
		{
			pStr[len - 1] = 0;
		}
		else
		{
			break;
		}

		len = len - 1;
	}

	strcat(pStr, "\n");
}

bool CStdProfile::DelSection(const char* const pSec)
{
	if (strlen(pSec) == 0)
	{
		return false;
	}

	char szContext[1024] = "";
	char* pSub;
	// 给段名加上中括号
	std::string strSec("[");	
	strSec.append(pSec);
	strSec.append("]");

	bool isNeedUpDate = false;

	std::map<std::string, tSTRUCT_INIINFO>::iterator it;
	
	for(it = m_Section.begin(); it != m_Section.end(); )
	{
		strcpy(szContext, it->first.c_str());
		
		pSub = strstr(szContext, ":");
		if (pSub == NULL)
		{
			continue;
		}
		
		pSub[0] = '\0';
		pSub[1] = '\0';
		pSub = pSub + 2;
		
		if (strSec.compare(szContext) == 0)
		{
			it = m_Section.erase(it);

			isNeedUpDate = true;

			continue;
		}

		it++;
	}

	if (isNeedUpDate)
	{
		UpdateProfile();
	}

	return true;
}

bool CStdProfile::DeleteKey(const char* const pSec, const char* const pKey)
{
	std::string strKey("[");
	
	strKey.append(pSec);
	strKey.append("]::");
	strKey.append(pKey);
	if (strKey.compare("[]::") == 0)
	{
		return false;
	}
	
	std::map<std::string, tSTRUCT_INIINFO>::iterator it;
	it = m_Section.find(strKey);

	if (it != m_Section.end())
	{
		it = m_Section.erase(it);

		UpdateProfile();
	}

	return true;
}
