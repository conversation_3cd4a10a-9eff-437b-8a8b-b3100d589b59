// StdBcp_MySql.h: interface for the CStdBcp_MySql class.
//
//////////////////////////////////////////////////////////////////////

#ifndef __STDBCP_MYSQL_H__
#define __STDBCP_MYSQL_H__

#include "./StdMonitor.h"
#include "./StdMySql_Blk.h"

class CStdBcp_MySql
{
public:
	CStdBcp_MySql(CStdLog* const pLog);

	~CStdBcp_MySql(void);

	bool BcpData(MYSQL *_pmysql, const char* const pFile, const char* const pTable);

private:
	bool BlkData(CStdMySql_Blk &MySqlBlk, int &nIndex);
	void SplitRowData(int &nIndex, std::vector<std::string> &vecfields);
	void TrimTailEnter(char* cptr);
	void TrimSpace(char** sptr);
	char* m_pstrRec;
	char* m_Token;

	CStdLog* m_pLog;
};



#endif 

