// StdMySql.h: interface for the CStdMySql class.
//
//////////////////////////////////////////////////////////////////////

#ifndef __STDFREETDS_H__
#define __STDFREETDS_H__

#include "./StdDbHeader.h"
#include "./StdBcp_MySql.h"
#include "./StdMonitor.h"
#include <mysql/mysql.h>

class CStdMySql
{
public:
	CStdMySql(void);
	~CStdMySql(void);
		
	MYSQL *m_mysql; // 句柄: 连接oceanbase服务器地址和端口号
	MYSQL_RES *m_result; // Sql 语句执行结果集
	MYSQL_ROW m_row;
	MYSQL_FIELD *m_fields;
	unsigned long *m_datalens;
	char cDbName[64];

	void Clear_Con(void);
	BOOL IsAvailable(void);
};

class CStdMySql_Connection
{
public:
	CStdMySql_Connection(void);
	CStdMySql_Connection(CStdLog* _bcplog);

	~CStdMySql_Connection(void);
	
	BOOL connect(const char* const szUser,
					const char* const szPwd,
					const char* const szSvr,
					const char* const szDb = NULL,
					const int nPort = 2881);
	
	BOOL Execute(const char* cpSql);
	int DbResults(void);
	int DbNextRow(void);
	int DbRowColNum(void);
	BOOL GetSelectColAttribute(std::vector<STRU_DBCOL>& vecColAttri);
	BOOL GetSelectColAttribute(const char* pSql, std::vector<STRU_DBCOL>& vecColAttri);
	void cleanup_conn(void);
	BOOL ClearResult(void);
	int Datalen(int colnum);
	BOOL UpdateDatalen(void);
	BOOL BindCol(int ncolnum, BYTE* pbuff, int bufflen);
	BOOL BcpData(const char* const pFile, const char* const pTable);
private:
	BOOL init_db(void);

	BOOL setup_conn(const char* const szUser,
					const char* const szPwd,
					const char* const szSvr,
					const char* const szDb = NULL,
					const int nPort = 2881);

	BOOL select_dbname(void);
	BOOL transform_fieldValue_into_buffer(BYTE* targetbuff, int ncolnum, char* valuebuff, int valuebufflen);

	BOOL transform_int_into_buffer(BYTE* targetbuff, char* valuebuff, int valuebufflen);
	BOOL transform_string_into_buffer(BYTE* targetbuff, char* valuebuff, int valuebufflen);
	BOOL transform_tinyint_into_buffer(BYTE* targetbuff, char* valuebuff, int valuebufflen);
	BOOL transform_short_into_buffer(BYTE* targetbuff, char* valuebuff, int valuebufflen);
	BOOL transform_float_into_buffer(BYTE* targetbuff, char* valuebuff, int valuebufflen);
	BOOL transform_double_into_buffer(BYTE* targetbuff, char* valuebuff, int valuebufflen);
	BOOL transform_int64_into_buffer(BYTE* targetbuff, char* valuebuff, int valuebufflen);

	typedef BOOL (CStdMySql_Connection::*TransformValue)(BYTE* targetbuff, char* valuebuff, int valuebufflen);
	std::map<enum_field_types, TransformValue> m_mapTransform;
	
	CStdMySql* m_pMySql;
	CStdBcp_MySql* m_pBcpMySql;
	CStdLog* m_pBcpLog;
};

#endif 

