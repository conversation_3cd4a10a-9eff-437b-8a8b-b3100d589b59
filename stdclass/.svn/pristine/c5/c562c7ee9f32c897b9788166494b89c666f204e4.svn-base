// StdBcp_Sybase.h: interface for the CStdBcp_Sybase class.
//
//////////////////////////////////////////////////////////////////////

#ifndef __STDBCP_SYBASE_H__
#define __STDBCP_SYBASE_H__

#include "./StdMonitor.h"
#include "./StdSybase_Blk.h"

class CStdBcp_Sybase
{
public:
	CStdBcp_Sybase(CStdLog* const pLog);

	~CStdBcp_Sybase(void);

	bool BcpData(DBPROCESS * _pdbproc, const char* const pFile, const char* const pTable);

	bool SimpleBcpData(DBPROCESS * _pdbproc, const char* const pFile, const char* const pTable, int Dir = DB_IN);
private:
	bool BlkData(CStdSybase_Blk & SybBlk, int &nIndex);
	void TrimTailEnter(char* cptr);
	void TrimSpace(char** sptr);
	char* m_pstrRec;
	char* m_Token;

	CStdLog* m_pLog;
};



#endif 

