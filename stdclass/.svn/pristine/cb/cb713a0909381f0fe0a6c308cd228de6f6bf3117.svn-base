// StdFreetds.h: interface for the CStdFreetds class.
//
//////////////////////////////////////////////////////////////////////

#ifndef __STDFREETDS_H__
#define __STDFREETDS_H__

#include "./StdDbHeader.h"
#include "./StdBcp_Sybase.h"
#include <sybfront.h> //freetds头文件
#include <sybdb.h> //freetds

class CStdFreetds
{
public:
	CStdFreetds(void);
	~CStdFreetds(void);
		
  LOGINREC *loginrec; //数据库连接对象
  DBPROCESS *dbprocess; // 句柄: 连接sqlserver服务器地址和端口号
	char cDbName[64];

	void Clear_Con(void);
	BOOL IsAvailable(void);
};

class CStdFreetds_Connection
{
public:
	CStdFreetds_Connection(void);
	CStdFreetds_Connection(CStdLog* _bcplog);

	~CStdFreetds_Connection(void);

	enum e_DbRetType
	{
		E_MORE_ROWS = -1,
		E_NO_MORE_ROWS = -2,
		E_BUF_FULL = -3,
		E_NO_MORE_RESULTS = 2,
		E_SUCCEED = 1,
		E_FAIL = 0,
	};
	
	BOOL connect(const char* const szUser,
					const char* const szPwd,
					const char* const szSvr,
					const char* const szDb = NULL);
	
	BOOL Execute(const char* cpSql);
	int DbResults(void);
	int DbNextRow(void);
	BOOL GetSelectColAttribute(std::vector<STRU_DBCOL>& vecColAttri);
	BOOL GetSelectColAttribute(const char* pSql, std::vector<STRU_DBCOL>& vecColAttri);
	void cleanup_conn(void);
	BOOL BindCol(int ncolnum, int bufflen, BYTE* pbuff);
	BOOL BindCol(int ncolnum, int bindtype, int bufflen, BYTE* pbuff);
	int Datalen(int colnum);
	BOOL BcpData(const char* const pFile, const char* const pTable);
private:
	BOOL init_db(void);

	BOOL setup_conn(const char* const szUser,
					const char* const szPwd,
					const char* const szSvr,
					const char* const szDb = NULL);

	BOOL select_dbname(void);
	
	CStdFreetds* m_pFreetds;
	CStdBcp_Sybase* m_pBcpSybase;
	CStdLog* m_pBcpLog;
};

#endif 

