#pragma once

#include "../StdLock.h"

#define BUF_SIZE (1024*1024)

class CStdCycBuffer 
{
public:
	CStdCycBuffer(void);
	CStdCycBuffer(const int nBufSize);
	~CStdCycBuffer(void);

	int Read(BYTE * const pBuf, const int nCount);
	int Write(const BYTE * const pBuf, const int nCount);
	int Peek(BYTE * const pBuf, const int nCount);
	int GetSize(void);
	void Reset(void);

	int ReadAny(BYTE * const pBuf, const int nCount);
	int GetFree(void);
private:
	void Initialize(void);

	BYTE * m_pBuffer;
	int m_nBufSize;
 	CStdLock m_Lock;
	BYTE * m_pRdPos;
	BYTE * m_pWtPos;
	// true:read false:write
	bool m_bFlag; 
	int m_nFreeSize;
};
