﻿#pragma once

#define SUPERLIBPATH(p,f) p##f  

#include "StdHeader.h"
#include "lzowrapper.h"

enum SeekPosition { beginL = SEEK_SET, currentL = SEEK_CUR, endL = SEEK_END };

class CStdLzoFile_r  
{
public:
	CStdLzoFile_r(void);
	~CStdLzoFile_r(void);

	bool Open(const char* const pstrFileName);

	long Seek(const long lOff, const unsigned int nFrom);
	long SeekToEnd(void);
	long SeekToBegin(void);

	int Read(void* const lpBuf, const unsigned int nCount);

	void Close(void);

 	bool IsOpen(void) const;
	bool IsEof(void) const;
 	std::string GetFilePath(void) const;
	long GetPosition(void) const;
	int GetLength(void ) const ;

protected:
	std::string m_strFileName;
	FILE* m_hFile;
	LZOFileReader* lzo_file_reader;
};

inline bool CStdLzoFile_r::IsOpen(void) const
{
	return ((m_hFile == NULL) ? false : true);
};

inline bool CStdLzoFile_r::IsEof(void) const
{
	return (fEof(*lzo_file_reader) == 1);
};

inline std::string CStdLzoFile_r::GetFilePath(void) const
{
	return m_strFileName;
};

inline long CStdLzoFile_r::SeekToEnd(void)
{
	assert(m_hFile != NULL);
	assert(lzo_file_reader != NULL);
	
	return fseek(*lzo_file_reader, 0, endL);
};

inline long CStdLzoFile_r::SeekToBegin(void)
{
	assert(m_hFile != NULL);
	assert(lzo_file_reader != NULL);
	
	return fseek(*lzo_file_reader, 0, beginL);
};

inline long CStdLzoFile_r::GetPosition(void) const
{
	assert(m_hFile != NULL);
	assert(lzo_file_reader != NULL);

	return ftell(*lzo_file_reader);
};

inline 	int CStdLzoFile_r::GetLength(void ) const 
{
	assert(m_hFile != NULL);
	assert(lzo_file_reader != NULL);

	return lzo_file_reader->Size();
}
///////////////////////////////////////////////////////

class CStdLzoFile_w  
{
public:
	CStdLzoFile_w(void);
	~CStdLzoFile_w(void);

	bool Open(const char* const pstrFileName, bool bZip = true, const char* const strOpenFlags = "wb");

	int WriteHuge(const void* const lpBuf, const unsigned int nLen, const unsigned int nCount = 1);
	int SuperWriteHuge(const void* const lpBuf, const unsigned int nLen);

	void Flush(void);

	void Close(void);
	int RenameSelf(const char* const lpszNewName);

	bool IsOpen(void) const;
	std::string GetFilePath(void) const;
	long GetPosition(void) const;
protected:
	std::string m_strFileName;
	FILE* m_hFile;
	LZOFileWriter* lzo_file_writer;
};

inline bool CStdLzoFile_w::IsOpen(void) const
{
	return ((m_hFile == NULL) ? false : true);
};

inline std::string CStdLzoFile_w::GetFilePath(void) const
{
	return m_strFileName;
};

inline long CStdLzoFile_w::GetPosition(void) const
{
	assert(m_hFile != NULL);
	assert(lzo_file_writer != NULL);

	return ftell(*lzo_file_writer);
};

inline int CStdLzoFile_w::RenameSelf(const char* const lpszNewName)
{
	try
	{
		std::string tm_filename = m_strFileName;
		this->Close();
		return rename(tm_filename.c_str(), lpszNewName);
	}
	catch (...)
	{
	}
	return -1;
}

inline void CStdLzoFile_w::Flush(void)
{
	if (m_hFile != NULL)
	{
		fflush(m_hFile);
	}
}


