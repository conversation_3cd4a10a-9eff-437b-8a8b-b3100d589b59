﻿#pragma once

#include "StdHeader.h"

/**
  * 功能描述：用于管理申请的内存块保存字节流，内存不足会自动适应。
*/

template <class T>
class CStdAdaptiveBuffer
{
public:
	CStdAdaptiveBuffer(size_t sz)
		:m_pBuffer(NULL),m_nCount(0),m_nBufferSize(0),m_nMaxBufferSize(0)
	{
		Initial(sz);
	};
	
	~CStdAdaptiveBuffer()
	{
		Release();
	};

	void Clear(void)
	{
		if (m_pBuffer != NULL)
		{
			memset(m_pBuffer, 0, m_nMaxBufferSize);
		}

		m_nCount = 0;
		m_nBufferSize = 0;
	};

	/**
	 * 功能描述：把信息写入Buffer，如果Buffer块不够长则会进行自扩展。
	 * @Param pBuffer：需要保存的流
	 * @Param nBuffSize：流字节数
	 * @Param nExpandSize：内存块扩大的字节数

	 * @Return：
	*/
	void WriteBuffer(T* pBuffer, int nBuffSize, const int nExpandSize = 512)
	{		
		if (((int)(m_nMaxBufferSize - m_nBufferSize)) < nBuffSize)
		{
			int nCount = m_nCount;

			const int nBufLen = m_nBufferSize + 1;
			T* pBufTemp = new T[nBufLen];
			memcpy(pBufTemp, m_pBuffer, m_nBufferSize);

			int nExpandSize_New = nExpandSize;

			if (nBuffSize > nExpandSize)
			{
				nExpandSize_New = (nExpandSize_New + nExpandSize);
			}
			
			Realloc(m_nMaxBufferSize + nBuffSize + nExpandSize_New);

			m_nCount = nCount;
			m_nBufferSize = (nBufLen - 1);
			memcpy(m_pBuffer, pBufTemp, m_nBufferSize);
			
			delete pBufTemp;
		}

		// 新增内容
		memcpy(m_pBuffer + m_nCount, pBuffer, nBuffSize);

		m_nBufferSize += nBuffSize;
		m_nCount = (int)((m_nBufferSize / sizeof(T)) + 0.999);
	};

	inline const T* operator[](size_t n) const 
	{
		assert (m_nCount > n);
		
		return &(m_pBuffer[n]);
	};
	
	inline T* operator[](size_t n)  
	{
		assert (m_nCount > n);
		
		return &(m_pBuffer[n]);
	};
	
	int Count() const 
	{
		return m_nCount;
	};

	int BufferSize() const
	{
		return m_nBufferSize;
	};

	CStdAdaptiveBuffer& operator= (const CStdAdaptiveBuffer& Array)
	{
		if (this != (&Array))
		{
			memcpy(m_pBuffer, Array.m_pBuffer, Array.m_nBufferSize);

			m_nCount = Array.m_nCount;
			m_nBufferSize = Array.m_nBufferSize;
			m_nMaxBufferSize = Array.m_nMaxBufferSize;
		}
		
		return (*this);
	};

protected:
	
private:
// 限制最大的内存块大小（128MB）
#define BUF_SIZE (128 * 1024 * 1024)

	//初始化
	void Initial(size_t sz)
	{
		if (m_pBuffer == NULL)
		{
			m_nBufferSize = 0;
			m_nMaxBufferSize = sz;
			m_pBuffer = new T[sz];
		}
		
		Clear();
	};

	//重新申请
	void Realloc(size_t sz)
	{
		assert (sz >= 0);
		assert (sz <= BUF_SIZE);

		if (sz > m_nMaxBufferSize)
		{
			if (m_pBuffer != NULL)
				delete m_pBuffer;
			
			m_pBuffer = new T[sz+1];
			m_nMaxBufferSize = sz+1;
			m_nBufferSize = 0;
			
			Clear();
		}	
	};
	//用完释放
	void Release(void)
	{
		if (m_pBuffer != NULL)
		{
			delete m_pBuffer;
			m_pBuffer = NULL;
		}

		m_nCount = 0;
		m_nBufferSize = 0;
		m_nMaxBufferSize = 0;
	};
	
	T* m_pBuffer; // 申请的内存块
	size_t m_nCount; // 保存的数据组大小
	size_t m_nBufferSize;   //字节数
	size_t m_nMaxBufferSize;//申请的最大空间
};

