// StdBuffer.h: interface for the CStdBuffer class.
//
//////////////////////////////////////////////////////////////////////

#ifndef __STDBUFFER_H__
#define __STDBUFFER_H__

#pragma once

#include "./StdHeader.h"
#include "./StdFile.h"
#include "./StdLock.h"
#include "./StdFileMapping.h"

#define DATAPACKHEAD 0X55AA

#define PKG_HEADER_LEN 4
#define NODE_BUF_SIZE 8192 * 8
#define STD_NODE_COUNT 256 //节点数量,2M
#define NODE_FIlE_SIZE (STD_NODE_COUNT*8192*200)// (STD_NODE_COUNT*NODE_BUF_SIZE*200)
// 发送包和应用数据包头格式：2 BYTE type | 2 BYTE len | 数据

///////////////////////////////////////////////////////////////////////////////

class CStdMultiFileMapping : public CStdFileMapping
{
public:
	CStdMultiFileMapping(const char* const pMapName, const int nSngLen, const int nCount);
	virtual ~CStdMultiFileMapping(void);

	void* Mapping(const std::string& subkey);
	void UnMapping(const std::string& subkey);
private:
	void Initialize(const int nSngLen, const int nCount);

	static std::map<std::string, void*>m_mapUsed;
	static std::list<void*>m_lstUnused;
	static CStdLock m_clsLock;
	static int m_nSngLen;
};

class CStdAutoFileMapping
{
public:
	CStdAutoFileMapping(const char* const pIpaddr = NULL, const int nPort = 0, const BYTE cDire = 0);
	~CStdAutoFileMapping(void);

	void ReadFileMap(void* const pMapData, const int nLen);
	void WriteFileMap(const void* const pMapData, const int nLen);
	void WriteFileMap(const long nPos, const std::string& strName);

private:
	static CStdMultiFileMapping m_clsMFileMapping;
	void* m_lpMapData;
	std::string m_strName;
};

///////////////////////////////////////////////////////////////////////////////////////
class CStdNode
{
public:
	CStdNode(const int nBufNodeSize)
	:m_pNodeBuf(NULL), m_nNodeLen(0), m_bFreeFlag(true), m_bAutoDel(false),m_nBufNodeSize(nBufNodeSize)
	{
	};
	virtual ~CStdNode(void)
	{
		m_pNodeBuf = NULL;
		m_bFreeFlag = true;
		m_nNodeLen = 0;
	};
	// 设置自动删除属性
	void SetAutoDel(const bool bDel)
	{
		m_bAutoDel = bDel;
	};
	// 节点自动删除
	bool IsAutoDel(void)
	{
		return m_bAutoDel;
	};
	// 设置有效数据长
	void SetNodeLen(const int nLen)
	{ 
		m_nNodeLen += nLen; 
	};
	// 取得有效数据长
	int GetNodeLen(void)
	{ 
		return m_nNodeLen; 
	};
	// 取得有效数据 + 包头长
	int GetTotalLen(void)
	{
		return (GetNodeLen() + PKG_HEADER_LEN);
	};
	// 取得节点指针
	BYTE* GetNodeBuf(void)
	{
		return m_pNodeBuf;
	}
	bool IsFreeNode(void)
	{ 
		return m_bFreeFlag; 
	};
	virtual void SetFree(void)
	{ 
		m_bFreeFlag = true;
		m_nNodeLen = 0; 
	};
	virtual void SetBusy(void)
	{ 
		m_bFreeFlag = false; 
	};
	// 读取节点数据（指针和长度）
	virtual int ReadData(BYTE** const pBuf, int& nCount)
	{
		if (pBuf == NULL)
		{
			return 0;
		}

		nCount = this->GetTotalLen();
		*pBuf = this->GetNodeBuf();
		m_nNodeLen = 0;
		return nCount;
	};
	virtual bool WriteData(const BYTE* const pBuf, const int nSize)
	{
		if (pBuf == NULL)
		{
			return false;
		}

		if ((m_nBufNodeSize - PKG_HEADER_LEN - m_nNodeLen) >= nSize)
		{
			memcpy(m_pNodeBuf + PKG_HEADER_LEN + m_nNodeLen, pBuf, nSize);
			m_nNodeLen += nSize;
			m_pNodeBuf[2] = (BYTE)(m_nNodeLen / 0x100);
			m_pNodeBuf[3] = (BYTE)(m_nNodeLen % 0x100);

			return true;
		}
		return false;
	};

	BYTE* m_pNodeBuf;

private:
	// 有效数据长，不包括包头
	int m_nNodeLen;
	bool m_bFreeFlag; // 忙闲标志
	bool m_bAutoDel;
protected:
	int m_nBufNodeSize;
};
////////////////////////////////////////////////
class CBufNode : public CStdNode
{
public:
	CBufNode(int nBufNodeSize); 

	virtual ~CBufNode(void)
	{
		if (NULL != m_pNodeBuf)
		{
			delete []m_pNodeBuf;
			m_pNodeBuf = NULL;
		}
	};
};
/////////////////////////////////////////////////
class CNodeMng;
class CFileNode : public CStdNode 
{
public:
	CFileNode(CBufNode* const prBuf, CBufNode* const pwBuf, const std::string& strName, CNodeMng* const pMng,const int nBufNodeSize);
	virtual ~CFileNode(void);

	virtual void SetFree(void) { Release(); };
	virtual void SetBusy(void);
	virtual bool WriteData(const BYTE* const pBuf, const int nSize);
	virtual int ReadData(BYTE** const pBuf, int& nCount);
	void SetStartPos(const long nPos)
	{
		m_nStartPos = nPos;
	}
private:
	void Initialize(void);
	void Release(void);

	CStdFile m_wFile;
	CStdFile m_rFile;
	std::string m_strFile;
	CBufNode* m_rBuf;
	CBufNode* m_wBuf;
	long m_nStartPos;
	CNodeMng* m_pMng;
};

/////////////////////////////////////////////////////
class CNodeMng
{
	typedef std::list<CStdNode*> Node_Buffer;
public:
	enum NodeType{BuffMode = 0, FileMode = 1, BothMode = 2, NullMode = 3};

	CNodeMng(const int nMaxSize = STD_NODE_COUNT, const BYTE eNodeType = CNodeMng::BothMode, 
		const char* const pIpaddr = NULL, const int nPort = 0, const BYTE cDire = 0, 
		const bool bReSendFlag = true,const int nBufNodeSize = 8192*8);
	~CNodeMng(void);

	CStdNode* GetFreeNode(void);
	CStdNode* GetBusyNode(void);
	CStdNode* GetFileNode(void);
	CStdNode* GetBufNode(void);
	void SetFreeNode(CStdNode* const pNode) const
	{
		if (NULL != pNode) 
		{
			pNode->SetFree();
			if (pNode->IsAutoDel())
			{
				delete pNode;
			}
		}
	};
	void SetBusyNode(CStdNode* const pNode);
	void DumpBusyNode(CStdNode* const pNode);
	void NodeTypeSwitch(const BYTE type)
	{
		m_bCurrType = type;
	};
	void WriteMapping(const long lPos, const std::string& strFile);

	int GetBusySize(void)
	{
		return (int)(m_BusyBuf.size());
	};
private:
	void Initialize(const int nMaxSize, const char* const pIpaddr, 
		const int nPort, const bool bReSendFlag,const int nBufNodeSize);
	void Release(void);
	void GetHisFile(const bool bReSendFlag);
	bool CreateDir( const char * const pstrPath) const;
	void GetCurrentFile(char* const pName);

	Node_Buffer m_FreeBuf;
	Node_Buffer m_BusyBuf;
	CStdLock m_BusyLock;
	// 当前创建节点类型标志
	BYTE m_bCurrType;
	// 允许创建类型
	BYTE m_eNodeType;
	// 文件读写用
	CBufNode* m_rBuf;
	CBufNode* m_wBuf;
	std::string m_strPath;
	int m_nIndex;

	CStdAutoFileMapping m_clsFileMap;

	int m_nBufNodeSize;
};
////////////////////////////////////////////////
class CStdBuffer  
{
public:

	CStdBuffer(const int nBufSize = STD_NODE_COUNT, 
			   const BYTE cBuffType = CNodeMng::BothMode,
			   const char* const pIpaddr = NULL,
			   const int nPort = 0,
			   const BYTE cDire = 0,
			   const bool bReSendFlag = true,const int nBufNodeSize=8192*8);
	~CStdBuffer(void);

	int Read(BYTE** const pBuf, int& nCount);
	int Write(const BYTE* const pBuf, const int nCount);
	
	int GetBusySize(void)
	{
		return m_NodeMng.GetBusySize();
	};
private:
	void Dump(void);

	CNodeMng m_NodeMng;
	CStdNode* m_pFreeNode;
	CStdNode* m_pBusyNode;
	CStdLock m_wLock;
	CStdLock m_rLock;

	int m_nBufNodeSize;
};

class CDataActor
{
public:
	CDataActor(){
		SetOverFlag(FALSE);
	};
	virtual ~CDataActor(void){};
	virtual BOOL DealData(const BYTE* const pData, const int nCount) = 0;
	virtual void ClientClose(){};
	void SetOverFlag(BOOL flag){
		m_bIsOver_ = flag;
	};
	BOOL GetOverFlag(){
		return m_bIsOver_;
	}
private:
	//通知DealData是否不要处理了
	BOOL m_bIsOver_;
};

#endif 

