// StdCommond.h: interface for the CStdCommond class.
//
//////////////////////////////////////////////////////////////////////

#ifndef __STDCOMMOND_H__
#define __STDCOMMOND_H__

#include "./StdHeader.h"

class CStdCommond  
{
public:
	static bool CreateDirec(const char * const pszPath);
	static void CreateDirecA(const char * const pPath);
	static void DeleteDirec(const char* const pszPath, bool bDelFile = true);
	static void GetProcessPath(char* const pszPath);
	/**
	* 功能描述：遍历指定路径的文件
	* @param strPath: 文件绝对路径 
	* @param vecFilePath: 保存找到的文件名信息
	* 
	* @Return
	*/
	static void ListFile(std::string strPath, std::vector<std::string>& vecFilePath);
	/**
	* 功能描述：遍历指定路径的文件
	* @param strPath: 文件绝对路径 
	* @param mapFilePath: 保存找到的文件名信息(key：文件名，value：文件路径+文件名)
	* 
	* @Return
	*/
	static void ListFile(std::string strPath, std::map<std::string, std::string>& mapFilePath);

	static void Sleep(const int nMilsec);

	static char *itoa(const int value, char * const string, const int radix);
	static std::string itostr(const int value, const int radix = 10);
	static std::string int64tostr(const INT64 value, const int radix = 10);
	static int atoi(const std::string sourceStr, const int radix = 10);
	static short strtosmallint(const std::string value);
	static BYTE strtotinyint(const std::string value);
	static __int64 _atoi64(const char *p64);
	static BOOL isValidHexNum(char ch1);
	static char toHex(char ch1);
	static int atox(char *const toHexChar, std::string& fromString);
	static int xtoa( const char *const fromHex, std::string& toString, const int nFromLen );

	static std::string str_trim(std::string sStr, char trim_char = ' ');
	static std::vector<std::string> str_slipt(const std::string stringTemp, const std::string markFlag);
	static std::string StringToUpOrLow(const std::string sourceStr, const int type);

	static int GetErrno(void);
	
	static BOOL vec_pushfront(void* p_item, std::vector<void*> vec_pItems);

  static std::string IPIntToStr(int ipInt);
	static int StrToIPInt(std::string ipStr);
	static int replace(std::string& src_str,   const std::string& old_str,   const std::string&   new_str); 
	static double GetDistance(int point1_X, int point1_Y, int point2_X, int point2_Y);

	static void OutputDebugPrintf(const char *format, ...);
	
};

#endif 
