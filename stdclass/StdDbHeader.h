#ifndef __STDDBHEADER_H__
#define __STDDBHEADER_H__

#include "StdHeader.h"

struct STRU_DBConnInfo
{
	STRU_DBConnInfo(){
		memset(this,0,sizeof(STRU_DBConnInfo));
	}
	char pchD<PERSON>erver[50];
	char pchD<PERSON><PERSON><PERSON>[50];
	char pchUser<PERSON>ame[50];
	char pchUserPassword[50];
	int nPort;
	int DBType;
	int DisplaySql;
};


struct STRU_DBCOL
{
	std::string columnName;
	int columnType;
	int colid;
};

class CSqlDBType
{
public:	
	enum E_SqlDbType
	{
		SQL_IMAGE = 34, 
		SQL_TEXT = 35,
		SQL_DATE = 40,
		SQL_TIME = 41,
		SQL_TINYINT = 48,
		SQL_SMALLINT = 52,
		SQL_INT = 56,
		SQL_SMALLDATETIME = 58,
		SQL_REAL = 59,
		SQL_MONEY = 60,
		SQL_DATETIME = 61,
		SQL_FLOAT = 62,
		SQL_SQL_VARIANT = 98,
		SQL_NTEXT = 99,
		SQL_BIT = 104,
		SQL_DECIMAL = 106,
		SQL_NUMERIC = 108,
		SQL_SMALLMONEY = 122,
		SQL_BIGINT = 127,
		SQL_VARBINARY = 165,
		SQL_VARCHAR = 167,
		SQL_BINARY = 173,
		SQL_CHAR = 175,
		SQL_TIMESTAMP = 189,
		SQL_NVARCHAR =  231,
		SQL_NCHAR = 239,
		SQL_GEOGRAPHY = 240,
		SQL_XML = 241
	};
};

class CSybDBType
{
public:
	enum SybType
	{
        E_SYB_SYBCHAR = 47,           /* 0x2F */
        E_SYB_SYBVARCHAR = 39,        /* 0x27 */
        E_SYB_SYBINTN = 38,           /* 0x26 */
        E_SYB_SYBINT1 = 48,           /* 0x30 */
        E_SYB_SYBINT2 = 52,           /* 0x34 */
        E_SYB_SYBINT4 = 56,           /* 0x38 */
        E_SYB_SYBINT8 = 127,          /* 0x7F */
        E_SYB_SYBFLT8 = 62,           /* 0x3E */
        E_SYB_SYBDATETIME = 61,       /* 0x3D */
        E_SYB_SYBBIT = 50,            /* 0x32 */
        E_SYB_SYBBITN = 104,          /* 0x68 */
        E_SYB_SYBTEXT = 35,           /* 0x23 */
        E_SYB_SYBNTEXT = 99,          /* 0x63 */
        E_SYB_SYBIMAGE = 34,          /* 0x22 */
        E_SYB_SYBMONEY4 = 122,        /* 0x7A */
        E_SYB_SYBMONEY = 60,          /* 0x3C */
        E_SYB_SYBDATETIME4 = 58,      /* 0x3A */
        E_SYB_SYBREAL = 59,           /* 0x3B */
        E_SYB_SYBBINARY = 45,         /* 0x2D */
        E_SYB_SYBVOID = 31,           /* 0x1F */
        E_SYB_SYBVARBINARY = 37,      /* 0x25 */
        E_SYB_SYBNUMERIC = 108,       /* 0x6C */
        E_SYB_SYBDECIMAL = 106,       /* 0x6A */
        E_SYB_SYBFLTN = 109,          /* 0x6D */
        E_SYB_SYBMONEYN = 110,        /* 0x6E */
        E_SYB_SYBDATETIMN = 111,      /* 0x6F */
        E_SYB_SYBNVARCHAR = 103,      /* 0x67 */
        E_SYB_SYBDATE = 49,           /* 0x31 */
        E_SYB_SYBTIME = 51,           /* 0x33 */
        E_SYB_SYBBIGDATETIME = 187,   /* 0xBB */
        E_SYB_SYBBIGTIME = 188,       /* 0xBC */
        E_SYB_SYBMSDATE = 40,         /* 0x28 */
        E_SYB_SYBMSTIME = 41,         /* 0x29 */
        E_SYB_SYBMSDATETIME2 = 42,    /* 0x2A */
        E_SYB_SYBMSDATETIMEOFFSET = 43, /* 0x2B */
        E_SYB_SYBUNIQUEIDENTIFIER = 36,
        E_SYB_SYBSQLVARIANT = 98,
        E_SYB_SYBGEOGRAPHY = 240,
        E_SYB_SYBXML = 241,
	};
};

class CMySqlType
{
public:
	enum MySqlType
	{
		E_MYSQL_DECIMAL,
		E_MYSQL_TINY,
		E_MYSQL_SHORT,
		E_MYSQL_LONG,
		E_MYSQL_FLOAT,
		E_MYSQL_DOUBLE,
		E_MYSQL_NULL,
		E_MYSQL_TIMESTAMP,
		E_MYSQL_LONGLONG,
		E_MYSQL_INT24,
		E_MYSQL_DATE,
		E_MYSQL_TIME,
		E_MYSQL_DATETIME,
		E_MYSQL_YEAR,
		E_MYSQL_NEWDATE, /**< Internal to MySQL. Not used in protocol */
		E_MYSQL_VARCHAR,
		E_MYSQL_BIT,
		E_MYSQL_TIMESTAMP2,
		E_MYSQL_DATETIME2,   /**< Internal to MySQL. Not used in protocol */
		E_MYSQL_TIME2,       /**< Internal to MySQL. Not used in protocol */
		E_MYSQL_TYPED_ARRAY, /**< Used for replication only */
		E_MYSQL_INVALID = 243,
		E_MYSQL_BOOL = 244, /**< Currently just a placeholder */
		E_MYSQL_JSON = 245,
		E_MYSQL_NEWDECIMAL = 246,
		E_MYSQL_ENUM = 247,
		E_MYSQL_SET = 248,
		E_MYSQL_TINY_BLOB = 249,
		E_MYSQL_MEDIUM_BLOB = 250,
		E_MYSQL_LONG_BLOB = 251,
		E_MYSQL_BLOB = 252,
		E_MYSQL_VAR_STRING = 253,
		E_MYSQL_STRING = 254,
		E_MYSQL_GEOMETRY = 255 
	};
};
#endif

