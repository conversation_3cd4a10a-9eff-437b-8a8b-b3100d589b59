﻿#pragma once

#include "StdAdaptiveBuffer.h"

#define LINE_RECORD_LEN 8192

/**
  * 功能描述：用于快速解析文本内容
  * 思想：记录匹配分隔字符串的起始位置
*/

class CStdFastSplitStr
{
public:
	// 使用此构造函数，切割字符串，需要自行调用接口&释放内存。
	CStdFastSplitStr(void)
		:m_pSplitStr(NULL),
		m_adBuffSplitPos(LINE_RECORD_LEN)
	{
		m_adBuffSplitPos.Clear();
	}

	// 使用此构造函数用完就释放内存
	CStdFastSplitStr(char* pStr, const char* pMatchStr, bool bUseOrgBuf = false)
		:m_pSplitStr(NULL),
		m_adBuffSplitPos(LINE_RECORD_LEN)
	{
		ParseContent(pStr, pMatchStr, bUseOrgBuf);
	}
	
	~CStdFastSplitStr()
	{
		m_strSplitStr = "";
		m_pSplitStr = NULL;
	}
	
	/**
	 * 功能描述：字符串根据匹配的字符串进行解析，保存字段的起始位置
	 * @Param pStr：需要解析的字符串
	 * @Param pMatchStr：匹配的字符串
	 * @Param bUseOrgBuf：是否使用原始的Buf指针（会修改字符串，匹配的字符修改为0）

	 * @Return：
	*/
	void ParseContent(char* pStr, const char* pMatchStr, bool bUseOrgBuf = false)
	{
		if (pStr == NULL 
			|| strlen(pStr) == 0)
		{
			return;
		}

		m_adBuffSplitPos.Clear();

		if (!bUseOrgBuf)
		{
			m_strSplitStr.assign(pStr);
			m_pSplitStr = (char*)m_strSplitStr.c_str();
		}
		else
		{
			m_pSplitStr = pStr;
		}
		
		parse(m_pSplitStr, pMatchStr);
	}

	inline const char* operator[](int n) const 
	{
		assert (m_adBuffSplitPos.Count() > n);
		
		return &m_pSplitStr[*m_adBuffSplitPos[n]];
	}
	
	inline char* operator[](int n)  
	{
		assert (m_adBuffSplitPos.Count() > n);
		
		return &m_pSplitStr[*m_adBuffSplitPos[n]];
	}
	
	int size() const 
	{
		return m_adBuffSplitPos.Count();
	}
	
private:
	CStdFastSplitStr* operator=(const CStdFastSplitStr&r)
	{
		assert(0);
	}
	
	CStdFastSplitStr(const CStdFastSplitStr &r)
		:m_pSplitStr(NULL),
		m_adBuffSplitPos(LINE_RECORD_LEN)
	{
		assert(0);
	}
		
	void parse(char* pStr, const char* pMatchStr) // 分解todo使用strtok_r()，多线程支持
	{
		int nPos = 0;
		m_adBuffSplitPos.WriteBuffer(&nPos, sizeof(nPos));
		
		char* pFinder = pStr;
		
		while ((pFinder = strstr(pFinder, pMatchStr)) != NULL)
		{
			nPos = ((int)(pFinder - pStr + strlen(pMatchStr)));
			m_adBuffSplitPos.WriteBuffer(&nPos, sizeof(nPos));
			
			*pFinder++ = 0; // 将分割点设为NULL，以结束上一个字符串
		}
	}
	
	std::string m_strSplitStr; // 被分割字符串
	char* m_pSplitStr; // 被分割字符串(地址)

	CStdAdaptiveBuffer<int> m_adBuffSplitPos; // 保存字段的起始位置
};


