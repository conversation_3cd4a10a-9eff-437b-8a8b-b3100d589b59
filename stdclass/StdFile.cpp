
#include "StdFile.h"

unsigned int CStdFile::m_nIsOpenCount = 0;

CStdFile::CStdFile(void)
:m_FileType(-1),
m_strFileName(""), 
m_hFile(NULL), 
m_nFileSize(0),
m_nLastErrorNo(0)
{

}

CStdFile::~CStdFile(void)
{
	if (m_hFile != NULL)
	{
		try
		{
			Close();
		}
		catch (...)
		{
		}
		m_hFile = NULL;
	}

	try
	{
		m_strFileName.assign("");
		m_FileType = -1;
	}
	catch (...)
	{
	}
}

bool CStdFile::Open(const char* const pstrFileName, const char* const strOpenFlags)
{
	m_nFileSize = 0;
	m_nLastErrorNo = 0;

	if (strcmp(pstrFileName, "") == 0)
	{
		return false;
	}

	m_strFileName.assign(pstrFileName);
	
	m_hFile = fopen(pstrFileName, strOpenFlags);

	if (NULL == m_hFile)
	{
		m_nLastErrorNo = -1;

		return false;
	}

	m_nIsOpenCount++;
	m_nFileSize = GetLength();
	
	return true;
}

long CStdFile::Seek(const long lOff, const unsigned int nFrom)
{
	assert(m_hFile != NULL);
	assert(nFrom == begin || nFrom == end || nFrom == current);

	if (m_hFile == NULL)
	{
		return -2;
	}

	if(fseek( m_hFile, lOff, nFrom) == 0)
	{
		return ftell(m_hFile);
	}
	else
	{
		return -1;
	}
}

long CStdFile::GetLength(void) const
{
	assert(m_hFile != NULL);

	if (m_nFileSize > 0)
	{
		return m_nFileSize;
	}

	long lCur = 0, lLen = 0;
	CStdFile* const pFile = (CStdFile*)this;

	lCur = pFile->Seek(0L, current);
	lLen = pFile->SeekToEnd();
	pFile->Seek(lCur, begin);

	return lLen;
}

size_t CStdFile::Read(void* const lpBuf, const unsigned int nCount)
{
	assert(m_hFile != NULL);

	if ((nCount == 0) || (m_hFile == NULL) || (lpBuf == NULL))
	{
		return 0; // nCount 最大 8192
	}
	else
	{
		return fread(lpBuf,  1,(size_t)nCount, m_hFile);
	}
}

size_t CStdFile::SuperRead(void* const lpBuf, const unsigned int nLen)
{
	assert(m_hFile != NULL);
	
	if ((nLen == 0) || (m_hFile == NULL) || (lpBuf == NULL))
	{
		return 0; // nCount 最大 8192
	}

	if (nLen <= 8192)
	{
		return fread(lpBuf,  1, (size_t)nLen, m_hFile);
	}
	else
	{
		size_t nCount = 0;
		size_t nTotalCount = 0;
		size_t nNum = nLen / 8192; 
		BYTE* pData = (BYTE*)lpBuf;

		size_t i = 0;
		for (i = 0; i < nNum; i++)
		{
			nCount = fread(pData + i * 8192, 1, 8192, m_hFile);

			nTotalCount += nCount; 

			if (nCount != 8192)
			{
				break;
			}
		}

		if (i == nNum)
		{
			nCount = nLen % 8192;

			nTotalCount += fread(pData + i * 8192, 1, nCount, m_hFile);
		}

		return nTotalCount;
	}
}

size_t CStdFile::Write(const void* const lpBuf, const unsigned int nCount)
{
	assert(m_hFile != NULL);

	if ((nCount == 0) || (m_hFile == NULL) || (lpBuf == NULL))
	{
		return 0; // nCount 最大 8192
	}
	else
	{
		size_t nSize = fwrite(lpBuf, 1, (size_t)nCount, m_hFile);
		if (fflush(m_hFile) == 0)
		{
			return nSize;
		}
		return 0;
	}
}

size_t CStdFile::SuperWrite(const void* const lpBuf, const unsigned int nLen)
{
	assert(m_hFile != NULL);
	
	if ((nLen == 0) || (m_hFile == NULL) || (lpBuf == NULL))
	{
		return 0; // nCount 最大 8192
	}

	if (nLen <= 8192)
	{
		size_t nSize = fwrite(lpBuf, 1, (size_t)nLen, m_hFile);
		if (fflush(m_hFile) == 0)
		{
			return nSize;
		}
		return 0;
	}
	else
	{
		size_t nCount = 0;
		size_t nTotalCount = 0;
		size_t nNum = nLen / 8192;

		BYTE* pData = (BYTE*)lpBuf;

		size_t i = 0;
		for (i = 0; i < nNum; i++)
		{
			nCount = fwrite(pData + i * 8192, 1, 8192, m_hFile);
			
			nTotalCount += nCount;
			
			if (nCount != 8192)
			{
				break;
			}
		}
		
		if (i == nNum)
		{
			nCount = nLen % 8192;
			
			nTotalCount += fwrite(pData + i * 8192, 1, nCount, m_hFile);
		}

		if (fflush(m_hFile) == 0)
		{
			return nTotalCount;
		}
	
		return 0;
	}
}

bool CStdFile::GetStatus(const char* const lpszFileName, unsigned int &nLen)
{
	struct stat filestat;
	if (stat(lpszFileName, &filestat) == 0)// 兼容问题用stat,否则unix用lstat
	{
		/**
		如果传入的是文件路径的话，返回false, 
		因为该函数的功能为用来检查文件是否存在，
		并不是文件夹(20190903 fhx)
		*/
		if (filestat.st_mode & S_IFDIR)
		{
			return false;
		}

		nLen = (unsigned int)filestat.st_size;

		return true;
	}

	return false;
}

time_t CStdFile::GetFileCreateTime(const char* const lpszFileName)
{
	struct stat filestat;
	if (stat(lpszFileName, &filestat) == 0)// 兼容问题用stat,否则unix用lstat
	{
		return filestat.st_ctime;
	}
	return 0;
}

bool CStdFile::GetFilestat(const char* const lpszFileName, struct stat& filestat)
{
	if (stat(lpszFileName, &filestat) == 0)// 兼容问题用stat,否则unix用lstat
	{
		return true;
	}

	return false;
}

size_t CStdFile::WriteHuge(const void* const lpBuf, const unsigned int nLen, const unsigned int nCount)
{
	assert(m_hFile != NULL);

	if ((nCount == 0) || (m_hFile == NULL) || (lpBuf == NULL))
	{
		return 0; // nLen 最大 8192
	}
	else
	{
		if (fwrite(lpBuf, (size_t)nLen, (size_t)nCount, m_hFile) == nCount)
		{
			return nLen;
		}

		return 0;
	}
}

size_t CStdFile::SuperWriteHuge(const void* const lpBuf, const unsigned int nLen)
{
	assert(m_hFile != NULL);
	
	if ((nLen == 0) || (m_hFile == NULL) || (lpBuf == NULL))
	{
		return 0; // nLen 最大 8192
	}

	if (nLen <= 8192)
	{
		if (fwrite(lpBuf, (size_t)nLen, 1, m_hFile) == 1)
		{
			return nLen;
		}
		
		return 0;
	}
	else
	{
		size_t nCount = 0;
		size_t nTotalCount = 0;
		size_t nNum = nLen / 8192;
		BYTE* pData = (BYTE*)lpBuf;

		size_t i = 0;
		for (i = 0; i < nNum; i++)
		{
			nCount = fwrite(pData + i * 8192, 1, 8192, m_hFile);

			nTotalCount += nCount;

			if (nCount != 8192)
			{
				break;
			}
		}

		if (i == nNum)
		{
			nCount = nLen % 8192;

			nTotalCount += fwrite(pData + i * 8192, 1, nCount, m_hFile);
		}
		
		return nTotalCount;
	}
}

void CStdFile::Flush(void)
{
	if (m_hFile != NULL)
	{
		fflush(m_hFile);
	}
}


