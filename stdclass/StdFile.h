#ifndef __STDFILE_H__
#define __STDFILE_H__

#include "StdHeader.h"

class CStdFile
{
public:
	CStdFile(void);
	~CStdFile(void);

	enum SeekPosition { begin = SEEK_SET, current = SEEK_CUR, end = SEEK_END };
     
	bool Open(const char* const pstrFileName, const char* const strOpenFlags = "a+b");
	long Seek(const long lOff, const unsigned int nFrom);
	long SeekToEnd(void);
	long SeekToBegin(void);
	bool IsEof(void) const;
	bool IsOpen(void) const;
	bool IsError(void) const;

	size_t Read(void* const lpBuf, const unsigned int nCount);
	size_t Write(const void* const lpBuf, const unsigned int nCount);
	size_t WriteHuge(const void* const lpBuf, const unsigned int nLen, const unsigned int nCount = 1);

	size_t SuperRead(void* const lpBuf, const unsigned int nLen);
	size_t SuperWrite(const void* const lpBuf, const unsigned int nLen);
	size_t SuperWriteHuge(const void* const lpBuf, const unsigned int nLen);

	char* GetStr(char* const pstring, const int n);
	int PutStr(const char *const pstring);
	void Flush(void);

	int Close(void);

	long GetPosition(void) const;
	long GetLength(void) const;
	std::string GetFilePath(void) const;
    FILE* GetFile(void);
	int RenameSelf(const char* const lpszNewName);
	void RemoveSelf();

	int GetLastError() {
		return m_nLastErrorNo;
	};

	static int Rename(const char* const lpszOldName, const char* const lpszNewName);
	static void Remove(const char* const lpszFileName);
	static bool GetStatus(const char* const lpszFileName, unsigned int &nLen);
	static bool IsExist(const char* const lpszFileName);
	static time_t GetFileCreateTime(const char* const lpszFileName);
	static bool GetFilestat(const char* const lpszFileName, struct stat& filestat);
	static unsigned int GetIsOpenFileCount(void) {
		return CStdFile::m_nIsOpenCount;
	};

protected:
	//文本文件类型枚举值
	typedef enum TextCodeType
	{
		UTF8 = 0,
		UNICODE = 1,
		UNICODEBIGENDIAN = 2,
		ANSI = 3,
		FILEERROR = 4  
	}TextCode;
	int m_FileType;

	std::string m_strFileName;
	FILE* m_hFile;
	long m_nFileSize;
	int m_nLastErrorNo;

	static unsigned int m_nIsOpenCount;
};

inline long CStdFile::SeekToEnd(void)
{
	assert(m_hFile != NULL);

	return Seek(0, end);
}

inline long CStdFile::SeekToBegin(void)
{
	assert(m_hFile != NULL);

	return Seek(0, begin);
}

inline bool CStdFile::IsEof(void) const
{
	assert(m_hFile != NULL);

	return !(feof(m_hFile) == 0);
}

inline long CStdFile::GetPosition(void) const
{
	assert(m_hFile != NULL);

	return ftell(m_hFile);
}

inline int CStdFile::Close(void)
{
	int nRet = 0;

	if (NULL != m_hFile)
	{
		nRet = fclose(m_hFile);
		m_hFile = NULL;

		if (nRet == 0)
		{
			m_nIsOpenCount--;
		}
	}

	m_strFileName.assign("");
	m_FileType = -1;

	return nRet;
}

inline std::string CStdFile::GetFilePath(void) const
{
	return m_strFileName;
}

inline int CStdFile::Rename(const char* const lpszOldName, const char* const  lpszNewName)
{
	try
	{
		return rename(lpszOldName, lpszNewName);
	}
	catch (...)
	{
	}
	return -1;
}

inline int CStdFile::RenameSelf(const char* const lpszNewName)
{
	try
	{
		std::string tm_filename = m_strFileName;
		this->Close();
		return rename(tm_filename.c_str(), lpszNewName);
	}
	catch (...)
	{
	}
	return -1;
}

inline void CStdFile::Remove(const char* const lpszFileName)
{
	try
	{
		remove(lpszFileName);
	}
	catch (...)
	{

	}
}

inline void CStdFile::RemoveSelf()
{
	try
	{
		std::string tm_strname = m_strFileName;
		this->Close();
		remove(tm_strname.c_str());
	}
	catch (...)
	{

	}
}

inline bool CStdFile::IsOpen(void) const
{
	return ((m_hFile == NULL) ? false : true);
}

inline bool CStdFile::IsError(void) const
{
	return (ferror(m_hFile) == 0 ? false : true);
}

inline FILE* CStdFile::GetFile(void)
{
	return m_hFile;
}

inline int CStdFile::PutStr(const char *const pstring)
{
	assert(m_hFile != NULL);
    if (m_hFile == NULL)
    {
        return 0;
    }
	int nSize = fputs(pstring, m_hFile);
	if (fflush(m_hFile) == 0)
	{
		return nSize;
	}
	return 0;
}

inline char* CStdFile::GetStr(char* const pstring, const int n)
{
	assert(m_hFile != NULL);

	if ((pstring == NULL) || (m_hFile == NULL))
	{
		return NULL;
	}

	return fgets(pstring, n, m_hFile);
}

inline bool CStdFile::IsExist(const char* const lpszFileName)
{
	bool bRet = true;

	unsigned int nLen = 0;

	if (!(bRet = CStdFile::GetStatus(lpszFileName, nLen)))
	{
		bRet = (access(lpszFileName, 0) != -1);
	}

	return bRet;
}

#endif
