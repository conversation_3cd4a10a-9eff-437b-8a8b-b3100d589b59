
#include "StdFileFind.h"

// scandir版本

CStdFileFind::CStdFileFind(void)
:m_strPath(""), m_strName(""), m_pfilefinder(NULL), m_pFind(NULL), m_pNext(NULL)
{

}

CStdFileFind::~CStdFileFind(void)
{
	try	{
		Close();
	}
	catch (...)	{
		
	}
}

bool CStdFileFind::IsDots(void) const
{
	if ((m_strName.compare(".") == 0) 
		|| (m_strName.compare("..") == 0))
	{
		return true;
	}

	return false;
}

bool CStdFileFind::IsDirectory(void) const
{
	std::string strFullPath(m_strPath);
	strFullPath.append(m_strName);

	struct stat filetype;
	if (lstat(strFullPath.c_str(), &filetype) == 0)
	{
		if (S_ISDIR(filetype.st_mode))
		{
			return true;
		}
	}

	return false;
}

void CStdFileFind::Close(void)
{
	if (NULL != m_pfilefinder)
	{
		closedir(m_pfilefinder);
		m_pfilefinder = NULL;
	}
	m_strPath.assign("");
	m_strName.assign("");
}

//只支持路径
bool CStdFileFind::FindFile(const char* pstrName /* 只支持路径,最后要加'/'或'\\',返回全部文件 */) 
{
	// 保证先关闭以前打开的目录
	Close();

	if (NULL == pstrName)
	{
		pstrName = ".";
	}

	m_strPath.assign(pstrName);
	if ((m_strPath[m_strPath.length() - 1] != '/') 
		&& (m_strPath[m_strPath.length() - 1] != '\\'))
	{
		m_strPath.append("/");
	}

	m_pfilefinder = opendir(pstrName);
	if (!m_pfilefinder)
	{
		return false;
	}
	else
	{
		m_pNext = readdir(m_pfilefinder);
		if (m_pNext == NULL)
		{
			return false;
		}

		return true;
	}
}

void CStdFileFind::FindAllFile(std::vector<std::string>& vec_Str, const char* pstrName, const char* pstrFileName)
{
   CStdFileFind filefinder;

   bool bFinded = filefinder.FindFile(pstrName);

   while(bFinded)
   {
	   bFinded = filefinder.FindNextFile();

	   if (filefinder.IsDots())
	   {
		   continue;
	   }
	   else if (filefinder.IsDirectory())
	   {
		   FindAllFile(vec_Str, filefinder.GetFilePath().c_str(), pstrFileName);
	   }
	   else
	   {
		   if (pstrFileName != NULL)
		   {
			   std::string toFindFileName = std::string(pstrFileName);
			   bool preAny = pstrFileName[0] == '*';
			   toFindFileName = preAny ? toFindFileName.substr(1, toFindFileName.size()-1):toFindFileName;
			   bool backAny = pstrFileName[strlen(pstrFileName) - 1] == '*';
			   toFindFileName = backAny ? toFindFileName.substr(0, toFindFileName.size()-1):toFindFileName;
			   int dotPos = int(filefinder.GetFileName().find_last_of('.'));

			   std::string fileNameWithoutExpend =  dotPos >= 0 ? filefinder.GetFileName().substr(0, dotPos):filefinder.GetFileName();
			   if (preAny && !backAny)
			   {
				   if (fileNameWithoutExpend.find(toFindFileName, 0) >= 0
					   && fileNameWithoutExpend.length() == toFindFileName.length())
				   {
					   vec_Str.push_back(filefinder.GetFilePath());
				   }
			   }
			   else if (preAny && backAny)
			   {
				   if (fileNameWithoutExpend.find(toFindFileName, 0) >= 0)
				   {
					   vec_Str.push_back(filefinder.GetFilePath());
				   }
			   }
			   else if (!preAny && !backAny)
			   {
				   if (fileNameWithoutExpend == toFindFileName)
				   {
					   vec_Str.push_back(filefinder.GetFilePath());
				   }
			   }
			   else if (!preAny && backAny)
			   {
				   if (fileNameWithoutExpend.find(toFindFileName, 0) == 0
					   && fileNameWithoutExpend.length() >= toFindFileName.length())
				   {
					   vec_Str.push_back(filefinder.GetFilePath());
				   }
			   }
		   }
		   else 
		   {
			   vec_Str.push_back(filefinder.GetFileName());
		   }
	   }
   }

   filefinder.Close();
}

bool CStdFileFind::FindNextFile(void)
{
	if (NULL == m_pfilefinder)
	{
		return false;
	}
	
	m_pFind = m_pNext;
	m_strName.assign(m_pFind->d_name);

	m_pNext = readdir(m_pfilefinder);
	if (m_pNext == NULL)
	{
		return false;
	}

	return true;
}

