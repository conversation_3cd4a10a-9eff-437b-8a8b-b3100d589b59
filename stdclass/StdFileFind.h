#ifndef _STDFILEFIND_H_
#define _STDFILEFIND_H_

#include "./StdHeader.h"

class CStdFileFind
{
public:
	CStdFileFind(void);
	~CStdFileFind(void);

	std::string GetFileName(void) const;// 只返回文件名
	std::string GetFilePath(void) const;// 返回路径+文件名

	bool IsDots(void) const;
	bool IsDirectory(void) const;

	void Close(void);
	bool FindFile(const char* pstrName = NULL /* 只支持路径,最后要加'/'或'\\',返回全部文件 */);
	bool FindNextFile(void);

	static void FindAllFile(std::vector<std::string>& vec_Str, const char* pstrName = NULL, const char* pstrFileName = NULL);

private:
	std::string m_strPath;
	std::string m_strName;
	DIR* m_pfilefinder;
	struct dirent* m_pFind;
	struct dirent* m_pNext;
};
// 只返回文件名
inline std::string CStdFileFind::GetFileName(void) const
{
	return m_strName;
};
// 返回路径+文件名
inline std::string CStdFileFind::GetFilePath(void) const
{
	return (m_strPath + m_strName); //要验证
};

#endif
