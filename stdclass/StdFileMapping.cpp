// StdFileMapping.cpp: implementation of the CStdFileMapping class.
//
//////////////////////////////////////////////////////////////////////

#include "StdFileMapping.h"

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////
CStdFileMapping::CStdFileMapping(void)
:m_strFileMapName(""), m_nMapLen(0), m_lpMapData(NULL), m_hFileMapping(-1)
{

}

CStdFileMapping::CStdFileMapping(const char* const pMapName, const int nLen)
:m_strFileMapName(pMapName), m_nMapLen(nLen), m_lpMapData(NULL), m_hFileMapping(-1)
{
	char szName[MAX_PATH] = "";
	sprintf(szName, "/shm%s", m_strFileMapName.c_str());
	m_strFileMapName.assign(szName);

	InitInstance();
}

CStdFileMapping::~CStdFileMapping(void)
{
	try
	{
		ExitInstance();
	}
	catch (...)
	{
	}
}

bool CStdFileMapping::Open(const char* const pMapName, const int nLen)
{
	m_strFileMapName.assign(pMapName);
	m_nMapLen = nLen;

	if ((m_strFileMapName.compare("") == 0) ||
		(m_nMapLen <= 0))
	{
		return false;
	}

	UnMapView();

	m_hFileMapping = shm_open(m_strFileMapName.c_str(), O_RDWR, S_IRWXO|S_IRWXG|S_IRWXU);

	if (m_hFileMapping < 0)
	{
		return false;
	}
	else
	{
		return MapView();
	}
}

bool CStdFileMapping::Create(const char* const pMapName, const int nLen)
{
	m_strFileMapName.assign(pMapName);
	m_nMapLen = nLen;

	return InitInstance();
}

bool CStdFileMapping::InitInstance(void)
{
	if ((m_strFileMapName.compare("") == 0) ||
		(m_nMapLen <= 0))
	{
		return false;
	}

#ifdef SYSTEMV_MAP
	m_Key = hashCode(m_strFileMapName.c_str());
#endif
	
	UnMapView();

	if (CreateMap())
	{
		return MapView();
	}

	return false;
}

void CStdFileMapping::ExitInstance(void)
{
	UnMapView();
}

bool CStdFileMapping::CreateMap(void)
{
#ifdef SYSTEMV_MAP
	m_hFileMapping = shmget(m_Key, m_nMapLen, IPC_CREAT|0666);
#else
	m_hFileMapping = shm_open(m_strFileMapName.c_str(), O_CREAT|O_RDWR, S_IRWXO|S_IRWXG|S_IRWXU);
	if (m_hFileMapping > 0 )
	{
		if (ftruncate(m_hFileMapping, m_nMapLen + 1) < 0)
		{
			return false;
		}
	}
#endif
	if (m_hFileMapping == -1)
	{
		return false;
	}

	return true;
}

bool CStdFileMapping::MapView(void)
{
#ifdef SYSTEMV_MAP
	struct shmid_ds buf;
	
	shmctl(m_hFileMapping, IPC_STAT, &buf);

	m_lpMapData = shmat(m_hFileMapping, 0, 0);

	if (buf.shm_nattch <= 0)
	{
		memset(m_lpMapData, 0, m_nMapLen);
	}

#else
	m_lpMapData = mmap(0, m_nMapLen, PROT_READ|PROT_WRITE|PROT_EXEC, MAP_SHARED, m_hFileMapping, 0);
#endif

	if (m_lpMapData == (void*)-1)
	{
		return false;
	}

	return true;
}

void CStdFileMapping::UnMapView(void)
{
	if (m_lpMapData > 0)
	{		
#ifdef SYSTEMV_MAP
		shmdt((char*)m_lpMapData);
#else
		munmap(m_lpMapData, m_nMapLen);
#endif
		
		m_lpMapData = NULL;
	}
	if (m_hFileMapping > 0)
	{
#ifdef SYSTEMV_MAP
		struct shmid_ds buf;
		
		shmctl(m_hFileMapping, IPC_STAT, &buf);
		
		if (buf.shm_nattch <= 0)
		{
			shmctl(m_hFileMapping, IPC_RMID, 0);
		}
#else
		shm_unlink(m_strFileMapName.c_str());

		close(m_hFileMapping);
#endif

		m_hFileMapping = -1;
	}

}

void CStdFileMapping::ReadFileMap(void* const pMapData, const int nLen,const int noffset)
{
	if ((m_lpMapData == 0) || (m_lpMapData == (void*)-1))
	{
		if (!InitInstance())
		{
			return;
		}
	}

	int nCount = nLen;

	if (nLen > (m_nMapLen - noffset))
	{
		nCount = m_nMapLen - noffset;
	}

	memcpy(pMapData, (BYTE*)m_lpMapData + noffset, nCount);
}

void CStdFileMapping::WriteFileMap(const void* const pMapData, const int nLen,const int noffset)
{
	if ((m_lpMapData == 0) || (m_lpMapData == (void*)-1))
	{
		if (!InitInstance())
		{
			return;
		}
	}

	int nCount = nLen;

	if (nLen > (m_nMapLen - noffset))
	{
		nCount = m_nMapLen - noffset;
	}

	memcpy((BYTE*)m_lpMapData + noffset, pMapData, nCount);
}

int CStdFileMapping::hashCode(const char* const pStr) 
{
	int hash = 0;
	
	size_t len = strlen(pStr);
	
	for (size_t i = 0; i < len; i++) 
	{
		hash = 31 * hash + pStr[i];
	}
	
	return hash;
}

