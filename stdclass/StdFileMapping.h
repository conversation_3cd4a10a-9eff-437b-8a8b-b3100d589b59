// StdFileMapping.h: interface for the CStdFileMapping class.
//
//////////////////////////////////////////////////////////////////////

#ifndef __STDFILEMAPPING_H__
#define __STDFILEMAPPING_H__

#include "./StdHeader.h"
#include "./StdLock.h"

#define MAX_MAPPING_COUNT 100

#define SYSTEMV_MAP

class CStdFileMapping  
{
public:
	CStdFileMapping(void);
	CStdFileMapping(const char* const pMapName, const int nLen);
	virtual ~CStdFileMapping(void);

	bool Open(const char* const pMapName, const int nLen);
	bool Create(const char* const pMapName, const int nLen);
	void ReadFileMap(void* const pMapData, const int nLen,const int noffset = 0);
	void WriteFileMap(const void* const pMapData, const int nLen,const int noffset = 0);

protected:
	bool InitInstance(void);
	void ExitInstance(void);

	bool CreateMap(void);
	bool MapView(void);
	void UnMapView(void);
	int hashCode(const char* const pStr); 

	std::string m_strFileMapName;
	int m_nMapLen;
	void* m_lpMapData;

	int m_hFileMapping;

#ifdef SYSTEMV_MAP
	key_t m_Key;
#endif
};

typedef struct STRUCT_BUFF_INFO
{
	char szSubKey[64];
	char szFileName[MAX_PATH];
	long nValue;
}tSTRUCT_BUFF_INFO;

#endif 
