
#ifndef __STDHEADER_H__
#define __STDHEADER_H__

#include <stdarg.h>
#include <ucontext.h>
#include <dirent.h>
#include <unistd.h>
#include <pthread.h>
#include <sys/mman.h>
#include <fcntl.h>
#include <sys/socket.h>
#include <sys/wait.h>
#include <arpa/inet.h>
#include <netinet/in.h>
#include <netdb.h>
#include <sys/ipc.h>
#include <sys/shm.h>
#include <sys/time.h>
#include <netinet/tcp.h>
//-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
//
// Path Manipulation
//
//-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
// Sizes for buffers used by the _makepath() and _splitpath() functions.
// note that the sizes include space for 0-terminator
#define MAX_PATH   256 // max. length of full pathname
#define MAX_DRIVE  3   // max. length of drive component
#define MAX_DIR    256 // max. length of path component
#define MAX_FNAME  256 // max. length of file name component
#define MAX_EXT    256 // max. length of extension component

#ifndef FALSE
#define FALSE   0
#endif

#ifndef TRUE
#define TRUE    1
#endif

typedef int                 INT;
typedef int                 BOOL;
typedef int                 SOCKET;
typedef unsigned int        UINT, UINT32;
typedef unsigned char       BYTE, UINT8;
typedef unsigned short      WORD, UINT16;
typedef unsigned long       DWORD;
typedef unsigned long long  __uint64, UINT64, DWORDLONG;
typedef long long           __int64;
typedef __int64             LONGLONG;

typedef signed char         INT8;
typedef signed short        INT16;
typedef signed int          INT32;
typedef __int64             INT64;

#define INVALID_SOCKET  (SOCKET)(~0)


#include <assert.h>
#include <sys/types.h>
#include <sys/resource.h>
#include <sys/stat.h>
#include <errno.h>
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <signal.h>
#include <algorithm>
#include <vector>
#include <string>
#include <list>
#include <map>
#include <set>
#include <deque>
#include <math.h>
#include <climits>

#endif
