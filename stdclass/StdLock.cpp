
#include "StdLock.h"

CStdLock::CStdLock(std::string name, BOOL bLog)
{
	pthread_mutexattr_t _attributes;
    if (pthread_mutexattr_init(&_attributes) == 0)
	{
		pthread_mutexattr_settype(&_attributes, PTHREAD_MUTEX_RECURSIVE);
		pthread_mutex_init(&m_hObject, &_attributes);
		pthread_mutexattr_destroy(&_attributes);
	}
	else
	{
		pthread_mutex_init(&m_hObject, NULL);
	}

    bLogFlag  = bLog;
    logName   = name;
}

CStdLock::CStdLock()
{
    pthread_mutexattr_t _attributes;
    if (pthread_mutexattr_init(&_attributes) == 0)
    {
        pthread_mutexattr_settype(&_attributes, PTHREAD_MUTEX_RECURSIVE);
        pthread_mutex_init(&m_hObject, &_attributes);
        pthread_mutexattr_destroy(&_attributes);
    }
    else
    {
        pthread_mutex_init(&m_hObject, NULL);
    }

	bLogFlag  = FALSE;
    logName   = "";
}

CStdLock::~CStdLock(void)
{
	pthread_mutex_destroy(&m_hObject);
}

void CStdLock::Lock(void)
{
	pthread_mutex_lock(&m_hObject);
}

void CStdLock::Unlock(void)
{
	pthread_mutex_unlock(&m_hObject);
}

