#ifndef __STDLOCK_H__
#define __STDLOCK_H__

#include "StdHeader.h"

class CStdLock
{
public:
    CStdLock(std::string name, BOOL bLog);
    CStdLock();
	~CStdLock(void);

	void Lock(void);
	void Unlock(void);
    BOOL bLogFlag;
    std::string logName;
private:
	pthread_mutex_t m_hObject;

};

class CStdAutoLock
{
public:
	CStdAutoLock(CStdLock* const inlock)
		: plock_(inlock)
	{
		if(plock_!= NULL)
		{
			plock_->Lock();
		}
	};
	~CStdAutoLock(void)
	{
		if(plock_ != NULL)
		{
			try
			{
				plock_->Unlock();
			}
			catch (...)
			{
			}
			plock_ = NULL;
		}
	};
private:
	CStdLock* plock_;
};

typedef CStdLock     STDLOCK;

#endif