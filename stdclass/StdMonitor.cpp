// StdMonitor.cpp: implementation of the CStdMonitor class.
//
//////////////////////////////////////////////////////////////////////

#include "StdMonitor.h"
#include "StdCommond.h"
#include "StdProfile.h"

#define MAX_LOG_SIZE (2048 * 1024 * 50)
//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

void CStdConsole::CreateStdConsole(void)
{

}

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

BYTE CStdMonitor::m_bStatus(CStdMonitor::Log);

CStdMonitor* CStdMonitor::Instance(void)
{
	static CStdMonitor theMonitor;
	return &theMonitor;
};


std::vector<std::string>CStdInfo::m_vecInfo;

void CStdInfo::OutPut(void)
{
	std::vector<std::string>::iterator it;
	for (it = m_vecInfo.begin(); it != m_vecInfo.end(); it++)
	{
		CStdConsole::OutPut(((*it).c_str()));
	}

	m_vecInfo.clear();
}

void CStdInfo::Input(const char* const pInfo)
{
	if ((pInfo == NULL) || (strlen(pInfo) == 0))
	{
		return;
	}

	std::string strInfo(pInfo);

	m_vecInfo.push_back(strInfo);
}

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

void CStdMonitor::OutPut(CStdLog& thelog, const char* const pInfo, const char* const pOutput)
{
	CStdAutoLock theLock(&m_lock);
	char szLog[100000] = "";

	struct tm tmNow;
	time_t totalspan = 0;
	time(&totalspan);

	tmNow = *localtime_r(&totalspan, &tmNow);

	if (pOutput == NULL)
	{
		if (strlen(pInfo) < sizeof(szLog) - 255)
		{
			sprintf(szLog, "%4d/%02d/%02d %02d:%02d:%02d [ThreadId:0x%08lx] %s\n", tmNow.tm_year + 1900, tmNow.tm_mon + 1, tmNow.tm_mday,
			tmNow.tm_hour, tmNow.tm_min, tmNow.tm_sec, pthread_self(), pInfo);
		}
	}
	else
	{
		if (strlen(pInfo) < sizeof(szLog) - 255)
		{
			sprintf(szLog, "%4d/%02d/%02d %02d:%02d:%02d [ThreadId:0x%08lx] %s %s\n", tmNow.tm_year + 1900, tmNow.tm_mon + 1, tmNow.tm_mday,
			tmNow.tm_hour, tmNow.tm_min, tmNow.tm_sec, pthread_self(), pInfo, pOutput);
		}
	}

	switch(m_bStatus)
	{
	case CStdMonitor::Log:
		thelog.OutPut(szLog);
		break;
	case CStdMonitor::Real:
		CStdConsole::OutPut(szLog);
		break;
	case CStdMonitor::All:
		CStdConsole::OutPut(szLog);
		thelog.OutPut(szLog);
		break;
	default:
		;
	}
}

void CStdMonitor::OutPut(const int pInfo1, const int pInfo2)
{
	CStdAutoLock theLock(&m_lock);
	if ((m_bStatus & CStdMonitor::Real) != CStdMonitor::Real) 
	{
		return;
	}

	char szLog[1024] = "";

	struct tm tmNow;
	time_t totalspan = 0;
	time(&totalspan);

	tmNow = *localtime_r(&totalspan, &tmNow);

	sprintf(szLog, "%4d/%02d/%02d %02d:%02d:%02d curdata %d, total = %u\n", tmNow.tm_year + 1900, tmNow.tm_mon + 1, tmNow.tm_mday,
		tmNow.tm_hour, tmNow.tm_min, tmNow.tm_sec, pInfo1, pInfo2);

	CStdConsole::OutPut(szLog);
}

void CStdMonitor::OutPut(const char* const pInfo)
{
	CStdAutoLock theLock(&m_lock);
	if ((m_bStatus & CStdMonitor::Real) != CStdMonitor::Real) 
	{
		return;
	}

	char szLog[100000] = "";

	struct tm tmNow;
	time_t totalspan = 0;
	time(&totalspan);

	tmNow = *localtime_r(&totalspan, &tmNow);

	if (strlen(pInfo) < sizeof(szLog) - 255)
	{
		sprintf(szLog, "%4d/%02d/%02d %02d:%02d:%02d %s\n", tmNow.tm_year + 1900, tmNow.tm_mon + 1, tmNow.tm_mday,
		tmNow.tm_hour, tmNow.tm_min, tmNow.tm_sec, pInfo);	
	}

	CStdConsole::OutPut(szLog);

}

void CStdMonitor::OutPutInterface(const char* const pInfo)
{
	char szLog[1024] = "";
	
	struct tm tmNow;
	time_t totalspan = 0;
	time(&totalspan);

	tmNow = *localtime_r(&totalspan, &tmNow);

	sprintf(szLog, "%4d/%02d/%02d %02d:%02d:%02d [ThreadId:0x%08lx] %s\n", tmNow.tm_year + 1900, tmNow.tm_mon + 1, tmNow.tm_mday,
		tmNow.tm_hour, tmNow.tm_min, tmNow.tm_sec, pthread_self(), pInfo);
}

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CStdLogFileMng::CStdLogFileMng(const char* const pLogName)
:m_LogFile(),
m_nCount(0)
{
	OpenLogFile(pLogName);
}

CStdLogFileMng::~CStdLogFileMng()
{
	m_LogFile.Close();
	m_nCount = 0;
}

void CStdLogFileMng::OpenLogFile(const char* const pLog)
{
	if (m_LogFile.IsOpen())
	{
		return;
	}

	char szPath[MAX_PATH] = "";
	CStdCommond::GetProcessPath(szPath);
	strcat(szPath, "log/");
	CStdCommond::CreateDirec(szPath);

	m_nCount = 0;
	if ((pLog == NULL) || (strlen(pLog) == 0))
	{
		strcat(szPath, "log.txt");
	}
	else
	{
		strcat(szPath, pLog);
	}

	unsigned int nLen = 0;

	if (CStdFile::GetStatus(szPath, nLen))
	{
		if (nLen >= MAX_LOG_SIZE)
		{
			LogMng(szPath);
		}
	}
	else
	{
		LogMng(szPath);
	}

	if (!(m_LogFile.IsOpen()))
	{
		m_LogFile.Open(szPath, "a+t");
	}
}

void CStdLogFileMng::LogMng(const char* const pLog)
{
	char szOld[MAX_PATH] = { 0 };
	memset(szOld, 0, sizeof(szOld));

	sprintf(szOld, "%s_%ld.bak", pLog, time(NULL));

	CStdFile::Remove(szOld);

	CStdFile::Rename(pLog, szOld);
}

void CStdLogFileMng::WriteLogFile(const char* const pOutput)
{
	if ((NULL == pOutput) 
		|| (!(m_LogFile.IsOpen())))
	{
		return;
	}

	char szLogCount[64] = "";
	sprintf(szLogCount, "(L:%d) \t", m_nCount);
	m_LogFile.PutStr(szLogCount);

	m_LogFile.PutStr(pOutput);
	if (pOutput[strlen(pOutput) - 1] != '\n')
	{
		m_LogFile.PutStr("\r\n");
	}
	m_nCount++;

	unsigned int nLen = m_LogFile.GetLength();
	if (nLen >= MAX_LOG_SIZE)
	{
		m_nCount = 0;

		std::string strfile(m_LogFile.GetFilePath());

		m_LogFile.Close();

		LogMng(strfile.c_str());

		m_LogFile.Open(strfile.c_str(), "a+t");
	}
}

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

std::map<std::string, CStdLogFileMng*> CStdLog::m_mapStdLogFileMng;

CStdLog::CStdLog(const char* const pLog)
: m_lock(),
m_pLogFileMng(NULL)
{
	Initialize(pLog);
}

CStdLog::~CStdLog(void)
{
	m_pLogFileMng = NULL;
}

void CStdLog::DeleteLogFileStaticMap(void)
{
	std::map<std::string, CStdLogFileMng*>::iterator it = m_mapStdLogFileMng.begin();

	while (it != m_mapStdLogFileMng.end())
	{
		delete it->second;
		it->second = NULL;

		it = m_mapStdLogFileMng.erase(it);
	}
}

CStdLog* CStdLog::Instance(void)
{
	static CStdLog _theLog("log.txt");
	return &_theLog;
}

void  CStdLog::Initialize(const char* const pLog)
{
	CStdAutoLock theLock(&m_lock);

	std::map<std::string, CStdLogFileMng*>::iterator it = m_mapStdLogFileMng.find(pLog);

	if (it != m_mapStdLogFileMng.end())
	{
		m_pLogFileMng = it->second;
	}
	else
	{
		CStdLogFileMng* logFileMng = new CStdLogFileMng(pLog);

		m_mapStdLogFileMng.insert(make_pair((std::string)pLog, logFileMng));

		m_pLogFileMng = logFileMng;
	}

	m_pLogFileMng->OpenLogFile(pLog);
}

void CStdLog::OutPut(const char* const pOutput)
{
	if (m_pLogFileMng == NULL)
	{
		return;
	}

	CStdAutoLock theLock(&m_lock);

	m_pLogFileMng->WriteLogFile(pOutput);
}

FILE* CStdLog::GetFile(void)
{
	if (m_pLogFileMng == NULL)
	{
		return NULL;
	}

	CStdAutoLock theLock(&m_lock);

	return m_pLogFileMng->GetFile();
};

std::string CStdLog::GetFileName(void)
{
	if (m_pLogFileMng == NULL)
	{
		return NULL;
	}

	CStdAutoLock theLock(&m_lock);

	return m_pLogFileMng->GetFileName();
};
