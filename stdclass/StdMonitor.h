// StdMonitor.h: interface for the CStdMonitor class.
//
//////////////////////////////////////////////////////////////////////

#ifndef __STDMONITOR_H__
#define __STDMONITOR_H__

#include "./StdHeader.h"
#include "./StdLock.h"
#include "./StdFile.h"

#define CMDWND_TITLE "实时信息查看器"

class CStdConsole
{
public:
	static void OutPut(const char* const pOutput = NULL)
	{
		printf(pOutput);
	};
	static void OutPutInt(int nValue, const char* format = "%d\n")
	{
		char pOutput[64];
		sprintf(pOutput, format, nValue);
		printf(pOutput);
	};
	
	static void CreateStdConsole(void);
};

class CStdInfo
{
public:
	static void OutPut(void);
	static void Input(const char* const pInfo);
private:
	static std::vector<std::string>m_vecInfo;
};

class CStdLogFileMng
{
public:
	CStdLogFileMng(const char* const pLogName);
	~CStdLogFileMng();

	void OpenLogFile(const char* const pLog);

	void WriteLogFile(const char* const pOutput);

	FILE* GetFile(void)
	{
		return m_LogFile.GetFile();
	};
	//从文件路径+文件名获取文件名
	std::string GetFileName(void)
	{
		std::string strfile = m_LogFile.GetFilePath();

		const char * finename = basename(strfile.c_str());

		return std::string(finename);
	};

private:
	void LogMng(const char* const pLog);

	CStdFile m_LogFile;
	UINT m_nCount;
};

class CStdLog
{
public:
	enum LOG_TYPE {Log = 0x01, Real = 0x02};

	CStdLog(const char* const pLog);
	
	~CStdLog(void);

	static CStdLog* Instance(void);

	void Initialize(const char* const pLog);
	void OutPut(const char* const pOutput = NULL);
	
	FILE* GetFile(void);
	//从文件路径+文件名获取文件名
	std::string GetFileName(void);

	/** 释放静态变量
	 *  所有对象默认在程序运行期间都不释放，程序退出时，在CStdMonitor析构中释放。
	 */
	static void DeleteLogFileStaticMap(void);

private:
	CStdLock m_lock;
	CStdLogFileMng* m_pLogFileMng;

	/** 控制同一个日志文件只能产生一个CStdLogFileMng对象。
	 *  防止多线程打开同一个日志文件时产生多个文件句柄。
	 *  所有对象默认在程序运行期间都不释放，程序退出时，在CStdMonitor析构中释放。
	 */
	static std::map<std::string, CStdLogFileMng*> m_mapStdLogFileMng;
};

class CStdMonitor  
{
public:
	CStdMonitor(void)	{};

	~CStdMonitor(void)
	{
		CStdLog::DeleteLogFileStaticMap();
	};

	enum MONITOR_STATUS {Log = 0x01, Real = 0x02, All = (Log|Real), Cmd = 0x04, Para = 0x08, Off = 0};

	static CStdMonitor* Instance(void);

	void setMonitor(const MONITOR_STATUS bStatus) const
	{
		if (Cmd == bStatus)
		{
			return;
		}
		else if (Para == bStatus)
		{
			CStdInfo::OutPut();
		}
		else
		{
			m_bStatus |= bStatus;
		}
	};

	void resetMonitor(const MONITOR_STATUS bStatus) const
	{
		if (Cmd != bStatus)
		{
			if (Para != bStatus)
			{
				m_bStatus ^= bStatus;
			}

			return;
		}
	};

	void OutPut(CStdLog& thelog, const char* const pInfo, const char* const pOutput = NULL);
	void OutPut(const char* const pInfo);
	void OutPut(const int pInfo1, const int pInfo2);
	void OutPutInterface(const char* const pInfo);
private:
	static BYTE m_bStatus;
  CStdLock    m_lock;
};

#define OUTPUT_MON2(content1, content2) CStdMonitor::Instance()->OutPut(content1, content2)
#define OUTPUT_MON(content) CStdMonitor::Instance()->OutPut(content)
#define OUTPUT_ERR(content) CStdMonitor::Instance()->OutPut(*(CStdLog::Instance()), content)
#define OUTPUT_LOG(thelog, content) CStdMonitor::Instance()->OutPut(thelog, content)
#define OUTPUT_LOG2(thelog, info, content) CStdMonitor::Instance()->OutPut(thelog, info, content)
#define OUTPUT_INTERFACE(content) CStdMonitor::Instance()->OutPutInterface(content)

#endif 
