#include "StdNetBuffer.h"

#define MOVENEXT(X,Y,Z) \
	if((X+Y) > Z){ \
	return FALSE; \
	}else{ \
	X += Y; \
	}
#define MOVENEXTTEST(X,Y,Z) \
	if((X+Y) > Z){ \
	return FALSE; \
	}


CStdNetBufferReader::CStdNetBufferReader(BYTE* buffer)
:m_buffer(NULL)
{
   m_buffer = buffer;
}

CStdNetBufferReader::~CStdNetBufferReader(void)
{
}

bool
CStdNetBufferReader::GetTinyInt(const BYTE* const buffer, int& ioffset, int& resultTinyInt, const int nCount)
{
	int itemp;
	memcpy(&itemp,buffer + ioffset,1);
	resultTinyInt = (int)(itemp & 0x000000ff);		
	MOVENEXT(ioffset,1,nCount);
	return true;
}

bool
CStdNetBufferReader::GetChar(const BYTE* const buffer, int& ioffset, char& resultChar, const int nCount)
{
	int itemp;
	memcpy(&itemp,buffer + ioffset,1);
	resultChar = (char)(itemp & 0x000000ff);		
	MOVENEXT(ioffset,1,nCount);
	return true;
}

bool 
CStdNetBufferReader::GetByte(const BYTE* const  buffer, int &ioffset, BYTE &resultByte, const int nCount)
{
	int itemp;
	memcpy(&itemp,buffer + ioffset,1);
	resultByte = BYTE(itemp & 0x000000ff);		
	MOVENEXT(ioffset,1,nCount);
	return true;
}

bool 
CStdNetBufferReader::GetSmallInt(const BYTE* const buffer, int& ioffset, WORD& resultWord, const int nCount)
{
	int itemp;
	memcpy(&itemp,buffer + ioffset,2);
	resultWord = WORD(itemp & 0x0000ffff);		
	resultWord = STD_MAKEWORD_NETSEQ1(resultWord);
	MOVENEXT(ioffset,2,nCount);
	return true;
}

bool 
CStdNetBufferReader::GetInt(const BYTE* const buffer, int& ioffset, int& resultInt, const int nCount)
{
	memcpy(&resultInt,buffer + ioffset,4);
	resultInt = STD_MAKEINT_NETSEQ1(resultInt);
	MOVENEXT(ioffset,4,nCount);
	return true;
}

bool 
CStdNetBufferReader::GetFloat(const BYTE* const buffer, int& ioffset, float& resultFloat, const int nCount)
{
	int temp;
	memcpy(&temp,buffer + ioffset,4);
	temp = STD_MAKEINT_NETSEQ1(temp);
	resultFloat = (float)(temp/1000.0);
	MOVENEXT(ioffset,4,nCount);
	return true;
}

bool 
CStdNetBufferReader::GetDouble(const BYTE* const buffer, int& ioffset, double& resultDouble, const int nCount)
{
	int temp;
	memcpy(&temp,buffer + ioffset,4);
	temp = STD_MAKEINT_NETSEQ1(temp);
	resultDouble = temp/10000000.0;
	MOVENEXT(ioffset,4,nCount);
	return true;
}

bool 
CStdNetBufferReader::GetStr(const BYTE* const buffer, int& ioffset, char* resultStr, const int nCount)
{
	memset(&resultStr, 0, sizeof(resultStr));

	MOVENEXTTEST(ioffset,2,nCount);
	WORD tm_len;
	memcpy(&tm_len,buffer + ioffset,2);
	tm_len = STD_MAKEWORD_NETSEQ1(tm_len);
	MOVENEXT(ioffset,2,nCount);

	MOVENEXTTEST(ioffset,tm_len,nCount);
	if(tm_len > sizeof(resultStr)){
		return FALSE;
	}
	memcpy(resultStr,buffer + ioffset,tm_len);
	MOVENEXT(ioffset,tm_len,nCount);

	return true;
}

bool 
CStdNetBufferReader::GetStr(const BYTE* const buffer, int& ioffset, std::string& resultStr, const int nCount)
{
	MOVENEXTTEST(ioffset,2,nCount);
	WORD tm_len;
	memcpy(&tm_len,buffer + ioffset,2);
	tm_len = STD_MAKEWORD_NETSEQ1(tm_len);
	MOVENEXT(ioffset,2,nCount);

	MOVENEXTTEST(ioffset,tm_len,nCount);
	char* strTemp = new char[tm_len+5];

	memset(strTemp, 0, tm_len+5);
	memcpy(strTemp,buffer + ioffset,tm_len);
	resultStr = std::string(strTemp);
	delete strTemp;
	MOVENEXT(ioffset,tm_len,nCount);
   
	return true;
}

bool 
CStdNetBufferReader::GetBuffer(const BYTE* const buffer, int& ioffset, int lenth, BYTE* resultBuffer, const int nCount)
{
    MOVENEXTTEST(ioffset,lenth,nCount);
    memcpy(resultBuffer,buffer + ioffset,lenth);
    MOVENEXT(ioffset,lenth,nCount);

	return true;
}


bool
CStdNetBufferReader::GetTinyInt(int& ioffset, int& resultChar, const int nCount)
{
	return GetTinyInt(m_buffer, ioffset, resultChar, nCount);
}

bool
CStdNetBufferReader::GetChar(int& ioffset, char& resultChar, const int nCount)
{
	return GetChar(m_buffer, ioffset, resultChar, nCount);
}

bool 
CStdNetBufferReader::GetByte(int &ioffset, BYTE &resultByte, const int nCount)
{
	return GetByte(m_buffer, ioffset, resultByte, nCount);
}

bool 
CStdNetBufferReader::GetSmallInt(int& ioffset, WORD& resultWord, const int nCount)
{
	return GetSmallInt(m_buffer, ioffset, resultWord, nCount);
}

bool 
CStdNetBufferReader::GetInt(int& ioffset, int& resultInt, const int nCount)
{
	return GetInt(m_buffer, ioffset, resultInt, nCount);
}

bool 
CStdNetBufferReader::GetFloat(int& ioffset, float& resultFloat, const int nCount)
{
	return GetFloat(m_buffer, ioffset, resultFloat, nCount);
}

bool 
CStdNetBufferReader::GetDouble(int& ioffset, double& resultDouble, const int nCount)
{
	return GetDouble(m_buffer, ioffset, resultDouble, nCount);
}

bool 
CStdNetBufferReader::GetStr(int& ioffset, char* resultStr, const int nCount)
{
	return GetStr(m_buffer, ioffset, resultStr, nCount);
}

bool 
CStdNetBufferReader::GetStr(int& ioffset, std::string& resultStr, const int nCount)
{
	return GetStr(m_buffer, ioffset, resultStr, nCount);
}

bool 
CStdNetBufferReader::GetBuffer(int& ioffset, int lenth, BYTE* resultBuffer, const int nCount)
{
	return GetBuffer(m_buffer, ioffset, lenth, resultBuffer, nCount);
}

//非网络字节序
bool 
CStdNetBufferReader::GetSmallIntNml(const BYTE* const buffer, int& ioffset, WORD& resultWord, const int nCount)
{
	int itemp;
	memcpy(&itemp,buffer + ioffset,2);
	resultWord = WORD(itemp & 0x0000ffff);		
	MOVENEXT(ioffset,2,nCount);
	return true;
}

bool 
CStdNetBufferReader::GetIntNml(const BYTE* const buffer, int& ioffset, int& resultInt, const int nCount)
{
	memcpy(&resultInt,buffer + ioffset,4);
	MOVENEXT(ioffset,4,nCount);
	return true;
}

bool 
CStdNetBufferReader::GetFloatNml(const BYTE* const buffer, int& ioffset, float& resultFloat, const int nCount)
{
	int temp;
	memcpy(&temp,buffer + ioffset,4);
	resultFloat = (float)(temp/1000.0);
	MOVENEXT(ioffset,4,nCount);
	return true;
}

bool 
CStdNetBufferReader::GetDoubleNml(const BYTE* const buffer, int& ioffset, double& resultDouble, const int nCount)
{
	int temp;
	memcpy(&temp,buffer + ioffset,4);
	resultDouble = temp/10000000.0;
	MOVENEXT(ioffset,4,nCount);
	return true;
}

bool 
CStdNetBufferReader::GetSmallIntNml(int& ioffset, WORD& resultWord, const int nCount)
{
	return GetSmallIntNml(m_buffer, ioffset, resultWord, nCount);
}

bool 
CStdNetBufferReader::GetIntNml(int& ioffset, int& resultInt, const int nCount)
{
	return GetIntNml(m_buffer, ioffset, resultInt, nCount);
}

bool 
CStdNetBufferReader::GetFloatNml(int& ioffset, float& resultFloat, const int nCount)
{
	return GetFloatNml(m_buffer, ioffset, resultFloat, nCount);
}

bool 
CStdNetBufferReader::GetDoubleNml(int& ioffset, double& resultDouble, const int nCount)
{
	return GetDoubleNml(m_buffer, ioffset, resultDouble, nCount);
}


//////////////////////////////////////////////////////////////////////////
CStdNetBufferWriter::CStdNetBufferWriter(BYTE* buffer)
:m_buffer(NULL)
{
	m_buffer = buffer;
}

CStdNetBufferWriter::~CStdNetBufferWriter(void)
{
}

bool
CStdNetBufferWriter::PushTinyInt(BYTE* const buffer, int& ioffset, int resultTinyInt, const int nCount)
{
	MOVENEXTTEST(ioffset,1,nCount);
	buffer[ioffset] = (BYTE)(resultTinyInt & 0x000000ff);	
	MOVENEXT(ioffset,1,nCount);
	return true;
}

bool
CStdNetBufferWriter::PushChar(BYTE* const buffer, int& ioffset, char resultChar, const int nCount)
{
	MOVENEXTTEST(ioffset,1,nCount);
	buffer[ioffset] = (BYTE)(resultChar & 0x000000ff);	
	MOVENEXT(ioffset,1,nCount);
	return true;
}

bool 
CStdNetBufferWriter::PushByte(BYTE* const buffer, int &ioffset, BYTE resultByte, const int nCount)
{
	MOVENEXTTEST(ioffset,1,nCount);
	buffer[ioffset] = (BYTE)(resultByte & 0x000000ff);	
	MOVENEXT(ioffset,1,nCount);
	return true;
}

bool 
CStdNetBufferWriter::PushSmallInt(BYTE* const buffer, int& ioffset, WORD resultWord, const int nCount)
{
	MOVENEXTTEST(ioffset,2,nCount);
	resultWord = STD_MAKEWORD_NETSEQ1(resultWord);
	memcpy(buffer+ioffset,&resultWord,2);
	MOVENEXT(ioffset,2,nCount);
	return true;
}

bool 
CStdNetBufferWriter::PushInt(BYTE* const buffer, int& ioffset, int resultInt, const int nCount)
{
	MOVENEXTTEST(ioffset,4,nCount);
	resultInt = STD_MAKEINT_NETSEQ1(resultInt);
	memcpy(buffer+ioffset,&resultInt,4);
	MOVENEXT(ioffset,4,nCount);
	return true;
}

bool 
CStdNetBufferWriter::PushInt64(BYTE* const buffer, int& ioffset, LONGLONG resultInt64, const int nCount)
{
	MOVENEXTTEST(ioffset,8,nCount);
	resultInt64 = STD_MAKEINT64_NETSEQ1(resultInt64);
	memcpy(buffer+ioffset,&resultInt64,8);
	MOVENEXT(ioffset,8,nCount);
	return true;
}

bool
CStdNetBufferWriter::PushFloat(BYTE* const buffer, int& ioffset, float resultFloat, const int nCount)
{
	MOVENEXTTEST(ioffset,4,nCount);
	int val = (int)(resultFloat*1000);
	val = STD_MAKEINT_NETSEQ1(val);
	memcpy(buffer+ioffset,&val,4);
	MOVENEXT(ioffset,4,nCount);
	return true; 
}

bool
CStdNetBufferWriter::PushDouble(BYTE* const buffer, int& ioffset, double resultDouble, const int nCount)
{
	//INT64 val = (INT64)(resultDouble*10000000);
	//val = MAKEINT64_NETSEQ1(val);
	//memcpy(buffer+ioffset,&val,8);
	//MOVENEXT(ioffset,8,nCount);

	MOVENEXTTEST(ioffset,4,nCount);
	int val = (int)(resultDouble*10000000);
	val = STD_MAKEINT_NETSEQ1(val);
	memcpy(buffer+ioffset,&val,4);
	MOVENEXT(ioffset,4,nCount);
	return true; 
}

bool 
CStdNetBufferWriter::PushStr(BYTE* const buffer, int& ioffset, char* resultStr, const int nCount)
{
    int lenth = strlen(resultStr);
	if (!PushSmallInt(buffer, ioffset, (WORD)lenth, nCount))
        return false;
	MOVENEXTTEST(ioffset,lenth,nCount);
	memcpy(buffer + ioffset,resultStr,lenth);
	MOVENEXT(ioffset,lenth,nCount);
	return true;
}

bool 
CStdNetBufferWriter::PushStr(BYTE* const buffer, int& ioffset, std::string resultStr, const int nCount)
{
	int lenth = resultStr.length();
	if (!PushSmallInt(buffer, ioffset, (WORD)lenth, nCount))
		return false;
	MOVENEXTTEST(ioffset,lenth,nCount);
	memcpy(buffer + ioffset,resultStr.c_str(),lenth);
	MOVENEXT(ioffset,lenth,nCount);

	return true;
}

bool 
CStdNetBufferWriter::PushStrNml(BYTE* const buffer, int& ioffset, std::string resultStr, const int nCount)
{
	int lenth = resultStr.length();
	if (!PushSmallIntNml(buffer, ioffset, (WORD)lenth, nCount))
		return false;
	MOVENEXTTEST(ioffset,lenth,nCount);
	memcpy(buffer + ioffset,resultStr.c_str(),lenth);
	MOVENEXT(ioffset,lenth,nCount);

	return true;
}


bool 
CStdNetBufferWriter::PushBuffer(BYTE* const tarBuffer, int& ioffset, int srcBufLen, const BYTE* const srcBuf, const int nCount)
{
    if(NULL == srcBuf
	   || srcBufLen < 0)
	{
       return false;
	}

	MOVENEXTTEST(ioffset,srcBufLen, nCount);
	memcpy(tarBuffer + ioffset,srcBuf,srcBufLen);
	MOVENEXT(ioffset,srcBufLen,nCount);
	return true;
}

bool 
CStdNetBufferWriter::PushBuffer(BYTE* const tarBuffer, int& ioffset, int srcBufLen, const BYTE* const srcBuf, int srcStartPos, int srcEndPos, const int nCount)
{
    if(srcStartPos < 0 || srcStartPos > srcBufLen
	   || srcEndPos < 0 || srcEndPos > srcBufLen
	   || srcStartPos > srcEndPos)
	{
		return false;
	}

	int len = srcEndPos - srcStartPos + 1;

	MOVENEXTTEST(ioffset,len, nCount);
	memcpy(tarBuffer + ioffset,srcBuf + srcStartPos,len);
	MOVENEXT(ioffset,len,nCount);

	return true;
}

bool 
CStdNetBufferWriter::PushHead(BYTE* const tarBuf, int& ioffset, int srcBufLen, const BYTE* const srcBuf, const int nCount)
{
	MOVENEXTTEST(ioffset,srcBufLen+2, nCount);
	memcpy(tarBuf + srcBufLen + 2,tarBuf,ioffset);
	memcpy(tarBuf,srcBuf,srcBufLen);
	int offsetTemp = srcBufLen;
	PushSmallInt(tarBuf, offsetTemp, (WORD)ioffset, nCount);
	MOVENEXT(ioffset,srcBufLen+2,nCount);
	return true;
}

bool 
CStdNetBufferWriter::FrontPushByte(BYTE* const buffer, int& ioffset, int addBufferLenth, const BYTE* const addBuffer, const int nCount)
{
	MOVENEXTTEST(ioffset,addBufferLenth, nCount);
	memcpy(buffer + addBufferLenth,buffer,ioffset);
	memcpy(buffer,addBuffer,addBufferLenth);
	MOVENEXT(ioffset,addBufferLenth,nCount);
	return true;
}

bool 
CStdNetBufferWriter::PushSmallIntNml(BYTE* const buffer, int& ioffset, WORD resultWord, const int nCount)
{
	MOVENEXTTEST(ioffset,2,nCount);
	memcpy(buffer+ioffset,&resultWord,2);
	MOVENEXT(ioffset,2,nCount);
	return true;
}

bool 
CStdNetBufferWriter::PushIntNml(BYTE* const buffer, int& ioffset, int resultInt, const int nCount)
{
	MOVENEXTTEST(ioffset,4,nCount);
	memcpy(buffer+ioffset,&resultInt,4);
	MOVENEXT(ioffset,4,nCount);
	return true;
}

bool 
CStdNetBufferWriter::PushInt64Nml(BYTE* const buffer, int& ioffset, LONGLONG resultInt64, const int nCount)
{
	MOVENEXTTEST(ioffset,8,nCount);
	memcpy(buffer+ioffset,&resultInt64,8);
	MOVENEXT(ioffset,8,nCount);
	return true;
}



bool
CStdNetBufferWriter::PushTinyInt(int& ioffset, int resultTinyInt, const int nCount)
{
	return PushTinyInt(m_buffer, ioffset, resultTinyInt, nCount);
}

bool
CStdNetBufferWriter::PushChar(int& ioffset, char resultChar, const int nCount)
{
	return PushChar(m_buffer, ioffset, resultChar, nCount);
}

bool 
CStdNetBufferWriter::PushByte(int &ioffset, BYTE resultByte, const int nCount)
{
	return PushByte(m_buffer, ioffset, resultByte, nCount);
}

bool 
CStdNetBufferWriter::PushSmallInt(int& ioffset, WORD resultWord, const int nCount)
{
	return PushSmallInt(m_buffer, ioffset, resultWord, nCount);
}

bool 
CStdNetBufferWriter::PushInt(int& ioffset, int resultInt, const int nCount)
{
	return PushInt(m_buffer, ioffset, resultInt, nCount);
}

bool 
CStdNetBufferWriter::PushInt64(int& ioffset, LONGLONG resultInt, const int nCount)
{
	return PushInt64(m_buffer, ioffset, resultInt, nCount);
}

bool 
CStdNetBufferWriter::PushFloat(int& ioffset, float resultFloat, const int nCount)
{
    return PushFloat(m_buffer, ioffset, resultFloat, nCount);
}

bool 
CStdNetBufferWriter::PushDouble(int& ioffset, double resultDouble, const int nCount)
{
	return PushDouble(m_buffer, ioffset, resultDouble, nCount);
}

bool 
CStdNetBufferWriter::PushStr(int& ioffset, char* resultStr, const int nCount)
{
	return PushStr(m_buffer, ioffset, resultStr, nCount);
}

bool 
CStdNetBufferWriter::PushStr(int& ioffset, std::string resultStr, const int nCount)
{
	return PushStr(m_buffer, ioffset, resultStr, nCount);
}

bool 
CStdNetBufferWriter::PushStrNml(int& ioffset, std::string resultStr, const int nCount)
{
	return PushStr(m_buffer, ioffset, resultStr, nCount);
}

bool 
CStdNetBufferWriter::PushBuffer(int& ioffset, int lenth, const BYTE* const resultBuffer, const int nCount)
{
	return PushBuffer(m_buffer, ioffset, lenth, resultBuffer, nCount);
}

bool 
CStdNetBufferWriter::PushBuffer(int& ioffset, int srcBufLen, const BYTE* const srcBuf, int srcStartPos, int srcEndPos, const int nCount)
{
	return PushBuffer(m_buffer, ioffset, srcBufLen, srcBuf, srcStartPos, srcEndPos, nCount);
}

bool 
CStdNetBufferWriter::PushHead(int& ioffset, int lenth, const BYTE* const resultBuffer, const int nCount)
{
    return PushHead(m_buffer, ioffset, lenth, resultBuffer, nCount);
}

bool 
CStdNetBufferWriter::FrontPushByte(int& ioffset, int addBufferLenth, const BYTE* const addBuffer, const int nCount)
{
   return FrontPushByte(m_buffer, ioffset, addBufferLenth, addBuffer, nCount);
}

bool 
CStdNetBufferWriter::PushSmallIntNml(int& ioffset, WORD resultWord, const int nCount)
{
	return PushSmallIntNml(m_buffer, ioffset, resultWord, nCount);
}

bool 
CStdNetBufferWriter::PushIntNml(int& ioffset, int resultInt, const int nCount)
{
	return PushIntNml(m_buffer, ioffset, resultInt, nCount);
}

bool 
CStdNetBufferWriter::PushInt64Nml(int& ioffset, LONGLONG resultInt, const int nCount)
{
	return PushInt64Nml(m_buffer, ioffset, resultInt, nCount);
}