#pragma once
#include "StdHeader.h"

#define STD_MAKEWORD_NETSEQ(low,high) ((WORD)((BYTE)(high)) | (((WORD)(BYTE)(low))<<8))
#define STD_MAKEWORD_NETSEQ1(wNum) STD_MAKEWORD_NETSEQ((BYTE)(wNum & 0xff),(BYTE)((wNum & 0xff00) >> 8))

#define STD_MAKEINT_NETSEQ(wLow,wHigh) ((UINT)STD_MAKEWORD_NETSEQ1(wHigh) | (((UINT)STD_MAKEWORD_NETSEQ1(wLow))<<16))
#define STD_MAKEINT_NETSEQ1(iNum) STD_MAKEINT_NETSEQ((WORD)(iNum & 0xffff),(WORD)((iNum & 0xffff0000) >> 16) )

const DWORDLONG  std_gm_dwordlongmask = (DWORDLONG)0xffffffff * (DWORDLONG)0x10000 * (DWORDLONG)0x10000;

#define STD_MAKEINT64_NETSEQ(iLow,iHigh) ((DWORDLONG)STD_MAKEINT_NETSEQ1(iHigh) | (((DWORDLONG)STD_MAKEINT_NETSEQ1(iLow))<<32))
#define STD_MAKEINT64_NETSEQ1(i64Num) STD_MAKEINT64_NETSEQ((UINT)(i64Num & 0xffffffff),(UINT)((i64Num & std_gm_dwordlongmask) >> 32))

class CStdNetBufferReader
{
public:
	CStdNetBufferReader(BYTE* const buffer);
	~CStdNetBufferReader(void);

public:
	bool GetTinyInt(int& ioffset, int& resultTinyInt, const int nCount);
	bool GetByte(int& ioffset, BYTE& resultByte, const int nCount);
	bool GetChar(int& ioffset, char& resultChar, const int nCount);
	bool GetSmallInt(int& ioffset, WORD& resultWord, const int nCount);
	bool GetInt(int& ioffset, int& resultInt, const int nCount);
	bool GetFloat(int& ioffset, float& resultFloat, const int nCount);
	bool GetDouble(int& ioffset, double& resultDouble, const int nCount);
	bool GetStr(int& ioffset, char* resultStr, const int nCount); 
	bool GetStr(int& ioffset, std::string& resultStr, const int nCount); 
	bool GetBuffer(int& ioffset, int lenth, BYTE* resultBuffer, const int nCount);

	bool GetSmallIntNml(int& ioffset, WORD& resultWord, const int nCount);
	bool GetIntNml(int& ioffset, int& resultInt, const int nCount);
	bool GetFloatNml(int& ioffset, float& resultFloat, const int nCount);
	bool GetDoubleNml(int& ioffset, double& resultDouble, const int nCount);

	static bool GetTinyInt(const BYTE* const m_buffer, int& ioffset, int& resultTinyInt, const int nCount);
	static bool GetByte(const BYTE* const m_buffer, int& ioffset, BYTE& resultByte, const int nCount);
	static bool GetChar(const BYTE* const m_buffer, int& ioffset, char& resultChar, const int nCount);
	static bool GetSmallInt(const BYTE* const m_buffer, int& ioffset, WORD& resultWord, const int nCount);
	static bool GetInt(const BYTE* const m_buffer, int& ioffset, int& resultInt, const int nCount);
	static bool GetFloat(const BYTE* const m_buffer, int& ioffset, float& resultFloat, const int nCount);
	static bool GetDouble(const BYTE* const m_buffer, int& ioffset, double& resultDouble, const int nCount);
	static bool GetStr(const BYTE* const m_buffer, int& ioffset, char* resultStr, const int nCount); 
	static bool GetStr(const BYTE* const m_buffer, int& ioffset, std::string& resultStr, const int nCount); 
	static bool GetBuffer(const BYTE* const m_buffer, int& ioffset, int lenth, BYTE* resultBuffer, const int nCount);

	static bool GetSmallIntNml(const BYTE* const m_buffer, int& ioffset, WORD& resultWord, const int nCount);
	static bool GetIntNml(const BYTE* const m_buffer, int& ioffset, int& resultInt, const int nCount);
	static bool GetFloatNml(const BYTE* const m_buffer, int& ioffset, float& resultFloat, const int nCount);
	static bool GetDoubleNml(const BYTE* const m_buffer, int& ioffset, double& resultDouble, const int nCount);

protected:
    BYTE* m_buffer;
    

};


class CStdNetBufferWriter
{
public:
	CStdNetBufferWriter(BYTE* buffer);
	~CStdNetBufferWriter(void);

public:
	bool PushTinyInt(int& ioffset, int resultTinyInt, const int nCount);
	bool PushByte(int& ioffset, BYTE resultByte, const int nCount);
	bool PushChar(int& ioffset, char resultChar, const int nCount);
	bool PushSmallInt(int& ioffset, WORD resultWord, const int nCount);
	bool PushInt(int& ioffset, int resultInt, const int nCount);
	bool PushInt64(int& ioffset, LONGLONG resultInt64, const int nCount);
	bool PushFloat(int& ioffset, float resultFloat, const int nCount);
	bool PushDouble(int& ioffset, double resultDouble, const int nCount);
	bool PushStr(int& ioffset, char* resultStr, const int nCount); 
	bool PushStr(int& ioffset, std::string resultStr, const int nCount); 
	bool PushStrNml(int& ioffset, std::string resultStr, const int nCount); 
	bool PushBuffer(int& ioffset, int lenth, const BYTE* const resultBuffer, const int nCount);	
	bool PushBuffer(int& ioffset, int srcBufLen, const BYTE* const srcBuf, int srcStartPos, int srcEndPos, const int nCount);
	bool PushHead(int& ioffset, int lenth, const BYTE* const resultBuffer, const int nCount);
	bool FrontPushByte(int& ioffset, int addBufferLenth, const BYTE* const addBuffer, const int nCount);
	bool PushSmallIntNml(int& ioffset, WORD resultWord, const int nCount);
	bool PushIntNml(int& ioffset, int resultInt, const int nCount);
	bool PushInt64Nml(int& ioffset, LONGLONG resultInt64, const int nCount);


	static bool PushTinyInt(BYTE* const m_buffer, int& ioffset, int resultTinyInt, const int nCount);
	static bool PushByte(BYTE* const m_buffer, int& ioffset, BYTE resultByte, const int nCount);
	static bool PushChar(BYTE* const m_buffer, int& ioffset, char resultChar, const int nCount);
	static bool PushSmallInt(BYTE* const m_buffer, int& ioffset, WORD resultWord, const int nCount);
	static bool PushInt(BYTE* const m_buffer, int& ioffset, int resultInt, const int nCount);
	static bool PushInt64(BYTE* const m_buffer, int& ioffset, LONGLONG resultInt64, const int nCount);
	static bool PushFloat(BYTE* const m_buffer, int& ioffset, float resultFloat, const int nCount);
	static bool PushDouble(BYTE* const m_buffer, int& ioffset, double resultDouble, const int nCount);
	static bool PushStr(BYTE* const m_buffer, int& ioffset, char* resultStr, const int nCount); 
	static bool PushStr(BYTE* const m_buffer, int& ioffset, std::string resultStr, const int nCount);
	static bool PushStrNml(BYTE* const m_buffer, int& ioffset, std::string resultStr, const int nCount);
	static bool PushBuffer(BYTE* const tarBuffer, int& ioffset, int srcBufLen, const BYTE* const srcBuf, const int nCount);
	static bool PushBuffer(BYTE* const tarBuffer, int& ioffset, int srcBufLen, const BYTE* const srcBuf, int srcStartPos, int srcEndPos, const int nCount);
	static bool PushHead(BYTE* const tarBuf, int& ioffset, int srcBufLen, const BYTE* const srcBuf, const int nCount);
	static bool FrontPushByte(BYTE* const buffer, int& ioffset, int addBufferLenth, const BYTE* const addBuffer, const int nCount);
	static bool PushSmallIntNml(BYTE* const m_buffer, int& ioffset, WORD resultWord, const int nCount);
	static bool PushIntNml(BYTE* const m_buffer, int& ioffset, int resultInt, const int nCount);
	static bool PushInt64Nml(BYTE* const m_buffer, int& ioffset, LONGLONG resultInt64, const int nCount);

protected:
	BYTE* m_buffer;


};
