// CStdProfile.h: interface for the CStdIniFile class.
//
//////////////////////////////////////////////////////////////////////

#ifndef __STDPROFILE_H__
#define __STDPROFILE_H__

#include "./StdFile.h"
#include "./StdLock.h"

class CStdProfile  
{
public:
	CStdProfile(const char* const pIniName);
	~CStdProfile(void);

	bool GetProStrValue(const char* const pSec, const char* const pKey, char* const pValue, const char* const pDefault = "", const bool bCreat = true);
	bool GetProIntValue(const char* const pSec, const char* const pKey, int& nValue, const char* const pDefault = "0", const bool bCreat = true);
	void SetProStrValue(const char* const pSec, const char* const pKey, const char* const pValue, const bool bCreat = true);
	void SetProIntValue(const char* const pSec, const char* const pKey, const int nValue, const bool bCreat = true);
	void CorrectStr(char* pStr);
	/** 
	 * 函数名称: DelSection
	 * 功能描述: 删除某个小节
	 * @pSec: 段名，不需带上中括号。
	 * @返回值：是否删除成功
	**/
	bool DelSection(const char* const pSec);
	/** 
	 * 函数名称: DeleteKey
	 * 功能描述: 删除关键字
	 * @pSec: 段名，不需带上中括号。
	 * @pKey: 关键字
	 * @返回值：是否删除成功
	**/
	bool DeleteKey(const char* const pSec, const char* const pKey);

private:
	typedef struct STRUCT_INIINFO
	{
		std::string strValue; // 内容
		std::string strInfo; // 注释，只支持在同一行的注释
	}tSTRUCT_INIINFO;
	void Initialize(void);
	void GetContext(char* const pContxt, const char* const pInfo);
	char* ClearContext(char* pContxt, char** const pInfo);
	void MakeEntry(char* const pContxt, const char* const pInfo);
	void UpdateProfile(void);

	std::string m_ProfileName;
	std::string m_strSec;
	std::map<std::string, tSTRUCT_INIINFO> m_Section;// [...]Entry, value
	static CStdLock m_StdLock;
};

#endif
