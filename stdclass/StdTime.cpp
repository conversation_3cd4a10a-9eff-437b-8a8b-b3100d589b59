#include "StdTime.h"

CStdTime::CStdTime(int nSec)
{
   m_nSec = nSec;
   fresh();
}
CStdTime::CStdTime(const CStdTime& time)
{
	m_nSec = time.GetAllSecond();
	fresh();
}
CStdTime::CStdTime()
{
	m_nSec = 0;
	fresh();
}
CStdTime::~CStdTime(void)
{
}

int CStdTime::GetYear(void)const
{
	return m_time.tm_year + 1900;
}
BOOL CStdTime::SetYear(int year)
{
	if (year < 1900 || year > 2100)
	{
		return FALSE;
	}
	m_time.tm_year = year - 1900;
    m_nSec = mktime(&m_time);
	fresh();
	return TRUE;
}
int CStdTime::GetMonth(void)const
{
	return m_time.tm_mon + 1;
}
BOOL CStdTime::SetMonth(int month)
{
	if (month < 1 || month > 12)
	{
		return FALSE;
	}
	m_time.tm_mon = month - 1;
	m_nSec = mktime(&m_time);
	fresh();
	return TRUE;
}
int CStdTime::GetDay(void)const
{
	return m_time.tm_mday;
}
BOOL CStdTime::SetDay(int day)
{
	if (day < 1 || day > 31)
	{
		return FALSE;
	}
	m_time.tm_mday = day;
	m_nSec = mktime(&m_time);
	fresh();
	return TRUE;
}
int CStdTime::GetHour(void)const
{ 
	return m_time.tm_hour;
}
BOOL CStdTime::SetHour(int hour)
{
	if (hour < 0 || hour > 23)
	{
		return FALSE;
	}
	m_time.tm_hour = hour;
	m_nSec = mktime(&m_time);
	fresh();
	return TRUE;
}
int CStdTime::GetMinute(void)const
{
	return m_time.tm_min;
}
BOOL CStdTime::SetMinute(int minute)
{
	if (minute < 0 || minute > 59)
	{
		return FALSE;
	}
	m_time.tm_min = minute;
	m_nSec = mktime(&m_time);
	fresh();
	return TRUE;
}
int CStdTime::GetSecond(void)const
{
	return m_time.tm_sec;
}
BOOL CStdTime::SetSecond(int second)
{
	if (second < 0 || second > 59)
	{
		return FALSE;
	}
	m_time.tm_sec = second;
	m_nSec = mktime(&m_time);
	fresh();
	return TRUE;
}
int CStdTime::GetAllSecond(void)const
{
	return (int)m_nSec;
}
BOOL CStdTime::SetAllSecond(int second)
{
	if (second < 0)
	{
		return FALSE;
	}
	m_nSec = second;
	fresh();
	return TRUE;
}
void CStdTime::AddSec(const int sec)
{
   m_nSec += sec;
   fresh();
}
void CStdTime::AddMinute(const int minute)
{
	m_nSec += minute * 60;
	fresh();
}
void CStdTime::AddHour(const int hour)
{
	m_nSec += hour * 3600;
	fresh();
}
void CStdTime::AddDay(const int day)
{
   m_nSec += 86400;
   fresh();
}
void CStdTime::AddMonth(const int month)
{
   int tempMonth = m_time.tm_mon;
   tempMonth += month;   
   if (tempMonth > 0)
   {
      int year = tempMonth/13;
	  m_time.tm_year += year;
	  if (tempMonth > 12)
	  {
         tempMonth = tempMonth%12;
	  }
	  m_time.tm_mon = tempMonth;
   }
   else if (tempMonth <= 0)
   {
	   int year = tempMonth/12-1;
	   m_time.tm_year += year;
	   tempMonth = 12 + tempMonth%12;
	   m_time.tm_mon = tempMonth;
   }
   m_nSec = mktime(&m_time);
   fresh();
}
void CStdTime::AddYear(const int year)
{
   m_time.tm_year += year;
   m_nSec = mktime(&m_time);
   fresh();
}
void CStdTime::fresh(void)
{
  m_time = *localtime(&m_nSec);
}

