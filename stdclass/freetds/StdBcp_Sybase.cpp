// StdBcp_Sybase.cpp: implementation of the CStdBcp_Sybase class.
//
//////////////////////////////////////////////////////////////////////

#include "./StdBcp_Sybase.h"

const int MAX_FIELDS = 256;
const int MAX_RECORDLEN = 8192 * 3;
const int BLK_BATCH_COUNT = 1000;
const char TOKEN[] = "\t,";

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////
CStdBcp_Sybase::CStdBcp_Sybase(CStdLog* const pLog)
:m_pstrRec(NULL),
m_Token(NULL),
m_pLog(pLog)
{
	m_pstrRec = new char[MAX_RECORDLEN];

	memset(m_pstrRec, 0, MAX_RECORDLEN);
}

CStdBcp_Sybase::~CStdBcp_Sybase(void)
{
	m_Token = NULL;
	if (m_pstrRec != NULL)
	{
		delete []m_pstrRec;
		m_pstrRec = NULL;
	}

	m_pLog = NULL;
}

bool CStdBcp_Sybase::BcpData(DBPROCESS * _pdbproc, const char* const pFile, const char* const pTable)
{
	if ((_pdbproc == FAIL) || (pFile == NULL) || (pTable == NULL))
	{
		return false;
	}
	// 记录日志
	char szLog[MAX_PATH] = "";

	// 打开文件
	CStdFile bcpfile;

	if (!bcpfile.Open(pFile, "rt"))
	{
		sprintf(szLog, "bcp open file %s failed.", pFile);
		OUTPUT_LOG(*m_pLog, szLog);

		return false;
	}

	// 初始化批拷贝
	CStdSybase_Blk SybBlk(_pdbproc);

	if (!SybBlk.Initilize(pTable))
	{
		bcpfile.Close();

		sprintf(szLog, "bcpblk init table %s failed.", pTable);
		OUTPUT_LOG(*m_pLog, szLog);

		return false;
	}

	int nBlkCounter = 0; // 批计数器
	int nIndex = 0; // 字段位置
	int nRow = 0;

	while(bcpfile.GetStr(m_pstrRec, MAX_RECORDLEN) != NULL)
	{
		nRow++;
		
		if ((strlen(m_pstrRec) <= 2) || ((m_pstrRec[strlen(m_pstrRec) - 1] != 0x0A) && (m_pstrRec[strlen(m_pstrRec) - 1] != 0x0D)))
		{
			sprintf(szLog, "bcp row[%d] field[%d] of table[%s] from file[%s] 缺少回车换行符!", nRow, nIndex, pTable, pFile);
			OUTPUT_LOG(*m_pLog, szLog);

			continue;
		}
		
		// 去掉字符串末尾的 回车换行符
		TrimTailEnter(m_pstrRec);

		if (!BlkData(SybBlk, nIndex))
		{
			sprintf(szLog, "bcpblk bind row[%d] field[%d] of table[%s] from file[%s] failed.", nRow, nIndex, pTable, pFile);
			OUTPUT_LOG(*m_pLog, szLog);

			continue;
		}

		++nBlkCounter;

		if (nBlkCounter >= BLK_BATCH_COUNT)
		{
			nBlkCounter = 0;

			SybBlk.Done(1);
		}

		memset(m_pstrRec, 0, MAX_RECORDLEN);
	}

	SybBlk.Done(2);

	SybBlk.Drop();

	bcpfile.Close();

	return true;
}

bool CStdBcp_Sybase::SimpleBcpData(DBPROCESS * _pdbproc, const char* const pFile, const char* const pTable, int Dir)
{
	if ((_pdbproc == FAIL) || (pFile == NULL) || (pTable == NULL))
	{
		return false;
	}
	// 记录日志
	char szLog[MAX_PATH] = "";

	// 初始化批拷贝
	CStdSybase_Blk SybBlk(_pdbproc);

	if (!SybBlk.Initilize(pTable, pFile, Dir))
	{
		sprintf(szLog, "bcpblk init table %s failed.", pTable);
		OUTPUT_LOG(*m_pLog, szLog);

		return false;
	}

	CStdFile bcpfile;

	if (!bcpfile.Open(pFile, "rt"))
	{
		sprintf(szLog, "bcp open file %s failed.", pFile);
		OUTPUT_LOG(*m_pLog, szLog);

		return false;
	}

	if (bcpfile.GetStr(m_pstrRec, MAX_RECORDLEN) == NULL)
	{
		bcpfile.Close();

		return false;
	}
	bcpfile.Close();

	int nCount = 0;

	size_t nLen = strlen(m_pstrRec);

	for (size_t i = 0; i < nLen; i++)
	{
		if ((m_pstrRec[i] == '\t') || (m_pstrRec[i] == '\n'))
		{
			nCount++;
		}
	}

	if (!SybBlk.InitCol(nCount))
	{
		return false;
	}

	if (!SybBlk.DoBcp())
	{
		return false;
	}
	
	return true;
}

bool CStdBcp_Sybase::BlkData(CStdSybase_Blk & SybBlk, int &nIndex)
{
	nIndex = 0;

	size_t slen = strlen(m_pstrRec);
	char* sptr = m_pstrRec;
	char* eptr = m_pstrRec;

	for (size_t i = 0; i < slen; i++)
	{
			if (strchr(TOKEN, eptr[0]) != NULL)
			{
					++nIndex;
					eptr[0] = '\0';
					// 去掉字段内容前后两端的空格
					TrimSpace(&sptr);

					if (!SybBlk.Bind(nIndex, SYBCHAR, sptr, (char*)"", 1))
					{
							return false;
					}

					eptr++;
					sptr = eptr;
			}
			else
			{
					eptr++;
			}
	}

	++nIndex;
	if ((eptr - m_pstrRec) < MAX_RECORDLEN)
	{
			eptr[0] = '\0';
	}
	else
	{
			eptr--;
			eptr[0] = '\0';
	}
	// 去掉字段内容前后两端的空格
	TrimSpace(&sptr);

	if (!SybBlk.Bind(nIndex, SYBCHAR, sptr, (char*)"", 1))
	{
			return false;
	}

	sptr = nullptr;
	eptr = nullptr;

	return SybBlk.Rowxfer();
}

void CStdBcp_Sybase::TrimTailEnter(char* cptr)
{
	// 去除数据内容后端的回车换行符
		size_t len = strlen(cptr);
		while(cptr[len-1] == '\r' || cptr[len-1] == '\n')
		{
			cptr[len-1] = '\0';
			len = strlen(cptr);
		}
}

void CStdBcp_Sybase::TrimSpace(char** sptr)
{
		if (sptr == nullptr)
		{
				return;
		}
	
		// 去除字段内容前端的空格
		while((*sptr)[0] == ' ')
		{
			(*sptr)++;
		}

		// 去除字段内容后端的空格
		size_t sptrlen = strlen((*sptr));
		while((*sptr)[sptrlen-1] == ' ')
		{
			(*sptr)[sptrlen-1] = '\0';
			sptrlen = strlen((*sptr));
		}
}
