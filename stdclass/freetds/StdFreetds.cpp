// StdSql_Syb.cpp: implementation of the CStdSql_Syb class.
//
//////////////////////////////////////////////////////////////////////

#include "./StdFreetds.h"
#include <iconv.h>

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////
CStdFreetds::CStdFreetds(void)
:loginrec(NULL),
dbprocess(NULL)
{
	memset(cDbName, 0, sizeof(cDbName));
}

CStdFreetds::~CStdFreetds(void)
{
	loginrec = NULL;
	dbprocess = NULL;
}

void CStdFreetds::Clear_Con(void)
{
	loginrec = NULL;
	dbprocess = NULL;
	memset(cDbName, 0, sizeof(cDbName));
}

BOOL CStdFreetds::IsAvailable(void)
{
	if (loginrec == NULL)
	{
		return FALSE;
	}

	if (dbprocess == NULL)
	{
		return FALSE;
	}

	if (strcmp(cDbName, "") == 0)
	{
		return FALSE;
	}

	return TRUE;
}

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////
CStdFreetds_Connection::CStdFreetds_Connection(void)
:m_pFreetds(NULL),
m_pBcpSybase(NULL),
m_pBcpLog(NULL)
{
	m_pFreetds = new CStdFreetds();
}

CStdFreetds_Connection::CStdFreetds_Connection(CStdLog* _bcplog)
:m_pFreetds(NULL),
m_pBcpSybase(NULL),
m_pBcpLog(_bcplog)
{
	m_pFreetds = new CStdFreetds();
	m_pBcpSybase = new CStdBcp_Sybase(m_pBcpLog);
}

CStdFreetds_Connection::~CStdFreetds_Connection(void)
{
	if (m_pFreetds != NULL)
	{
		cleanup_conn();
		delete m_pFreetds;
		m_pFreetds = NULL;
	}

	if (m_pBcpSybase != NULL)
	{
		delete m_pBcpSybase;
		m_pBcpSybase = NULL;
	}
	
	m_pBcpLog = NULL;
}

BOOL CStdFreetds_Connection::connect(
				const char* const szUser,
				const char* const szPwd,
				const char* const szSvr,
				const char* const szDb)
{
	if (!m_pFreetds->IsAvailable())
	{
		if (!setup_conn(szUser, szPwd, szSvr, szDb))
		{
			cleanup_conn();
			return FALSE;
		}
	}

	return TRUE;
}

BOOL CStdFreetds_Connection::init_db(void)
{
	try
	{
		dbinit();
	}
	catch(...)
	{
		return FALSE;
	}

	return TRUE;
}

BOOL CStdFreetds_Connection::setup_conn(
				const char* const szUser,
				const char* const szPwd,
				const char* const szSvr,
				const char* const szDb)
{
	if (!init_db())
	{
		return FALSE;
	}

	if ((szUser == NULL) || (strcmp(szUser, "") == 0) ||
		(szPwd == NULL) || (strcmp(szPwd, "") == 0) ||
		(szSvr == NULL) || (strcmp(szSvr, "") == 0))
	{
		return FALSE;
	}

	// Create database connection object
	m_pFreetds->loginrec=dblogin();
		
	// Set the login user name
	DBSETLUSER(m_pFreetds->loginrec,szUser);
	
	// Set the login password
	DBSETLPWD(m_pFreetds->loginrec,szPwd);
	
	// Connect SQL server server address and port, here is the connection
	m_pFreetds->dbprocess = dbopen(m_pFreetds->loginrec, szSvr);
	if(m_pFreetds->dbprocess == FAIL)
	{
		OUTPUT_ERR("Connect MSSQLSERVER fail!...");
		return FALSE;
	}
	
  strcpy(m_pFreetds->cDbName, szDb);
	
	return TRUE;
}

void CStdFreetds_Connection::cleanup_conn(void)
{
	if (m_pFreetds->IsAvailable())
	{
		dbcancel(m_pFreetds->dbprocess);
		dbclose(m_pFreetds->dbprocess);
		m_pFreetds->Clear_Con();
	}
}

BOOL CStdFreetds_Connection::BindCol(int ncolnum, int bufflen, BYTE* pbuff)
{
	RETCODE ret = dbbind(m_pFreetds->dbprocess, ncolnum, STRINGBIND, bufflen, pbuff);
	if (ret != SUCCEED)
	{
		return FALSE;
	}
	
	return TRUE;
}

BOOL CStdFreetds_Connection::BindCol(int ncolnum, int bindtype, int bufflen, BYTE* pbuff)
{
	RETCODE ret = dbbind(m_pFreetds->dbprocess, ncolnum, bindtype, bufflen, pbuff);
	if (ret != SUCCEED)
	{
		return FALSE;
	}
	
	return TRUE;
}

BOOL CStdFreetds_Connection::select_dbname(void)
{
	if (!m_pFreetds->IsAvailable())
	{
		OUTPUT_ERR("[CStdFreetds_Connection::Execute] Connect Info is Invalid!...");
		return FALSE;
	}
	// 连接数据库
	if(dbuse(m_pFreetds->dbprocess, m_pFreetds->cDbName)==FAIL)
	{
		OUTPUT_ERR("[CStdFreetds_Connection::Execute] Open data basename fail!...");
		return FALSE;
	}

	return TRUE;
}

int CStdFreetds_Connection::Datalen(int colnum)
{
	int dlen = dbdatlen(m_pFreetds->dbprocess, colnum);

	return dlen;
}

BOOL CStdFreetds_Connection::BcpData(const char* const pFile, const char* const pTable)
{
	if (!select_dbname())
	{
		return FALSE;
	}
	
	bool bRt = m_pBcpSybase->BcpData(m_pFreetds->dbprocess, pFile, pTable);
	if (!bRt)
	{
		return FALSE;
	}

	return TRUE;
}

BOOL CStdFreetds_Connection::Execute(const char* cpSql)
{
	if (!select_dbname())
	{
		return FALSE;
	}

	dbcmd(m_pFreetds->dbprocess, cpSql);  //初始化查询数据指令
	
	// 执行命令
	if(dbsqlexec(m_pFreetds->dbprocess) == FAIL)
	{
		OUTPUT_ERR("[CStdFreetds_Connection::Execute] Query Sql Exec error!...");
		return FALSE;
	}

	return TRUE;
}

int CStdFreetds_Connection::DbResults(void)
{
	DBINT result_code=dbresults(m_pFreetds->dbprocess);
	
//	printf("NO_MORE_RESULTS = %d, SUCCEED = %d, FAIL = %d\n", NO_MORE_RESULTS, SUCCEED, FAIL);
//	printf("result_code = %d\n", result_code);
	
	if (result_code == NO_MORE_RESULTS)
	{  
		return E_NO_MORE_RESULTS;
	}
	else if (result_code == SUCCEED)
	{
		return E_SUCCEED;
	}
	else if (result_code == FAIL)
	{
		return E_FAIL;
	}

	return E_NO_MORE_RESULTS;
}

int CStdFreetds_Connection::DbNextRow(void)
{
	DBINT result_code = dbnextrow(m_pFreetds->dbprocess);
	
//	printf("NO_MORE_ROWS = %d, FAIL = %d\n", NO_MORE_ROWS, FAIL);
//	printf("result_code = %d\n", result_code);

	if (result_code == NO_MORE_ROWS)
	{
		return E_NO_MORE_ROWS;
	}
	else if (result_code == MORE_ROWS)
	{
		return E_MORE_ROWS;
	}
	else if (result_code == FAIL)
	{  
		return E_FAIL;
	}

	return E_NO_MORE_ROWS;
}


BOOL CStdFreetds_Connection::GetSelectColAttribute(const char* c_sql, std::vector<STRU_DBCOL>& vecColAttri)
{
	if (!select_dbname())
	{
		return FALSE;
	}
		
  // 查询数据库中表中的内容
  dbcmd(m_pFreetds->dbprocess, c_sql);  //设置查询数据表中的语句  
	// 执行命令
  if(dbsqlexec(m_pFreetds->dbprocess)==FAIL)
	{
		OUTPUT_ERR("[CStdFreetds_Connection::GetSelectColAttribute] Query table error!...");
		return FALSE;
	}

	int numcols = dbnumcols(m_pFreetds->dbprocess);
	// 查看命令执行的结果。
	for (int i = 1; i <= numcols; i++)
	{		
		STRU_DBCOL tdbcol;
		std::string strColName(dbcolname(m_pFreetds->dbprocess, i));
		tdbcol.columnName = strColName;
		tdbcol.columnType = dbcoltype(m_pFreetds->dbprocess, i);
		vecColAttri.push_back(tdbcol);
		//printf("colnum = %d, colname = %s, coltype = %d\n", i, strColName.c_str(), tdbcol.columnType);
	}
	
	//关闭数据库连接  
  cleanup_conn();

	return TRUE;
}

BOOL CStdFreetds_Connection::GetSelectColAttribute(std::vector<STRU_DBCOL>& vecColAttri)
{
	int numcols = dbnumcols(m_pFreetds->dbprocess);
	if (numcols <= 0)
	{
		return FALSE;
	}
	// 查看命令执行的结果。
	for (int i = 1; i <= numcols; i++)
	{		
		STRU_DBCOL tdbcol;
		std::string strColName(dbcolname(m_pFreetds->dbprocess, i));
		tdbcol.columnName = strColName;
		tdbcol.columnType = dbcoltype(m_pFreetds->dbprocess, i);
		tdbcol.colid = i;
		vecColAttri.push_back(tdbcol);
		//printf("colnum = %d, colname = %s, coltype = %d\n", i, strColName.c_str(), tdbcol.columnType);
	}

	return TRUE;
}





