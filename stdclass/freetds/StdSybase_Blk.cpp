// StdSybase_Blk.cpp: implementation of the CStdSybase_Blk class.
//
//////////////////////////////////////////////////////////////////////

#include "StdSybase_Blk.h"

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////
CStdSybase_Blk::CStdSybase_Blk(DBPROCESS * _pdbproc)
:m_pdbproc(_pdbproc),
m_nCounter(0)
{

}

CStdSybase_Blk::~CStdSybase_Blk(void)
{
	Drop();
	m_pdbproc = FAIL;
}

bool CStdSybase_Blk::Initilize(const char* const pTableName, const char* const pFile, int Dir)
{
	if (FAIL == m_pdbproc)
	{
		return false;
	}

	/**
	RETCODE bcp_init(DBPROCESS * dbproc, 
					const char *tblname, 
					const char *hfile, 
					const char *errfile, 
					int direction)
	*/
	if (bcp_init(m_pdbproc, pTableName, pFile, NULL, Dir) == FAIL)
	{
		printf("BulkCopyIn: blk_init() failed\n");
		return false;
	}

	return true;
}

bool CStdSybase_Blk::Bind(const int nIndex, const int nType, char * const pData, char * const pTerm, int nTmLen)
{
	/**
	RETCODE bcp_bind(DBPROCESS * dbproc, 
					BYTE * varaddr, 
					int prefixlen, 
					DBINT varlen,    //0:NULL  -1:固定长度数据类型
					BYTE * terminator, 
					int termlen, 
					int type,
				    int table_column);
	*/
	if (strlen(pData) == 0)
	{
		if (bcp_bind(m_pdbproc, (BYTE*)pData, 0, (DBINT)0, NULL, 0, nType, nIndex) == FAIL)
		{
			printf("BulkCopyIn: blk_bind failed\n");
			return false;
		}
	}
	else
	{
		if (bcp_bind(m_pdbproc, (BYTE*)pData, 0, (DBINT)-1, (BYTE*)pTerm, nTmLen, nType, nIndex) == FAIL)
		{
			printf("BulkCopyIn: blk_bind failed\n");
			return false;
		}
	}

	return true;

}

bool CStdSybase_Blk::DirBind(BYTE *const varaddr, int prefixlen, long varlen, BYTE *const  terminator, int termlen, int type, int table_column)
{
	if (bcp_bind(m_pdbproc, varaddr, prefixlen, varlen, terminator, termlen, type, table_column) == FAIL)
	{
		char szlog[64];
		sprintf(szlog, "BulkCopyIn: blk_bind %d failed", table_column);
		printf("%s\n", szlog);
		return false;
	}

	return true;
}

bool CStdSybase_Blk::Drop(void)
{
	return true;
}

bool CStdSybase_Blk::Done(const INT &nType)
{
	if (nType == 1)
	{
		if ((m_nCounter = bcp_batch(m_pdbproc)) < 0)
		{
			printf("BulkCopyIn: bcp_batch() failed\n");
			return false;
		}
	}
	else
	{
		if ((m_nCounter = bcp_done(m_pdbproc)) < 0)
		{
			printf("BulkCopyIn: bcp_done() failed\n");
			return false;
		}
	}
	
	return true;
}

bool CStdSybase_Blk::Rowxfer(void)
{
	if (bcp_sendrow(m_pdbproc) == FAIL)
	{
		printf("BulkCopyIn: bcp_sendrow() failed\n"); 
		return false;
	}

	return true;
}

bool CStdSybase_Blk::InitCol(int nCount)
{
	if (bcp_columns(m_pdbproc, nCount) == FAIL)
	{
		printf("bcp_columns failed.\n");

		return false;
	}

	char * szTm = (char*)"\t";
	
	for (int i = 1; i < nCount; i++)
	{
		// Set the file format. 
		if (bcp_colfmt(m_pdbproc, i, SYBCHAR, 0, (DBINT)-1, (BYTE*)szTm, 1, i) == FAIL)
		{
			printf("bcp_colformat failed.\n");

			return false;
		}
	}

	char * szTm1 = (char*)"\n";
	if (bcp_colfmt(m_pdbproc, nCount, SYBCHAR, 0, (DBINT)-1, (BYTE*)szTm1, 1, nCount) == FAIL)
	{
		printf("bcp_colformat failed.\n");

		return false;
	}

	return true;
}

bool CStdSybase_Blk::DoBcp(void)
{
	if (bcp_exec(m_pdbproc, NULL) == FAIL)
	{
		printf("Incomplete bulk copy. Only partial copied or complete failure occurs.\n");

		return false;
	}

	return true;
}

void CStdSybase_Blk::SetConn(DBPROCESS * _pdbproc)
{
	m_pdbproc = _pdbproc;
}


