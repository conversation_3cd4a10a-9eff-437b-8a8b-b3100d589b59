// StdSybase_Blk.h: interface for the CStdSybase_Blk class.
//
//////////////////////////////////////////////////////////////////////

#ifndef __STDSYBASE_BLK_H__
#define __STDSYBASE_BLK_H__

#include "./StdHeader.h"
#include <sybfront.h> //freetds头文件
#include <sybdb.h> //freetds


class CStdSybase_Blk  
{
public:
	CStdSybase_Blk(DBPROCESS * _pdbproc);

	~CStdSybase_Blk(void);

	bool Initilize(const char* const pTableName, const char* const pFile = NULL, int Dir = DB_IN);

	bool Bind(const int nIndex, const int nType, char * const pData, char * const pTerm, int nTmLen);

	bool DirBind(BYTE *const varaddr, int prefixlen, long varlen, BYTE *const  terminator, int termlen, int type, int table_column);

	bool Drop(void);

	bool Done(const INT &nType);

	bool Rowxfer(void);

	bool InitCol(int nCount);

	bool DoBcp(void);

	void SetConn(DBPROCESS * _pdbproc);
	
private:
	DBPROCESS * m_pdbproc;

	INT m_nCounter;

};



#endif 

