﻿#include "StdLzoFile.h"

CStdLzoFile_r::CStdLzoFile_r(void)
:m_strFileName(""), m_hFile(NULL), lzo_file_reader(NULL)
{

}

CStdLzoFile_r::~CStdLzoFile_r(void)
{
	Close();
}

bool CStdLzoFile_r::Open(const char* const pstrFileName)
{
	if (strcmp(pstrFileName, "") == 0)
	{
		return false;
	}
	
	m_strFileName.assign(pstrFileName);
	
	m_hFile = fopen(pstrFileName, "rb");
	
	if (NULL == m_hFile)
	{
		return false;
	}

	lzo_file_reader = new LZOFileReader;

	if (lzo_file_reader->Init(m_hFile) < 0)
	{
		Close();
	}

	return true;
}

void CStdLzoFile_r::Close(void)
{
	if (lzo_file_reader != NULL)
	{
		try
		{
			lzo_file_reader->Close();
		}
		catch (...)
		{
		}
		
		delete lzo_file_reader;
		lzo_file_reader = NULL;
		
		m_hFile = NULL;
	}
	
	try
	{
		m_strFileName.assign("");
	}
	catch (...)
	{
	}
}

long CStdLzoFile_r::Seek(const long lOff, const unsigned int nFrom)
{
	assert(m_hFile != NULL);
	assert(nFrom == beginL || nFrom == endL || nFrom == currentL);
	assert(lzo_file_reader != NULL);
	
	if (m_hFile == NULL)
	{
		return -2;
	}
	
	return fseek(*lzo_file_reader, lOff, nFrom);
}

int CStdLzoFile_r::Read(void* const lpBuf, const unsigned int nCount)
{
	return lzo_file_reader->Read(lpBuf, 1, nCount);
}

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CStdLzoFile_w::CStdLzoFile_w(void)
:m_strFileName(""), m_hFile(NULL), lzo_file_writer(NULL)
{
	
}

CStdLzoFile_w::~CStdLzoFile_w(void)
{
	Close();
}

void CStdLzoFile_w::Close(void)
{
	if (lzo_file_writer != NULL)
	{
		try
		{
			lzo_file_writer->Close();
		}
		catch (...)
		{
		}
		
		delete lzo_file_writer;
		lzo_file_writer = NULL;
		
		m_hFile = NULL;
	}
	
	try
	{
		m_strFileName.assign("");
	}
	catch (...)
	{
	}	
}

bool CStdLzoFile_w::Open(const char* const pstrFileName, bool bZip, const char* const strOpenFlags)
{
	if (strcmp(pstrFileName, "") == 0)
	{
		return false;
	}
	
	m_strFileName.assign(pstrFileName);
	
	m_hFile = fopen(pstrFileName, strOpenFlags);
	
	if (NULL == m_hFile)
	{
		return false;
	}
	
	lzo_file_writer = new LZOFileWriter;
	
	if (lzo_file_writer->Init(m_hFile, BLOCKSIZE, bZip ? COMPRESS_LEVEL_FAST : COMPRESS_LEVEL_NO_COMPRESS) < 0)
	{
		Close();
	}
	
	return true;
}

int CStdLzoFile_w::WriteHuge(const void* const lpBuf, const unsigned int nLen, const unsigned int nCount)
{
	assert(m_hFile != NULL);
	
	if ((nCount == 0) || (m_hFile == NULL) || (lpBuf == NULL))
	{
		return 0; // nCount 最大 8192
	}
	else
	{
		if (lzo_file_writer->Write(lpBuf, nLen, nCount) == int(nCount))
		{
			return int(nCount);
		}

		return 0;
	}
}

int CStdLzoFile_w::SuperWriteHuge(const void* const lpBuf, const unsigned int nLen)
{
	assert(m_hFile != NULL);
	
	if ((nLen == 0) || (m_hFile == NULL) || (lpBuf == NULL))
	{
		return 0; // nLen 鏈€澶?8192
	}
	
	if (nLen <= 8192)
	{
		if (lzo_file_writer->Write(lpBuf, nLen, 1) == 1)
		{
			return nLen;
		}
		
		return 0;
	}
	else
	{
		int nCount = 0;
		int nTotalCount = 0;
		int nNum = nLen / 8192;
		BYTE* pData = (BYTE*)lpBuf;
		
		int i = 0;
		for (i = 0; i < nNum; i++)
		{
			nCount = lzo_file_writer->Write(pData + i * 8192, 1, 8192);
			
			if (nCount != 8192)
			{
				break;
			}

			nTotalCount += nCount;
		}
		
		if (i == nNum)
		{
			nCount = nLen % 8192;
			
			nTotalCount += lzo_file_writer->Write(pData + i * 8192, 1, nCount);
		}
		
		return nTotalCount;
	}	
}




