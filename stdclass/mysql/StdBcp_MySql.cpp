// StdBcp_MySql.cpp: implementation of the CStdBcp_MySql class.
//
//////////////////////////////////////////////////////////////////////

#include "./StdBcp_MySql.h"

const int MAX_RECORDLEN = 8192 * 3;
const char TOKEN[] = "\t,";

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////
CStdBcp_MySql::CStdBcp_MySql(CStdLog* const pLog)
:m_pstrRec(NULL),
m_Token(NULL),
m_pLog(pLog)
{
	m_pstrRec = new char[MAX_RECORDLEN];

	memset(m_pstrRec, 0, MAX_RECORDLEN);
}

CStdBcp_MySql::~CStdBcp_MySql(void)
{
	m_Token = NULL;
	if (m_pstrRec != NULL)
	{
		delete []m_pstrRec;
		m_pstrRec = NULL;
	}

	m_pLog = NULL;
}

bool CStdBcp_MySql::BcpData(MYSQL *_pmysql, const char* const pFile, const char* const pTable)
{
	if ((_pmysql == nullptr) || (pFile == nullptr) || (pTable == nullptr))
	{
		return false;
	}
	// 记录日志
	char szLog[MAX_PATH] = "";

	// 打开文件
	CStdFile bcpfile;

	if (!bcpfile.Open(pFile, "rt"))
	{
		sprintf(szLog, "bcp open file %s failed.", pFile);
		OUTPUT_LOG(*m_pLog, szLog);

		return false;
	}

	// 初始化批拷贝
	CStdMySql_Blk MySqlBlk(_pmysql);

	if (!MySqlBlk.Initilize(pTable))
	{
		bcpfile.Close();

		sprintf(szLog, "bcpblk init table %s failed.", pTable);
		OUTPUT_LOG(*m_pLog, szLog);

		return false;
	}

	int nIndex = 0; // 字段位置
	int nRow = 0;

	while(bcpfile.GetStr(m_pstrRec, MAX_RECORDLEN) != NULL)
	{
		nRow++;
		
		if ((strlen(m_pstrRec) <= 2) || ((m_pstrRec[strlen(m_pstrRec) - 1] != 0x0A) && (m_pstrRec[strlen(m_pstrRec) - 1] != 0x0D)))
		{
			sprintf(szLog, "bcp row[%d] field[%d] of table[%s] from file[%s] 缺少回车换行符!", nRow, nIndex, pTable, pFile);
			OUTPUT_LOG(*m_pLog, szLog);

			continue;
		}
		
		// 去掉字符串末尾的 回车换行符
		TrimTailEnter(m_pstrRec);

		if (!BlkData(MySqlBlk, nIndex))
		{
			sprintf(szLog, "bcpblk bind row[%d] field[%d] of table[%s] from file[%s] failed.", nRow, nIndex, pTable, pFile);
			OUTPUT_LOG(*m_pLog, szLog);

			continue;
		}

		memset(m_pstrRec, 0, MAX_RECORDLEN);
	}

	bcpfile.Close();

	return true;
}

void CStdBcp_MySql::SplitRowData(int &nIndex, std::vector<std::string> &vecfields)
{
	nIndex = 0;

	size_t slen = strlen(m_pstrRec);
	char* sptr = m_pstrRec;
	char* eptr = m_pstrRec;

	for (size_t i = 0; i < slen; i++)
	{
		if (strchr(TOKEN, eptr[0]) != NULL)
		{
			++nIndex;
			eptr[0] = '\0';
			// 去掉字段内容前后两端的空格
			TrimSpace(&sptr);

			vecfields.push_back(std::string(sptr));

			eptr++;
			sptr = eptr;
		}
		else
		{
			eptr++;
		}
	}

	++nIndex;
	if ((eptr - m_pstrRec) < MAX_RECORDLEN)
	{
		eptr[0] = '\0';
	}
	else
	{
		eptr--;
		eptr[0] = '\0';
	}
	// 去掉字段内容前后两端的空格
	TrimSpace(&sptr);

	vecfields.push_back(std::string(sptr));

	sptr = nullptr;
	eptr = nullptr;
}

bool CStdBcp_MySql::BlkData(CStdMySql_Blk &MySqlBlk, int &nIndex)
{
	std::vector<std::string> vecfileds;
	vecfileds.clear();
	SplitRowData(nIndex, vecfileds);

	char szLog[MAX_PATH] = "";

	if (!MySqlBlk.IsColNumEqual(vecfileds.size()))
	{
		memset(szLog, 0, sizeof(szLog));
		sprintf(szLog, "bcpblk column num not equal fileds num...");
		OUTPUT_LOG(*m_pLog, szLog);
		return false;
	}

	// 绑定字段
	for (size_t i = 0; i < vecfileds.size(); i++)
	{
		bool bret = MySqlBlk.Bind(i, vecfileds[i]);
		if (!bret)
		{
			memset(szLog, 0, sizeof(szLog));
			sprintf(szLog, "bcpblk bind column error...");
			OUTPUT_LOG(*m_pLog, szLog);
			return false;
		}
	}
	
	if (!MySqlBlk.Done())
	{
		return false;
	}
	
	return true;
}

void CStdBcp_MySql::TrimTailEnter(char* cptr)
{
	// 去除数据内容后端的回车换行符
		size_t len = strlen(cptr);
		while(cptr[len-1] == '\r' || cptr[len-1] == '\n')
		{
			cptr[len-1] = '\0';
			len = strlen(cptr);
		}
}

void CStdBcp_MySql::TrimSpace(char** sptr)
{
		if (sptr == nullptr)
		{
				return;
		}
	
		// 去除字段内容前端的空格
		while((*sptr)[0] == ' ')
		{
			(*sptr)++;
		}

		// 去除字段内容后端的空格
		size_t sptrlen = strlen((*sptr));
		while((*sptr)[sptrlen-1] == ' ')
		{
			(*sptr)[sptrlen-1] = '\0';
			sptrlen = strlen((*sptr));
		}
}
