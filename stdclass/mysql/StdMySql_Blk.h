// StdMySql_Blk.h: interface for the CStdMySql_Blk class.
//
//////////////////////////////////////////////////////////////////////

#ifndef __STDMYSQL_BLK_H__
#define __STDMYSQL_BLK_H__

#include "./StdHeader.h"
#include <mysql/mysql.h>

class CStdMySql_Blk  
{
public:
	CStdMySql_Blk(MYSQL * _pmysql);

	~CStdMySql_Blk(void);

	bool Initilize(const char* const pTableName, const char* const pFile = NULL);

	bool Bind(const int nIndex, std::string strValue);

	bool Done(void);

	bool GetSqlColumnType(const char* const pTableName, std::vector<int> &coltypevec);

	bool IsColNumEqual(size_t colnum);

	int HexStrToByteWithoutMidBlank(char* const pStr, BYTE* const pBinary, BYTE btype);
	
private:
	MYSQL * m_pmysql;
	MYSQL_BIND *m_mysqlbind;
	MYSQL_STMT *m_pstmt;

	std::vector<int> m_vecColType;

	INT m_nCounter;

};



#endif 

