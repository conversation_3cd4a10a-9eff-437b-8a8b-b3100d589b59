// SqlOpHelper.h: interface for the SqlOpHelper class.
//  * String utility definitions
//////////////////////////////////////////////////////////////////////

#ifndef __SQLOPHELPER_H__
#define __SQLOPHELPER_H__

#include "StdMySql.h"
#include "StdDataObject.h"

#define MAX_DBDATA_BUFFERLENTH 8192 * 2

struct STRU_DBData
{
	std::string name;
	int sqlType;
	BYTE* pBuffer;
	int bufferLenth;
	int dataType;
	int bindType;
	long dataLenth;
	int ColId;

	STRU_DBData(std::string _name, int _sqlType, int id)
	{
		name = _name;
		sqlType = _sqlType;
		ColId = id;
	}
	~STRU_DBData()
	{
		delete pBuffer;
	}

	void clear()
	{
		if (nullptr == pBuffer)
		{
			return;
		}

		if (dataType == CDataObject::E_DataType_Text)
		{
			memset(pBuffer, 0, MAX_DBDATA_BUFFERLENTH);
		}
		else
		{
			memset(pBuffer, 0, bufferLenth);
		}
	}
};

class CStdMySql_Connection;

class CStdSqlOpHelper
{
public:
	enum E_DataStatus
	{
		E_DataStatus_Error = -10,
		E_DataStatus_ReadOver = 0,
		E_DataStatus_ReadAble = 1
	};

	CStdSqlOpHelper(STRU_DBConnInfo* pConnInfo);
	~CStdSqlOpHelper(void);

	size_t GetColumnCount(void);
	STRU_DBData* GetColStruct(size_t i);
	BOOL ExecuteNonQuery(const char* const pszSql);
	int ExecuteReader(const char* const strSql);
	BOOL Init();
	BOOL Read(void);

	BOOL PushToBufferWihtNetSeq(BYTE* const pTarBuffer, int& ioffset, const int nCount, size_t sourStruIndex, int& sourBufferLenth);
	BOOL PushToBufferWihtNetSeq(BYTE* const pTarBuffer, int& ioffset, const int dataType, const int nCount, size_t sourStruIndex, int& sourBufferLenth);

private:
	void ClearData(void);
	BOOL SqlType2DataObject(STRU_DBData* pSTRU_DBData);
	BOOL CreateBindBuffer(STRU_DBData* pSTRU_DBData);
	BOOL subRead(void);

public:
	BYTE* operator [](size_t i)
	{
		if (m_DataStatus != E_DataStatus_ReadAble)
		{
			return nullptr;
		}
		if (i < m_vecDBDataByID.size())
		{
			return m_vecDBDataByID[i]->pBuffer;
		}
		return nullptr;
	}
	BYTE* operator [](std::string columnName)
	{
		if (m_DataStatus != E_DataStatus_ReadAble)
		{
			return nullptr;
		}
		if (m_mapDBDataByName.find(columnName) != m_mapDBDataByName.end())
		{
			return m_mapDBDataByName[columnName]->pBuffer;
		}
		return nullptr;
	}
	///////////////////////////////////////////////////////////////////
	char GetChar(std::string columnName)
	{
		return *(char*)(operator[](columnName));
	}
	int GetSmallInt(std::string columnName)
	{
		return *(WORD*)(operator[](columnName));
	}
	int GetInt(std::string columnName)
	{
		return *(int*)(operator[](columnName));
	}
	float GetFloat(std::string columnName)
	{
		return *(float*)(operator[](columnName));
	}
	double GetDouble(std::string columnName)
	{
		return *(double*)(operator[](columnName));
	}
	std::string GetStr(std::string columnName)
	{
		return std::string((char*)(operator[](columnName)));
	}
	std::string GetTime(std::string columnName)
	{
		// BYTE *p = operator[](columnName);
		// char r[32];
		// snprintf(r, sizeof(r), "%04d/%02d/%02d %02d:%02d:%02d.%03d",
		// 		*(WORD*)p, *(WORD*)(p+2), *(WORD*)(p+4), *(WORD*)(p+6), 
		// 		*(WORD*)(p+8), *(WORD*)(p+10), *(UINT*)(p+12) / 1000000);
		return std::string((char*)(operator[](columnName)));
	}
	BYTE* GetByte(std::string columnName)
	{
		return operator[](columnName);
	}
	///////////////////////////////////////////////////////////////////
	char GetChar(size_t index)
	{
		return *(char*)(operator[](index));
	}
	int GetSmallInt(size_t index)
	{
		return *(WORD*)(operator[](index));
	}
	int GetInt(size_t index)
	{
		return *(int*)(operator[](index));
	}
	float GetFloat(size_t index)
	{
		return *(float*)(operator[](index));
	}
	double GetDouble(size_t index)
	{
		return *(double*)(operator[](index));
	}
	LONGLONG GetInt64(size_t index)
	{
		return *(INT64*)(operator[](index));
	}
	std::string GetStr(size_t index)
	{
		return std::string((char*)(operator[](index)));
	}
	std::string GetTime(size_t index)
	{
		// BYTE *p = operator[](index);
		// char r[32];
		// snprintf(r, sizeof(r), "%04d/%02d/%02d %02d:%02d:%02d.%03d",
		// 		*(WORD*)p, *(WORD*)(p+2), *(WORD*)(p+4), *(WORD*)(p+6), 
		// 		*(WORD*)(p+8), *(WORD*)(p+10), *(UINT*)(p+12) / 1000000);
		return std::string((char*)(operator[](index)));
	}
	BYTE GetByte(size_t index)
	{
		return *operator[](index);
	}
  	long GetDataLen(size_t index)
	{
		if (m_DataStatus != E_DataStatus_ReadAble)
		{
				return -1;
		}

		return SqlDataLen(index);
	}
	long GetDataLen(std::string columnName)
	{
		if (m_DataStatus != E_DataStatus_ReadAble)
		{
			return -1;
		}
		if (m_mapDBDataByName.find(columnName) != m_mapDBDataByName.end())
		{

			return SqlDataLen(m_mapDBDataByName[columnName]->ColId);
		}

		return -1;
	}
	int SqlDataLen(size_t index)
	{
		if (m_DataStatus != E_DataStatus_ReadAble)
		{
			return -1;
		}
	
		return m_DbConn->Datalen(index);
	}
	int GetDataType(std::string columnName)
	{
		if (m_DataStatus != E_DataStatus_ReadAble)
		{
			return -1;
		}
		if (m_mapDBDataByName.find(columnName) != m_mapDBDataByName.end())
		{
			return m_mapDBDataByName[columnName]->dataType;
		}

		return -1;
	}
	int UpdateBuffer(std::string columnName, BYTE* newdata, size_t newlen)
	{
		if (m_DataStatus != E_DataStatus_ReadAble)
		{
			return -1;
		}

		if (m_mapDBDataByName.find(columnName) != m_mapDBDataByName.end())
		{
			m_mapDBDataByName[columnName]->clear();
			memcpy(m_mapDBDataByName[columnName]->pBuffer, newdata, newlen);
			m_mapDBDataByName[columnName]->dataLenth = newlen + 1;
			return 0;
		}

		return -1;
	}
	long GetUpdateBufferDataLen(std::string columnName)
	{
		if (m_DataStatus != E_DataStatus_ReadAble)
		{
			return -1;
		}
		if (m_mapDBDataByName.find(columnName) != m_mapDBDataByName.end())
		{
			return m_mapDBDataByName[columnName]->dataLenth;
		}

		return -1;
	}

private:
	CStdMySql_Connection* m_DbConn; 

	std::vector<STRU_DBCOL> m_vecDBCol;

	STRU_DBConnInfo m_ConnInfo;
	std::map<std::string, STRU_DBData*> m_mapDBDataByName;
  	std::vector<STRU_DBData*> m_vecDBDataByID;
	int m_DataStatus;

	CStdLog m_SqlLog;
};

#endif 