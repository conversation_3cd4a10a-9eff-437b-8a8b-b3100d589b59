
#include "StdCycBuffer.h"

CStdCycBuffer::CStdCycBuffer(void)
:m_pBuffer(NULL), m_nBufSize(BUF_SIZE), m_Lock(), m_pRdPos(NULL), m_pWtPos(NULL), m_bFlag(false)
{
	Initialize();
}

CStdCycBuffer::CStdCycBuffer(const int nBufSize)
:m_pBuffer(NULL), m_nBufSize(nBufSize), m_Lock(), m_pRdPos(NULL), m_pWtPos(NULL), m_bFlag(false)
{
	Initialize();
}

CStdCycBuffer::~CStdCycBuffer(void)
{
	m_pRdPos = NULL;
	m_pWtPos = NULL;
	if (NULL != m_pBuffer)
	{
		delete []m_pBuffer;
		m_pBuffer = NULL;
	}
}

void CStdCycBuffer::Initialize(void)
{
	if (m_nBufSize < 1024*1024)
	{
		printf("CStdCycBuffer::initialize:m_nBufSize is too small, should greater than 1M!");
	}

	m_pBuffer = new BYTE[m_nBufSize];
	if (NULL == m_pBuffer)
	{
		printf("CStdCycBuffer::initialize m_pBuffer=NULL!");
		return;
	}

	m_pRdPos = m_pBuffer;
	m_pWtPos = m_pBuffer;
	m_bFlag = false;
	m_nFreeSize = m_nBufSize;
}

int CStdCycBuffer::Read(BYTE * const pBuf, const int nCount)
{
	assert(m_pBuffer != NULL);
	assert(m_pRdPos != NULL);
	assert(m_pWtPos != NULL);
	if ((pBuf == NULL) || 
		(nCount > m_nBufSize) ||
		(m_pBuffer == NULL) ||
		(m_pRdPos == NULL) ||
		(m_pWtPos == NULL))
	{
		return 0;
	}

	int nCanReadSize = 0;
	int nRtValue = 0;

	m_Lock.Lock();

	// |******W------R******|
	if (m_pRdPos >= m_pWtPos)
	{
		if ((m_pWtPos == m_pRdPos) && (!m_bFlag))
		{
			nRtValue = 0;
		}
		else
		{
			nCanReadSize = m_nBufSize - (int)(m_pRdPos - m_pWtPos);
			if (nCanReadSize < nCount)
			{
				nRtValue = 0;
			}
			else
			{
				nCanReadSize = m_nBufSize - (int)(m_pRdPos - m_pBuffer);
				if (nCanReadSize >= nCount)
				{
					memcpy(pBuf, m_pRdPos, nCount);
					if (nCanReadSize == nCount)
					{
						m_pRdPos = m_pBuffer;
						m_bFlag = false;
					}
					else
					{
						m_pRdPos += nCount;
					}
				}
				else
				{
					memcpy(pBuf, m_pRdPos, nCanReadSize);
					m_pRdPos = m_pBuffer;
					m_bFlag = false;
					memcpy(pBuf + nCanReadSize, m_pRdPos, nCount - nCanReadSize);
					m_pRdPos += nCount - nCanReadSize;
				}
				nRtValue = nCount;
			}
		}
	}
	// |------R******W------|
	else // m_pRdPos < m_pWtPos
	{
		nCanReadSize = (int)(m_pWtPos - m_pRdPos);
		if (nCanReadSize < nCount)
		{
			nRtValue = 0;
		}
		else
		{
			memcpy(pBuf, m_pRdPos, nCount);
			m_pRdPos += nCount;
			nRtValue = nCount;
		}
	}

	m_nFreeSize += nRtValue;
	m_Lock.Unlock();
	return nRtValue;
}

int CStdCycBuffer::Write(const BYTE * const pBuf, const int nCount)
{
	assert(m_pBuffer != NULL);
	assert(m_pRdPos != NULL);
	assert(m_pWtPos != NULL);

	if ((pBuf == NULL) || 
		(nCount > m_nBufSize) ||
		(m_pBuffer == NULL) ||
		(m_pRdPos == NULL) ||
		(m_pWtPos == NULL))
	{
		return 0;
	}

	int nCanWriteSize = 0;
	int nRtValue = 0;
	
 	m_Lock.Lock();
	
	// |------R********W------|
	if (m_pWtPos >= m_pRdPos)
	{
		if ((m_pWtPos == m_pRdPos) && m_bFlag)
		{
			nRtValue = 0;
		}
		else
		{
			nCanWriteSize = m_nBufSize - (int)(m_pWtPos - m_pRdPos);

			if (nCanWriteSize < nCount)
			{
				nRtValue = 0;
			}
			else
			{
				nCanWriteSize = m_nBufSize - (int)(m_pWtPos - m_pBuffer);

				if (nCanWriteSize >= nCount) 
				{
					memcpy(m_pWtPos, pBuf, nCount);
					if (nCanWriteSize == nCount)
					{
						m_pWtPos = m_pBuffer;
						m_bFlag = true;
					}
					else
					{
						m_pWtPos += nCount;
					}
				}
				else
				{
					memcpy(m_pWtPos, pBuf, nCanWriteSize);
					m_pWtPos = m_pBuffer;
					m_bFlag = true;
					memcpy(m_pWtPos, pBuf + nCanWriteSize, nCount - nCanWriteSize);
					m_pWtPos += nCount - nCanWriteSize;
				}
				nRtValue = nCount;
			}
		}
	}
	// |******W--------R******|
	else // m_pWtPos < m_pRdPos
	{
		nCanWriteSize = (int)(m_pRdPos - m_pWtPos);

		if (nCanWriteSize < nCount)
		{
			nRtValue = 0;
		}
		else
		{
			memcpy(m_pWtPos, pBuf, nCount);
			m_pWtPos += nCount;
			nRtValue = nCount;
		}
	}

	m_nFreeSize -= nRtValue;
	m_Lock.Unlock();
	return nRtValue;
}

int CStdCycBuffer::GetSize(void)
{
	assert(m_pBuffer != NULL);
	assert(m_pRdPos != NULL);
	assert(m_pWtPos != NULL);

	if ((m_pBuffer == NULL) ||
		(m_pRdPos == NULL) ||
		(m_pWtPos == NULL))
	{
		return 0;
	}

	int Size = 0;
 	m_Lock.Lock();
	if (m_pWtPos < m_pRdPos)
	{
		Size = m_nBufSize - (int)(m_pRdPos - m_pWtPos);
	}
	else if (m_pWtPos > m_pRdPos)
	{
		Size = (int)( m_pWtPos - m_pRdPos );
	}
	else if ((m_pWtPos == m_pRdPos) && m_bFlag)
	{
		Size = m_nBufSize;
	}
	else
	{
		Size = 0;
	}

	m_nFreeSize = m_nBufSize - Size;
 	m_Lock.Unlock();
	return Size;
}

// 只看数据，不移动指针
int CStdCycBuffer::Peek(BYTE * const pBuf, const int nCount)
{
	assert(m_pBuffer != NULL);
	assert(m_pRdPos != NULL);
	assert(m_pWtPos != NULL);
	if ((pBuf == NULL) || 
		(nCount > m_nBufSize) ||
		(m_pBuffer == NULL) ||
		(m_pRdPos == NULL) ||
		(m_pWtPos == NULL))
	{
		return 0;
	}

	int nCanReadSize = 0;
	int nRtValue = 0;

	m_Lock.Lock();

	// |******W------R******|
	if (m_pRdPos >= m_pWtPos)
	{
		if ((m_pWtPos == m_pRdPos) && (!m_bFlag))
		{
			nRtValue = 0;
		}
		else
		{
			nCanReadSize = m_nBufSize - (int)(m_pRdPos - m_pWtPos);
			if (nCanReadSize < nCount)
			{
				nRtValue = 0;
			}
			else
			{
				nCanReadSize = m_nBufSize - (int)(m_pRdPos - m_pBuffer);
				if (nCanReadSize >= nCount)
				{
					memcpy(pBuf, m_pRdPos, nCount);
				}
				else
				{
					memcpy(pBuf, m_pRdPos, nCanReadSize);
					memcpy(pBuf + nCanReadSize, m_pBuffer, nCount - nCanReadSize);
				}
				nRtValue = nCount;
			}
		}
	}
	// |------R******W------|
	else // m_pRdPos < m_pWtPos
	{
		nCanReadSize = (int)(m_pWtPos - m_pRdPos);
		if (nCanReadSize < nCount)
		{
			nRtValue = 0;
		}
		else
		{
			memcpy(pBuf, m_pRdPos, nCount);
			nRtValue = nCount;
		}
	}

	m_Lock.Unlock();
	return nRtValue;
}

void CStdCycBuffer::Reset(void)
{
 	m_Lock.Lock();
	m_pRdPos = m_pBuffer;
	m_pWtPos = m_pBuffer;
	m_bFlag = false;
	m_nFreeSize = m_nBufSize;
 	m_Lock.Unlock();
}

int CStdCycBuffer::GetFree(void)
{
	return m_nFreeSize; // m_nBufSize - GetSize();
}

int CStdCycBuffer::ReadAny(BYTE * const pBuf, const int nCount)
{
	assert(m_pBuffer != NULL);
	assert(m_pRdPos != NULL);
	assert(m_pWtPos != NULL);
	if ((pBuf == NULL) || 
		(nCount > m_nBufSize) ||
		(m_pBuffer == NULL) ||
		(m_pRdPos == NULL) ||
		(m_pWtPos == NULL))
	{
		return 0;
	}
	
	int nCanReadSize = 0;
	int nRtValue = 0;
	
	m_Lock.Lock();
	
	// |******W------R******|
	if (m_pRdPos >= m_pWtPos)
	{
		if ((m_pWtPos == m_pRdPos) && (!m_bFlag))
		{
			nRtValue = 0;
		}
		else
		{
			nCanReadSize = m_nBufSize - (int)(m_pRdPos - m_pBuffer);
			if (nCanReadSize >= nCount)
			{
				memcpy(pBuf, m_pRdPos, nCount);
				if (nCanReadSize == nCount)
				{
					m_pRdPos = m_pBuffer;
					m_bFlag = false;
				}
				else
				{
					m_pRdPos += nCount;
				}
				nRtValue = nCount;
			}
			else
			{
				memcpy(pBuf, m_pRdPos, nCanReadSize);
				m_pRdPos = m_pBuffer;
				m_bFlag = false;
				nRtValue = nCanReadSize;

				int nRes = nCount - nCanReadSize;

				if (nRes > (m_pWtPos - m_pBuffer))
				{
					nRes = (int)(m_pWtPos - m_pBuffer);
				}

				memcpy(pBuf + nCanReadSize, m_pRdPos, nRes);

				m_pRdPos += nRes;
				nRtValue += nRes;
			}
		}
	}
	// |------R******W------|
	else // m_pRdPos < m_pWtPos
	{
		nCanReadSize = (int)(m_pWtPos - m_pRdPos);
		if (nCanReadSize > nCount)
		{
			nCanReadSize = nCount;
		}

		memcpy(pBuf, m_pRdPos, nCanReadSize);
		m_pRdPos += nCanReadSize;
		nRtValue = nCanReadSize;
	}
	
	m_nFreeSize += nRtValue;
	m_Lock.Unlock();
	return nRtValue;
}