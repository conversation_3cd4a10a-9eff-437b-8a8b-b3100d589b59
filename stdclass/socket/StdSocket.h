// StdSocket.h: interface for the CStdSocket class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_STDSOCKET_H__2D5BDEA5_CB12_4C05_A9D2_1C926772C81A__INCLUDED_)
#define AFX_STDSOCKET_H__2D5BDEA5_CB12_4C05_A9D2_1C926772C81A__INCLUDED_

#pragma once

#include "../StdHeader.h"
#include "../StdMonitor.h"

typedef struct STRU_SOCKFD_INFO
{
	char szIp[32]; // 客户端IP
	char szSvrIp[32]; // 服务端IP
	unsigned short cltPort; // 客户端端口
	unsigned short svrPort; // 服务端端口
}tSTRU_SOCKFD_INFO;

class CStdSocket  
{
public:
	CStdSocket(void);
	~CStdSocket(void);

	// 使用socket前必须先init
	static bool SocketEnvInit(void); 
	static void SocketEnvTerm(void);
	static bool m_bInited;
	static UINT64 uCounter;

	static UINT64 cStatus[10];

	bool Create(const int nSocketType = SOCK_STREAM);
 	bool Bind(const unsigned short nSocketPort, const char* const lpszSocketAddress = NULL);
 	bool Listen(void);
	bool Accept(CStdSocket& rConnectedSocket);
	SOCKET  Accept(void);
	bool Connect(const char* const lpszHostAddress, const unsigned short nHostPort);

	int Select(const int nMilliSecond = 2000);// 毫秒
	int rSelect(const int nMilliSecond = 2000);// 毫秒
	int wSelect(const int nMilliSecond = 100); // 毫秒
	int Receive(void* const lpBuf, const int nBufLen, const int nFlags = 0);
	int Send(const char* const lpBuf, const int nBufLen);
	int ReceiveFrom(void* const lpBuf, const int nBufLen, unsigned short& rSocketPort, char* const rSocketAddress);
	int SendTo(const void* const lpBuf, const int nBufLen, const unsigned short rSocketPort, const char* const rSocketAddress = NULL);
	void Close(void);

	void Attach(const SOCKET hSocket);
	bool GetPeerName(char* const rPeerAddress, unsigned short& rPeerPort);
	static bool GetPeerName(const SOCKET hSocket, char* const rPeerAddress, unsigned short& rPeerPort);
	bool SetSockOpt(const int nOptionName, const void* const lpOptionValue,
		const int nOptionLen, const int nLevel = SOL_SOCKET);
	bool GetSockName(char* const pCltAddress, unsigned short& wCltPort);

	static void Terminate(SOCKET hSocket);

	static CStdLog m_socket_actor_log;

private:
	SOCKET m_hSocket;
};

inline void CStdSocket::SocketEnvTerm(void)
{
	m_bInited = false;
}

inline bool CStdSocket::Listen(void)
{
	assert(m_hSocket != INVALID_SOCKET);

	return (listen(m_hSocket, 50) != -1);
}

inline void CStdSocket::Attach(const SOCKET hSocket)
{
	assert(hSocket != INVALID_SOCKET);
	m_hSocket = hSocket;
}

inline bool CStdSocket::SetSockOpt(const int nOptionName, const void* const lpOptionValue, const int nOptionLen, const int nLevel /* = SOL_SOCKET */)
{
	return (-1 != setsockopt(m_hSocket, nLevel, nOptionName, (const char *)lpOptionValue, nOptionLen));
}

inline int CStdSocket::Receive(void* const lpBuf, const int nBufLen, const int nFlags)
{
	return recv(m_hSocket, (char*)lpBuf, nBufLen, nFlags);
}

inline int CStdSocket::Send(const char* const lpBuf, const int nBufLen)
{
	return send(m_hSocket, lpBuf, nBufLen, 0);
}



#endif // !defined(AFX_STDSOCKET_H__2D5BDEA5_CB12_4C05_A9D2_1C926772C81A__INCLUDED_)
