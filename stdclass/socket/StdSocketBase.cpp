// StdSocketBase.cpp: implementation of the StdSocketBase class.
//
//////////////////////////////////////////////////////////////////////

#include "StdSocketBase.h"

/////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CStdServerBase::CStdServerBase(CStdActor* const pStdActor)
:m_pSvrSock(NULL), m_nPort(0), m_pStdActor(pStdActor)
{

}

CStdServerBase::~CStdServerBase(void)
{
	CloseServer();
}

bool CStdServerBase::OpenServer(const unsigned short nPort, const int nType)
{
	if (nPort == 0)
	{
		OUTPUT_LOG(CStdSocket::m_socket_actor_log, "server port is 0, must be reset.");
		return false;
	}

	m_nPort = nPort;

	m_pSvrSock = new CStdSocket();

	if (m_pSvrSock == NULL)
	{
		OUTPUT_LOG(CStdSocket::m_socket_actor_log, "server socket create failed.");
		return false;
	}

	if (m_pSvrSock->Create(nType))
	{
		int OptionValue = 1;

		if (!m_pSvrSock->SetSockOpt(SO_REUSEADDR, &OptionValue, sizeof(OptionValue), SOL_SOCKET))
		{
			OUTPUT_LOG(CStdSocket::m_socket_actor_log, "server setsockopt SO_REUSEADDR failed.");
		}
#ifdef SO_REUSEPORT
		OptionValue = 1;
		if (!m_pSvrSock->SetSockOpt(SO_REUSEPORT, &OptionValue, sizeof(OptionValue), SOL_SOCKET))
		{
			OUTPUT_LOG(CStdSocket::m_socket_actor_log, "server setsockopt SO_REUSEPORT failed.");
		}
#endif
	}
	else
	{
		OUTPUT_LOG(CStdSocket::m_socket_actor_log, "Create server failed.");
		return false;
	}

	if (!m_pSvrSock->Bind(nPort))
	{
		m_pSvrSock->Close();

		OUTPUT_LOG(CStdSocket::m_socket_actor_log, "server bind port failed.");

		return false;
	}

	char szLog[64] = "";
	if (nType == (CStdServerBase::E_TCP))
	{
		if (!m_pSvrSock->Listen())
		{
			m_pSvrSock->Close();

			OUTPUT_LOG(CStdSocket::m_socket_actor_log, "server listen failed.");

			return false;
		}

		sprintf(szLog, "tcp server is listenning at %d.", nPort);
	}
	else
	{
		sprintf(szLog, "udp server is listenning at %d.", nPort);
	}

	OUTPUT_LOG(CStdSocket::m_socket_actor_log, szLog);

	if (!CreateThread(0))
	{ 
		m_pSvrSock->Close();
		OUTPUT_LOG(CStdSocket::m_socket_actor_log, "server thread creates unsuccessfully.");
		return false;
	}

	OUTPUT_LOG(CStdSocket::m_socket_actor_log, "server thread creates successfully.");
	
	return true;
}

void CStdServerBase::CloseServer(void)
{
	CloseThread(10000);

	if (NULL != m_pSvrSock)
	{
		m_pSvrSock->Close();
		delete m_pSvrSock;
		m_pSvrSock = NULL;
	}
	m_nPort = 0;
	m_pStdActor = NULL;
}

//////////////////////////////////////////////////////////////////////////////////////////
CStdClientBase::CStdClientBase(void)
	:m_pCltSock(NULL),
	m_nPort(0),
	m_strAddr(""),
	m_socket_actor_log("tcp_socket_actor_log.txt")
{
	m_pCltSock = new CStdSocket;
}

CStdClientBase::~CStdClientBase(void)
{
	if (NULL != m_pCltSock)
	{
		try	{ m_pCltSock->Close(); } catch (...) {	}
		try	{ delete m_pCltSock; } catch (...) {	}
		m_pCltSock = NULL;
	}
	m_nPort = 0;
	try	{ m_strAddr.assign(""); } catch (...) {	}
	
}

bool CStdClientBase::OpenClient(const char* const pAddr, const unsigned short nPort, const int nType)
{
	if ((nPort == 0) || strcmp(pAddr, "") == 0)
	{
		OUTPUT_LOG(m_socket_actor_log, "client connect port = 0 or addr is null, must be reset.");
		return false;
	}

	m_nPort = nPort;
	m_strAddr.assign(pAddr);

	if (NULL != m_pCltSock)
	{
		m_pCltSock->Close();
	}
	else
	{
		OUTPUT_LOG(m_socket_actor_log, "client socket new failed.");
		return false;
	}

	if (m_pCltSock->Create(nType))
	{
		int OptionValue = 1;

		if (!m_pCltSock->SetSockOpt(SO_REUSEADDR, &OptionValue, sizeof(OptionValue), SOL_SOCKET))
		{
			OUTPUT_LOG(m_socket_actor_log, "client setsockopt SO_REUSEADDR failed.");
		}
#ifdef SO_REUSEPORT
		OptionValue = 1;
		if (!m_pCltSock->SetSockOpt(SO_REUSEPORT, &OptionValue, sizeof(OptionValue), SOL_SOCKET))
		{
			OUTPUT_LOG(m_socket_actor_log, "client setsockopt SO_REUSEPORT failed.");
		}
#endif
		if (nType == E_TCP)
		{
			OptionValue = 1;
			if (!m_pCltSock->SetSockOpt(SO_KEEPALIVE, &OptionValue, sizeof(OptionValue), SOL_SOCKET))
			{
				OUTPUT_LOG(m_socket_actor_log, "client setsockopt SO_KEEPALIVE failed.");
			}

			linger  opt_linvalue;
			opt_linvalue.l_onoff = 0;
			opt_linvalue.l_linger = 0;
			if (!m_pCltSock->SetSockOpt(SO_LINGER, &opt_linvalue, sizeof(opt_linvalue), SOL_SOCKET))
			{
				OUTPUT_LOG(m_socket_actor_log, "client setsockopt SO_LINGER failed.");
			}
		}
	}
	else
	{
		OUTPUT_LOG(m_socket_actor_log, "client socket create failed.");
		return false;
	}

	if (nType == E_TCP)
	{
		char szLog[MAX_PATH] = "";

		if (!m_pCltSock->Connect(pAddr, nPort))
		{
			m_pCltSock->Close();

			sprintf(szLog, "client socket connect %s:%d failed", pAddr, nPort);

			OUTPUT_LOG(m_socket_actor_log, szLog);

			return false;
		}

		sprintf(szLog, "client socket connect %s:%d succ", pAddr, nPort);

		OUTPUT_LOG(m_socket_actor_log, szLog);
	}

	return true;
}








