// StdSocketBase.h: interface for the CStdSocketBase class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_STDSOCKETBASE_H__C6D8CF6D_9E86_4FE8_B436_B6F323F36220__INCLUDED_)
#define AFX_STDSOCKETBASE_H__C6D8CF6D_9E86_4FE8_B436_B6F323F36220__INCLUDED_

#pragma once

#include "../StdMonitor.h"
#include "../StdCommond.h"
#include "../StdThread.h"
#include "StdSocket.h"

/////////////////////////////////////////////////////////////
#define MAX_CONNECTION 64
#define RECV_PKG_SIZE 8192 * 8
const BYTE SOCK_Test_PKG[4] = {0xE7, 0xE7, 0, 0};

class CStdActor
{
public:
	virtual ~CStdActor(void){};

	virtual int OnAction(const BYTE* const lpBuf, const int nBufLen) = 0;
};

class CStdServerBase : public CStdThread
{
public:
	CStdServerBase(CStdActor* const pStdActor);

	virtual ~CStdServerBase(void);

	enum {E_TCP = SOCK_STREAM, E_UDP = SOCK_DGRAM};

	void CloseServer(void);
protected:
	bool OpenServer(const unsigned short nPort, const int nType = E_TCP);

	CStdSocket* m_pSvrSock;
	
	unsigned short m_nPort;

	CStdActor* m_pStdActor;
};

class CStdClientBase
{
public:
	CStdClientBase(void);

	virtual ~CStdClientBase(void);

	enum{E_TCP = SOCK_STREAM, E_UDP = SOCK_DGRAM};

protected:

	bool OpenClient(const char* const pAddr, const unsigned short nPort, const int nType = E_TCP);

	CStdSocket* m_pCltSock;
	
	unsigned short m_nPort;
	
	std::string m_strAddr;

	CStdLog m_socket_actor_log;
};

#endif // !defined(AFX_STDSOCKETBASE_H__C6D8CF6D_9E86_4FE8_B436_B6F323F36220__INCLUDED_)

