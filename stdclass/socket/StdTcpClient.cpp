﻿// StdTcpClient.cpp: implementation of the CStdTcpClient class.
//
//////////////////////////////////////////////////////////////////////

#include "StdTcpClient.h"

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////
CStdTcpClient::CStdTcpClient(void)
:CStdClientBase(), CStdActor(), m_pActor(NULL), m_bAutoTest(false), m_bRe<PERSON>onn(false)
{

}

CStdTcpClient::~CStdTcpClient(void)
{
	try
	{
		if (NULL != m_pCltSock)
		{
			m_pCltSock->Close();
		}
	}
	catch (...)
	{
	}
}

// 非缓冲直接发送方式需要用户自己确保发送成功
bool CStdTcpClient::CreateClient(const char* const pAddr, const unsigned short nPort, const bool bReConn, const bool bAutoTest)
{
	if (!OpenClient(pAddr, nPort, CStdClientBase::E_TCP))
	{
		OUTPUT_LOG(m_socket_actor_log, "tcp client sock create failed.");

		return false;
	}

	m_bReConn = bReConn;
	m_bAutoTest = bAutoTest;
	m_pActor = (CStdActor*)this;

	return true;
}

int CStdTcpClient::OnAction(const BYTE* const lpBuf, const int nBufLen)
{
	int nRt = 0;

	nRt = m_pCltSock->wSelect();

	if (nRt > 0)
	{
		int nLeft = nBufLen; 
		int nWritten = 0;
		const char* pBuf = (char*)lpBuf;

		while (nLeft > 0)
		{
			nWritten = m_pCltSock->Send(pBuf, nLeft);

			if (nWritten == -1)
			{
				OUTPUT_LOG(m_socket_actor_log, "tcp client send error.");

				m_pCltSock->Close();

				return false;
			}

			nLeft -= nWritten;
			pBuf += nWritten;
		}

		return (nBufLen - nLeft);
	}
	else if (nRt < 0)
	{
		char szLog[MAX_PATH] = "";

		if (m_bReConn)
		{
			OpenClient(m_strAddr.c_str(), m_nPort, E_TCP);
						
			sprintf(szLog, "reconnect %s:%d", m_strAddr.c_str(), m_nPort);
			
			OUTPUT_LOG(m_socket_actor_log, szLog);
		}
		else
		{
			sprintf(szLog, "client %s:%d disconnected", m_strAddr.c_str(), m_nPort);
			
			OUTPUT_LOG(m_socket_actor_log, szLog);
		}

		return -1;
	}
	else
	{
		if (m_bAutoTest)
		{
			nRt = m_pCltSock->rSelect();

			if (nRt > 0)
			{
				char szTest[40] = "";
				m_pCltSock->Receive(szTest, sizeof(szTest));
			}
			else if (nRt < 0)
			{
				m_pCltSock->Close();
			}
		}
	}

	return 0;
}

int CStdTcpClient::RecvData(unsigned char* const pData, const int nLen)
{
	int nRt = 0;

	nRt = m_pCltSock->rSelect();

	if (nRt > 0)
	{
		nRt = m_pCltSock->Receive(pData, nLen);

		if (nRt <= 0)
		{
			return -1;
		}
	}
	else if (nRt < 0)
	{
		return -1;
	}

	return nRt;
}