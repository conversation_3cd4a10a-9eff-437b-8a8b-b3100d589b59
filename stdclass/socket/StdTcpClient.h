﻿// StdTcpClient.h: interface for the CStdTcpClient class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_STDTCPCLIENT_H__B9E8ED7B_057A_44EB_A37E_945DA2C7F250__INCLUDED_)
#define AFX_STDTCPCLIENT_H__B9E8ED7B_057A_44EB_A37E_945DA2C7F250__INCLUDED_

#pragma once

#include "StdSocketBase.h"

class CStdTcpClient : public CStdClientBase, public CStdActor
{
public:
	CStdTcpClient(void);

	virtual ~CStdTcpClient(void);

	bool CreateClient(const char* const pAddr, const unsigned short nPort, const bool bReConn = false, const bool bAutoTest = false);

	int SendData(const unsigned char* const pData, const int nLen)
	{
		// return m_pActor->OnAction(pData, nLen);
		return OnAction(pData, nLen);
	};
	
	int RecvData(unsigned char* const pData, const int nLen);

	virtual int OnAction(const BYTE* const lpBuf, const int nBufLen);

private:

	CStdActor *m_pActor;

	bool m_bAutoTest;

	bool m_bReConn;
};

#endif // !defined(AFX_STDTCPCLIENT_H__B9E8ED7B_057A_44EB_A37E_945DA2C7F250__INCLUDED_)
