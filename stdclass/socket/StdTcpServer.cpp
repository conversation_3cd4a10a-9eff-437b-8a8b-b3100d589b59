// StdTcpServer.cpp: implementation of the CStdTcpServer class.
//
//////////////////////////////////////////////////////////////////////

#include "StdTcpServer.h"

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CStdTcpServer::CStdTcpServer(CStdActor* const pStdActor)
:CStdServerBase(pStdActor)
{

}

CStdTcpServer::~CStdTcpServer(void)
{

}

int CStdTcpServer::OnAccept(void)
{
	const SOCKET nConnectSockfd = m_pSvrSock->Accept();

	if ((nConnectSockfd > 0) && (m_pStdActor != NULL))
	{
		tSTRU_SOCKFD_INFO tInfo;

		tInfo.svrPort = m_nPort;
		
		if (!CStdSocket::GetPeerName(nConnectSockfd, tInfo.szIp, tInfo.cltPort))
		{
			OUTPUT_LOG(CStdSocket::m_socket_actor_log, "get peer name failed.");
			CStdSocket::Terminate(nConnectSockfd);
			return 0;
		}

		char szLog[MAX_PATH];

		if (m_pStdActor->OnAction((BYTE*)(&tInfo), nConnectSockfd) <= 0)
		{
			sprintf(szLog, "tcp server on %d accept failed.", m_nPort);
			OUTPUT_LOG(CStdSocket::m_socket_actor_log, szLog);
			CStdSocket::Terminate(nConnectSockfd);
			return 0;
		}

		sprintf(szLog, "tcp server on %d accept %s successfully.", m_nPort, tInfo.szIp);
		OUTPUT_LOG(CStdSocket::m_socket_actor_log, szLog);

		return 1;
	}

	return 0;
}

void CStdTcpServer::ThreadMain(void)
{
	char szLog[MAX_PATH];

	sprintf(szLog, "tcp server %d thread start.", m_nPort);
	OUTPUT_LOG(CStdSocket::m_socket_actor_log, szLog);
	int nRt = 0;

	if (NULL == m_pSvrSock)
	{
		sprintf(szLog, "tcp server %d sock is null.", m_nPort);
		OUTPUT_LOG(CStdSocket::m_socket_actor_log, szLog);
		return;
	}

	while(Runflag())
	{
		nRt = m_pSvrSock->rSelect(3000);

		if (nRt > 0)
		{
			if (OnAccept() < 0)
			{
				sprintf(szLog, "tcp server %d accept failed.", m_nPort);
				OUTPUT_LOG(CStdSocket::m_socket_actor_log, szLog);
			}
		}
		else if (nRt < 0)
		{
			sprintf(szLog, "tcp server %d closed, so exit thread.", m_nPort);
			OUTPUT_LOG(CStdSocket::m_socket_actor_log, szLog);
			break;
		}
	}

	sprintf(szLog, "tcp server %d thread end.", m_nPort);
	OUTPUT_LOG(CStdSocket::m_socket_actor_log, szLog);
}

//////////////////////////////////////////////////////////////////////
CStdConnection::CStdConnection(bool bAutoTest)
:CStdThread(), 
m_pStdSocket(NULL), 
m_pStdActor(NULL),
m_bAutoTest(bAutoTest),
m_pData(NULL)
{
	m_pData = new BYTE[RECV_PKG_SIZE];
	memset(&m_tInfo, 0, sizeof(m_tInfo));
}

CStdConnection::~CStdConnection(void)
{
	CloseThread(1000);

	Release();
}

void CStdConnection::Release(void)
{
	if (m_pStdActor != NULL)
	{
		m_pStdActor->OnAction(NULL, 0); // CSdtpSvrFlow
	}

	SetStop();

	CStdAutoLock thelock(&m_lock);

	if (NULL != m_pStdSocket)
	{
		m_pStdSocket->Close();
		delete m_pStdSocket;
		m_pStdSocket = NULL;
	}
	
	if (NULL != m_pData)
	{
		delete []m_pData;
		m_pData = NULL;
	}
	
	m_pStdActor = NULL;
}

bool CStdConnection::Initialize(const SOCKET nConnSock, tSTRU_SOCKFD_INFO* pInfo, CStdActor* pStdActor)
{
	memcpy(&m_tInfo, pInfo, sizeof(tSTRU_SOCKFD_INFO));

	m_pStdActor = pStdActor;
	if (m_pStdActor == NULL)
	{
		char szLog[MAX_PATH];
		sprintf(szLog, "tcp connection  %s on %d actor is null.", m_tInfo.szIp, m_tInfo.svrPort);
		OUTPUT_LOG(CStdSocket::m_socket_actor_log, szLog);
	}

	if (m_pStdSocket == NULL)
	{
		m_pStdSocket = new CStdSocket();
	}
	m_pStdSocket->Close();

	if (NULL == m_pStdSocket)
	{
		char szLog[MAX_PATH];
		sprintf(szLog, "tcp connection  %s on %d socket create failed.", m_tInfo.szIp, m_tInfo.svrPort);
		OUTPUT_LOG(CStdSocket::m_socket_actor_log, szLog);
		return false;
	}

	const int OptionValue = 1;
	m_pStdSocket->Attach(nConnSock);
	m_pStdSocket->SetSockOpt(SO_KEEPALIVE, &OptionValue, sizeof(OptionValue), SOL_SOCKET);

	if (!CreateThread())
	{
		m_pStdSocket->Close();
		char szLog[MAX_PATH];
		sprintf(szLog, "tcp connection %s on %d thread create failed.", m_tInfo.szIp, m_tInfo.svrPort);
		OUTPUT_LOG(CStdSocket::m_socket_actor_log, szLog);
		return false;
	}

	return true;
}

void CStdConnection::ThreadMain(void)
{
	char szLog[MAX_PATH];
	sprintf(szLog, "tcp connection %s on [s%d,c%d] thread start. [%0llx]", m_tInfo.szIp, m_tInfo.svrPort , m_tInfo.cltPort , __int64(this));
	OUTPUT_LOG(CStdSocket::m_socket_actor_log, szLog);
	
	int nRt = 0;

	if (NULL == m_pStdSocket)
	{
		sprintf(szLog, "tcp connection sock is null.");
		OUTPUT_LOG(CStdSocket::m_socket_actor_log, szLog);
		return;
	}

	BYTE nNum = 0;

	while(Runflag())
	{
		CStdSocket::cStatus[0]++;

		nRt = m_pStdSocket->rSelect();

		if (nRt > 0)
		{
			nNum = 0;
			if (OnRecv() <= 0)
			{
				break;
			}
		}
		else if (nRt < 0)
		{
			nNum = 0;
			sprintf(szLog, "tcp connection rselect error, so exit thread.");
			OUTPUT_LOG(CStdSocket::m_socket_actor_log, szLog);
			break;
		}
		else // == 0
		{
			if ((m_bAutoTest) && ((++nNum) > 2))
			{
				nNum = 0;

				if (!ConnectionTest())
				{
					sprintf(szLog, "tcp connection test error, so exit thread.");
					OUTPUT_LOG(CStdSocket::m_socket_actor_log, szLog);
					break;
				}
			}
		}
	}

	sprintf(szLog, "tcp connection  %s on [s%d,c%d] thread end.[%0llx]", m_tInfo.szIp, m_tInfo.svrPort , m_tInfo.cltPort , __int64(this));
	OUTPUT_LOG(CStdSocket::m_socket_actor_log, szLog);

	Release();
	OUTPUT_LOG(CStdSocket::m_socket_actor_log, "tcp connection thread exit.");
}

int CStdConnection::OnRecv(void)
{
	int nRecv = 0;
	nRecv = m_pStdSocket->Receive(m_pData, RECV_PKG_SIZE);

	if (nRecv > 0)
	{
		if (m_pStdActor != NULL)
		{
			int nTotal = 0;
			do 
			{
				nTotal += m_pStdActor->OnAction(m_pData + nTotal, nRecv - nTotal);

				if (nTotal < nRecv)
				{
					CStdCommond::Sleep(5);
				}
				else
				{
					break;
				}
			} while (Runflag());
		}
	}
	else if (nRecv == 0)
	{
		char szLog[MAX_PATH];
		sprintf(szLog, "tcp connection %s on %d closed, so exit thread.", m_tInfo.szIp, m_tInfo.svrPort);
		OUTPUT_LOG(CStdSocket::m_socket_actor_log, szLog);
	}
	else
	{
		char szLog[MAX_PATH];
		sprintf(szLog, "tcp connection %s on %d recv error, so exit thread.", m_tInfo.szIp, m_tInfo.svrPort);
		OUTPUT_LOG(CStdSocket::m_socket_actor_log, szLog);
	}

	return nRecv;
}

int CStdConnection::OnSend(const char* const lpBuf, const int nBufLen)
{
	CStdAutoLock thelock(&m_lock);

	if (m_pStdSocket == NULL)
	{
		return -1;
	}

	int nRt = 0;

	nRt = m_pStdSocket->wSelect();

	if (nRt > 0)
	{
		int nLeft = nBufLen; 
		int nWritten = 0;
		const char* pBuf = (char*)lpBuf;

		while ((nLeft > 0) && Runflag())
		{
			nWritten = m_pStdSocket->Send(pBuf, nLeft);

			if (nWritten == -1)
			{
				SetStop();
				char szLog[MAX_PATH];
				sprintf(szLog, "tcp connection  %s on %d send error.", m_tInfo.szIp, m_tInfo.svrPort);
				OUTPUT_LOG(CStdSocket::m_socket_actor_log, szLog);
				return -1;
			}

			nLeft -= nWritten;
			pBuf += nWritten;
		}

		return 1;
	}
	else if (nRt < 0)
	{
		SetStop();
		char szLog[MAX_PATH];
		sprintf(szLog, "tcp connection  %s on %d wselect error.", m_tInfo.szIp, m_tInfo.svrPort);
		OUTPUT_LOG(CStdSocket::m_socket_actor_log, szLog);
		return -1;
	}

	return 0;
}

bool CStdConnection::ConnectionTest(void)
{
	return (OnSend((char*)SOCK_Test_PKG, sizeof(SOCK_Test_PKG)) >= 0);
}
