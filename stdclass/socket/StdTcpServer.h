// StdTcpServer.h: interface for the CStdTcpServer class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_STDTCPSERVER_H__DF08CC80_9C25_4717_AB52_5F46D7D39D33__INCLUDED_)
#define AFX_STDTCPSERVER_H__DF08CC80_9C25_4717_AB52_5F46D7D39D33__INCLUDED_

#pragma once

#include "./StdSocketBase.h"

class CStdTcpServer : public CStdServerBase  
{
public:
	CStdTcpServer(CStdActor* const pStdActor);

	virtual ~CStdTcpServer(void);

	virtual void ThreadMain(void);

	bool CreateServer(const unsigned short nPort)
	{
		return OpenServer(nPort, E_TCP);
	};
private:
	int OnAccept(void);
};

/////////////////////////////////////////////////////////////////
class CStdConnection : public CStdThread
{
public:
	CStdConnection(bool bAutoTest = false);
	
	virtual ~CStdConnection(void);
	
	bool Initialize(const SOCKET nConnSock, tSTRU_SOCKFD_INFO* pInfo, CStdActor* pStdActor);
	
	virtual void ThreadMain(void);
	
	int OnSend(const char* const lpBuf, const int nBufLen);
	
private:
	
	int OnRecv(void);
	
	void Release(void);
	
	bool ConnectionTest(void);

	CStdSocket* m_pStdSocket;
	CStdActor* m_pStdActor;
	CStdLock m_lock;
	bool m_bAutoTest;

	BYTE* m_pData;

	tSTRU_SOCKFD_INFO m_tInfo;
};
#endif // !defined(AFX_STDTCPSERVER_H__DF08CC80_9C25_4717_AB52_5F46D7D39D33__INCLUDED_)


