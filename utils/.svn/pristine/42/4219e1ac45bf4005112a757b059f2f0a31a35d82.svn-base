// DataBaseDeal.h: interface for the CDataBaseDeal class.
//
//////////////////////////////////////////////////////////////////////

#ifndef __DATABASEDEAL_H__
#define __DATABASEDEAL_H__

#include "StdDbHeader.h"
#include "StdDataObject.h"
#include "CommandStru_Base.h"

#define _FOR_TEST 0

#define MAX_RECORD_SIZE   32768
#define Max_RECORD_Data   0

typedef struct DBConnInfo
{
	int nDbId;
	char pchDBServer[64];
	char pchDBName[64];
	char pchUserName[64];
	char pchPassword[64];
	int nPort;
	int nDisplaySql;

}tDBConnInfo;

typedef struct STRU_COLUMN
{
	int  iColType;
	BYTE pColValue[MAX_PATH];
	BYTE* pBigValue; //只有VARBINARY才用
	long iBigLen;
	long iDataLen;
	char szColName[MAX_PATH];
	void Clear(void)
	{
		iColType = 0;
		pBigValue = NULL;
		iBigLen = 0;
		iDataLen = 0;
		memset(pColValue, 0, sizeof(pColValue));
		memset(szColName, 0, sizeof(szColName));
	};
	void Init(int nColType, int nBuffLen)
	{
		if ((pBigValue == NULL) && (nBuffLen > 0))
		{
			pBigValue = new BYTE[nBuffLen];
			iBigLen = nBuffLen;
		}

		iColType = nColType;
	};
	void Release(void)
	{
		if (pBigValue != NULL)
		{
			delete[] pBigValue;
			pBigValue = NULL;
		}
		iBigLen = 0;
	};

	void ExpandMemField(tStru_Array_Pointer* pFieldInfo, int nlen)
	{		
		if (((int)(pFieldInfo->nMaxSize - pFieldInfo->nSize)) < nlen)
		{
			const int nBufLen = pFieldInfo->nSize + 1;
			BYTE* pBufTemp = new BYTE[nBufLen];
			memcpy(pBufTemp, pFieldInfo->pPointer, pFieldInfo->nSize);
			
			pFieldInfo->Realloc(pFieldInfo->nMaxSize + nlen + 512);
			pFieldInfo->nSize = (nBufLen - 1);
			memcpy(pFieldInfo->pPointer, pBufTemp, pFieldInfo->nSize);
			
			delete pBufTemp;
		}
	};
		
	//总长度(2BYTE)+列名长度(nStrLen 2BYTE)+列名(nStrLen BYTE)+列数据类型(2BYTE)+DataLen(2BYTE)+pColValue(DataLen BYTE)
	void BuildSendBuff(tStru_Array_Pointer* pFieldInfo)
	{
		int nStrLen = strlen(szColName);

		int nlen = (2 + nStrLen + 2 + 2 + iDataLen);

		ExpandMemField(pFieldInfo, nlen);

		memcpy(pFieldInfo->pPointer + pFieldInfo->nSize, &nlen, sizeof(WORD));
		pFieldInfo->nSize += sizeof(WORD);

		memcpy(pFieldInfo->pPointer + pFieldInfo->nSize, &nStrLen, sizeof(WORD));
		pFieldInfo->nSize += sizeof(WORD);

		memcpy(pFieldInfo->pPointer + pFieldInfo->nSize, szColName, nStrLen);
		pFieldInfo->nSize += nStrLen;

		memcpy(pFieldInfo->pPointer + pFieldInfo->nSize, &iColType, sizeof(WORD));
		pFieldInfo->nSize += sizeof(WORD);

		if (pBigValue != NULL && iBigLen > 0)
		{
			memcpy(pFieldInfo->pPointer + pFieldInfo->nSize, &iDataLen, sizeof(WORD));
			pFieldInfo->nSize += sizeof(WORD);

			memcpy(pFieldInfo->pPointer + pFieldInfo->nSize, pBigValue, iDataLen);
			pFieldInfo->nSize += iDataLen;
		}
		else
		{
			// 字段为 NULL 的情况
			if (iDataLen < 0)
			{
				iDataLen = sizeof(pColValue);

				if (TYPE_CS_TEXT == iColType)
				{
					if (strlen((char*)pColValue) == 0)
					{
						iDataLen = 1;
					}
				}
			}

			memcpy(pFieldInfo->pPointer + pFieldInfo->nSize, &iDataLen, sizeof(WORD));
			pFieldInfo->nSize += sizeof(WORD);

			memcpy(pFieldInfo->pPointer + pFieldInfo->nSize, pColValue, iDataLen);
			pFieldInfo->nSize += iDataLen;
		}
	};
}tSTRU_COLUMN;

class CDataBaseDeal;
class CQueryRecord
{
public:
	CQueryRecord(void);

	virtual ~CQueryRecord(void);

	void Clear(void);

	CDataBaseDeal* m_pDataBaseDeal_;
	BYTE m_bIsNeedDelDB_;
	int m_iCurrentDBID_;

	std::vector<tSTRU_COLUMN> m_vecColumn;

	// 处理查询信息
	virtual BOOL RowResultDeal(int nQType) = 0;

	//获取表字段属性, 并对m_vecColumn进行处理
	BOOL SetColumnInfo(tDBConnInfo* tInfo, std::string strSql);

private:
	BOOL InitDataType(int nDataType, tSTRU_COLUMN& tCol);
	BOOL GetColInfoBySql(tDBConnInfo* dbConnInfo, std::string strSql, std::vector<STRU_DBCOL>& vecColAttri);
	BOOL DataType2DataObject(const int sqlDataType, int& retCType);

};

#include "StdMySql.h"

class CStdMySql_Connection;

class CDataBaseDeal  
{
public:
	CDataBaseDeal(void);

	~CDataBaseDeal(void);

	bool ResetDBConnInfo(tDBConnInfo tInfo);
	void SetDbConnPara(tDBConnInfo& tInfo);

	int ExecSql(const char* const strSql, BOOL bDisplayLog = true);

	int SearchSql(const char* const strSql, CQueryRecord* pQueryRecord, int nQType = 0, BOOL needInit = true);

	void BcpDataFile(std::string filename, std::string tablename, BOOL delmode = true);

	BOOL DbConnIsNormal(void);

	tDBConnInfo* GetConnectInfo();

private:
	CStdMySql_Connection* m_DbConn; 

	tDBConnInfo m_tDBConnInfo;

	CStdLog m_BcpLog;
	CStdLog m_SqlLog;
};


#endif 
