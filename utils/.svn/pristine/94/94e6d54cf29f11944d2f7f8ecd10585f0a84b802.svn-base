#ifndef __LINUX_API_H__
#define __LINUX_API_H__

#include <limits.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <iostream>
#include <netdb.h>
#include <arpa/inet.h>
#include <ifaddrs.h>

class Clinux_api
{
public:
	Clinux_api();
	~Clinux_api();

public:
	static int get_executable_name(char* processname);
	static int get_executable_fullname(char* processAllPathname);
	static std::string GetlocalIPV4(std::string networkCard);
	static std::string GetlocalIPV6(std::string networkCard);
	static bool IsProcessRunning(char* processname, int npid);
	static bool path_exists(const std::string& path);
	static int mkdir_p(std::string destPath);
	
};

#endif
