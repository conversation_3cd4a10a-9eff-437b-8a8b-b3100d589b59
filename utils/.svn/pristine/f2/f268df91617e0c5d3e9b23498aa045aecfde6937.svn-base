#include "linux_api.h"
#include <dirent.h>
#include <vector>
#include "Strutils.h"

Clinux_api::Clinux_api()
{
}


Clinux_api::~Clinux_api()
{
}

int Clinux_api::get_executable_name(char* processname)
{
	char processdir[PATH_MAX] = {0};

	char* path_end;
	if (readlink("/proc/self/exe", processdir, PATH_MAX) <= 0)
	{
		return -1;
	}

	path_end = strrchr(processdir, '/');

	if (path_end == NULL)
	{
		return -1;
	}

	++path_end;
	strcpy(processname, path_end);
	*path_end = '\0';

	return 0;
}

int Clinux_api::get_executable_fullname(char* processAllPathname)
{
	if (readlink("/proc/self/exe", processAllPathname, PATH_MAX) <= 0)
	{
		return -1;
	}

	return 0;
}


std::string Clinux_api::GetlocalIPV4(std::string networkCard)
{
  struct ifaddrs * ifAddrStruct=NULL;
  void * tmpAddrPtr=NULL;

  getifaddrs(&ifAddrStruct);

  while (ifAddrStruct!=NULL) 
  {
      if (ifAddrStruct->ifa_addr->sa_family==AF_INET          // check it is IP4
        && strcmp(ifAddrStruct->ifa_name, networkCard.c_str()) == 0)  // check network card name
      {
          // is a valid IP4 Address
          tmpAddrPtr=&((struct sockaddr_in*)ifAddrStruct->ifa_addr)->sin_addr;
          char addressBuffer[INET_ADDRSTRLEN];
          inet_ntop(AF_INET, tmpAddrPtr, addressBuffer, INET_ADDRSTRLEN);
          return std::string(addressBuffer);
      }

      ifAddrStruct=ifAddrStruct->ifa_next;
  }

  return "";
}

std::string Clinux_api::GetlocalIPV6(std::string networkCard)
{
  struct ifaddrs * ifAddrStruct=NULL;
  void * tmpAddrPtr=NULL;

  getifaddrs(&ifAddrStruct);

  while (ifAddrStruct!=NULL) 
  {
      if (ifAddrStruct->ifa_addr->sa_family==AF_INET6         // check it is IP6
        && strcmp(ifAddrStruct->ifa_name, networkCard.c_str()) == 0)  // check network card name
      {
          // is a valid IP6 Address
          tmpAddrPtr=&((struct sockaddr_in*)ifAddrStruct->ifa_addr)->sin_addr;
          char addressBuffer[INET6_ADDRSTRLEN];
          inet_ntop(AF_INET6, tmpAddrPtr, addressBuffer, INET6_ADDRSTRLEN);
          return std::string(addressBuffer);
      }

      ifAddrStruct=ifAddrStruct->ifa_next;
  }

  return "";
}

bool Clinux_api::IsProcessRunning(char* processname, int npid)
{
    DIR *dir = opendir("/proc");
    if (dir == nullptr) {
        std::cerr << "Unable to open /proc directory." << std::endl;
        return false;
    }
 
    struct dirent *entry;

    bool bIsRunning = false;
    while ((entry = readdir(dir)) != nullptr) {
        // 忽略不是数字的目录（通常是指向当前和父目录的符号链接）
        if (entry->d_type == DT_DIR && strcmp(entry->d_name, ".") != 0 && strcmp(entry->d_name, "..") != 0) {
            // 尝试打开命令行执行符号链接
            char cmdpath[PATH_MAX];
            snprintf(cmdpath, sizeof(cmdpath), "/proc/%s/cmdline", entry->d_name);
            FILE *cmdfile = fopen(cmdpath, "r");
            if (cmdfile != nullptr) {
                // 读取并打印命令行
                char cmd[PATH_MAX];
                if (fgets(cmd, sizeof(cmd), cmdfile) != nullptr) {
                  if (strstr(cmd, processname) != nullptr && std::atoi(entry->d_name) != npid)
                  {
                    bIsRunning = true;
                  }
                }
                fclose(cmdfile);
            }
        }

        // 找到就跳出
        if (bIsRunning)
        {
          break;
        }
    }
 
    closedir(dir);

    return bIsRunning;
}

int Clinux_api::mkdir_p(std::string destPath)
{
  if (access(destPath.c_str(), 0) != 0)
	{
		std::vector<std::string> vec;
		vec = CStrutils::Split(destPath, "/");
		std::string tmp = "/"; // 根目录
		for (auto it = vec.begin(); it != vec.end(); ++it)
		{
      if ("" != *it)
      {
        tmp += *it;
        tmp += '/';

        if (!path_exists(tmp) && mkdir(tmp.c_str(), S_IRWXU | S_IRWXG | S_IRWXO) == -1)
        {
          return -1;
        }
      }
		}
	}

  return 0;
}

bool Clinux_api::path_exists(const std::string& path) 
{
    struct stat st;
    return (stat(path.c_str(), &st) == 0);
}
