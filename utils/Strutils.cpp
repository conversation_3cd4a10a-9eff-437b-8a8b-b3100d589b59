// strutils.cpp: implementation of the strutils class.
//
//////////////////////////////////////////////////////////////////////

#include "Strutils.h"

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

std::string CStrutils::ReplaceStr(const std::string orignStr, const std::string oldStr, const std::string newStr)   
{
	if (orignStr.find(oldStr.c_str()) == std::string::npos)
	{
		return orignStr;
	}

	size_t pos = 0;   
	std::string tempStr = orignStr;   
	std::string::size_type newStrLen = newStr.length();   
	std::string::size_type oldStrLen = oldStr.length();   
	while(true)   
	{
		pos = tempStr.find(oldStr, pos);   
		if (pos == std::string::npos) break;   
		tempStr.replace(pos, oldStrLen, newStr);  
		pos += newStrLen;  
	}   
	
	return tempStr;   
} 

std::string CStrutils::Trim(const std::string& strInput)
{
	if (strInput.empty())
	{
		return strInput;
	}

	int nStartPos = 0;
	int nEndPos = strInput.length();
	while (strInput.at(nStartPos) == ' ' || strInput.at(nStartPos) == '\t')
	{
		nStartPos++;

		if (nStartPos >= nEndPos)
		{
			break;
		}
	}
	while (nEndPos > 0)
	{
		if (strInput.at(nEndPos - 1) == ' ' || strInput.at(nEndPos - 1) == '\t')
		{
			nEndPos--;
		}
		else
		{
			break;
		}
	}

	std::string strRet("");
	if (nEndPos > nStartPos)
		strRet = strInput.substr(nStartPos, nEndPos - nStartPos);

    return strRet;
}

/// <summary>
/// Splits the string s on the given delimiter(s) and
/// returns a list of tokens without the delimiter(s)
/// </summary>
/// <param name=s>The string being split</param>
/// <param name=match>The delimiter(s) for splitting</param>
/// <param name=removeEmpty>Removes empty tokens from the list</param>
/// <param name=fullMatch>
/// True if the whole match string is a match, false
/// if any character in the match string is a match
/// </param>
/// <returns>A list of tokens</returns>
typedef std::string::size_type (std::string::*find_t)(const std::string& delim, std::string::size_type offset) const;
std::vector<std::string> CStrutils::Split(const std::string& s, const std::string& match, bool removeEmpty, bool fullMatch)
{
	std::vector<std::string> result;   // return container for tokens
	result.clear();

	std::string::size_type start = 0,  // starting position for searches
	skip = 1;                          // positions to skip after a match
	find_t pfind = &std::string::find_first_of; // search algorithm for matches
	
	if (fullMatch)
	{
		// use the whole match string as a key
		// instead of individual characters
		// skip might be 0. see search loop comments
		skip = match.length();
		pfind = &std::string::find;
	}
	
	while (start != std::string::npos)
	{
		// get a complete range [start..end)
		std::string::size_type end = (s.*pfind)(match, start);
		
		// null strings always match in string::find, but
		// a skip of 0 causes infinite loops. pretend that
		// no tokens were found and extract the whole string
		if (skip == 0) end = std::string::npos;
		
		std::string token = s.substr(start, end - start);
		
		if (!(removeEmpty && token.empty()))
		{
			if (removeEmpty)
			{
				token = CStrutils::ReplaceStr(token, " ", "");
			}
			// extract the token and add it to the result list
			result.push_back(token);
		}
		
		// start the next range
		if ((start = end) != std::string::npos) start += skip;
	}
	
	return result;
}

void CStrutils::ToUpperString(std::string &str)
{
	transform(str.begin(), str.end(), str.begin(), ::toupper);
}

void CStrutils::ToLowerString(std::string &str)
{
	transform(str.begin(), str.end(), str.begin(), ::tolower);
}

int CStrutils::Stricmp(std::string str1, std::string str2)
{
	ToLowerString(str1);
	ToLowerString(str2);

	return strcmp(str1.c_str(), str2.c_str());
}

int CStrutils::Strnicmp(std::string str1, std::string str2, size_t n)
{
	ToLowerString(str1);
	ToLowerString(str2);

	return strncmp(str1.c_str(), str2.c_str(), n);
}

