// strutils.h: interface for the strutils class.
//  * String utility definitions
//////////////////////////////////////////////////////////////////////

#ifndef __STRUTILS_H__
#define __STRUTILS_H__

#include "StdHeader.h"

#define UI_UTIL_TOKEN ("$")

#define CHARCDEC( c ) ( ((c) >= '0' && (c) <= '9'))
#define CHARCHX( c ) ( ((c) >= '0' && (c) <= '9') ||\
((c) >= 'A' && (c) <= 'F') ||\
((c) >= 'a' && (c) <= 'f'))
#define CHARLAWFUL( c ) ( ((c) >= '0' && (c) <= '9') ||\
((c) >= 'A' && (c) <= 'Z') ||\
((c) >= 'a' && (c) <= 'z') ||\
((c) == '_'))

class CStrutils  
{
public:
	static std::string ReplaceStr(const std::string orignStr, const std::string oldStr, const std::string newStr);
	static std::string Trim(const std::string& strInput);
	static std::vector<std::string> Split(const std::string& s, const std::string& match, bool removeEmpty=false, bool fullMatch=false);
	static void ToUpperString(std::string &str);
	static void ToLowerString(std::string &str);
	static int Stricmp(std::string str1, std::string str2);
	static int Strnicmp(std::string str1, std::string str2, size_t size);
};

#endif 