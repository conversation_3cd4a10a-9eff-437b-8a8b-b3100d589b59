// DataBaseDeal.cpp: implementation of the CDataBaseDeal class.
//
//////////////////////////////////////////////////////////////////////

#include "DataBaseDeal.h"
#include "./Utility.h"
#include "./Strutils.h"

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////
CQueryRecord::CQueryRecord(void)
:m_vecColumn()
{
    Clear();
}

CQueryRecord::~CQueryRecord(void)
{
    Clear();
}

void CQueryRecord::Clear(void)
{
    for (size_t i = 0; i < m_vecColumn.size(); i++)
    {
        m_vecColumn[i].Release();
        m_vecColumn[i].Clear();
    }

    m_vecColumn.clear();
}

void CQueryRecord::ClearColumnBuffer()
{
    for (size_t i = 0; i < m_vecColumn.size(); i++)
    {
        memset(m_vecColumn[i].pColValue, 0, MAX_PATH);
        if ((m_vecColumn[i].pBigValue == NULL) && (m_vecColumn[i].iBigLen > 0))
		{
            memset(m_vecColumn[i].pBigValue, 0, m_vecColumn[i].iBigLen);
		}
    }
}

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////
CDataBaseDeal::CDataBaseDeal(void)
:m_DbConn(NULL),
m_BcpLog("dbbcpfile.txt"),
m_SqlLog("ms_execute_sql_log.txt")
{
    memset(&m_tDBConnInfo, 0, sizeof(m_tDBConnInfo));
	m_DbConn = new CStdFreetds_Connection(&m_BcpLog);
}

CDataBaseDeal::~CDataBaseDeal(void)
{
    if (NULL != m_DbConn)
    {
        delete m_DbConn;
        m_DbConn = NULL;
    }
}

BOOL CDataBaseDeal::DbConnIsNormal(void)
{
    if ((m_tDBConnInfo.pchUserName == NULL) || (strcmp(m_tDBConnInfo.pchUserName, "") == 0) ||
        (m_tDBConnInfo.pchPassword == NULL) || (strcmp(m_tDBConnInfo.pchPassword, "") == 0) ||
        (m_tDBConnInfo.pchDBServer == NULL) || (strcmp(m_tDBConnInfo.pchDBServer, "") == 0))
    {
        return FALSE;
    }

    if (!m_DbConn->connect(m_tDBConnInfo.pchUserName, 
        m_tDBConnInfo.pchPassword, 
        m_tDBConnInfo.pchDBServer, 
        m_tDBConnInfo.pchDBName))
    {
        return FALSE;
    }

	m_DbConn->cleanup_conn();
	
    return TRUE;
}

tDBConnInfo* CDataBaseDeal::GetConnectInfo()
{
    return &m_tDBConnInfo;
}

/************************************************************************
恢复数据库连接指向主库
************************************************************************/
bool CDataBaseDeal::ResetDBConnInfo(tDBConnInfo tInfo)
{
    memcpy(&m_tDBConnInfo, &tInfo, sizeof(tDBConnInfo));
	return true;
}

void CDataBaseDeal::SetDbConnPara(tDBConnInfo& tInfo)
{
    if ((strcasecmp(m_tDBConnInfo.pchDBName, tInfo.pchDBName) != 0)
        || (strcasecmp(m_tDBConnInfo.pchDBServer, tInfo.pchDBServer) != 0)
        || (m_tDBConnInfo.nDbId != tInfo.nDbId))
    {
        memcpy(&m_tDBConnInfo, &tInfo, sizeof(tDBConnInfo));
        m_DbConn->cleanup_conn();
    }
}

void CDataBaseDeal::BcpDataFile(std::string filename, std::string tablename, BOOL delmode)
{
    if (m_tDBConnInfo.nDisplaySql == 1)
    {
        OUTPUT_LOG(m_BcpLog, CUtility::OutPutInfo("[BCP FileName: ] %s", filename.c_str()).c_str());
    }
    if (!m_DbConn->connect(m_tDBConnInfo.pchUserName, 
        m_tDBConnInfo.pchPassword, 
        m_tDBConnInfo.pchDBServer, 
        m_tDBConnInfo.pchDBName))
    {
        m_BcpLog.OutPut("数据库连接失败.\r\n");
        return;
    }

    bool bRt = m_DbConn->BcpData(filename.c_str(), tablename.c_str());

    if (!bRt)
    {
        char szLog[512] = "";
        snprintf(szLog, 512 - 1, "Bcp filed:%s\r\n", filename.c_str());
        m_BcpLog.OutPut(szLog);
    }
    else
    {
        if (delmode)
        {
            CStdFile::Remove(filename.c_str());
        }
    }

    m_DbConn->cleanup_conn();
}

int CDataBaseDeal::ExecSql(const char* const strSql, BOOL bDisplayLog)
{
    if (m_tDBConnInfo.nDisplaySql == 1 && bDisplayLog)
    {
        OUTPUT_LOG(m_SqlLog, CUtility::OutPutInfo("[Exec SQL: ] %s", strSql).c_str());
    }
		
    if (!m_DbConn->connect(m_tDBConnInfo.pchUserName, 
        m_tDBConnInfo.pchPassword, 
        m_tDBConnInfo.pchDBServer, 
        m_tDBConnInfo.pchDBName))
    {
        return -10;
    }
    
    if (!m_DbConn->Execute(strSql))
    {
    	m_DbConn->cleanup_conn();
		return -10;
    }

    if (m_DbConn->DbResults() == CStdFreetds_Connection::E_FAIL)
    {
    	m_DbConn->cleanup_conn();
		return -10;
	}

	m_DbConn->cleanup_conn();
    return 0;
}

int CDataBaseDeal::SearchSql(const char* const strSql, CQueryRecord* pQueryRecord, int nQType)
{
    if (m_tDBConnInfo.nDisplaySql == 1)
    {
        OUTPUT_LOG(m_SqlLog, CUtility::OutPutInfo("[Exec SQL: ] %s", strSql).c_str());
    }
	
    if(NULL == pQueryRecord)
    {
        return -1;
    }

    if (!m_DbConn->connect(m_tDBConnInfo.pchUserName, 
        m_tDBConnInfo.pchPassword, 
        m_tDBConnInfo.pchDBServer, 
        m_tDBConnInfo.pchDBName))
    {
    	m_DbConn->cleanup_conn();
        return -2; 
    }
	
    if (!m_DbConn->Execute(strSql))
    {
        m_DbConn->cleanup_conn();
        return -1;
    } 
	
    // 获取字段属性,并绑定buffer
    if (FALSE == SetColumnInfo(pQueryRecord))
    {
        OUTPUT_LOG(m_SqlLog, CUtility::OutPutInfo("[Failed to get table field properties!]: %s", strSql).c_str());
        return -1;
    }

    // 跳到下一行
    while(m_DbConn->DbNextRow() == CStdFreetds_Connection::E_MORE_ROWS)
    {
        GetColumnsDatalen(pQueryRecord);
        pQueryRecord->RowResultDeal(nQType);

        // 清空Buffer，然后读取下一行
        pQueryRecord->ClearColumnBuffer();
    }  

	m_DbConn->cleanup_conn();
    return 1;
}

BOOL CDataBaseDeal::SetColumnInfo(CQueryRecord* pQueryRecord)
{
    if (nullptr == m_DbConn)
    {
        return FALSE;
    }

    pQueryRecord->Clear();

    std::vector<STRU_DBCOL> vecColAttri; //列属性容器
    vecColAttri.clear();
    
    if (FALSE == GetColumnAttribute(vecColAttri))
    {
        OUTPUT_LOG(m_SqlLog, CUtility::OutPutInfo("[Failed GetColumnAttribute!]:").c_str());
        return FALSE;
    }

    if (FALSE == BuildQueryRecordColumnInfo(pQueryRecord, vecColAttri))
    {
        OUTPUT_LOG(m_SqlLog, CUtility::OutPutInfo("[Failed BuildQueryRecordColumnInfo!]").c_str());
        return FALSE;
    }

    if (FALSE == BindQueryRecordColumns(pQueryRecord))
    {
        OUTPUT_LOG(m_SqlLog, CUtility::OutPutInfo("[Failed BindQueryRecordColumns!]").c_str());
        return FALSE;
    }
    
    return TRUE;
}

BOOL CDataBaseDeal::BuildQueryRecordColumnInfo(CQueryRecord* pQueryRecord, std::vector<STRU_DBCOL> vecColAttri)
{
    if (0 == vecColAttri.size())
    {
        return FALSE;
    }

    tSTRU_COLUMN tTemp;

    for (size_t i = 0; i < vecColAttri.size(); i++)
    {
        tTemp.Clear();
        tTemp.iSqlType = vecColAttri[i].columnType;
        tTemp.iColId = vecColAttri[i].colid;

        if (!SqlType2DataType(tTemp)) return FALSE;
        if (!SqlType2BindType(tTemp)) return FALSE;
        if (!InitDataType(tTemp))     return FALSE;
        
        strncpy(tTemp.szColName, vecColAttri[i].columnName.c_str(), sizeof(tTemp.szColName));

        pQueryRecord->m_vecColumn.push_back(tTemp);
    }
    
    if (pQueryRecord->m_vecColumn.size() == 0)
    {
        return FALSE;
    }

    return TRUE;
}

BOOL CDataBaseDeal::GetColumnAttribute(std::vector<STRU_DBCOL>& vecColAttri)
{
    do
    {
        m_DbConn->GetSelectColAttribute(vecColAttri);
    }
    while (vecColAttri.size() == 0 && (m_DbConn->DbResults() != CStdFreetds_Connection::E_FAIL));

	if (vecColAttri.size() == 0)
	{
		return FALSE;
	}

	if (vecColAttri.size() > 0 && m_DbConn->DbResults() == CStdFreetds_Connection::E_NO_MORE_RESULTS)
	{
		return FALSE;
	}

    return TRUE;
}

BOOL CDataBaseDeal::BindQueryRecordColumns(CQueryRecord* pQueryRecord)
{
    BOOL bRet = FALSE;
    if (nullptr == pQueryRecord)
    {
        return bRet;
    }
    // 绑定字段
    for (auto it = pQueryRecord->m_vecColumn.begin(); it != pQueryRecord->m_vecColumn.end(); it++)
    {
        memset((*it).pColValue, 0, MAX_PATH);
        if (((*it).pBigValue != nullptr) && ((*it).iBigLen > 0))
        {
            memset((*it).pBigValue, 0, (*it).iBigLen);
        }

        if (TYPE_CS_VARYBIN == (*it).iColType)
        {
            if ((*it).pBigValue != NULL)
            {
                long nlen = (*it).iBigLen;
                assert (nlen > 0);
                assert (nlen <= MAX_RECORD_SIZE);
                if (nlen == 0)
                {
                    nlen = 8000; /*实际没这么长*/
                }
           
                bRet = m_DbConn->BindCol((*it).iColId, (*it).iBindType, nlen, (*it).pBigValue);
            }
            else
            {
                bRet = m_DbConn->BindCol((*it).iColId, (*it).iBindType, MAX_PATH, (*it).pColValue);
            }
        }
        else if (TYPE_CS_TEXT == (*it).iColType)
        {
            bRet = m_DbConn->BindCol((*it).iColId, (*it).iBindType, 0, (*it).pColValue);
        }
        else
        {
            bRet = m_DbConn->BindCol((*it).iColId, (*it).iBindType, MAX_PATH, (*it).pColValue);
        }

        if (!bRet)
        {
            OUTPUT_LOG(m_SqlLog, CUtility::OutPutInfo("Failed to bind database column %s, column type %d!", (*it).szColName, (*it).iBindType).c_str());
            return FALSE;
        }
    }

    return TRUE;
}

BOOL CDataBaseDeal::InitDataType(tSTRU_COLUMN& tCol)
{
    switch (tCol.iColType)
    {
    case CDataObject::E_DataType_Text:
        tCol.Init(TYPE_CS_TEXT, Max_RECORD_Data);
        break;
        
    case CDataObject::E_DataType_Image:
    case CDataObject::E_DataType_Byte:
        tCol.Init(TYPE_CS_VARYBIN, MAX_RECORD_SIZE);
        break;
        
    case CDataObject::E_DataType_TinyInt:
        tCol.Init(TYPE_CS_SMALLINT, Max_RECORD_Data);
        break;
        
    case CDataObject::E_DataType_SmallInt:
        tCol.Init(TYPE_CS_SMALLINT, Max_RECORD_Data);
        break;
        
    case CDataObject::E_DataType_Int:
        tCol.Init(TYPE_CS_INT, Max_RECORD_Data);
        break;
        
    case CDataObject::E_DataType_Float:
        tCol.Init(TYPE_CS_FLOAT, Max_RECORD_Data);
        break;
    case CDataObject::E_DataType_Numeric: 
    case CDataObject::E_DataType_Int64:
        tCol.Init(TYPE_CS_INT64, Max_RECORD_Data);
        break;
    
	case CDataObject::E_DataType_DateTime:
		tCol.Init(TYPE_CS_INT, Max_RECORD_Data);
		break;
	
    default:
        {            
            return FALSE;
        }
    }

    return TRUE;
}

BOOL CDataBaseDeal::SqlType2DataType(tSTRU_COLUMN& tCol)
{
	tCol.iColType = CDataObject::E_DataType_Text;

	switch (tCol.iSqlType)
	{
		case CSybDBType::E_SYB_SYBTEXT:
		case CSybDBType::E_SYB_SYBCHAR:
		case CSybDBType::E_SYB_SYBMSDATE:
		case CSybDBType::E_SYB_SYBMSTIME:
		case CSybDBType::E_SYB_SYBSQLVARIANT:
		case CSybDBType::E_SYB_SYBGEOGRAPHY:
		case CSybDBType::E_SYB_SYBXML:
			tCol.iColType = CDataObject::E_DataType_Text;
			break;
		case CSybDBType::E_SYB_SYBINT1:
			tCol.iColType = CDataObject::E_DataType_TinyInt;
			break;
		case CSybDBType::E_SYB_SYBINT2:
			tCol.iColType = CDataObject::E_DataType_SmallInt;
			break;
		case CSybDBType::E_SYB_SYBINT4:
		case CSybDBType::E_SYB_SYBMSDATETIMEOFFSET:
			tCol.iColType = CDataObject::E_DataType_Int;
			break;
		case CSybDBType::E_SYB_SYBREAL:
			tCol.iColType = CDataObject::E_DataType_Double;
			break;
		case CSybDBType::E_SYB_SYBDATETIME:
		case CSybDBType::E_SYB_SYBMSDATETIME2:
		case CSybDBType::E_SYB_SYBDATETIME4:
			tCol.iColType = CDataObject::E_DataType_DateTime;
			break;
		case CSybDBType::E_SYB_SYBFLT8:
		case CSybDBType::E_SYB_SYBMONEY:
		case CSybDBType::E_SYB_SYBMONEY4:
			tCol.iColType = CDataObject::E_DataType_Float;
			break;
		case CSybDBType::E_SYB_SYBBIT:			
			tCol.iColType = CDataObject::E_DataType_TinyInt;
			break;
		case CSybDBType::E_SYB_SYBNUMERIC:
			tCol.iColType = CDataObject::E_DataType_Numeric;
			break;		
		case CSybDBType::E_SYB_SYBDECIMAL:
		case CSybDBType::E_SYB_SYBINT8:
			tCol.iColType = CDataObject::E_DataType_Int64;
			break;
		case CSybDBType::E_SYB_SYBIMAGE: 
			tCol.iColType = CDataObject::E_DataType_Image;
			break;	
		case CSybDBType::E_SYB_SYBBINARY:
			tCol.iColType = CDataObject::E_DataType_Byte;
			break;
		
		default:
		    return FALSE;
	}
	return TRUE;
}

BOOL CDataBaseDeal::SqlType2BindType(tSTRU_COLUMN& tCol)
{
	switch (tCol.iSqlType)
	{
		case CSybDBType::E_SYB_SYBCHAR:
			tCol.iBindType = CHARBIND;
			break;
		case CSybDBType::E_SYB_SYBINT1:
			tCol.iBindType = TINYBIND;
			break;
		case CSybDBType::E_SYB_SYBINT2:
			tCol.iBindType = SMALLBIND;
			break;		
		case CSybDBType::E_SYB_SYBINT4:
			tCol.iBindType = INTBIND;
			break;
		case CSybDBType::E_SYB_SYBINT8:
			tCol.iBindType = BIGINTBIND;
			break;
		case CSybDBType::E_SYB_SYBBINARY:
			tCol.iBindType = BINARYBIND;
			break;
		case CSybDBType::E_SYB_SYBTEXT:
		case CSybDBType::E_SYB_SYBMSDATE:
		case CSybDBType::E_SYB_SYBMSTIME:
		case CSybDBType::E_SYB_SYBSQLVARIANT:
		case CSybDBType::E_SYB_SYBGEOGRAPHY:
		case CSybDBType::E_SYB_SYBXML:
		case CSybDBType::E_SYB_SYBMSDATETIMEOFFSET:
		case CSybDBType::E_SYB_SYBREAL:
		case CSybDBType::E_SYB_SYBDATETIME:
		case CSybDBType::E_SYB_SYBMSDATETIME2:
		case CSybDBType::E_SYB_SYBDATETIME4:
		case CSybDBType::E_SYB_SYBFLT8:
		case CSybDBType::E_SYB_SYBMONEY:
		case CSybDBType::E_SYB_SYBMONEY4:
		case CSybDBType::E_SYB_SYBBIT:
		case CSybDBType::E_SYB_SYBNUMERIC:
		case CSybDBType::E_SYB_SYBDECIMAL:
		case CSybDBType::E_SYB_SYBIMAGE:
		default:
		    return FALSE;
	}
	return TRUE;
}

BOOL CDataBaseDeal::GetColumnsDatalen(CQueryRecord* pQueryRecord)
{
    if (nullptr == pQueryRecord)
    {
        return FALSE;
    }

    for (auto it = pQueryRecord->m_vecColumn.begin(); it != pQueryRecord->m_vecColumn.end(); it++)
    {
        (*it).iDataLen = m_DbConn->Datalen((*it).iColId);
    }
    
    return TRUE;
}

