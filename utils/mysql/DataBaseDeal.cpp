// DataBaseDeal.cpp: implementation of the CDataBaseDeal class.
//
//////////////////////////////////////////////////////////////////////

#include "DataBaseDeal.h"
#include "./Utility.h"
#include "./Strutils.h"

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////
CQueryRecord::CQueryRecord(void)
:m_vecColumn()
{
    Clear();
}

CQueryRecord::~CQueryRecord(void)
{
    Clear();
}

void CQueryRecord::Clear(void)
{
    for (size_t i = 0; i < m_vecColumn.size(); i++)
    {
        m_vecColumn[i].Release();
        m_vecColumn[i].Clear();
    }

    m_vecColumn.clear();
}

BOOL CQueryRecord::InitDataType(int nDataType, tSTRU_COLUMN& tCol)
{
    tCol.Clear();
    
    switch (nDataType)
    {
    case CDataObject::E_DataType_Text:
        tCol.Init(TYPE_CS_TEXT, Max_RECORD_Data);
        break;
        
    case CDataObject::E_DataType_Image:
    case CDataObject::E_DataType_Byte:
        tCol.Init(TYPE_CS_VARYBIN, MAX_RECORD_SIZE);
        break;
        
    case CDataObject::E_DataType_TinyInt:
        tCol.Init(TYPE_CS_SMALLINT, Max_RECORD_Data);
        break;
        
    case CDataObject::E_DataType_SmallInt:
        tCol.Init(TYPE_CS_SMALLINT, Max_RECORD_Data);
        break;
        
    case CDataObject::E_DataType_Int:
        tCol.Init(TYPE_CS_INT, Max_RECORD_Data);
        break;
        
    case CDataObject::E_DataType_Float:
        tCol.Init(TYPE_CS_FLOAT, Max_RECORD_Data);
        break;
    case CDataObject::E_DataType_Numeric: 
    case CDataObject::E_DataType_Int64:
        tCol.Init(TYPE_CS_INT64, Max_RECORD_Data);
        break;
    
	case CDataObject::E_DataType_DateTime:
		tCol.Init(TYPE_CS_INT, Max_RECORD_Data);
		break;
	
    default:
        {            
            return FALSE;
        }
    }

    return TRUE;
}

BOOL CQueryRecord::GetColInfoBySql(tDBConnInfo* dbConnInfo, std::string strSql, std::vector<STRU_DBCOL>& vecColAttri)
{
	CStdMySql_Connection m_DbConn;
	if (!m_DbConn.connect(dbConnInfo->pchUserName,
                          dbConnInfo->pchPassword,
                          dbConnInfo->pchDBServer,
                          dbConnInfo->pchDBName,
                          dbConnInfo->nPort))
	{
		return FALSE;
	}

	if (!m_DbConn.GetSelectColAttribute(strSql.c_str(), vecColAttri))
	{
		return FALSE;
	}

//	for (int i = 0; i < vecColAttri.size(); i++)
//	{
//		printf("i = %d, name = %s, type = %d\n", i+1, vecColAttri[i].columnName.c_str(), vecColAttri[i].columnType);
//	}
	
	return TRUE;
}

BOOL CQueryRecord::SetColumnInfo(tDBConnInfo* tInfo, std::string strSql)
{
    Clear();

    std::vector<STRU_DBCOL> vecColAttri; //列属性
    vecColAttri.clear();
    
    if (!GetColInfoBySql(tInfo, strSql, vecColAttri))
    {
		return FALSE;
	}
    
    tSTRU_COLUMN tTemp;
    tTemp.Clear();

    for (size_t i = 0; i < vecColAttri.size(); i++)
    {
        int retCType = CDataObject::E_DataType_Text;
        if(!DataType2DataObject(vecColAttri[i].columnType, retCType))
        {
        	//printf("DataType2DataObject  %s --- > %d\n", vecColAttri[i].columnName.c_str(), vecColAttri[i].columnType);
            return FALSE;
        }
        if (!InitDataType(retCType, tTemp))
        {
        	//printf("InitDataType  %s --- > %d\n", vecColAttri[i].columnName.c_str(), vecColAttri[i].columnType);
			return FALSE;
        }

        strncpy(tTemp.szColName, vecColAttri[i].columnName.c_str(), sizeof(tTemp.szColName));

        m_vecColumn.push_back(tTemp);
    }

    if (vecColAttri.size() == 0)
    {
        return FALSE;
    }
    
    return TRUE;
}

BOOL CQueryRecord::DataType2DataObject(const int sqlDataType, int& retCType)
{
	retCType = CDataObject::E_DataType_Text;

	switch (sqlDataType)
	{
		case CMySqlType::E_MYSQL_JSON:
		case CMySqlType::E_MYSQL_VARCHAR:
		case CMySqlType::E_MYSQL_VAR_STRING:
		case CMySqlType::E_MYSQL_STRING:
		case CMySqlType::E_MYSQL_ENUM:
            retCType = CDataObject::E_DataType_Text;
			break;
		case CMySqlType::E_MYSQL_BIT:
		case CMySqlType::E_MYSQL_TINY:
            retCType = CDataObject::E_DataType_TinyInt;
			break;
		case CMySqlType::E_MYSQL_SHORT:
            retCType = CDataObject::E_DataType_SmallInt;
			break;
		case CMySqlType::E_MYSQL_LONG:
            retCType = CDataObject::E_DataType_Int;
			break;
		case CMySqlType::E_MYSQL_FLOAT:
            retCType = CDataObject::E_DataType_Float;
			break;
		case CMySqlType::E_MYSQL_DOUBLE:
            retCType = CDataObject::E_DataType_Double;
			break;
		case CMySqlType::E_MYSQL_DATETIME:
		case CMySqlType::E_MYSQL_DATETIME2:
            retCType = CDataObject::E_DataType_DateTime;
			break;
		case CMySqlType::E_MYSQL_DECIMAL:
		case CMySqlType::E_MYSQL_NEWDECIMAL:
        case CMySqlType::E_MYSQL_LONGLONG:
            retCType = CDataObject::E_DataType_Int64;
			break;
        case CMySqlType::E_MYSQL_BLOB:
            retCType = CDataObject::E_DataType_Byte;
            break;
		default:
		    return FALSE;
	}
	return TRUE;
}


//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////
CDataBaseDeal::CDataBaseDeal(void)
:m_DbConn(nullptr),
m_BcpLog("dbbcpfile.txt"),
m_SqlLog("ms_execute_sql_log.txt")
{
    memset(&m_tDBConnInfo, 0, sizeof(m_tDBConnInfo));
	m_DbConn = new CStdMySql_Connection(&m_BcpLog);
}

CDataBaseDeal::~CDataBaseDeal(void)
{
    if (nullptr != m_DbConn)
    {
        delete m_DbConn;
        m_DbConn = nullptr;
    }
}

BOOL CDataBaseDeal::DbConnIsNormal(void)
{
    if ((m_tDBConnInfo.pchUserName == NULL) || (strcmp(m_tDBConnInfo.pchUserName, "") == 0) ||
        (m_tDBConnInfo.pchPassword == NULL) || (strcmp(m_tDBConnInfo.pchPassword, "") == 0) ||
        (m_tDBConnInfo.pchDBServer == NULL) || (strcmp(m_tDBConnInfo.pchDBServer, "") == 0) || m_tDBConnInfo.nPort <= 1000)
    {
        return FALSE;
    }

    if (!m_DbConn->connect(m_tDBConnInfo.pchUserName, 
        m_tDBConnInfo.pchPassword, 
        m_tDBConnInfo.pchDBServer, 
        m_tDBConnInfo.pchDBName,
        m_tDBConnInfo.nPort))
    {
        return FALSE;
    }

	m_DbConn->cleanup_conn();
	
    return TRUE;
}

tDBConnInfo* CDataBaseDeal::GetConnectInfo()
{
    return &m_tDBConnInfo;
}

/************************************************************************
恢复数据库连接指向主库
************************************************************************/
bool
CDataBaseDeal::ResetDBConnInfo(tDBConnInfo tInfo)
{
    memcpy(&m_tDBConnInfo, &tInfo, sizeof(tDBConnInfo));
	return true;
}

void CDataBaseDeal::SetDbConnPara(tDBConnInfo& tInfo)
{
    if ((strcasecmp(m_tDBConnInfo.pchDBName, tInfo.pchDBName) != 0)
        || (strcasecmp(m_tDBConnInfo.pchDBServer, tInfo.pchDBServer) != 0)
        || (m_tDBConnInfo.nDbId != tInfo.nDbId))
    {
        memcpy(&m_tDBConnInfo, &tInfo, sizeof(tDBConnInfo));
        m_DbConn->cleanup_conn();
    }
}

void CDataBaseDeal::BcpDataFile(std::string filename, std::string tablename, BOOL delmode)
{
    if (m_tDBConnInfo.nDisplaySql == 1)
    {
        OUTPUT_LOG(m_BcpLog, CUtility::OutPutInfo("[BCP FileName: ] %s", filename.c_str()).c_str());
    }
    if (!m_DbConn->connect(m_tDBConnInfo.pchUserName, 
        m_tDBConnInfo.pchPassword, 
        m_tDBConnInfo.pchDBServer, 
        m_tDBConnInfo.pchDBName,
        m_tDBConnInfo.nPort))
    {
        m_BcpLog.OutPut("数据库连接失败.\r\n");
        return;
    }

    bool bRt = m_DbConn->BcpData(filename.c_str(), tablename.c_str());

    if (!bRt)
    {
        char szLog[512] = "";
        snprintf(szLog, 512 - 1, "Bcp filed:%s\r\n", filename.c_str());
        m_BcpLog.OutPut(szLog);
    }
    else
    {
        if (delmode)
        {
            CStdFile::Remove(filename.c_str());
        }
    }

    m_DbConn->cleanup_conn();
}

int CDataBaseDeal::ExecSql(const char* const strSql, BOOL bDisplayLog)
{
    if (m_tDBConnInfo.nDisplaySql == 1 && bDisplayLog)
    {
        OUTPUT_LOG(m_SqlLog, CUtility::OutPutInfo("[Exec SQL: ] %s", strSql).c_str());
    }
		
    if (!m_DbConn->connect(m_tDBConnInfo.pchUserName, 
        m_tDBConnInfo.pchPassword, 
        m_tDBConnInfo.pchDBServer, 
        m_tDBConnInfo.pchDBName,
        m_tDBConnInfo.nPort))
    {
        return -10;
    }
    
    if (!m_DbConn->Execute(strSql))
    {
    	m_DbConn->cleanup_conn();
		return -10;
    }

	m_DbConn->cleanup_conn();
    return 0;
}

int CDataBaseDeal::SearchSql(const char* const strSql, CQueryRecord* pQueryRecord, int nQType, BOOL needInit)
{
    if (needInit == true)
    {
        if (!pQueryRecord->SetColumnInfo(&m_tDBConnInfo, strSql))
        {
            OUTPUT_LOG(m_SqlLog, CUtility::OutPutInfo("[Failed to get table field properties!]: %s", strSql).c_str());
        }
    }

    if (m_tDBConnInfo.nDisplaySql == 1)
    {
        OUTPUT_LOG(m_SqlLog, CUtility::OutPutInfo("[Exec SQL: ] %s", strSql).c_str());
    }
	
    if(NULL == pQueryRecord)
    {
        return -1;
    }

    int nCount = (int)(pQueryRecord->m_vecColumn.size());
    if(nCount == 0)
    {
        return -1;
    }

    if (!m_DbConn->connect(m_tDBConnInfo.pchUserName, 
        m_tDBConnInfo.pchPassword, 
        m_tDBConnInfo.pchDBServer, 
        m_tDBConnInfo.pchDBName,
        m_tDBConnInfo.nPort))
    {
    	m_DbConn->cleanup_conn();
        return -2; 
    }
	
    if (!m_DbConn->Execute(strSql))
    {
        m_DbConn->cleanup_conn();
        return -1;
    } 
	
    std::vector<int> vecTemp;
    vecTemp.clear();

    if (m_DbConn->DbResults() == 0)
    {
        return 2;
    }
    
	while(0 == m_DbConn->DbNextRow())
	{ 
        for (int i = 0; i < nCount; i++)
        {		
            memset(pQueryRecord->m_vecColumn[i].pColValue, 0, MAX_PATH);
            if ((pQueryRecord->m_vecColumn[i].pBigValue != NULL) && (pQueryRecord->m_vecColumn[i].iBigLen > 0))
            {
                memset(pQueryRecord->m_vecColumn[i].pBigValue, 0, pQueryRecord->m_vecColumn[i].iBigLen);
            }

            switch(pQueryRecord->m_vecColumn[i].iColType)
            {
            case TYPE_CS_TINYINT:
            case TYPE_CS_SMALLINT:
            case TYPE_CS_FLOAT:
            case TYPE_CS_TEXT:
            case TYPE_CS_INT:
            case TYPE_CS_INT64:
                m_DbConn->BindCol(i, pQueryRecord->m_vecColumn[i].pColValue, MAX_PATH);
                break;
            case TYPE_CS_VARYBIN:
                if (pQueryRecord->m_vecColumn[i].pBigValue != NULL)
                {
                    long nlen = pQueryRecord->m_vecColumn[i].iBigLen;
                    assert (nlen > 0);
                    assert (nlen <= MAX_RECORD_SIZE);
                    if (nlen == 0)
                    {
                        nlen = 8000; /*实际没这么长*/
                    }
                    m_DbConn->BindCol(i, pQueryRecord->m_vecColumn[i].pBigValue, nlen);
                }
                else
                {
                    m_DbConn->BindCol(i, pQueryRecord->m_vecColumn[i].pColValue, MAX_PATH);
                }
                vecTemp.push_back(i);
                break;
            default:
                {
                    m_DbConn->cleanup_conn();
                    return -1;
                } 
            }
        }
	
        pQueryRecord->RowResultDeal(nQType);
        
        for(UINT j = 0; j < vecTemp.size(); j++)
        {
            memset(pQueryRecord->m_vecColumn[vecTemp[j]].pColValue, 0, MAX_PATH);
            if ((pQueryRecord->m_vecColumn[vecTemp[j]].pBigValue != NULL) 
                && (pQueryRecord->m_vecColumn[vecTemp[j]].iBigLen > 0))
            {
                memset(pQueryRecord->m_vecColumn[vecTemp[j]].pBigValue, 0, pQueryRecord->m_vecColumn[vecTemp[j]].iBigLen);
            }
        }
	}    

	m_DbConn->cleanup_conn();
    return 1;
}

